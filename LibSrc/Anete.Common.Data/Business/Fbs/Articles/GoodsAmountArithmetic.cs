using Anete.Utils;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Anete.Common.Data.Business.Fbs.Articles
{
	public class GoodsAmountArithmetic
	{
		/// <summary>
		/// Soucet hmotnosti. Pokud secist nelze, vraci null.
		/// </summary>
		/// <param name="args"></param>
		/// <returns></returns>
		public GoodsAmount Sum(params GoodsAmount[] args)
		{
			// nejjednodussi pripad, kdy mam vsechny jednotky stejne
			if (args.Select(a => a.Unit.ToLower()).Distinct().Count() == 1)
			{
				return new GoodsAmount(args.Sum(a => a.Amount), args.First().Unit);
			}

			// pokusim se o prevod jednotek
			PredefinedUnitType? resultUnit = GetPrefferedUnit(args.Select(a => a.Unit).ToArray());
			if (!resultUnit.HasValue)
			{
				// neznama jednotka
				return null;
			}

			// mam stanovenou vyslednou jednotku, muzu opet secist vysledek
			GoodsAmount[] converted = ConvertTo(args, resultUnit.Value).ToArray();
			return Sum(converted);
		}

		private PredefinedUnitType? GetPrefferedUnit(params string[] units)
		{
			// abych dosahl presnych vysledku, budu vysledet uvadet v nejnizsi jednotce
			// nejvyssi prioritu ma tedy pro me gram
			if (units.Any(u => UnitMathPredefinedUnit(u, PredefinedUnitType.g)))
			{
				return PredefinedUnitType.g;
			}

			// nasleduje kilogram
			if (units.Any(u => UnitMathPredefinedUnit(u, PredefinedUnitType.kg)))
			{
				return PredefinedUnitType.kg;
			}

			// neocekavana jednotka, vratim null, nejsem schopen stanovit vysledek
			return null;
		}

		public IEnumerable<GoodsAmount> ConvertTo(GoodsAmount[] args, PredefinedUnitType unitType)
		{
			foreach (GoodsAmount goodsAmount in args)
			{
				yield return ConvertTo(goodsAmount, unitType);
			}
		}

		public GoodsAmount ConvertTo(GoodsAmount goodsAmount, PredefinedUnitType unitType)
		{
			if (UnitMathPredefinedUnit(goodsAmount.Unit, unitType))
			{
				// nemusim nic prevadet, mam primo v pozadovane jednotce
				return goodsAmount;
			}

			// musim provest prevod
			switch (unitType)
			{
				case PredefinedUnitType.g:
					return ConvertToG(goodsAmount);

				case PredefinedUnitType.kg:
					return ConvertToKg(goodsAmount);

				default:
					throw ExcUtils.ArgumentOutOfRange("unitType", unitType);
			}
		}

		public GoodsAmount ConvertToG(GoodsAmount goodsAmount)
		{
			if (UnitMathPredefinedUnit(goodsAmount.Unit, PredefinedUnitType.l) || UnitMathPredefinedUnit(goodsAmount.Unit, PredefinedUnitType.kg))
			{				
				return new GoodsAmount(goodsAmount.Amount * 1000, PredefinedUnitType.g);
			}			
			else
			{
				throw ExcUtils.ArgumentOutOfRange("goodsAmount.Unit", goodsAmount.Unit);
			}
		}

		public GoodsAmount ConvertToKg(GoodsAmount goodsAmount)
		{
			if (UnitMathPredefinedUnit(goodsAmount.Unit, PredefinedUnitType.l))
			{
				// litr povazaju hmotnostne stejny jako kg
				return new GoodsAmount(goodsAmount.Amount, PredefinedUnitType.kg);
			}
			else if (UnitMathPredefinedUnit(goodsAmount.Unit, PredefinedUnitType.g))
			{
				return new GoodsAmount(goodsAmount.Amount / 1000, PredefinedUnitType.kg);
			}
			else
			{
				throw ExcUtils.ArgumentOutOfRange("goodsAmount.Unit", goodsAmount.Unit);
			}
		}

		public static bool UnitMathPredefinedUnit(string unit, PredefinedUnitType predefinedUnit)
		{
			return unit.ToLower() == predefinedUnit.ToString();
		}
	}
}
