using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using Anete.Common.Core.Interface.Business.Eet;
using Anete.Utils.Extensions;

namespace Anete.Common.Core.Interface.Business.Eet
{
	/// <summary>
	/// Finanční položky tržby
	/// </summary>
	public partial class EetTrzbaData : IEetTrzbaData
	{

		/// <summary>
		/// DIČ poplatníka (dic_popl) 
		/// </summary>
		public string DicPopl { get; set; }

		/// <summary>
		/// DIČ pověřujícího poplatníka (dic_poverujiciho) 
		/// </summary>
		public string DicPoverujiciho { get; set; }

		/// <summary>
		/// Identifikace provozovny (id_provoz) 
		/// </summary>
		public int IdProvoz { get; set; }

		/// <summary>
		/// Identifikace pokladního zaří<PERSON> poplatníka (id_pokl) 
		/// </summary>
		public string IdPokl { get; set; }

		/// <summary>
		/// Pořadové <PERSON> (porad_cis) 
		/// </summary>
		public string PoradCis { get; set; }

		/// <summary>
		/// Datum a čas přijetí tržby (dat_trzby) 
		/// </summary>
		public System.DateTime DatTrzby { get; set; }

		/// <summary>
		/// Celková částka tržby 
		/// </summary>
		public decimal CelkTrzba { get; set; }

		/// <summary>
		/// Celková částka plnění osvobozených od DPH, ostatních plnění 
		/// </summary>
		public decimal? ZaklNepodlDph { get; set; }

		/// <summary>
		/// Celkový základ daně se základní sazbou DPH
		/// </summary>
		public decimal? ZaklDan1 { get; set; }

		/// <summary>
		/// Celková DPH se základní sazbou 
		/// </summary>
		public decimal? Dan1 { get; set; }

		/// <summary>
		/// Celkový základ daně s první sníženou sazbou DPH 
		/// </summary>
		public decimal? ZaklDan2 { get; set; }

		/// <summary>
		/// Celková DPH s první sníženou sazbou 
		/// </summary>
		public decimal? Dan2 { get; set; }

		/// <summary>
		/// Celkový základ daně s druhou sníženou sazbou DPH 
		/// </summary>
		public decimal? ZaklDan3 { get; set; }

		/// <summary>
		/// Celková DPH s druhou sníženou sazbou 
		/// </summary>
		public decimal? Dan3 { get; set; }

		/// <summary>
		/// Celková částka v režimu DPH pro cestovní službu 
		/// </summary>
		public decimal? CestSluz { get; set; }

		/// <summary>
		/// Celková částka v režimu DPH pro prodej použitého zboží se základní sazbou 
		/// </summary>
		public decimal? PouzitZboz1 { get; set; }

		/// <summary>
		/// Celková částka v režimu DPH pro prodej použitého zboží s první sníženou sazbou
		/// </summary>
		public decimal? PouzitZboz2 { get; set; }

		/// <summary>
		/// Celková částka v režimu DPH pro prodej použitého zboží s druhou sníženou sazbou
		/// </summary>
		public decimal? PouzitZboz3 { get; set; }

		/// <summary>
		/// Celková částka plateb určená k následnému čerpání nebo zúčtování 
		/// </summary>
		public decimal? UrcenoCerpZuct { get; set; }

		/// <summary>
		/// Celková částka plateb, které jsou následným čerpáním nebo zúčtováním platby 
		/// </summary>
		public decimal? CerpZuct { get; set; }

		/// <summary>
		/// Režim tržby (rezim) 
		/// </summary>
		public EetRezimTrzby Rezim { get; set; }

		/// <summary>
		/// Zkontroluje validitu pozadavku - zda sedi celkovy soucet
		/// </summary>
		public void CheckIsValid()
		{
			//IEnumerable<decimal?> items = new decimal?[] { ZaklNepodlDph, ZaklDan1, Dan1, ZaklDan2, Dan2, ZaklDan3, Dan3, PouzitZboz1,
			//  PouzitZboz2, PouzitZboz3, CestSluz, UrcenoCerpZuct, CerpZuct};

			// Kak: 20.09.2016 Toto jsem musel zrusit ve chvili, kdy se do CelkTrzba zacaly pri prodeji pro klienty davat pouze vklady
			// zaloh a pak samozrejme kontrolni soucet nesedi
			// uprava od chvile, kdy se zacalo pouzivat i odesilani zaloh 
			//IEnumerable<decimal?> items = new decimal?[] { ZaklNepodlDph, ZaklDan1, Dan1, ZaklDan2, Dan2, ZaklDan3, Dan3, PouzitZboz1,
			//  PouzitZboz2, PouzitZboz3, CestSluz};

			//if (items.Sum() != CelkTrzba)
			//{
			//	throw new InvalidOperationException("Suma celkem za jednotlivé sazby daně nesouhlasí s celkovou cenou k úhradě.");
			//}
		}

		public override string ToString()
		{
			return this.ToReportString();
		}

	}

}
