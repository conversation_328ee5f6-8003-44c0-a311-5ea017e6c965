using System;

namespace Anete.ApiServices
{

	[AttributeUsage(AttributeTargets.Method, AllowMultiple = false)]
	public class ApiServiceActionAttribute : Attribute
	{
		/// <summary>
		/// Název akce v controlleru.
		/// Při nevyplnění se použije název metody.
		/// </summary>
		public string Name { get; set; }

		/// <summary>
		/// Při volání se na serveru nekontrolují přihlašovací údaje.
		/// </summary>
		public bool AllowAnonymous { get; set; }
	}
}