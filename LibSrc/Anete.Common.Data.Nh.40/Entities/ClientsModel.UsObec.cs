//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Anete.Common.Data.Nh.Entities
{

    /// <summary>
    /// Tabulka: us.Obec
    /// </summary>
    public partial class UsObec : Anete.Data.Nh.NhEntityBase, INotifyPropertyChanging, INotifyPropertyChanged {

        private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(System.String.Empty);

        private int _Id;

        private string _Nazev;

        private string _Posta;

        private string _Psc;

        private string _SpravniObvod;

        private string _Okres;

        private string _Kraj;

        private string _KodObce;

        private string _NazevCastiObce;

        private string _KodCastiObce;

        private string _Zdroj;

        private ISet<UsAdresa> _UsAdresas;

        private ISet<UsMesto> _UsMestos;

        private UsStat _UsStat;
    
        #region Extensibility Method Definitions
        
        partial void OnCreated();

        public override bool Equals(object obj)
        {
          UsObec toCompare = obj as UsObec;
          if (toCompare == null)
          {
            return false;
          }

          if (!Object.Equals(this.Id, toCompare.Id))
            return false;
         // pokud mam default primary key, znamena to, ze jedna o nove vytvorenou entitu, ktera jeste nema key prirazen. 
        // V takove pripade musim porovnat podle instance
        bool isDefault = true;
        if (this.Id != default(int))
        {
          isDefault = false;
        }
     

          if (isDefault)
          {
            return object.ReferenceEquals(toCompare, this);
          }     
          return true;
        }

	private int? _hashCode = null;
        public override int GetHashCode()
        {
	if (_hashCode == null)
          {
          _hashCode = 13;
          _hashCode = (_hashCode * 7) + Id.GetHashCode();
}
          return _hashCode.Value;
        }
        partial void OnIdChanging(int value);
        
        partial void OnIdChanged();
        partial void OnNazevChanging(string value);
        
        partial void OnNazevChanged();
        partial void OnPostaChanging(string value);
        
        partial void OnPostaChanged();
        partial void OnPscChanging(string value);
        
        partial void OnPscChanged();
        partial void OnSpravniObvodChanging(string value);
        
        partial void OnSpravniObvodChanged();
        partial void OnOkresChanging(string value);
        
        partial void OnOkresChanged();
        partial void OnKrajChanging(string value);
        
        partial void OnKrajChanged();
        partial void OnKodObceChanging(string value);
        
        partial void OnKodObceChanged();
        partial void OnNazevCastiObceChanging(string value);
        
        partial void OnNazevCastiObceChanged();
        partial void OnKodCastiObceChanging(string value);
        
        partial void OnKodCastiObceChanged();
        partial void OnZdrojChanging(string value);
        
        partial void OnZdrojChanged();
        partial void OnUsStatChanging(UsStat value);

        partial void OnUsStatChanged();
        
        #endregion
        public UsObec()
        {
            this._UsAdresas = new HashSet<UsAdresa>();
            this._UsMestos = new HashSet<UsMesto>();
            OnCreated();
        }

    
        /// <summary>
        /// Sloupec: id
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Id", typeof(UsObecSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(UsObec), Tag="Id")]
        public virtual int Id
        {
            get
            {
                return this._Id;
            }
            set
            {
                if (this._Id != value)
                {
                    this.OnIdChanging(value);
                    this.SendPropertyChanging();
                    this._Id = value;
                    this.SendPropertyChanged("Id");
                    this.OnIdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: nazev
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Nazev", typeof(UsObecSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(UsObec), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 50,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="Nazev")]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(UsObec), Tag="Nazev")]
        public virtual string Nazev
        {
            get
            {
                return this._Nazev;
            }
            set
            {
                if (this._Nazev != value)
                {
                    this.OnNazevChanging(value);
                    this.SendPropertyChanging();
                    this._Nazev = value;
                    this.SendPropertyChanged("Nazev");
                    this.OnNazevChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: posta
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Posta", typeof(UsObecSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(UsObec), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 50,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="Posta")]
        public virtual string Posta
        {
            get
            {
                return this._Posta;
            }
            set
            {
                if (this._Posta != value)
                {
                    this.OnPostaChanging(value);
                    this.SendPropertyChanging();
                    this._Posta = value;
                    this.SendPropertyChanged("Posta");
                    this.OnPostaChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: psc
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Psc", typeof(UsObecSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(UsObec), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 10,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="Psc")]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(UsObec), Tag="Psc")]
        public virtual string Psc
        {
            get
            {
                return this._Psc;
            }
            set
            {
                if (this._Psc != value)
                {
                    this.OnPscChanging(value);
                    this.SendPropertyChanging();
                    this._Psc = value;
                    this.SendPropertyChanged("Psc");
                    this.OnPscChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: spravniObvod
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("SpravniObvod", typeof(UsObecSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(UsObec), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 50,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="SpravniObvod")]
        public virtual string SpravniObvod
        {
            get
            {
                return this._SpravniObvod;
            }
            set
            {
                if (this._SpravniObvod != value)
                {
                    this.OnSpravniObvodChanging(value);
                    this.SendPropertyChanging();
                    this._SpravniObvod = value;
                    this.SendPropertyChanged("SpravniObvod");
                    this.OnSpravniObvodChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: okres
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Okres", typeof(UsObecSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(UsObec), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 50,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="Okres")]
        public virtual string Okres
        {
            get
            {
                return this._Okres;
            }
            set
            {
                if (this._Okres != value)
                {
                    this.OnOkresChanging(value);
                    this.SendPropertyChanging();
                    this._Okres = value;
                    this.SendPropertyChanged("Okres");
                    this.OnOkresChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: kraj
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Kraj", typeof(UsObecSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(UsObec), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 50,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="Kraj")]
        public virtual string Kraj
        {
            get
            {
                return this._Kraj;
            }
            set
            {
                if (this._Kraj != value)
                {
                    this.OnKrajChanging(value);
                    this.SendPropertyChanging();
                    this._Kraj = value;
                    this.SendPropertyChanged("Kraj");
                    this.OnKrajChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: kodObce
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("KodObce", typeof(UsObecSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(UsObec), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 6,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="KodObce")]
        public virtual string KodObce
        {
            get
            {
                return this._KodObce;
            }
            set
            {
                if (this._KodObce != value)
                {
                    this.OnKodObceChanging(value);
                    this.SendPropertyChanging();
                    this._KodObce = value;
                    this.SendPropertyChanged("KodObce");
                    this.OnKodObceChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: nazevCastiObce
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("NazevCastiObce", typeof(UsObecSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(UsObec), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 50,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="NazevCastiObce")]
        public virtual string NazevCastiObce
        {
            get
            {
                return this._NazevCastiObce;
            }
            set
            {
                if (this._NazevCastiObce != value)
                {
                    this.OnNazevCastiObceChanging(value);
                    this.SendPropertyChanging();
                    this._NazevCastiObce = value;
                    this.SendPropertyChanged("NazevCastiObce");
                    this.OnNazevCastiObceChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: kodCastiObce
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("KodCastiObce", typeof(UsObecSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(UsObec), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 6,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="KodCastiObce")]
        public virtual string KodCastiObce
        {
            get
            {
                return this._KodCastiObce;
            }
            set
            {
                if (this._KodCastiObce != value)
                {
                    this.OnKodCastiObceChanging(value);
                    this.SendPropertyChanging();
                    this._KodCastiObce = value;
                    this.SendPropertyChanged("KodCastiObce");
                    this.OnKodCastiObceChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: zdroj
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Zdroj", typeof(UsObecSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(UsObec), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 1,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="Zdroj")]
        public virtual string Zdroj
        {
            get
            {
                return this._Zdroj;
            }
            set
            {
                if (this._Zdroj != value)
                {
                    this.OnZdrojChanging(value);
                    this.SendPropertyChanging();
                    this._Zdroj = value;
                    this.SendPropertyChanged("Zdroj");
                    this.OnZdrojChanged();
                }
            }
        }

        [Anete.Data.Attributes.EntitySRDisplayName("UsAdresas", typeof(UsObecSR))]
        [Anete.Data.Attributes.DbCollectionProperty]
        public virtual ISet<UsAdresa> UsAdresas
        {
            get
            {
                return this._UsAdresas;
            }
            set
            {
                this._UsAdresas = value;
            }
        }

        [Anete.Data.Attributes.EntitySRDisplayName("UsMestos", typeof(UsObecSR))]
        [Anete.Data.Attributes.DbCollectionProperty]
        public virtual ISet<UsMesto> UsMestos
        {
            get
            {
                return this._UsMestos;
            }
            set
            {
                this._UsMestos = value;
            }
        }

    
        /// <summary>
        /// Sloupec: kodStat
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("UsStat", typeof(UsObecSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual UsStat UsStat
        {
            get
            {
                return this._UsStat;
            }
            set
            {
                if (this._UsStat != value)
                {
                    this.OnUsStatChanging(value);
                    this.SendPropertyChanging();
                    this._UsStat = value;
                    this.SendPropertyChanged("UsStat");
                    this.OnUsStatChanged();
                }
            }
        }
   
        public virtual event PropertyChangingEventHandler PropertyChanging;

	// Mam v bazove tride, nema smysl generovat
	// public virtual event PropertyChangedEventHandler PropertyChanged;

        protected virtual void SendPropertyChanging()
        {
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, emptyChangingEventArgs);
        }

        protected virtual void SendPropertyChanging(System.String propertyName) 
        {   	
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, new PropertyChangingEventArgs(propertyName));
        }

        protected virtual void SendPropertyChanged(System.String propertyName)
        {    
		// mam v bazove tride, proto musim volat takto jednoduse
		OnPropertyChanged(propertyName);
/*		        var handler = this.PropertyChanged;
            if (handler != null)
                handler(this, new PropertyChangedEventArgs(propertyName));*/
        }
    }

}
