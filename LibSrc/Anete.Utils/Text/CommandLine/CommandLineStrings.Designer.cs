//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Anete.Utils.Text.CommandLine {
    using System;
    
    
    /// <summary>
    /// A strongly-typed resource class, for looking up localized strings, formatting them, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilderEx class via the ResXFileCodeGeneratorEx custom tool.
    // To add or remove a member, edit your .ResX file then rerun the ResXFileCodeGeneratorEx custom tool or rebuild your VS.NET project.
    // Copyright (c) Dmytro <PERSON> 2006-2018 (http://dmytro.kryvko.googlepages.com/)
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("DMKSoftware.CodeGenerators.Tools.StronglyTypedResourceBuilderEx", "2.6.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Naming", "CA1724:TypeNamesShouldNotMatchNamespaces")]
    public partial class CommandLineStrings {
        
        private static global::System.Resources.ResourceManager _resourceManager;
        
        private static object _internalSyncObject;
        
        private static global::System.Globalization.CultureInfo _resourceCulture;
        
        /// <summary>
        /// Initializes a CommandLineStrings object.
        /// </summary>
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        public CommandLineStrings() {
        }
        
        /// <summary>
        /// Thread safe lock object used by this class.
        /// </summary>
        public static object InternalSyncObject {
            get {
                if (object.ReferenceEquals(_internalSyncObject, null)) {
                    global::System.Threading.Interlocked.CompareExchange(ref _internalSyncObject, new object(), null);
                }
                return _internalSyncObject;
            }
        }
        
        /// <summary>
        /// Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(_resourceManager, null)) {
                    global::System.Threading.Monitor.Enter(InternalSyncObject);
                    try {
                        if (object.ReferenceEquals(_resourceManager, null)) {
                            global::System.Threading.Interlocked.Exchange(ref _resourceManager, new global::System.Resources.ResourceManager("Anete.Utils.Text.CommandLine.CommandLineStrings", typeof(CommandLineStrings).Assembly));
                        }
                    }
                    finally {
                        global::System.Threading.Monitor.Exit(InternalSyncObject);
                    }
                }
                return _resourceManager;
            }
        }
        
        /// <summary>
        /// Overrides the current thread's CurrentUICulture property for all
        /// resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return _resourceCulture;
            }
            set {
                _resourceCulture = value;
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Přístup k souboru &quot;{0}&quot; byl odepřen.'.
        /// </summary>
        public static string AccessToTheSpecifiedFile0IsDenied {
            get {
                return ResourceManager.GetString(ResourceNames.AccessToTheSpecifiedFile0IsDenied, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Alias &quot;{0}&quot; není platný pro volbu &quot;{1}&quot;; BoolFunction je nastavena na {2} je aktivní styl nastavení {3} který zakazuje jakékoliv parametry delší než jeden znak.'.
        /// </summary>
        public static string Alias0IsInvalidForOption1BoolFunctionIsSetTo2AndThe3OptionStyleIsEnabledWhichProhibitsAnyNameLongerThanOneCharacter {
            get {
                return ResourceManager.GetString(ResourceNames.Alias0IsInvalidForOption1BoolFunctionIsSetTo2AndThe3OptionStyleIsEnabledWhichProhibitsAnyNameLongerThanOneCharacter, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Alias &quot;{0}&quot; je už použit jiným parametrem.'.
        /// </summary>
        public static string AliasName0IsAlreadyInUseByAnotherOption {
            get {
                return ResourceManager.GetString(ResourceNames.AliasName0IsAlreadyInUseByAnotherOption, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Musí být zadány všechny parametry {0}.'.
        /// </summary>
        public static string AllOfTheOptions0MustBeSpecified {
            get {
                return ResourceManager.GetString(ResourceNames.AllOfTheOptions0MustBeSpecified, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to '{0} nesmí být menší než {1}.'.
        /// </summary>
        public static string Arg0MustNotBeLessThan1 {
            get {
                return ResourceManager.GetString(ResourceNames.Arg0MustNotBeLessThan1, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to '{0} musí být větší než nula.'.
        /// </summary>
        public static string ArgMustBeGreaterThanZero {
            get {
                return ResourceManager.GetString(ResourceNames.ArgMustBeGreaterThanZero, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to '{0} nesmí být záporné číslo.'.
        /// </summary>
        public static string ArgMustBeNonNegative {
            get {
                return ResourceManager.GetString(ResourceNames.ArgMustBeNonNegative, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'AssigmentCharacters nesmí být měněny v průběhu práce parseru.'.
        /// </summary>
        public static string AssignmentCharactersMustNotBeChangedWhileParseInProgress {
            get {
                return ResourceManager.GetString(ResourceNames.AssignmentCharactersMustNotBeChangedWhileParseInProgress, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Musí být zadán nejméně jeden parametr {0}.'.
        /// </summary>
        public static string AtLeastOneOfTheOption0MustBeSpecified {
            get {
                return ResourceManager.GetString(ResourceNames.AtLeastOneOfTheOption0MustBeSpecified, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Parametr {0} může být zadán maximálně jednou.'.
        /// </summary>
        public static string AtMostOneOfTheOptions0MayBeSpecifiedAtOnce {
            get {
                return ResourceManager.GetString(ResourceNames.AtMostOneOfTheOptions0MayBeSpecifiedAtOnce, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'BoolFunction can not be set to &apos;{0}&apos; for option with a name longer than one character when option style &apos;{1}&apos; is specified in the {2} attribute'.
        /// </summary>
        public static string BoolFunctionCanNotBeSetTo0ForOptionWithANameLongerThanOneCharacterWhenOptionStyle1IsSpecifiedInThe2Attribute {
            get {
                return ResourceManager.GetString(ResourceNames.BoolFunctionCanNotBeSetTo0ForOptionWithANameLongerThanOneCharacterWhenOptionStyle1IsSpecifiedInThe2Attribute, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'BoolFunction may not be set to &apos;{0}&apos; when option style &apos;{1}&apos; is not specified in the {2} attribute'.
        /// </summary>
        public static string BoolFunctionMayNotBeSetTo0WhenOptionStyle1IsNotSpecifiedInThe2Attribute {
            get {
                return ResourceManager.GetString(ResourceNames.BoolFunctionMayNotBeSetTo0WhenOptionStyle1IsNotSpecifiedInThe2Attribute, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'BoolFunction may not be set to &apos;{0}&apos; when option style &apos;{1}&apos; is specified in the {2} attribute'.
        /// </summary>
        public static string BoolFunctionMayNotBeSetTo0WhenOptionStyle1IsSpecifiedInThe2Attribute {
            get {
                return ResourceManager.GetString(ResourceNames.BoolFunctionMayNotBeSetTo0WhenOptionStyle1IsSpecifiedInThe2Attribute, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Chyba v příkazové řádce.'.
        /// </summary>
        public static string DefaultCommandLineExceptionErrorMessage {
            get {
                return ResourceManager.GetString(ResourceNames.DefaultCommandLineExceptionErrorMessage, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Pro parametr příkazové řádky chybí název parametru.'.
        /// </summary>
        public static string DefaultMissingOptionNameExceptionMessage {
            get {
                return ResourceManager.GetString(ResourceNames.DefaultMissingOptionNameExceptionMessage, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Název parametru musí být zadán.'.
        /// </summary>
        public static string EmptyOptionNameIsNotAllowed {
            get {
                return ResourceManager.GetString(ResourceNames.EmptyOptionNameIsNotAllowed, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konec vstupu.'.
        /// </summary>
        public static string EndOfInput {
            get {
                return ResourceManager.GetString(ResourceNames.EndOfInput, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Seznam chyb:'.
        /// </summary>
        public static string ErrorList {
            get {
                return ResourceManager.GetString(ResourceNames.ErrorList, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Soubor &quot;{0}&quot; nebyl nalezen.'.
        /// </summary>
        public static string FileNotFound0 {
            get {
                return ResourceManager.GetString(ResourceNames.FileNotFound0, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Internal error; non-implemented group requirement specified'.
        /// </summary>
        public static string InternalErrorNonImplementedGroupRequirementSpecified {
            get {
                return ResourceManager.GetString(ResourceNames.InternalErrorNonImplementedGroupRequirementSpecified, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Internal error; option specified in prohibition does not exist'.
        /// </summary>
        public static string InternalErrorOptionSpecifiedInProhibitionDoesNotExist {
            get {
                return ResourceManager.GetString(ResourceNames.InternalErrorOptionSpecifiedInProhibitionDoesNotExist, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Internal error; Option.Value was set more than MaxOccurs number of times'.
        /// </summary>
        public static string InternalErrorOptionValueWasSetMoreThanMaxOccursNumberOfTimes {
            get {
                return ResourceManager.GetString(ResourceNames.InternalErrorOptionValueWasSetMoreThanMaxOccursNumberOfTimes, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Internal error; unimplemented BoolFunction {0} used in option &quot;{1}&quot;'.
        /// </summary>
        public static string InternalErrorUnimplementedBoolFunction0UsedInOption1 {
            get {
                return ResourceManager.GetString(ResourceNames.InternalErrorUnimplementedBoolFunction0UsedInOption1, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Internal error; Unimplemented token type returned to CommandLineParser'.
        /// </summary>
        public static string InternalErrorUnimplementedTokenTypeReturnedToCommandLineParser {
            get {
                return ResourceManager.GetString(ResourceNames.InternalErrorUnimplementedTokenTypeReturnedToCommandLineParser, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Internal error; Unimplemented VerticalAlignment used'.
        /// </summary>
        public static string InternalErrorUnimplementedVerticalAlignmentUsed {
            get {
                return ResourceManager.GetString(ResourceNames.InternalErrorUnimplementedVerticalAlignmentUsed, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Chybný prvek enumerace &quot;{0}&quot;.'.
        /// </summary>
        public static string InvalidEnumerationValue0 {
            get {
                return ResourceManager.GetString(ResourceNames.InvalidEnumerationValue0, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Chybné Id skupiny, Id musí být zadáno.'.
        /// </summary>
        public static string InvalidIdOfGroupIdMustNotBeNullOrEmpty {
            get {
                return ResourceManager.GetString(ResourceNames.InvalidIdOfGroupIdMustNotBeNullOrEmpty, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Chybná hodnota ({0}) pro parametr &quot;{1}&quot;.'.
        /// </summary>
        public static string InvalidValue0ForOption1 {
            get {
                return ResourceManager.GetString(ResourceNames.InvalidValue0ForOption1, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Chybná hodnota &quot;{0}&quot; pro parametr &quot;{1}&quot;; Hodnota musí být jedna z {2}'.
        /// </summary>
        public static string InvalidValue0ForOption1TheValueMustBeOneOf2 {
            get {
                return ResourceManager.GetString(ResourceNames.InvalidValue0ForOption1TheValueMustBeOneOf2, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'nebo &quot;{0}&quot;'.
        /// </summary>
        public static string LastItemOfExclusiveList {
            get {
                return ResourceManager.GetString(ResourceNames.LastItemOfExclusiveList, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Chybějící uzavírací apostrof.'.
        /// </summary>
        public static string MissingClosingQuote {
            get {
                return ResourceManager.GetString(ResourceNames.MissingClosingQuote, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Chybějící uzavírací apostrof pro hodnotu v apostrofech.'.
        /// </summary>
        public static string MissingClosingQuoteForQuotedValue {
            get {
                return ResourceManager.GetString(ResourceNames.MissingClosingQuoteForQuotedValue, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Missing closing quote for quote command line value: {0}'.
        /// </summary>
        public static string MissingClosingQuoteForValue0 {
            get {
                return ResourceManager.GetString(ResourceNames.MissingClosingQuoteForValue0, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Missing required attribute for a command line manager object: CommandLineManagerAttribute'.
        /// </summary>
        public static string MissingRequiredAttributeForACommandLineManagerObjectCommandLineManagerAttribute {
            get {
                return ResourceManager.GetString(ResourceNames.MissingRequiredAttributeForACommandLineManagerObjectCommandLineManagerAttribute, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Vyžadovaný parametr &quot;{0}&quot; nebyl zadán.'.
        /// </summary>
        public static string MissingRequiredOption0 {
            get {
                return ResourceManager.GetString(ResourceNames.MissingRequiredOption0, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Pro parametr &quot;{0}&quot; nebyla zadána hodnota.'.
        /// </summary>
        public static string MissingRequiredValueForOption0 {
            get {
                return ResourceManager.GetString(ResourceNames.MissingRequiredValueForOption0, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Musí být zadán jeden z parametrů {0}.'.
        /// </summary>
        public static string OneOfTheOptions0MustBeSpecified {
            get {
                return ResourceManager.GetString(ResourceNames.OneOfTheOptions0MustBeSpecified, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to ' (rádek {0} v {1})'.
        /// </summary>
        public static string OnLine0InFile1 {
            get {
                return ResourceManager.GetString(ResourceNames.OnLine0InFile1, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Only {0} occurence(s) of option &quot;{1}&quot; found; it must be specified at least {2} time(s)'.
        /// </summary>
        public static string Only0OccurenceSOfOption1FoundItMustBeSpecifiedAtLeast2TimeS {
            get {
                return ResourceManager.GetString(ResourceNames.Only0OccurenceSOfOption1FoundItMustBeSpecifiedAtLeast2TimeS, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Only {0} occurence(s) of option &quot;{1}&quot; found; it must be specified exactly {2} time(s)'.
        /// </summary>
        public static string Only0OccurenceSOfOption1FoundItMustBeSpecifiedExactly2TimeS {
            get {
                return ResourceManager.GetString(ResourceNames.Only0OccurenceSOfOption1FoundItMustBeSpecifiedExactly2TimeS, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Musí být zadán pouze jeden z parametrů {0}.'.
        /// </summary>
        public static string OnlyOneOfTheOptions0MayBeSpecified {
            get {
                return ResourceManager.GetString(ResourceNames.OnlyOneOfTheOptions0MayBeSpecified, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Parametr &quot;{0}&quot; neumožnuje zadat hodnotu.'.
        /// </summary>
        public static string Option0DoesNotAcceptAValue {
            get {
                return ResourceManager.GetString(ResourceNames.Option0DoesNotAcceptAValue, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Parametr &quot;{0}&quot; nesmí být zadán zároveň s parametrem &quot;{1}&quot;.'.
        /// </summary>
        public static string Option0MayNotBeSpecifiedTogetherWithOption1 {
            get {
                return ResourceManager.GetString(ResourceNames.Option0MayNotBeSpecifiedTogetherWithOption1, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Parametr &quot;{0}&quot; nesmí být zadán více než {1} krát.'.
        /// </summary>
        public static string Option0MustNotBeSpecifiedMoreThan1Times {
            get {
                return ResourceManager.GetString(ResourceNames.Option0MustNotBeSpecifiedMoreThan1Times, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Parametr &quot;{0}&quot; nesmí být zadán vícekrát.'.
        /// </summary>
        public static string Option0MustNotBeSpecifiedMultipleTimes {
            get {
                return ResourceManager.GetString(ResourceNames.Option0MustNotBeSpecifiedMultipleTimes, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Parametry:'.
        /// </summary>
        public static string Options {
            get {
                return ResourceManager.GetString(ResourceNames.Options, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Opakovaná definice parametru s názvem &quot;{0}&quot; ({1})'.
        /// </summary>
        public static string RedefinitionOfCommandLineOptionWithParameterName01 {
            get {
                return ResourceManager.GetString(ResourceNames.RedefinitionOfCommandLineOptionWithParameterName01, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Opakovaná definice skupiny s id &quot;{0}&quot; ({1}).'.
        /// </summary>
        public static string RedefinitionOfGroupWithId01 {
            get {
                return ResourceManager.GetString(ResourceNames.RedefinitionOfGroupWithId01, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Cesta (&quot;{0}&quot;) je chybná.'.
        /// </summary>
        public static string TheSpecifiedPath0IsInvalid {
            get {
                return ResourceManager.GetString(ResourceNames.TheSpecifiedPath0IsInvalid, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Cesta (&quot;{0}&quot;) je příliš dlouhá.'.
        /// </summary>
        public static string TheSpecifiedPath0IsTooLong {
            get {
                return ResourceManager.GetString(ResourceNames.TheSpecifiedPath0IsTooLong, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Undefined option &quot;{0}&quot; referenced from prohibition section of option &quot;{1}&quot;'.
        /// </summary>
        public static string UndefinedOption0ReferencedFromProhibitionSectionOfOption1 {
            get {
                return ResourceManager.GetString(ResourceNames.UndefinedOption0ReferencedFromProhibitionSectionOfOption1, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Neznámý znak &apos;{0}&apos; na příkazové řádce.'.
        /// </summary>
        public static string Unexpected0CharacterOnCommandLine {
            get {
                return ResourceManager.GetString(ResourceNames.Unexpected0CharacterOnCommandLine, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Neočekávaný konec řetězce.'.
        /// </summary>
        public static string UnexpectedEndOfString {
            get {
                return ResourceManager.GetString(ResourceNames.UnexpectedEndOfString, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Neznámý parametr &quot;{0}&quot;.'.
        /// </summary>
        public static string UnknownOption0 {
            get {
                return ResourceManager.GetString(ResourceNames.UnknownOption0, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'používám jména nerozlišující velikost písma'.
        /// </summary>
        public static string UsingCaseInsensitiveNames {
            get {
                return ResourceManager.GetString(ResourceNames.UsingCaseInsensitiveNames, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'používám jména rozlišující velikost písma'.
        /// </summary>
        public static string UsingCaseSensitiveNames {
            get {
                return ResourceManager.GetString(ResourceNames.UsingCaseSensitiveNames, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Hodnota zadaná pro &quot;{0}&quot; ({3}) je mimo povolený rozsah. Je očekávána numerická hodnota v rozsahu od {1} do {2}.'.
        /// </summary>
        public static string ValueFor03OutOfRangeExpectedNumericBetween1And2 {
            get {
                return ResourceManager.GetString(ResourceNames.ValueFor03OutOfRangeExpectedNumericBetween1And2, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Hodnota je buď příliš veliká nebo příliš malá pro daný typ parametru.'.
        /// </summary>
        public static string ValueWasEitherTooLargeOrTooSmallForThisOptionType {
            get {
                return ResourceManager.GetString(ResourceNames.ValueWasEitherTooLargeOrTooSmallForThisOptionType, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'verze'.
        /// </summary>
        public static string Version {
            get {
                return ResourceManager.GetString(ResourceNames.Version, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'šířka nesmí být menší než {0}'.
        /// </summary>
        public static string WidthMustNotBeLessThan0 {
            get {
                return ResourceManager.GetString(ResourceNames.WidthMustNotBeLessThan0, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Přístup k souboru &quot;{0}&quot; byl odepřen.'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string AccessToTheSpecifiedFile0IsDeniedFormat(object arg0) {
            return string.Format(_resourceCulture, AccessToTheSpecifiedFile0IsDenied, arg0);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Alias &quot;{0}&quot; není platný pro volbu &quot;{1}&quot;; BoolFunction je nastavena na {2} je aktivní styl nastavení {3} který zakazuje jakékoliv parametry delší než jeden znak.'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <param name="arg1">An object (1) to format.</param>
        /// <param name="arg2">An object (2) to format.</param>
        /// <param name="arg3">An object (3) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1025:ReplaceRepetitiveArgumentsWithParamsArray")]
        public static string Alias0IsInvalidForOption1BoolFunctionIsSetTo2AndThe3OptionStyleIsEnabledWhichProhibitsAnyNameLongerThanOneCharacterFormat(object arg0, object arg1, object arg2, object arg3) {
            return string.Format(_resourceCulture, Alias0IsInvalidForOption1BoolFunctionIsSetTo2AndThe3OptionStyleIsEnabledWhichProhibitsAnyNameLongerThanOneCharacter, arg0, arg1, arg2, arg3);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Alias &quot;{0}&quot; je už použit jiným parametrem.'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string AliasName0IsAlreadyInUseByAnotherOptionFormat(object arg0) {
            return string.Format(_resourceCulture, AliasName0IsAlreadyInUseByAnotherOption, arg0);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Musí být zadány všechny parametry {0}.'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string AllOfTheOptions0MustBeSpecifiedFormat(object arg0) {
            return string.Format(_resourceCulture, AllOfTheOptions0MustBeSpecified, arg0);
        }
        
        /// <summary>
        /// Formats a localized string similar to '{0} nesmí být menší než {1}.'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <param name="arg1">An object (1) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string Arg0MustNotBeLessThan1Format(object arg0, object arg1) {
            return string.Format(_resourceCulture, Arg0MustNotBeLessThan1, arg0, arg1);
        }
        
        /// <summary>
        /// Formats a localized string similar to '{0} musí být větší než nula.'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string ArgMustBeGreaterThanZeroFormat(object arg0) {
            return string.Format(_resourceCulture, ArgMustBeGreaterThanZero, arg0);
        }
        
        /// <summary>
        /// Formats a localized string similar to '{0} nesmí být záporné číslo.'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string ArgMustBeNonNegativeFormat(object arg0) {
            return string.Format(_resourceCulture, ArgMustBeNonNegative, arg0);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Musí být zadán nejméně jeden parametr {0}.'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string AtLeastOneOfTheOption0MustBeSpecifiedFormat(object arg0) {
            return string.Format(_resourceCulture, AtLeastOneOfTheOption0MustBeSpecified, arg0);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Parametr {0} může být zadán maximálně jednou.'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string AtMostOneOfTheOptions0MayBeSpecifiedAtOnceFormat(object arg0) {
            return string.Format(_resourceCulture, AtMostOneOfTheOptions0MayBeSpecifiedAtOnce, arg0);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'BoolFunction can not be set to &apos;{0}&apos; for option with a name longer than one character when option style &apos;{1}&apos; is specified in the {2} attribute'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <param name="arg1">An object (1) to format.</param>
        /// <param name="arg2">An object (2) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string BoolFunctionCanNotBeSetTo0ForOptionWithANameLongerThanOneCharacterWhenOptionStyle1IsSpecifiedInThe2AttributeFormat(object arg0, object arg1, object arg2) {
            return string.Format(_resourceCulture, BoolFunctionCanNotBeSetTo0ForOptionWithANameLongerThanOneCharacterWhenOptionStyle1IsSpecifiedInThe2Attribute, arg0, arg1, arg2);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'BoolFunction may not be set to &apos;{0}&apos; when option style &apos;{1}&apos; is not specified in the {2} attribute'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <param name="arg1">An object (1) to format.</param>
        /// <param name="arg2">An object (2) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string BoolFunctionMayNotBeSetTo0WhenOptionStyle1IsNotSpecifiedInThe2AttributeFormat(object arg0, object arg1, object arg2) {
            return string.Format(_resourceCulture, BoolFunctionMayNotBeSetTo0WhenOptionStyle1IsNotSpecifiedInThe2Attribute, arg0, arg1, arg2);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'BoolFunction may not be set to &apos;{0}&apos; when option style &apos;{1}&apos; is specified in the {2} attribute'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <param name="arg1">An object (1) to format.</param>
        /// <param name="arg2">An object (2) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string BoolFunctionMayNotBeSetTo0WhenOptionStyle1IsSpecifiedInThe2AttributeFormat(object arg0, object arg1, object arg2) {
            return string.Format(_resourceCulture, BoolFunctionMayNotBeSetTo0WhenOptionStyle1IsSpecifiedInThe2Attribute, arg0, arg1, arg2);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Soubor &quot;{0}&quot; nebyl nalezen.'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string FileNotFound0Format(object arg0) {
            return string.Format(_resourceCulture, FileNotFound0, arg0);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Internal error; unimplemented BoolFunction {0} used in option &quot;{1}&quot;'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <param name="arg1">An object (1) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string InternalErrorUnimplementedBoolFunction0UsedInOption1Format(object arg0, object arg1) {
            return string.Format(_resourceCulture, InternalErrorUnimplementedBoolFunction0UsedInOption1, arg0, arg1);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Chybný prvek enumerace &quot;{0}&quot;.'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string InvalidEnumerationValue0Format(object arg0) {
            return string.Format(_resourceCulture, InvalidEnumerationValue0, arg0);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Chybná hodnota ({0}) pro parametr &quot;{1}&quot;.'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <param name="arg1">An object (1) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string InvalidValue0ForOption1Format(object arg0, object arg1) {
            return string.Format(_resourceCulture, InvalidValue0ForOption1, arg0, arg1);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Chybná hodnota &quot;{0}&quot; pro parametr &quot;{1}&quot;; Hodnota musí být jedna z {2}'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <param name="arg1">An object (1) to format.</param>
        /// <param name="arg2">An object (2) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string InvalidValue0ForOption1TheValueMustBeOneOf2Format(object arg0, object arg1, object arg2) {
            return string.Format(_resourceCulture, InvalidValue0ForOption1TheValueMustBeOneOf2, arg0, arg1, arg2);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'nebo &quot;{0}&quot;'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string LastItemOfExclusiveListFormat(object arg0) {
            return string.Format(_resourceCulture, LastItemOfExclusiveList, arg0);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Missing closing quote for quote command line value: {0}'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string MissingClosingQuoteForValue0Format(object arg0) {
            return string.Format(_resourceCulture, MissingClosingQuoteForValue0, arg0);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Vyžadovaný parametr &quot;{0}&quot; nebyl zadán.'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string MissingRequiredOption0Format(object arg0) {
            return string.Format(_resourceCulture, MissingRequiredOption0, arg0);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Pro parametr &quot;{0}&quot; nebyla zadána hodnota.'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string MissingRequiredValueForOption0Format(object arg0) {
            return string.Format(_resourceCulture, MissingRequiredValueForOption0, arg0);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Musí být zadán jeden z parametrů {0}.'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string OneOfTheOptions0MustBeSpecifiedFormat(object arg0) {
            return string.Format(_resourceCulture, OneOfTheOptions0MustBeSpecified, arg0);
        }
        
        /// <summary>
        /// Formats a localized string similar to ' (rádek {0} v {1})'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <param name="arg1">An object (1) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string OnLine0InFile1Format(object arg0, object arg1) {
            return string.Format(_resourceCulture, OnLine0InFile1, arg0, arg1);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Only {0} occurence(s) of option &quot;{1}&quot; found; it must be specified at least {2} time(s)'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <param name="arg1">An object (1) to format.</param>
        /// <param name="arg2">An object (2) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string Only0OccurenceSOfOption1FoundItMustBeSpecifiedAtLeast2TimeSFormat(object arg0, object arg1, object arg2) {
            return string.Format(_resourceCulture, Only0OccurenceSOfOption1FoundItMustBeSpecifiedAtLeast2TimeS, arg0, arg1, arg2);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Only {0} occurence(s) of option &quot;{1}&quot; found; it must be specified exactly {2} time(s)'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <param name="arg1">An object (1) to format.</param>
        /// <param name="arg2">An object (2) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string Only0OccurenceSOfOption1FoundItMustBeSpecifiedExactly2TimeSFormat(object arg0, object arg1, object arg2) {
            return string.Format(_resourceCulture, Only0OccurenceSOfOption1FoundItMustBeSpecifiedExactly2TimeS, arg0, arg1, arg2);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Musí být zadán pouze jeden z parametrů {0}.'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string OnlyOneOfTheOptions0MayBeSpecifiedFormat(object arg0) {
            return string.Format(_resourceCulture, OnlyOneOfTheOptions0MayBeSpecified, arg0);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Parametr &quot;{0}&quot; neumožnuje zadat hodnotu.'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string Option0DoesNotAcceptAValueFormat(object arg0) {
            return string.Format(_resourceCulture, Option0DoesNotAcceptAValue, arg0);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Parametr &quot;{0}&quot; nesmí být zadán zároveň s parametrem &quot;{1}&quot;.'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <param name="arg1">An object (1) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string Option0MayNotBeSpecifiedTogetherWithOption1Format(object arg0, object arg1) {
            return string.Format(_resourceCulture, Option0MayNotBeSpecifiedTogetherWithOption1, arg0, arg1);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Parametr &quot;{0}&quot; nesmí být zadán více než {1} krát.'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <param name="arg1">An object (1) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string Option0MustNotBeSpecifiedMoreThan1TimesFormat(object arg0, object arg1) {
            return string.Format(_resourceCulture, Option0MustNotBeSpecifiedMoreThan1Times, arg0, arg1);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Parametr &quot;{0}&quot; nesmí být zadán vícekrát.'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string Option0MustNotBeSpecifiedMultipleTimesFormat(object arg0) {
            return string.Format(_resourceCulture, Option0MustNotBeSpecifiedMultipleTimes, arg0);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Opakovaná definice parametru s názvem &quot;{0}&quot; ({1})'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <param name="arg1">An object (1) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string RedefinitionOfCommandLineOptionWithParameterName01Format(object arg0, object arg1) {
            return string.Format(_resourceCulture, RedefinitionOfCommandLineOptionWithParameterName01, arg0, arg1);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Opakovaná definice skupiny s id &quot;{0}&quot; ({1}).'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <param name="arg1">An object (1) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string RedefinitionOfGroupWithId01Format(object arg0, object arg1) {
            return string.Format(_resourceCulture, RedefinitionOfGroupWithId01, arg0, arg1);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Cesta (&quot;{0}&quot;) je chybná.'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string TheSpecifiedPath0IsInvalidFormat(object arg0) {
            return string.Format(_resourceCulture, TheSpecifiedPath0IsInvalid, arg0);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Cesta (&quot;{0}&quot;) je příliš dlouhá.'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string TheSpecifiedPath0IsTooLongFormat(object arg0) {
            return string.Format(_resourceCulture, TheSpecifiedPath0IsTooLong, arg0);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Undefined option &quot;{0}&quot; referenced from prohibition section of option &quot;{1}&quot;'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <param name="arg1">An object (1) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string UndefinedOption0ReferencedFromProhibitionSectionOfOption1Format(object arg0, object arg1) {
            return string.Format(_resourceCulture, UndefinedOption0ReferencedFromProhibitionSectionOfOption1, arg0, arg1);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Neznámý znak &apos;{0}&apos; na příkazové řádce.'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string Unexpected0CharacterOnCommandLineFormat(object arg0) {
            return string.Format(_resourceCulture, Unexpected0CharacterOnCommandLine, arg0);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Neznámý parametr &quot;{0}&quot;.'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string UnknownOption0Format(object arg0) {
            return string.Format(_resourceCulture, UnknownOption0, arg0);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Hodnota zadaná pro &quot;{0}&quot; ({3}) je mimo povolený rozsah. Je očekávána numerická hodnota v rozsahu od {1} do {2}.'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <param name="arg1">An object (1) to format.</param>
        /// <param name="arg2">An object (2) to format.</param>
        /// <param name="arg3">An object (3) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1025:ReplaceRepetitiveArgumentsWithParamsArray")]
        public static string ValueFor03OutOfRangeExpectedNumericBetween1And2Format(object arg0, object arg1, object arg2, object arg3) {
            return string.Format(_resourceCulture, ValueFor03OutOfRangeExpectedNumericBetween1And2, arg0, arg1, arg2, arg3);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'šířka nesmí být menší než {0}'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string WidthMustNotBeLessThan0Format(object arg0) {
            return string.Format(_resourceCulture, WidthMustNotBeLessThan0, arg0);
        }
        
        /// <summary>
        /// Lists all the resource names as constant string fields.
        /// </summary>
        public class ResourceNames {
            
            /// <summary>
            /// Stores the resource name 'AccessToTheSpecifiedFile0IsDenied'.
            /// </summary>
            public const string AccessToTheSpecifiedFile0IsDenied = "AccessToTheSpecifiedFile0IsDenied";
            
            /// <summary>
            /// Stores the resource name 'Alias0IsInvalidForOption1BoolFunctionIsSetTo2AndThe3OptionStyleIsEnabledWhichProhibitsAnyNameLongerThanOneCharacter'.
            /// </summary>
            public const string Alias0IsInvalidForOption1BoolFunctionIsSetTo2AndThe3OptionStyleIsEnabledWhichProhibitsAnyNameLongerThanOneCharacter = "Alias0IsInvalidForOption1BoolFunctionIsSetTo2AndThe3OptionStyleIsEnabledWhichProh" +
                "ibitsAnyNameLongerThanOneCharacter";
            
            /// <summary>
            /// Stores the resource name 'AliasName0IsAlreadyInUseByAnotherOption'.
            /// </summary>
            public const string AliasName0IsAlreadyInUseByAnotherOption = "AliasName0IsAlreadyInUseByAnotherOption";
            
            /// <summary>
            /// Stores the resource name 'AllOfTheOptions0MustBeSpecified'.
            /// </summary>
            public const string AllOfTheOptions0MustBeSpecified = "AllOfTheOptions0MustBeSpecified";
            
            /// <summary>
            /// Stores the resource name 'Arg0MustNotBeLessThan1'.
            /// </summary>
            public const string Arg0MustNotBeLessThan1 = "Arg0MustNotBeLessThan1";
            
            /// <summary>
            /// Stores the resource name 'ArgMustBeGreaterThanZero'.
            /// </summary>
            public const string ArgMustBeGreaterThanZero = "ArgMustBeGreaterThanZero";
            
            /// <summary>
            /// Stores the resource name 'ArgMustBeNonNegative'.
            /// </summary>
            public const string ArgMustBeNonNegative = "ArgMustBeNonNegative";
            
            /// <summary>
            /// Stores the resource name 'AssignmentCharactersMustNotBeChangedWhileParseInProgress'.
            /// </summary>
            public const string AssignmentCharactersMustNotBeChangedWhileParseInProgress = "AssignmentCharactersMustNotBeChangedWhileParseInProgress";
            
            /// <summary>
            /// Stores the resource name 'AtLeastOneOfTheOption0MustBeSpecified'.
            /// </summary>
            public const string AtLeastOneOfTheOption0MustBeSpecified = "AtLeastOneOfTheOption0MustBeSpecified";
            
            /// <summary>
            /// Stores the resource name 'AtMostOneOfTheOptions0MayBeSpecifiedAtOnce'.
            /// </summary>
            public const string AtMostOneOfTheOptions0MayBeSpecifiedAtOnce = "AtMostOneOfTheOptions0MayBeSpecifiedAtOnce";
            
            /// <summary>
            /// Stores the resource name 'BoolFunctionCanNotBeSetTo0ForOptionWithANameLongerThanOneCharacterWhenOptionStyle1IsSpecifiedInThe2Attribute'.
            /// </summary>
            public const string BoolFunctionCanNotBeSetTo0ForOptionWithANameLongerThanOneCharacterWhenOptionStyle1IsSpecifiedInThe2Attribute = "BoolFunctionCanNotBeSetTo0ForOptionWithANameLongerThanOneCharacterWhenOptionStyle" +
                "1IsSpecifiedInThe2Attribute";
            
            /// <summary>
            /// Stores the resource name 'BoolFunctionMayNotBeSetTo0WhenOptionStyle1IsNotSpecifiedInThe2Attribute'.
            /// </summary>
            public const string BoolFunctionMayNotBeSetTo0WhenOptionStyle1IsNotSpecifiedInThe2Attribute = "BoolFunctionMayNotBeSetTo0WhenOptionStyle1IsNotSpecifiedInThe2Attribute";
            
            /// <summary>
            /// Stores the resource name 'BoolFunctionMayNotBeSetTo0WhenOptionStyle1IsSpecifiedInThe2Attribute'.
            /// </summary>
            public const string BoolFunctionMayNotBeSetTo0WhenOptionStyle1IsSpecifiedInThe2Attribute = "BoolFunctionMayNotBeSetTo0WhenOptionStyle1IsSpecifiedInThe2Attribute";
            
            /// <summary>
            /// Stores the resource name 'DefaultCommandLineExceptionErrorMessage'.
            /// </summary>
            public const string DefaultCommandLineExceptionErrorMessage = "DefaultCommandLineExceptionErrorMessage";
            
            /// <summary>
            /// Stores the resource name 'DefaultMissingOptionNameExceptionMessage'.
            /// </summary>
            public const string DefaultMissingOptionNameExceptionMessage = "DefaultMissingOptionNameExceptionMessage";
            
            /// <summary>
            /// Stores the resource name 'EmptyOptionNameIsNotAllowed'.
            /// </summary>
            public const string EmptyOptionNameIsNotAllowed = "EmptyOptionNameIsNotAllowed";
            
            /// <summary>
            /// Stores the resource name 'EndOfInput'.
            /// </summary>
            public const string EndOfInput = "EndOfInput";
            
            /// <summary>
            /// Stores the resource name 'ErrorList'.
            /// </summary>
            public const string ErrorList = "ErrorList";
            
            /// <summary>
            /// Stores the resource name 'FileNotFound0'.
            /// </summary>
            public const string FileNotFound0 = "FileNotFound0";
            
            /// <summary>
            /// Stores the resource name 'InternalErrorNonImplementedGroupRequirementSpecified'.
            /// </summary>
            public const string InternalErrorNonImplementedGroupRequirementSpecified = "InternalErrorNonImplementedGroupRequirementSpecified";
            
            /// <summary>
            /// Stores the resource name 'InternalErrorOptionSpecifiedInProhibitionDoesNotExist'.
            /// </summary>
            public const string InternalErrorOptionSpecifiedInProhibitionDoesNotExist = "InternalErrorOptionSpecifiedInProhibitionDoesNotExist";
            
            /// <summary>
            /// Stores the resource name 'InternalErrorOptionValueWasSetMoreThanMaxOccursNumberOfTimes'.
            /// </summary>
            public const string InternalErrorOptionValueWasSetMoreThanMaxOccursNumberOfTimes = "InternalErrorOptionValueWasSetMoreThanMaxOccursNumberOfTimes";
            
            /// <summary>
            /// Stores the resource name 'InternalErrorUnimplementedBoolFunction0UsedInOption1'.
            /// </summary>
            public const string InternalErrorUnimplementedBoolFunction0UsedInOption1 = "InternalErrorUnimplementedBoolFunction0UsedInOption1";
            
            /// <summary>
            /// Stores the resource name 'InternalErrorUnimplementedTokenTypeReturnedToCommandLineParser'.
            /// </summary>
            public const string InternalErrorUnimplementedTokenTypeReturnedToCommandLineParser = "InternalErrorUnimplementedTokenTypeReturnedToCommandLineParser";
            
            /// <summary>
            /// Stores the resource name 'InternalErrorUnimplementedVerticalAlignmentUsed'.
            /// </summary>
            public const string InternalErrorUnimplementedVerticalAlignmentUsed = "InternalErrorUnimplementedVerticalAlignmentUsed";
            
            /// <summary>
            /// Stores the resource name 'InvalidEnumerationValue0'.
            /// </summary>
            public const string InvalidEnumerationValue0 = "InvalidEnumerationValue0";
            
            /// <summary>
            /// Stores the resource name 'InvalidIdOfGroupIdMustNotBeNullOrEmpty'.
            /// </summary>
            public const string InvalidIdOfGroupIdMustNotBeNullOrEmpty = "InvalidIdOfGroupIdMustNotBeNullOrEmpty";
            
            /// <summary>
            /// Stores the resource name 'InvalidValue0ForOption1'.
            /// </summary>
            public const string InvalidValue0ForOption1 = "InvalidValue0ForOption1";
            
            /// <summary>
            /// Stores the resource name 'InvalidValue0ForOption1TheValueMustBeOneOf2'.
            /// </summary>
            public const string InvalidValue0ForOption1TheValueMustBeOneOf2 = "InvalidValue0ForOption1TheValueMustBeOneOf2";
            
            /// <summary>
            /// Stores the resource name 'LastItemOfExclusiveList'.
            /// </summary>
            public const string LastItemOfExclusiveList = "LastItemOfExclusiveList";
            
            /// <summary>
            /// Stores the resource name 'MissingClosingQuote'.
            /// </summary>
            public const string MissingClosingQuote = "MissingClosingQuote";
            
            /// <summary>
            /// Stores the resource name 'MissingClosingQuoteForQuotedValue'.
            /// </summary>
            public const string MissingClosingQuoteForQuotedValue = "MissingClosingQuoteForQuotedValue";
            
            /// <summary>
            /// Stores the resource name 'MissingClosingQuoteForValue0'.
            /// </summary>
            public const string MissingClosingQuoteForValue0 = "MissingClosingQuoteForValue0";
            
            /// <summary>
            /// Stores the resource name 'MissingRequiredAttributeForACommandLineManagerObjectCommandLineManagerAttribute'.
            /// </summary>
            public const string MissingRequiredAttributeForACommandLineManagerObjectCommandLineManagerAttribute = "MissingRequiredAttributeForACommandLineManagerObjectCommandLineManagerAttribute";
            
            /// <summary>
            /// Stores the resource name 'MissingRequiredOption0'.
            /// </summary>
            public const string MissingRequiredOption0 = "MissingRequiredOption0";
            
            /// <summary>
            /// Stores the resource name 'MissingRequiredValueForOption0'.
            /// </summary>
            public const string MissingRequiredValueForOption0 = "MissingRequiredValueForOption0";
            
            /// <summary>
            /// Stores the resource name 'OneOfTheOptions0MustBeSpecified'.
            /// </summary>
            public const string OneOfTheOptions0MustBeSpecified = "OneOfTheOptions0MustBeSpecified";
            
            /// <summary>
            /// Stores the resource name 'OnLine0InFile1'.
            /// </summary>
            public const string OnLine0InFile1 = "OnLine0InFile1";
            
            /// <summary>
            /// Stores the resource name 'Only0OccurenceSOfOption1FoundItMustBeSpecifiedAtLeast2TimeS'.
            /// </summary>
            public const string Only0OccurenceSOfOption1FoundItMustBeSpecifiedAtLeast2TimeS = "Only0OccurenceSOfOption1FoundItMustBeSpecifiedAtLeast2TimeS";
            
            /// <summary>
            /// Stores the resource name 'Only0OccurenceSOfOption1FoundItMustBeSpecifiedExactly2TimeS'.
            /// </summary>
            public const string Only0OccurenceSOfOption1FoundItMustBeSpecifiedExactly2TimeS = "Only0OccurenceSOfOption1FoundItMustBeSpecifiedExactly2TimeS";
            
            /// <summary>
            /// Stores the resource name 'OnlyOneOfTheOptions0MayBeSpecified'.
            /// </summary>
            public const string OnlyOneOfTheOptions0MayBeSpecified = "OnlyOneOfTheOptions0MayBeSpecified";
            
            /// <summary>
            /// Stores the resource name 'Option0DoesNotAcceptAValue'.
            /// </summary>
            public const string Option0DoesNotAcceptAValue = "Option0DoesNotAcceptAValue";
            
            /// <summary>
            /// Stores the resource name 'Option0MayNotBeSpecifiedTogetherWithOption1'.
            /// </summary>
            public const string Option0MayNotBeSpecifiedTogetherWithOption1 = "Option0MayNotBeSpecifiedTogetherWithOption1";
            
            /// <summary>
            /// Stores the resource name 'Option0MustNotBeSpecifiedMoreThan1Times'.
            /// </summary>
            public const string Option0MustNotBeSpecifiedMoreThan1Times = "Option0MustNotBeSpecifiedMoreThan1Times";
            
            /// <summary>
            /// Stores the resource name 'Option0MustNotBeSpecifiedMultipleTimes'.
            /// </summary>
            public const string Option0MustNotBeSpecifiedMultipleTimes = "Option0MustNotBeSpecifiedMultipleTimes";
            
            /// <summary>
            /// Stores the resource name 'Options'.
            /// </summary>
            public const string Options = "Options";
            
            /// <summary>
            /// Stores the resource name 'RedefinitionOfCommandLineOptionWithParameterName01'.
            /// </summary>
            public const string RedefinitionOfCommandLineOptionWithParameterName01 = "RedefinitionOfCommandLineOptionWithParameterName01";
            
            /// <summary>
            /// Stores the resource name 'RedefinitionOfGroupWithId01'.
            /// </summary>
            public const string RedefinitionOfGroupWithId01 = "RedefinitionOfGroupWithId01";
            
            /// <summary>
            /// Stores the resource name 'TheSpecifiedPath0IsInvalid'.
            /// </summary>
            public const string TheSpecifiedPath0IsInvalid = "TheSpecifiedPath0IsInvalid";
            
            /// <summary>
            /// Stores the resource name 'TheSpecifiedPath0IsTooLong'.
            /// </summary>
            public const string TheSpecifiedPath0IsTooLong = "TheSpecifiedPath0IsTooLong";
            
            /// <summary>
            /// Stores the resource name 'UndefinedOption0ReferencedFromProhibitionSectionOfOption1'.
            /// </summary>
            public const string UndefinedOption0ReferencedFromProhibitionSectionOfOption1 = "UndefinedOption0ReferencedFromProhibitionSectionOfOption1";
            
            /// <summary>
            /// Stores the resource name 'Unexpected0CharacterOnCommandLine'.
            /// </summary>
            public const string Unexpected0CharacterOnCommandLine = "Unexpected0CharacterOnCommandLine";
            
            /// <summary>
            /// Stores the resource name 'UnexpectedEndOfString'.
            /// </summary>
            public const string UnexpectedEndOfString = "UnexpectedEndOfString";
            
            /// <summary>
            /// Stores the resource name 'UnknownOption0'.
            /// </summary>
            public const string UnknownOption0 = "UnknownOption0";
            
            /// <summary>
            /// Stores the resource name 'UsingCaseInsensitiveNames'.
            /// </summary>
            public const string UsingCaseInsensitiveNames = "UsingCaseInsensitiveNames";
            
            /// <summary>
            /// Stores the resource name 'UsingCaseSensitiveNames'.
            /// </summary>
            public const string UsingCaseSensitiveNames = "UsingCaseSensitiveNames";
            
            /// <summary>
            /// Stores the resource name 'ValueFor03OutOfRangeExpectedNumericBetween1And2'.
            /// </summary>
            public const string ValueFor03OutOfRangeExpectedNumericBetween1And2 = "ValueFor03OutOfRangeExpectedNumericBetween1And2";
            
            /// <summary>
            /// Stores the resource name 'ValueWasEitherTooLargeOrTooSmallForThisOptionType'.
            /// </summary>
            public const string ValueWasEitherTooLargeOrTooSmallForThisOptionType = "ValueWasEitherTooLargeOrTooSmallForThisOptionType";
            
            /// <summary>
            /// Stores the resource name 'Version'.
            /// </summary>
            public const string Version = "Version";
            
            /// <summary>
            /// Stores the resource name 'WidthMustNotBeLessThan0'.
            /// </summary>
            public const string WidthMustNotBeLessThan0 = "WidthMustNotBeLessThan0";
        }
    }
}
