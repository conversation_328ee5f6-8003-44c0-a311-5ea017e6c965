<?xml version="1.0" encoding="utf-8"?>
<EntityDeveloper Version="*********">
  <ModelSettings xmlns:ed="http://devart.com/schemas/EntityDeveloper/1.0" ConnectionStringInAppConfig="False" TargetFramework="Net45" DetectManyToManyAssociations="False" DetectTPTInheritance="False" UseDatabaseComments="False" DefaultUnicode="True" IncludeForeignKeysInModel="true">
    <Connection ConnectionString="Data Source=pkremlacek\sql19;Initial Catalog=Kredit_UTB;Integrated Security=False;Persist Security Info=True;User ID=sa;Password=*****" Provider="System.Data.SqlClient" />
    <Generation>
      <GeneratedFiles>
        <File Name="ClientsModel.Methods.Client.cs" />
        <File Name="ClientsModel.Methods.Canteen.cs" />
        <File Name="ClientsModel.Methods.ClientRegister.cs" />
        <File Name="ClientsModel.Methods.Organization.cs" />
        <File Name="ClientsModel.Methods.Resort.cs" />
        <File Name="ClientsModel.Methods.AccountingDepartment.cs" />
        <File Name="ClientsModel.Methods.ClientGroup.cs" />
        <File Name="ClientsModel.Methods.ClientRegisterHistory.cs" />
        <File Name="ClientsModel.Methods.Card.cs" />
        <File Name="ClientsModel.Methods.CardMovement.cs" />
        <File Name="ClientsModel.Methods.CardMovementTotals.cs" />
        <File Name="ClientsModel.Methods.Account.cs" />
        <File Name="ClientsModel.Methods.ProviderGroupCategory.cs" />
        <File Name="ClientsModel.Methods.Provider.cs" />
        <File Name="ClientsModel.Methods.Workplace.cs" />
        <File Name="ClientsModel.Methods.ActivityLog.cs" />
        <File Name="ClientsModel.Methods.ClientContact.cs" />
        <File Name="ClientsModel.Methods.CardCode.cs" />
        <File Name="ClientsModel.Methods.AwaitingTransaction.cs" />
        <File Name="ClientsModel.Methods.CanteenAccessCategory.cs" />
        <File Name="ClientsModel.Methods.Language.cs" />
        <File Name="ClientsModel.Methods.AppInstallation.cs" />
        <File Name="ClientsModel.Methods.CashMovement.cs" />
        <File Name="ClientsModel.Methods.CashBalance.cs" />
        <File Name="ClientsModel.Methods.AccountBalance.cs" />
        <File Name="ClientsModel.Methods.ClientBalance.cs" />
        <File Name="ClientsModel.Methods.ClientBalanceReceipt.cs" />
        <File Name="ClientsModel.Methods.Order.cs" />
        <File Name="ClientsModel.Methods.GoodsGroup.cs" />
        <File Name="ClientsModel.Methods.EBankingItem.cs" />
        <File Name="ClientsModel.Methods.AccountMovement.cs" />
        <File Name="ClientsModel.Methods.EBankingBatch.cs" />
        <File Name="ClientsModel.Methods.VatGroup.cs" />
        <File Name="ClientsModel.Methods.Goods.cs" />
        <File Name="ClientsModel.Methods.AccountTemplate.cs" />
        <File Name="ClientsModel.Methods.MealKind.cs" />
        <File Name="ClientsModel.Methods.BatchOrderTemplate.cs" />
        <File Name="ClientsModel.Methods.BatchOrderTemplateRow.cs" />
        <File Name="ClientsModel.Methods.ClientBatchOrderTemplate.cs" />
        <File Name="ClientsModel.Methods.ClientBatchOrderTemplateRow.cs" />
        <File Name="ClientsModel.Methods.BatchOrder.cs" />
        <File Name="ClientsModel.Methods.Licence.cs" />
        <File Name="ClientsModel.Methods.Server.cs" />
        <File Name="ClientsModel.Methods.MenuGroupName.cs" />
        <File Name="ClientsModel.Methods.ApplicationFunc.cs" />
        <File Name="ClientsModel.Methods.DeviceControllerSet.cs" />
        <File Name="ClientsModel.Methods.ServingSchema.cs" />
        <File Name="ClientsModel.Methods.OrderingRule.cs" />
        <File Name="ClientsModel.Methods.Pictogram.cs" />
        <File Name="ClientsModel.Methods.MenuRow.cs" />
        <File Name="ClientsModel.Methods.MenuTemplate.cs" />
        <File Name="ClientsModel.Methods.MenuTemplateRow.cs" />
        <File Name="ClientsModel.Methods.Menu.cs" />
        <File Name="ClientsModel.Methods.MealTemplate.cs" />
        <File Name="ClientsModel.Methods.MenuRowLimitForCanteen.cs" />
        <File Name="ClientsModel.Methods.Currency.cs" />
        <File Name="ClientsModel.Methods.CurrencyType.cs" />
        <File Name="ClientsModel.Methods.DeviceController.cs" />
        <File Name="ClientsModel.Methods.NumericSeries.cs" />
        <File Name="ClientsModel.Methods.SystemCard.cs" />
        <File Name="ClientsModel.Methods.ProvidedService.cs" />
        <File Name="ClientsModel.Methods.Stock.cs" />
        <File Name="ClientsModel.Methods.DataExpiration.cs" />
        <File Name="ClientsModel.Methods.ForeignCurrency.cs" />
        <File Name="ClientsModel.Methods.Section.cs" />
        <File Name="ClientsModel.Methods.VatGroupFull.cs" />
        <File Name="ClientsModel.Methods.VatRate.cs" />
        <File Name="ClientsModel.Methods.ExportDef.cs" />
        <File Name="ClientsModel.Methods.ExportDefColumn.cs" />
        <File Name="ClientsModel.Methods.ExportClass.cs" />
        <File Name="ClientsModel.Methods.ExportClassColumn.cs" />
        <File Name="ClientsModel.Methods.ServingTime.cs" />
        <File Name="ClientsModel.Methods.OrderingSchema.cs" />
        <File Name="ClientsModel.Methods.VendingMachine.cs" />
        <File Name="ClientsModel.Methods.SubsidyCategory.cs" />
        <File Name="ClientsModel.Methods.FinancialSource.cs" />
        <File Name="ClientsModel.Methods.DayAccessCategory.cs" />
        <File Name="ClientsModel.Methods.DeviceControllerSetAccessCategory.cs" />
        <File Name="ClientsModel.Methods.AgeGroup.cs" />
        <File Name="ClientsModel.Methods.MenuCategory.cs" />
        <File Name="ClientsModel.Methods.MenuCategoryRestriction.cs" />
        <File Name="ClientsModel.Methods.MenuGroup.cs" />
        <File Name="ClientsModel.Methods.PriceElementName.cs" />
        <File Name="ClientsModel.Methods.CanteenOperationLockout.cs" />
        <File Name="ClientsModel.Methods.MonthRestriction.cs" />
        <File Name="ClientsModel.Methods.MagionAccountCoding1.cs" />
        <File Name="ClientsModel.Methods.MagionAccountCoding3.cs" />
        <File Name="ClientsModel.Methods.MagionEarningsResort.cs" />
        <File Name="ClientsModel.Methods.PriceCategory.cs" />
        <File Name="ClientsModel.Methods.JasuAccountCoding.cs" />
        <File Name="ClientsModel.Methods.JasuResort.cs" />
        <File Name="ClientsModel.Methods.Diet.cs" />
        <File Name="ClientsModel.Methods.MealRule.cs" />
        <File Name="ClientsModel.Methods.MealRuleComposition.cs" />
        <File Name="ClientsModel.Methods.SvoWorkplace.cs" />
        <File Name="ClientsModel.Methods.SvoWorkplaceReplacement.cs" />
        <File Name="ClientsModel.Methods.SvoShiftType.cs" />
        <File Name="ClientsModel.Methods.SvoShiftAddition.cs" />
        <File Name="ClientsModel.Methods.Unit.cs" />
        <File Name="ClientsModel.Methods.Operator.cs" />
        <File Name="ClientsModel.Methods.Floor.cs" />
        <File Name="ClientsModel.Methods.Room.cs" />
        <File Name="ClientsModel.Methods.Holiday.cs" />
        <File Name="ClientsModel.Methods.Price.cs" />
        <File Name="ClientsModel.Methods.BatchPrint.cs" />
        <File Name="ClientsModel.Methods.BatchPrintItem.cs" />
        <File Name="ClientsModel.Methods.KreditEvent.cs" />
        <File Name="ClientsModel.Methods.AccountBalanceDetail.cs" />
        <File Name="ClientsModel.Methods.CostRecap.cs" />
        <File Name="ClientsModel.Methods.ApplicationRole.cs" />
        <File Name="ClientsModel.Methods.User.cs" />
        <File Name="ClientsModel.Methods.ClientBalanceDetail.cs" />
        <File Name="ClientsModel.Methods.ServingPeriod.cs" />
        <File Name="ClientsModel.Methods.KreditMessage.cs" />
        <File Name="ClientsModel.Methods.LogAction.cs" />
        <File Name="ClientsModel.Methods.ZrdLog.cs" />
        <File Name="ClientsModel.Methods.Attendance.cs" />
        <File Name="ClientsModel.Methods.KpsNotifyUser.cs" />
        <File Name="ClientsModel.Methods.KpsClientReadMessages.cs" />
        <File Name="ClientsModel.Methods.KpsClientSettings.cs" />
        <File Name="ClientsModel.Methods.KpsMessageType.cs" />
        <File Name="ClientsModel.Methods.KpsMessage.cs" />
        <File Name="ClientsModel.Methods.Cashier.cs" />
        <File Name="ClientsModel.Methods.KpsUserReadMessage.cs" />
        <File Name="ClientsModel.Methods.CanceledAccountsCompensation.cs" />
        <File Name="ClientsModel.Methods.MessageForAppInstallation.cs" />
        <File Name="ClientsModel.Methods.GoodsBarCode.cs" />
        <File Name="ClientsModel.Methods.GoodsProfitMargin.cs" />
        <File Name="ClientsModel.Methods.GoodsPrice.cs" />
        <File Name="ClientsModel.Methods.GoodsConnection.cs" />
        <File Name="ClientsModel.Methods.ExchangeRate.cs" />
        <File Name="ClientsModel.Methods.SalesSlip.cs" />
        <File Name="ClientsModel.Methods.SalesSlipRow.cs" />
        <File Name="ClientsModel.Methods.GoodsGroupPriceCategoryMargins.cs" />
        <File Name="ClientsModel.Methods.HsHotelAccount.cs" />
        <File Name="ClientsModel.Methods.HsRoomRental.cs" />
        <File Name="ClientsModel.Methods.HsRoom.cs" />
        <File Name="ClientsModel.Methods.HsRoomType.cs" />
        <File Name="ClientsModel.Methods.HsPerson.cs" />
        <File Name="ClientsModel.Methods.HsHotelAccountExt.cs" />
        <File Name="ClientsModel.Methods.HsRoomOccupancy.cs" />
        <File Name="ClientsModel.Methods.HsCountry.cs" />
        <File Name="ClientsModel.Methods.PriceDiscount.cs" />
        <File Name="ClientsModel.Methods.SubsidyCategoryPrice.cs" />
        <File Name="ClientsModel.Methods.ProfitAccountCoding.cs" />
        <File Name="ClientsModel.Methods.OrderPrice.cs" />
        <File Name="ClientsModel.Methods.CashDeskCardPayment.cs" />
        <File Name="ClientsModel.Methods.CashDeskAlternateLogin.cs" />
        <File Name="ClientsModel.Methods.PriceDiscountForMeal.cs" />
        <File Name="ClientsModel.Methods.PriceDiscountInterval.cs" />
        <File Name="ClientsModel.Methods.MealRestriction.cs" />
        <File Name="ClientsModel.Methods.RADSubLogin.cs" />
        <File Name="ClientsModel.Methods.Driver.cs" />
        <File Name="ClientsModel.Methods.CurrencyTypeProperties.cs" />
        <File Name="ClientsModel.Methods.Cook.cs" />
        <File Name="ClientsModel.Methods.Vehicle.cs" />
        <File Name="ClientsModel.Methods.Delivery.cs" />
        <File Name="ClientsModel.Methods.Inquiry.cs" />
        <File Name="ClientsModel.Methods.InquiryRow.cs" />
        <File Name="ClientsModel.Methods.MealTypeRow.cs" />
        <File Name="ClientsModel.Methods.MealType.cs" />
        <File Name="ClientsModel.Methods.Allergen.cs" />
        <File Name="ClientsModel.Methods.PredefinedNote.cs" />
        <File Name="ClientsModel.Methods.OrganizationAddress.cs" />
        <File Name="ClientsModel.Methods.DeliveryOrder.cs" />
        <File Name="ClientsModel.Methods.ClientRegisterExtInfo.cs" />
        <File Name="ClientsModel.Methods.TouchGroup.cs" />
        <File Name="ClientsModel.Methods.TouchGoods.cs" />
        <File Name="ClientsModel.Methods.ClientRegisterHistoryRange.cs" />
        <File Name="ClientsModel.Methods.Contact.cs" />
        <File Name="ClientsModel.Methods.OrganizationContacts.cs" />
        <File Name="ClientsModel.Methods.DeliveryOrderRow.cs" />
        <File Name="ClientsModel.Methods.MenuRowDetail.cs" />
        <File Name="ClientsModel.Methods.MenuRowCanteenPrintOn.cs" />
        <File Name="ClientsModel.Methods.CampaignClientAnswer.cs" />
        <File Name="ClientsModel.Methods.Campaign.cs" />
        <File Name="ClientsModel.Methods.CampaignQuestions.cs" />
        <File Name="ClientsModel.Methods.CampaignQuestionAnswer.cs" />
        <File Name="ClientsModel.Methods.CampaignQuestion.cs" />
        <File Name="ClientsModel.Methods.MenuRowNutritionalValue.cs" />
        <File Name="ClientsModel.Methods.NutritionalValue.cs" />
        <File Name="ClientsModel.Methods.NutritionalValueLng.cs" />
        <File Name="ClientsModel.Methods.LocalizedNutritionalValue.cs" />
        <File Name="ClientsModel.Methods.CashDeskPaymentOrder.cs" />
        <File Name="ClientsModel.Methods.SvoClientRegisterExt.cs" />
        <File Name="ClientsModel.Methods.ClientMealRule.cs" />
        <File Name="ClientsModel.Methods.MenuRowEan.cs" />
        <File Name="ClientsModel.Methods.AccGroupHierarchy.cs" />
        <File Name="ClientsModel.Methods.AccGroup.cs" />
        <File Name="ClientsModel.Methods.AccGroupRole.cs" />
        <File Name="ClientsModel.Methods.AccInstallation.cs" />
        <File Name="ClientsModel.Methods.AccInstallationRole.cs" />
        <File Name="ClientsModel.Methods.AccRightCode.cs" />
        <File Name="ClientsModel.Methods.AccRightGroup.cs" />
        <File Name="ClientsModel.Methods.AccRight.cs" />
        <File Name="ClientsModel.Methods.AccRightDefaultParameterValue.cs" />
        <File Name="ClientsModel.Methods.AccRightCodeParameterValue.cs" />
        <File Name="ClientsModel.Methods.AccRightCodeParameter.cs" />
        <File Name="ClientsModel.Methods.AccParameter.cs" />
        <File Name="ClientsModel.Methods.AccRightUserParameterValue.cs" />
        <File Name="ClientsModel.Methods.AllergenLng.cs" />
        <File Name="ClientsModel.Methods.Offer.cs" />
        <File Name="ClientsModel.Methods.OfferDetail.cs" />
        <File Name="ClientsModel.Methods.EetCertificate.cs" />
        <File Name="ClientsModel.Methods.EetLog.cs" />
        <File Name="ClientsModel.Methods.OrderAddInfo.cs" />
        <File Name="ClientsModel.Methods.IssueSlip.cs" />
        <File Name="ClientsModel.Methods.AppObject.cs" />
        <File Name="ClientsModel.Methods.AccRightCodeObject.cs" />
        <File Name="ClientsModel.Methods.MealKindLng.cs" />
        <File Name="ClientsModel.Methods.MealTypeRowLng.cs" />
        <File Name="ClientsModel.Methods.MenuTemplateRowLng.cs" />
        <File Name="ClientsModel.Methods.MealTemplateLng.cs" />
        <File Name="ClientsModel.Methods.MenuRowLng.cs" />
        <File Name="ClientsModel.Methods.MenuRowAllergen.cs" />
        <File Name="ClientsModel.Methods.MenuRowPictogram.cs" />
        <File Name="ClientsModel.Methods.SubsidyCategoryLng.cs" />
        <File Name="ClientsModel.Methods.SysCounter.cs" />
        <File Name="ClientsModel.Methods.SysCounterLocal.cs" />
        <File Name="ClientsModel.Methods.GdprPermissionTypeLng.cs" />
        <File Name="ClientsModel.Methods.ClientGdprPermission.cs" />
        <File Name="ClientsModel.Methods.GdprAddInfo.cs" />
        <File Name="ClientsModel.Methods.GdprLog.cs" />
        <File Name="ClientsModel.Methods.GdprLogType.cs" />
        <File Name="ClientsModel.Methods.SalaryDrawbacksExportDef.cs" />
        <File Name="ClientsModel.Methods.ViewTemplate.cs" />
        <File Name="ClientsModel.Methods.UsAdresa.cs" />
        <File Name="ClientsModel.Methods.UsAreal.cs" />
        <File Name="ClientsModel.Methods.UsAtributPokoje.cs" />
        <File Name="ClientsModel.Methods.UsBudova.cs" />
        <File Name="ClientsModel.Methods.UsBunka.cs" />
        <File Name="ClientsModel.Methods.UsHost.cs" />
        <File Name="ClientsModel.Methods.UsMesto.cs" />
        <File Name="ClientsModel.Methods.UsObec.cs" />
        <File Name="ClientsModel.Methods.UsPokoj.cs" />
        <File Name="ClientsModel.Methods.UsPokojAtributPokoje.cs" />
        <File Name="ClientsModel.Methods.UsPokojRezervace.cs" />
        <File Name="ClientsModel.Methods.UsPokojSkupinaProUbytovani.cs" />
        <File Name="ClientsModel.Methods.UsPokojVyclenenaKapacita.cs" />
        <File Name="ClientsModel.Methods.UsPokojVylukaPokoju.cs" />
        <File Name="ClientsModel.Methods.UsRezervace.cs" />
        <File Name="ClientsModel.Methods.UsSkupinaProUbytovani.cs" />
        <File Name="ClientsModel.Methods.UsTypPokoje.cs" />
        <File Name="ClientsModel.Methods.UsUbytovani.cs" />
        <File Name="ClientsModel.Methods.UsUbytovany.cs" />
        <File Name="ClientsModel.Methods.UsUbytovanyHost.cs" />
        <File Name="ClientsModel.Methods.UsVyclenenaKapacita.cs" />
        <File Name="ClientsModel.Methods.UsVyclenenaKapacitaSkupinaProUbytovani.cs" />
        <File Name="ClientsModel.Methods.UsVylukaPokoju.cs" />
        <File Name="ClientsModel.Methods.UsStat.cs" />
        <File Name="ClientsModel.Methods.UsTypUbytovani.cs" />
        <File Name="ClientsModel.Methods.AccRoleUzivatele.cs" />
        <File Name="ClientsModel.Methods.Obdobi.cs" />
        <File Name="ClientsModel.Methods.PokojObdobi.cs" />
        <File Name="ClientsModel.Methods.Termin.cs" />
        <File Name="ClientsModel.Methods.UsUcelPobytu.cs" />
        <File Name="ClientsModel.Methods.Zadost.cs" />
        <File Name="ClientsModel.Methods.ZadostZvyhodneniProPoradnik.cs" />
        <File Name="ClientsModel.Methods.ZvyhodneniProPoradnik.cs" />
        <File Name="ClientsModel.Methods.ObdobiSkupinaProUbytovani.cs" />
        <File Name="ClientsModel.Methods.Podminka.cs" />
        <File Name="ClientsModel.Methods.PokojovaSluzba.cs" />
        <File Name="ClientsModel.Methods.ZarizeniKVypujcce.cs" />
        <File Name="ClientsModel.Methods.UbytovaniPokojovaSluzba.cs" />
        <File Name="ClientsModel.Methods.CenikovePolozky.cs" />
        <File Name="ClientsModel.Methods.Ceniky.cs" />
        <File Name="ClientsModel.Methods.Ceny.cs" />
        <File Name="ClientsModel.Methods.CenyNazvy.cs" />
        <File Name="ClientsModel.Methods.FinancniZdroje.cs" />
        <File Name="ClientsModel.Methods.PokojCenikovePolozky.cs" />
        <File Name="ClientsModel.Methods.SkupinaProUbytovaniCenikovePolozky.cs" />
        <File Name="ClientsModel.Methods.TypPokojeCenikovePolozky.cs" />
        <File Name="ClientsModel.Methods.UbytovanyZarizeniKVypujcce.cs" />
        <File Name="ClientsModel.Methods.Pohledavky.cs" />
        <File Name="ClientsModel.Methods.VzdalenostiCHAP.cs" />
        <File Name="ClientsModel.Methods.Kauce.cs" />
        <File Name="ClientsModel.Methods.OdeslaneZpravy.cs" />
        <File Name="ClientsModel.Methods.SablonyZprav.cs" />
        <File Name="ClientsModel.Methods.SablonyZpravLng.cs" />
        <File Name="ClientsModel.Methods.UbyPortLog.cs" />
        <File Name="ClientsModel.Methods.AccHistorie.cs" />
        <File Name="ClientsModel.Methods.AccVerzePrav.cs" />
        <File Name="ClientsModel.Methods.DeviceController_DeviceControllerHw.cs" />
        <File Name="ClientsModel.Methods.DeviceControllerService.cs" />
        <File Name="ClientsModel.Methods.DeviceControllerHw.cs" />
        <File Name="ClientsModel.Methods.DMJidelnicky.cs" />
        <File Name="ClientsModel.Methods.DMObrazovky.cs" />
        <File Name="ClientsModel.Methods.DMSablony.cs" />
        <File Name="ClientsModel.Methods.DMSablonyObrazovky.cs" />
        <File Name="ClientsModel.Methods.DMSloupce.cs" />
        <File Name="ClientsModel.Methods.ClenoveRodinyDietVybery.cs" />
        <File Name="ClientsModel.Methods.RodinaDietVybery.cs" />
        <File Name="ClientsModel.Methods.DMKomponenty.cs" />
        <File Name="ClientsModel.Methods.DMSchema.cs" />
        <File Name="ClientsModel.Methods.DMSchemaSablony.cs" />
        <File Name="ClientsModel.Methods.Blob.cs" />
        <File Name="ClientsModel.Methods.BlobsData.cs" />
        <File Name="ClientsModel.Methods.DMObrazovkyZobrazeni.cs" />
        <File Name="ClientsModel.Methods.PriceVatRate.cs" />
        <File Name="ClientsModel.Methods.CfTypyVydAutomatu.cs" />
        <File Name="ClientsModel.Methods.CfTypyVydAutomatuPozice.cs" />
        <File Name="ClientsModel.Methods.VydAutomatyMapovani.cs" />
        <File Name="ClientsModel.Methods.VydAutomatyMapovaniPozice.cs" />
        <File Name="ClientsModel.Methods.DMStyly.cs" />
        <File Name="ClientsModel.Methods.SectionObjects.cs" />
        <File Name="ClientsModel.Methods.WebPayPendingTransaction.cs" />
        <File Name="ClientsModel.Methods.SMSNotifikace.cs" />
        <File Name="ClientsModel.Methods.SMSNotifikaceLng.cs" />
        <File Name="ClientsModel.Methods.STADeviceLogs.cs" />
        <File Name="ClientsModel.Methods.UsAkce.cs" />
        <File Name="ClientsModel.Methods.AppFormularRozlozeni.cs" />
        <File Name="ClientsModel.Methods.AccAutentizace.cs" />
        <File Name="ClientsModel.Methods.AccAutentizaceUzivatele.cs" />
        <File Name="ClientsModel.Methods.AccSyncUzivatel.cs" />
        <File Name="ClientsModel.Methods.Synchronizace.cs" />
        <File Name="ClientsModel.Methods.SynchronizaceLog.cs" />
        <File Name="ClientsModel.Methods.AppServerSluzby.cs" />
        <File Name="ClientsModel.Methods.AppServer.cs" />
        <File Name="ClientsModel.Methods.AppServerSpojeni.cs" />
        <File Name="ClientsModel.Methods.NISMedicalcImportLog.cs" />
        <File Name="ClientsModel.Methods.BatchPrintGroup.cs" />
        <File Name="ClientsModel.Methods.MealTypeRowAllergen.cs" />
        <File Name="ClientsModel.Methods.UserReport.cs" />
        <File Name="ClientsModel.Methods.UserReportRole.cs" />
        <File Name="ClientsModel.Methods.CampaignClientGroup.cs" />
        <File Name="ClientsModel.Methods.CampaignCanteen.cs" />
        <File Name="ClientsModel.FluentMapping.Client.cs" />
        <File Name="ClientsModel.FluentMapping.Canteen.cs" />
        <File Name="ClientsModel.FluentMapping.ClientRegister.cs" />
        <File Name="ClientsModel.FluentMapping.Organization.cs" />
        <File Name="ClientsModel.FluentMapping.Resort.cs" />
        <File Name="ClientsModel.FluentMapping.AccountingDepartment.cs" />
        <File Name="ClientsModel.FluentMapping.ClientGroup.cs" />
        <File Name="ClientsModel.FluentMapping.ClientRegisterHistory.cs" />
        <File Name="ClientsModel.FluentMapping.Card.cs" />
        <File Name="ClientsModel.FluentMapping.CardMovement.cs" />
        <File Name="ClientsModel.FluentMapping.CardMovementTotals.cs" />
        <File Name="ClientsModel.FluentMapping.Account.cs" />
        <File Name="ClientsModel.FluentMapping.ProviderGroupCategory.cs" />
        <File Name="ClientsModel.FluentMapping.Provider.cs" />
        <File Name="ClientsModel.FluentMapping.Workplace.cs" />
        <File Name="ClientsModel.FluentMapping.ActivityLog.cs" />
        <File Name="ClientsModel.FluentMapping.ClientContact.cs" />
        <File Name="ClientsModel.FluentMapping.CardCode.cs" />
        <File Name="ClientsModel.FluentMapping.AwaitingTransaction.cs" />
        <File Name="ClientsModel.FluentMapping.CanteenAccessCategory.cs" />
        <File Name="ClientsModel.FluentMapping.Language.cs" />
        <File Name="ClientsModel.FluentMapping.AppInstallation.cs" />
        <File Name="ClientsModel.FluentMapping.CashMovement.cs" />
        <File Name="ClientsModel.FluentMapping.CashBalance.cs" />
        <File Name="ClientsModel.FluentMapping.AccountBalance.cs" />
        <File Name="ClientsModel.FluentMapping.ClientBalance.cs" />
        <File Name="ClientsModel.FluentMapping.ClientBalanceReceipt.cs" />
        <File Name="ClientsModel.FluentMapping.Order.cs" />
        <File Name="ClientsModel.FluentMapping.GoodsGroup.cs" />
        <File Name="ClientsModel.FluentMapping.EBankingItem.cs" />
        <File Name="ClientsModel.FluentMapping.AccountMovement.cs" />
        <File Name="ClientsModel.FluentMapping.EBankingBatch.cs" />
        <File Name="ClientsModel.FluentMapping.VatGroup.cs" />
        <File Name="ClientsModel.FluentMapping.Goods.cs" />
        <File Name="ClientsModel.FluentMapping.AccountTemplate.cs" />
        <File Name="ClientsModel.FluentMapping.MealKind.cs" />
        <File Name="ClientsModel.FluentMapping.BatchOrderTemplate.cs" />
        <File Name="ClientsModel.FluentMapping.BatchOrderTemplateRow.cs" />
        <File Name="ClientsModel.FluentMapping.ClientBatchOrderTemplate.cs" />
        <File Name="ClientsModel.FluentMapping.ClientBatchOrderTemplateRow.cs" />
        <File Name="ClientsModel.FluentMapping.BatchOrder.cs" />
        <File Name="ClientsModel.FluentMapping.Licence.cs" />
        <File Name="ClientsModel.FluentMapping.Server.cs" />
        <File Name="ClientsModel.FluentMapping.MenuGroupName.cs" />
        <File Name="ClientsModel.FluentMapping.ApplicationFunc.cs" />
        <File Name="ClientsModel.FluentMapping.DeviceControllerSet.cs" />
        <File Name="ClientsModel.FluentMapping.ServingSchema.cs" />
        <File Name="ClientsModel.FluentMapping.OrderingRule.cs" />
        <File Name="ClientsModel.FluentMapping.Pictogram.cs" />
        <File Name="ClientsModel.FluentMapping.MenuRow.cs" />
        <File Name="ClientsModel.FluentMapping.MenuTemplate.cs" />
        <File Name="ClientsModel.FluentMapping.MenuTemplateRow.cs" />
        <File Name="ClientsModel.FluentMapping.Menu.cs" />
        <File Name="ClientsModel.FluentMapping.MealTemplate.cs" />
        <File Name="ClientsModel.FluentMapping.MenuRowLimitForCanteen.cs" />
        <File Name="ClientsModel.FluentMapping.Currency.cs" />
        <File Name="ClientsModel.FluentMapping.CurrencyType.cs" />
        <File Name="ClientsModel.FluentMapping.DeviceController.cs" />
        <File Name="ClientsModel.FluentMapping.NumericSeries.cs" />
        <File Name="ClientsModel.FluentMapping.SystemCard.cs" />
        <File Name="ClientsModel.FluentMapping.ProvidedService.cs" />
        <File Name="ClientsModel.FluentMapping.Stock.cs" />
        <File Name="ClientsModel.FluentMapping.DataExpiration.cs" />
        <File Name="ClientsModel.FluentMapping.ForeignCurrency.cs" />
        <File Name="ClientsModel.FluentMapping.Section.cs" />
        <File Name="ClientsModel.FluentMapping.VatGroupFull.cs" />
        <File Name="ClientsModel.FluentMapping.VatRate.cs" />
        <File Name="ClientsModel.FluentMapping.ExportDef.cs" />
        <File Name="ClientsModel.FluentMapping.ExportDefColumn.cs" />
        <File Name="ClientsModel.FluentMapping.ExportClass.cs" />
        <File Name="ClientsModel.FluentMapping.ExportClassColumn.cs" />
        <File Name="ClientsModel.FluentMapping.ServingTime.cs" />
        <File Name="ClientsModel.FluentMapping.OrderingSchema.cs" />
        <File Name="ClientsModel.FluentMapping.VendingMachine.cs" />
        <File Name="ClientsModel.FluentMapping.SubsidyCategory.cs" />
        <File Name="ClientsModel.FluentMapping.FinancialSource.cs" />
        <File Name="ClientsModel.FluentMapping.DayAccessCategory.cs" />
        <File Name="ClientsModel.FluentMapping.DeviceControllerSetAccessCategory.cs" />
        <File Name="ClientsModel.FluentMapping.AgeGroup.cs" />
        <File Name="ClientsModel.FluentMapping.MenuCategory.cs" />
        <File Name="ClientsModel.FluentMapping.MenuCategoryRestriction.cs" />
        <File Name="ClientsModel.FluentMapping.MenuGroup.cs" />
        <File Name="ClientsModel.FluentMapping.PriceElementName.cs" />
        <File Name="ClientsModel.FluentMapping.CanteenOperationLockout.cs" />
        <File Name="ClientsModel.FluentMapping.MonthRestriction.cs" />
        <File Name="ClientsModel.FluentMapping.MagionAccountCoding1.cs" />
        <File Name="ClientsModel.FluentMapping.MagionAccountCoding3.cs" />
        <File Name="ClientsModel.FluentMapping.MagionEarningsResort.cs" />
        <File Name="ClientsModel.FluentMapping.PriceCategory.cs" />
        <File Name="ClientsModel.FluentMapping.JasuAccountCoding.cs" />
        <File Name="ClientsModel.FluentMapping.JasuResort.cs" />
        <File Name="ClientsModel.FluentMapping.Diet.cs" />
        <File Name="ClientsModel.FluentMapping.MealRule.cs" />
        <File Name="ClientsModel.FluentMapping.MealRuleComposition.cs" />
        <File Name="ClientsModel.FluentMapping.SvoWorkplace.cs" />
        <File Name="ClientsModel.FluentMapping.SvoWorkplaceReplacement.cs" />
        <File Name="ClientsModel.FluentMapping.SvoShiftType.cs" />
        <File Name="ClientsModel.FluentMapping.SvoShiftAddition.cs" />
        <File Name="ClientsModel.FluentMapping.Unit.cs" />
        <File Name="ClientsModel.FluentMapping.Operator.cs" />
        <File Name="ClientsModel.FluentMapping.Floor.cs" />
        <File Name="ClientsModel.FluentMapping.Room.cs" />
        <File Name="ClientsModel.FluentMapping.Holiday.cs" />
        <File Name="ClientsModel.FluentMapping.Price.cs" />
        <File Name="ClientsModel.FluentMapping.BatchPrint.cs" />
        <File Name="ClientsModel.FluentMapping.BatchPrintItem.cs" />
        <File Name="ClientsModel.FluentMapping.KreditEvent.cs" />
        <File Name="ClientsModel.FluentMapping.AccountBalanceDetail.cs" />
        <File Name="ClientsModel.FluentMapping.CostRecap.cs" />
        <File Name="ClientsModel.FluentMapping.ApplicationRole.cs" />
        <File Name="ClientsModel.FluentMapping.User.cs" />
        <File Name="ClientsModel.FluentMapping.ClientBalanceDetail.cs" />
        <File Name="ClientsModel.FluentMapping.ServingPeriod.cs" />
        <File Name="ClientsModel.FluentMapping.KreditMessage.cs" />
        <File Name="ClientsModel.FluentMapping.LogAction.cs" />
        <File Name="ClientsModel.FluentMapping.ZrdLog.cs" />
        <File Name="ClientsModel.FluentMapping.Attendance.cs" />
        <File Name="ClientsModel.FluentMapping.KpsNotifyUser.cs" />
        <File Name="ClientsModel.FluentMapping.KpsClientReadMessages.cs" />
        <File Name="ClientsModel.FluentMapping.KpsClientSettings.cs" />
        <File Name="ClientsModel.FluentMapping.KpsMessageType.cs" />
        <File Name="ClientsModel.FluentMapping.KpsMessage.cs" />
        <File Name="ClientsModel.FluentMapping.Cashier.cs" />
        <File Name="ClientsModel.FluentMapping.KpsUserReadMessage.cs" />
        <File Name="ClientsModel.FluentMapping.CanceledAccountsCompensation.cs" />
        <File Name="ClientsModel.FluentMapping.MessageForAppInstallation.cs" />
        <File Name="ClientsModel.FluentMapping.GoodsBarCode.cs" />
        <File Name="ClientsModel.FluentMapping.GoodsProfitMargin.cs" />
        <File Name="ClientsModel.FluentMapping.GoodsPrice.cs" />
        <File Name="ClientsModel.FluentMapping.GoodsConnection.cs" />
        <File Name="ClientsModel.FluentMapping.ExchangeRate.cs" />
        <File Name="ClientsModel.FluentMapping.SalesSlip.cs" />
        <File Name="ClientsModel.FluentMapping.SalesSlipRow.cs" />
        <File Name="ClientsModel.FluentMapping.GoodsGroupPriceCategoryMargins.cs" />
        <File Name="ClientsModel.FluentMapping.HsHotelAccount.cs" />
        <File Name="ClientsModel.FluentMapping.HsRoomRental.cs" />
        <File Name="ClientsModel.FluentMapping.HsRoom.cs" />
        <File Name="ClientsModel.FluentMapping.HsRoomType.cs" />
        <File Name="ClientsModel.FluentMapping.HsPerson.cs" />
        <File Name="ClientsModel.FluentMapping.HsHotelAccountExt.cs" />
        <File Name="ClientsModel.FluentMapping.HsRoomOccupancy.cs" />
        <File Name="ClientsModel.FluentMapping.HsCountry.cs" />
        <File Name="ClientsModel.FluentMapping.PriceDiscount.cs" />
        <File Name="ClientsModel.FluentMapping.SubsidyCategoryPrice.cs" />
        <File Name="ClientsModel.FluentMapping.ProfitAccountCoding.cs" />
        <File Name="ClientsModel.FluentMapping.OrderPrice.cs" />
        <File Name="ClientsModel.FluentMapping.CashDeskCardPayment.cs" />
        <File Name="ClientsModel.FluentMapping.CashDeskAlternateLogin.cs" />
        <File Name="ClientsModel.FluentMapping.PriceDiscountForMeal.cs" />
        <File Name="ClientsModel.FluentMapping.PriceDiscountInterval.cs" />
        <File Name="ClientsModel.FluentMapping.MealRestriction.cs" />
        <File Name="ClientsModel.FluentMapping.RADSubLogin.cs" />
        <File Name="ClientsModel.FluentMapping.Driver.cs" />
        <File Name="ClientsModel.FluentMapping.CurrencyTypeProperties.cs" />
        <File Name="ClientsModel.FluentMapping.Cook.cs" />
        <File Name="ClientsModel.FluentMapping.Vehicle.cs" />
        <File Name="ClientsModel.FluentMapping.Delivery.cs" />
        <File Name="ClientsModel.FluentMapping.Inquiry.cs" />
        <File Name="ClientsModel.FluentMapping.InquiryRow.cs" />
        <File Name="ClientsModel.FluentMapping.MealTypeRow.cs" />
        <File Name="ClientsModel.FluentMapping.MealType.cs" />
        <File Name="ClientsModel.FluentMapping.Allergen.cs" />
        <File Name="ClientsModel.FluentMapping.PredefinedNote.cs" />
        <File Name="ClientsModel.FluentMapping.OrganizationAddress.cs" />
        <File Name="ClientsModel.FluentMapping.DeliveryOrder.cs" />
        <File Name="ClientsModel.FluentMapping.ClientRegisterExtInfo.cs" />
        <File Name="ClientsModel.FluentMapping.TouchGroup.cs" />
        <File Name="ClientsModel.FluentMapping.TouchGoods.cs" />
        <File Name="ClientsModel.FluentMapping.ClientRegisterHistoryRange.cs" />
        <File Name="ClientsModel.FluentMapping.Contact.cs" />
        <File Name="ClientsModel.FluentMapping.OrganizationContacts.cs" />
        <File Name="ClientsModel.FluentMapping.DeliveryOrderRow.cs" />
        <File Name="ClientsModel.FluentMapping.MenuRowDetail.cs" />
        <File Name="ClientsModel.FluentMapping.MenuRowCanteenPrintOn.cs" />
        <File Name="ClientsModel.FluentMapping.CampaignClientAnswer.cs" />
        <File Name="ClientsModel.FluentMapping.Campaign.cs" />
        <File Name="ClientsModel.FluentMapping.CampaignQuestions.cs" />
        <File Name="ClientsModel.FluentMapping.CampaignQuestionAnswer.cs" />
        <File Name="ClientsModel.FluentMapping.CampaignQuestion.cs" />
        <File Name="ClientsModel.FluentMapping.MenuRowNutritionalValue.cs" />
        <File Name="ClientsModel.FluentMapping.NutritionalValue.cs" />
        <File Name="ClientsModel.FluentMapping.NutritionalValueLng.cs" />
        <File Name="ClientsModel.FluentMapping.LocalizedNutritionalValue.cs" />
        <File Name="ClientsModel.FluentMapping.CashDeskPaymentOrder.cs" />
        <File Name="ClientsModel.FluentMapping.SvoClientRegisterExt.cs" />
        <File Name="ClientsModel.FluentMapping.ClientMealRule.cs" />
        <File Name="ClientsModel.FluentMapping.MenuRowEan.cs" />
        <File Name="ClientsModel.FluentMapping.AccGroupHierarchy.cs" />
        <File Name="ClientsModel.FluentMapping.AccGroup.cs" />
        <File Name="ClientsModel.FluentMapping.AccGroupRole.cs" />
        <File Name="ClientsModel.FluentMapping.AccInstallation.cs" />
        <File Name="ClientsModel.FluentMapping.AccInstallationRole.cs" />
        <File Name="ClientsModel.FluentMapping.AccRightCode.cs" />
        <File Name="ClientsModel.FluentMapping.AccRightGroup.cs" />
        <File Name="ClientsModel.FluentMapping.AccRight.cs" />
        <File Name="ClientsModel.FluentMapping.AccRightDefaultParameterValue.cs" />
        <File Name="ClientsModel.FluentMapping.AccRightCodeParameterValue.cs" />
        <File Name="ClientsModel.FluentMapping.AccRightCodeParameter.cs" />
        <File Name="ClientsModel.FluentMapping.AccParameter.cs" />
        <File Name="ClientsModel.FluentMapping.AccRightUserParameterValue.cs" />
        <File Name="ClientsModel.FluentMapping.AllergenLng.cs" />
        <File Name="ClientsModel.FluentMapping.Offer.cs" />
        <File Name="ClientsModel.FluentMapping.OfferDetail.cs" />
        <File Name="ClientsModel.FluentMapping.EetCertificate.cs" />
        <File Name="ClientsModel.FluentMapping.EetLog.cs" />
        <File Name="ClientsModel.FluentMapping.OrderAddInfo.cs" />
        <File Name="ClientsModel.FluentMapping.IssueSlip.cs" />
        <File Name="ClientsModel.FluentMapping.AppObject.cs" />
        <File Name="ClientsModel.FluentMapping.AccRightCodeObject.cs" />
        <File Name="ClientsModel.FluentMapping.MealKindLng.cs" />
        <File Name="ClientsModel.FluentMapping.MealTypeRowLng.cs" />
        <File Name="ClientsModel.FluentMapping.MenuTemplateRowLng.cs" />
        <File Name="ClientsModel.FluentMapping.MealTemplateLng.cs" />
        <File Name="ClientsModel.FluentMapping.MenuRowLng.cs" />
        <File Name="ClientsModel.FluentMapping.MenuRowAllergen.cs" />
        <File Name="ClientsModel.FluentMapping.MenuRowPictogram.cs" />
        <File Name="ClientsModel.FluentMapping.SubsidyCategoryLng.cs" />
        <File Name="ClientsModel.FluentMapping.SysCounter.cs" />
        <File Name="ClientsModel.FluentMapping.SysCounterLocal.cs" />
        <File Name="ClientsModel.FluentMapping.GdprPermissionTypeLng.cs" />
        <File Name="ClientsModel.FluentMapping.ClientGdprPermission.cs" />
        <File Name="ClientsModel.FluentMapping.GdprAddInfo.cs" />
        <File Name="ClientsModel.FluentMapping.GdprLog.cs" />
        <File Name="ClientsModel.FluentMapping.GdprLogType.cs" />
        <File Name="ClientsModel.FluentMapping.SalaryDrawbacksExportDef.cs" />
        <File Name="ClientsModel.FluentMapping.ViewTemplate.cs" />
        <File Name="ClientsModel.FluentMapping.UsAdresa.cs" />
        <File Name="ClientsModel.FluentMapping.UsAreal.cs" />
        <File Name="ClientsModel.FluentMapping.UsAtributPokoje.cs" />
        <File Name="ClientsModel.FluentMapping.UsBudova.cs" />
        <File Name="ClientsModel.FluentMapping.UsBunka.cs" />
        <File Name="ClientsModel.FluentMapping.UsHost.cs" />
        <File Name="ClientsModel.FluentMapping.UsMesto.cs" />
        <File Name="ClientsModel.FluentMapping.UsObec.cs" />
        <File Name="ClientsModel.FluentMapping.UsPokoj.cs" />
        <File Name="ClientsModel.FluentMapping.UsPokojAtributPokoje.cs" />
        <File Name="ClientsModel.FluentMapping.UsPokojRezervace.cs" />
        <File Name="ClientsModel.FluentMapping.UsPokojSkupinaProUbytovani.cs" />
        <File Name="ClientsModel.FluentMapping.UsPokojVyclenenaKapacita.cs" />
        <File Name="ClientsModel.FluentMapping.UsPokojVylukaPokoju.cs" />
        <File Name="ClientsModel.FluentMapping.UsRezervace.cs" />
        <File Name="ClientsModel.FluentMapping.UsSkupinaProUbytovani.cs" />
        <File Name="ClientsModel.FluentMapping.UsTypPokoje.cs" />
        <File Name="ClientsModel.FluentMapping.UsUbytovani.cs" />
        <File Name="ClientsModel.FluentMapping.UsUbytovany.cs" />
        <File Name="ClientsModel.FluentMapping.UsUbytovanyHost.cs" />
        <File Name="ClientsModel.FluentMapping.UsVyclenenaKapacita.cs" />
        <File Name="ClientsModel.FluentMapping.UsVyclenenaKapacitaSkupinaProUbytovani.cs" />
        <File Name="ClientsModel.FluentMapping.UsVylukaPokoju.cs" />
        <File Name="ClientsModel.FluentMapping.UsStat.cs" />
        <File Name="ClientsModel.FluentMapping.UsTypUbytovani.cs" />
        <File Name="ClientsModel.FluentMapping.AccRoleUzivatele.cs" />
        <File Name="ClientsModel.FluentMapping.Obdobi.cs" />
        <File Name="ClientsModel.FluentMapping.PokojObdobi.cs" />
        <File Name="ClientsModel.FluentMapping.Termin.cs" />
        <File Name="ClientsModel.FluentMapping.UsUcelPobytu.cs" />
        <File Name="ClientsModel.FluentMapping.Zadost.cs" />
        <File Name="ClientsModel.FluentMapping.ZadostZvyhodneniProPoradnik.cs" />
        <File Name="ClientsModel.FluentMapping.ZvyhodneniProPoradnik.cs" />
        <File Name="ClientsModel.FluentMapping.ObdobiSkupinaProUbytovani.cs" />
        <File Name="ClientsModel.FluentMapping.Podminka.cs" />
        <File Name="ClientsModel.FluentMapping.PokojovaSluzba.cs" />
        <File Name="ClientsModel.FluentMapping.ZarizeniKVypujcce.cs" />
        <File Name="ClientsModel.FluentMapping.UbytovaniPokojovaSluzba.cs" />
        <File Name="ClientsModel.FluentMapping.CenikovePolozky.cs" />
        <File Name="ClientsModel.FluentMapping.Ceniky.cs" />
        <File Name="ClientsModel.FluentMapping.Ceny.cs" />
        <File Name="ClientsModel.FluentMapping.CenyNazvy.cs" />
        <File Name="ClientsModel.FluentMapping.FinancniZdroje.cs" />
        <File Name="ClientsModel.FluentMapping.PokojCenikovePolozky.cs" />
        <File Name="ClientsModel.FluentMapping.SkupinaProUbytovaniCenikovePolozky.cs" />
        <File Name="ClientsModel.FluentMapping.TypPokojeCenikovePolozky.cs" />
        <File Name="ClientsModel.FluentMapping.UbytovanyZarizeniKVypujcce.cs" />
        <File Name="ClientsModel.FluentMapping.Pohledavky.cs" />
        <File Name="ClientsModel.FluentMapping.VzdalenostiCHAP.cs" />
        <File Name="ClientsModel.FluentMapping.Kauce.cs" />
        <File Name="ClientsModel.FluentMapping.OdeslaneZpravy.cs" />
        <File Name="ClientsModel.FluentMapping.SablonyZprav.cs" />
        <File Name="ClientsModel.FluentMapping.SablonyZpravLng.cs" />
        <File Name="ClientsModel.FluentMapping.UbyPortLog.cs" />
        <File Name="ClientsModel.FluentMapping.AccHistorie.cs" />
        <File Name="ClientsModel.FluentMapping.AccVerzePrav.cs" />
        <File Name="ClientsModel.FluentMapping.DeviceController_DeviceControllerHw.cs" />
        <File Name="ClientsModel.FluentMapping.DeviceControllerService.cs" />
        <File Name="ClientsModel.FluentMapping.DeviceControllerHw.cs" />
        <File Name="ClientsModel.FluentMapping.DMJidelnicky.cs" />
        <File Name="ClientsModel.FluentMapping.DMObrazovky.cs" />
        <File Name="ClientsModel.FluentMapping.DMSablony.cs" />
        <File Name="ClientsModel.FluentMapping.DMSablonyObrazovky.cs" />
        <File Name="ClientsModel.FluentMapping.DMSloupce.cs" />
        <File Name="ClientsModel.FluentMapping.ClenoveRodinyDietVybery.cs" />
        <File Name="ClientsModel.FluentMapping.RodinaDietVybery.cs" />
        <File Name="ClientsModel.FluentMapping.DMKomponenty.cs" />
        <File Name="ClientsModel.FluentMapping.DMSchema.cs" />
        <File Name="ClientsModel.FluentMapping.DMSchemaSablony.cs" />
        <File Name="ClientsModel.FluentMapping.Blob.cs" />
        <File Name="ClientsModel.FluentMapping.BlobsData.cs" />
        <File Name="ClientsModel.FluentMapping.DMObrazovkyZobrazeni.cs" />
        <File Name="ClientsModel.FluentMapping.PriceVatRate.cs" />
        <File Name="ClientsModel.FluentMapping.CfTypyVydAutomatu.cs" />
        <File Name="ClientsModel.FluentMapping.CfTypyVydAutomatuPozice.cs" />
        <File Name="ClientsModel.FluentMapping.VydAutomatyMapovani.cs" />
        <File Name="ClientsModel.FluentMapping.VydAutomatyMapovaniPozice.cs" />
        <File Name="ClientsModel.FluentMapping.DMStyly.cs" />
        <File Name="ClientsModel.FluentMapping.SectionObjects.cs" />
        <File Name="ClientsModel.FluentMapping.WebPayPendingTransaction.cs" />
        <File Name="ClientsModel.FluentMapping.SMSNotifikace.cs" />
        <File Name="ClientsModel.FluentMapping.SMSNotifikaceLng.cs" />
        <File Name="ClientsModel.FluentMapping.STADeviceLogs.cs" />
        <File Name="ClientsModel.FluentMapping.UsAkce.cs" />
        <File Name="ClientsModel.FluentMapping.AppFormularRozlozeni.cs" />
        <File Name="ClientsModel.FluentMapping.AccAutentizace.cs" />
        <File Name="ClientsModel.FluentMapping.AccAutentizaceUzivatele.cs" />
        <File Name="ClientsModel.FluentMapping.AccSyncUzivatel.cs" />
        <File Name="ClientsModel.FluentMapping.Synchronizace.cs" />
        <File Name="ClientsModel.FluentMapping.SynchronizaceLog.cs" />
        <File Name="ClientsModel.FluentMapping.AppServerSluzby.cs" />
        <File Name="ClientsModel.FluentMapping.AppServer.cs" />
        <File Name="ClientsModel.FluentMapping.AppServerSpojeni.cs" />
        <File Name="ClientsModel.FluentMapping.NISMedicalcImportLog.cs" />
        <File Name="ClientsModel.FluentMapping.BatchPrintGroup.cs" />
        <File Name="ClientsModel.FluentMapping.MealTypeRowAllergen.cs" />
        <File Name="ClientsModel.FluentMapping.UserReport.cs" />
        <File Name="ClientsModel.FluentMapping.UserReportRole.cs" />
        <File Name="ClientsModel.FluentMapping.CampaignClientGroup.cs" />
        <File Name="ClientsModel.FluentMapping.CampaignCanteen.cs" />
        <File Name="ClientsModel.Client.cs" />
        <File Name="ClientsModel.Canteen.cs" />
        <File Name="ClientsModel.ClientRegister.cs" />
        <File Name="ClientsModel.Organization.cs" />
        <File Name="ClientsModel.Resort.cs" />
        <File Name="ClientsModel.AccountingDepartment.cs" />
        <File Name="ClientsModel.ClientGroup.cs" />
        <File Name="ClientsModel.ClientRegisterHistory.cs" />
        <File Name="ClientsModel.Card.cs" />
        <File Name="ClientsModel.CardMovement.cs" />
        <File Name="ClientsModel.CardMovementTotals.cs" />
        <File Name="ClientsModel.Account.cs" />
        <File Name="ClientsModel.ProviderGroupCategory.cs" />
        <File Name="ClientsModel.Provider.cs" />
        <File Name="ClientsModel.Workplace.cs" />
        <File Name="ClientsModel.ActivityLog.cs" />
        <File Name="ClientsModel.ClientContact.cs" />
        <File Name="ClientsModel.CardCode.cs" />
        <File Name="ClientsModel.AwaitingTransaction.cs" />
        <File Name="ClientsModel.CanteenAccessCategory.cs" />
        <File Name="ClientsModel.Language.cs" />
        <File Name="ClientsModel.AppInstallation.cs" />
        <File Name="ClientsModel.CashMovement.cs" />
        <File Name="ClientsModel.CashBalance.cs" />
        <File Name="ClientsModel.AccountBalance.cs" />
        <File Name="ClientsModel.ClientBalance.cs" />
        <File Name="ClientsModel.ClientBalanceReceipt.cs" />
        <File Name="ClientsModel.Order.cs" />
        <File Name="ClientsModel.GoodsGroup.cs" />
        <File Name="ClientsModel.EBankingItem.cs" />
        <File Name="ClientsModel.AccountMovement.cs" />
        <File Name="ClientsModel.EBankingBatch.cs" />
        <File Name="ClientsModel.VatGroup.cs" />
        <File Name="ClientsModel.Goods.cs" />
        <File Name="ClientsModel.AccountTemplate.cs" />
        <File Name="ClientsModel.MealKind.cs" />
        <File Name="ClientsModel.BatchOrderTemplate.cs" />
        <File Name="ClientsModel.BatchOrderTemplateRow.cs" />
        <File Name="ClientsModel.ClientBatchOrderTemplate.cs" />
        <File Name="ClientsModel.ClientBatchOrderTemplateRow.cs" />
        <File Name="ClientsModel.BatchOrder.cs" />
        <File Name="ClientsModel.Licence.cs" />
        <File Name="ClientsModel.Server.cs" />
        <File Name="ClientsModel.MenuGroupName.cs" />
        <File Name="ClientsModel.ApplicationFunc.cs" />
        <File Name="ClientsModel.DeviceControllerSet.cs" />
        <File Name="ClientsModel.ServingSchema.cs" />
        <File Name="ClientsModel.OrderingRule.cs" />
        <File Name="ClientsModel.Pictogram.cs" />
        <File Name="ClientsModel.MenuRow.cs" />
        <File Name="ClientsModel.MenuTemplate.cs" />
        <File Name="ClientsModel.MenuTemplateRow.cs" />
        <File Name="ClientsModel.Menu.cs" />
        <File Name="ClientsModel.MealTemplate.cs" />
        <File Name="ClientsModel.MenuRowLimitForCanteen.cs" />
        <File Name="ClientsModel.Currency.cs" />
        <File Name="ClientsModel.CurrencyType.cs" />
        <File Name="ClientsModel.DeviceController.cs" />
        <File Name="ClientsModel.NumericSeries.cs" />
        <File Name="ClientsModel.SystemCard.cs" />
        <File Name="ClientsModel.ProvidedService.cs" />
        <File Name="ClientsModel.Stock.cs" />
        <File Name="ClientsModel.DataExpiration.cs" />
        <File Name="ClientsModel.ForeignCurrency.cs" />
        <File Name="ClientsModel.Section.cs" />
        <File Name="ClientsModel.VatGroupFull.cs" />
        <File Name="ClientsModel.VatRate.cs" />
        <File Name="ClientsModel.ExportDef.cs" />
        <File Name="ClientsModel.ExportDefColumn.cs" />
        <File Name="ClientsModel.ExportClass.cs" />
        <File Name="ClientsModel.ExportClassColumn.cs" />
        <File Name="ClientsModel.ServingTime.cs" />
        <File Name="ClientsModel.OrderingSchema.cs" />
        <File Name="ClientsModel.VendingMachine.cs" />
        <File Name="ClientsModel.SubsidyCategory.cs" />
        <File Name="ClientsModel.FinancialSource.cs" />
        <File Name="ClientsModel.DayAccessCategory.cs" />
        <File Name="ClientsModel.DeviceControllerSetAccessCategory.cs" />
        <File Name="ClientsModel.AgeGroup.cs" />
        <File Name="ClientsModel.MenuCategory.cs" />
        <File Name="ClientsModel.MenuCategoryRestriction.cs" />
        <File Name="ClientsModel.MenuGroup.cs" />
        <File Name="ClientsModel.PriceElementName.cs" />
        <File Name="ClientsModel.CanteenOperationLockout.cs" />
        <File Name="ClientsModel.MonthRestriction.cs" />
        <File Name="ClientsModel.MagionAccountCoding1.cs" />
        <File Name="ClientsModel.MagionAccountCoding3.cs" />
        <File Name="ClientsModel.MagionEarningsResort.cs" />
        <File Name="ClientsModel.PriceCategory.cs" />
        <File Name="ClientsModel.JasuAccountCoding.cs" />
        <File Name="ClientsModel.JasuResort.cs" />
        <File Name="ClientsModel.Diet.cs" />
        <File Name="ClientsModel.MealRule.cs" />
        <File Name="ClientsModel.MealRuleComposition.cs" />
        <File Name="ClientsModel.SvoWorkplace.cs" />
        <File Name="ClientsModel.SvoWorkplaceReplacement.cs" />
        <File Name="ClientsModel.SvoShiftType.cs" />
        <File Name="ClientsModel.SvoShiftAddition.cs" />
        <File Name="ClientsModel.Unit.cs" />
        <File Name="ClientsModel.Operator.cs" />
        <File Name="ClientsModel.Floor.cs" />
        <File Name="ClientsModel.Room.cs" />
        <File Name="ClientsModel.Holiday.cs" />
        <File Name="ClientsModel.Price.cs" />
        <File Name="ClientsModel.BatchPrint.cs" />
        <File Name="ClientsModel.BatchPrintItem.cs" />
        <File Name="ClientsModel.KreditEvent.cs" />
        <File Name="ClientsModel.AccountBalanceDetail.cs" />
        <File Name="ClientsModel.CostRecap.cs" />
        <File Name="ClientsModel.ApplicationRole.cs" />
        <File Name="ClientsModel.User.cs" />
        <File Name="ClientsModel.ClientBalanceDetail.cs" />
        <File Name="ClientsModel.ServingPeriod.cs" />
        <File Name="ClientsModel.KreditMessage.cs" />
        <File Name="ClientsModel.LogAction.cs" />
        <File Name="ClientsModel.ZrdLog.cs" />
        <File Name="ClientsModel.Attendance.cs" />
        <File Name="ClientsModel.KpsNotifyUser.cs" />
        <File Name="ClientsModel.KpsClientReadMessages.cs" />
        <File Name="ClientsModel.KpsClientSettings.cs" />
        <File Name="ClientsModel.KpsMessageType.cs" />
        <File Name="ClientsModel.KpsMessage.cs" />
        <File Name="ClientsModel.Cashier.cs" />
        <File Name="ClientsModel.KpsUserReadMessage.cs" />
        <File Name="ClientsModel.CanceledAccountsCompensation.cs" />
        <File Name="ClientsModel.MessageForAppInstallation.cs" />
        <File Name="ClientsModel.GoodsBarCode.cs" />
        <File Name="ClientsModel.GoodsProfitMargin.cs" />
        <File Name="ClientsModel.GoodsPrice.cs" />
        <File Name="ClientsModel.GoodsConnection.cs" />
        <File Name="ClientsModel.ExchangeRate.cs" />
        <File Name="ClientsModel.SalesSlip.cs" />
        <File Name="ClientsModel.SalesSlipRow.cs" />
        <File Name="ClientsModel.GoodsGroupPriceCategoryMargins.cs" />
        <File Name="ClientsModel.HsHotelAccount.cs" />
        <File Name="ClientsModel.HsRoomRental.cs" />
        <File Name="ClientsModel.HsRoom.cs" />
        <File Name="ClientsModel.HsRoomType.cs" />
        <File Name="ClientsModel.HsPerson.cs" />
        <File Name="ClientsModel.HsHotelAccountExt.cs" />
        <File Name="ClientsModel.HsRoomOccupancy.cs" />
        <File Name="ClientsModel.HsCountry.cs" />
        <File Name="ClientsModel.PriceDiscount.cs" />
        <File Name="ClientsModel.SubsidyCategoryPrice.cs" />
        <File Name="ClientsModel.ProfitAccountCoding.cs" />
        <File Name="ClientsModel.OrderPrice.cs" />
        <File Name="ClientsModel.CashDeskCardPayment.cs" />
        <File Name="ClientsModel.CashDeskAlternateLogin.cs" />
        <File Name="ClientsModel.PriceDiscountForMeal.cs" />
        <File Name="ClientsModel.PriceDiscountInterval.cs" />
        <File Name="ClientsModel.MealRestriction.cs" />
        <File Name="ClientsModel.RADSubLogin.cs" />
        <File Name="ClientsModel.Driver.cs" />
        <File Name="ClientsModel.CurrencyTypeProperties.cs" />
        <File Name="ClientsModel.Cook.cs" />
        <File Name="ClientsModel.Vehicle.cs" />
        <File Name="ClientsModel.Delivery.cs" />
        <File Name="ClientsModel.Inquiry.cs" />
        <File Name="ClientsModel.InquiryRow.cs" />
        <File Name="ClientsModel.MealTypeRow.cs" />
        <File Name="ClientsModel.MealType.cs" />
        <File Name="ClientsModel.Allergen.cs" />
        <File Name="ClientsModel.PredefinedNote.cs" />
        <File Name="ClientsModel.OrganizationAddress.cs" />
        <File Name="ClientsModel.DeliveryOrder.cs" />
        <File Name="ClientsModel.ClientRegisterExtInfo.cs" />
        <File Name="ClientsModel.TouchGroup.cs" />
        <File Name="ClientsModel.TouchGoods.cs" />
        <File Name="ClientsModel.ClientRegisterHistoryRange.cs" />
        <File Name="ClientsModel.Contact.cs" />
        <File Name="ClientsModel.OrganizationContacts.cs" />
        <File Name="ClientsModel.DeliveryOrderRow.cs" />
        <File Name="ClientsModel.MenuRowDetail.cs" />
        <File Name="ClientsModel.MenuRowCanteenPrintOn.cs" />
        <File Name="ClientsModel.CampaignClientAnswer.cs" />
        <File Name="ClientsModel.Campaign.cs" />
        <File Name="ClientsModel.CampaignQuestions.cs" />
        <File Name="ClientsModel.CampaignQuestionAnswer.cs" />
        <File Name="ClientsModel.CampaignQuestion.cs" />
        <File Name="ClientsModel.MenuRowNutritionalValue.cs" />
        <File Name="ClientsModel.NutritionalValue.cs" />
        <File Name="ClientsModel.NutritionalValueLng.cs" />
        <File Name="ClientsModel.LocalizedNutritionalValue.cs" />
        <File Name="ClientsModel.CashDeskPaymentOrder.cs" />
        <File Name="ClientsModel.SvoClientRegisterExt.cs" />
        <File Name="ClientsModel.ClientMealRule.cs" />
        <File Name="ClientsModel.MenuRowEan.cs" />
        <File Name="ClientsModel.AccGroupHierarchy.cs" />
        <File Name="ClientsModel.AccGroup.cs" />
        <File Name="ClientsModel.AccGroupRole.cs" />
        <File Name="ClientsModel.AccInstallation.cs" />
        <File Name="ClientsModel.AccInstallationRole.cs" />
        <File Name="ClientsModel.AccRightCode.cs" />
        <File Name="ClientsModel.AccRightGroup.cs" />
        <File Name="ClientsModel.AccRight.cs" />
        <File Name="ClientsModel.AccRightDefaultParameterValue.cs" />
        <File Name="ClientsModel.AccRightCodeParameterValue.cs" />
        <File Name="ClientsModel.AccRightCodeParameter.cs" />
        <File Name="ClientsModel.AccParameter.cs" />
        <File Name="ClientsModel.AccRightUserParameterValue.cs" />
        <File Name="ClientsModel.AllergenLng.cs" />
        <File Name="ClientsModel.Offer.cs" />
        <File Name="ClientsModel.OfferDetail.cs" />
        <File Name="ClientsModel.EetCertificate.cs" />
        <File Name="ClientsModel.EetLog.cs" />
        <File Name="ClientsModel.OrderAddInfo.cs" />
        <File Name="ClientsModel.IssueSlip.cs" />
        <File Name="ClientsModel.AppObject.cs" />
        <File Name="ClientsModel.AccRightCodeObject.cs" />
        <File Name="ClientsModel.MealKindLng.cs" />
        <File Name="ClientsModel.MealTypeRowLng.cs" />
        <File Name="ClientsModel.MenuTemplateRowLng.cs" />
        <File Name="ClientsModel.MealTemplateLng.cs" />
        <File Name="ClientsModel.MenuRowLng.cs" />
        <File Name="ClientsModel.MenuRowAllergen.cs" />
        <File Name="ClientsModel.MenuRowPictogram.cs" />
        <File Name="ClientsModel.SubsidyCategoryLng.cs" />
        <File Name="ClientsModel.SysCounter.cs" />
        <File Name="ClientsModel.SysCounterLocal.cs" />
        <File Name="ClientsModel.GdprPermissionTypeLng.cs" />
        <File Name="ClientsModel.ClientGdprPermission.cs" />
        <File Name="ClientsModel.GdprAddInfo.cs" />
        <File Name="ClientsModel.GdprLog.cs" />
        <File Name="ClientsModel.GdprLogType.cs" />
        <File Name="ClientsModel.SalaryDrawbacksExportDef.cs" />
        <File Name="ClientsModel.ViewTemplate.cs" />
        <File Name="ClientsModel.UsAdresa.cs" />
        <File Name="ClientsModel.UsAreal.cs" />
        <File Name="ClientsModel.UsAtributPokoje.cs" />
        <File Name="ClientsModel.UsBudova.cs" />
        <File Name="ClientsModel.UsBunka.cs" />
        <File Name="ClientsModel.UsHost.cs" />
        <File Name="ClientsModel.UsMesto.cs" />
        <File Name="ClientsModel.UsObec.cs" />
        <File Name="ClientsModel.UsPokoj.cs" />
        <File Name="ClientsModel.UsPokojAtributPokoje.cs" />
        <File Name="ClientsModel.UsPokojRezervace.cs" />
        <File Name="ClientsModel.UsPokojSkupinaProUbytovani.cs" />
        <File Name="ClientsModel.UsPokojVyclenenaKapacita.cs" />
        <File Name="ClientsModel.UsPokojVylukaPokoju.cs" />
        <File Name="ClientsModel.UsRezervace.cs" />
        <File Name="ClientsModel.UsSkupinaProUbytovani.cs" />
        <File Name="ClientsModel.UsTypPokoje.cs" />
        <File Name="ClientsModel.UsUbytovani.cs" />
        <File Name="ClientsModel.UsUbytovany.cs" />
        <File Name="ClientsModel.UsUbytovanyHost.cs" />
        <File Name="ClientsModel.UsVyclenenaKapacita.cs" />
        <File Name="ClientsModel.UsVyclenenaKapacitaSkupinaProUbytovani.cs" />
        <File Name="ClientsModel.UsVylukaPokoju.cs" />
        <File Name="ClientsModel.UsStat.cs" />
        <File Name="ClientsModel.UsTypUbytovani.cs" />
        <File Name="ClientsModel.AccRoleUzivatele.cs" />
        <File Name="ClientsModel.Obdobi.cs" />
        <File Name="ClientsModel.PokojObdobi.cs" />
        <File Name="ClientsModel.Termin.cs" />
        <File Name="ClientsModel.UsUcelPobytu.cs" />
        <File Name="ClientsModel.Zadost.cs" />
        <File Name="ClientsModel.ZadostZvyhodneniProPoradnik.cs" />
        <File Name="ClientsModel.ZvyhodneniProPoradnik.cs" />
        <File Name="ClientsModel.ObdobiSkupinaProUbytovani.cs" />
        <File Name="ClientsModel.Podminka.cs" />
        <File Name="ClientsModel.PokojovaSluzba.cs" />
        <File Name="ClientsModel.ZarizeniKVypujcce.cs" />
        <File Name="ClientsModel.UbytovaniPokojovaSluzba.cs" />
        <File Name="ClientsModel.CenikovePolozky.cs" />
        <File Name="ClientsModel.Ceniky.cs" />
        <File Name="ClientsModel.Ceny.cs" />
        <File Name="ClientsModel.CenyNazvy.cs" />
        <File Name="ClientsModel.FinancniZdroje.cs" />
        <File Name="ClientsModel.PokojCenikovePolozky.cs" />
        <File Name="ClientsModel.SkupinaProUbytovaniCenikovePolozky.cs" />
        <File Name="ClientsModel.TypPokojeCenikovePolozky.cs" />
        <File Name="ClientsModel.UbytovanyZarizeniKVypujcce.cs" />
        <File Name="ClientsModel.Pohledavky.cs" />
        <File Name="ClientsModel.VzdalenostiCHAP.cs" />
        <File Name="ClientsModel.Kauce.cs" />
        <File Name="ClientsModel.OdeslaneZpravy.cs" />
        <File Name="ClientsModel.SablonyZprav.cs" />
        <File Name="ClientsModel.SablonyZpravLng.cs" />
        <File Name="ClientsModel.UbyPortLog.cs" />
        <File Name="ClientsModel.AccHistorie.cs" />
        <File Name="ClientsModel.AccVerzePrav.cs" />
        <File Name="ClientsModel.DeviceController_DeviceControllerHw.cs" />
        <File Name="ClientsModel.DeviceControllerService.cs" />
        <File Name="ClientsModel.DeviceControllerHw.cs" />
        <File Name="ClientsModel.DMJidelnicky.cs" />
        <File Name="ClientsModel.DMObrazovky.cs" />
        <File Name="ClientsModel.DMSablony.cs" />
        <File Name="ClientsModel.DMSablonyObrazovky.cs" />
        <File Name="ClientsModel.DMSloupce.cs" />
        <File Name="ClientsModel.ClenoveRodinyDietVybery.cs" />
        <File Name="ClientsModel.RodinaDietVybery.cs" />
        <File Name="ClientsModel.DMKomponenty.cs" />
        <File Name="ClientsModel.DMSchema.cs" />
        <File Name="ClientsModel.DMSchemaSablony.cs" />
        <File Name="ClientsModel.Blob.cs" />
        <File Name="ClientsModel.BlobsData.cs" />
        <File Name="ClientsModel.DMObrazovkyZobrazeni.cs" />
        <File Name="ClientsModel.PriceVatRate.cs" />
        <File Name="ClientsModel.CfTypyVydAutomatu.cs" />
        <File Name="ClientsModel.CfTypyVydAutomatuPozice.cs" />
        <File Name="ClientsModel.VydAutomatyMapovani.cs" />
        <File Name="ClientsModel.VydAutomatyMapovaniPozice.cs" />
        <File Name="ClientsModel.DMStyly.cs" />
        <File Name="ClientsModel.SectionObjects.cs" />
        <File Name="ClientsModel.WebPayPendingTransaction.cs" />
        <File Name="ClientsModel.SMSNotifikace.cs" />
        <File Name="ClientsModel.SMSNotifikaceLng.cs" />
        <File Name="ClientsModel.STADeviceLogs.cs" />
        <File Name="ClientsModel.UsAkce.cs" />
        <File Name="ClientsModel.AppFormularRozlozeni.cs" />
        <File Name="ClientsModel.AccAutentizace.cs" />
        <File Name="ClientsModel.AccAutentizaceUzivatele.cs" />
        <File Name="ClientsModel.AccSyncUzivatel.cs" />
        <File Name="ClientsModel.Synchronizace.cs" />
        <File Name="ClientsModel.SynchronizaceLog.cs" />
        <File Name="ClientsModel.AppServerSluzby.cs" />
        <File Name="ClientsModel.AppServer.cs" />
        <File Name="ClientsModel.AppServerSpojeni.cs" />
        <File Name="ClientsModel.NISMedicalcImportLog.cs" />
        <File Name="ClientsModel.BatchPrintGroup.cs" />
        <File Name="ClientsModel.MealTypeRowAllergen.cs" />
        <File Name="ClientsModel.UserReport.cs" />
        <File Name="ClientsModel.UserReportRole.cs" />
        <File Name="ClientsModel.CampaignClientGroup.cs" />
        <File Name="ClientsModel.CampaignCanteen.cs" />
        <File Name="ClientsModel.HsFlight.cs" />
        <File Name="ClientsModel.CardMovementType.cs" />
        <File Name="ClientsModel.CardState.cs" />
        <File Name="ClientsModel.ClientAccessRight.cs" />
        <File Name="ClientsModel.AccountType.cs" />
        <File Name="ClientsModel.ClientContactType.cs" />
        <File Name="ClientsModel.CollectionType.cs" />
        <File Name="ClientsModel.CashMovementType.cs" />
        <File Name="ClientsModel.PaymentType.cs" />
        <File Name="ClientsModel.AccountMovenentType.cs" />
        <File Name="ClientsModel.SystemCardFunction.cs" />
        <File Name="ClientsModel.DeviceControllerType.cs" />
        <File Name="ClientsModel.ExportDefColumnSection.cs" />
        <File Name="ClientsModel.ExportFormat.cs" />
        <File Name="ClientsModel.HolidayKind.cs" />
        <File Name="ClientsModel.ExtDispType.cs" />
        <File Name="ClientsModel.KpsNotifyClientType.cs" />
        <File Name="ClientsModel.ExportSeparator.cs" />
        <File Name="ClientsModel.HsHotelAccountState.cs" />
        <File Name="ClientsModel.HsSex.cs" />
        <File Name="ClientsModel.HsReservationSource.cs" />
        <File Name="ClientsModel.AddressType.cs" />
        <File Name="ClientsModel.ClientRegisterExtInfoType.cs" />
        <File Name="ClientsModel.ContactType.cs" />
        <File Name="ClientsModel.CampaignPriority.cs" />
        <File Name="ClientsModel.CampaignQuestionType.cs" />
        <File Name="ClientsModel.CampaignAnswerType.cs" />
        <File Name="ClientsModel.OrderAddInfoType.cs" />
        <File Name="ClientsModel.CollectionFlag.cs" />
        <File Name="ClientsModel.ViewTemplateKind.cs" />
        <File Name="ClientsModel.UsRezervaceExtInfoType.cs" />
      </GeneratedFiles>
    </Generation>
    <generator class="assigned" />
    <DatabaseFirstNamingRules>
      <EntitySet PluralizationName="Pluralize" />
      <Class UseSchemaAsPrefix="False" PluralizeCollectionNavigationPropertyName="True" AddConstraintColumnsToNavigationPropertyName="False" RemoveUnderscores="True" AddUnderscores="False" RemoveInvalidCharacters="True" CodeCase="FirstLetterUppercase" AddPrefix="" AddSuffix="" RemovePrefixes="" RemoveSuffixes="" PluralizationName="Unchanged" />
      <Property RemoveUnderscores="True" AddUnderscores="False" RemoveInvalidCharacters="True" CodeCase="FirstLetterUppercase" AddPrefix="" AddSuffix="" RemovePrefixes="" RemoveSuffixes="" PluralizationName="Unchanged" />
    </DatabaseFirstNamingRules>
    <ModelFirst StorageSynchronizationEnabled="False" TargetSchema="dbo" TargetProviderName="System.Data.SqlClient">
      <TargetServer Server="SQL Server" ServerVersion="2019" />
      <ModelFirstNamingRules>
        <Table RemoveUnderscores="False" AddUnderscores="False" RemoveInvalidCharacters="True" CodeCase="Unchanged" AddPrefix="" AddSuffix="" RemovePrefixes="" RemoveSuffixes="" PluralizationName="Pluralize" />
        <Column RemoveUnderscores="False" AddUnderscores="False" RemoveInvalidCharacters="True" CodeCase="Unchanged" AddPrefix="" AddSuffix="" RemovePrefixes="" RemoveSuffixes="" PluralizationName="Unchanged" />
      </ModelFirstNamingRules>
    </ModelFirst>
    <ExtendedProperties>
      <Property>
        <ed:Property Name="GenerateDisplayNameAttribute" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" Default="True" />
        <ed:Property Name="GenerateFluentMap" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" Default="True" />
      </Property>
    </ExtendedProperties>
    <Diagrams>
      <Diagram Name="Clients" />
      <Diagram Name="Receipts" />
      <Diagram Name="EBanking" />
      <Diagram Name="BatchOrderTemplate" />
      <Diagram Name="Cards" />
      <Diagram Name="LicencesManagement" />
      <Diagram Name="EntityManagement-Config" />
      <Diagram Name="Menu" />
      <Diagram Name="EntityManagement-Assignments" />
      <Diagram Name="EntityManagement-Exports" />
      <Diagram Name="EntityManagement-Meals" />
      <Diagram Name="EntityManagement-Access" />
      <Diagram Name="EntityManagement-Diets" />
      <Diagram Name="SystemSettings" />
      <Diagram Name="PriceList" />
      <Diagram Name="Workplaces-Reports" />
      <Diagram Name="AccessRights" />
      <Diagram Name="SystemMaintenance" />
      <Diagram Name="Workplaces-AltOrdering" />
      <Diagram Name="Book of complaints and suggestions" />
      <Diagram Name="GoodsPriceList" />
      <Diagram Name="Workplaces-SalesSlips" />
      <Diagram Name="Hotel" />
      <Diagram Name="GoodsSubsidies" />
      <Diagram Name="SaleSummary" />
      <Diagram Name="Discount" />
      <Diagram Name="Goods" />
      <Diagram Name="MealRestriction" />
      <Diagram Name="MobileOrdering" />
      <Diagram Name="MealDistribution" />
      <Diagram Name="Diets" />
      <Diagram Name="Statistics" />
      <Diagram Name="Workplaces-TouchSetup" />
      <Diagram Name="Organizations" />
      <Diagram Name="Campaign" />
      <Diagram Name="NutritionalValue" />
      <Diagram Name="Allergens" />
      <Diagram Name="OfferForAction" />
      <Diagram Name="EetCertificate" />
      <Diagram Name="Workplaces-IssueSlip" />
      <Diagram Name="Localization" />
      <Diagram Name="ViewTemplates" />
      <Diagram Name="Accommodation" />
      <Diagram Name="MenuBasic" />
      <Diagram Name="AccommodationPrice" />
      <Diagram Name="DeviceControllers" />
      <Diagram Name="MenuPresenter" />
      <Diagram Name="Clenove rodiny" />
      <Diagram Name="Blobs" />
      <Diagram Name="VendingMachinesMap" DefaultDiagram="True" />
      <Diagram Name="RestaurantMap" />
      <Diagram Name="Alt DPH" />
      <Diagram Name="BatchPrint" />
      <Diagram Name="AppServerSluzby" />
      <Diagram Name="MealType" />
    </Diagrams>
    <Templates>
      <Template Name="CustomValidation" Enabled="True" IsShared="True" File="CustomValidation.tmpl">
        <ed:Property Name="ValidationFramework" Type="EntityDeveloper.TemplateEngine.ValidationFramework, EntityDeveloper.Common, Version=*********, Culture=neutral, PublicKeyToken=09af7300eec23701">
          <ValidationFramework>EnterpriseLibrary</ValidationFramework>
        </ed:Property>
        <ed:Property Name="ValidationErrorMessages" Type="EntityDeveloper.TemplateEngine.ValidationErrorMessages, EntityDeveloper.Common, Version=*********, Culture=neutral, PublicKeyToken=09af7300eec23701">
          <ValidationErrorMessages xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" />
        </ed:Property>
      </Template>
      <Template Name="EntityMethods" Description="New empty template for Devart Entity Developer C# code generation." Enabled="True" IsShared="True" File="EntityMethods.tmpl">
        <ed:Property Name="Output" Type="EntityDeveloper.TemplateEngine.OutputInfo, EntityDeveloper.Common, Version=*********, Culture=neutral, PublicKeyToken=09af7300eec23701">
          <OutputInfo xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <Project />
            <ProjectFolder />
            <DestinationFolder />
          </OutputInfo>
        </ed:Property>
      </Template>
      <Template Name="Fluent NHibernate Anete" Description="Use this template to generate a Fluent NHibernate Mapping classes." Enabled="True" IsShared="True" File="Fluent NHibernate Anete.tmpl">
        <ed:Property Name="FilePerClass" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>true</boolean>
        </ed:Property>
        <ed:Property Name="GeneratePartialClass" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>false</boolean>
        </ed:Property>
        <ed:Property Name="ModelNameAsFilesPrefix" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>true</boolean>
        </ed:Property>
        <ed:Property Name="HeaderTimestampVersionControlTag" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <string>Neverzovano</string>
        </ed:Property>
        <ed:Property Name="Output" Type="EntityDeveloper.TemplateEngine.OutputInfo, EntityDeveloper.Common, Version=*********, Culture=neutral, PublicKeyToken=09af7300eec23701">
          <OutputInfo xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <Project />
            <ProjectFolder />
            <DestinationFolder />
          </OutputInfo>
        </ed:Property>
      </Template>
      <Template Name="NHibernate Anete" Description="Use this template to generate classes and xml mappings for an NHibernate model." Enabled="True" IsShared="True" File="NHibernate Anete.tmpl">
        <ed:Property Name="ValidationFramework" Type="EntityDeveloper.TemplateEngine.ValidationFramework, EntityDeveloper.Common, Version=*********, Culture=neutral, PublicKeyToken=09af7300eec23701">
          <ValidationFramework>EnterpriseLibrary</ValidationFramework>
        </ed:Property>
        <ed:Property Name="ValidationErrorMessages" Type="EntityDeveloper.TemplateEngine.ValidationErrorMessages, EntityDeveloper.Common, Version=*********, Culture=neutral, PublicKeyToken=09af7300eec23701">
          <ValidationErrorMessages xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" />
        </ed:Property>
        <ed:Property Name="FilePerClass" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>true</boolean>
        </ed:Property>
        <ed:Property Name="ModelNameAsFilesPrefix" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>true</boolean>
        </ed:Property>
        <ed:Property Name="HeaderTimestampVersionControlTag" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <string>Neverzovano</string>
        </ed:Property>
        <ed:Property Name="EntitiesOutput" Type="EntityDeveloper.TemplateEngine.OutputInfo, EntityDeveloper.Common, Version=*********, Culture=neutral, PublicKeyToken=09af7300eec23701">
          <OutputInfo xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <Project />
            <ProjectFolder />
            <DestinationFolder />
          </OutputInfo>
        </ed:Property>
        <ed:Property Name="NHibernateV3Compatible" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>false</boolean>
        </ed:Property>
        <ed:Property Name="PropertyChangeNotifiers" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>true</boolean>
        </ed:Property>
        <ed:Property Name="PropertyChangePartialMethods" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>true</boolean>
        </ed:Property>
        <ed:Property Name="ImplementValidatable" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>false</boolean>
        </ed:Property>
        <ed:Property Name="ImplementEquals" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>true</boolean>
        </ed:Property>
        <ed:Property Name="ImplementCloneable" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>false</boolean>
        </ed:Property>
        <ed:Property Name="GeneratePartialClass" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>false</boolean>
        </ed:Property>
        <ed:Property Name="GenerateDataContracts" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>false</boolean>
        </ed:Property>
        <ed:Property Name="GenerateDummyComments" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>false</boolean>
        </ed:Property>
        <ed:Property Name="GenerateSerializableAttributes" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>false</boolean>
        </ed:Property>
        <ed:Property Name="XmlMappingOutput" Type="EntityDeveloper.TemplateEngine.OutputInfo, EntityDeveloper.Common, Version=*********, Culture=neutral, PublicKeyToken=09af7300eec23701">
          <OutputInfo xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <Project />
            <ProjectFolder />
            <DestinationFolder />
          </OutputInfo>
        </ed:Property>
        <ed:Property Name="XmlMappingAction" Type="EntityDeveloper.MetadataArtifactProcessing, EntityDeveloper.Orm.Common, Version=*********, Culture=neutral, PublicKeyToken=09af7300eec23701">
          <MetadataArtifactProcessing>DoNotGenerateMappingFiles</MetadataArtifactProcessing>
        </ed:Property>
        <ed:Property Name="XmlMappingFilePerClass" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>true</boolean>
        </ed:Property>
        <ed:Property Name="GenerateSRResourceWithDot" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>false</boolean>
        </ed:Property>
      </Template>
    </Templates>
    <AttributeAssemblies />
    <Configuration ProxyFactory="LinFu" EnableSaveConfiguration="False" UseProxyValidator="False" />
    <UpdateFromDatabaseExcludes>
      <Exclude Name="id" Table="dba.ANK_Data" Type="Column" />
      <Exclude Name="id_kampan" Table="dba.ANK_Data" Type="Column" />
      <Exclude Name="id_otazka" Table="dba.ANK_Data" Type="Column" />
      <Exclude Name="id_odpoved" Table="dba.ANK_Data" Type="Column" />
      <Exclude Name="datum" Table="dba.ANK_Data" Type="Column" />
      <Exclude Name="id_vydejna" Table="dba.ANK_Data" Type="Column" />
      <Exclude Name="jidlo_pc" Table="dba.ANK_Data" Type="Column" />
      <Exclude Name="poznamka" Table="dba.ANK_Data" Type="Column" />
      <Exclude Name="id_lk" Table="dba.ANK_Data" Type="Column" />
      <Exclude Name="tstamp" Table="dba.ANK_Data" Type="Column" />
      <Exclude Name="PK_ANK_Data" Table="dba.ANK_Data" Type="Constraint" />
      <Exclude Name="FK_ANK_Data_Kampane" Table="dba.ANK_Data" Type="Constraint" />
      <Exclude Name="FK_ANK_Data_CFVydejny" Table="dba.ANK_Data" Type="Constraint" />
      <Exclude Name="FK_ANK_Data_jidelnicek" Table="dba.ANK_Data" Type="Constraint" />
      <Exclude Name="FK_ANK_Data_Odpovedi" Table="dba.ANK_Data" Type="Constraint" />
      <Exclude Name="id_kampan" Table="dba.ANK_KampanSkupiny" Type="Column" />
      <Exclude Name="id_sk" Table="dba.ANK_KampanSkupiny" Type="Column" />
      <Exclude Name="PK_ANK_KampanSkupiny" Table="dba.ANK_KampanSkupiny" Type="Constraint" />
      <Exclude Name="" Table="dba.ANK_KampanSkupiny" Type="Constraint" />
      <Exclude Name="" Table="dba.ANK_KampanSkupiny" Type="Constraint" />
      <Exclude Name="id_kampan" Table="dba.ANK_KampanVydejny" Type="Column" />
      <Exclude Name="id_vydejna" Table="dba.ANK_KampanVydejny" Type="Column" />
      <Exclude Name="PK_ANK_KampanVydejny" Table="dba.ANK_KampanVydejny" Type="Constraint" />
      <Exclude Name="" Table="dba.ANK_KampanVydejny" Type="Constraint" />
      <Exclude Name="" Table="dba.ANK_KampanVydejny" Type="Constraint" />
      <Exclude Name="id_zarizeni" Table="dba.AppServer" Type="Column" />
      <Exclude Name="AppServerSluzby" Table="dba.AppServer" Type="Column" />
      <Exclude Name="Hlavni" Table="dba.AppServer" Type="Column" />
      <Exclude Name="PK_AppServer" Table="dba.AppServer" Type="Constraint" />
      <Exclude Name="FK_AppServer_CFZarizeni" Table="dba.AppServer" Type="Constraint" />
      <Exclude Name="id_zarizeni" Table="dba.Automaty" Type="Column" />
      <Exclude Name="id_ctecka" Table="dba.Automaty" Type="Column" />
      <Exclude Name="MaxNapln" Table="dba.Automaty" Type="Column" />
      <Exclude Name="MinNapln" Table="dba.Automaty" Type="Column" />
      <Exclude Name="tstamp" Table="dba.Automaty" Type="Column" />
      <Exclude Name="Zustatek" Table="dba.Automaty" Type="Column" />
      <Exclude Name="id_karusel" Table="dba.Automaty" Type="Column" />
      <Exclude Name="PK_Automaty" Table="dba.Automaty" Type="Constraint" />
      <Exclude Name="FK_Automaty_CFSeznamCtecek" Table="dba.Automaty" Type="Constraint" />
      <Exclude Name="id_operace" Table="dba.BLOKACE" Type="Column" />
      <Exclude Name="blokace" Table="dba.BLOKACE" Type="Column" />
      <Exclude Name="id_lk" Table="dba.BLOKACE" Type="Column" />
      <Exclude Name="id_dodavatel" Table="dba.BLOKACE" Type="Column" />
      <Exclude Name="id_zarizeni" Table="dba.BLOKACE" Type="Column" />
      <Exclude Name="tstamp" Table="dba.BLOKACE" Type="Column" />
      <Exclude Name="PK_BLOKACE" Table="dba.BLOKACE" Type="Constraint" />
      <Exclude Name="id_zarizeni" Table="dba.CFCasyVydeje" Type="Column" />
      <Exclude Name="start" Table="dba.CFCasyVydeje" Type="Column" />
      <Exclude Name="stop" Table="dba.CFCasyVydeje" Type="Column" />
      <Exclude Name="vydava" Table="dba.CFCasyVydeje" Type="Column" />
      <Exclude Name="PK_CFCasyVydeje" Table="dba.CFCasyVydeje" Type="Constraint" />
      <Exclude Name="id_provozovatel" Table="dba.CFCiselneRady" Type="Column" />
      <Exclude Name="id_rada" Table="dba.CFCiselneRady" Type="Column" />
      <Exclude Name="prefix" Table="dba.CFCiselneRady" Type="Column" />
      <Exclude Name="akt_cislo" Table="dba.CFCiselneRady" Type="Column" />
      <Exclude Name="delka_cisla" Table="dba.CFCiselneRady" Type="Column" />
      <Exclude Name="PlatiOd" Table="dba.CFCiselneRady" Type="Column" />
      <Exclude Name="PK_CFCiselneRady" Table="dba.CFCiselneRady" Type="Constraint" />
      <Exclude Name="id_ctecka" Table="dba.CFCtecka_HW" Type="Column" />
      <Exclude Name="id_zarizeni" Table="dba.CFCtecka_HW" Type="Column" />
      <Exclude Name="Index" Table="dba.CFCtecka_HW" Type="Column" />
      <Exclude Name="IdHw" Table="dba.CFCtecka_HW" Type="Column" />
      <Exclude Name="PK_CFCtecka_HW" Table="dba.CFCtecka_HW" Type="Constraint" />
      <Exclude Name="FK_CFCtecka_HW_CFSeznamCtecek" Table="dba.CFCtecka_HW" Type="Constraint" />
      <Exclude Name="FK_CFCtecka_HW_CFSeznamHW" Table="dba.CFCtecka_HW" Type="Constraint" />
      <Exclude Name="id" Table="dba.CFGdprAddInfo" Type="Column" />
      <Exclude Name="popisCZ" Table="dba.CFGdprAddInfo" Type="Column" />
      <Exclude Name="popisSK" Table="dba.CFGdprAddInfo" Type="Column" />
      <Exclude Name="hodnota" Table="dba.CFGdprAddInfo" Type="Column" />
      <Exclude Name="plati_od" Table="dba.CFGdprAddInfo" Type="Column" />
      <Exclude Name="PK_CFGdprAddInfo" Table="dba.CFGdprAddInfo" Type="Constraint" />
      <Exclude Name="id_jidelna" Table="dba.CFJidelny" Type="Column" />
      <Exclude Name="nazev" Table="dba.CFJidelny" Type="Column" />
      <Exclude Name="adresa" Table="dba.CFJidelny" Type="Column" />
      <Exclude Name="telefon_modem" Table="dba.CFJidelny" Type="Column" />
      <Exclude Name="zkraceny_nazev" Table="dba.CFJidelny" Type="Column" />
      <Exclude Name="kontaktni_osoba" Table="dba.CFJidelny" Type="Column" />
      <Exclude Name="id_provozovatel" Table="dba.CFJidelny" Type="Column" />
      <Exclude Name="dietni" Table="dba.CFJidelny" Type="Column" />
      <Exclude Name="id_sklad" Table="dba.CFJidelny" Type="Column" />
      <Exclude Name="poznamka" Table="dba.CFJidelny" Type="Column" />
      <Exclude Name="PK_CFJIDELNY" Table="dba.CFJidelny" Type="Constraint" />
      <Exclude Name="FK_CFJidelny_Sklady" Table="dba.CFJidelny" Type="Constraint" />
      <Exclude Name="FK_CFJidelny_CFProvozovatele" Table="dba.CFJidelny" Type="Constraint" />
      <Exclude Name="IndCFJidelny" Table="dba.CFJidelny" Type="Constraint" />
      <Exclude Name="id_licence" Table="dba.CFLicence" Type="Column" />
      <Exclude Name="hodnota" Table="dba.CFLicence" Type="Column" />
      <Exclude Name="klic" Table="dba.CFLicence" Type="Column" />
      <Exclude Name="id_zarizeni" Table="dba.CFLicence" Type="Column" />
      <Exclude Name="platnost" Table="dba.CFLicence" Type="Column" />
      <Exclude Name="Hash" Table="dba.CFLicence" Type="Column" />
      <Exclude Name="PK_CFLicence" Table="dba.CFLicence" Type="Constraint" />
      <Exclude Name="jidlo_druh" Table="dba.CFObjedPrav" Type="Column" />
      <Exclude Name="id_jidelna" Table="dba.CFObjedPrav" Type="Column" />
      <Exclude Name="den" Table="dba.CFObjedPrav" Type="Column" />
      <Exclude Name="dnu_dopredu" Table="dba.CFObjedPrav" Type="Column" />
      <Exclude Name="dnu_predem" Table="dba.CFObjedPrav" Type="Column" />
      <Exclude Name="stop_cas" Table="dba.CFObjedPrav" Type="Column" />
      <Exclude Name="stop_vydejna" Table="dba.CFObjedPrav" Type="Column" />
      <Exclude Name="dnu_predem_vyber" Table="dba.CFObjedPrav" Type="Column" />
      <Exclude Name="vyber_od" Table="dba.CFObjedPrav" Type="Column" />
      <Exclude Name="vyber_do" Table="dba.CFObjedPrav" Type="Column" />
      <Exclude Name="obj_od" Table="dba.CFObjedPrav" Type="Column" />
      <Exclude Name="obj_do" Table="dba.CFObjedPrav" Type="Column" />
      <Exclude Name="stop_burza" Table="dba.CFObjedPrav" Type="Column" />
      <Exclude Name="dnu_predem_burza" Table="dba.CFObjedPrav" Type="Column" />
      <Exclude Name="PK_CFOBJEDPRAV" Table="dba.CFObjedPrav" Type="Constraint" />
      <Exclude Name="FK_CFOBJEDPRAV_REF_JIDLADRUHY" Table="dba.CFObjedPrav" Type="Constraint" />
      <Exclude Name="id_provozovatel" Table="dba.CFProvozovatele" Type="Column" />
      <Exclude Name="popis" Table="dba.CFProvozovatele" Type="Column" />
      <Exclude Name="nazev" Table="dba.CFProvozovatele" Type="Column" />
      <Exclude Name="adresa" Table="dba.CFProvozovatele" Type="Column" />
      <Exclude Name="misto" Table="dba.CFProvozovatele" Type="Column" />
      <Exclude Name="IC" Table="dba.CFProvozovatele" Type="Column" />
      <Exclude Name="DIC" Table="dba.CFProvozovatele" Type="Column" />
      <Exclude Name="platce_dph" Table="dba.CFProvozovatele" Type="Column" />
      <Exclude Name="EetPovoleno" Table="dba.CFProvozovatele" Type="Column" />
      <Exclude Name="IdUB" Table="dba.CFProvozovatele" Type="Column" />
      <Exclude Name="KodUB" Table="dba.CFProvozovatele" Type="Column" />
      <Exclude Name="kod" Table="dba.CFProvozovatele" Type="Column" />
      <Exclude Name="PK_CFProvozovatele" Table="dba.CFProvozovatele" Type="Constraint" />
      <Exclude Name="id_replika" Table="dba.CFServery" Type="Column" />
      <Exclude Name="server_name" Table="dba.CFServery" Type="Column" />
      <Exclude Name="central" Table="dba.CFServery" Type="Column" />
      <Exclude Name="poznamka" Table="dba.CFServery" Type="Column" />
      <Exclude Name="PK_CFServery" Table="dba.CFServery" Type="Constraint" />
      <Exclude Name="id_ctecka" Table="dba.CFSeznamCtecek" Type="Column" />
      <Exclude Name="typ" Table="dba.CFSeznamCtecek" Type="Column" />
      <Exclude Name="sada" Table="dba.CFSeznamCtecek" Type="Column" />
      <Exclude Name="alt" Table="dba.CFSeznamCtecek" Type="Column" />
      <Exclude Name="vydava" Table="dba.CFSeznamCtecek" Type="Column" />
      <Exclude Name="externi_display" Table="dba.CFSeznamCtecek" Type="Column" />
      <Exclude Name="popis" Table="dba.CFSeznamCtecek" Type="Column" />
      <Exclude Name="id_vydejna" Table="dba.CFSeznamCtecek" Type="Column" />
      <Exclude Name="id_zarizeni" Table="dba.CFSeznamCtecek" Type="Column" />
      <Exclude Name="XmlConfig" Table="dba.CFSeznamCtecek" Type="Column" />
      <Exclude Name="Class" Table="dba.CFSeznamCtecek" Type="Column" />
      <Exclude Name="Aktivni" Table="dba.CFSeznamCtecek" Type="Column" />
      <Exclude Name="citac" Table="dba.CFSeznamCtecek" Type="Column" />
      <Exclude Name="id_typvydautomatu" Table="dba.CFSeznamCtecek" Type="Column" />
      <Exclude Name="PK_CFSEZNAMCTECEK" Table="dba.CFSeznamCtecek" Type="Constraint" />
      <Exclude Name="FK_CFSeznamCtecek_CFZarizeni" Table="dba.CFSeznamCtecek" Type="Constraint" />
      <Exclude Name="FK_CFSEZNAMCTEC_REF_CFSEZNAMSAD" Table="dba.CFSeznamCtecek" Type="Constraint" />
      <Exclude Name="FK_CFSEZNAMCTEC_REF_CFTYPYCTECEK" Table="dba.CFSeznamCtecek" Type="Constraint" />
      <Exclude Name="FK_CFSEZNAMCTEC_REF_CFVYDEJNY" Table="dba.CFSeznamCtecek" Type="Constraint" />
      <Exclude Name="FK_CFSEZNAMCTEC_REF_CFZARIZENI" Table="dba.CFSeznamCtecek" Type="Constraint" />
      <Exclude Name="FK_CFSeznamCtecek_REF_CFTypyVydAutomatu" Table="dba.CFSeznamCtecek" Type="Constraint" />
      <Exclude Name="IndCFSeznamCtecek" Table="dba.CFSeznamCtecek" Type="Constraint" />
      <Exclude Name="id_sada" Table="dba.CFseznamSad" Type="Column" />
      <Exclude Name="popis" Table="dba.CFseznamSad" Type="Column" />
      <Exclude Name="id_zarizeni" Table="dba.CFseznamSad" Type="Column" />
      <Exclude Name="poznamka" Table="dba.CFseznamSad" Type="Column" />
      <Exclude Name="PK_CFSEZNAMSAD" Table="dba.CFseznamSad" Type="Constraint" />
      <Exclude Name="IndCFSeznamSad" Table="dba.CFseznamSad" Type="Constraint" />
      <Exclude Name="kod" Table="dba.CFSystKarty" Type="Column" />
      <Exclude Name="funkce" Table="dba.CFSystKarty" Type="Column" />
      <Exclude Name="parametr" Table="dba.CFSystKarty" Type="Column" />
      <Exclude Name="l_kod" Table="dba.CFSystKarty" Type="Column" />
      <Exclude Name="PK_CFSYSTKARTY" Table="dba.CFSystKarty" Type="Constraint" />
      <Exclude Name="FK_CFSYSTKARTY_REF_CFSYSTKARTYF" Table="dba.CFSystKarty" Type="Constraint" />
      <Exclude Name="id_vydejna" Table="dba.CFUseky" Type="Column" />
      <Exclude Name="usek" Table="dba.CFUseky" Type="Column" />
      <Exclude Name="popis" Table="dba.CFUseky" Type="Column" />
      <Exclude Name="id_blob" Table="dba.CFUseky" Type="Column" />
      <Exclude Name="sirka" Table="dba.CFUseky" Type="Column" />
      <Exclude Name="vyska" Table="dba.CFUseky" Type="Column" />
      <Exclude Name="PK_CFUseky" Table="dba.CFUseky" Type="Constraint" />
      <Exclude Name="FK_CFUseky_CFVydejny" Table="dba.CFUseky" Type="Constraint" />
      <Exclude Name="FK_CFUseky_Blobs" Table="dba.CFUseky" Type="Constraint" />
      <Exclude Name="id_vydejna" Table="dba.CFVydejny" Type="Column" />
      <Exclude Name="popis" Table="dba.CFVydejny" Type="Column" />
      <Exclude Name="k_jidelnicek" Table="dba.CFVydejny" Type="Column" />
      <Exclude Name="zobrazit" Table="dba.CFVydejny" Type="Column" />
      <Exclude Name="zkratka" Table="dba.CFVydejny" Type="Column" />
      <Exclude Name="pozice_radek" Table="dba.CFVydejny" Type="Column" />
      <Exclude Name="pozice_sloupec" Table="dba.CFVydejny" Type="Column" />
      <Exclude Name="id_jidelna" Table="dba.CFVydejny" Type="Column" />
      <Exclude Name="id_replika" Table="dba.CFVydejny" Type="Column" />
      <Exclude Name="cenovy_koeficient" Table="dba.CFVydejny" Type="Column" />
      <Exclude Name="k_jidelnicek_poptavky" Table="dba.CFVydejny" Type="Column" />
      <Exclude Name="id_sklad" Table="dba.CFVydejny" Type="Column" />
      <Exclude Name="ext_poptavky" Table="dba.CFVydejny" Type="Column" />
      <Exclude Name="poznamka" Table="dba.CFVydejny" Type="Column" />
      <Exclude Name="kod" Table="dba.CFVydejny" Type="Column" />
      <Exclude Name="id_provoz" Table="dba.CFVydejny" Type="Column" />
      <Exclude Name="provoz" Table="dba.CFVydejny" Type="Column" />
      <Exclude Name="bez_burzy" Table="dba.CFVydejny" Type="Column" />
      <Exclude Name="PK_CFVYDEJNY" Table="dba.CFVydejny" Type="Constraint" />
      <Exclude Name="FK_CFVydejny_KJidelnicekPopisSkupin" Table="dba.CFVydejny" Type="Constraint" />
      <Exclude Name="FK_CFVydejny_Sklady" Table="dba.CFVydejny" Type="Constraint" />
      <Exclude Name="FK_CFVYDEJNY_REF_CFJIDELNY" Table="dba.CFVydejny" Type="Constraint" />
      <Exclude Name="FK_CFVYDEJNY_REF_KJIDELNICEKPOPISSKUPIN" Table="dba.CFVydejny" Type="Constraint" />
      <Exclude Name="id_vydejna" Table="dba.CFVydejnyVylukyProvozu" Type="Column" />
      <Exclude Name="DatumOd" Table="dba.CFVydejnyVylukyProvozu" Type="Column" />
      <Exclude Name="DatumDo" Table="dba.CFVydejnyVylukyProvozu" Type="Column" />
      <Exclude Name="PK_CFVydejnyVylukyProvozu" Table="dba.CFVydejnyVylukyProvozu" Type="Constraint" />
      <Exclude Name="id_zarizeni" Table="dba.CFZarizeni" Type="Column" />
      <Exclude Name="funkce_zar" Table="dba.CFZarizeni" Type="Column" />
      <Exclude Name="aktivni" Table="dba.CFZarizeni" Type="Column" />
      <Exclude Name="popis" Table="dba.CFZarizeni" Type="Column" />
      <Exclude Name="id_vydejna" Table="dba.CFZarizeni" Type="Column" />
      <Exclude Name="last_contact" Table="dba.CFZarizeni" Type="Column" />
      <Exclude Name="stav_queue" Table="dba.CFZarizeni" Type="Column" />
      <Exclude Name="id_sklad" Table="dba.CFZarizeni" Type="Column" />
      <Exclude Name="id_funkce" Table="dba.CFZarizeni" Type="Column" />
      <Exclude Name="id_jidelna" Table="dba.CFZarizeni" Type="Column" />
      <Exclude Name="poznamka" Table="dba.CFZarizeni" Type="Column" />
      <Exclude Name="id_dm_schema" Table="dba.CFZarizeni" Type="Column" />
      <Exclude Name="PK_CFZARIZENI" Table="dba.CFZarizeni" Type="Constraint" />
      <Exclude Name="FK_CFZARIZENI_REF_CFVYDEJNY" Table="dba.CFZarizeni" Type="Constraint" />
      <Exclude Name="FK_CFZarizeni_DM_Schema" Table="dba.CFZarizeni" Type="Constraint" />
      <Exclude Name="FK_CFZARIZENI_REF_CFZARIZENIFU" Table="dba.CFZarizeni" Type="Constraint" />
      <Exclude Name="id_funkce" Table="dba.CFZarizeniFunkce" Type="Column" />
      <Exclude Name="popis" Table="dba.CFZarizeniFunkce" Type="Column" />
      <Exclude Name="prefix" Table="dba.CFZarizeniFunkce" Type="Column" />
      <Exclude Name="id_jidelnicek" Table="dba.DM_Jidelnicky" Type="Column" />
      <Exclude Name="nazev" Table="dba.DM_Jidelnicky" Type="Column" />
      <Exclude Name="config" Table="dba.DM_Jidelnicky" Type="Column" />
      <Exclude Name="PK_DM_Jidelnicky" Table="dba.DM_Jidelnicky" Type="Constraint" />
      <Exclude Name="id_komponenta" Table="dba.DM_Komponenty" Type="Column" />
      <Exclude Name="typ" Table="dba.DM_Komponenty" Type="Column" />
      <Exclude Name="nazev" Table="dba.DM_Komponenty" Type="Column" />
      <Exclude Name="config" Table="dba.DM_Komponenty" Type="Column" />
      <Exclude Name="PK_DM_Komponenty" Table="dba.DM_Komponenty" Type="Constraint" />
      <Exclude Name="id_obrazovka" Table="dba.DM_Obrazovky" Type="Column" />
      <Exclude Name="nazev" Table="dba.DM_Obrazovky" Type="Column" />
      <Exclude Name="config" Table="dba.DM_Obrazovky" Type="Column" />
      <Exclude Name="PK_DM_Obrazovky" Table="dba.DM_Obrazovky" Type="Constraint" />
      <Exclude Name="id_sablona" Table="dba.DM_Sablony" Type="Column" />
      <Exclude Name="id_jidelnicek" Table="dba.DM_Sablony" Type="Column" />
      <Exclude Name="nazev" Table="dba.DM_Sablony" Type="Column" />
      <Exclude Name="config" Table="dba.DM_Sablony" Type="Column" />
      <Exclude Name="PK_DM_Sablony" Table="dba.DM_Sablony" Type="Constraint" />
      <Exclude Name="FK_DM_Sablony_Jidelnicky" Table="dba.DM_Sablony" Type="Constraint" />
      <Exclude Name="id_sloupec" Table="dba.DM_Sloupce" Type="Column" />
      <Exclude Name="typ" Table="dba.DM_Sloupce" Type="Column" />
      <Exclude Name="nazev" Table="dba.DM_Sloupce" Type="Column" />
      <Exclude Name="config" Table="dba.DM_Sloupce" Type="Column" />
      <Exclude Name="PK_DM_Sloupce" Table="dba.DM_Sloupce" Type="Constraint" />
      <Exclude Name="id_styl" Table="dba.DM_Styly" Type="Column" />
      <Exclude Name="typ" Table="dba.DM_Styly" Type="Column" />
      <Exclude Name="nazev" Table="dba.DM_Styly" Type="Column" />
      <Exclude Name="config" Table="dba.DM_Styly" Type="Column" />
      <Exclude Name="globalni" Table="dba.DM_Styly" Type="Column" />
      <Exclude Name="PK_DM_Styly" Table="dba.DM_Styly" Type="Constraint" />
      <Exclude Name="id_lk" Table="dba.DOCHAZKA" Type="Column" />
      <Exclude Name="datum" Table="dba.DOCHAZKA" Type="Column" />
      <Exclude Name="pocet" Table="dba.DOCHAZKA" Type="Column" />
      <Exclude Name="pocet2" Table="dba.DOCHAZKA" Type="Column" />
      <Exclude Name="PK_DOCHAZKA" Table="dba.DOCHAZKA" Type="Constraint" />
      <Exclude Name="DOHAZKA_UNIQUE" Table="dba.DOCHAZKA" Type="Constraint" />
      <Exclude Name="id_zarizeni" Table="dba.EET_Log" Type="Column" />
      <Exclude Name="id_uop" Table="dba.EET_Log" Type="Column" />
      <Exclude Name="id_paragon" Table="dba.EET_Log" Type="Column" />
      <Exclude Name="Stav" Table="dba.EET_Log" Type="Column" />
      <Exclude Name="PocetPokusu" Table="dba.EET_Log" Type="Column" />
      <Exclude Name="PocetFatalnichChyb" Table="dba.EET_Log" Type="Column" />
      <Exclude Name="CasVytvoreni" Table="dba.EET_Log" Type="Column" />
      <Exclude Name="DalsiPokus" Table="dba.EET_Log" Type="Column" />
      <Exclude Name="CasOdeslani" Table="dba.EET_Log" Type="Column" />
      <Exclude Name="PlanovacLog" Table="dba.EET_Log" Type="Column" />
      <Exclude Name="PozadavkyLog" Table="dba.EET_Log" Type="Column" />
      <Exclude Name="UdalostiLog" Table="dba.EET_Log" Type="Column" />
      <Exclude Name="PK_EET_Log" Table="dba.EET_Log" Type="Constraint" />
      <Exclude Name="EXPORT_ID" Table="dba.EXP_EXPORT" Type="Column" />
      <Exclude Name="EXPORT_CLASS" Table="dba.EXP_EXPORT" Type="Column" />
      <Exclude Name="POPIS" Table="dba.EXP_EXPORT" Type="Column" />
      <Exclude Name="EXP_FORMAT" Table="dba.EXP_EXPORT" Type="Column" />
      <Exclude Name="OUT_FILE" Table="dba.EXP_EXPORT" Type="Column" />
      <Exclude Name="FLD_SEPARATOR" Table="dba.EXP_EXPORT" Type="Column" />
      <Exclude Name="REC_SEPARATOR" Table="dba.EXP_EXPORT" Type="Column" />
      <Exclude Name="PK_EXP_EXPORT" Table="dba.EXP_EXPORT" Type="Constraint" />
      <Exclude Name="FK_EXP_EXPORT_EXP_EXPORT_CLASS" Table="dba.EXP_EXPORT" Type="Constraint" />
      <Exclude Name="EXPORT_CLASS" Table="dba.EXP_EXPORT_CLASS" Type="Column" />
      <Exclude Name="POPIS" Table="dba.EXP_EXPORT_CLASS" Type="Column" />
      <Exclude Name="FROM_CLAUSE" Table="dba.EXP_EXPORT_CLASS" Type="Column" />
      <Exclude Name="EXPORT_CLASS" Table="dba.EXP_EXPORT_CLASS_COLS" Type="Column" />
      <Exclude Name="EXPORT_SECTION" Table="dba.EXP_EXPORT_CLASS_COLS" Type="Column" />
      <Exclude Name="SELECT_CLAUSE" Table="dba.EXP_EXPORT_CLASS_COLS" Type="Column" />
      <Exclude Name="DISPLAY_TEXT" Table="dba.EXP_EXPORT_CLASS_COLS" Type="Column" />
      <Exclude Name="DATA_TYPE" Table="dba.EXP_EXPORT_CLASS_COLS" Type="Column" />
      <Exclude Name="id" Table="dba.GDPR_Log" Type="Column" />
      <Exclude Name="id_zarizeni" Table="dba.GDPR_Log" Type="Column" />
      <Exclude Name="IdObjekt" Table="dba.GDPR_Log" Type="Column" />
      <Exclude Name="id_vyrizeni" Table="dba.GDPR_Log" Type="Column" />
      <Exclude Name="vyridit_k" Table="dba.GDPR_Log" Type="Column" />
      <Exclude Name="vyrizeno" Table="dba.GDPR_Log" Type="Column" />
      <Exclude Name="vyridil" Table="dba.GDPR_Log" Type="Column" />
      <Exclude Name="tstamp" Table="dba.GDPR_Log" Type="Column" />
      <Exclude Name="uzivatel" Table="dba.GDPR_Log" Type="Column" />
      <Exclude Name="PK_GDPR_Log" Table="dba.GDPR_Log" Type="Constraint" />
      <Exclude Name="FK_GDPR_Log_CFZarizeni" Table="dba.GDPR_Log" Type="Constraint" />
      <Exclude Name="FK_GDPR_Log_SCC_GDPR_TypLogu" Table="dba.GDPR_Log" Type="Constraint" />
      <Exclude Name="id_uo" Table="dba.HaleroveVyrovnani" Type="Column" />
      <Exclude Name="id_lk" Table="dba.HaleroveVyrovnani" Type="Column" />
      <Exclude Name="id_stredisko" Table="dba.HaleroveVyrovnani" Type="Column" />
      <Exclude Name="jmeno" Table="dba.HaleroveVyrovnani" Type="Column" />
      <Exclude Name="ocs" Table="dba.HaleroveVyrovnani" Type="Column" />
      <Exclude Name="pohyb" Table="dba.HaleroveVyrovnani" Type="Column" />
      <Exclude Name="PK_HaleroveVyrovnani" Table="dba.HaleroveVyrovnani" Type="Constraint" />
      <Exclude Name="id_davka" Table="dba.HMB_DATA" Type="Column" />
      <Exclude Name="datum" Table="dba.HMB_DATA" Type="Column" />
      <Exclude Name="id_transakce" Table="dba.HMB_DATA" Type="Column" />
      <Exclude Name="typ" Table="dba.HMB_DATA" Type="Column" />
      <Exclude Name="id_lk" Table="dba.HMB_DATA" Type="Column" />
      <Exclude Name="ucet" Table="dba.HMB_DATA" Type="Column" />
      <Exclude Name="vs" Table="dba.HMB_DATA" Type="Column" />
      <Exclude Name="ss" Table="dba.HMB_DATA" Type="Column" />
      <Exclude Name="castka" Table="dba.HMB_DATA" Type="Column" />
      <Exclude Name="provedeno" Table="dba.HMB_DATA" Type="Column" />
      <Exclude Name="detail" Table="dba.HMB_DATA" Type="Column" />
      <Exclude Name="DatumSplatnosti" Table="dba.HMB_DATA" Type="Column" />
      <Exclude Name="DatumZpracovani" Table="dba.HMB_DATA" Type="Column" />
      <Exclude Name="PK_HMB_DATA" Table="dba.HMB_DATA" Type="Constraint" />
      <Exclude Name="id_davka" Table="dba.HMB_DAVKY" Type="Column" />
      <Exclude Name="datum" Table="dba.HMB_DAVKY" Type="Column" />
      <Exclude Name="pocet_vet" Table="dba.HMB_DAVKY" Type="Column" />
      <Exclude Name="suma" Table="dba.HMB_DAVKY" Type="Column" />
      <Exclude Name="typ" Table="dba.HMB_DAVKY" Type="Column" />
      <Exclude Name="NazevSouboru" Table="dba.HMB_DAVKY" Type="Column" />
      <Exclude Name="Sha512Hash" Table="dba.HMB_DAVKY" Type="Column" />
      <Exclude Name="PK_HMB_DAVKY" Table="dba.HMB_DAVKY" Type="Constraint" />
      <Exclude Name="Datum" Table="dba.HOObjednavky" Type="Column" />
      <Exclude Name="id_lk" Table="dba.HOObjednavky" Type="Column" />
      <Exclude Name="id_jidelna" Table="dba.HOObjednavky" Type="Column" />
      <Exclude Name="id_vydejna" Table="dba.HOObjednavky" Type="Column" />
      <Exclude Name="jidlo_druh" Table="dba.HOObjednavky" Type="Column" />
      <Exclude Name="jidlo_alt" Table="dba.HOObjednavky" Type="Column" />
      <Exclude Name="pocet" Table="dba.HOObjednavky" Type="Column" />
      <Exclude Name="id_sk" Table="dba.HOObjednavky" Type="Column" />
      <Exclude Name="cena" Table="dba.HOObjednavky" Type="Column" />
      <Exclude Name="id_zarizeni" Table="dba.HOObjednavky" Type="Column" />
      <Exclude Name="delet" Table="dba.HOObjednavky" Type="Column" />
      <Exclude Name="volna" Table="dba.HOObjednavky" Type="Column" />
      <Exclude Name="id_zar2" Table="dba.HOObjednavky" Type="Column" />
      <Exclude Name="cas_objednavky" Table="dba.HOObjednavky" Type="Column" />
      <Exclude Name="cnt" Table="dba.HOObjednavky" Type="Column" />
      <Exclude Name="koef_porce" Table="dba.HOObjednavky" Type="Column" />
      <Exclude Name="id_sleva" Table="dba.HOObjednavky" Type="Column" />
      <Exclude Name="data_ext" Table="dba.HOObjednavky" Type="Column" />
      <Exclude Name="alt_dph" Table="dba.HOObjednavky" Type="Column" />
      <Exclude Name="PK__hoobjednavky__3A235722" Table="dba.HOObjednavky" Type="Constraint" />
      <Exclude Name="IndPKHOObjednavky" Table="dba.HOObjednavky" Type="Constraint" />
      <Exclude Name="Code" Table="dba.HS_Countries" Type="Column" />
      <Exclude Name="Name" Table="dba.HS_Countries" Type="Column" />
      <Exclude Name="IsActive" Table="dba.HS_Countries" Type="Column" />
      <Exclude Name="PK_HS_Countries" Table="dba.HS_Countries" Type="Constraint" />
      <Exclude Name="HotelAccountId" Table="dba.HS_HotelAccounts" Type="Column" />
      <Exclude Name="CreationTime" Table="dba.HS_HotelAccounts" Type="Column" />
      <Exclude Name="Code" Table="dba.HS_HotelAccounts" Type="Column" />
      <Exclude Name="State" Table="dba.HS_HotelAccounts" Type="Column" />
      <Exclude Name="Note" Table="dba.HS_HotelAccounts" Type="Column" />
      <Exclude Name="PersonId" Table="dba.HS_HotelAccounts" Type="Column" />
      <Exclude Name="PK_HS_HotelAccounts" Table="dba.HS_HotelAccounts" Type="Constraint" />
      <Exclude Name="" Table="dba.HS_HotelAccounts" Type="Constraint" />
      <Exclude Name="" Table="dba.HS_HotelAccounts" Type="Constraint" />
      <Exclude Name="HotelAccountExtId" Table="dba.HS_HotelAccountsExt" Type="Column" />
      <Exclude Name="NumberOfGuests" Table="dba.HS_HotelAccountsExt" Type="Column" />
      <Exclude Name="Arrival_Number" Table="dba.HS_HotelAccountsExt" Type="Column" />
      <Exclude Name="Arrival_Airport" Table="dba.HS_HotelAccountsExt" Type="Column" />
      <Exclude Name="Departure_Number" Table="dba.HS_HotelAccountsExt" Type="Column" />
      <Exclude Name="Departure_Airport" Table="dba.HS_HotelAccountsExt" Type="Column" />
      <Exclude Name="ReservationSource" Table="dba.HS_HotelAccountsExt" Type="Column" />
      <Exclude Name="PK_HS_HotelAccountsExt" Table="dba.HS_HotelAccountsExt" Type="Constraint" />
      <Exclude Name="" Table="dba.HS_HotelAccountsExt" Type="Constraint" />
      <Exclude Name="PersonId" Table="dba.HS_People" Type="Column" />
      <Exclude Name="CreationTime" Table="dba.HS_People" Type="Column" />
      <Exclude Name="GivenName" Table="dba.HS_People" Type="Column" />
      <Exclude Name="Surname" Table="dba.HS_People" Type="Column" />
      <Exclude Name="Sex" Table="dba.HS_People" Type="Column" />
      <Exclude Name="Birthdate" Table="dba.HS_People" Type="Column" />
      <Exclude Name="Note" Table="dba.HS_People" Type="Column" />
      <Exclude Name="IdentityDocumentCode" Table="dba.HS_People" Type="Column" />
      <Exclude Name="id_lk" Table="dba.HS_People" Type="Column" />
      <Exclude Name="CountryCode" Table="dba.HS_People" Type="Column" />
      <Exclude Name="PK_HS_People" Table="dba.HS_People" Type="Constraint" />
      <Exclude Name="" Table="dba.HS_People" Type="Constraint" />
      <Exclude Name="" Table="dba.HS_People" Type="Constraint" />
      <Exclude Name="RoomOccupancyId" Table="dba.HS_RoomOccupancies" Type="Column" />
      <Exclude Name="OccupiedFrom" Table="dba.HS_RoomOccupancies" Type="Column" />
      <Exclude Name="OccupiedTo" Table="dba.HS_RoomOccupancies" Type="Column" />
      <Exclude Name="RoomId" Table="dba.HS_RoomOccupancies" Type="Column" />
      <Exclude Name="PK_HS_RoomOccupancies" Table="dba.HS_RoomOccupancies" Type="Constraint" />
      <Exclude Name="" Table="dba.HS_RoomOccupancies" Type="Constraint" />
      <Exclude Name="" Table="dba.HS_RoomOccupancies" Type="Constraint" />
      <Exclude Name="RoomRentalId" Table="dba.HS_RoomRentals" Type="Column" />
      <Exclude Name="RentedFrom" Table="dba.HS_RoomRentals" Type="Column" />
      <Exclude Name="RentedTo" Table="dba.HS_RoomRentals" Type="Column" />
      <Exclude Name="HotelAccountId" Table="dba.HS_RoomRentals" Type="Column" />
      <Exclude Name="RoomId" Table="dba.HS_RoomRentals" Type="Column" />
      <Exclude Name="PK_HS_RoomRentals" Table="dba.HS_RoomRentals" Type="Constraint" />
      <Exclude Name="" Table="dba.HS_RoomRentals" Type="Constraint" />
      <Exclude Name="" Table="dba.HS_RoomRentals" Type="Constraint" />
      <Exclude Name="RoomId" Table="dba.HS_Rooms" Type="Column" />
      <Exclude Name="Name" Table="dba.HS_Rooms" Type="Column" />
      <Exclude Name="Description" Table="dba.HS_Rooms" Type="Column" />
      <Exclude Name="NumberOfBeds" Table="dba.HS_Rooms" Type="Column" />
      <Exclude Name="IsActive" Table="dba.HS_Rooms" Type="Column" />
      <Exclude Name="RoomTypeId" Table="dba.HS_Rooms" Type="Column" />
      <Exclude Name="PK_HS_Rooms" Table="dba.HS_Rooms" Type="Constraint" />
      <Exclude Name="" Table="dba.HS_Rooms" Type="Constraint" />
      <Exclude Name="RoomTypeId" Table="dba.HS_RoomTypes" Type="Column" />
      <Exclude Name="Name" Table="dba.HS_RoomTypes" Type="Column" />
      <Exclude Name="Description" Table="dba.HS_RoomTypes" Type="Column" />
      <Exclude Name="IsActive" Table="dba.HS_RoomTypes" Type="Column" />
      <Exclude Name="FixedTime" Table="dba.HS_RoomTypes" Type="Column" />
      <Exclude Name="PK_HS_RoomTypes" Table="dba.HS_RoomTypes" Type="Constraint" />
      <Exclude Name="htisk_id" Table="dba.HTisk" Type="Column" />
      <Exclude Name="popis" Table="dba.HTisk" Type="Column" />
      <Exclude Name="aktivni" Table="dba.HTisk" Type="Column" />
      <Exclude Name="kategorie" Table="dba.HTisk" Type="Column" />
      <Exclude Name="htisk_skupina_id" Table="dba.HTisk" Type="Column" />
      <Exclude Name="PK_HTisk" Table="dba.HTisk" Type="Constraint" />
      <Exclude Name="FK_HTisk_HTiskSkupina" Table="dba.HTisk" Type="Constraint" />
      <Exclude Name="htisk_id" Table="dba.HTiskSkladba" Type="Column" />
      <Exclude Name="htisk_rpt" Table="dba.HTiskSkladba" Type="Column" />
      <Exclude Name="popis" Table="dba.HTiskSkladba" Type="Column" />
      <Exclude Name="rpt_id" Table="dba.HTiskSkladba" Type="Column" />
      <Exclude Name="rpt_params" Table="dba.HTiskSkladba" Type="Column" />
      <Exclude Name="rpt_destination" Table="dba.HTiskSkladba" Type="Column" />
      <Exclude Name="rpt_destination_params" Table="dba.HTiskSkladba" Type="Column" />
      <Exclude Name="active" Table="dba.HTiskSkladba" Type="Column" />
      <Exclude Name="IdZobrazeni" Table="dba.HTiskSkladba" Type="Column" />
      <Exclude Name="PK_HTiskSkladba" Table="dba.HTiskSkladba" Type="Constraint" />
      <Exclude Name="FK_HTiskSkladba_HTisk" Table="dba.HTiskSkladba" Type="Constraint" />
      <Exclude Name="htisk_skupina_id" Table="dba.HTiskSkupina" Type="Column" />
      <Exclude Name="popis" Table="dba.HTiskSkupina" Type="Column" />
      <Exclude Name="systemova" Table="dba.HTiskSkupina" Type="Column" />
      <Exclude Name="PK_HTiskSkupina" Table="dba.HTiskSkupina" Type="Constraint" />
      <Exclude Name="id_jidelna" Table="dba.IntervalVydeje" Type="Column" />
      <Exclude Name="jidlo_druh" Table="dba.IntervalVydeje" Type="Column" />
      <Exclude Name="dow" Table="dba.IntervalVydeje" Type="Column" />
      <Exclude Name="id_stredisko" Table="dba.IntervalVydeje" Type="Column" />
      <Exclude Name="vydej_do" Table="dba.IntervalVydeje" Type="Column" />
      <Exclude Name="vydej_od" Table="dba.IntervalVydeje" Type="Column" />
      <Exclude Name="PK_IntervalVydeje" Table="dba.IntervalVydeje" Type="Constraint" />
      <Exclude Name="FK_IntervalVydeje_JidlaDruhy" Table="dba.IntervalVydeje" Type="Constraint" />
      <Exclude Name="FK_IntervalVydeje_ZarazeniS" Table="dba.IntervalVydeje" Type="Constraint" />
      <Exclude Name="polozka_id" Table="dba.JID_CENIK" Type="Column" />
      <Exclude Name="k_dotace" Table="dba.JID_CENIK" Type="Column" />
      <Exclude Name="id_jidelna" Table="dba.JID_CENIK" Type="Column" />
      <Exclude Name="jidlo_druh" Table="dba.JID_CENIK" Type="Column" />
      <Exclude Name="jidlo_alt" Table="dba.JID_CENIK" Type="Column" />
      <Exclude Name="plati_od" Table="dba.JID_CENIK" Type="Column" />
      <Exclude Name="vyraz" Table="dba.JID_CENIK" Type="Column" />
      <Exclude Name="PK_dba.JID_CENIK" Table="dba.JID_CENIK" Type="Constraint" />
      <Exclude Name="FK_JID_CENIK_KDotace" Table="dba.JID_CENIK" Type="Constraint" />
      <Exclude Name="FK_JID_CENIK_SCC_CENIK_NAZVY" Table="dba.JID_CENIK" Type="Constraint" />
      <Exclude Name="id_jidelna" Table="dba.JID_CENIK_DPH" Type="Column" />
      <Exclude Name="jidlo_druh" Table="dba.JID_CENIK_DPH" Type="Column" />
      <Exclude Name="jidlo_alt" Table="dba.JID_CENIK_DPH" Type="Column" />
      <Exclude Name="k_dotace" Table="dba.JID_CENIK_DPH" Type="Column" />
      <Exclude Name="plati_od" Table="dba.JID_CENIK_DPH" Type="Column" />
      <Exclude Name="id_dph1" Table="dba.JID_CENIK_DPH" Type="Column" />
      <Exclude Name="id_dph2" Table="dba.JID_CENIK_DPH" Type="Column" />
      <Exclude Name="PK_JID_CENIK_DPH" Table="dba.JID_CENIK_DPH" Type="Constraint" />
      <Exclude Name="FK_JID_CENIK_DPH_KDotace" Table="dba.JID_CENIK_DPH" Type="Constraint" />
      <Exclude Name="FK_JID_CENIK_DPH_CFSazbyDPH_1" Table="dba.JID_CENIK_DPH" Type="Constraint" />
      <Exclude Name="FK_JID_CENIK_DPH_CFSazbyDPH_2" Table="dba.JID_CENIK_DPH" Type="Constraint" />
      <Exclude Name="FK_JID_CENIK_DPH_JidlaDruhy" Table="dba.JID_CENIK_DPH" Type="Constraint" />
      <Exclude Name="typ_id" Table="dba.JID_Menu" Type="Column" />
      <Exclude Name="id" Table="dba.JID_Menu" Type="Column" />
      <Exclude Name="parent_typ_id" Table="dba.JID_Menu" Type="Column" />
      <Exclude Name="parent_id" Table="dba.JID_Menu" Type="Column" />
      <Exclude Name="kod" Table="dba.JID_Menu" Type="Column" />
      <Exclude Name="aktivni" Table="dba.JID_Menu" Type="Column" />
      <Exclude Name="PK_JID_Menu" Table="dba.JID_Menu" Type="Constraint" />
      <Exclude Name="FK_JID_Menu_SCC_TypMenu" Table="dba.JID_Menu" Type="Constraint" />
      <Exclude Name="FK_JID_Menu_JID_Menu" Table="dba.JID_Menu" Type="Constraint" />
      <Exclude Name="datum" Table="dba.jidelnicek" Type="Column" />
      <Exclude Name="id_jidelna" Table="dba.jidelnicek" Type="Column" />
      <Exclude Name="jidlo_druh" Table="dba.jidelnicek" Type="Column" />
      <Exclude Name="jidlo_alt" Table="dba.jidelnicek" Type="Column" />
      <Exclude Name="dopredu" Table="dba.jidelnicek" Type="Column" />
      <Exclude Name="vyber" Table="dba.jidelnicek" Type="Column" />
      <Exclude Name="limit" Table="dba.jidelnicek" Type="Column" />
      <Exclude Name="zobrazit" Table="dba.jidelnicek" Type="Column" />
      <Exclude Name="id_vyvarovna" Table="dba.jidelnicek" Type="Column" />
      <Exclude Name="pc" Table="dba.jidelnicek" Type="Column" />
      <Exclude Name="hmotnost_porce" Table="dba.jidelnicek" Type="Column" />
      <Exclude Name="tisk_objednavky" Table="dba.jidelnicek" Type="Column" />
      <Exclude Name="skl_KOD_ZBOZI" Table="dba.jidelnicek" Type="Column" />
      <Exclude Name="KodReceptury" Table="dba.jidelnicek" Type="Column" />
      <Exclude Name="mnozstvi" Table="dba.jidelnicek" Type="Column" />
      <Exclude Name="typove_menu" Table="dba.jidelnicek" Type="Column" />
      <Exclude Name="id_obrazek" Table="dba.jidelnicek" Type="Column" />
      <Exclude Name="externi_display" Table="dba.jidelnicek" Type="Column" />
      <Exclude Name="PK_JIDELNICEK" Table="dba.jidelnicek" Type="Constraint" />
      <Exclude Name="FK_Jidelnicek_Blobs" Table="dba.jidelnicek" Type="Constraint" />
      <Exclude Name="IX_jidelnicek_pc" Table="dba.jidelnicek" Type="Constraint" />
      <Exclude Name="id_jidelna" Table="dba.Jidelnicek_EAN" Type="Column" />
      <Exclude Name="jidlo_druh" Table="dba.Jidelnicek_EAN" Type="Column" />
      <Exclude Name="jidlo_alt" Table="dba.Jidelnicek_EAN" Type="Column" />
      <Exclude Name="EAN" Table="dba.Jidelnicek_EAN" Type="Column" />
      <Exclude Name="PK_Jidelnicek_EAN" Table="dba.Jidelnicek_EAN" Type="Constraint" />
      <Exclude Name="FK_Jidelnicek_EAN_JidlaDruhy" Table="dba.Jidelnicek_EAN" Type="Constraint" />
      <Exclude Name="UQ_Jidelnicek_IdJidelnaEan" Table="dba.Jidelnicek_EAN" Type="Constraint" />
      <Exclude Name="IdSablona" Table="dba.JidelnicekSablony" Type="Column" />
      <Exclude Name="Nazev" Table="dba.JidelnicekSablony" Type="Column" />
      <Exclude Name="IdJidelna" Table="dba.JidelnicekSablony" Type="Column" />
      <Exclude Name="Defaultni" Table="dba.JidelnicekSablony" Type="Column" />
      <Exclude Name="Dow" Table="dba.JidelnicekSablony" Type="Column" />
      <Exclude Name="Dnu" Table="dba.JidelnicekSablony" Type="Column" />
      <Exclude Name="PK_JidelnicekSablony" Table="dba.JidelnicekSablony" Type="Constraint" />
      <Exclude Name="IdSablona" Table="dba.JidelnicekSablonyDetail" Type="Column" />
      <Exclude Name="jidlo_druh" Table="dba.JidelnicekSablonyDetail" Type="Column" />
      <Exclude Name="jidlo_alt" Table="dba.JidelnicekSablonyDetail" Type="Column" />
      <Exclude Name="dopredu" Table="dba.JidelnicekSablonyDetail" Type="Column" />
      <Exclude Name="vyber" Table="dba.JidelnicekSablonyDetail" Type="Column" />
      <Exclude Name="limit" Table="dba.JidelnicekSablonyDetail" Type="Column" />
      <Exclude Name="zobrazit" Table="dba.JidelnicekSablonyDetail" Type="Column" />
      <Exclude Name="id_vyvarovna" Table="dba.JidelnicekSablonyDetail" Type="Column" />
      <Exclude Name="hmotnost_porce" Table="dba.JidelnicekSablonyDetail" Type="Column" />
      <Exclude Name="tisk_objednavky" Table="dba.JidelnicekSablonyDetail" Type="Column" />
      <Exclude Name="tisk_objednavky_vydejny" Table="dba.JidelnicekSablonyDetail" Type="Column" />
      <Exclude Name="poradi" Table="dba.JidelnicekSablonyDetail" Type="Column" />
      <Exclude Name="piktogramy" Table="dba.JidelnicekSablonyDetail" Type="Column" />
      <Exclude Name="skl_KOD_ZBOZI" Table="dba.JidelnicekSablonyDetail" Type="Column" />
      <Exclude Name="KodReceptury" Table="dba.JidelnicekSablonyDetail" Type="Column" />
      <Exclude Name="mnozstvi" Table="dba.JidelnicekSablonyDetail" Type="Column" />
      <Exclude Name="alergeny" Table="dba.JidelnicekSablonyDetail" Type="Column" />
      <Exclude Name="id_obrazek" Table="dba.JidelnicekSablonyDetail" Type="Column" />
      <Exclude Name="externi_display" Table="dba.JidelnicekSablonyDetail" Type="Column" />
      <Exclude Name="PK_JidelnicekSablonyDetail" Table="dba.JidelnicekSablonyDetail" Type="Constraint" />
      <Exclude Name="FK_JidelnicekSablonyDetail_JidelnicekSablony" Table="dba.JidelnicekSablonyDetail" Type="Constraint" />
      <Exclude Name="FK_JidelnicekSablonyDetail_Blobs" Table="dba.JidelnicekSablonyDetail" Type="Constraint" />
      <Exclude Name="jidlo_druh" Table="dba.JidelnicekSkupiny" Type="Column" />
      <Exclude Name="id_jidelna" Table="dba.JidelnicekSkupiny" Type="Column" />
      <Exclude Name="jidlo_alt" Table="dba.JidelnicekSkupiny" Type="Column" />
      <Exclude Name="id_skupinaJ" Table="dba.JidelnicekSkupiny" Type="Column" />
      <Exclude Name="PK_JIDELNICEKSKUPINY" Table="dba.JidelnicekSkupiny" Type="Constraint" />
      <Exclude Name="FK_JIDELNICEKSK_REF_JIDLADRUHY" Table="dba.JidelnicekSkupiny" Type="Constraint" />
      <Exclude Name="FK_JIDELNICEKSK_REF_KJIDELNICEKP" Table="dba.JidelnicekSkupiny" Type="Constraint" />
      <Exclude Name="datum" Table="dba.jidelnicky" Type="Column" />
      <Exclude Name="id_jidelna" Table="dba.jidelnicky" Type="Column" />
      <Exclude Name="platny" Table="dba.jidelnicky" Type="Column" />
      <Exclude Name="PK_JIDELNICKY" Table="dba.jidelnicky" Type="Constraint" />
      <Exclude Name="IndJidelnickyDatum" Table="dba.jidelnicky" Type="Constraint" />
      <Exclude Name="jidlo_druh" Table="dba.JidlaDruhy" Type="Column" />
      <Exclude Name="pocet" Table="dba.JidlaDruhy" Type="Column" />
      <Exclude Name="id_jidelna" Table="dba.JidlaDruhy" Type="Column" />
      <Exclude Name="poradi" Table="dba.JidlaDruhy" Type="Column" />
      <Exclude Name="PK_JIDLADRUHY" Table="dba.JidlaDruhy" Type="Constraint" />
      <Exclude Name="FK_JIDLADRUHY_REF_CFJIDELNY" Table="dba.JidlaDruhy" Type="Constraint" />
      <Exclude Name="IndJidlaDruhy" Table="dba.JidlaDruhy" Type="Constraint" />
      <Exclude Name="IndJidlaDruhyIDJ" Table="dba.JidlaDruhy" Type="Constraint" />
      <Exclude Name="id_fk" Table="dba.KartyF" Type="Column" />
      <Exclude Name="id_lk" Table="dba.KartyF" Type="Column" />
      <Exclude Name="stav" Table="dba.KartyF" Type="Column" />
      <Exclude Name="cenaFK" Table="dba.KartyF" Type="Column" />
      <Exclude Name="kod" Table="dba.KartyF" Type="Column" />
      <Exclude Name="PK_KARTYF" Table="dba.KartyF" Type="Constraint" />
      <Exclude Name="IndKartyFKod" Table="dba.KartyF" Type="Constraint" />
      <Exclude Name="id_fk" Table="dba.KartyFK" Type="Column" />
      <Exclude Name="kod" Table="dba.KartyFK" Type="Column" />
      <Exclude Name="PK_KARTYFK" Table="dba.KartyFK" Type="Constraint" />
      <Exclude Name="IndKartyFKIDFK" Table="dba.KartyFK" Type="Constraint" />
      <Exclude Name="IndKartyFKkod" Table="dba.KartyFK" Type="Constraint" />
      <Exclude Name="id_zarizeni" Table="dba.KartyFP" Type="Column" />
      <Exclude Name="tstamp" Table="dba.KartyFP" Type="Column" />
      <Exclude Name="id_fk" Table="dba.KartyFP" Type="Column" />
      <Exclude Name="id_dpk" Table="dba.KartyFP" Type="Column" />
      <Exclude Name="pocet" Table="dba.KartyFP" Type="Column" />
      <Exclude Name="cena" Table="dba.KartyFP" Type="Column" />
      <Exclude Name="kod" Table="dba.KartyFP" Type="Column" />
      <Exclude Name="id_lk" Table="dba.KartyFP" Type="Column" />
      <Exclude Name="pc" Table="dba.KartyFP" Type="Column" />
      <Exclude Name="k_vydeji" Table="dba.KartyFP" Type="Column" />
      <Exclude Name="poznamka" Table="dba.KartyFP" Type="Column" />
      <Exclude Name="b_k_vydeji" Table="dba.KartyFP" Type="Column" />
      <Exclude Name="PK_KARTYFP" Table="dba.KartyFP" Type="Constraint" />
      <Exclude Name="FK_KartyFP_KartyFDP_Lng" Table="dba.KartyFP" Type="Constraint" />
      <Exclude Name="id_zarizeni" Table="dba.KartyFS" Type="Column" />
      <Exclude Name="id_uok" Table="dba.KartyFS" Type="Column" />
      <Exclude Name="stav_k" Table="dba.KartyFS" Type="Column" />
      <Exclude Name="pocet" Table="dba.KartyFS" Type="Column" />
      <Exclude Name="cena" Table="dba.KartyFS" Type="Column" />
      <Exclude Name="pohyby" Table="dba.KartyFS" Type="Column" />
      <Exclude Name="PK_KartyFS" Table="dba.KartyFS" Type="Constraint" />
      <Exclude Name="id_lk" Table="dba.KartyL" Type="Column" />
      <Exclude Name="id_u" Table="dba.KartyL" Type="Column" />
      <Exclude Name="id_s" Table="dba.KartyL" Type="Column" />
      <Exclude Name="id_str" Table="dba.KartyL" Type="Column" />
      <Exclude Name="id_kj" Table="dba.KartyL" Type="Column" />
      <Exclude Name="id_sk" Table="dba.KartyL" Type="Column" />
      <Exclude Name="platiod" Table="dba.KartyL" Type="Column" />
      <Exclude Name="platido" Table="dba.KartyL" Type="Column" />
      <Exclude Name="tstamp" Table="dba.KartyL" Type="Column" />
      <Exclude Name="jidelnicek_Jazyk" Table="dba.KartyL" Type="Column" />
      <Exclude Name="aplikace_Jazyk" Table="dba.KartyL" Type="Column" />
      <Exclude Name="PK_KARTYL" Table="dba.KartyL" Type="Constraint" />
      <Exclude Name="FK_KARTYL_REF_SKUPINY" Table="dba.KartyL" Type="Constraint" />
      <Exclude Name="FK_KARTYL_REF_ZARAZENIS" Table="dba.KartyL" Type="Constraint" />
      <Exclude Name="FK_KartyL_CFjazyky" Table="dba.KartyL" Type="Constraint" />
      <Exclude Name="FK_KartyL_CFjazyky2" Table="dba.KartyL" Type="Constraint" />
      <Exclude Name="IndKartyLIDLK" Table="dba.KartyL" Type="Constraint" />
      <Exclude Name="id_lk" Table="dba.KartyL_ExtInfo" Type="Column" />
      <Exclude Name="id_typ" Table="dba.KartyL_ExtInfo" Type="Column" />
      <Exclude Name="data" Table="dba.KartyL_ExtInfo" Type="Column" />
      <Exclude Name="PK_KartyL_ExtInfo" Table="dba.KartyL_ExtInfo" Type="Constraint" />
      <Exclude Name="FK_KartyL_ExtInfo_KartyL" Table="dba.KartyL_ExtInfo" Type="Constraint" />
      <Exclude Name="FK_KartyL_ExtInfo_SCC_TypExtInfo" Table="dba.KartyL_ExtInfo" Type="Constraint" />
      <Exclude Name="id_lk" Table="dba.KartyLHistorie" Type="Column" />
      <Exclude Name="datum" Table="dba.KartyLHistorie" Type="Column" />
      <Exclude Name="id_sk" Table="dba.KartyLHistorie" Type="Column" />
      <Exclude Name="id_str" Table="dba.KartyLHistorie" Type="Column" />
      <Exclude Name="PK_KartyLHistorie" Table="dba.KartyLHistorie" Type="Constraint" />
      <Exclude Name="FK_KARTYLHISTOR_REF_SKUPINY" Table="dba.KartyLHistorie" Type="Constraint" />
      <Exclude Name="FK_KARTYLHISTOR_REF_ZARAZENIS" Table="dba.KartyLHistorie" Type="Constraint" />
      <Exclude Name="id_lk" Table="dba.KartyLHistorieLin" Type="Column" />
      <Exclude Name="DatumOd" Table="dba.KartyLHistorieLin" Type="Column" />
      <Exclude Name="DatumDo" Table="dba.KartyLHistorieLin" Type="Column" />
      <Exclude Name="id_sk" Table="dba.KartyLHistorieLin" Type="Column" />
      <Exclude Name="id_str" Table="dba.KartyLHistorieLin" Type="Column" />
      <Exclude Name="k_jidelnicek" Table="dba.KJidelnicek" Type="Column" />
      <Exclude Name="popis" Table="dba.KJidelnicek" Type="Column" />
      <Exclude Name="PK_KJIDELNICEK" Table="dba.KJidelnicek" Type="Constraint" />
      <Exclude Name="IndKJidelnicek" Table="dba.KJidelnicek" Type="Constraint" />
      <Exclude Name="k_jidelnicek" Table="dba.KJidelnicekOmezeni" Type="Column" />
      <Exclude Name="id_skupinaJ" Table="dba.KJidelnicekOmezeni" Type="Column" />
      <Exclude Name="pocet" Table="dba.KJidelnicekOmezeni" Type="Column" />
      <Exclude Name="ruseni_povoleno" Table="dba.KJidelnicekOmezeni" Type="Column" />
      <Exclude Name="preobjednani_povoleno" Table="dba.KJidelnicekOmezeni" Type="Column" />
      <Exclude Name="objednani_povoleno" Table="dba.KJidelnicekOmezeni" Type="Column" />
      <Exclude Name="pocet2" Table="dba.KJidelnicekOmezeni" Type="Column" />
      <Exclude Name="NahraditDochazkou" Table="dba.KJidelnicekOmezeni" Type="Column" />
      <Exclude Name="PK_KJIDELNICEKOMEZENI" Table="dba.KJidelnicekOmezeni" Type="Constraint" />
      <Exclude Name="FK_KJIDELNICEKO_REF_KJIDELNICEK" Table="dba.KJidelnicekOmezeni" Type="Constraint" />
      <Exclude Name="FK_KJIDELNICEKO_REF_KJIDELNICEKP" Table="dba.KJidelnicekOmezeni" Type="Constraint" />
      <Exclude Name="IndKJidOmezeniKS" Table="dba.KJidelnicekOmezeni" Type="Constraint" />
      <Exclude Name="id_skupinaJ" Table="dba.KJidelnicekPopisSkupin" Type="Column" />
      <Exclude Name="popis" Table="dba.KJidelnicekPopisSkupin" Type="Column" />
      <Exclude Name="pozice" Table="dba.KJidelnicekPopisSkupin" Type="Column" />
      <Exclude Name="PK_KJIDELNICEKPOPISSKUPIN" Table="dba.KJidelnicekPopisSkupin" Type="Constraint" />
      <Exclude Name="IndKJidPopisSkupin" Table="dba.KJidelnicekPopisSkupin" Type="Constraint" />
      <Exclude Name="k_pristupMisto" Table="dba.KPristupMisto" Type="Column" />
      <Exclude Name="MapaSad" Table="dba.KPristupMisto" Type="Column" />
      <Exclude Name="popis" Table="dba.KPristupMisto" Type="Column" />
      <Exclude Name="PK_KPRISTUPMISTO" Table="dba.KPristupMisto" Type="Constraint" />
      <Exclude Name="typdotazu_id" Table="dba.KPS_Notifikace" Type="Column" />
      <Exclude Name="id_jidelna" Table="dba.KPS_Notifikace" Type="Column" />
      <Exclude Name="IdUzivatel" Table="dba.KPS_Notifikace" Type="Column" />
      <Exclude Name="id" Table="dba.KPS_Notifikace" Type="Column" />
      <Exclude Name="PK_KPS_Notifikace" Table="dba.KPS_Notifikace" Type="Constraint" />
      <Exclude Name="FK_KPS_Notifikace_KPS_TypDotazu" Table="dba.KPS_Notifikace" Type="Constraint" />
      <Exclude Name="FK_KPS_Notifikace_CFJidelny" Table="dba.KPS_Notifikace" Type="Constraint" />
      <Exclude Name="UQ_KPS_Notifikace" Table="dba.KPS_Notifikace" Type="Constraint" />
      <Exclude Name="zprava_id" Table="dba.KPS_PrecteneZpravyObsluhou" Type="Column" />
      <Exclude Name="IdUzivatel" Table="dba.KPS_PrecteneZpravyObsluhou" Type="Column" />
      <Exclude Name="precteno" Table="dba.KPS_PrecteneZpravyObsluhou" Type="Column" />
      <Exclude Name="PK_KPS_PrecteneZpravyObsluhou" Table="dba.KPS_PrecteneZpravyObsluhou" Type="Constraint" />
      <Exclude Name="FK_KPS_PrecteneZpravyObsluhou_KPS_Zpravy" Table="dba.KPS_PrecteneZpravyObsluhou" Type="Constraint" />
      <Exclude Name="id" Table="dba.KPS_Zpravy" Type="Column" />
      <Exclude Name="parent_id" Table="dba.KPS_Zpravy" Type="Column" />
      <Exclude Name="vytvoreno" Table="dba.KPS_Zpravy" Type="Column" />
      <Exclude Name="typdotazu_id" Table="dba.KPS_Zpravy" Type="Column" />
      <Exclude Name="id_vydejna" Table="dba.KPS_Zpravy" Type="Column" />
      <Exclude Name="id_lk" Table="dba.KPS_Zpravy" Type="Column" />
      <Exclude Name="IdUzivatel" Table="dba.KPS_Zpravy" Type="Column" />
      <Exclude Name="zprava" Table="dba.KPS_Zpravy" Type="Column" />
      <Exclude Name="PK_KPS_Zpravy" Table="dba.KPS_Zpravy" Type="Constraint" />
      <Exclude Name="FK_KPS_Zpravy_parent_id" Table="dba.KPS_Zpravy" Type="Constraint" />
      <Exclude Name="FK_KPS_Zpravy_KPS_TypDotazu" Table="dba.KPS_Zpravy" Type="Constraint" />
      <Exclude Name="FK_KPS_Zpravy_CFVydejny" Table="dba.KPS_Zpravy" Type="Constraint" />
      <Exclude Name="FK_KPS_Zpravy_KartyL" Table="dba.KPS_Zpravy" Type="Constraint" />
      <Exclude Name="id_akce" Table="dba.LogAkce" Type="Column" />
      <Exclude Name="alias" Table="dba.LogAkce" Type="Column" />
      <Exclude Name="popis" Table="dba.LogAkce" Type="Column" />
      <Exclude Name="helptext" Table="dba.LogAkce" Type="Column" />
      <Exclude Name="PK_LOGAKCE" Table="dba.LogAkce" Type="Constraint" />
      <Exclude Name="IndLogAkce" Table="dba.LogAkce" Type="Constraint" />
      <Exclude Name="pc" Table="dba.Menu" Type="Column" />
      <Exclude Name="id_jidelna" Table="dba.Menu" Type="Column" />
      <Exclude Name="skl_KOD_ZBOZI" Table="dba.Menu" Type="Column" />
      <Exclude Name="KodReceptury" Table="dba.Menu" Type="Column" />
      <Exclude Name="mnozstvi" Table="dba.Menu" Type="Column" />
      <Exclude Name="dietni" Table="dba.Menu" Type="Column" />
      <Exclude Name="id_obrazek" Table="dba.Menu" Type="Column" />
      <Exclude Name="OpakovatPo" Table="dba.Menu" Type="Column" />
      <Exclude Name="PK__menu__5E3FF0B0" Table="dba.Menu" Type="Constraint" />
      <Exclude Name="FK_Menu_Blobs" Table="dba.Menu" Type="Constraint" />
      <Exclude Name="id" Table="dba.NAB_Nabidka" Type="Column" />
      <Exclude Name="datum" Table="dba.NAB_Nabidka" Type="Column" />
      <Exclude Name="id_lk" Table="dba.NAB_Nabidka" Type="Column" />
      <Exclude Name="odberatel_nazev" Table="dba.NAB_Nabidka" Type="Column" />
      <Exclude Name="odberatel_adresa" Table="dba.NAB_Nabidka" Type="Column" />
      <Exclude Name="platnost" Table="dba.NAB_Nabidka" Type="Column" />
      <Exclude Name="id_zarizeni" Table="dba.NAB_Nabidka" Type="Column" />
      <Exclude Name="IdUzivatel" Table="dba.NAB_Nabidka" Type="Column" />
      <Exclude Name="vytvoreno" Table="dba.NAB_Nabidka" Type="Column" />
      <Exclude Name="uzavreno" Table="dba.NAB_Nabidka" Type="Column" />
      <Exclude Name="poznamka" Table="dba.NAB_Nabidka" Type="Column" />
      <Exclude Name="PK_NAB_Nabidka" Table="dba.NAB_Nabidka" Type="Constraint" />
      <Exclude Name="FK_NAB_Nabidka_KartyL" Table="dba.NAB_Nabidka" Type="Constraint" />
      <Exclude Name="FK_NAB_Nabidka_CFZarizeni" Table="dba.NAB_Nabidka" Type="Constraint" />
      <Exclude Name="datum" Table="dba.objednavky" Type="Column" />
      <Exclude Name="id_lk" Table="dba.objednavky" Type="Column" />
      <Exclude Name="jidlo_druh" Table="dba.objednavky" Type="Column" />
      <Exclude Name="id_jidelna" Table="dba.objednavky" Type="Column" />
      <Exclude Name="jidlo_alt" Table="dba.objednavky" Type="Column" />
      <Exclude Name="pocet" Table="dba.objednavky" Type="Column" />
      <Exclude Name="vydano" Table="dba.objednavky" Type="Column" />
      <Exclude Name="id_sk" Table="dba.objednavky" Type="Column" />
      <Exclude Name="k_dotace" Table="dba.objednavky" Type="Column" />
      <Exclude Name="cas_objednavky" Table="dba.objednavky" Type="Column" />
      <Exclude Name="cas_odberu" Table="dba.objednavky" Type="Column" />
      <Exclude Name="cena" Table="dba.objednavky" Type="Column" />
      <Exclude Name="dotace" Table="dba.objednavky" Type="Column" />
      <Exclude Name="delet" Table="dba.objednavky" Type="Column" />
      <Exclude Name="id_zarizeni" Table="dba.objednavky" Type="Column" />
      <Exclude Name="id_vydejna" Table="dba.objednavky" Type="Column" />
      <Exclude Name="pc" Table="dba.objednavky" Type="Column" />
      <Exclude Name="volna" Table="dba.objednavky" Type="Column" />
      <Exclude Name="vydano2" Table="dba.objednavky" Type="Column" />
      <Exclude Name="citac" Table="dba.objednavky" Type="Column" />
      <Exclude Name="koef_porce" Table="dba.objednavky" Type="Column" />
      <Exclude Name="id_dph" Table="dba.objednavky" Type="Column" />
      <Exclude Name="id_sleva" Table="dba.objednavky" Type="Column" />
      <Exclude Name="alt_dph" Table="dba.objednavky" Type="Column" />
      <Exclude Name="pc" Table="dba.objednavky_add_info" Type="Column" />
      <Exclude Name="add_info" Table="dba.objednavky_add_info" Type="Column" />
      <Exclude Name="id_typ" Table="dba.objednavky_add_info" Type="Column" />
      <Exclude Name="PK_objednavky_add_info" Table="dba.objednavky_add_info" Type="Constraint" />
      <Exclude Name="FK_objednavky_add_info_objednavky" Table="dba.objednavky_add_info" Type="Constraint" />
      <Exclude Name="pc" Table="dba.objednavky_ceny" Type="Column" />
      <Exclude Name="polozka_id" Table="dba.objednavky_ceny" Type="Column" />
      <Exclude Name="hodnota" Table="dba.objednavky_ceny" Type="Column" />
      <Exclude Name="id" Table="dba.OdeslaneZpravy" Type="Column" />
      <Exclude Name="idSablony" Table="dba.OdeslaneZpravy" Type="Column" />
      <Exclude Name="id_lk" Table="dba.OdeslaneZpravy" Type="Column" />
      <Exclude Name="adresat" Table="dba.OdeslaneZpravy" Type="Column" />
      <Exclude Name="predmet" Table="dba.OdeslaneZpravy" Type="Column" />
      <Exclude Name="htmlText" Table="dba.OdeslaneZpravy" Type="Column" />
      <Exclude Name="odeslano" Table="dba.OdeslaneZpravy" Type="Column" />
      <Exclude Name="mailitem_id" Table="dba.OdeslaneZpravy" Type="Column" />
      <Exclude Name="PK_OdeslaneZpravy" Table="dba.OdeslaneZpravy" Type="Constraint" />
      <Exclude Name="FK_OdeslaneZpravy_SablonyZprav" Table="dba.OdeslaneZpravy" Type="Constraint" />
      <Exclude Name="FK_OdeslaneZpravy_KartyL" Table="dba.OdeslaneZpravy" Type="Constraint" />
      <Exclude Name="id_zarizeni" Table="dba.Paragony" Type="Column" />
      <Exclude Name="id_uop" Table="dba.Paragony" Type="Column" />
      <Exclude Name="id_paragon" Table="dba.Paragony" Type="Column" />
      <Exclude Name="Tstamp" Table="dba.Paragony" Type="Column" />
      <Exclude Name="id_pokladni" Table="dba.Paragony" Type="Column" />
      <Exclude Name="Soucet" Table="dba.Paragony" Type="Column" />
      <Exclude Name="KUhrade" Table="dba.Paragony" Type="Column" />
      <Exclude Name="Hotovost" Table="dba.Paragony" Type="Column" />
      <Exclude Name="id_lk" Table="dba.Paragony" Type="Column" />
      <Exclude Name="id_uo" Table="dba.Paragony" Type="Column" />
      <Exclude Name="id_FM" Table="dba.Paragony" Type="Column" />
      <Exclude Name="Poznamka" Table="dba.Paragony" Type="Column" />
      <Exclude Name="usek" Table="dba.Paragony" Type="Column" />
      <Exclude Name="CasProdeje" Table="dba.Paragony" Type="Column" />
      <Exclude Name="CisloDokladu" Table="dba.Paragony" Type="Column" />
      <Exclude Name="RozpisDph" Table="dba.Paragony" Type="Column" />
      <Exclude Name="atributy" Table="dba.Paragony" Type="Column" />
      <Exclude Name="PK_Paragony" Table="dba.Paragony" Type="Constraint" />
      <Exclude Name="id_zarizeni" Table="dba.ParagonyRadky" Type="Column" />
      <Exclude Name="id_uop" Table="dba.ParagonyRadky" Type="Column" />
      <Exclude Name="id_paragon" Table="dba.ParagonyRadky" Type="Column" />
      <Exclude Name="id_polozka" Table="dba.ParagonyRadky" Type="Column" />
      <Exclude Name="id_sklad" Table="dba.ParagonyRadky" Type="Column" />
      <Exclude Name="id_sortiment" Table="dba.ParagonyRadky" Type="Column" />
      <Exclude Name="Jednotka" Table="dba.ParagonyRadky" Type="Column" />
      <Exclude Name="cena_jednotky" Table="dba.ParagonyRadky" Type="Column" />
      <Exclude Name="id_dph" Table="dba.ParagonyRadky" Type="Column" />
      <Exclude Name="mnozstvi" Table="dba.ParagonyRadky" Type="Column" />
      <Exclude Name="cena" Table="dba.ParagonyRadky" Type="Column" />
      <Exclude Name="sleva" Table="dba.ParagonyRadky" Type="Column" />
      <Exclude Name="ListPC" Table="dba.ParagonyRadky" Type="Column" />
      <Exclude Name="cena_nakupni" Table="dba.ParagonyRadky" Type="Column" />
      <Exclude Name="id_sleva" Table="dba.ParagonyRadky" Type="Column" />
      <Exclude Name="CenaProDotace" Table="dba.ParagonyRadky" Type="Column" />
      <Exclude Name="CenaRadku" Table="dba.ParagonyRadky" Type="Column" />
      <Exclude Name="Priznak" Table="dba.ParagonyRadky" Type="Column" />
      <Exclude Name="atributy" Table="dba.ParagonyRadky" Type="Column" />
      <Exclude Name="PK_ParagonyRadky" Table="dba.ParagonyRadky" Type="Constraint" />
      <Exclude Name="FK_ParagonyRadky_Paragony" Table="dba.ParagonyRadky" Type="Constraint" />
      <Exclude Name="druh" Table="dba.PlatidlaDruhy" Type="Column" />
      <Exclude Name="popis" Table="dba.PlatidlaDruhy" Type="Column" />
      <Exclude Name="id_uhrady" Table="dba.PlatidlaDruhy" Type="Column" />
      <Exclude Name="vracet" Table="dba.PlatidlaDruhy" Type="Column" />
      <Exclude Name="zaokrouhleni_Typ" Table="dba.PlatidlaDruhy" Type="Column" />
      <Exclude Name="zaokrouhleni_DesMista" Table="dba.PlatidlaDruhy" Type="Column" />
      <Exclude Name="tisk_PocetKopii" Table="dba.PlatidlaDruhy" Type="Column" />
      <Exclude Name="PK_PlatidlaDruhy" Table="dba.PlatidlaDruhy" Type="Constraint" />
      <Exclude Name="FK_PlatidlaDruhy_PokladnaPDU_Lng" Table="dba.PlatidlaDruhy" Type="Constraint" />
      <Exclude Name="id_lk" Table="dba.Pokladna_NahradniPrihlaseni" Type="Column" />
      <Exclude Name="id_zarizeni" Table="dba.Pokladna_NahradniPrihlaseni" Type="Column" />
      <Exclude Name="PK_Pokladna_NahradniPrihlaseni" Table="dba.Pokladna_NahradniPrihlaseni" Type="Constraint" />
      <Exclude Name="FK_Pokladna_NahradniPrihlaseni_KartyL" Table="dba.Pokladna_NahradniPrihlaseni" Type="Constraint" />
      <Exclude Name="FK_Pokladna_NahradniPrihlaseni_CFZarizeni" Table="dba.Pokladna_NahradniPrihlaseni" Type="Constraint" />
      <Exclude Name="id_zarizeni" Table="dba.Pokladna_UhradyPoradi" Type="Column" />
      <Exclude Name="id_uhrady" Table="dba.Pokladna_UhradyPoradi" Type="Column" />
      <Exclude Name="Kod" Table="dba.Pokladna_UhradyPoradi" Type="Column" />
      <Exclude Name="Poradi" Table="dba.Pokladna_UhradyPoradi" Type="Column" />
      <Exclude Name="Zobrazit" Table="dba.Pokladna_UhradyPoradi" Type="Column" />
      <Exclude Name="PK_Pokladna_UhradyPoradi" Table="dba.Pokladna_UhradyPoradi" Type="Constraint" />
      <Exclude Name="FK_Pokladna_UhradyPoradi_CFZarizeni" Table="dba.Pokladna_UhradyPoradi" Type="Constraint" />
      <Exclude Name="FK_Pokladna_UhradyPoradi_PokladnaPDU_Lng" Table="dba.Pokladna_UhradyPoradi" Type="Constraint" />
      <Exclude Name="id_zarizeni" Table="dba.PokladnaP" Type="Column" />
      <Exclude Name="id_uop" Table="dba.PokladnaP" Type="Column" />
      <Exclude Name="id_pokladni" Table="dba.PokladnaP" Type="Column" />
      <Exclude Name="tstamp" Table="dba.PokladnaP" Type="Column" />
      <Exclude Name="id_dpp" Table="dba.PokladnaP" Type="Column" />
      <Exclude Name="Pohyb" Table="dba.PokladnaP" Type="Column" />
      <Exclude Name="id_lk" Table="dba.PokladnaP" Type="Column" />
      <Exclude Name="pc" Table="dba.PokladnaP" Type="Column" />
      <Exclude Name="id_uo" Table="dba.PokladnaP" Type="Column" />
      <Exclude Name="id_paragon" Table="dba.PokladnaP" Type="Column" />
      <Exclude Name="id_uhrady" Table="dba.PokladnaP" Type="Column" />
      <Exclude Name="poznamka" Table="dba.PokladnaP" Type="Column" />
      <Exclude Name="PK_POKLADNAP" Table="dba.PokladnaP" Type="Constraint" />
      <Exclude Name="pc" Table="dba.PokladnaU" Type="Column" />
      <Exclude Name="Tstamp" Table="dba.PokladnaU" Type="Column" />
      <Exclude Name="id_zarizeni" Table="dba.PokladnaU" Type="Column" />
      <Exclude Name="id_pokladni" Table="dba.PokladnaU" Type="Column" />
      <Exclude Name="id_uo" Table="dba.PokladnaU" Type="Column" />
      <Exclude Name="id_uop" Table="dba.PokladnaU" Type="Column" />
      <Exclude Name="id_dup" Table="dba.PokladnaU" Type="Column" />
      <Exclude Name="id_FM" Table="dba.PokladnaU" Type="Column" />
      <Exclude Name="GT1" Table="dba.PokladnaU" Type="Column" />
      <Exclude Name="GT2" Table="dba.PokladnaU" Type="Column" />
      <Exclude Name="GT3" Table="dba.PokladnaU" Type="Column" />
      <Exclude Name="storna" Table="dba.PokladnaU" Type="Column" />
      <Exclude Name="slevy" Table="dba.PokladnaU" Type="Column" />
      <Exclude Name="vratky" Table="dba.PokladnaU" Type="Column" />
      <Exclude Name="SumObrat" Table="dba.PokladnaU" Type="Column" />
      <Exclude Name="pocet" Table="dba.PokladnaU" Type="Column" />
      <Exclude Name="id_poradi" Table="dba.PokladnaU" Type="Column" />
      <Exclude Name="RozpisDph" Table="dba.PokladnaU" Type="Column" />
      <Exclude Name="PK__PokladnaU__3A179ED3" Table="dba.PokladnaU" Type="Constraint" />
      <Exclude Name="pc" Table="dba.Pokladni" Type="Column" />
      <Exclude Name="id_zarizeni" Table="dba.Pokladni" Type="Column" />
      <Exclude Name="id_pokladni" Table="dba.Pokladni" Type="Column" />
      <Exclude Name="id_opravneni" Table="dba.Pokladni" Type="Column" />
      <Exclude Name="popis" Table="dba.Pokladni" Type="Column" />
      <Exclude Name="heslo" Table="dba.Pokladni" Type="Column" />
      <Exclude Name="delet" Table="dba.Pokladni" Type="Column" />
      <Exclude Name="id_lk" Table="dba.Pokladni" Type="Column" />
      <Exclude Name="login_name" Table="dba.Pokladni" Type="Column" />
      <Exclude Name="jazyk" Table="dba.Pokladni" Type="Column" />
      <Exclude Name="kod" Table="dba.Pokladni" Type="Column" />
      <Exclude Name="PK__Pokladni__02E7657A" Table="dba.Pokladni" Type="Constraint" />
      <Exclude Name="IndPokladni" Table="dba.Pokladni" Type="Constraint" />
      <Exclude Name="id_lk" Table="dba.RAD_SubLogin" Type="Column" />
      <Exclude Name="AccountName" Table="dba.RAD_SubLogin" Type="Column" />
      <Exclude Name="passwordhash" Table="dba.RAD_SubLogin" Type="Column" />
      <Exclude Name="Rights" Table="dba.RAD_SubLogin" Type="Column" />
      <Exclude Name="PK_RAD_SubLogin" Table="dba.RAD_SubLogin" Type="Constraint" />
      <Exclude Name="pc" Table="dba.RekapitulaceNakladu" Type="Column" />
      <Exclude Name="id_uo" Table="dba.RekapitulaceNakladu" Type="Column" />
      <Exclude Name="id_jidelna" Table="dba.RekapitulaceNakladu" Type="Column" />
      <Exclude Name="jidlo_druh" Table="dba.RekapitulaceNakladu" Type="Column" />
      <Exclude Name="druh" Table="dba.RekapitulaceNakladu" Type="Column" />
      <Exclude Name="id_organizace" Table="dba.RekapitulaceNakladu" Type="Column" />
      <Exclude Name="organizace" Table="dba.RekapitulaceNakladu" Type="Column" />
      <Exclude Name="id_uctarna" Table="dba.RekapitulaceNakladu" Type="Column" />
      <Exclude Name="uctarna" Table="dba.RekapitulaceNakladu" Type="Column" />
      <Exclude Name="id_stredisko" Table="dba.RekapitulaceNakladu" Type="Column" />
      <Exclude Name="stredisko" Table="dba.RekapitulaceNakladu" Type="Column" />
      <Exclude Name="k_dotace" Table="dba.RekapitulaceNakladu" Type="Column" />
      <Exclude Name="pocet" Table="dba.RekapitulaceNakladu" Type="Column" />
      <Exclude Name="cena" Table="dba.RekapitulaceNakladu" Type="Column" />
      <Exclude Name="sum_cena" Table="dba.RekapitulaceNakladu" Type="Column" />
      <Exclude Name="n1" Table="dba.RekapitulaceNakladu" Type="Column" />
      <Exclude Name="n2" Table="dba.RekapitulaceNakladu" Type="Column" />
      <Exclude Name="n3" Table="dba.RekapitulaceNakladu" Type="Column" />
      <Exclude Name="n4" Table="dba.RekapitulaceNakladu" Type="Column" />
      <Exclude Name="n5" Table="dba.RekapitulaceNakladu" Type="Column" />
      <Exclude Name="d1" Table="dba.RekapitulaceNakladu" Type="Column" />
      <Exclude Name="d2" Table="dba.RekapitulaceNakladu" Type="Column" />
      <Exclude Name="d3" Table="dba.RekapitulaceNakladu" Type="Column" />
      <Exclude Name="d4" Table="dba.RekapitulaceNakladu" Type="Column" />
      <Exclude Name="d5" Table="dba.RekapitulaceNakladu" Type="Column" />
      <Exclude Name="stredisko_kod" Table="dba.RekapitulaceNakladu" Type="Column" />
      <Exclude Name="DPH" Table="dba.RekapitulaceNakladu" Type="Column" />
      <Exclude Name="srazet" Table="dba.RekapitulaceNakladu" Type="Column" />
      <Exclude Name="koef_porce" Table="dba.RekapitulaceNakladu" Type="Column" />
      <Exclude Name="PK_REKAPITULACENAKLADU" Table="dba.RekapitulaceNakladu" Type="Constraint" />
      <Exclude Name="rodina_id" Table="dba.RodinaDietVybery" Type="Column" />
      <Exclude Name="id_jidelna" Table="dba.RodinaDietVybery" Type="Column" />
      <Exclude Name="jidlo_druh" Table="dba.RodinaDietVybery" Type="Column" />
      <Exclude Name="nazev" Table="dba.RodinaDietVybery" Type="Column" />
      <Exclude Name="PK_RodinaDietVybery" Table="dba.RodinaDietVybery" Type="Constraint" />
      <Exclude Name="FK_RodinaDietVybery_JidlaDruhy" Table="dba.RodinaDietVybery" Type="Constraint" />
      <Exclude Name="id" Table="dba.SablonyZprav" Type="Column" />
      <Exclude Name="popis" Table="dba.SablonyZprav" Type="Column" />
      <Exclude Name="odesilatel" Table="dba.SablonyZprav" Type="Column" />
      <Exclude Name="dulezitost" Table="dba.SablonyZprav" Type="Column" />
      <Exclude Name="citlivost" Table="dba.SablonyZprav" Type="Column" />
      <Exclude Name="logovat" Table="dba.SablonyZprav" Type="Column" />
      <Exclude Name="dataSource" Table="dba.SablonyZprav" Type="Column" />
      <Exclude Name="dataParametr" Table="dba.SablonyZprav" Type="Column" />
      <Exclude Name="PK_SablonyZprav" Table="dba.SablonyZprav" Type="Constraint" />
      <Exclude Name="polozka_id" Table="dba.SCC_CENIK_NAZVY" Type="Column" />
      <Exclude Name="popis" Table="dba.SCC_CENIK_NAZVY" Type="Column" />
      <Exclude Name="vstupni_hodnota" Table="dba.SCC_CENIK_NAZVY" Type="Column" />
      <Exclude Name="sl_rekapitulace" Table="dba.SCC_CENIK_NAZVY" Type="Column" />
      <Exclude Name="denni_zmena" Table="dba.SCC_CENIK_NAZVY" Type="Column" />
      <Exclude Name="sl_rentability" Table="dba.SCC_CENIK_NAZVY" Type="Column" />
      <Exclude Name="sl_dotace" Table="dba.SCC_CENIK_NAZVY" Type="Column" />
      <Exclude Name="zobrazit" Table="dba.SCC_CENIK_NAZVY" Type="Column" />
      <Exclude Name="poznamka" Table="dba.SCC_CENIK_NAZVY" Type="Column" />
      <Exclude Name="systemovy" Table="dba.SCC_CENIK_NAZVY" Type="Column" />
      <Exclude Name="pro_maxima" Table="dba.SCC_CENIK_NAZVY" Type="Column" />
      <Exclude Name="pro_jidla" Table="dba.SCC_CENIK_NAZVY" Type="Column" />
      <Exclude Name="pro_zbozi" Table="dba.SCC_CENIK_NAZVY" Type="Column" />
      <Exclude Name="PK_JID_CENIK" Table="dba.SCC_CENIK_NAZVY" Type="Constraint" />
      <Exclude Name="id" Table="dba.SCC_GDPR_TypLogu" Type="Column" />
      <Exclude Name="popis" Table="dba.SCC_GDPR_TypLogu" Type="Column" />
      <Exclude Name="HLEDISKO_KOD" Table="dba.SCC_NUTR_HODNOTY_Popis" Type="Column" />
      <Exclude Name="langid" Table="dba.SCC_NUTR_HODNOTY_Popis" Type="Column" />
      <Exclude Name="POPIS" Table="dba.SCC_NUTR_HODNOTY_Popis" Type="Column" />
      <Exclude Name="PK_SCC_NUTR_HODNOTY_Popis" Table="dba.SCC_NUTR_HODNOTY_Popis" Type="Constraint" />
      <Exclude Name="FK_SCC_NUTR_HODNOTY_Popis_SCC_NUTR_HODNOTY" Table="dba.SCC_NUTR_HODNOTY_Popis" Type="Constraint" />
      <Exclude Name="id" Table="dba.SCC_TypMenu" Type="Column" />
      <Exclude Name="popis" Table="dba.SCC_TypMenu" Type="Column" />
      <Exclude Name="delka_kodu" Table="dba.SCC_TypMenu" Type="Column" />
      <Exclude Name="poradi" Table="dba.SCC_TypMenu" Type="Column" />
      <Exclude Name="parent_id" Table="dba.SCC_TypMenu" Type="Column" />
      <Exclude Name="povinne" Table="dba.SCC_TypMenu" Type="Column" />
      <Exclude Name="do_mnozstvi" Table="dba.SCC_TypMenu" Type="Column" />
      <Exclude Name="PK_SCC_TypMenu" Table="dba.SCC_TypMenu" Type="Constraint" />
      <Exclude Name="FK_SCC_TypMenu_SCC_TypMenu" Table="dba.SCC_TypMenu" Type="Constraint" />
      <Exclude Name="id_sklad" Table="dba.Sklady" Type="Column" />
      <Exclude Name="Delet" Table="dba.Sklady" Type="Column" />
      <Exclude Name="popis" Table="dba.Sklady" Type="Column" />
      <Exclude Name="Kod" Table="dba.Sklady" Type="Column" />
      <Exclude Name="KodPopis" Table="dba.Sklady" Type="Column" />
      <Exclude Name="poznamka" Table="dba.Sklady" Type="Column" />
      <Exclude Name="PK_Sklady" Table="dba.Sklady" Type="Constraint" />
      <Exclude Name="UX_Sklady_id_sklad" Table="dba.Sklady" Type="Constraint" />
      <Exclude Name="id_sk" Table="dba.Skupiny" Type="Column" />
      <Exclude Name="popis" Table="dba.Skupiny" Type="Column" />
      <Exclude Name="k_dotace" Table="dba.Skupiny" Type="Column" />
      <Exclude Name="k_dotace2" Table="dba.Skupiny" Type="Column" />
      <Exclude Name="k_pristup" Table="dba.Skupiny" Type="Column" />
      <Exclude Name="k_pristupMisto" Table="dba.Skupiny" Type="Column" />
      <Exclude Name="k_jidelnicek" Table="dba.Skupiny" Type="Column" />
      <Exclude Name="k_migrace" Table="dba.Skupiny" Type="Column" />
      <Exclude Name="k_cena" Table="dba.Skupiny" Type="Column" />
      <Exclude Name="maxdp" Table="dba.Skupiny" Type="Column" />
      <Exclude Name="id_vydejna" Table="dba.Skupiny" Type="Column" />
      <Exclude Name="k_migracevydejny" Table="dba.Skupiny" Type="Column" />
      <Exclude Name="povol_zobrazenibonu" Table="dba.Skupiny" Type="Column" />
      <Exclude Name="dlimit_kd1" Table="dba.Skupiny" Type="Column" />
      <Exclude Name="mapagrafu" Table="dba.Skupiny" Type="Column" />
      <Exclude Name="strav_limit" Table="dba.Skupiny" Type="Column" />
      <Exclude Name="typ_uctu" Table="dba.Skupiny" Type="Column" />
      <Exclude Name="id_dph_zal" Table="dba.Skupiny" Type="Column" />
      <Exclude Name="CenovaKategorie" Table="dba.Skupiny" Type="Column" />
      <Exclude Name="poznamka" Table="dba.Skupiny" Type="Column" />
      <Exclude Name="PovoleneKombinaceJidel" Table="dba.Skupiny" Type="Column" />
      <Exclude Name="dietni" Table="dba.Skupiny" Type="Column" />
      <Exclude Name="EetZalohy" Table="dba.Skupiny" Type="Column" />
      <Exclude Name="ElektrUctenky" Table="dba.Skupiny" Type="Column" />
      <Exclude Name="pro_anonimizovane" Table="dba.Skupiny" Type="Column" />
      <Exclude Name="nevydaneDoSecKD" Table="dba.Skupiny" Type="Column" />
      <Exclude Name="aktivni" Table="dba.Skupiny" Type="Column" />
      <Exclude Name="PK_SKUPINY" Table="dba.Skupiny" Type="Constraint" />
      <Exclude Name="FK_SKUPINY_REF_CFVYDEJNY" Table="dba.Skupiny" Type="Constraint" />
      <Exclude Name="FK_SKUPINY_REF_KDOTACE" Table="dba.Skupiny" Type="Constraint" />
      <Exclude Name="FK_SKUPINY_REF_KDOTACE2" Table="dba.Skupiny" Type="Constraint" />
      <Exclude Name="FK_SKUPINY_REF_KJIDELNICEK" Table="dba.Skupiny" Type="Constraint" />
      <Exclude Name="FK_SKUPINY_REF_KMIGRACEVYDE" Table="dba.Skupiny" Type="Constraint" />
      <Exclude Name="FK_SKUPINY_REF_KPRISTUP" Table="dba.Skupiny" Type="Constraint" />
      <Exclude Name="FK_SKUPINY_REF_KPRISTUPMIST" Table="dba.Skupiny" Type="Constraint" />
      <Exclude Name="FK_Skupiny_SCC_PovoleneKombinaceJidel" Table="dba.Skupiny" Type="Constraint" />
      <Exclude Name="FK_Skupiny_SCC_CenoveKategorie" Table="dba.Skupiny" Type="Constraint" />
      <Exclude Name="IndSkupinyIDSK" Table="dba.Skupiny" Type="Constraint" />
      <Exclude Name="id_dph" Table="dba.SkupinyDPH" Type="Column" />
      <Exclude Name="dph_proc" Table="dba.SkupinyDPH" Type="Column" />
      <Exclude Name="text" Table="dba.SkupinyDPH" Type="Column" />
      <Exclude Name="TextExtended" Table="dba.SkupinyDPH" Type="Column" />
      <Exclude Name="id_dph" Table="dba.SkupinyDPHFull" Type="Column" />
      <Exclude Name="dph_proc" Table="dba.SkupinyDPHFull" Type="Column" />
      <Exclude Name="text" Table="dba.SkupinyDPHFull" Type="Column" />
      <Exclude Name="TextExtended" Table="dba.SkupinyDPHFull" Type="Column" />
      <Exclude Name="PlatiOd" Table="dba.SkupinyDPHFull" Type="Column" />
      <Exclude Name="PlatiDo" Table="dba.SkupinyDPHFull" Type="Column" />
      <Exclude Name="PK_SKUPINYDPHFULL" Table="dba.SkupinyDPHFull" Type="Constraint" />
      <Exclude Name="id_zprava" Table="dba.SMS_ZPRAVY" Type="Column" />
      <Exclude Name="cas_odeslani" Table="dba.SMS_ZPRAVY" Type="Column" />
      <Exclude Name="snd_id_zarizeni" Table="dba.SMS_ZPRAVY" Type="Column" />
      <Exclude Name="odesilatel" Table="dba.SMS_ZPRAVY" Type="Column" />
      <Exclude Name="msg_text" Table="dba.SMS_ZPRAVY" Type="Column" />
      <Exclude Name="zobrazeno" Table="dba.SMS_ZPRAVY" Type="Column" />
      <Exclude Name="precteno" Table="dba.SMS_ZPRAVY" Type="Column" />
      <Exclude Name="rcp_id_zarizeni" Table="dba.SMS_ZPRAVY" Type="Column" />
      <Exclude Name="rcp_id_lk" Table="dba.SMS_ZPRAVY" Type="Column" />
      <Exclude Name="PK_SMS_ZPRAVY" Table="dba.SMS_ZPRAVY" Type="Constraint" />
      <Exclude Name="id_polozka" Table="dba.SortimentSkupiny" Type="Column" />
      <Exclude Name="text" Table="dba.SortimentSkupiny" Type="Column" />
      <Exclude Name="zobrazit" Table="dba.SortimentSkupiny" Type="Column" />
      <Exclude Name="tisk_rozpis" Table="dba.SortimentSkupiny" Type="Column" />
      <Exclude Name="id_sluzby" Table="dba.SortimentSkupiny" Type="Column" />
      <Exclude Name="dotovatelna" Table="dba.SortimentSkupiny" Type="Column" />
      <Exclude Name="PK_SORTIMENTSKUPINY" Table="dba.SortimentSkupiny" Type="Constraint" />
      <Exclude Name="IndSortimentSkupiny" Table="dba.SortimentSkupiny" Type="Constraint" />
      <Exclude Name="id_s" Table="dba.stravnici" Type="Column" />
      <Exclude Name="ocs" Table="dba.stravnici" Type="Column" />
      <Exclude Name="rcs" Table="dba.stravnici" Type="Column" />
      <Exclude Name="jmeno" Table="dba.stravnici" Type="Column" />
      <Exclude Name="prijmeni" Table="dba.stravnici" Type="Column" />
      <Exclude Name="titul" Table="dba.stravnici" Type="Column" />
      <Exclude Name="id_vydejna" Table="dba.stravnici" Type="Column" />
      <Exclude Name="samAccountName" Table="dba.stravnici" Type="Column" />
      <Exclude Name="passwordhash" Table="dba.stravnici" Type="Column" />
      <Exclude Name="poznamka" Table="dba.stravnici" Type="Column" />
      <Exclude Name="TitulZaJmenem" Table="dba.stravnici" Type="Column" />
      <Exclude Name="PK_STRAVNICI" Table="dba.stravnici" Type="Constraint" />
      <Exclude Name="id" Table="dba.StravniciKontakty" Type="Column" />
      <Exclude Name="id_lk" Table="dba.StravniciKontakty" Type="Column" />
      <Exclude Name="id_typ" Table="dba.StravniciKontakty" Type="Column" />
      <Exclude Name="kontakt" Table="dba.StravniciKontakty" Type="Column" />
      <Exclude Name="PK_StnraviciKontakty" Table="dba.StravniciKontakty" Type="Constraint" />
      <Exclude Name="FK_StravniciKontakty_SCC_TypyKontaktu_Lng" Table="dba.StravniciKontakty" Type="Constraint" />
      <Exclude Name="FK_StravniciKontakty_KartyL" Table="dba.StravniciKontakty" Type="Constraint" />
      <Exclude Name="id_lk" Table="dba.StravniciStravniPredpis" Type="Column" />
      <Exclude Name="plati_od" Table="dba.StravniciStravniPredpis" Type="Column" />
      <Exclude Name="KodSP" Table="dba.StravniciStravniPredpis" Type="Column" />
      <Exclude Name="PK_StravniciStravniPredpis" Table="dba.StravniciStravniPredpis" Type="Constraint" />
      <Exclude Name="FK_StravniciStravniPredpis_StravniPredpisy" Table="dba.StravniciStravniPredpis" Type="Constraint" />
      <Exclude Name="kod" Table="dba.StravniPredpisSkladba" Type="Column" />
      <Exclude Name="id_jidelna" Table="dba.StravniPredpisSkladba" Type="Column" />
      <Exclude Name="jidlo_druh" Table="dba.StravniPredpisSkladba" Type="Column" />
      <Exclude Name="jidlo_alt" Table="dba.StravniPredpisSkladba" Type="Column" />
      <Exclude Name="PK_StravniPredpisSkladba" Table="dba.StravniPredpisSkladba" Type="Constraint" />
      <Exclude Name="FK_StravniPredpisSkladba_Diety" Table="dba.StravniPredpisSkladba" Type="Constraint" />
      <Exclude Name="FK_StravniPredpisSkladba_JidlaDruhy" Table="dba.StravniPredpisSkladba" Type="Constraint" />
      <Exclude Name="FK_StravniPredpisSkladba_StravniPredpisy" Table="dba.StravniPredpisSkladba" Type="Constraint" />
      <Exclude Name="kod" Table="dba.StravniPredpisy" Type="Column" />
      <Exclude Name="parent_kod" Table="dba.StravniPredpisy" Type="Column" />
      <Exclude Name="popis" Table="dba.StravniPredpisy" Type="Column" />
      <Exclude Name="poznamka" Table="dba.StravniPredpisy" Type="Column" />
      <Exclude Name="aktivni" Table="dba.StravniPredpisy" Type="Column" />
      <Exclude Name="PK_StravniPredpisy" Table="dba.StravniPredpisy" Type="Constraint" />
      <Exclude Name="FK_StravniPredpisy_StravniPredpisy" Table="dba.StravniPredpisy" Type="Constraint" />
      <Exclude Name="datum" Table="dba.svatky" Type="Column" />
      <Exclude Name="druh" Table="dba.svatky" Type="Column" />
      <Exclude Name="id_jidelna" Table="dba.svatky" Type="Column" />
      <Exclude Name="PK__svatky__4E1E9780" Table="dba.svatky" Type="Constraint" />
      <Exclude Name="FK_svatky_SvatkyDruh" Table="dba.svatky" Type="Constraint" />
      <Exclude Name="Pracoviste" Table="dba.SVO_PracovisteNahrady" Type="Column" />
      <Exclude Name="TypSmeny" Table="dba.SVO_PracovisteNahrady" Type="Column" />
      <Exclude Name="jidlo_druh" Table="dba.SVO_PracovisteNahrady" Type="Column" />
      <Exclude Name="id_jidelna" Table="dba.SVO_PracovisteNahrady" Type="Column" />
      <Exclude Name="jidlo_alt" Table="dba.SVO_PracovisteNahrady" Type="Column" />
      <Exclude Name="novy_druh" Table="dba.SVO_PracovisteNahrady" Type="Column" />
      <Exclude Name="nova_alt" Table="dba.SVO_PracovisteNahrady" Type="Column" />
      <Exclude Name="PK_SVO_PracovisteNahrady" Table="dba.SVO_PracovisteNahrady" Type="Constraint" />
      <Exclude Name="FK_SVO_PracovisteNahrady_Diety" Table="dba.SVO_PracovisteNahrady" Type="Constraint" />
      <Exclude Name="FK_SVO_PracovisteNahrady_DietyN" Table="dba.SVO_PracovisteNahrady" Type="Constraint" />
      <Exclude Name="FK_SVO_PracovisteNahrady_JidlaDruhy" Table="dba.SVO_PracovisteNahrady" Type="Constraint" />
      <Exclude Name="FK_SVO_PracovisteNahrady_JidlaDruhyN" Table="dba.SVO_PracovisteNahrady" Type="Constraint" />
      <Exclude Name="FK_SVO_PracovisteNahrady_SVO_Pracoviste" Table="dba.SVO_PracovisteNahrady" Type="Constraint" />
      <Exclude Name="id_lk" Table="dba.SVO_StravniciExt" Type="Column" />
      <Exclude Name="id_ubytovani" Table="dba.SVO_StravniciExt" Type="Column" />
      <Exclude Name="KodPracoviste" Table="dba.SVO_StravniciExt" Type="Column" />
      <Exclude Name="TypSmeny_def" Table="dba.SVO_StravniciExt" Type="Column" />
      <Exclude Name="PK_SVO_StravniciExt" Table="dba.SVO_StravniciExt" Type="Constraint" />
      <Exclude Name="FK_SVO_StravniciExt_SVO_Pracoviste" Table="dba.SVO_StravniciExt" Type="Constraint" />
      <Exclude Name="FK_SVO_StravniciExt_SVO_TypySmen" Table="dba.SVO_StravniciExt" Type="Constraint" />
      <Exclude Name="FK_SVO_StravniciExt_UbytovaniS" Table="dba.SVO_StravniciExt" Type="Constraint" />
      <Exclude Name="id_zarizeni" Table="dba.TouchSkupiny" Type="Column" />
      <Exclude Name="id_pokladni" Table="dba.TouchSkupiny" Type="Column" />
      <Exclude Name="TouchSkup" Table="dba.TouchSkupiny" Type="Column" />
      <Exclude Name="TouchR" Table="dba.TouchSkupiny" Type="Column" />
      <Exclude Name="TouchS" Table="dba.TouchSkupiny" Type="Column" />
      <Exclude Name="TouchPopis" Table="dba.TouchSkupiny" Type="Column" />
      <Exclude Name="TouchSkupinyFGUID" Table="dba.TouchSkupiny" Type="Column" />
      <Exclude Name="Color" Table="dba.TouchSkupiny" Type="Column" />
      <Exclude Name="TouchTypSkup" Table="dba.TouchSkupiny" Type="Column" />
      <Exclude Name="externi_display" Table="dba.TouchSkupiny" Type="Column" />
      <Exclude Name="PK_TouchSkupiny" Table="dba.TouchSkupiny" Type="Constraint" />
      <Exclude Name="TouchSkupinyFGUID" Table="dba.TouchSortiment" Type="Column" />
      <Exclude Name="TouchPolozkyR" Table="dba.TouchSortiment" Type="Column" />
      <Exclude Name="TouchPolozkyS" Table="dba.TouchSortiment" Type="Column" />
      <Exclude Name="id_sortiment" Table="dba.TouchSortiment" Type="Column" />
      <Exclude Name="Popis" Table="dba.TouchSortiment" Type="Column" />
      <Exclude Name="PK_TouchSortiment" Table="dba.TouchSortiment" Type="Constraint" />
      <Exclude Name="id_ubytovaniM" Table="dba.UbytovaniM" Type="Column" />
      <Exclude Name="kod" Table="dba.UbytovaniM" Type="Column" />
      <Exclude Name="popis" Table="dba.UbytovaniM" Type="Column" />
      <Exclude Name="id_ubytovaniL" Table="dba.UbytovaniM" Type="Column" />
      <Exclude Name="PK_UbytovaniM" Table="dba.UbytovaniM" Type="Constraint" />
      <Exclude Name="FK_UbytovaniM_UbytovaniL" Table="dba.UbytovaniM" Type="Constraint" />
      <Exclude Name="UQ_UbytovaniM" Table="dba.UbytovaniM" Type="Constraint" />
      <Exclude Name="id_ubytovaniS" Table="dba.UbytovaniS" Type="Column" />
      <Exclude Name="kod" Table="dba.UbytovaniS" Type="Column" />
      <Exclude Name="popis" Table="dba.UbytovaniS" Type="Column" />
      <Exclude Name="id_ubytovaniM" Table="dba.UbytovaniS" Type="Column" />
      <Exclude Name="PK_UbytovaniS" Table="dba.UbytovaniS" Type="Constraint" />
      <Exclude Name="FK_UbytovaniS_UbytovaniM" Table="dba.UbytovaniS" Type="Constraint" />
      <Exclude Name="UQ_UbytovaniS" Table="dba.UbytovaniS" Type="Constraint" />
      <Exclude Name="id_u" Table="dba.Ucty" Type="Column" />
      <Exclude Name="nazev" Table="dba.Ucty" Type="Column" />
      <Exclude Name="srazet" Table="dba.Ucty" Type="Column" />
      <Exclude Name="hdu" Table="dba.Ucty" Type="Column" />
      <Exclude Name="mzu" Table="dba.Ucty" Type="Column" />
      <Exclude Name="mdp" Table="dba.Ucty" Type="Column" />
      <Exclude Name="uzavreno" Table="dba.Ucty" Type="Column" />
      <Exclude Name="inkaso_MinCastka" Table="dba.Ucty" Type="Column" />
      <Exclude Name="inkaso_DorovnatNa" Table="dba.Ucty" Type="Column" />
      <Exclude Name="mmaxjid" Table="dba.Ucty" Type="Column" />
      <Exclude Name="mmaxsort" Table="dba.Ucty" Type="Column" />
      <Exclude Name="ucet_Predcisli" Table="dba.Ucty" Type="Column" />
      <Exclude Name="ucet_Cislo" Table="dba.Ucty" Type="Column" />
      <Exclude Name="ucet_KodBanky" Table="dba.Ucty" Type="Column" />
      <Exclude Name="inkaso_Typ" Table="dba.Ucty" Type="Column" />
      <Exclude Name="inkaso_SpecSymbol" Table="dba.Ucty" Type="Column" />
      <Exclude Name="inkaso_VarSymbol" Table="dba.Ucty" Type="Column" />
      <Exclude Name="souhrnny_doklad" Table="dba.Ucty" Type="Column" />
      <Exclude Name="inkaso_priznak" Table="dba.Ucty" Type="Column" />
      <Exclude Name="PK_UCTY" Table="dba.Ucty" Type="Constraint" />
      <Exclude Name="IndUctyIDU" Table="dba.Ucty" Type="Constraint" />
      <Exclude Name="id_uo" Table="dba.UctyHU" Type="Column" />
      <Exclude Name="DatumOd" Table="dba.UctyHU" Type="Column" />
      <Exclude Name="DatumDo" Table="dba.UctyHU" Type="Column" />
      <Exclude Name="Stav" Table="dba.UctyHU" Type="Column" />
      <Exclude Name="Ok" Table="dba.UctyHU" Type="Column" />
      <Exclude Name="Cas" Table="dba.UctyHU" Type="Column" />
      <Exclude Name="PK_UCTYHU" Table="dba.UctyHU" Type="Constraint" />
      <Exclude Name="IndUctyHU" Table="dba.UctyHU" Type="Constraint" />
      <Exclude Name="id_uo" Table="dba.UctyP" Type="Column" />
      <Exclude Name="id_u" Table="dba.UctyP" Type="Column" />
      <Exclude Name="tstamp" Table="dba.UctyP" Type="Column" />
      <Exclude Name="id_dpu" Table="dba.UctyP" Type="Column" />
      <Exclude Name="pohyb" Table="dba.UctyP" Type="Column" />
      <Exclude Name="id_lk" Table="dba.UctyP" Type="Column" />
      <Exclude Name="id_zarizeni" Table="dba.UctyP" Type="Column" />
      <Exclude Name="id_jidelna" Table="dba.UctyP" Type="Column" />
      <Exclude Name="id_vydejna" Table="dba.UctyP" Type="Column" />
      <Exclude Name="pc" Table="dba.UctyP" Type="Column" />
      <Exclude Name="id_Pokladni" Table="dba.UctyP" Type="Column" />
      <Exclude Name="PcPok" Table="dba.UctyP" Type="Column" />
      <Exclude Name="PK_UctyP" Table="dba.UctyP" Type="Constraint" />
      <Exclude Name="id_uo" Table="dba.UctyS" Type="Column" />
      <Exclude Name="id_u" Table="dba.UctyS" Type="Column" />
      <Exclude Name="tstamp" Table="dba.UctyS" Type="Column" />
      <Exclude Name="stav" Table="dba.UctyS" Type="Column" />
      <Exclude Name="status" Table="dba.UctyS" Type="Column" />
      <Exclude Name="PK_UCTYS" Table="dba.UctyS" Type="Constraint" />
      <Exclude Name="IndUctySPK" Table="dba.UctyS" Type="Constraint" />
      <Exclude Name="id_u" Table="dba.UctySDetail" Type="Column" />
      <Exclude Name="id_uo" Table="dba.UctySDetail" Type="Column" />
      <Exclude Name="id_provozovatel" Table="dba.UctySDetail" Type="Column" />
      <Exclude Name="id_sluzby" Table="dba.UctySDetail" Type="Column" />
      <Exclude Name="id_sklad" Table="dba.UctySDetail" Type="Column" />
      <Exclude Name="id_sortiment" Table="dba.UctySDetail" Type="Column" />
      <Exclude Name="datum" Table="dba.UctySDetail" Type="Column" />
      <Exclude Name="id_dph" Table="dba.UctySDetail" Type="Column" />
      <Exclude Name="mnozstvi" Table="dba.UctySDetail" Type="Column" />
      <Exclude Name="DPH" Table="dba.UctySDetail" Type="Column" />
      <Exclude Name="cena" Table="dba.UctySDetail" Type="Column" />
      <Exclude Name="id_organizace" Table="dba.UctySDetail" Type="Column" />
      <Exclude Name="cena_jednotky" Table="dba.UctySDetail" Type="Column" />
      <Exclude Name="id_zarizeni" Table="dba.UctySDetail" Type="Column" />
      <Exclude Name="plna_cena" Table="dba.UctySDetail" Type="Column" />
      <Exclude Name="id_organizace_dofakturace" Table="dba.UctySDetail" Type="Column" />
      <Exclude Name="PK_UctySDetail" Table="dba.UctySDetail" Type="Constraint" />
      <Exclude Name="FK_UctySDetail_UctyS" Table="dba.UctySDetail" Type="Constraint" />
      <Exclude Name="id_u" Table="dba.UctySDoklady" Type="Column" />
      <Exclude Name="id_uo" Table="dba.UctySDoklady" Type="Column" />
      <Exclude Name="id_provozovatel" Table="dba.UctySDoklady" Type="Column" />
      <Exclude Name="cislo_dokladu" Table="dba.UctySDoklady" Type="Column" />
      <Exclude Name="PK_UctySDoklady" Table="dba.UctySDoklady" Type="Constraint" />
      <Exclude Name="FK_UctySDoklady_UctyS" Table="dba.UctySDoklady" Type="Constraint" />
      <Exclude Name="typ" Table="dba.UctyT" Type="Column" />
      <Exclude Name="popis" Table="dba.UctyT" Type="Column" />
      <Exclude Name="srazet" Table="dba.UctyT" Type="Column" />
      <Exclude Name="hdu" Table="dba.UctyT" Type="Column" />
      <Exclude Name="mzu" Table="dba.UctyT" Type="Column" />
      <Exclude Name="mdp" Table="dba.UctyT" Type="Column" />
      <Exclude Name="mmaxjid" Table="dba.UctyT" Type="Column" />
      <Exclude Name="mmaxsort" Table="dba.UctyT" Type="Column" />
      <Exclude Name="poznamka" Table="dba.UctyT" Type="Column" />
      <Exclude Name="PK_UCTYT" Table="dba.UctyT" Type="Constraint" />
      <Exclude Name="IndUctyT" Table="dba.UctyT" Type="Constraint" />
      <Exclude Name="id" Table="dba.USYSPocitadla" Type="Column" />
      <Exclude Name="Popis" Table="dba.USYSPocitadla" Type="Column" />
      <Exclude Name="PosledniPC" Table="dba.USYSPocitadla" Type="Column" />
      <Exclude Name="MinPC" Table="dba.USYSPocitadla" Type="Column" />
      <Exclude Name="MaxPC" Table="dba.USYSPocitadla" Type="Column" />
      <Exclude Name="PK_USYSPOCITADLA" Table="dba.USYSPocitadla" Type="Constraint" />
      <Exclude Name="IndUSYSPocitadla" Table="dba.USYSPocitadla" Type="Constraint" />
      <Exclude Name="id" Table="dba.USysPocitadlaLokalni" Type="Column" />
      <Exclude Name="Popis" Table="dba.USysPocitadlaLokalni" Type="Column" />
      <Exclude Name="PosledniPC" Table="dba.USysPocitadlaLokalni" Type="Column" />
      <Exclude Name="MinPC" Table="dba.USysPocitadlaLokalni" Type="Column" />
      <Exclude Name="MaxPC" Table="dba.USysPocitadlaLokalni" Type="Column" />
      <Exclude Name="id_zarizeni" Table="dba.USysPocitadlaLokalni" Type="Column" />
      <Exclude Name="PK_USYSPOCITADLALOKALNI" Table="dba.USysPocitadlaLokalni" Type="Constraint" />
      <Exclude Name="IndUSysPocitadlaLokalni" Table="dba.USysPocitadlaLokalni" Type="Constraint" />
      <Exclude Name="id_organizace" Table="dba.Uzaverka" Type="Column" />
      <Exclude Name="organizace" Table="dba.Uzaverka" Type="Column" />
      <Exclude Name="id_uctarna" Table="dba.Uzaverka" Type="Column" />
      <Exclude Name="uctarna" Table="dba.Uzaverka" Type="Column" />
      <Exclude Name="id_stredisko" Table="dba.Uzaverka" Type="Column" />
      <Exclude Name="stredisko" Table="dba.Uzaverka" Type="Column" />
      <Exclude Name="id_sk" Table="dba.Uzaverka" Type="Column" />
      <Exclude Name="srazet" Table="dba.Uzaverka" Type="Column" />
      <Exclude Name="id_uo" Table="dba.Uzaverka" Type="Column" />
      <Exclude Name="kod" Table="dba.Uzaverka" Type="Column" />
      <Exclude Name="id_lk" Table="dba.Uzaverka" Type="Column" />
      <Exclude Name="prijmeni" Table="dba.Uzaverka" Type="Column" />
      <Exclude Name="jmeno" Table="dba.Uzaverka" Type="Column" />
      <Exclude Name="titul" Table="dba.Uzaverka" Type="Column" />
      <Exclude Name="ocs" Table="dba.Uzaverka" Type="Column" />
      <Exclude Name="rcs" Table="dba.Uzaverka" Type="Column" />
      <Exclude Name="prenos" Table="dba.Uzaverka" Type="Column" />
      <Exclude Name="sum_cena" Table="dba.Uzaverka" Type="Column" />
      <Exclude Name="sum_pocet" Table="dba.Uzaverka" Type="Column" />
      <Exclude Name="sum_cenadot" Table="dba.Uzaverka" Type="Column" />
      <Exclude Name="sum_pocetdot" Table="dba.Uzaverka" Type="Column" />
      <Exclude Name="hotovost" Table="dba.Uzaverka" Type="Column" />
      <Exclude Name="srazka" Table="dba.Uzaverka" Type="Column" />
      <Exclude Name="ostatni" Table="dba.Uzaverka" Type="Column" />
      <Exclude Name="prevod" Table="dba.Uzaverka" Type="Column" />
      <Exclude Name="kod_strediska" Table="dba.Uzaverka" Type="Column" />
      <Exclude Name="vklad_prevod" Table="dba.Uzaverka" Type="Column" />
      <Exclude Name="add_info" Table="dba.Uzaverka" Type="Column" />
      <Exclude Name="prispevek" Table="dba.Uzaverka" Type="Column" />
      <Exclude Name="HLEDISKO_KOD" Table="dba.W_NUTR_HODNOTY" Type="Column" />
      <Exclude Name="POPIS" Table="dba.W_NUTR_HODNOTY" Type="Column" />
      <Exclude Name="MJ" Table="dba.W_NUTR_HODNOTY" Type="Column" />
      <Exclude Name="DDD" Table="dba.W_NUTR_HODNOTY" Type="Column" />
      <Exclude Name="PRIORITA" Table="dba.W_NUTR_HODNOTY" Type="Column" />
      <Exclude Name="id_organizace" Table="dba.ZarazeniO" Type="Column" />
      <Exclude Name="popis" Table="dba.ZarazeniO" Type="Column" />
      <Exclude Name="kod" Table="dba.ZarazeniO" Type="Column" />
      <Exclude Name="ICO" Table="dba.ZarazeniO" Type="Column" />
      <Exclude Name="individualni_doklady" Table="dba.ZarazeniO" Type="Column" />
      <Exclude Name="dodaci_listy" Table="dba.ZarazeniO" Type="Column" />
      <Exclude Name="aktivni" Table="dba.ZarazeniO" Type="Column" />
      <Exclude Name="nazev" Table="dba.ZarazeniO" Type="Column" />
      <Exclude Name="IC" Table="dba.ZarazeniO" Type="Column" />
      <Exclude Name="DIC" Table="dba.ZarazeniO" Type="Column" />
      <Exclude Name="polozka_id_plna_cena" Table="dba.ZarazeniO" Type="Column" />
      <Exclude Name="id_ExportSrazek" Table="dba.ZarazeniO" Type="Column" />
      <Exclude Name="PK_ZARAZENIO" Table="dba.ZarazeniO" Type="Constraint" />
      <Exclude Name="FK_ZarazeniO_EXP_ExportSrazekDef" Table="dba.ZarazeniO" Type="Constraint" />
      <Exclude Name="FK_ZarazeniO_SCC_CENIK_NAZVY" Table="dba.ZarazeniO" Type="Constraint" />
      <Exclude Name="IndZarazeniOIDO" Table="dba.ZarazeniO" Type="Constraint" />
      <Exclude Name="id_organizace" Table="dba.ZarazeniO_Adresy" Type="Column" />
      <Exclude Name="id_typ" Table="dba.ZarazeniO_Adresy" Type="Column" />
      <Exclude Name="nazev" Table="dba.ZarazeniO_Adresy" Type="Column" />
      <Exclude Name="adresa" Table="dba.ZarazeniO_Adresy" Type="Column" />
      <Exclude Name="misto" Table="dba.ZarazeniO_Adresy" Type="Column" />
      <Exclude Name="psc" Table="dba.ZarazeniO_Adresy" Type="Column" />
      <Exclude Name="PK_ZarazeniO_Adresy" Table="dba.ZarazeniO_Adresy" Type="Constraint" />
      <Exclude Name="FK_ZarazeniO_Adresy_ZarazeniO" Table="dba.ZarazeniO_Adresy" Type="Constraint" />
      <Exclude Name="FK_ZarazeniO_Adresy_SCC_TypAdresy_Lng" Table="dba.ZarazeniO_Adresy" Type="Constraint" />
      <Exclude Name="id_kontakt" Table="dba.ZarazeniO_Kontakty" Type="Column" />
      <Exclude Name="id_organizace" Table="dba.ZarazeniO_Kontakty" Type="Column" />
      <Exclude Name="id_typ" Table="dba.ZarazeniO_Kontakty" Type="Column" />
      <Exclude Name="PK_ZarazeniO_Kontakty" Table="dba.ZarazeniO_Kontakty" Type="Constraint" />
      <Exclude Name="FK_ZarazeniO_Kontakty_Kontakty" Table="dba.ZarazeniO_Kontakty" Type="Constraint" />
      <Exclude Name="FK_ZarazeniO_Kontakty_ZarazeniO" Table="dba.ZarazeniO_Kontakty" Type="Constraint" />
      <Exclude Name="FK_ZarazeniO_Kontakty_SCC_TypKontaktuOrg" Table="dba.ZarazeniO_Kontakty" Type="Constraint" />
      <Exclude Name="id_stredisko" Table="dba.ZarazeniS" Type="Column" />
      <Exclude Name="id_uctarna" Table="dba.ZarazeniS" Type="Column" />
      <Exclude Name="popis" Table="dba.ZarazeniS" Type="Column" />
      <Exclude Name="kod" Table="dba.ZarazeniS" Type="Column" />
      <Exclude Name="id_vydejna" Table="dba.ZarazeniS" Type="Column" />
      <Exclude Name="aktivni" Table="dba.ZarazeniS" Type="Column" />
      <Exclude Name="id_sablona" Table="dba.ZarazeniS" Type="Column" />
      <Exclude Name="pro_anonimizovane" Table="dba.ZarazeniS" Type="Column" />
      <Exclude Name="kodEko" Table="dba.ZarazeniS" Type="Column" />
      <Exclude Name="PK_ZARAZENIS" Table="dba.ZarazeniS" Type="Constraint" />
      <Exclude Name="FK_ZARAZENIS_REF_ZARAZENIU" Table="dba.ZarazeniS" Type="Constraint" />
      <Exclude Name="FK_ZarazeniS_HOSablony" Table="dba.ZarazeniS" Type="Constraint" />
      <Exclude Name="IndZarazeniSIDS" Table="dba.ZarazeniS" Type="Constraint" />
      <Exclude Name="id_uctarna" Table="dba.ZarazeniU" Type="Column" />
      <Exclude Name="id_organizace" Table="dba.ZarazeniU" Type="Column" />
      <Exclude Name="popis" Table="dba.ZarazeniU" Type="Column" />
      <Exclude Name="kod" Table="dba.ZarazeniU" Type="Column" />
      <Exclude Name="zkratka" Table="dba.ZarazeniU" Type="Column" />
      <Exclude Name="aktivni" Table="dba.ZarazeniU" Type="Column" />
      <Exclude Name="PK_ZARAZENIU" Table="dba.ZarazeniU" Type="Constraint" />
      <Exclude Name="FK_ZARAZENIU_REF_ZARAZENIO" Table="dba.ZarazeniU" Type="Constraint" />
      <Exclude Name="IDX_ZarazeniU_kod_UNQ" Table="dba.ZarazeniU" Type="Constraint" />
      <Exclude Name="IndZarazeniUIDU" Table="dba.ZarazeniU" Type="Constraint" />
      <Exclude Name="id_sklad" Table="dba.Zbozi" Type="Column" />
      <Exclude Name="id_skupina" Table="dba.Zbozi" Type="Column" />
      <Exclude Name="id_sortiment" Table="dba.Zbozi" Type="Column" />
      <Exclude Name="id_sortlokal" Table="dba.Zbozi" Type="Column" />
      <Exclude Name="Delet" Table="dba.Zbozi" Type="Column" />
      <Exclude Name="popis" Table="dba.Zbozi" Type="Column" />
      <Exclude Name="zpopis" Table="dba.Zbozi" Type="Column" />
      <Exclude Name="Jednotka" Table="dba.Zbozi" Type="Column" />
      <Exclude Name="JednotkaTyp" Table="dba.Zbozi" Type="Column" />
      <Exclude Name="cena_jednotky" Table="dba.Zbozi" Type="Column" />
      <Exclude Name="cena_nakupni" Table="dba.Zbozi" Type="Column" />
      <Exclude Name="id_dph" Table="dba.Zbozi" Type="Column" />
      <Exclude Name="mnozstvi" Table="dba.Zbozi" Type="Column" />
      <Exclude Name="sleva" Table="dba.Zbozi" Type="Column" />
      <Exclude Name="KodZboziExt" Table="dba.Zbozi" Type="Column" />
      <Exclude Name="EAN" Table="dba.Zbozi" Type="Column" />
      <Exclude Name="volitelna_DPH" Table="dba.Zbozi" Type="Column" />
      <Exclude Name="KodReceptury" Table="dba.Zbozi" Type="Column" />
      <Exclude Name="mj" Table="dba.Zbozi" Type="Column" />
      <Exclude Name="mnozstvi_mj" Table="dba.Zbozi" Type="Column" />
      <Exclude Name="PK_Zbozi" Table="dba.Zbozi" Type="Constraint" />
      <Exclude Name="FK_Zbozi_SortimentSkupiny" Table="dba.Zbozi" Type="Constraint" />
      <Exclude Name="id_sklad" Table="dba.Zbozi_BarCode" Type="Column" />
      <Exclude Name="id_sortiment" Table="dba.Zbozi_BarCode" Type="Column" />
      <Exclude Name="externi" Table="dba.Zbozi_BarCode" Type="Column" />
      <Exclude Name="BarCode" Table="dba.Zbozi_BarCode" Type="Column" />
      <Exclude Name="PK_Zbozi_BarCode" Table="dba.Zbozi_BarCode" Type="Constraint" />
      <Exclude Name="FK_Zbozi_BarCode_Zbozi" Table="dba.Zbozi_BarCode" Type="Constraint" />
      <Exclude Name="IX_Zbozi_BarCode" Table="dba.Zbozi_BarCode" Type="Constraint" />
      <Exclude Name="veta" Table="dba.ZRD" Type="Column" />
      <Exclude Name="pc" Table="dba.ZRD" Type="Column" />
      <Exclude Name="ocs" Table="dba.ZRD" Type="Column" />
      <Exclude Name="rcs" Table="dba.ZRD" Type="Column" />
      <Exclude Name="prijmeni" Table="dba.ZRD" Type="Column" />
      <Exclude Name="jmeno" Table="dba.ZRD" Type="Column" />
      <Exclude Name="titul" Table="dba.ZRD" Type="Column" />
      <Exclude Name="KodOrg" Table="dba.ZRD" Type="Column" />
      <Exclude Name="KodStr" Table="dba.ZRD" Type="Column" />
      <Exclude Name="KodUct" Table="dba.ZRD" Type="Column" />
      <Exclude Name="KodKarty" Table="dba.ZRD" Type="Column" />
      <Exclude Name="CisloKarty" Table="dba.ZRD" Type="Column" />
      <Exclude Name="stav" Table="dba.ZRD" Type="Column" />
      <Exclude Name="PlatiOd" Table="dba.ZRD" Type="Column" />
      <Exclude Name="PlatiDo" Table="dba.ZRD" Type="Column" />
      <Exclude Name="idSkupina" Table="dba.ZRD" Type="Column" />
      <Exclude Name="srazet" Table="dba.ZRD" Type="Column" />
      <Exclude Name="mzu" Table="dba.ZRD" Type="Column" />
      <Exclude Name="mdp" Table="dba.ZRD" Type="Column" />
      <Exclude Name="csu" Table="dba.ZRD" Type="Column" />
      <Exclude Name="hduMax" Table="dba.ZRD" Type="Column" />
      <Exclude Name="hduMin" Table="dba.ZRD" Type="Column" />
      <Exclude Name="Vydejna" Table="dba.ZRD" Type="Column" />
      <Exclude Name="su_cus" Table="dba.ZRD" Type="Column" />
      <Exclude Name="su_ad" Table="dba.ZRD" Type="Column" />
      <Exclude Name="id_lk" Table="dba.ZRD" Type="Column" />
      <Exclude Name="TStamp" Table="dba.ZRD" Type="Column" />
      <Exclude Name="TStampZprac" Table="dba.ZRD" Type="Column" />
      <Exclude Name="Druh" Table="dba.ZRD" Type="Column" />
      <Exclude Name="id_davka" Table="dba.ZRD" Type="Column" />
      <Exclude Name="Err" Table="dba.ZRD" Type="Column" />
      <Exclude Name="Err_text" Table="dba.ZRD" Type="Column" />
      <Exclude Name="hdu" Table="dba.ZRD" Type="Column" />
      <Exclude Name="SAN" Table="dba.ZRD" Type="Column" />
      <Exclude Name="DatZmenyStr" Table="dba.ZRD" Type="Column" />
      <Exclude Name="DatZmenySk" Table="dba.ZRD" Type="Column" />
      <Exclude Name="VarSymbol" Table="dba.ZRD" Type="Column" />
      <Exclude Name="tel" Table="dba.ZRD" Type="Column" />
      <Exclude Name="email" Table="dba.ZRD" Type="Column" />
      <Exclude Name="titul_za" Table="dba.ZRD" Type="Column" />
      <Exclude Name="PK__ZRD__74643BF9" Table="dba.ZRD" Type="Constraint" />
      <Exclude Name="IndZRD" Table="dba.ZRD" Type="Constraint" />
      <Exclude Name="" Table="dba.ANK_Data" Type="Constraint" />
      <Exclude Name="Hlavni" Table="dba.AppServer" Type="Column" />
      <Exclude Name="" Table="dba.Automaty" Type="Constraint" />
      <Exclude Name="tstamp" Table="dba.BLOKACE" Type="Column" />
      <Exclude Name="" Table="dba.BLOKACE" Type="Constraint" />
      <Exclude Name="" Table="dba.BLOKACE" Type="Constraint" />
      <Exclude Name="" Table="dba.BLOKACE" Type="Constraint" />
      <Exclude Name="" Table="dba.CFCasyVydeje" Type="Constraint" />
      <Exclude Name="" Table="dba.CFCiselneRady" Type="Constraint" />
      <Exclude Name="PK_CFCtecka_HW" Table="dba.CFCtecka_HW" Type="Constraint" />
      <Exclude Name="popisCZ" Table="dba.CFGdprAddInfo" Type="Column" />
      <Exclude Name="popisSK" Table="dba.CFGdprAddInfo" Type="Column" />
      <Exclude Name="dietni" Table="dba.CFJidelny" Type="Column" />
      <Exclude Name="id_sklad" Table="dba.CFJidelny" Type="Column" />
      <Exclude Name="poznamka" Table="dba.CFJidelny" Type="Column" />
      <Exclude Name="IndCFJidelny" Table="dba.CFJidelny" Type="Constraint" />
      <Exclude Name="" Table="dba.CFLicence" Type="Constraint" />
      <Exclude Name="id" Table="dba.CFObjedPrav" Type="Column" />
      <Exclude Name="id_vydejna" Table="dba.CFObjedPrav" Type="Column" />
      <Exclude Name="jidlo_alt" Table="dba.CFObjedPrav" Type="Column" />
      <Exclude Name="PK_CFOBJEDPRAV" Table="dba.CFObjedPrav" Type="Constraint" />
      <Exclude Name="" Table="dba.CFObjedPrav" Type="Constraint" />
      <Exclude Name="UX_CFObjedPrav" Table="dba.CFObjedPrav" Type="Constraint" />
      <Exclude Name="IdUB" Table="dba.CFProvozovatele" Type="Column" />
      <Exclude Name="IDUB" Table="dba.CFProvozovatele" Type="Column" />
      <Exclude Name="poznamka" Table="dba.CFServery" Type="Column" />
      <Exclude Name="externi_display" Table="dba.CFSeznamCtecek" Type="Column" />
      <Exclude Name="citac" Table="dba.CFSeznamCtecek" Type="Column" />
      <Exclude Name="id_typvydautomatu" Table="dba.CFSeznamCtecek" Type="Column" />
      <Exclude Name="FK_CFSEZNAMCTEC_REF_CFTYPYCTECEK" Table="dba.CFSeznamCtecek" Type="Constraint" />
      <Exclude Name="IndCFSeznamCtecek" Table="dba.CFSeznamCtecek" Type="Constraint" />
      <Exclude Name="IndCFSeznamSad" Table="dba.CFseznamSad" Type="Constraint" />
      <Exclude Name="FK_CFSYSTKARTY_REF_CFSYSTKARTYF" Table="dba.CFSystKarty" Type="Constraint" />
      <Exclude Name="sirka" Table="dba.CFUseky" Type="Column" />
      <Exclude Name="vyska" Table="dba.CFUseky" Type="Column" />
      <Exclude Name="k_jidelnicek_poptavky" Table="dba.CFVydejny" Type="Column" />
      <Exclude Name="id_sklad" Table="dba.CFVydejny" Type="Column" />
      <Exclude Name="poznamka" Table="dba.CFVydejny" Type="Column" />
      <Exclude Name="id_provoz" Table="dba.CFVydejny" Type="Column" />
      <Exclude Name="" Table="dba.CFVydejny" Type="Constraint" />
      <Exclude Name="" Table="dba.CFVydejnyVylukyProvozu" Type="Constraint" />
      <Exclude Name="id_funkce" Table="dba.CFZarizeni" Type="Column" />
      <Exclude Name="id_jidelna" Table="dba.CFZarizeni" Type="Column" />
      <Exclude Name="aktivni" Table="dba.CFZarizeni" Type="Column" />
      <Exclude Name="FK_CFZARIZENI_REF_CFZARIZENIFU" Table="dba.CFZarizeni" Type="Constraint" />
      <Exclude Name="" Table="dba.CFZarizeni" Type="Constraint" />
      <Exclude Name="" Table="dba.CFZarizeni" Type="Constraint" />
      <Exclude Name="PK_CFZarizeniFunkce" Table="dba.CFZarizeniFunkce" Type="Constraint" />
      <Exclude Name="verze" Table="dba.DM_Jidelnicky" Type="Column" />
      <Exclude Name="verze" Table="dba.DM_Komponenty" Type="Column" />
      <Exclude Name="verze" Table="dba.DM_Obrazovky" Type="Column" />
      <Exclude Name="verze" Table="dba.DM_Sablony" Type="Column" />
      <Exclude Name="verze" Table="dba.DM_Sloupce" Type="Column" />
      <Exclude Name="verze" Table="dba.DM_Styly" Type="Column" />
      <Exclude Name="" Table="dba.DOCHAZKA" Type="Constraint" />
      <Exclude Name="DOHAZKA_UNIQUE" Table="dba.DOCHAZKA" Type="Constraint" />
      <Exclude Name="" Table="dba.EET_Log" Type="Constraint" />
      <Exclude Name="EXPORT_CLASS" Table="dba.EXP_EXPORT" Type="Column" />
      <Exclude Name="EXP_FORMAT" Table="dba.EXP_EXPORT" Type="Column" />
      <Exclude Name="FK_EXP_EXPORT_EXP_EXPORT_CLASS" Table="dba.EXP_EXPORT" Type="Constraint" />
      <Exclude Name="" Table="dba.EXP_EXPORT" Type="Constraint" />
      <Exclude Name="PK_EXP_EXPORT_CLASS" Table="dba.EXP_EXPORT_CLASS" Type="Constraint" />
      <Exclude Name="PK_EXP_EXPORT_CLASS_COLS" Table="dba.EXP_EXPORT_CLASS_COLS" Type="Constraint" />
      <Exclude Name="FK_GDPR_Log_SCC_GDPR_TypLogu" Table="dba.GDPR_Log" Type="Constraint" />
      <Exclude Name="" Table="dba.GDPR_Log" Type="Constraint" />
      <Exclude Name="" Table="dba.GDPR_Log" Type="Constraint" />
      <Exclude Name="id_s" Table="dba.HaleroveVyrovnani" Type="Column" />
      <Exclude Name="id_stredisko" Table="dba.HaleroveVyrovnani" Type="Column" />
      <Exclude Name="ocs" Table="dba.HaleroveVyrovnani" Type="Column" />
      <Exclude Name="" Table="dba.HaleroveVyrovnani" Type="Constraint" />
      <Exclude Name="" Table="dba.HaleroveVyrovnani" Type="Constraint" />
      <Exclude Name="" Table="dba.HaleroveVyrovnani" Type="Constraint" />
      <Exclude Name="id_lk" Table="dba.HMB_DATA" Type="Column" />
      <Exclude Name="ucet" Table="dba.HMB_DATA" Type="Column" />
      <Exclude Name="" Table="dba.HMB_DATA" Type="Constraint" />
      <Exclude Name="" Table="dba.HMB_DATA" Type="Constraint" />
      <Exclude Name="pocet_vet" Table="dba.HMB_DAVKY" Type="Column" />
      <Exclude Name="suma" Table="dba.HMB_DAVKY" Type="Column" />
      <Exclude Name="" Table="dba.HOObjednavky" Type="Constraint" />
      <Exclude Name="" Table="dba.HOObjednavky" Type="Constraint" />
      <Exclude Name="" Table="dba.HOObjednavky" Type="Constraint" />
      <Exclude Name="" Table="dba.HOObjednavky" Type="Constraint" />
      <Exclude Name="" Table="dba.HOObjednavky" Type="Constraint" />
      <Exclude Name="IndPKHOObjednavky" Table="dba.HOObjednavky" Type="Constraint" />
      <Exclude Name="htisk_skupina_id" Table="dba.HTisk" Type="Column" />
      <Exclude Name="" Table="dba.HTiskSkladba" Type="Constraint" />
      <Exclude Name="systemova" Table="dba.HTiskSkupina" Type="Column" />
      <Exclude Name="" Table="dba.IntervalVydeje" Type="Constraint" />
      <Exclude Name="" Table="dba.JID_CENIK" Type="Constraint" />
      <Exclude Name="" Table="dba.JID_CENIK" Type="Constraint" />
      <Exclude Name="" Table="dba.JID_CENIK_DPH" Type="Constraint" />
      <Exclude Name="FK_JID_Menu_JID_Menu" Table="dba.JID_Menu" Type="Constraint" />
      <Exclude Name="pc" Table="dba.jidelnicek" Type="Column" />
      <Exclude Name="tisk_objednavky" Table="dba.jidelnicek" Type="Column" />
      <Exclude Name="mnozstvi" Table="dba.jidelnicek" Type="Column" />
      <Exclude Name="typove_menu" Table="dba.jidelnicek" Type="Column" />
      <Exclude Name="" Table="dba.jidelnicek" Type="Constraint" />
      <Exclude Name="" Table="dba.jidelnicek" Type="Constraint" />
      <Exclude Name="" Table="dba.Jidelnicek_EAN" Type="Constraint" />
      <Exclude Name="UQ_Jidelnicek_IdJidelnaEan" Table="dba.Jidelnicek_EAN" Type="Constraint" />
      <Exclude Name="" Table="dba.Jidelnicek_EAN" Type="Constraint" />
      <Exclude Name="IdSablona" Table="dba.JidelnicekSablony" Type="Column" />
      <Exclude Name="Nazev" Table="dba.JidelnicekSablony" Type="Column" />
      <Exclude Name="IdJidelna" Table="dba.JidelnicekSablony" Type="Column" />
      <Exclude Name="Defaultni" Table="dba.JidelnicekSablony" Type="Column" />
      <Exclude Name="" Table="dba.JidelnicekSablony" Type="Constraint" />
      <Exclude Name="dopredu" Table="dba.JidelnicekSablonyDetail" Type="Column" />
      <Exclude Name="vyber" Table="dba.JidelnicekSablonyDetail" Type="Column" />
      <Exclude Name="zobrazit" Table="dba.JidelnicekSablonyDetail" Type="Column" />
      <Exclude Name="mnozstvi" Table="dba.JidelnicekSablonyDetail" Type="Column" />
      <Exclude Name="" Table="dba.JidelnicekSkupiny" Type="Constraint" />
      <Exclude Name="platny" Table="dba.jidelnicky" Type="Column" />
      <Exclude Name="IndJidelnickyDatum" Table="dba.jidelnicky" Type="Constraint" />
      <Exclude Name="IndJidlaDruhy" Table="dba.JidlaDruhy" Type="Constraint" />
      <Exclude Name="IndJidlaDruhyIDJ" Table="dba.JidlaDruhy" Type="Constraint" />
      <Exclude Name="id_lk" Table="dba.KartyF" Type="Column" />
      <Exclude Name="" Table="dba.KartyF" Type="Constraint" />
      <Exclude Name="IndKartyFKod" Table="dba.KartyF" Type="Constraint" />
      <Exclude Name="IndKartyFKIDFK" Table="dba.KartyFK" Type="Constraint" />
      <Exclude Name="IndKartyFKkod" Table="dba.KartyFK" Type="Constraint" />
      <Exclude Name="tstamp" Table="dba.KartyFP" Type="Column" />
      <Exclude Name="id_fk" Table="dba.KartyFP" Type="Column" />
      <Exclude Name="id_dpk" Table="dba.KartyFP" Type="Column" />
      <Exclude Name="pocet" Table="dba.KartyFP" Type="Column" />
      <Exclude Name="cena" Table="dba.KartyFP" Type="Column" />
      <Exclude Name="id_lk" Table="dba.KartyFP" Type="Column" />
      <Exclude Name="pc" Table="dba.KartyFP" Type="Column" />
      <Exclude Name="k_vydeji" Table="dba.KartyFP" Type="Column" />
      <Exclude Name="poznamka" Table="dba.KartyFP" Type="Column" />
      <Exclude Name="b_k_vydeji" Table="dba.KartyFP" Type="Column" />
      <Exclude Name="FK_KartyFP_KartyFDP_Lng" Table="dba.KartyFP" Type="Constraint" />
      <Exclude Name="" Table="dba.KartyFP" Type="Constraint" />
      <Exclude Name="" Table="dba.KartyFP" Type="Constraint" />
      <Exclude Name="PK_KartyFS" Table="dba.KartyFS" Type="Constraint" />
      <Exclude Name="id_kj" Table="dba.KartyL" Type="Column" />
      <Exclude Name="jidelnicek_Jazyk" Table="dba.KartyL" Type="Column" />
      <Exclude Name="aplikace_Jazyk" Table="dba.KartyL" Type="Column" />
      <Exclude Name="" Table="dba.KartyL" Type="Constraint" />
      <Exclude Name="" Table="dba.KartyL" Type="Constraint" />
      <Exclude Name="IndKartyLIDLK" Table="dba.KartyL" Type="Constraint" />
      <Exclude Name="FK_KartyL_ExtInfo_SCC_TypExtInfo" Table="dba.KartyL_ExtInfo" Type="Constraint" />
      <Exclude Name="datum" Table="dba.KartyLHistorie" Type="Column" />
      <Exclude Name="" Table="dba.KartyLHistorie" Type="Constraint" />
      <Exclude Name="id_sk" Table="dba.KartyLHistorieLin" Type="Column" />
      <Exclude Name="id_str" Table="dba.KartyLHistorieLin" Type="Column" />
      <Exclude Name="PK_KartyLHistorieLin" Table="dba.KartyLHistorieLin" Type="Constraint" />
      <Exclude Name="IndKJidelnicek" Table="dba.KJidelnicek" Type="Constraint" />
      <Exclude Name="IndKJidOmezeniKS" Table="dba.KJidelnicekOmezeni" Type="Constraint" />
      <Exclude Name="IndKJidPopisSkupin" Table="dba.KJidelnicekPopisSkupin" Type="Constraint" />
      <Exclude Name="MapaSad" Table="dba.KPristupMisto" Type="Column" />
      <Exclude Name="id" Table="dba.KPS_Notifikace" Type="Column" />
      <Exclude Name="Id" Table="dba.KPS_Notifikace" Type="Column" />
      <Exclude Name="PK_KPS_Notifikace" Table="dba.KPS_Notifikace" Type="Constraint" />
      <Exclude Name="" Table="dba.KPS_Notifikace" Type="Constraint" />
      <Exclude Name="UQ_KPS_Notifikace" Table="dba.KPS_Notifikace" Type="Constraint" />
      <Exclude Name="" Table="dba.KPS_PrecteneZpravyObsluhou" Type="Constraint" />
      <Exclude Name="id" Table="dba.KPS_Zpravy" Type="Column" />
      <Exclude Name="" Table="dba.KPS_Zpravy" Type="Constraint" />
      <Exclude Name="IndLogAkce" Table="dba.LogAkce" Type="Constraint" />
      <Exclude Name="id_jidelna" Table="dba.Menu" Type="Column" />
      <Exclude Name="mnozstvi" Table="dba.Menu" Type="Column" />
      <Exclude Name="" Table="dba.Menu" Type="Constraint" />
      <Exclude Name="" Table="dba.NAB_Nabidka" Type="Constraint" />
      <Exclude Name="id_jidelna" Table="dba.objednavky" Type="Column" />
      <Exclude Name="id_sk" Table="dba.objednavky" Type="Column" />
      <Exclude Name="id_zarizeni" Table="dba.objednavky" Type="Column" />
      <Exclude Name="PK_objednavky" Table="dba.objednavky" Type="Constraint" />
      <Exclude Name="FK_objednavky_add_info_objednavky" Table="dba.objednavky_add_info" Type="Constraint" />
      <Exclude Name="" Table="dba.objednavky_add_info" Type="Constraint" />
      <Exclude Name="PK_objednavky_ceny" Table="dba.objednavky_ceny" Type="Constraint" />
      <Exclude Name="adresat" Table="dba.OdeslaneZpravy" Type="Column" />
      <Exclude Name="CasProdeje" Table="dba.Paragony" Type="Column" />
      <Exclude Name="" Table="dba.Paragony" Type="Constraint" />
      <Exclude Name="" Table="dba.Paragony" Type="Constraint" />
      <Exclude Name="" Table="dba.Paragony" Type="Constraint" />
      <Exclude Name="" Table="dba.ParagonyRadky" Type="Constraint" />
      <Exclude Name="" Table="dba.ParagonyRadky" Type="Constraint" />
      <Exclude Name="" Table="dba.ParagonyRadky" Type="Constraint" />
      <Exclude Name="" Table="dba.ParagonyRadky" Type="Constraint" />
      <Exclude Name="FK_PlatidlaDruhy_PokladnaPDU_Lng" Table="dba.PlatidlaDruhy" Type="Constraint" />
      <Exclude Name="FK_Pokladna_NahradniPrihlaseni_KartyL" Table="dba.Pokladna_NahradniPrihlaseni" Type="Constraint" />
      <Exclude Name="" Table="dba.Pokladna_NahradniPrihlaseni" Type="Constraint" />
      <Exclude Name="FK_Pokladna_UhradyPoradi_PokladnaPDU_Lng" Table="dba.Pokladna_UhradyPoradi" Type="Constraint" />
      <Exclude Name="id_lk" Table="dba.PokladnaP" Type="Column" />
      <Exclude Name="" Table="dba.PokladnaP" Type="Constraint" />
      <Exclude Name="id_uo" Table="dba.PokladnaU" Type="Column" />
      <Exclude Name="" Table="dba.PokladnaU" Type="Constraint" />
      <Exclude Name="id_lk" Table="dba.Pokladni" Type="Column" />
      <Exclude Name="id_zarizeni" Table="dba.Pokladni" Type="Column" />
      <Exclude Name="" Table="dba.Pokladni" Type="Constraint" />
      <Exclude Name="IndPokladni" Table="dba.Pokladni" Type="Constraint" />
      <Exclude Name="passwordhash" Table="dba.RAD_SubLogin" Type="Column" />
      <Exclude Name="stredisko" Table="dba.RekapitulaceNakladu" Type="Column" />
      <Exclude Name="" Table="dba.RekapitulaceNakladu" Type="Constraint" />
      <Exclude Name="" Table="dba.RekapitulaceNakladu" Type="Constraint" />
      <Exclude Name="" Table="dba.RekapitulaceNakladu" Type="Constraint" />
      <Exclude Name="" Table="dba.RekapitulaceNakladu" Type="Constraint" />
      <Exclude Name="" Table="dba.RekapitulaceNakladu" Type="Constraint" />
      <Exclude Name="" Table="dba.RekapitulaceNakladu" Type="Constraint" />
      <Exclude Name="" Table="dba.RekapitulaceNakladu" Type="Constraint" />
      <Exclude Name="" Table="dba.RodinaDietVybery" Type="Constraint" />
      <Exclude Name="logovat" Table="dba.SablonyZprav" Type="Column" />
      <Exclude Name="pro_zbozi" Table="dba.SCC_CENIK_NAZVY" Type="Column" />
      <Exclude Name="PK_SCC_GDPR_TypLogu" Table="dba.SCC_GDPR_TypLogu" Type="Constraint" />
      <Exclude Name="POPIS" Table="dba.SCC_NUTR_HODNOTY_Popis" Type="Column" />
      <Exclude Name="do_mnozstvi" Table="dba.SCC_TypMenu" Type="Column" />
      <Exclude Name="UX_Sklady_id_sklad" Table="dba.Sklady" Type="Constraint" />
      <Exclude Name="k_cena" Table="dba.Skupiny" Type="Column" />
      <Exclude Name="k_dotace" Table="dba.Skupiny" Type="Column" />
      <Exclude Name="k_dotace2" Table="dba.Skupiny" Type="Column" />
      <Exclude Name="k_pristup" Table="dba.Skupiny" Type="Column" />
      <Exclude Name="k_pristupMisto" Table="dba.Skupiny" Type="Column" />
      <Exclude Name="k_jidelnicek" Table="dba.Skupiny" Type="Column" />
      <Exclude Name="k_migracevydejny" Table="dba.Skupiny" Type="Column" />
      <Exclude Name="povol_zobrazenibonu" Table="dba.Skupiny" Type="Column" />
      <Exclude Name="typ_uctu" Table="dba.Skupiny" Type="Column" />
      <Exclude Name="CenovaKategorie" Table="dba.Skupiny" Type="Column" />
      <Exclude Name="poznamka" Table="dba.Skupiny" Type="Column" />
      <Exclude Name="dietni" Table="dba.Skupiny" Type="Column" />
      <Exclude Name="" Table="dba.Skupiny" Type="Constraint" />
      <Exclude Name="IndSkupinyIDSK" Table="dba.Skupiny" Type="Constraint" />
      <Exclude Name="PK_SkupinyDPH" Table="dba.SkupinyDPH" Type="Constraint" />
      <Exclude Name="" Table="dba.SkupinyDPHFull" Type="Constraint" />
      <Exclude Name="rcp_id_zarizeni" Table="dba.SMS_ZPRAVY" Type="Column" />
      <Exclude Name="rcp_id_lk" Table="dba.SMS_ZPRAVY" Type="Column" />
      <Exclude Name="" Table="dba.SMS_ZPRAVY" Type="Constraint" />
      <Exclude Name="" Table="dba.SMS_ZPRAVY" Type="Constraint" />
      <Exclude Name="" Table="dba.SMS_ZPRAVY" Type="Constraint" />
      <Exclude Name="zobrazit" Table="dba.SortimentSkupiny" Type="Column" />
      <Exclude Name="tisk_rozpis" Table="dba.SortimentSkupiny" Type="Column" />
      <Exclude Name="id_sluzby" Table="dba.SortimentSkupiny" Type="Column" />
      <Exclude Name="" Table="dba.SortimentSkupiny" Type="Constraint" />
      <Exclude Name="IndSortimentSkupiny" Table="dba.SortimentSkupiny" Type="Constraint" />
      <Exclude Name="" Table="dba.stravnici" Type="Constraint" />
      <Exclude Name="id" Table="dba.StravniciKontakty" Type="Column" />
      <Exclude Name="FK_StravniciKontakty_SCC_TypyKontaktu_Lng" Table="dba.StravniciKontakty" Type="Constraint" />
      <Exclude Name="" Table="dba.StravniciStravniPredpis" Type="Constraint" />
      <Exclude Name="kod" Table="dba.StravniPredpisSkladba" Type="Column" />
      <Exclude Name="kod" Table="dba.StravniPredpisy" Type="Column" />
      <Exclude Name="parent_kod" Table="dba.StravniPredpisy" Type="Column" />
      <Exclude Name="FK_svatky_SvatkyDruh" Table="dba.svatky" Type="Constraint" />
      <Exclude Name="" Table="dba.svatky" Type="Constraint" />
      <Exclude Name="kod" Table="dba.SVO_PracovisteNahrady" Type="Column" />
      <Exclude Name="" Table="dba.SVO_PracovisteNahrady" Type="Constraint" />
      <Exclude Name="" Table="dba.SVO_StravniciExt" Type="Constraint" />
      <Exclude Name="" Table="dba.TouchSkupiny" Type="Constraint" />
      <Exclude Name="" Table="dba.TouchSortiment" Type="Constraint" />
      <Exclude Name="UQ_UbytovaniM" Table="dba.UbytovaniM" Type="Constraint" />
      <Exclude Name="KEY1" Table="dba.UbytovaniM" Type="Constraint" />
      <Exclude Name="UQ_UbytovaniS" Table="dba.UbytovaniS" Type="Constraint" />
      <Exclude Name="KEY1" Table="dba.UbytovaniS" Type="Constraint" />
      <Exclude Name="ucet_Predcisli" Table="dba.Ucty" Type="Column" />
      <Exclude Name="ucet_Cislo" Table="dba.Ucty" Type="Column" />
      <Exclude Name="ucet_KodBanky" Table="dba.Ucty" Type="Column" />
      <Exclude Name="iban" Table="dba.Ucty" Type="Column" />
      <Exclude Name="IndUctyIDU" Table="dba.Ucty" Type="Constraint" />
      <Exclude Name="Ok" Table="dba.UctyHU" Type="Column" />
      <Exclude Name="IndUctyHU" Table="dba.UctyHU" Type="Constraint" />
      <Exclude Name="id_uo" Table="dba.UctyP" Type="Column" />
      <Exclude Name="" Table="dba.UctyP" Type="Constraint" />
      <Exclude Name="" Table="dba.UctyS" Type="Constraint" />
      <Exclude Name="IndUctySPK" Table="dba.UctyS" Type="Constraint" />
      <Exclude Name="id_organizace_dofakturace" Table="dba.UctySDetail" Type="Column" />
      <Exclude Name="" Table="dba.UctySDetail" Type="Constraint" />
      <Exclude Name="" Table="dba.UctySDetail" Type="Constraint" />
      <Exclude Name="" Table="dba.UctySDetail" Type="Constraint" />
      <Exclude Name="" Table="dba.UctySDetail" Type="Constraint" />
      <Exclude Name="" Table="dba.UctySDetail" Type="Constraint" />
      <Exclude Name="" Table="dba.UctySDetail" Type="Constraint" />
      <Exclude Name="" Table="dba.UctySDetail" Type="Constraint" />
      <Exclude Name="" Table="dba.UctySDetail" Type="Constraint" />
      <Exclude Name="" Table="dba.UctySDoklady" Type="Constraint" />
      <Exclude Name="srazet" Table="dba.UctyT" Type="Column" />
      <Exclude Name="poznamka" Table="dba.UctyT" Type="Column" />
      <Exclude Name="IndUctyT" Table="dba.UctyT" Type="Constraint" />
      <Exclude Name="IndUSYSPocitadla" Table="dba.USYSPocitadla" Type="Constraint" />
      <Exclude Name="" Table="dba.USysPocitadlaLokalni" Type="Constraint" />
      <Exclude Name="IndUSysPocitadlaLokalni" Table="dba.USysPocitadlaLokalni" Type="Constraint" />
      <Exclude Name="prispevek" Table="dba.Uzaverka" Type="Column" />
      <Exclude Name="id_organizace" Table="dba.Uzaverka" Type="Column" />
      <Exclude Name="uctarna" Table="dba.Uzaverka" Type="Column" />
      <Exclude Name="id_stredisko" Table="dba.Uzaverka" Type="Column" />
      <Exclude Name="stredisko" Table="dba.Uzaverka" Type="Column" />
      <Exclude Name="kod" Table="dba.Uzaverka" Type="Column" />
      <Exclude Name="ocs" Table="dba.Uzaverka" Type="Column" />
      <Exclude Name="kod_strediska" Table="dba.Uzaverka" Type="Column" />
      <Exclude Name="add_info" Table="dba.Uzaverka" Type="Column" />
      <Exclude Name="PK_Uzaverka" Table="dba.Uzaverka" Type="Constraint" />
      <Exclude Name="" Table="dba.Uzaverka" Type="Constraint" />
      <Exclude Name="" Table="dba.Uzaverka" Type="Constraint" />
      <Exclude Name="" Table="dba.Uzaverka" Type="Constraint" />
      <Exclude Name="" Table="dba.Uzaverka" Type="Constraint" />
      <Exclude Name="" Table="dba.Uzaverka" Type="Constraint" />
      <Exclude Name="PK_W_NUTR_HODNOTY" Table="dba.W_NUTR_HODNOTY" Type="Constraint" />
      <Exclude Name="IndZarazeniOIDO" Table="dba.ZarazeniO" Type="Constraint" />
      <Exclude Name="FK_ZarazeniO_Adresy_SCC_TypAdresy_Lng" Table="dba.ZarazeniO_Adresy" Type="Constraint" />
      <Exclude Name="FK_ZarazeniO_Kontakty_SCC_TypKontaktuOrg" Table="dba.ZarazeniO_Kontakty" Type="Constraint" />
      <Exclude Name="id_vydejna" Table="dba.ZarazeniS" Type="Column" />
      <Exclude Name="id_sablona" Table="dba.ZarazeniS" Type="Column" />
      <Exclude Name="" Table="dba.ZarazeniS" Type="Constraint" />
      <Exclude Name="IndZarazeniSIDS" Table="dba.ZarazeniS" Type="Constraint" />
      <Exclude Name="IDX_ZarazeniU_kod_UNQ" Table="dba.ZarazeniU" Type="Constraint" />
      <Exclude Name="IndZarazeniUIDU" Table="dba.ZarazeniU" Type="Constraint" />
      <Exclude Name="Jednotka" Table="dba.Zbozi" Type="Column" />
      <Exclude Name="cena_jednotky" Table="dba.Zbozi" Type="Column" />
      <Exclude Name="id_dph" Table="dba.Zbozi" Type="Column" />
      <Exclude Name="volitelna_DPH" Table="dba.Zbozi" Type="Column" />
      <Exclude Name="KodReceptury" Table="dba.Zbozi" Type="Column" />
      <Exclude Name="" Table="dba.Zbozi" Type="Constraint" />
      <Exclude Name="" Table="dba.Zbozi" Type="Constraint" />
      <Exclude Name="IX_Zbozi_BarCode" Table="dba.Zbozi_BarCode" Type="Constraint" />
      <Exclude Name="KEY1" Table="dba.Zbozi_BarCode" Type="Constraint" />
      <Exclude Name="KodStr" Table="dba.ZRD" Type="Column" />
      <Exclude Name="IndZRD" Table="dba.ZRD" Type="Constraint" />
    </UpdateFromDatabaseExcludes>
  </ModelSettings>
</EntityDeveloper>