using Anete.Log.Core.Log4NetProxy;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;

namespace Anete.Utils.Net.TcpIp
{
    /// <summary>
    /// PacketWithSubpackets s podporou parsovani.
    /// Podminkou pro parsovani je konstantni velikost vsech subpacketu a podpora rozhrani IParsedPacket u vsech subpacketu.
    /// Vyjimku tvori posledni subpacket, do ktereho se automaticky predaji veskera zbyla data. Cili nemusi mit nastaveni konstantni velikost dat.    
    /// </summary>
    public abstract class PacketWithSubpacketsParsed : PacketWithSubpackets, IParsedPacket
    {
        private static readonly ILogEx _log = LogManagerEx.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        public PacketWithSubpacketsParsed(bool dataPrepared) : base(dataPrepared)
        {            
           
        }

        public PacketWithSubpacketsParsed(int size, bool dataPrepared) : base(size, dataPrepared)
        {

        }

        #region IParsedPacket Members

        /// <summary>
        /// Nastavi jednotlive property Packetu podle prijatych dat tak, aby volani GetData vratilo stejne data jako v argumentu metody Parse.
        /// </summary>
        /// <param name="data">Prijata data.</param>
        public void Parse(byte[] data)
        {
            // povolim, aby mel jeden paket nedefinvanou delku dat, ale musi byt prave jeden
            IPacket[] variablePackets = Packets.Where(p => !p.IsFixedSize()).ToArray();
            IPacket onlyOneVariablePacket = variablePackets.Length == 1 ? variablePackets[0] : null;

            int start = 0;
            for (int i = 0; i < Packets.Length; i++)
            {
                IParsedPacket packet = Packets[i] as IParsedPacket;
                if (packet == null)
                {
                    throw new NotImplementedException(TCPIPSR.PacketNelzeParsovatProtozeNeimplementujeRozhraniIpacrsedPacketFormat(Packets[i]));
                }
                
                // Packet musi mit konstantni velikost dat, aby bylo mozne predat metode parse spravnou cast dat.
                // Pokud ji nema, musi se jednat o jediny packet s volitelnou delkou, protoze ten jsem schopen dopocitat, budou v nem veskera zbyla data
                if (!packet.IsFixedSize() && packet != onlyOneVariablePacket)
                {
                    throw new ArgumentException(TCPIPSR.PacketNelzeParsovatProtozeNemaPevneStanovenuVelikostDatFormat(packet));
                }
                

                byte[] packetData;
                if (packet.IsFixedSize())
                {
                    // paket ma konstantni velikost dat
                    packetData = new byte[packet.MinimumSize];
                }
                else
                {
                    // paket nema konstantni velikost dat, ale vim, ze je jediny, tzn. jeho velikost urcim jako celkovou velikost dat minus velikost vsech ostatnich paketu
					var fixedSizeHeader = Packets.Where(p => p.IsFixedSize()).Select(p => p.MinimumSize).Sum();
					int variablePacketSize = data.Length - fixedSizeHeader;
					packetData = new byte[variablePacketSize];
                }
                Array.ConstrainedCopy(data, start, packetData, 0, packetData.Length);              
                packet.Parse(packetData);

                start += packetData.Length;
            }

            DataPrepared = true;            
        }

        #endregion
    }
}
