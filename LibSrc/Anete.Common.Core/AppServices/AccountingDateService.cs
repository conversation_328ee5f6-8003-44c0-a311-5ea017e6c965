using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Anete.Common.Core.Interface.AppServices;
using Anete.Config.Configs.Core.Global.Rules;
using Anete.Config.Core;
using Anete.Utils.Extensions;

namespace Anete.Common.Core.AppServices
{
	public class AccountingDateService : IAccountingDateService
	{

		#region private fields...
		private readonly IConfigManager _configManager;
		private decimal? _midnightOffset;
		#endregion

		#region constructors...
		public AccountingDateService(IConfigManager configManager)
		{
			_configManager = configManager;
		}
		#endregion

		#region IAccountingDateService members...
		/// <summary>
		/// Vraci ucetni datum pro Now
		/// </summary>
		public DateTime AccountingDate
		{
			get
			{
				return CalcAccountingDate(DateTime.Now);
			}
		}

		/// <summary>
		/// Vraci true, pokud je prave posunuto datum
		/// </summary>
		public bool IsDateShifted
		{
			get
			{
				return IsDateShiftedInt(DateTime.Now);
			}
		}

		/// <summary>
		/// Vraci true, pokud je aktivni posun ucetni pulnoci
		/// </summary>
		/// <param name="date">Datum, pro ktery zjistujeme</param>
		public bool GetIsDateShifted(DateTime date)
		{
			return IsDateShiftedInt(date);
		}

		/// <summary>
		/// Vypocita ucetni datum pro datum date. Bere v uvahu posun ucetni pulnoci
		/// </summary>
		/// <param name="saleDate"></param>
		/// <returns></returns>
		public DateTime CalcAccountingDate(DateTime saleDate)
		{
			DateTime result = saleDate;

			if (IsDateShiftedInt(saleDate))
			{
				result = saleDate.AddDays(-1).EndOfDay();
			}

			return result;
		}
		#endregion

		#region private methods...
		/// <summary>
		/// Vraci true, pokud je aktivni posun ucetni pulnoci
		/// </summary>
		private bool IsDateShiftedInt(DateTime date)
		{
			return date.Date != CalcDateWithOffset(date).Date;
		}

		/// <summary>
		/// Vraci ucetni datum s prihlednutim k nastaveni parametru nastaveni ucetni pulnoci
		/// </summary>
		/// <param name="dateTime">Casovy okamzik, od ktereho odecitam</param>
		private DateTime CalcDateWithOffset(DateTime dateTime)
		{
			if (_midnightOffset == null)
			{
				_midnightOffset = _configManager.GetConfig<GlobalRulesAccountingMidnightConfig>().MidnightOffset;
			}

			DateTime result = dateTime.AddHours((double)-_midnightOffset.Value).Date;
			return result;
		}
		#endregion
	}
}
