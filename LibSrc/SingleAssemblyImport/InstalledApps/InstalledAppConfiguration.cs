using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Security.Permissions;
using Anete.Common.Core.Interface.Enums;
using Microsoft.Win32;
using Anete.Utils;
using System.Security.Cryptography.X509Certificates;
using Anete.Utils.Extensions;
using Anete.Common.Core.Interface.AppServices.InstalledApplication;

namespace Anete.Common.Core.AppUtils
{
	//<PERSON><PERSON>, odkaz do Libsrc prilis slozity
	// Aktualne vychazi z AneteRegistryInstalledApplicationManager

	/// <summary>
	/// Informace o nainstalovane applikaci
	/// Nemuze byt soucasti Anete.Deployment, protoze je potreba v Anete.AutoUpgrades.
	/// Zaroven nemuze byt soucasti Anete.AutoUpgrades, protoze nechci v Anete.Deployment Anete.AutoUpgrades referencovat.
	/// Anete.Deployment nijak s automatickymi aktualizacemi nesouvisi, tyka se predevsim servisnich aplikaci a instalace jako takove.
	/// 
	/// Nove upraveno tak, aby podporovala i cteni z 32-bitove/64-bitove casti registru.
	/// </summary>
	public class InstalledAppConfiguration : AneteRegistryInstalledApplicationManagerBase
	{		
		private static string FindRegistryKeyName(UpdateTypeId updateTypeId)
		{
			ApplicationType applicationType = updateTypeId.ToApplicationType();
			if (applicationType == ApplicationType.Unassigned)
			{
				// jedna se o skript, v takovem pripade vytvarim nazev klice ciste podle ID
				int id = (int)updateTypeId;
				return $"UserUpdate{id}";
			}
			else
			{
				// jedna se o klasickou aplikaci, pouziju puvodni prevodni mustek

				if (RegistryApplicationNames.ApplicationNames.ContainsKey(applicationType))
				{
					return RegistryApplicationNames.ApplicationNames[applicationType];
				}

				return null;
			}
		}

		private static string GetRegistryKeyName(UpdateTypeId updateTypeId)
		{
			string key = FindRegistryKeyName(updateTypeId);
			if (key == null)
			{
				throw new ArgumentOutOfRangeException($"Pro typ aktualizace {updateTypeId} nelze stanovit klic v registru pro ulozeni nainstalovane verze");
			}

			return key;
		}

		#region constructors...
		/// <summary>
		/// Vytvori neinicializovanou instanci. Pri ulozeni konfigurace se pouziji pouze nastavene hodnoty.
		/// Lze pouzit v pripade, kdy jsou nektere hodnoty zapsany primo instalatorem.
		/// </summary>
		public InstalledAppConfiguration(ApplicationType funkceZarizeni, SoftwarePlatform softwarePlatform, UpdateTypeId updateTypeId)
		{
			UpdateTypeId = updateTypeId;
			SoftwarePlatform = softwarePlatform;
			ApplicationType = funkceZarizeni;
			Version = null;
			InstallDir = null;
			_canRun = null;
			_runCommands = null;
			_exeFileNames = null;
		}

		public InstalledAppConfiguration(UpdateTypeId updateTypeId, Version version, string installDir, bool canRun, string[] runCommands, string[] exeFileNames)
		{
			ApplicationType = updateTypeId.ToApplicationType();
			UpdateTypeId = updateTypeId;
			Version = version;
			InstallDir = installDir;
			_canRun = canRun;
			_runCommands = runCommands;
			_exeFileNames = exeFileNames;
		}

		/// <summary>
		/// Vytvori instanci primo z klice v registru, ktery musi obsahovat potrebne polozky.
		/// </summary>
		protected InstalledAppConfiguration(RegistryKey key, RegistryKey currentUserKey, UpdateTypeId updateTypeId)
		{
			SoftwarePlatform = updateTypeId.ToSoftwarePlatform();
			UpdateTypeId = updateTypeId;

			ApplicationType = (ApplicationType)(int)key.GetValue(RegistryKeyNames.FunkceZarizeniKeyName);

			Version = new Version((string)key.GetValue(RegistryKeyNames.VerzeKeyName));
			InstallDir = (string)key.GetValue(RegistryKeyNames.InstallDirKeyName);

			// CanRun nemusi byt nutne v registrech. Metoda GetValue vraci null, pokud neexistuje.
			int? canRunStr = (int?)key.GetValue(RegistryKeyNames.CanRunKeyName);
			if (canRunStr != null)
			{
				_canRun = Convert.ToBoolean(canRunStr);
			}
			else
			{
				_canRun = null;
			}

			string runCommandsStr = (string)key.GetValue(RegistryKeyNames.RunCommandsKeyName);
			if (runCommandsStr != null)
			{
				// jako oddelovat pouzivam |, protoze je to zakazany znak pri pojmenovani souboru
				_runCommands = runCommandsStr.Trim().Split(new char[] { '|' }, StringSplitOptions.RemoveEmptyEntries);
			}
			else
			{
				_runCommands = null;
			}

			string exeFileNamesStr = (string)key.GetValue(RegistryKeyNames.ExeFileNamesKeyName);
			if (exeFileNamesStr != null)
			{
				_exeFileNames = exeFileNamesStr.Trim().Split(new char[] { '|' }, StringSplitOptions.RemoveEmptyEntries);
			}
			else
			{
				_exeFileNames = null;
			}

			// muze byt null, argumenty prikazoveho radku nejsou vyzadovany
			if (currentUserKey != null)
			{
				// argumenty prikazoveho radku nemusi existovat, ale v tom pripade klidne priradim null
				CmdLineArgs = (string)currentUserKey.GetValue(RegistryKeyNames.CmdLineKeyName);
			}
		}
		#endregion

		#region private static methods...
		private static RegistryKey GetLocalMachineSoftwareAneteKey(bool writeable, SoftwarePlatform platform, InstalledAppConfigurationType installedAppConfigurationType)
		{
			// mam 32-bitovou aplikaci, musim rozlisit, jaky klic registru otevru
			RegistryKey localMachineKey;
			switch (platform)
			{
				case SoftwarePlatform.x86:
				// pro hodnotu Othetr pouziju klasicky 32-bitove registry
				// ukladaji se timto zpusobem skripty
				case SoftwarePlatform.Other:
					localMachineKey = RegistryKey.OpenBaseKey(RegistryHive.LocalMachine, RegistryView.Registry32);
					break;
				case SoftwarePlatform.x64:
					localMachineKey = RegistryKey.OpenBaseKey(RegistryHive.LocalMachine, RegistryView.Registry64);
					break;

				default:
					throw new ArgumentOutOfRangeException($"SoftwarePlatform: {platform}");
			}

			if (writeable)
			{
				return localMachineKey.OpenSubKey(GetAneteRegistryKeyName(installedAppConfigurationType), RegistryKeyPermissionCheck.ReadWriteSubTree);
			}
			else
			{
				// Musi se volat timto zpusobe, jinak nefunguje v .net 4.0
				// http://stackoverflow.com/questions/31741599/unable-to-read-registry-system-security-securityexception-requested-registry-a
				return localMachineKey.OpenSubKey(GetAneteRegistryKeyName(installedAppConfigurationType), RegistryKeyPermissionCheck.ReadSubTree, System.Security.AccessControl.RegistryRights.ReadKey);
			}
		}

		private static RegistryKey GetCurrentUserSoftwareAneteKey(bool writeable, SoftwarePlatform platform, InstalledAppConfigurationType installedAppConfigurationType)
		{
			// mam 32-bitovou aplikaci, musim rozlisit, jaky klic registru otevru
			RegistryKey currentUserKey;
			switch (platform)
			{
				// pro hodnotu Othetr pouziju klasicky 32-bitove registry
				// ukladaji se timto zpusobem skripty
				case SoftwarePlatform.Other:
				case SoftwarePlatform.x86:
					currentUserKey = RegistryKey.OpenBaseKey(RegistryHive.CurrentUser, RegistryView.Registry32);
					break;
				case SoftwarePlatform.x64:
					currentUserKey = RegistryKey.OpenBaseKey(RegistryHive.CurrentUser, RegistryView.Registry64);
					break;

				default:
					//throw ExcUtils.ArgumentOutOfRange("platform", platform);
					throw new ArgumentOutOfRangeException($"SoftwarePlatform: {platform}");

			}

			if (writeable)
			{
				return currentUserKey.OpenSubKey(GetAneteRegistryKeyName(installedAppConfigurationType), RegistryKeyPermissionCheck.ReadWriteSubTree);
			}
			else
			{
				return currentUserKey.OpenSubKey(GetAneteRegistryKeyName(installedAppConfigurationType), RegistryKeyPermissionCheck.ReadSubTree, System.Security.AccessControl.RegistryRights.ReadKey);
			}

		}

		/// <summary>
		/// Vraci klic v registru, v kterem je ulozena konfigurace pro danou funkci zarizeni.
		/// </summary>
		/// <param name="funkceZarizeni"></param>
		/// <returns></returns>
		private static RegistryKey GetAdminRegistryKey(UpdateTypeId updateTypeId, InstalledAppConfigurationType installedAppConfigurationType)
		{
			SoftwarePlatform platform = updateTypeId.ToSoftwarePlatform();

			RegistryKey key = GetLocalMachineSoftwareAneteKey(false, platform, installedAppConfigurationType);
			if (key == null)
			{
				// teoreticky muze vratit null, pokud neni klic vubec zalozen
				return null;
			}

			key = key.OpenSubKey(GetRegistryKeyName(updateTypeId));
			return key;
		}

		private static RegistryKey GetCurrentUserRegistryKey(UpdateTypeId updateTypeId, InstalledAppConfigurationType installedAppConfigurationType)
		{
			SoftwarePlatform platform = updateTypeId.ToSoftwarePlatform();

			RegistryKey key = GetCurrentUserSoftwareAneteKey(false, platform, installedAppConfigurationType);
			if (key == null)
			{
				// teoreticky muze vratit null, pokud neni klic vubec zalozen
				return null;
			}

			key = key.OpenSubKey(GetRegistryKeyName(updateTypeId));
			return key;
		}


		private static RegistryKey CreateAneteKey(RegistryKey registryKey, UpdateTypeId updateTypeId, InstalledAppConfigurationType installedAppConfigurationType)
		{
			string registryPath = GetAneteRegistryKeyName(installedAppConfigurationType);
			return registryKey.CreateSubKey(registryPath + GetRegistryKeyName(updateTypeId), RegistryKeyPermissionCheck.ReadWriteSubTree);
		}

		private static string GetAneteRegistryKeyName(InstalledAppConfigurationType installedAppConfigurationType)
		{
			switch (installedAppConfigurationType)
			{
				case InstalledAppConfigurationType.Release:
					return "SOFTWARE\\ANETE\\";

				case InstalledAppConfigurationType.Backup:
					return "SOFTWARE\\ANETEBCK\\";

				default:
					//throw ExcUtils.ArgumentOutOfRange(nameof(installedAppConfigurationType), installedAppConfigurationType);
					throw new ArgumentOutOfRangeException($"InstalledAppConfigurationType: {installedAppConfigurationType}");
			}

		}

		private static RegistryKey CreateRegistryKey(UpdateTypeId updateTypeId, InstalledAppConfigurationType installedAppConfigurationType)
		{
			return CreateAneteKey(Registry.LocalMachine, updateTypeId, installedAppConfigurationType);
		}

		private static RegistryKey CreateCurrentUserRegistryKey(UpdateTypeId updateTypeId, InstalledAppConfigurationType installedAppConfigurationType)
		{
			return CreateAneteKey(Registry.CurrentUser, updateTypeId, installedAppConfigurationType);
		}


		/// <summary>
		/// Vraci true, pokud klic obsahuje vsechny pozadovane polozky.
		/// </summary>
		/// <param name="key"></param>
		/// <returns></returns>
		private static bool IsValidApplicationKey(RegistryKey key)
		{
			// neni zde CmdLine, protoze tento klic neni vyzadovan
			// a stejne tak slozka - ta ma smysl jen u aplikaci, ale nema smysl u skriptu
			return key.GetValue(RegistryKeyNames.FunkceZarizeniKeyName) != null && key.GetValue(RegistryKeyNames.VerzeKeyName) != null;
		}
		#endregion

		#region public static methods...

		/// <summary>
		/// Vraci true, pokud je na pocitaci nainstalovana aplikace odpovidaji dane funkci zarizeni
		/// </summary>        
		public static bool ApplicationExists(UpdateTypeId updateTypeId)
		{
			return LoadConfiguration(updateTypeId) != null;
		}

		/// <summary>
		/// Nahraje konfiguraci pro danou funkci zarizeni. Pokud konfigurace neexistuje, vraci null.
		/// </summary>     
		public static InstalledAppConfiguration LoadConfiguration(UpdateTypeId updateTypeId, InstalledAppConfigurationType installedAppConfigurationType = InstalledAppConfigurationType.Release)
		{
			//_log.DebugFormat("LoadConfiguration ({0})", updateTypeId);

			string subKey = FindRegistryKeyName(updateTypeId);

			if (subKey == null)
			{
				return null;
			}

			RegistryKey key = GetAdminRegistryKey(updateTypeId, installedAppConfigurationType);
			if (key != null && InstalledAppConfiguration.IsValidApplicationKey(key))
			{
				RegistryKey currentUserKey = GetCurrentUserRegistryKey(updateTypeId, installedAppConfigurationType);
				return new InstalledAppConfiguration(key, currentUserKey, updateTypeId);
			}
			return null;
		}

		/// <summary>
		/// Vraci seznam vsech nainstalovanych aplikaci.
		/// Seznam je ziskan z registru.
		/// </summary>
		/// <returns></returns>
		public static IEnumerable<InstalledAppConfiguration> GetInstalledApplications(bool withNamedInstance = false)
		{
			UpdateTypeIdEnumerator enumerator = new UpdateTypeIdEnumerator();
			UpdateTypeId[] updateTypeIds = EnumUtils.GetValues<UpdateTypeId>().Where(x => SupportedUpdates.Any(u => u.UpdateTypeId == x)).ToArray();
			foreach (UpdateTypeId updateTypeId in updateTypeIds)
			{
				InstalledAppConfiguration installedAppConfiguration = InstalledAppConfiguration.LoadConfiguration(updateTypeId);
				// konfigurace muze byt null, pokud pro dana aplikace neni na PC nainstalovana
				if (installedAppConfiguration != null)
				{
					yield return installedAppConfiguration;
				}
			}
			//

#if DEBUG
			//yield return new InstalledAppConfiguration(ApplicationType.AneteAdmin, SoftwarePlatform.x86, UpdateTypeId.AneteAdminx86);
			//yield return new InstalledAppConfiguration(ApplicationType.Office8, SoftwarePlatform.x86, UpdateTypeId.Officex86);
#endif
			if (withNamedInstance)
			{
				throw new NotImplementedException();
			}

		}

		public static SupportedUpdate[] SupportedUpdates
		{
			get
			{
				return new SupportedUpdate[] {
						//new SupportedUpdate(UpdateTypeId.Officex86,"Shell8.exe.config"),
						//new SupportedUpdate(UpdateTypeId.Officex64,"Shell8.exe.config"),
						//new SupportedUpdate(UpdateTypeId.PresPointx86,"PM8.exe.config"),
						//new SupportedUpdate(UpdateTypeId.CashDeskx86,"Kasa8.exe.config")
						new SupportedUpdate(UpdateTypeId.AppServerx86,"Anete.AppServer.WindowsService.exe.config"),
						new SupportedUpdate(UpdateTypeId.AppServerx64,"Anete.AppServer.WindowsService.exe.config"),						
						//new SupportedUpdate(UpdateTypeId.Officex86,"Shell8.exe.config","Shell8.exe.config"),
						//new SupportedUpdate(UpdateTypeId.Officex64,"Shell8.exe.config","Shell8.exe.config"),
						//new SupportedUpdate(UpdateTypeId.PresPointx86,"PM8.exe.config","PM8.exe.config"),
						//new SupportedUpdate(UpdateTypeId.CashDeskx86,"Kasa8.exe.config","Kasa8.exe.config")
				};
			}
		}		
		#endregion

		#region public methods...
		/// <summary>
		/// Ulozi konfiguration.
		/// </summary>
		/// <param name="onlyCurrentUser">Pouze nastaveni vztazena pro aktualniho uzivatele.</param>
		/// <exception cref="ArgumentException">Pokud je pozadovani ulozit globalni nastaveni a pritom je onlyCurrentUser == true</exception>
		[RegistryPermissionAttribute(SecurityAction.Demand, Unrestricted = true)]
		public void SaveConfiguration(bool onlyCurrentUser, InstalledAppConfigurationType installedAppConfigurationType = InstalledAppConfigurationType.Release)
		{
			if (onlyCurrentUser == false)
			{
				RegistryKey key = CreateRegistryKey(UpdateTypeId, installedAppConfigurationType);

				// appinstallation type muzu ulozit vzdy, pokud ukladam kompletni nastaveni
				key.SetValue(RegistryKeyNames.FunkceZarizeniKeyName, (int)ApplicationType);

				// ukladaji se pouze inicializovane hodnoty
				if (Version != null)
				{
					key.SetValue(RegistryKeyNames.VerzeKeyName, Version.ToString());
				}

				if (InstallDir != null)
				{
					key.SetValue(RegistryKeyNames.InstallDirKeyName, InstallDir);
				}

				if (_canRun != null)
				{
					key.SetValue(RegistryKeyNames.CanRunKeyName, Convert.ToInt32((bool)_canRun));
				}

				if (_runCommands != null)
				{
					key.SetValue(RegistryKeyNames.RunCommandsKeyName, RunCommandsDelimitedText);
				}

				if (_exeFileNames != null)
				{
					key.SetValue(RegistryKeyNames.ExeFileNamesKeyName, ExeFileNamesDelimitedText);
				}

				if (UpdateBatchLogId != null)
				{
					key.SetValue(RegistryKeyNames.UpdateBatchLogIdKeyName, UpdateBatchLogId.Value);
				}
				else
				{
					// musim zajistit, aby v pripade, ze neexistuje 
					key.DeleteValue(RegistryKeyNames.UpdateBatchLogIdKeyName, false);
				}

				if (LogFileName != null)
				{
					key.SetValue(RegistryKeyNames.LogFileNameKey, LogFileName);
				}
				else
				{
					key.DeleteValue(RegistryKeyNames.LogFileNameKey, false);
				}
			}

			// parametry prikazove radku zapisuju do CurrentUser, protoze tento parametr je nutne zapsat po kazdem spusteni aplikace
			if (CmdLineArgs != null)
			{
				RegistryKey currentUserKey = CreateCurrentUserRegistryKey(UpdateTypeId, installedAppConfigurationType);
				currentUserKey.SetValue(RegistryKeyNames.CmdLineKeyName, CmdLineArgs);
			}
		}

		/// <summary>
		/// Odstrani konfiguraci
		/// </summary>
		[RegistryPermissionAttribute(SecurityAction.Demand, Unrestricted = true)]
		public void RemoveConfiguration(InstalledAppConfigurationType installedAppConfigurationType)
		{
			string subkey = GetRegistryKeyName(UpdateTypeId);
			GetLocalMachineSoftwareAneteKey(true, SoftwarePlatform, installedAppConfigurationType).DeleteSubKeyTree(subkey);

			// 27.3.2009 toto nebude fungovat, protoze se odinstalace spousti pod Admin, ktery ma uplne jine klice. Nezbyva, nez konfiguraci ponechat
			RegistryKey userKey = GetCurrentUserSoftwareAneteKey(true, SoftwarePlatform, installedAppConfigurationType);
			// mazeme pouze pokud klic existuje
			if (userKey != null && userKey.OpenSubKey(subkey) != null)
			{
				userKey.DeleteSubKeyTree(subkey);
			}
		}

		/// <summary>
		/// Odstrani konfiguraci
		/// </summary>
		[RegistryPermissionAttribute(SecurityAction.Demand, Unrestricted = true)]
		public static void RemoveConfiguration(UpdateTypeId updateTypeId, InstalledAppConfigurationType installedAppConfigurationType)
		{
			string subkey = GetRegistryKeyName(updateTypeId);
			SoftwarePlatform softwarePlatform = updateTypeId.ToSoftwarePlatform();
			GetLocalMachineSoftwareAneteKey(true, softwarePlatform, installedAppConfigurationType).DeleteSubKeyTree(subkey);

			// 27.3.2009 toto nebude fungovat, protoze se odinstalace spousti pod Admin, ktery ma uplne jine klice. Nezbyva, nez konfiguraci ponechat
			RegistryKey userKey = GetCurrentUserSoftwareAneteKey(true, softwarePlatform, installedAppConfigurationType);
			// mazeme pouze pokud klic existuje
			if (userKey != null && userKey.OpenSubKey(subkey) != null)
			{
				userKey.DeleteSubKeyTree(subkey);
			}
		}
		#endregion

		#region public properties...

		/// <summary>
		/// Funkce zarizeni. Jedna se o hodnotu, ktere dane slouzi jako ID pro danou konfiguraci. Proto ji lze jen cist.
		/// </summary>
		public ApplicationType ApplicationType { get; }

		/// <summary>
		/// Verze
		/// </summary>
		public Version Version { get; set; }

		/// <summary>
		/// Slozka, kde je aplikace nainstalovana
		/// </summary>
		public string InstallDir { get; set; }

		public string LogFileName { get; set; }

		public int? UpdateBatchLogId { get; set; }

		private string[] _runCommands;
		/// <summary>
		/// Seznam prikazu pro spusteni aplikace v ramci automatickeho spousteni pomoci automatickych aktualizaci.
		/// Pokud je aplikace nema definovany, neni aplikace spoustena.
		/// </summary>
		public IEnumerable<string> RunCommands
		{
			get
			{
				if (_runCommands == null)
				{
					return new string[] { };
				}
				return _runCommands;
			}
			set
			{
				_runCommands = value.ToArray();
			}
		}

		/// <summary>
		/// Seznam prikazu ke spousteni. V tomto tvaru se uklada do registru
		/// </summary>
		public string RunCommandsDelimitedText
		{
			get
			{
				if (_runCommands == null)
				{
					return "";
				}
				return string.Join("|", _runCommands);
			}
			set
			{
				_runCommands = value.Trim().Split(new char[] { '|' }, StringSplitOptions.RemoveEmptyEntries);
			}
		}

		private string[] _exeFileNames;
		/// <summary>
		/// Seznam spustitelnych souboru. Vyuziva se pri sestrelovani aplikace.
		/// </summary>
		public IEnumerable<string> ExeFileNames
		{
			get
			{
				if (_exeFileNames == null)
				{
					return new string[] { };
				}
				return _exeFileNames;
			}
			set
			{
				_exeFileNames = value.ToArray();
			}
		}

		/// <summary>
		/// Seznam spustitelnych souboru. V tomto tvaru se uklada do registru
		/// </summary>
		public string ExeFileNamesDelimitedText
		{
			get
			{
				if (_exeFileNames == null)
				{
					return "";
				}
				return string.Join("|", _exeFileNames);
			}
			set
			{
				_exeFileNames = value.Trim().Split(new char[] { '|' }, StringSplitOptions.RemoveEmptyEntries);
			}
		}

		/// <summary>
		/// Argumenty prikazove radky, s jakymi byla aplikace naposledy spustila.
		/// Vyuziva se pri automatickych aktualizacich, kdy je potreba ukoncit a nasledne spustit danou aplikaci.
		/// </summary>
		public string CmdLineArgs { get; set; }

		private bool? _canRun = null;
		/// <summary>
		/// Lze aplikaci spustit? Vyuziva AutoUpdater, pokud je potreba zabranit spousteni dane aplikace.
		/// Je-li null, nebyla prirazena.
		/// </summary>
		public bool? CanRun
		{
			get
			{
				return _canRun;
			}
			set
			{
				_canRun = value;
			}
		}

		public SoftwarePlatform SoftwarePlatform { get; }

		public UpdateTypeId UpdateTypeId { get; }
		#endregion

		#region public overrides...
		/// <summary>
		/// Returns a <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.
		/// </summary>
		/// <returns>
		/// A <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.
		/// </returns>
		public override string ToString()
		{
			return string.Format("{0} ({1}) ver {2} '{3}' cmd: '{4}'", ApplicationType, SoftwarePlatform, Version, InstallDir, CmdLineArgs);
		}
		#endregion

		protected override InstalledApplicationBase GetInstalledApplicationByUpdateTypeInt(UpdateTypeId updateTypeId)
		{
			return GetInstalledApplicationByUpdateTypeInt(updateTypeId);
		}
	}
}
