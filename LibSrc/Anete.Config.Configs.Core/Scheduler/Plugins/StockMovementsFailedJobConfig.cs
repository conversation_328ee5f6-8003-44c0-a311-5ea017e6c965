using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Anete.Config.Configs.Core.Shared.Scheduler;
using System.ComponentModel;

namespace Anete.Config.Configs.Core.Scheduler.Plugins
{
	/// <summary>
	/// Predefinovani s jinymi implicitnimi hodnotami
	/// </summary>
	public class StockMovementsFailedJobConfig : FailedJobConfig
	{

		/// <summary>
		/// Initializes a new instance
		/// </summary>
		public StockMovementsFailedJobConfig()
		{
			RetryOnFailEnabled = true;
			MaxRetryCount = 5;
			RetryAfter = TimeSpan.FromMinutes(30);
		}

		[DefaultValue(true)]
		public override bool RetryOnFailEnabled
		{
			get
			{
				return base.RetryOnFailEnabled;
			}
			set
			{
				base.RetryOnFailEnabled = value;
			}
		}

		[DefaultValue(5)]
		public override int MaxRetryCount
		{
			get
			{
				return base.MaxRetryCount;
			}
			set
			{
				base.MaxRetryCount = value;
			}
		}

		[DefaultValue(typeof(TimeSpan), "00:30:00")]
		public override TimeSpan RetryAfter
		{
			get
			{
				return base.RetryAfter;
			}
			set
			{
				base.RetryAfter = value;
			}
		}
	}
}
