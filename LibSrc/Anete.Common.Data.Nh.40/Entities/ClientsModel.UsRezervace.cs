//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Anete.Common.Data.Nh.Entities
{

    /// <summary>
    /// Tabulka: us.Rezervace
    /// </summary>
    public partial class UsRezervace : Anete.Data.Nh.NhEntityBase, INotifyPropertyChanging, INotifyPropertyChanged {

        private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(System.String.Empty);

        private int _Id;

        private System.DateTime _DatumDo;

        private System.DateTime _DatumNastupu;

        private System.DateTime _DatumOd;

        private string _Poznamka;

        private short? _PozadovanaKapacita;

        private Anete.Common.Data.Interface.Enums.ReservationStateType _Stav;

        private string _DuvodZruseni;

        private UsBudova _UsBudova;

        private UsHost _UsHost;

        private ISet<UsPokojRezervace> _UsPokojRezervaces;

        private UsUbytovany _UsUbytovany;

        private ISet<UsUbytovani> _UsUbytovanis;
    
        #region Extensibility Method Definitions
        
        partial void OnCreated();

        public override bool Equals(object obj)
        {
          UsRezervace toCompare = obj as UsRezervace;
          if (toCompare == null)
          {
            return false;
          }

          if (!Object.Equals(this.Id, toCompare.Id))
            return false;
         // pokud mam default primary key, znamena to, ze jedna o nove vytvorenou entitu, ktera jeste nema key prirazen. 
        // V takove pripade musim porovnat podle instance
        bool isDefault = true;
        if (this.Id != default(int))
        {
          isDefault = false;
        }
     

          if (isDefault)
          {
            return object.ReferenceEquals(toCompare, this);
          }     
          return true;
        }

	private int? _hashCode = null;
        public override int GetHashCode()
        {
	if (_hashCode == null)
          {
          _hashCode = 13;
          _hashCode = (_hashCode * 7) + Id.GetHashCode();
}
          return _hashCode.Value;
        }
        partial void OnIdChanging(int value);
        
        partial void OnIdChanged();
        partial void OnDatumDoChanging(System.DateTime value);
        
        partial void OnDatumDoChanged();
        partial void OnDatumNastupuChanging(System.DateTime value);
        
        partial void OnDatumNastupuChanged();
        partial void OnDatumOdChanging(System.DateTime value);
        
        partial void OnDatumOdChanged();
        partial void OnPoznamkaChanging(string value);
        
        partial void OnPoznamkaChanged();
        partial void OnPozadovanaKapacitaChanging(short? value);
        
        partial void OnPozadovanaKapacitaChanged();
        partial void OnStavChanging(Anete.Common.Data.Interface.Enums.ReservationStateType value);
        
        partial void OnStavChanged();
        partial void OnDuvodZruseniChanging(string value);
        
        partial void OnDuvodZruseniChanged();
        partial void OnUsBudovaChanging(UsBudova value);

        partial void OnUsBudovaChanged();
        partial void OnUsHostChanging(UsHost value);

        partial void OnUsHostChanged();
        partial void OnUsUbytovanyChanging(UsUbytovany value);

        partial void OnUsUbytovanyChanged();
        
        #endregion
        public UsRezervace()
        {
            this._UsPokojRezervaces = new HashSet<UsPokojRezervace>();
            this._UsUbytovanis = new HashSet<UsUbytovani>();
            OnCreated();
        }

    
        /// <summary>
        /// Sloupec: id
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Id", typeof(UsRezervaceSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(UsRezervace), Tag="Id")]
        public virtual int Id
        {
            get
            {
                return this._Id;
            }
            set
            {
                if (this._Id != value)
                {
                    this.OnIdChanging(value);
                    this.SendPropertyChanging();
                    this._Id = value;
                    this.SendPropertyChanged("Id");
                    this.OnIdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: datumDo
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("DatumDo", typeof(UsRezervaceSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(UsRezervace), Tag="DatumDo")]
        public virtual System.DateTime DatumDo
        {
            get
            {
                return this._DatumDo;
            }
            set
            {
                if (this._DatumDo != value)
                {
                    this.OnDatumDoChanging(value);
                    this.SendPropertyChanging();
                    this._DatumDo = value;
                    this.SendPropertyChanged("DatumDo");
                    this.OnDatumDoChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: datumNastupu
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("DatumNastupu", typeof(UsRezervaceSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(UsRezervace), Tag="DatumNastupu")]
        public virtual System.DateTime DatumNastupu
        {
            get
            {
                return this._DatumNastupu;
            }
            set
            {
                if (this._DatumNastupu != value)
                {
                    this.OnDatumNastupuChanging(value);
                    this.SendPropertyChanging();
                    this._DatumNastupu = value;
                    this.SendPropertyChanged("DatumNastupu");
                    this.OnDatumNastupuChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: datumOd
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("DatumOd", typeof(UsRezervaceSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(UsRezervace), Tag="DatumOd")]
        public virtual System.DateTime DatumOd
        {
            get
            {
                return this._DatumOd;
            }
            set
            {
                if (this._DatumOd != value)
                {
                    this.OnDatumOdChanging(value);
                    this.SendPropertyChanging();
                    this._DatumOd = value;
                    this.SendPropertyChanged("DatumOd");
                    this.OnDatumOdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: poznamka
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Poznamka", typeof(UsRezervaceSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(UsRezervace), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 500,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="Poznamka")]
        public virtual string Poznamka
        {
            get
            {
                return this._Poznamka;
            }
            set
            {
                if (this._Poznamka != value)
                {
                    this.OnPoznamkaChanging(value);
                    this.SendPropertyChanging();
                    this._Poznamka = value;
                    this.SendPropertyChanged("Poznamka");
                    this.OnPoznamkaChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: pozadovanaKapacita
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("PozadovanaKapacita", typeof(UsRezervaceSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual short? PozadovanaKapacita
        {
            get
            {
                return this._PozadovanaKapacita;
            }
            set
            {
                if (this._PozadovanaKapacita != value)
                {
                    this.OnPozadovanaKapacitaChanging(value);
                    this.SendPropertyChanging();
                    this._PozadovanaKapacita = value;
                    this.SendPropertyChanged("PozadovanaKapacita");
                    this.OnPozadovanaKapacitaChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: stav
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Stav", typeof(UsRezervaceSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(UsRezervace), Tag="Stav")]
        public virtual Anete.Common.Data.Interface.Enums.ReservationStateType Stav
        {
            get
            {
                return this._Stav;
            }
            set
            {
                if (this._Stav != value)
                {
                    this.OnStavChanging(value);
                    this.SendPropertyChanging();
                    this._Stav = value;
                    this.SendPropertyChanged("Stav");
                    this.OnStavChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: duvodZruseni
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("DuvodZruseni", typeof(UsRezervaceSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(UsRezervace), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 500,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="DuvodZruseni")]
        public virtual string DuvodZruseni
        {
            get
            {
                return this._DuvodZruseni;
            }
            set
            {
                if (this._DuvodZruseni != value)
                {
                    this.OnDuvodZruseniChanging(value);
                    this.SendPropertyChanging();
                    this._DuvodZruseni = value;
                    this.SendPropertyChanged("DuvodZruseni");
                    this.OnDuvodZruseniChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: idBudova
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("UsBudova", typeof(UsRezervaceSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual UsBudova UsBudova
        {
            get
            {
                return this._UsBudova;
            }
            set
            {
                if (this._UsBudova != value)
                {
                    this.OnUsBudovaChanging(value);
                    this.SendPropertyChanging();
                    this._UsBudova = value;
                    this.SendPropertyChanged("UsBudova");
                    this.OnUsBudovaChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: idHost
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("UsHost", typeof(UsRezervaceSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual UsHost UsHost
        {
            get
            {
                return this._UsHost;
            }
            set
            {
                if (this._UsHost != value)
                {
                    this.OnUsHostChanging(value);
                    this.SendPropertyChanging();
                    this._UsHost = value;
                    this.SendPropertyChanged("UsHost");
                    this.OnUsHostChanged();
                }
            }
        }

        [Anete.Data.Attributes.EntitySRDisplayName("UsPokojRezervaces", typeof(UsRezervaceSR))]
        [Anete.Data.Attributes.DbCollectionProperty]
        public virtual ISet<UsPokojRezervace> UsPokojRezervaces
        {
            get
            {
                return this._UsPokojRezervaces;
            }
            set
            {
                this._UsPokojRezervaces = value;
            }
        }

    
        /// <summary>
        /// Sloupec: idUbytovany
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("UsUbytovany", typeof(UsRezervaceSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual UsUbytovany UsUbytovany
        {
            get
            {
                return this._UsUbytovany;
            }
            set
            {
                if (this._UsUbytovany != value)
                {
                    this.OnUsUbytovanyChanging(value);
                    this.SendPropertyChanging();
                    this._UsUbytovany = value;
                    this.SendPropertyChanged("UsUbytovany");
                    this.OnUsUbytovanyChanged();
                }
            }
        }

        [Anete.Data.Attributes.EntitySRDisplayName("UsUbytovanis", typeof(UsRezervaceSR))]
        [Anete.Data.Attributes.DbCollectionProperty]
        public virtual ISet<UsUbytovani> UsUbytovanis
        {
            get
            {
                return this._UsUbytovanis;
            }
            set
            {
                this._UsUbytovanis = value;
            }
        }
   
        public virtual event PropertyChangingEventHandler PropertyChanging;

	// Mam v bazove tride, nema smysl generovat
	// public virtual event PropertyChangedEventHandler PropertyChanged;

        protected virtual void SendPropertyChanging()
        {
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, emptyChangingEventArgs);
        }

        protected virtual void SendPropertyChanging(System.String propertyName) 
        {   	
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, new PropertyChangingEventArgs(propertyName));
        }

        protected virtual void SendPropertyChanged(System.String propertyName)
        {    
		// mam v bazove tride, proto musim volat takto jednoduse
		OnPropertyChanged(propertyName);
/*		        var handler = this.PropertyChanged;
            if (handler != null)
                handler(this, new PropertyChangedEventArgs(propertyName));*/
        }
    }

}
