using Anete.Common.Core.Business.InstallationParameters.Factories;
using Anete.Common.Core.Interface.Business.InstallationParameters.Factories;
using Anete.Common.Core.Interface.Business.InstallationParameters.Parameters;
using System;

namespace Anete.Common.Core.Business.InstallationParameters.Parameters.Computer.ComputerRemoteAccess
{
	public class ComputerRemoteAccessValueBasedFactory : InstallationParameterValueBasedFactoryBase, IComputerParameterFactoryFromValue
	{
		public IComputerInstallationParameter CreateFromParameterValue(IInstallationParameterValue value, Guid computerId)
		{
			return new ComputerRemoteAccessParameter((ComputerRemoteAccessParameterValue)value, computerId);
		}
	}
}