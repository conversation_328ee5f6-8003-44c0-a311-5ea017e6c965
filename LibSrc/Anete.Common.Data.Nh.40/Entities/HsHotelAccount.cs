using System;
using System.Collections.Generic;
using System.Linq;
using Anete.Common.Data.Nh.Business.Hotel;
using NHibernate;
using Anete.Utils;
using System.Collections;

namespace Anete.Common.Data.Nh.Entities
{
	public partial class HsHotelAccount
	{
		public HsHotelAccount(Guid hotelAccountId, string code, HsPerson owner)
			:this()
		{
			// VaHTODO: 17.7.2014 (VaH) kontrola, že hotelAccountId není default guid
			Guard.ArgumentNotNullOrEmptyString(code, "code");
			Guard.ArgumentNotNull(owner, "owner");

			HotelAccountId = hotelAccountId;
			Code = code;
			State = HsHotelAccountState.Reserved;
			Owner = owner;
			// VaHTODO: 27.6.2014 (VaH) použít DateTimeProvider
			CreationTime = DateTime.Now;
		}


		//public void ChangeRoom(Guid currentRoomRentalId, Guid newRoomId, IRoomChanger roomChanger) 
		//{
		//	roomChanger.ChangeRoom(this, currentRoomRentalId, newRoomId);
		//}

		/// <summary>
		/// Potvrdí rezervaci
		/// </summary>
		public virtual void Confirm() 
		{
			if (State != HsHotelAccountState.Reserved)
			{
				throw new InvalidOperationException("Nelze provést potvrzení, hotelový účet není v odpovídajícím stavu.");
			}

			State = HsHotelAccountState.Confirmed;
		}

		/// <summary>
		/// Zruší rezervaci
		/// </summary>
		/// <param name="session"></param>
		public virtual void Cancel(ISession session)
		{
			if (State != HsHotelAccountState.Reserved && State != HsHotelAccountState.Confirmed)
			{
				throw new InvalidOperationException("Nelze provést zrušení, hotelový účet není v odpovídajícím stavu.");
			}

			State = HsHotelAccountState.Canceled;

			foreach (var rental in RentedRooms)
			{
				var occupancy = rental.RoomOccupancy;
				rental.RoomOccupancy = null;
				occupancy.RoomRental = null;
				session.Delete(occupancy);
			}
		}

		/// <summary>
		/// Zahájí ubytování (check-in)
		/// </summary>
		/// <param name="session"></param>
		public virtual void CheckIn(ISession session)
		{
			if (State != HsHotelAccountState.Confirmed)
			{
				throw new InvalidOperationException("Nelze provést check-in, hotelový účet není v odpovídajícím stavu.");
			}

			State = HsHotelAccountState.Accommodation;

			var now = DateTime.Now;

			// Pokud check-in probíhá tak pozdě, že už některý z pronájmů měl skončit, odebere se obsazenost…
			foreach (var rental in RentedRooms.Where(x => x.RentedTo <= now))
			{
				// VaHNOTE: 16.7.2014 (VaH) NH nemá možnost, jak odstranit entitu, než přes explicitní volání Delete(), null nastaviji kvůli následujícímu filtrování
				var occupancy = rental.RoomOccupancy;
				rental.RoomOccupancy = null;
				occupancy.RoomRental = null;
				session.Delete(occupancy);
			}

			// …pokud už má nějaký pronájem probíhat, nastaví se mu obsazenost od teď.
			// Jinak, začíná-li ubytování dříve, se najdou nejbližší pronájmy a těm seobsazenost pokoje prodlouží do teď.
			var rentalsWithOccupancy = RentedRooms.Where(x => x.RoomOccupancy != null);
			var rentalsToSetFrom = rentalsWithOccupancy.Where(x => x.RentedFrom <= now);
			if (!rentalsToSetFrom.Any() && rentalsWithOccupancy.Any())
			{
				// VaHTODO: 31.7.2014 (VaH) Pokud jsou všechny pronájmy už po, zkončí to zde chybou, že posloupnost neobsahuje žádný prvek.
				// Jak to řešit? Poslednímu (posledním) nemazat obsazenost, ale nastavit ji na aktuální čas? 
				// Nedovolit ubytování, jsou-li pronájmy po čase?
				var min = rentalsWithOccupancy.Min(x => x.RentedFrom);
				rentalsToSetFrom = RentedRooms.Where(x => x.RentedFrom == min);
			}

			// VaHTODO: 31.7.2014 (VaH) pokud nastavuji obsazenost na dřívější čas, hlídat, že tam je volno!
			foreach (var rental in rentalsToSetFrom)
			{
				rental.RoomOccupancy.OccupiedFrom = now;
			}
		}

		/// <summary>
		/// Ukončí ubytování (check-out)
		/// </summary>
		/// <param name="session"></param>
		public virtual void CheckOut(ISession session)
		{
			if (State != HsHotelAccountState.Accommodation)
			{
				throw new InvalidOperationException("Nelze provést check-out, hotelový účet není v odpovídajícím stavu.");
			}

			State = HsHotelAccountState.Closed;

			var now = DateTime.Now;

			// Pokud probíhá check-out tak brzo, že ještě některý pronájem vůbec nezačal, odebere se jeho obsazenost…
			foreach (var rental in RentedRooms.Where(x => x.RentedFrom >= now))
			{
				// VaHNOTE: 16.7.2014 (VaH) NH nemá možnost, jak odstranit entitu, než přes explicitní volání Delete(), null nastavuji kvůli následujícímu filtrování
				var occupancy = rental.RoomOccupancy;
				rental.RoomOccupancy = null;
				occupancy.RoomRental = null;
				session.Delete(occupancy);
			}

			// … těm pronájmům, které aktuálně probíhají se nastaví obsazenost do teď.
			// Pokud bylo ubytování přetaženo, najdou se pronájmy, které končili nejpozději, a těm se prodlouží obsazenost do teď.
			var noClosedRentalsWithOccupancy = RentedRooms.Where(x => x.RoomOccupancy != null)
				.Where(x => x.RoomOccupancy.OccupiedTo == x.RentedTo); // pouze ta ubytování, která ještě nebyla ukončena;
			var rentalsToSetTo = noClosedRentalsWithOccupancy
				.Where(x => x.RentedTo >= now);
			if (!rentalsToSetTo.Any() && noClosedRentalsWithOccupancy.Any())
			{
				var max = noClosedRentalsWithOccupancy.Max(x => x.RentedTo);
				rentalsToSetTo = RentedRooms.Where(x => x.RentedTo == max);
			}

			foreach (var rental in rentalsToSetTo)
			{
				rental.RoomOccupancy.OccupiedTo = now;
			}
		}
	}
}
