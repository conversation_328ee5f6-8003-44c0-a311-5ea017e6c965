using Anete.Log4Net.Core;
using Anete.Utils.AppServices;
using System;
using System.IO;
using System.Linq;
using System.Runtime.Serialization;

namespace Anete.Common.Core.Business.Gdpr.GlobalParams
{
	public class GlobalParamsProvider
	{
		#region private fields...
		private const string _fileName = "UserGlobalParams.appstorage.xml";
		private static readonly ILogEx _log = LogManagerEx.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
		private GlobalParamsCache _cache;
		private readonly IAppDirsService _appDirsService;
		#endregion


		public GlobalParamsProvider(IAppDirsService appDirsService)
		{
			_appDirsService = appDirsService;
		}

		#region public methods...
		public void Save(GlobalParamsCache cache)
		{
			_cache = cache;

			string fileName = GetFileName();
			_log.Debug($"Save global params to {fileName}.");

			if (File.Exists(fileName))
			{
				File.Delete(fileName);
			}

			using (FileStream stream = new FileStream(fileName, FileMode.OpenOrCreate))
			{
				DataContractSerializer serializer = new DataContractSerializer(typeof(GlobalParamsCache));
				serializer.WriteObject(stream, cache);
			}
		}

		public UserGlobalParamsItem GetUserData(string userName)
		{
			if (_cache == null)
			{
				_cache = Load();
			}

			return _cache?.UserGlobalParamsItems?.FirstOrDefault(x => x.Login.ToLower() == userName.ToLower());
		}
		#endregion

		#region private methods...
		private GlobalParamsCache Load()
		{
			string fileName = GetFileName();
			_log.Debug($"Load global params from {fileName}.");

			GlobalParamsCache cache = null;

			if (!File.Exists(fileName))
			{
				return null;
			}

			FileStream stream = null;
			try
			{
				stream = new FileStream(fileName, FileMode.Open);
				DataContractSerializer serializer = new DataContractSerializer(typeof(GlobalParamsCache));
				cache = (GlobalParamsCache)serializer.ReadObject(stream);
			}
			catch (Exception ex)
			{
				_log.Error($"Nepodařilo se deseralizovat data v souboru {fileName}.", ex);
			}
			finally
			{
				stream.Dispose();
			}

			return cache;
		}

		private string GetFileName()
		{
			//AppDirsService appDirsService = AppDirsService.CreateTempService(Interface.Enums.ApplicationType.CashDesk, null);

			string folder = _appDirsService.CommonAppStorageDir;
			string fileName = Path.Combine(folder, _fileName);
			return fileName;
		}
		#endregion
	}
}