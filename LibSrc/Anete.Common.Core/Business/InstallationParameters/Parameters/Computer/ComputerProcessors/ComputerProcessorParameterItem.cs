using Anete.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Anete.Common.Core.Business.InstallationParameters.Parameters.Computer.ComputerProcessors
{
	public class ComputerProcessorParameterItem
	{
		public ComputerProcessorParameterItem(string name, uint numberOfCores, uint numberOfLogicalProcessors, uint maxClockSpeed)
		{
			Name = name;
			NumberOfCores = numberOfCores;
			NumberOfLogicalProcessors = numberOfLogicalProcessors;
			MaxClockSpeed = maxClockSpeed;
		}

		public string Name { get; }
		public uint NumberOfCores { get; }
		public uint NumberOfLogicalProcessors { get; }
		public uint MaxClockSpeed { get; }

		public override int GetHashCode()
		{
			return HashCodeUtils.GetHashCode(Name, NumberOfCores, NumberOfLogicalProcessors, MaxClockSpeed);
		}

		public override bool Equals(object obj)
		{
			ComputerProcessorParameterItem other = obj as ComputerProcessorParameterItem;
			if (other == null)
			{
				return false;
			}

			return Name == other.Name
				&& NumberOfCores == other.NumberOfCores
				&& NumberOfLogicalProcessors == other.NumberOfLogicalProcessors
				&& MaxClockSpeed == other.MaxClockSpeed;
		}
	}
}
