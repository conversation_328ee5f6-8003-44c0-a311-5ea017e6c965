//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Anete.Common.Data.Nh.Entities
{

    /// <summary>
    /// Tabulka: dba.KartyLHistorieLin
    /// KartyLHistorie s rozsahem datumů
    /// </summary>
    /// <remark>
    /// Jedná se v podstatě o KartyLHistorie
    /// , ale místo 1 datumu obsahuje rozsah datumů
    /// </remark>
    public partial class ClientRegisterHistoryRange : Anete.Data.Nh.NhEntityBase, INotifyPropertyChanging, INotifyPropertyChanged {

        private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(System.String.Empty);

        private int _ClientRegisterId;

        private System.DateTime _StartDate;

        private System.DateTime? _EndDate;

        private ClientRegister _ClientRegister;

        private Resort _Resort;

        private ClientGroup _ClientGroup;
    
        #region Extensibility Method Definitions
        
        partial void OnCreated();

        public override bool Equals(object obj)
        {
          ClientRegisterHistoryRange toCompare = obj as ClientRegisterHistoryRange;
          if (toCompare == null)
          {
            return false;
          }

          if (!Object.Equals(this.ClientRegisterId, toCompare.ClientRegisterId))
            return false;
          if (!Object.Equals(this.StartDate, toCompare.StartDate))
            return false;
         // pokud mam default primary key, znamena to, ze jedna o nove vytvorenou entitu, ktera jeste nema key prirazen. 
        // V takove pripade musim porovnat podle instance
        bool isDefault = true;
        if (this.ClientRegisterId != default(int))
        {
          isDefault = false;
        }
     
        if (this.StartDate != default(System.DateTime))
        {
          isDefault = false;
        }
     

          if (isDefault)
          {
            return object.ReferenceEquals(toCompare, this);
          }     
          return true;
        }

	private int? _hashCode = null;
        public override int GetHashCode()
        {
	if (_hashCode == null)
          {
          _hashCode = 13;
          _hashCode = (_hashCode * 7) + ClientRegisterId.GetHashCode();
          _hashCode = (_hashCode * 7) + StartDate.GetHashCode();
}
          return _hashCode.Value;
        }
        partial void OnClientRegisterIdChanging(int value);
        
        partial void OnClientRegisterIdChanged();
        partial void OnStartDateChanging(System.DateTime value);
        
        partial void OnStartDateChanged();
        partial void OnEndDateChanging(System.DateTime? value);
        
        partial void OnEndDateChanged();
        partial void OnClientRegisterChanging(ClientRegister value);

        partial void OnClientRegisterChanged();
        partial void OnResortChanging(Resort value);

        partial void OnResortChanged();
        partial void OnClientGroupChanging(ClientGroup value);

        partial void OnClientGroupChanged();
        
        #endregion
        public ClientRegisterHistoryRange()
        {
            OnCreated();
        }

    
        /// <summary>
        /// Sloupec: id_lk
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("ClientRegisterId", typeof(ClientRegisterHistoryRangeSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(ClientRegisterHistoryRange), Tag="ClientRegisterId")]
        public virtual int ClientRegisterId
        {
            get
            {
                return this._ClientRegisterId;
            }
            set
            {
                if (this._ClientRegisterId != value)
                {
                    this.OnClientRegisterIdChanging(value);
                    this.SendPropertyChanging();
                    this._ClientRegisterId = value;
                    this.SendPropertyChanged("ClientRegisterId");
                    this.OnClientRegisterIdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: DatumOd
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("StartDate", typeof(ClientRegisterHistoryRangeSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(ClientRegisterHistoryRange), Tag="StartDate")]
        public virtual System.DateTime StartDate
        {
            get
            {
                return this._StartDate;
            }
            set
            {
                if (this._StartDate != value)
                {
                    this.OnStartDateChanging(value);
                    this.SendPropertyChanging();
                    this._StartDate = value;
                    this.SendPropertyChanged("StartDate");
                    this.OnStartDateChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: DatumDo
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("EndDate", typeof(ClientRegisterHistoryRangeSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual System.DateTime? EndDate
        {
            get
            {
                return this._EndDate;
            }
            set
            {
                if (this._EndDate != value)
                {
                    this.OnEndDateChanging(value);
                    this.SendPropertyChanging();
                    this._EndDate = value;
                    this.SendPropertyChanged("EndDate");
                    this.OnEndDateChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: id_lk
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("ClientRegister", typeof(ClientRegisterHistoryRangeSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual ClientRegister ClientRegister
        {
            get
            {
                return this._ClientRegister;
            }
            set
            {
                if (this._ClientRegister != value)
                {
                    this.OnClientRegisterChanging(value);
                    this.SendPropertyChanging();
                    this._ClientRegister = value;
                    this.SendPropertyChanged("ClientRegister");
                    this.OnClientRegisterChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: id_str
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Resort", typeof(ClientRegisterHistoryRangeSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual Resort Resort
        {
            get
            {
                return this._Resort;
            }
            set
            {
                if (this._Resort != value)
                {
                    this.OnResortChanging(value);
                    this.SendPropertyChanging();
                    this._Resort = value;
                    this.SendPropertyChanged("Resort");
                    this.OnResortChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: id_sk
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("ClientGroup", typeof(ClientRegisterHistoryRangeSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual ClientGroup ClientGroup
        {
            get
            {
                return this._ClientGroup;
            }
            set
            {
                if (this._ClientGroup != value)
                {
                    this.OnClientGroupChanging(value);
                    this.SendPropertyChanging();
                    this._ClientGroup = value;
                    this.SendPropertyChanged("ClientGroup");
                    this.OnClientGroupChanged();
                }
            }
        }
   
        public virtual event PropertyChangingEventHandler PropertyChanging;

	// Mam v bazove tride, nema smysl generovat
	// public virtual event PropertyChangedEventHandler PropertyChanged;

        protected virtual void SendPropertyChanging()
        {
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, emptyChangingEventArgs);
        }

        protected virtual void SendPropertyChanging(System.String propertyName) 
        {   	
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, new PropertyChangingEventArgs(propertyName));
        }

        protected virtual void SendPropertyChanged(System.String propertyName)
        {    
		// mam v bazove tride, proto musim volat takto jednoduse
		OnPropertyChanged(propertyName);
/*		        var handler = this.PropertyChanged;
            if (handler != null)
                handler(this, new PropertyChangedEventArgs(propertyName));*/
        }
    }

}
