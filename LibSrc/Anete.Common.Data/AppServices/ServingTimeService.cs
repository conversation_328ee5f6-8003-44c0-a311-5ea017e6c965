using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using Anete.Data;
using Anete.Log4Net.Core;

namespace Anete.Common.Data.AppServices
{
	/// <summary>
	/// Sluzba pracujuci s tabulkou CFCasyVydeje. Vyuziva ji Kancelar a Vydejni misto.
	/// </summary>
	public class ServingTimeService
	{
		private static readonly ILogEx _log = LogManagerEx.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

		/// <summary>
		/// Vraci seznam vydavanych druuh jidel v zavislosti na case.
		/// Parametry vytvoreny tak, aby bylo mozne volani pouzit pro NHibernate a i bezny SqlCommand.
		/// </summary>		
		public IEnumerable<short> GetServedMealKinds(Func<SqlCommand> createCommandFunc, Action<SqlCommand> initCommand, short appInstallationId, ILogEx log = null)
		{
			log = log ?? _log;

			string vydava;		
			using (SqlCommand command = createCommandFunc())
			{
				initCommand(command);				
				command.CommandText = "select vydava from dba.CFCasyvydeje where dba.time(getdate()) between dba.time(start) and dba.time(stop) and id_zarizeni = @idZarizeni";
				command.Parameters.Add("@idZarizeni", System.Data.SqlDbType.SmallInt).Value = appInstallationId;
				log.DebugFormat(command.GetLogText());
				vydava = (string)command.ExecuteScalar();
			}

			return vydava == null ? new short[] { } : vydava.Split('|').Select(mk => short.Parse(mk));			
		}
	}
}
