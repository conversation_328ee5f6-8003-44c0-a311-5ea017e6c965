<Project>
	<Import Project="$(SolutionDir)Shared\TargetFramework.targets" />

	<!-- Musim pouzit StartWidth, protoze Debug profily Shellu jsou vytvoreny zvlast pro jednotlive profile catalogy -->
	<PropertyGroup Condition="$(Configuration.StartsWith('Debug'))">
		<DebugType>Full</DebugType>
		<DefineConstants>DEBUG;TRACE</DefineConstants>
		<NoWarn>NU1803;CA1416</NoWarn>
	</PropertyGroup>

	<PropertyGroup>
		<!-- Workaround for https://github.com/dotnet/roslyn-project-system/issues/1739 -->
		<!-- Nacteni assembly z GACU -->
		<AssemblySearchPaths>$(AssemblySearchPaths);{GAC}</AssemblySearchPaths>
		<Platforms>AnyCPU;x86;x64</Platforms>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)' == 'Release'">
		<DebugType>pdbonly</DebugType>
		<DefineConstants>RELEASE;TRACE</DefineConstants>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)' == 'Internal'">
		<DebugType>pdbonly</DebugType>
		<DefineConstants>INTERNAL;TRACE</DefineConstants>
	</PropertyGroup>

	<PropertyGroup>
		<AppDesignerFolder>Properties</AppDesignerFolder>
		<GenerateAssemblyInfo>false</GenerateAssemblyInfo>
		<EnableDefaultEmbeddedResourceItems>false</EnableDefaultEmbeddedResourceItems>
		<LangVersion>8.0</LangVersion>
	</PropertyGroup>

	<ItemGroup>
		<Compile Include="..\Shared\SharedAssemblyInfo.cs" Link="Properties\SharedAssemblyInfo.cs" Condition="Exists('..\Shared\SharedAssemblyInfo.cs')" />
		<!-- Stale podporuju knihovnu umistenou o jednu uroven vyse, pouziva se napriklad u Anete.AccessRightsGenTest -->
		<Compile Include="..\..\Shared\SharedAssemblyInfo.cs" Link="Properties\SharedAssemblyInfo.cs" Condition="Exists('..\..\Shared\SharedAssemblyInfo.cs')" />
	</ItemGroup>

	<ItemGroup>
		<!-- Musim pridat jen resource s suffixem SR. Mam totiz problem s datasety, kdy nemuzu jednoduse vyhodit vsechny souboru *.designer.cs, ale musim tyto soubory detekovat podle nazvu -->
		<None Remove="**\*SR.resx" />
		<Compile Remove="**\*SR.designer.cs" />

		<None Remove="**\*BR.resx" />
		<Compile Remove="**\*BR.designer.cs" />

		<EmbeddedResource Include="**/*.resx" LastGenOutput="%(Filename).Designer$(_SdkLanguageExtension)" Generator="ResXFileCodeGeneratorEx" />
		<_ResxFiles Include="**/*SR.resx" DesignerName="%(RecursiveDir)%(Filename).Designer$(_SdkLanguageExtension)" />
		<_ResxFiles Include="**/*BR.resx" DesignerName="%(RecursiveDir)%(Filename).Designer$(_SdkLanguageExtension)" />
		<Compile Include="@(_ResxFiles -> '%(DesignerName).cs')" AutoGen="True" DesignTime="True" DependentUpon="$([System.String]::Copy(&quot;%(Filename)&quot;).Replace(&quot;.Designer&quot;,&quot;&quot;)).resx" />

		<None Remove="**\*.edmx" />
		<EntityDeploy Include="**\*.edmx" LastGenOutput="%(Filename).Designer$(_SdkLanguageExtension)" Generator="EntityModelCodeGenerator" />
	</ItemGroup>

	<ItemGroup Condition="'$(TargetFramework)' != 'net472'">
		<!-- .NET Core assemblyBindingy stejně ignoruje a nic jinýho tam není -->
		<None Remove="app.config" />
	</ItemGroup>
</Project>