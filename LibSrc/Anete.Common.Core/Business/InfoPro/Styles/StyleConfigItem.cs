using Anete.Common.Core.Business.InfoPro.Components;
using Anete.Utils.ComponentModel.UI;
using Newtonsoft.Json;

namespace Anete.Common.Core.Business.InfoPro.Styles
{
	[JsonObject(MemberSerialization.OptOut)]
	public class StyleConfigItem : NotifyPropertyChangedBase
	{
		private StyleConfigItemType _itemType;
		/// <summary>
		/// Typ ke kteremu se styl prirazuje (sablona, komponenta atd.)
		/// </summary>
		public StyleConfigItemType ItemType
		{
			get
			{
				return _itemType;
			}
			set
			{
				if (_itemType != value)
				{
					_itemType = value;
					OnPropertyChanged();
				}
			}
		}

		private ComponentType? _componentType;
		/// <summary>
		/// Typ komponenty. Pouziva se pouze v situaci, kdy se styl prirazuje ke komponente.
		/// </summary>
		public ComponentType? ComponentType
		{
			get
			{
				return _componentType;
			}
			set
			{
				if (_componentType != value)
				{
					_componentType = value;
					OnPropertyChanged();
				}
			}
		}
	}
}