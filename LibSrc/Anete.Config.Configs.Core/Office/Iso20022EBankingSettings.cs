using System;
using System.Collections.Generic;
using System.Text;
using System.ComponentModel;
using Anete.Config.Core.Attributes;
using Anete.Utils;
using Anete.Utils.Collections;

namespace Anete.Config.Configs.Core.Office
{
	public class Iso20022EBankingSettings : EBankingSettingsBase
	{
		private const int _ownerIdDefValue = 0;
		private int _ownerId = _ownerIdDefValue;
		/// <summary>
		/// Id klienta
		/// </summary>
		[DefaultValue(_ownerIdDefValue)]
		[ConfigSRDisplayName("OwnerId", typeof(Iso20022EBankingSettings))]
		[ConfigSRDescription("OwnerId", typeof(Iso20022EBankingSettings))]
		public int OwnerId
		{
			get { return _ownerId; }
			set
			{
				if (_ownerId == value)
				{
					return;
				}
				_ownerId = value;
				OnPropertyChanged("OwnerId");
			}
		}

		private const string _ownerNameDefValue = "";
		private string _ownerName = _ownerNameDefValue;
		/// <summary>
		/// Nazev klienta
		/// </summary>
		[DefaultValue(_ownerNameDefValue)]
		[ConfigSRDisplayName("OwnerName", typeof(Iso20022EBankingSettings))]
		[ConfigSRDescription("OwnerName", typeof(Iso20022EBankingSettings))]
		public string OwnerName
		{
			get { return _ownerName; }
			set
			{
				if (_ownerName == value)
				{
					return;
				}

				if (!DeserializationRunning && value.Length > 70)
				{
					throw new ArgumentOutOfRangeException("OwnerName", "Maxim�ln� po�et znak� je 70");
				}

				_ownerName = value;
				OnPropertyChanged("OwnerName");
			}
		}

		private const string _ownerIbanDefValue = "";
		private string _ownerIban = _ownerIbanDefValue;
		/// <summary>
		/// IBAN klienta
		/// </summary>
		[DefaultValue(_ownerIbanDefValue)]
		[ConfigSRDisplayName("OwnerIban", typeof(Iso20022EBankingSettings))]
		[ConfigSRDescription("OwnerIban", typeof(Iso20022EBankingSettings))]
		public string OwnerIban
		{
			get { return _ownerIban; }
			set
			{
				if (_ownerIban == value)
				{
					return;
				}
				_ownerIban = value;
				OnPropertyChanged("OwnerIban");
			}
		}

		private const string _ownerBicDefValue = "";
		private string _ownerBic = _ownerBicDefValue;
		/// <summary>
		/// BIC kod klienta
		/// </summary>
		[DefaultValue(_ownerBicDefValue)]
		[ConfigSRDisplayName("OwnerBic", typeof(Iso20022EBankingSettings))]
		[ConfigSRDescription("OwnerBic", typeof(Iso20022EBankingSettings))]
		public string OwnerBic
		{
			get { return _ownerBic; }
			set
			{
				if (_ownerBic == value)
				{
					return;
				}
				_ownerBic = value;
				OnPropertyChanged("OwnerBic");
			}
		}


		public override void GetCFSystKonfigTokens(KeyValueTokens tokens)
		{
			tokens.Add("Id", OwnerId.ToString());
			tokens.Add("Name", OwnerName);
			tokens.Add("IBAN", OwnerIban);
			tokens.Add("BIC", OwnerBic);
		}

		public override void ParseCFSystKonfigTokens(KeyValueTokens tokens)
		{
			OwnerId = tokens.GetWithDefault<int>("Id", _ownerIdDefValue);
			OwnerName = tokens.GetWithDefault<string>("Name", _ownerNameDefValue);
			OwnerIban = tokens.GetWithDefault<string>("IBAN", _ownerIbanDefValue);
			OwnerBic = tokens.GetWithDefault<string>("BIC", _ownerBicDefValue);
		}
	}
}
