using System;
using Anete.Utils.Extensions;
using Anete.Utils.Reflection.ClassReports;

// pozor, zde jsem nechal puvodni namespace, vz<PERSON><PERSON> v jiz uvolnene verzi kvuli implementaci ctecky otisku prstu
// zmenit namespace muzu az v trunk verzi
namespace Anete.Devices.Common.Reader
{
    /// <summary>
    /// Argumenty pri precteni karty
    /// Rozsiruje CodeReadedEventArgs o typ karty.
    /// Pouziva se pouze u EWallet, ktera je schopna predat typ karty. Ten se ale nikde nepouziva. 
    /// </summary>
    public class IdentCardReadedEventArgs : Anete.Devices.Common.Reader.CodeReadedEventArgs
    {
        /// <summary>
        /// Initializes a new instance of the CardReadedEventArgs class.
        /// </summary>
        /// <param name="cardId">The card id.</param>
        /// <param name="cardType">Type of the card.</param>
        public IdentCardReadedEventArgs(string cardId, IdentCardType cardType)
            : base(cardId)
        {
            CardType = cardType;
        }

        /// <summary>
        /// Typ karty
        /// </summary>
        public IdentCardType CardType { get; private set; }

        /// <summary>
        /// Returns a <see cref="System.String"/> that represents this instance.
        /// </summary>
        /// <returns>
        /// A <see cref="System.String"/> that represents this instance.
        /// </returns>
        public override string ToString()
        {
            return this.ToReportString(); 
        }
    }
}
