//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate Fluent Mapping template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;
using FluentNHibernate.Mapping;
using FluentNHibernate.MappingModel.Collections;

namespace Anete.Common.Data.Nh.Entities
{
    /// <summary>
    /// There are no comments for MagionAccountCoding3Map in the schema.
    /// </summary>
    public partial class MagionAccountCoding3Map : ClassMap<MagionAccountCoding3>
    {
        /// <summary>
        /// There are no comments for MagionAccountCoding3Map constructor in the schema.
        /// </summary>
        public MagionAccountCoding3Map()
        {
              Schema(@"dbo");
              Table(@"OSU_MagionKontaceSluzba3");
              DynamicInsert();
              DynamicUpdate();
              LazyLoad();
              CompositeId()
                .KeyProperty(x => x.GoodsGroupId, set => {
                    set.Type("Int16");
                    set.ColumnName("id_skupina");
                    set.Access.Property(); } )
                .KeyProperty(x => x.CanteenId, set => {
                    set.Type("Int16");
                    set.ColumnName("id_vydejna");
                    set.Access.Property(); } )
                .KeyProperty(x => x.ValidFrom, set => {
                    set.Type("DateTime");
                    set.ColumnName("PlatiOd");
                    set.Access.Property(); } );
              Map(x => x.FinancialSource)    
                .Column("OSU_FinZdroj")
                .CustomType("String")
                .Access.Property()
                .Generated.Never().CustomSqlType("varchar(6)")
                .Not.Nullable()
                .Length(6);
              Map(x => x.Contract)    
                .Column("OSU_Zakazka")
                .CustomType("String")
                .Access.Property()
                .Generated.Never().CustomSqlType("varchar(6)")
                .Not.Nullable()
                .Length(6);
              Map(x => x.Type)    
                .Column("OSU_Typ")
                .CustomType("String")
                .Access.Property()
                .Generated.Never().CustomSqlType("varchar(6)")
                .Not.Nullable()
                .Length(6);
              Map(x => x.ValidTo)    
                .Column("PlatiDo")
                .CustomType("DateTime")
                .Access.Property()
                .Generated.Never().CustomSqlType("date")
                .Not.Nullable();
              References(x => x.Canteen)
                .Class<Canteen>()
                .Access.Property()
                .Cascade.None()
                .LazyLoad()
                .Not.Insert()
                .Not.Update()
                .Columns("id_vydejna");
              References(x => x.GoodsGroup)
                .Class<GoodsGroup>()
                .Access.Property()
                .Cascade.None()
                .LazyLoad()
                .Not.Insert()
                .Not.Update()
                .Columns("id_skupina");
              ExtendMapping();
        }

        #region Partial Methods

        partial void ExtendMapping();

        #endregion
    }

}
