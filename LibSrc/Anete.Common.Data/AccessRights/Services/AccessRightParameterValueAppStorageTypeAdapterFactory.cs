using System;
using System.Collections.Generic;
using System.Linq;
using Anete.Common.Data.Interface.Business.AccessRights.Parameters;
using Anete.Common.Core.AppServices.AppStorage;
using Unity;

namespace Anete.Common.Data.AccessRights.Services
{
	/// <summary>
	/// Implementace IAccessRightParameterValueAppStorageAdapterFactory
	/// </summary>
	public class AccessRightParameterValueAppStorageTypeAdapterFactory : AppStorageTypeAdapterFactory, IAccessRightParameterValueAppStorageAdapterFactory
	{
		/// <summary>
		/// Vytvori novou instanci
		/// </summary>
		public AccessRightParameterValueAppStorageTypeAdapterFactory(IUnityContainer container)
			: base(container)
		{
			
		}

	}
}
