//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Anete.Common.Data.Nh.Entities.Sklady
{

    /// <summary>
    /// Tabulka: dba.SKC_UCETNI_OBDOBI
    /// </summary>
    public partial class SkcAccountingPeriod : Anete.Data.Nh.NhEntityBase, INotifyPropertyChanging, INotifyPropertyChanged {

        private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(System.String.Empty);

        private short _StockId;

        private short _AccountingPeriodId;

        private System.DateTime? _Closed;

        private string _Name;

        private System.DateTime? _DateEnd;

        private System.DateTime? _DateStart;

        private string _Username;

        private SkcStock _SkcStock;
    
        #region Extensibility Method Definitions
        
        partial void OnCreated();

        public override bool Equals(object obj)
        {
          SkcAccountingPeriod toCompare = obj as SkcAccountingPeriod;
          if (toCompare == null)
          {
            return false;
          }

          if (!Object.Equals(this.StockId, toCompare.StockId))
            return false;
          if (!Object.Equals(this.AccountingPeriodId, toCompare.AccountingPeriodId))
            return false;
         // pokud mam default primary key, znamena to, ze jedna o nove vytvorenou entitu, ktera jeste nema key prirazen. 
        // V takove pripade musim porovnat podle instance
        bool isDefault = true;
        if (this.StockId != default(short))
        {
          isDefault = false;
        }
     
        if (this.AccountingPeriodId != default(short))
        {
          isDefault = false;
        }
     

          if (isDefault)
          {
            return object.ReferenceEquals(toCompare, this);
          }     
          return true;
        }

	private int? _hashCode = null;
        public override int GetHashCode()
        {
	if (_hashCode == null)
          {
          _hashCode = 13;
          _hashCode = (_hashCode * 7) + StockId.GetHashCode();
          _hashCode = (_hashCode * 7) + AccountingPeriodId.GetHashCode();
}
          return _hashCode.Value;
        }
        partial void OnStockIdChanging(short value);
        
        partial void OnStockIdChanged();
        partial void OnAccountingPeriodIdChanging(short value);
        
        partial void OnAccountingPeriodIdChanged();
        partial void OnClosedChanging(System.DateTime? value);
        
        partial void OnClosedChanged();
        partial void OnNameChanging(string value);
        
        partial void OnNameChanged();
        partial void OnDateEndChanging(System.DateTime? value);
        
        partial void OnDateEndChanged();
        partial void OnDateStartChanging(System.DateTime? value);
        
        partial void OnDateStartChanged();
        partial void OnUsernameChanging(string value);
        
        partial void OnUsernameChanged();
        partial void OnSkcStockChanging(SkcStock value);

        partial void OnSkcStockChanged();
        
        #endregion
        public SkcAccountingPeriod()
        {
            OnCreated();
        }

    
        /// <summary>
        /// Sloupec: SKLAD_ID
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("StockId", typeof(SkcAccountingPeriodSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(SkcAccountingPeriod), Tag="StockId")]
        public virtual short StockId
        {
            get
            {
                return this._StockId;
            }
            set
            {
                if (this._StockId != value)
                {
                    this.OnStockIdChanging(value);
                    this.SendPropertyChanging();
                    this._StockId = value;
                    this.SendPropertyChanged("StockId");
                    this.OnStockIdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: UCETNI_OBDOBI
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("AccountingPeriodId", typeof(SkcAccountingPeriodSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(SkcAccountingPeriod), Tag="AccountingPeriodId")]
        public virtual short AccountingPeriodId
        {
            get
            {
                return this._AccountingPeriodId;
            }
            set
            {
                if (this._AccountingPeriodId != value)
                {
                    this.OnAccountingPeriodIdChanging(value);
                    this.SendPropertyChanging();
                    this._AccountingPeriodId = value;
                    this.SendPropertyChanged("AccountingPeriodId");
                    this.OnAccountingPeriodIdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: UZAVRENO
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Closed", typeof(SkcAccountingPeriodSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual System.DateTime? Closed
        {
            get
            {
                return this._Closed;
            }
            set
            {
                if (this._Closed != value)
                {
                    this.OnClosedChanging(value);
                    this.SendPropertyChanging();
                    this._Closed = value;
                    this.SendPropertyChanged("Closed");
                    this.OnClosedChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: POPIS
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Name", typeof(SkcAccountingPeriodSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(SkcAccountingPeriod), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 50,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="Name")]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(SkcAccountingPeriod), Tag="Name")]
        public virtual string Name
        {
            get
            {
                return this._Name;
            }
            set
            {
                if (this._Name != value)
                {
                    this.OnNameChanging(value);
                    this.SendPropertyChanging();
                    this._Name = value;
                    this.SendPropertyChanged("Name");
                    this.OnNameChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: DATUM_UZAVERKY
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("DateEnd", typeof(SkcAccountingPeriodSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual System.DateTime? DateEnd
        {
            get
            {
                return this._DateEnd;
            }
            set
            {
                if (this._DateEnd != value)
                {
                    this.OnDateEndChanging(value);
                    this.SendPropertyChanging();
                    this._DateEnd = value;
                    this.SendPropertyChanged("DateEnd");
                    this.OnDateEndChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: DATUM_OD
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("DateStart", typeof(SkcAccountingPeriodSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual System.DateTime? DateStart
        {
            get
            {
                return this._DateStart;
            }
            set
            {
                if (this._DateStart != value)
                {
                    this.OnDateStartChanging(value);
                    this.SendPropertyChanging();
                    this._DateStart = value;
                    this.SendPropertyChanged("DateStart");
                    this.OnDateStartChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: PROVEDL_KDO
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Username", typeof(SkcAccountingPeriodSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(SkcAccountingPeriod), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 30,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="Username")]
        public virtual string Username
        {
            get
            {
                return this._Username;
            }
            set
            {
                if (this._Username != value)
                {
                    this.OnUsernameChanging(value);
                    this.SendPropertyChanging();
                    this._Username = value;
                    this.SendPropertyChanged("Username");
                    this.OnUsernameChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: SKLAD_ID
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("SkcStock", typeof(SkcAccountingPeriodSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual SkcStock SkcStock
        {
            get
            {
                return this._SkcStock;
            }
            set
            {
                if (this._SkcStock != value)
                {
                    this.OnSkcStockChanging(value);
                    this.SendPropertyChanging();
                    this._SkcStock = value;
                    this.SendPropertyChanged("SkcStock");
                    this.OnSkcStockChanged();
                }
            }
        }
   
        public virtual event PropertyChangingEventHandler PropertyChanging;

	// Mam v bazove tride, nema smysl generovat
	// public virtual event PropertyChangedEventHandler PropertyChanged;

        protected virtual void SendPropertyChanging()
        {
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, emptyChangingEventArgs);
        }

        protected virtual void SendPropertyChanging(System.String propertyName) 
        {   	
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, new PropertyChangingEventArgs(propertyName));
        }

        protected virtual void SendPropertyChanged(System.String propertyName)
        {    
		// mam v bazove tride, proto musim volat takto jednoduse
		OnPropertyChanged(propertyName);
/*		        var handler = this.PropertyChanged;
            if (handler != null)
                handler(this, new PropertyChangedEventArgs(propertyName));*/
        }
    }

}
