using System;
using System.Collections.Generic;
using Anete.Common.Data.Interface.Business.Gdpr;
using Anete.Utils.ComponentModel.Attributes;

namespace Anete.Common.Data.Business.Paragon.LinkedSalesSlip
{

	/// <summary>
	/// Polozka pripojenych paragonu
	/// </summary>
	public class LinkedSalesSlipItem : IOrganizationIdEntityProvider
	{
		[ResXDisplayNameAttribute(nameof(AccountBalanceId), typeof(LinkedSalesSlipItemSR))]
		public short AccountBalanceId { get; set; }

		[ResXDisplayNameAttribute(nameof(SalesSlipId), typeof(LinkedSalesSlipItemSR))]
		public int SalesSlipId { get; set; }

		[ResXDisplayNameAttribute(nameof(TimeStamp), typeof(LinkedSalesSlipItemSR))]
		public DateTime TimeStamp { get; set; }

		[ResXDisplayNameAttribute(nameof(SaleTime), typeof(LinkedSalesSlipItemSR))]
		public DateTime SaleTime { get; set; }

		[ResXDisplayNameAttribute(nameof(CashierName), typeof(LinkedSalesSlipItemSR))]
		public string CashierName { get; set; }

		[ResXDisplayNameAttribute(nameof(TotalPrice), typeof(LinkedSalesSlipItemSR))]
		public decimal TotalPrice { get; set; }

		[ResXDisplayNameAttribute(nameof(ClientId), typeof(LinkedSalesSlipItemSR))]
		public int ClientId { get; set; }

		[ResXDisplayNameAttribute(nameof(ClientName), typeof(LinkedSalesSlipItemSR))]
		public string ClientName { get; set; }

		[ResXDisplayNameAttribute(nameof(Note), typeof(LinkedSalesSlipItemSR))]
		public string Note { get; set; }

		[ResXDisplayNameAttribute(nameof(SalesSlipNumber), typeof(LinkedSalesSlipItemSR))]
		public long SalesSlipNumber { get; set; }

		public int? OrganizationId { get; set; }

		public List<LinkedSalesSlipDetailItem> DetailItems { get; set; }

	}
}
