using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Anete.Common.Data.Validators;
using Anete.Common.Core.Interface.Validators;

namespace Anete.Common.Data.Nh.Entities
{
	public class Pictogram_Metadata
	{
		[CollectionMaximumLengthValidator(100 * 1024, Tag = "Picture", MessageTemplateResourceType=typeof(PictogramSR), MessageTemplateResourceName="PictogramMaximumSize")]
		public virtual byte[] Picture { get; set; }
	}
}
