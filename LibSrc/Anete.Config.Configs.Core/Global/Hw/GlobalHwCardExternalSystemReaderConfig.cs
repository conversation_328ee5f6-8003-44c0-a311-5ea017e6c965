using Anete.Common.Core.Interface.Enums;
using Anete.Config.Core.Attributes;
using Anete.Config.Core.Categories;
using Anete.Config.Core.Internals;
using Anete.Utils;
using Anete.Utils.ComponentModel.Attributes;
using System;
using System.ComponentModel;

namespace Anete.Config.Configs.Core.Global.Hw
{
    /// <summary>
    /// Obecna konfigurace ctecky zalozena na prejimani dat z externiho systemu.
    /// Vytvoreno kvuli ctecce otisku prstu, nicmene je mozne pouzit obecne.
    /// </summary>
    [GlobalHwConfigDBKey("CardExternalSystemReader")]
    [ConfigVersion("*******")]
    [DefaultConfigInstance(true)]
    [ConfigClassSRDescription(typeof(GlobalHwCardExternalSystemReaderConfig))]
    [ApplicationCategory(ApplicationType.PresPoint)]
    [ApplicationCategory(ApplicationType.DevicesController)]
    //[CashDeskCategory(CategoryLevel.Basic, CategoryLevel.Advanced)]
    //[OfficeCategory(CategoryLevel.Basic, CategoryLevel.Advanced)]
    //[OfflineConfig(ApplicationType.CashDesk)]
    public class GlobalHwCardExternalSystemReaderConfig : ConfigBase
    {
        public GlobalHwCardExternalSystemReaderConfig()
        {
            _settings = EnumBasedFactory<ExternalSystemReaderType, ExternalSystemReaderSettings>.CreateInstance(_readerType);
        }

        private const ExternalSystemReaderType _readerTypeDefValue = ExternalSystemReaderType.None;
        private ExternalSystemReaderType _readerType = _readerTypeDefValue;
        /// <summary>
        /// Typ externi ctecky. Pri zmene se vytvori odpovidaji instance.
        /// </summary>
        [DefaultValue(typeof(ExternalSystemReaderType), nameof(ExternalSystemReaderType.None))]
        [ConfigSRDisplayName("ReaderType", typeof(GlobalHwCardExternalSystemReaderConfig))]
        [ConfigSRDescriptionWithEnumDescription("ReaderType", typeof(ExternalSystemReaderType), typeof(GlobalHwCardExternalSystemReaderConfig))]
        [RefreshProperties(RefreshProperties.All)]
        public ExternalSystemReaderType ReaderType
        {
            get
            {
                return _readerType;
            }
            set
            {
                if (_readerType == value)
                {
                    return;
                }
                _readerType = value;
                // pokud doslo ke zmene, musi vytvorit spravnou instanci   
                Settings = EnumBasedFactory<ExternalSystemReaderType, ExternalSystemReaderSettings>.CreateInstance(_readerType);
                OnPropertyChanged();
            }
        }

        private ExternalSystemReaderSettings _settings;
        /// <summary>
        /// Instance nakonfigurovane externi ctecky.
        /// </summary>
        [ConfigSRDisplayName("Settings", typeof(GlobalHwCardExternalSystemReaderConfig))]
        [ConfigSRDescription("Settings", typeof(GlobalHwCardExternalSystemReaderConfig))]
        [NestedClass]
        [Inherit(false)]
        public ExternalSystemReaderSettings Settings
        {
            get
            {
                return _settings;
            }
            set
            {
                if (_settings == value)
                {
                    return;
                }

                _settings = value;
                OnPropertyChanged();
            }
        }
    }
}
