<?xml version="1.0" encoding="utf-8"?>
<EntityDeveloperDiagram>
  <Diagram Version="1.20.0.95">
    <DiagramModel>
      <Model xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xsi:type="ContextVwModel">
        <CustomProperties>
          <OID>0</OID>
          <BackgroundColor>Window</BackgroundColor>
        </CustomProperties>
        <Children>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>1</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>2</OID>
                  <Parent>1</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>3</OID>
                      <Parent>2</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>3f5e0fc8-e1e2-41e5-a2fd-63296659af5f</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>4</OID>
                      <Parent>2</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>f7107c8f-3c43-481a-8ab9-f2e4ba8bd7f9</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>5</OID>
                      <Parent>2</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>4b2f575c-46a8-4e85-b760-9f686380ee8a</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>6</OID>
                      <Parent>2</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>54.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>c4af297f-9e2f-4be4-b4cf-5ecb84d25654</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>7</OID>
                      <Parent>2</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>72.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>e3f9adc4-d0ff-4dc3-a678-2d3e9f54d022</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>8</OID>
                      <Parent>2</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>90.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>31915355-3638-4066-8147-1ee355826b80</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>145 px</Width>
                  <Height>109 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>109 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>9</OID>
                  <Parent>1</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>10</OID>
                      <Parent>9</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>c22ee921-ee18-48b5-badc-50a222af7a3f</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>11</OID>
                      <Parent>9</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>18 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>3ac0267e-732b-4b56-b500-151ded4b025a</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>12</OID>
                      <Parent>9</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>36 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>cbb74cce-3874-466d-8333-1f95712ef7bb</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>13</OID>
                      <Parent>9</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>54 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>2ff23b96-bd7e-41dc-92e0-09537efac4f5</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>14</OID>
                      <Parent>9</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>72 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>68034511-5d68-4e61-81d7-80c790499678</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>15</OID>
                      <Parent>9</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>90 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>7d49dee9-6450-4250-a5a6-e183154c7058</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>108 px</Y>
                </Location>
                <Size>
                  <Width>145 px</Width>
                  <Height>127 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>127 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>16 px</X>
              <Y>-112 px</Y>
            </Location>
            <Size>
              <Width>150 px</Width>
              <Height>284 px</Height>
            </Size>
            <MinSize>
              <Width>120 px</Width>
              <Height>284 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>284 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>966fbc90-825f-4a3c-b3f4-c92a66fb7439</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>16</OID>
              <OutModel>1</OutModel>
              <InModel>1</InModel>
              <OutPort>17</OutPort>
              <InPort>18</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>142 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Left</Align>
                <CustomProperties>
                  <OID>17</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>213 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Left</Align>
                <CustomProperties>
                  <OID>18</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <BendPoints>
              <PointD>
                <X>-3 px</X>
                <Y>30 px</Y>
              </PointD>
              <PointD>
                <X>-3 px</X>
                <Y>101 px</Y>
              </PointD>
            </BendPoints>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>372039cc-73eb-41a9-9da8-5ee7825f2cb5</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>19</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>20</OID>
                  <Parent>19</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>21</OID>
                      <Parent>20</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>aace7be1-ecfd-4b39-b18b-9441cd62d0fa</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>22</OID>
                      <Parent>20</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>c4c86712-702a-411f-bbad-1cc8cd540b6d</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>23</OID>
                      <Parent>20</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>f9890cfb-ec87-4b44-9563-2db655de7fd3</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>24</OID>
                      <Parent>20</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>54.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>f4209ef3-ab10-4f3e-86dc-bc33e2560004</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>145 px</Width>
                  <Height>73 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>73 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>25</OID>
                  <Parent>19</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>26</OID>
                      <Parent>25</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>665bf2ab-c422-424b-be75-b2d88ea2c7f4</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>27</OID>
                      <Parent>25</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>18 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>294957aa-4645-4854-9975-7c2cc0df3386</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>28</OID>
                      <Parent>25</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>36 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>59e7b1a6-9b90-4883-a1df-472ccc741fe4</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>29</OID>
                      <Parent>25</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>54 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>c17bb515-5baa-4609-a8f8-6658b98f308f</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>72 px</Y>
                </Location>
                <Size>
                  <Width>145 px</Width>
                  <Height>91 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>91 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>240 px</X>
              <Y>-112 px</Y>
            </Location>
            <Size>
              <Width>150 px</Width>
              <Height>212 px</Height>
            </Size>
            <MinSize>
              <Width>120 px</Width>
              <Height>212 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>212 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>18ae285f-f9b0-4dcd-9b3b-7b1227a80c87</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>30</OID>
              <OutModel>19</OutModel>
              <InModel>19</InModel>
              <OutPort>31</OutPort>
              <InPort>32</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>106 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Left</Align>
                <CustomProperties>
                  <OID>31</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>159 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Left</Align>
                <CustomProperties>
                  <OID>32</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <BendPoints>
              <PointD>
                <X>221 px</X>
                <Y>-6 px</Y>
              </PointD>
              <PointD>
                <X>221 px</X>
                <Y>47 px</Y>
              </PointD>
            </BendPoints>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>fc1e9d17-6f39-44fc-b28e-61242932bd76</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>33</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>34</OID>
                  <Parent>33</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>35</OID>
                      <Parent>34</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>03266e01-e9ef-4291-b468-d66c8837008b</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>36</OID>
                      <Parent>34</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>1447959f-beea-42cf-8f72-13518796c0b6</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>37</OID>
                      <Parent>34</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>99713df4-dc50-472e-b572-19efd851c119</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>38</OID>
                      <Parent>34</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>54.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>c5787e88-b8f7-4e29-818a-d9924c3c59b3</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>39</OID>
                      <Parent>34</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>72.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>7ad50ba9-1b69-494a-9e56-bfa01a3280be</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>40</OID>
                      <Parent>34</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>90.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>ddf068ff-1c30-4dc4-a4ca-52f147eb4bc2</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>41</OID>
                      <Parent>34</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>108.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>1d04b342-76ec-4e96-a577-5469bcf977aa</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>42</OID>
                      <Parent>34</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>126.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>0c462509-9284-484f-8019-307c1e0f3df6</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>43</OID>
                      <Parent>34</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>144.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>39729b89-83d1-433f-8499-2d82d1d01481</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>44</OID>
                      <Parent>34</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>162.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>4a3723cf-d9b5-4f16-91dc-a0ffeb165abe</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>45</OID>
                      <Parent>34</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>180.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>72b04cc0-c199-48ee-a8be-f3b5a37a89d6</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>46</OID>
                      <Parent>34</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>198.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>d29ce36c-2fa6-433d-acb4-2e48d4d9bfc5</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>47</OID>
                      <Parent>34</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>216.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>f954510f-8196-4ee0-bd15-df35b18dc15c</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>48</OID>
                      <Parent>34</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>234.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>b8804c89-8ed8-4b27-9613-c034e311f883</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>145 px</Width>
                  <Height>253 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>253 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>49</OID>
                  <Parent>33</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>50</OID>
                      <Parent>49</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>e683e3e4-2590-4e02-b6ee-4fc5b6ff062f</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>51</OID>
                      <Parent>49</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>18 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>aedcf370-a026-4b87-a85c-b5e9e90e7fef</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>52</OID>
                      <Parent>49</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>36 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>6eae9f0a-10cc-4fc5-9c93-3788bd0449df</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>53</OID>
                      <Parent>49</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>54 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>e5c12e1c-015a-49da-ac20-fad8dea1c933</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>54</OID>
                      <Parent>49</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>72 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>13cf16a8-133f-4f59-917e-717984b68bd1</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>55</OID>
                      <Parent>49</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>90 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>b3985f7f-6ee8-4e8e-a17e-4c6496049d3a</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>56</OID>
                      <Parent>49</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>108 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>4447370a-4028-466e-8995-f3ec63f0d420</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>57</OID>
                      <Parent>49</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>126 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>22851a6e-1ccb-4afd-aa4e-979a301e7acd</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>58</OID>
                      <Parent>49</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>144 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>ccda2546-c71c-46a4-b6e2-e9914d4dca18</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>59</OID>
                      <Parent>49</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>162 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>1dec1348-3f21-4fec-8ca3-b8a22efc406e</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>60</OID>
                      <Parent>49</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>180 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>89a9cfbd-0385-4296-b650-973a6db35eba</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>61</OID>
                      <Parent>49</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>198 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>9fc1f191-dfe5-487c-b6ae-ba9d2f550377</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>62</OID>
                      <Parent>49</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>216 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>14de7f4f-616b-4e51-a0a5-49614ca35a18</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>63</OID>
                      <Parent>49</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>234 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>91795900-8dd8-4181-a56a-5abb67b68607</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>64</OID>
                      <Parent>49</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>252 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>ddfc637d-8f79-4d96-9ad5-f557714b7e68</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>65</OID>
                      <Parent>49</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>23b8829c-cb84-404e-be19-f1b789d0b382</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>66</OID>
                      <Parent>49</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>00d1645e-9739-490b-baa2-d29aa70adeaf</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>67</OID>
                      <Parent>49</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>2619bc35-066d-40f3-a065-1156a41b4800</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>68</OID>
                      <Parent>49</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>01dc7653-19d1-4185-9478-ee0ae8f6e282</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>69</OID>
                      <Parent>49</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>8e13fcb4-6407-431a-81be-5e060c031cd7</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>252 px</Y>
                </Location>
                <Size>
                  <Width>145 px</Width>
                  <Height>289 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>289 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>-192 px</X>
              <Y>-104 px</Y>
            </Location>
            <Size>
              <Width>150 px</Width>
              <Height>590 px</Height>
            </Size>
            <MinSize>
              <Width>120 px</Width>
              <Height>590 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>590 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>41d6120e-a7a7-4f0b-a1e8-6d1d3f4064da</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>70</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>71</OID>
                  <Parent>70</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>72</OID>
                      <Parent>71</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>8a3d1676-fffc-4357-be7a-bf8cd175a5db</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>73</OID>
                      <Parent>71</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>add585b1-68d5-4938-81cd-6c288181852e</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>74</OID>
                      <Parent>71</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>ec5d90d3-43af-48a7-b7df-3090535ba62d</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>145 px</Width>
                  <Height>55 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>55 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>75</OID>
                  <Parent>70</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>76</OID>
                      <Parent>75</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>67d8d0d3-3b34-435a-9cd7-fc11d219f397</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>77</OID>
                      <Parent>75</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>18 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>0756d3fa-8456-4e61-bd78-c2537aac28cd</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>78</OID>
                      <Parent>75</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>36 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>05eb7aa4-b719-4ac5-ad19-5a90049e7d94</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>54 px</Y>
                </Location>
                <Size>
                  <Width>145 px</Width>
                  <Height>73 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>73 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>240 px</X>
              <Y>144 px</Y>
            </Location>
            <Size>
              <Width>150 px</Width>
              <Height>176 px</Height>
            </Size>
            <MinSize>
              <Width>120 px</Width>
              <Height>176 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>176 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>b30eb337-2989-4328-ba0d-0c83b69cd006</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>79</OID>
              <OutModel>1</OutModel>
              <InModel>70</InModel>
              <OutPort>80</OutPort>
              <InPort>81</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>37.5 px</X>
                  <Y>0 px</Y>
                </Location>
                <Anchors>Left Right</Anchors>
                <Align>Top</Align>
                <CustomProperties>
                  <OID>81</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>133 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Right</Align>
                <CustomProperties>
                  <OID>80</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <BendPoints>
              <PointD>
                <X>186 px</X>
                <Y>21 px</Y>
              </PointD>
              <PointD>
                <X>186 px</X>
                <Y>124 px</Y>
              </PointD>
              <PointD>
                <X>277.5 px</X>
                <Y>124 px</Y>
              </PointD>
            </BendPoints>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>5b0f9388-48a5-42f6-9bae-d3c6f04ca896</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>82</OID>
              <OutModel>33</OutModel>
              <InModel>70</InModel>
              <OutPort>83</OutPort>
              <InPort>84</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>88 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Left</Align>
                <CustomProperties>
                  <OID>84</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>Undefined</X>
                  <Y>336 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Right</Align>
                <CustomProperties>
                  <OID>83</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>e2144c7e-b21d-4d0d-84ee-f55c3eaef0d8</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>85</OID>
              <OutModel>19</OutModel>
              <InModel>70</InModel>
              <OutPort>86</OutPort>
              <InPort>87</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>112.5 px</X>
                  <Y>0 px</Y>
                </Location>
                <Anchors>Left Right</Anchors>
                <Align>Top</Align>
                <CustomProperties>
                  <OID>87</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>112.5 px</X>
                  <Y>0 px</Y>
                </Location>
                <Anchors>Left Right</Anchors>
                <Align>Bottom</Align>
                <CustomProperties>
                  <OID>86</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>b54f676c-1185-4015-a067-e433585800ae</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>88</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>89</OID>
                  <Parent>88</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>90</OID>
                      <Parent>89</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>2cae3620-091a-414b-bff3-ffdf20e1d0c5</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>91</OID>
                      <Parent>89</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>b6a36de1-eb6a-48f7-b76d-607e66e9228e</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>92</OID>
                      <Parent>89</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>b953f04a-8ce1-47a1-9766-fa5f751d040a</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>145 px</Width>
                  <Height>55 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>55 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>93</OID>
                  <Parent>88</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>94</OID>
                      <Parent>93</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>64d2b43c-3f15-475c-afb6-dc7945d6ce35</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>95</OID>
                      <Parent>93</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>18 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>82b7227f-cf73-476e-8894-d13b364f3702</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>54 px</Y>
                </Location>
                <Size>
                  <Width>145 px</Width>
                  <Height>55 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>55 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>-200 px</X>
              <Y>480 px</Y>
            </Location>
            <Size>
              <Width>150 px</Width>
              <Height>158 px</Height>
            </Size>
            <MinSize>
              <Width>120 px</Width>
              <Height>158 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>158 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>8ea32d9a-6a2f-4064-8097-339be6c438e1</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>96</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>97</OID>
                  <Parent>96</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>98</OID>
                      <Parent>97</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>d9935d08-f576-4e9d-a087-ebccf6117c25</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>99</OID>
                      <Parent>97</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>c57486fd-94ca-451e-b15c-55511fbc9965</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>100</OID>
                      <Parent>97</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>6fb20b74-2fb8-4e24-b3ef-2f7147bac6e4</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>101</OID>
                      <Parent>97</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>54.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>3949bd56-207f-49c8-b07a-1ff404e1f588</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>102</OID>
                      <Parent>97</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>72.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>e4805003-155a-43e6-9f5d-84241ecbdfb9</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>145 px</Width>
                  <Height>91 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>91 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>103</OID>
                  <Parent>96</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>104</OID>
                      <Parent>103</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>c2517d54-65e3-4b73-a45b-7a1e7731a7e4</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>105</OID>
                      <Parent>103</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>18 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>e6239402-81a2-438a-9b9c-3d04d8895149</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>106</OID>
                      <Parent>103</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>36 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>2e56dee6-b57a-4fbe-bae2-29abdf289335</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>107</OID>
                      <Parent>103</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>54 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>69ef6bf4-fc83-4a1b-be8c-77a534d2fb5c</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>108</OID>
                      <Parent>103</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>72 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>ff0bb76c-317c-46b5-90ea-2fdcd7ff4aa8</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>109</OID>
                      <Parent>103</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>90 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>4270e9c6-2148-4d96-9aa4-967631ef006a</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>90 px</Y>
                </Location>
                <Size>
                  <Width>145 px</Width>
                  <Height>127 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>127 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>16 px</X>
              <Y>280 px</Y>
            </Location>
            <Size>
              <Width>150 px</Width>
              <Height>266 px</Height>
            </Size>
            <MinSize>
              <Width>120 px</Width>
              <Height>266 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>266 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>a78c2177-a902-43af-8aaf-a1c3fa597f13</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>110</OID>
              <OutModel>1</OutModel>
              <InModel>96</InModel>
              <OutPort>111</OutPort>
              <InPort>112</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>112.5 px</X>
                  <Y>0 px</Y>
                </Location>
                <Anchors>Left Right</Anchors>
                <Align>Top</Align>
                <CustomProperties>
                  <OID>112</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>112.5 px</X>
                  <Y>0 px</Y>
                </Location>
                <Anchors>Left Right</Anchors>
                <Align>Bottom</Align>
                <CustomProperties>
                  <OID>111</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>c178b4b2-5970-470d-a254-18d2dd88eac7</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>113</OID>
              <OutModel>1</OutModel>
              <InModel>96</InModel>
              <OutPort>114</OutPort>
              <InPort>115</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>37.5 px</X>
                  <Y>0 px</Y>
                </Location>
                <Anchors>Left Right</Anchors>
                <Align>Top</Align>
                <CustomProperties>
                  <OID>115</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>37.5 px</X>
                  <Y>0 px</Y>
                </Location>
                <Anchors>Left Right</Anchors>
                <Align>Bottom</Align>
                <CustomProperties>
                  <OID>114</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>bc8d0f85-5925-46b4-b2d3-a21371a07404</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>116</OID>
              <OutModel>33</OutModel>
              <InModel>96</InModel>
              <OutPort>117</OutPort>
              <InPort>118</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>49 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Left</Align>
                <CustomProperties>
                  <OID>118</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>Undefined</X>
                  <Y>433 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Right</Align>
                <CustomProperties>
                  <OID>117</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>851aa752-fa69-4deb-af70-786170b981d9</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>119</OID>
              <OutModel>33</OutModel>
              <InModel>96</InModel>
              <OutPort>120</OutPort>
              <InPort>121</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>24.5 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Left</Align>
                <CustomProperties>
                  <OID>121</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>Undefined</X>
                  <Y>408.5 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Right</Align>
                <CustomProperties>
                  <OID>120</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>5579992b-3d7f-473d-8496-60d3a128aacf</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>122</OID>
              <OutModel>88</OutModel>
              <InModel>96</InModel>
              <OutPort>123</OutPort>
              <InPort>124</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>79 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Right</Align>
                <CustomProperties>
                  <OID>123</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>190 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Left</Align>
                <CustomProperties>
                  <OID>124</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <BendPoints>
              <PointD>
                <X>-22 px</X>
                <Y>559 px</Y>
              </PointD>
              <PointD>
                <X>-22 px</X>
                <Y>470 px</Y>
              </PointD>
            </BendPoints>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>8f1ce025-8df1-48a8-9fce-76b9832c7641</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>125</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>126</OID>
                  <Parent>125</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>127</OID>
                      <Parent>126</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>028a0c52-d813-4b06-8ee8-21303cb2eb0c</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>128</OID>
                      <Parent>126</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>a7717d46-291e-41ee-85ce-a09541263bec</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>145 px</Width>
                  <Height>37 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>37 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>129</OID>
                  <Parent>125</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>130</OID>
                      <Parent>129</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>7e0bc50c-5090-4572-84bd-630876f0fd13</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>131</OID>
                      <Parent>129</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>18 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>366fc6c3-797d-4e7e-8e52-9aeb0a0f30d4</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>132</OID>
                      <Parent>129</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>36 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>3d639dc7-645f-424a-be34-aafd6a23c2d9</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>36 px</Y>
                </Location>
                <Size>
                  <Width>145 px</Width>
                  <Height>73 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>73 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>240 px</X>
              <Y>352 px</Y>
            </Location>
            <Size>
              <Width>150 px</Width>
              <Height>158 px</Height>
            </Size>
            <MinSize>
              <Width>120 px</Width>
              <Height>158 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>158 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>f2db8166-5c03-41c4-8336-72187be66257</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>133</OID>
              <OutModel>125</OutModel>
              <InModel>96</InModel>
              <OutPort>134</OutPort>
              <InPort>135</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>79 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Left</Align>
                <CustomProperties>
                  <OID>134</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>142 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Right</Align>
                <CustomProperties>
                  <OID>135</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <BendPoints>
              <PointD>
                <X>220 px</X>
                <Y>431 px</Y>
              </PointD>
              <PointD>
                <X>220 px</X>
                <Y>422 px</Y>
              </PointD>
            </BendPoints>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>a5900f51-8980-4e60-86a4-696f5168e040</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>136</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>137</OID>
                  <Parent>136</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>138</OID>
                      <Parent>137</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>d71ab0f5-74e2-447e-bace-5b16aedee4ff</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>139</OID>
                      <Parent>137</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>d75d1dc4-6560-4821-b401-d6ac704e7a88</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>140</OID>
                      <Parent>137</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>f8ac91c9-5415-469b-92d7-8f3c32049d04</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>141</OID>
                      <Parent>137</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>54.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>3d4a5656-a4a8-472e-9288-acb347a10674</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>142</OID>
                      <Parent>137</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>72.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>73029d8c-f907-47b5-8e6b-b412dcf1035e</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>145 px</Width>
                  <Height>91 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>91 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>143</OID>
                  <Parent>136</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>144</OID>
                      <Parent>143</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>b6d06d38-2cb7-449f-9380-89f949c7bcfe</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>145</OID>
                      <Parent>143</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>18 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>b8b9c5ae-ec14-4392-a742-9319e3ac9dc9</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>146</OID>
                      <Parent>143</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>36 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>d297b922-bcef-4e1f-8faa-80fc4e9ed1d0</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>90 px</Y>
                </Location>
                <Size>
                  <Width>145 px</Width>
                  <Height>73 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>73 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>240 px</X>
              <Y>584 px</Y>
            </Location>
            <Size>
              <Width>150 px</Width>
              <Height>212 px</Height>
            </Size>
            <MinSize>
              <Width>120 px</Width>
              <Height>212 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>212 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>2052c681-ef24-4cdb-9baf-2f0e1bd63222</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>147</OID>
              <OutModel>1</OutModel>
              <InModel>136</InModel>
              <OutPort>148</OutPort>
              <InPort>149</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>106 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Left</Align>
                <CustomProperties>
                  <OID>149</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>131.25 px</X>
                  <Y>0 px</Y>
                </Location>
                <Anchors>Left Right</Anchors>
                <Align>Bottom</Align>
                <CustomProperties>
                  <OID>148</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <BendPoints>
              <PointD>
                <X>147.25 px</X>
                <Y>174 px</Y>
              </PointD>
              <PointD>
                <X>185 px</X>
                <Y>174 px</Y>
              </PointD>
              <PointD>
                <X>185 px</X>
                <Y>690 px</Y>
              </PointD>
            </BendPoints>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>73c59e8f-747e-48fb-8006-3e951d06ef2c</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>150</OID>
              <OutModel>33</OutModel>
              <InModel>136</InModel>
              <OutPort>151</OutPort>
              <InPort>152</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>37.5 px</X>
                  <Y>0 px</Y>
                </Location>
                <Anchors>Left Right</Anchors>
                <Align>Top</Align>
                <CustomProperties>
                  <OID>152</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>Undefined</X>
                  <Y>241 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Right</Align>
                <CustomProperties>
                  <OID>151</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <BendPoints>
              <PointD>
                <X>-22 px</X>
                <Y>137 px</Y>
              </PointD>
              <PointD>
                <X>-22 px</X>
                <Y>191.5 px</Y>
              </PointD>
              <PointD>
                <X>202.5 px</X>
                <Y>191.5 px</Y>
              </PointD>
              <PointD>
                <X>202.5 px</X>
                <Y>564 px</Y>
              </PointD>
              <PointD>
                <X>277.5 px</X>
                <Y>564 px</Y>
              </PointD>
            </BendPoints>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>b37d19ca-e673-415b-9520-dfb4ef0d9f28</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>153</OID>
              <OutModel>125</OutModel>
              <InModel>136</InModel>
              <OutPort>154</OutPort>
              <InPort>155</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>112.5 px</X>
                  <Y>0 px</Y>
                </Location>
                <Anchors>Left Right</Anchors>
                <Align>Bottom</Align>
                <CustomProperties>
                  <OID>154</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>112.5 px</X>
                  <Y>0 px</Y>
                </Location>
                <Anchors>Left Right</Anchors>
                <Align>Top</Align>
                <CustomProperties>
                  <OID>155</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>aa10c076-8621-40a3-9baa-c28039d61f06</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>156</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>157</OID>
                  <Parent>156</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>158</OID>
                      <Parent>157</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>e1ce2fb9-298d-4684-b854-6986f70376cb</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>159</OID>
                      <Parent>157</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>4eaf50a6-bdee-432c-bfbc-067725812376</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>160</OID>
                      <Parent>157</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>85ba3ec0-faaa-4114-bd4e-70d828bee22e</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>145 px</Width>
                  <Height>55 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>55 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>161</OID>
                  <Parent>156</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>162</OID>
                      <Parent>161</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>cc7f3b5d-ba22-438a-bdf6-6556130cfb07</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>54 px</Y>
                </Location>
                <Size>
                  <Width>145 px</Width>
                  <Height>37 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>37 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>432 px</X>
              <Y>-112 px</Y>
            </Location>
            <Size>
              <Width>150 px</Width>
              <Height>140 px</Height>
            </Size>
            <MinSize>
              <Width>120 px</Width>
              <Height>140 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>140 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>9a43cc70-ddba-477b-aade-88df5765d085</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>163</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>164</OID>
                  <Parent>163</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>165</OID>
                      <Parent>164</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>bb047266-edd9-4e15-b971-076e18cc24a9</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>166</OID>
                      <Parent>164</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>9fcb0603-4899-4e40-8f35-ec6540b3a176</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>167</OID>
                      <Parent>164</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>4f4b74fc-27a6-42a8-b2b9-258b0468924e</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>145 px</Width>
                  <Height>55 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>55 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>168</OID>
                  <Parent>163</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>169</OID>
                      <Parent>168</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>dacbae83-b849-45b2-a6a2-6c265cdd7c1a</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>170</OID>
                      <Parent>168</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>18 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>7a5c9e4c-1338-4a8e-bba4-6f7afddf6335</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>54 px</Y>
                </Location>
                <Size>
                  <Width>145 px</Width>
                  <Height>55 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>55 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>440 px</X>
              <Y>160 px</Y>
            </Location>
            <Size>
              <Width>150 px</Width>
              <Height>158 px</Height>
            </Size>
            <MinSize>
              <Width>120 px</Width>
              <Height>158 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>158 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>7506e3c9-237f-49c8-bda8-5ff9d52f890e</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>171</OID>
              <OutModel>156</OutModel>
              <InModel>163</InModel>
              <OutPort>172</OutPort>
              <InPort>173</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>79 px</X>
                  <Y>0 px</Y>
                </Location>
                <Anchors>Left Right</Anchors>
                <Align>Bottom</Align>
                <CustomProperties>
                  <OID>172</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>71 px</X>
                  <Y>0 px</Y>
                </Location>
                <Anchors>Left Right</Anchors>
                <Align>Top</Align>
                <CustomProperties>
                  <OID>173</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>9632b82e-f6e4-47d9-966e-91ca2c43b2cf</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>174</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>175</OID>
                  <Parent>174</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>176</OID>
                      <Parent>175</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>bd6c1110-4b81-4ae4-9fb8-416d1f763ed5</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>177</OID>
                      <Parent>175</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>0cce4f14-74e5-4467-b114-924064c89c39</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>178</OID>
                      <Parent>175</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>dd8bba68-467b-4bbb-a8b8-3553d6009f95</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>145 px</Width>
                  <Height>55 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>55 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>179</OID>
                  <Parent>174</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>180</OID>
                      <Parent>179</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>424aafae-0216-41e5-9591-4bd66ce13c03</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>181</OID>
                      <Parent>179</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>18 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>a3a842a1-886d-44d6-9851-db94b41b8a38</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>54 px</Y>
                </Location>
                <Size>
                  <Width>145 px</Width>
                  <Height>55 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>55 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>448 px</X>
              <Y>408 px</Y>
            </Location>
            <Size>
              <Width>150 px</Width>
              <Height>158 px</Height>
            </Size>
            <MinSize>
              <Width>120 px</Width>
              <Height>158 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>158 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>111619c6-bc2c-4a51-9253-75f68082a318</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>182</OID>
              <OutModel>163</OutModel>
              <InModel>174</InModel>
              <OutPort>183</OutPort>
              <InPort>184</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>79 px</X>
                  <Y>0 px</Y>
                </Location>
                <Anchors>Left Right</Anchors>
                <Align>Bottom</Align>
                <CustomProperties>
                  <OID>183</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>71 px</X>
                  <Y>0 px</Y>
                </Location>
                <Anchors>Left Right</Anchors>
                <Align>Top</Align>
                <CustomProperties>
                  <OID>184</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>a896eb32-9633-405c-aaa7-1a82cee5c128</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>185</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>186</OID>
                  <Parent>185</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>187</OID>
                      <Parent>186</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>19109a03-4843-4589-9c2c-bae05af208bb</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>145 px</Width>
                  <Height>19 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>19 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>188</OID>
                  <Parent>185</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>189</OID>
                      <Parent>188</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>7fa8026d-86ea-4ba4-a08b-49709b8ead6b</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>190</OID>
                      <Parent>188</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>18 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>8da56a7e-27f7-4a58-91d7-8a814a826c92</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>191</OID>
                      <Parent>188</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>36 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>ef00e8fb-28dd-4bdb-9caa-c03adae007f3</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>192</OID>
                      <Parent>188</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>54 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>5a47af77-7639-414b-b046-d15a41727ce2</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>18 px</Y>
                </Location>
                <Size>
                  <Width>145 px</Width>
                  <Height>91 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>91 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>168 px</X>
              <Y>944 px</Y>
            </Location>
            <Size>
              <Width>150 px</Width>
              <Height>158 px</Height>
            </Size>
            <MinSize>
              <Width>100 px</Width>
              <Height>158 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>158 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>d8bdae29-3a3d-4dcd-a025-c770a0f497ca</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>193</OID>
              <OutModel>88</OutModel>
              <InModel>185</InModel>
              <OutPort>194</OutPort>
              <InPort>195</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>118.5 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Right</Align>
                <CustomProperties>
                  <OID>194</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>75 px</X>
                  <Y>0 px</Y>
                </Location>
                <Anchors>Left Right</Anchors>
                <Align>Top</Align>
                <CustomProperties>
                  <OID>195</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <BendPoints>
              <PointD>
                <X>-22 px</X>
                <Y>598.5 px</Y>
              </PointD>
              <PointD>
                <X>-22 px</X>
                <Y>572 px</Y>
              </PointD>
              <PointD>
                <X>243 px</X>
                <Y>572 px</Y>
              </PointD>
            </BendPoints>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>2c7fe95e-5eb6-4f2f-82b3-1413eb349014</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>196</OID>
              <OutModel>125</OutModel>
              <InModel>185</InModel>
              <OutPort>197</OutPort>
              <InPort>198</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>118.5 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Left</Align>
                <CustomProperties>
                  <OID>197</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>112.5 px</X>
                  <Y>0 px</Y>
                </Location>
                <Anchors>Left Right</Anchors>
                <Align>Top</Align>
                <CustomProperties>
                  <OID>198</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <BendPoints>
              <PointD>
                <X>220 px</X>
                <Y>470.5 px</Y>
              </PointD>
              <PointD>
                <X>220 px</X>
                <Y>572 px</Y>
              </PointD>
              <PointD>
                <X>280.5 px</X>
                <Y>572 px</Y>
              </PointD>
            </BendPoints>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>e8853e0e-6b52-4611-b829-d1921ebb3798</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>199</OID>
              <OutModel>174</OutModel>
              <InModel>185</InModel>
              <OutPort>200</OutPort>
              <InPort>201</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>79 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Left</Align>
                <CustomProperties>
                  <OID>200</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>37.5 px</X>
                  <Y>0 px</Y>
                </Location>
                <Anchors>Left Right</Anchors>
                <Align>Top</Align>
                <CustomProperties>
                  <OID>201</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <BendPoints>
              <PointD>
                <X>428 px</X>
                <Y>487 px</Y>
              </PointD>
              <PointD>
                <X>428 px</X>
                <Y>554.5 px</Y>
              </PointD>
              <PointD>
                <X>205.5 px</X>
                <Y>554.5 px</Y>
              </PointD>
            </BendPoints>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>ae0af082-6f66-4cf9-beba-398f5e7b9a4c</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>202</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>203</OID>
                  <Parent>202</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>204</OID>
                      <Parent>203</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>250 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>61967e45-eef2-4c39-a498-f8a457c97abc</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>205</OID>
                      <Parent>203</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>250 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>8e5f0919-1423-4059-b3cd-13a1907837a7</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>206</OID>
                      <Parent>203</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>250 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>79cba190-eb66-413e-a091-4a287349fe4e</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>207</OID>
                      <Parent>203</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>54.5 px</Y>
                    </Location>
                    <Size>
                      <Width>250 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>90225ab1-ec71-452f-b130-5d9a67d800f5</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>208</OID>
                      <Parent>203</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>72.5 px</Y>
                    </Location>
                    <Size>
                      <Width>250 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>f0e03275-7a68-41f1-992d-1dc5438ec681</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>209</OID>
                      <Parent>203</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>90.5 px</Y>
                    </Location>
                    <Size>
                      <Width>250 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>ce725e32-64d9-425c-b19c-70a14a63d846</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>251 px</Width>
                  <Height>109 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>109 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>210</OID>
                  <Parent>202</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>211</OID>
                      <Parent>210</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>250 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>2839399d-333f-46e2-97e2-91c2e23feb34</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>212</OID>
                      <Parent>210</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>18 px</Y>
                    </Location>
                    <Size>
                      <Width>250 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>6c2a1e4f-3cf6-46e5-9258-bfb1aceb3e8b</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>213</OID>
                      <Parent>210</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>36 px</Y>
                    </Location>
                    <Size>
                      <Width>250 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>e2e0cf53-981b-483b-ac2e-cacbb00a52ab</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>214</OID>
                      <Parent>210</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>54 px</Y>
                    </Location>
                    <Size>
                      <Width>250 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>03bb8955-ccb3-4cc5-be7f-70fce55a629f</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>215</OID>
                      <Parent>210</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>72 px</Y>
                    </Location>
                    <Size>
                      <Width>250 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>769f0ed5-17d0-4895-9937-3c19b600d61e</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>216</OID>
                      <Parent>210</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>90 px</Y>
                    </Location>
                    <Size>
                      <Width>250 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>8f603b6b-7b2e-4a4b-b286-27f1a819352f</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>217</OID>
                      <Parent>210</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>108 px</Y>
                    </Location>
                    <Size>
                      <Width>250 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>e15762e7-2dee-4790-8661-a2e3d1bf8ea2</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>218</OID>
                      <Parent>210</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>126 px</Y>
                    </Location>
                    <Size>
                      <Width>250 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>215285f9-dd84-4f4f-9f9d-67a31bafc1fa</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>219</OID>
                      <Parent>210</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>144 px</Y>
                    </Location>
                    <Size>
                      <Width>250 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>204962e9-c757-42b7-a06f-cc32d3cf16aa</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>220</OID>
                      <Parent>210</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>162 px</Y>
                    </Location>
                    <Size>
                      <Width>250 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>10ebc08f-3bb3-435b-a76e-7aba0b0a65f2</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>221</OID>
                      <Parent>210</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>180 px</Y>
                    </Location>
                    <Size>
                      <Width>250 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>defa9498-9d59-4b2a-8960-472940b280ff</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>222</OID>
                      <Parent>210</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>198 px</Y>
                    </Location>
                    <Size>
                      <Width>250 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>eef1815e-d6f3-46f7-bf2c-9675de2113ba</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>223</OID>
                      <Parent>210</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>216 px</Y>
                    </Location>
                    <Size>
                      <Width>250 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>d322106e-bd06-4f73-9636-e61cbf999ebc</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>224</OID>
                      <Parent>210</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>234 px</Y>
                    </Location>
                    <Size>
                      <Width>250 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>569044be-2316-4f33-8da8-18332b8bc463</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>225</OID>
                      <Parent>210</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>252 px</Y>
                    </Location>
                    <Size>
                      <Width>250 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>42825e92-905b-4f7b-a615-cac733fe2acb</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>226</OID>
                      <Parent>210</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>270 px</Y>
                    </Location>
                    <Size>
                      <Width>250 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>9f8c8451-9f76-4d77-bdad-91a1e78c5574</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>227</OID>
                      <Parent>210</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>288 px</Y>
                    </Location>
                    <Size>
                      <Width>250 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>45da5807-ef59-4ff4-b24b-286a1efa3899</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>228</OID>
                      <Parent>210</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>306 px</Y>
                    </Location>
                    <Size>
                      <Width>250 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>03c32bfa-e740-4e75-92ba-587595cd2389</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>229</OID>
                      <Parent>210</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>324 px</Y>
                    </Location>
                    <Size>
                      <Width>250 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>241b452a-18f9-4d96-a2d7-494783c4fc4b</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>230</OID>
                      <Parent>210</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>342 px</Y>
                    </Location>
                    <Size>
                      <Width>250 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>ff862d42-f9e2-4f31-bea2-e8711e00237b</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>231</OID>
                      <Parent>210</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>360 px</Y>
                    </Location>
                    <Size>
                      <Width>250 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>bafac98a-648f-46b0-aaad-1ea214659370</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>232</OID>
                      <Parent>210</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>378 px</Y>
                    </Location>
                    <Size>
                      <Width>250 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>a6f6e6cb-aad8-40ae-8dfe-fcc0562988c7</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>233</OID>
                      <Parent>210</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>396 px</Y>
                    </Location>
                    <Size>
                      <Width>250 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>4d474501-cf55-494d-8132-b94e0239ac34</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>234</OID>
                      <Parent>210</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>414 px</Y>
                    </Location>
                    <Size>
                      <Width>250 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>5445e20c-2b64-41db-a655-0ae498038f6c</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>235</OID>
                      <Parent>210</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>432 px</Y>
                    </Location>
                    <Size>
                      <Width>250 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>7e75901c-29a8-44c0-939c-75b5762f35b6</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>108 px</Y>
                </Location>
                <Size>
                  <Width>251 px</Width>
                  <Height>469 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>469 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>-176 px</X>
              <Y>648 px</Y>
            </Location>
            <Size>
              <Width>256 px</Width>
              <Height>626 px</Height>
            </Size>
            <MinSize>
              <Width>100 px</Width>
              <Height>626 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>626 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>92dfb580-b9ce-4bfe-a178-e3af3d6d27b9</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>236</OID>
              <OutModel>185</OutModel>
              <InModel>202</InModel>
              <OutPort>237</OutPort>
              <InPort>238</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>79 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Left</Align>
                <CustomProperties>
                  <OID>237</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>191 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Right</Align>
                <CustomProperties>
                  <OID>238</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <BendPoints>
              <PointD>
                <X>148 px</X>
                <Y>1023 px</Y>
              </PointD>
              <PointD>
                <X>148 px</X>
                <Y>839 px</Y>
              </PointD>
            </BendPoints>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>10b1b5d3-f80d-4eac-848e-408b4dc0c599</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
        </Children>
        <GridSize>8 px</GridSize>
        <ViewPort>
          <ScaleMode>Free</ScaleMode>
          <Scale>1</Scale>
          <Location>
            <X>-209 px</X>
            <Y>743 px</Y>
          </Location>
        </ViewPort>
        <Oid xsi:type="SchemaModelOID">
          <Path>9cf690cb-e5be-4b9e-9cd9-51471560c52c</Path>
          <TypeName>EntityDeveloper.NHibernate.HibernateContextModel</TypeName>
        </Oid>
      </Model>
    </DiagramModel>
  </Diagram>
  <DiagramOptions Version="v2.0">
    <Options xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xsi:type="PageOptions">
      <TopLeftMargins>
        <Width>39.3700787401575 in/100</Width>
        <Height>39.3700787401575 in/100</Height>
      </TopLeftMargins>
      <BottomRightMargins>
        <Width>39.3700787401575 in/100</Width>
        <Height>39.3700787401575 in/100</Height>
      </BottomRightMargins>
      <PaperSize>
        <Width>827 in/100</Width>
        <Height>1169 in/100</Height>
      </PaperSize>
    </Options>
    <Options xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xsi:type="PrintOptions" />
    <Options xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xsi:type="ViewOptions">
      <ShadowOffset>
        <X>4 px</X>
        <Y>4 px</Y>
      </ShadowOffset>
      <CustomProperties />
    </Options>
    <EdDiagramOptions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
      <CustomProperties />
    </EdDiagramOptions>
  </DiagramOptions>
</EntityDeveloperDiagram>