using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Anete.Utils.Collections
{
    /// <summary>
    /// Jednotny pristup k PendingChangeSet.
    /// </summary>
    public interface IPendingChangeSet
    {
        /// <summary>
        /// Vraci true, pokud existuji nejake neulozene zmeny
        /// </summary>
        bool DataChanged();

        /// <summary>
        /// Vynuluje vsechny zmeny
        /// </summary>
        void Clear();

        IEnumerable ItemsToAdd { get; }
        IEnumerable ItemsToDelete { get; }
        IEnumerable ItemsToUpdate { get; }

		/// <summary>
		/// Typ polozek, ktere jsou v ChangeSetu ulozeny
		/// </summary>
		Type ItemType { get; }
    }
}
