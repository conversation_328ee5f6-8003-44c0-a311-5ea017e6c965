using System;
using System.Collections.Generic;
using System.Text;

namespace Anete.Utils.ComponentModel
{
    /// <summary>
    /// Rozhrani pro tridu, ktera musi byt inicializovana
    /// </summary>
    public interface ICheckIsInitialized: IIsInitialized
    {
        /// <summary>
        /// Zkontroluje, zda byl objekt inicializovan. Pokud ne, vyvola vyjimku
        /// </summary>
        /// <exception cref="InvalidOperationException">Pokud nebyl objekt inicializovan.</exception>
        void CheckIsInitialized();

    }
}
