using NHibernate;
using System;
using System.Collections.Generic;
using System.Linq;
using Anete.Utils;
using Anete.Utils.Patterns;

namespace Anete.Data.Nh.Services
{
	/// <summary>
	/// Jednoduchy session provider propojeny s nemennou session.
	/// </summary>
	public class CustomSessionProvider : ISessionProvider
	{
		/// <summary>
		/// Initializes a new instance of the StaticSessionProvider class.
		/// </summary>
		public CustomSessionProvider(ISession session)
		{
			_session = session;
		}

		#region ISessionProvider Members
		private readonly ISession _session;
		public ISession Session
		{
			get { return _session; }
		}

		public object SessionLock
		{
			get { throw new NotImplementedException(); }
		}

		#endregion
	}
}
