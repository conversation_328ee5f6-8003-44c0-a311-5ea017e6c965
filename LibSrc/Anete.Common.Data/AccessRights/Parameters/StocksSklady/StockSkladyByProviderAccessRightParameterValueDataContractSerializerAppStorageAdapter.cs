using Anete.Common.Data.Interface.Business.AccessRights.Services;
using Anete.Common.Data.Interface.Entities;
using System;
using System.Linq;

namespace Anete.Common.Data.AccessRights.Parameters.StocksSklady
{
	public class StockSkladyByProviderAccessRightParameterValueDataContractSerializerAppStorageAdapter : AccessRightParameterValueDataContractSerializerAppStorageAdapter<StockSklady, StockSkladyByProviderAccessRightParameterValueSerializableData>
	{
		public StockSkladyByProviderAccessRightParameterValueDataContractSerializerAppStorageAdapter(ICustomAccessRightParameterValueFactoryAggregator<StockSklady> factories)
			: base(factories)
		{

		}

	}
}