{"ProviderId": "Microsoft.VisualStudio.ConnectedService.Wcf", "Version": "15.0.40203.910", "GettingStartedDocument": {"Uri": "https://go.microsoft.com/fwlink/?linkid=858517"}, "ExtendedData": {"inputs": ["../../../../../../../Users/<USER>/Downloads/GP_webpay_WS_API_WSDL_V1/cws_v1.wsdl"], "collectionTypes": ["System.Array", "System.Collections.Generic.Dictionary`2"], "namespaceMappings": ["*, Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS"], "references": ["GoPay.net-sdk, {GoPay.NETCore, 1.0.2}", "Microsoft.AspNetCore.Authentication.Abstractions, {Microsoft.AspNetCore.Authentication.Abstractions, 2.0.3}", "Microsoft.AspNetCore.Authentication.Core, {Microsoft.AspNetCore.Authentication.Core, 2.0.3}", "Microsoft.AspNetCore.Authorization, {Microsoft.AspNetCore.Authorization, 2.0.4}", "Microsoft.AspNetCore.Authorization.Policy, {Microsoft.AspNetCore.Authorization.Policy, 2.0.4}", "Microsoft.AspNetCore.Hosting.Abstractions, {Microsoft.AspNetCore.Hosting.Abstractions, 2.0.3}", "Microsoft.AspNetCore.Hosting.Server.Abstractions, {Microsoft.AspNetCore.Hosting.Server.Abstractions, 2.0.3}", "Microsoft.AspNetCore.Http, {Microsoft.AspNetCore.Http, 2.0.3}", "Microsoft.AspNetCore.Http.Abstractions, {Microsoft.AspNetCore.Http.Abstractions, 2.0.3}", "Microsoft.AspNetCore.Http.Extensions, {Microsoft.AspNetCore.Http.Extensions, 2.0.3}", "Microsoft.AspNetCore.Http.Features, {Microsoft.AspNetCore.Http.Features, 2.0.3}", "Microsoft.AspNetCore.Mvc.Abstractions, {Microsoft.AspNetCore.Mvc.Abstractions, 2.0.4}", "Microsoft.AspNetCore.Mvc.Core, {Microsoft.AspNetCore.Mvc.Core, 2.0.4}", "Microsoft.AspNetCore.ResponseCaching.Abstractions, {Microsoft.AspNetCore.ResponseCaching.Abstractions, 2.0.3}", "Microsoft.AspNetCore.Routing, {Microsoft.AspNetCore.Routing, 2.0.3}", "Microsoft.AspNetCore.Routing.Abstractions, {Microsoft.AspNetCore.Routing.Abstractions, 2.0.3}", "Microsoft.AspNetCore.WebUtilities, {Microsoft.AspNetCore.WebUtilities, 2.0.3}", "Microsoft.DotNet.PlatformAbstractions, {Microsoft.DotNet.PlatformAbstractions, 2.0.3}", "Microsoft.Extensions.Configuration.Abstractions, {Microsoft.Extensions.Configuration.Abstractions, 2.0.2}", "Microsoft.Extensions.DependencyInjection.Abstractions, {Microsoft.Extensions.DependencyInjection.Abstractions, 2.0.0}", "Microsoft.Extensions.DependencyModel, {Microsoft.Extensions.DependencyModel, 2.0.3}", "Microsoft.Extensions.FileProviders.Abstractions, {Microsoft.Extensions.FileProviders.Abstractions, 2.0.1}", "Microsoft.Extensions.Hosting.Abstractions, {Microsoft.Extensions.Hosting.Abstractions, 2.0.3}", "Microsoft.Extensions.Logging.Abstractions, {Microsoft.Extensions.Logging.Abstractions, 2.0.2}", "Microsoft.Extensions.ObjectPool, {Microsoft.Extensions.ObjectPool, 2.0.0}", "Microsoft.Extensions.Options, {Microsoft.Extensions.Options, 2.0.2}", "Microsoft.Extensions.Primitives, {Microsoft.Extensions.Primitives, 2.0.0}", "Microsoft.Net.Http.Headers, {Microsoft.Net.Http.Headers, 2.0.3}", "Microsoft.Practices.EnterpriseLibrary.Common, {EnterpriseLibrary.Common.NetCore, 6.1.15-rc}", "Microsoft.Practices.EnterpriseLibrary.Validation, {EnterpriseLibrary.Validation.NetCore, 6.1.4-rc}", "Microsoft.Win32.Registry, {Microsoft.Win32.Registry, 4.5.0}", "Newtonsoft.Json, {Newtonsoft.J<PERSON>, 12.0.1}", "RestSharp, {RestSharp, 106.5.4}", "System.AppContext, {System.AppContext, 4.1.0}", "<PERSON><PERSON>Buff<PERSON>, {<PERSON><PERSON>Buffers, 4.4.0}", "System.Collections, {System.Collections, 4.3.0}", "System.ComponentModel.Annotations, {System.ComponentModel.Annotations, 4.5.0}", "System.Configuration.ConfigurationManager, {System.Configuration.ConfigurationManager, 4.5.0}", "System.Diagnostics.Debug, {System.Diagnostics.Debug, 4.3.0}", "System.Diagnostics.DiagnosticSource, {System.Diagnostics.DiagnosticSource, 4.4.1}", "System.Diagnostics.Tracing, {System.Diagnostics.Tracing, 4.3.0}", "System.Dynamic.Runtime, {System.Dynamic.Runtime, 4.0.11}", "System.IO, {System.IO, 4.3.0}", "System.IO.FileSystem, {System.IO.FileSystem, 4.0.1}", "System.IO.FileSystem.Primitives, {System.IO.FileSystem.Primitives, 4.0.1}", "<PERSON><PERSON>Lin<PERSON>, {System.Linq, 4.1.0}", "System.Linq.Expressions, {System.Linq.Expressions, 4.1.0}", "System.ObjectModel, {System.ObjectModel, 4.0.12}", "System.Reflection, {System.Reflection, 4.3.0}", "System.Reflection.Emit, {System.Reflection.Emit, 4.3.0}", "System.Reflection.Emit.ILGeneration, {System.Reflection.Emit.ILGeneration, 4.3.0}", "System.Reflection.Primitives, {System.Reflection.Primitives, 4.3.0}", "System.Reflection.TypeExtensions, {System.Reflection.TypeExtensions, 4.1.0}", "System.Runtime, {System.Runtime, 4.3.0}", "System.Runtime.CompilerServices.Unsafe, {System.Runtime.CompilerServices.Unsafe, 4.5.0}", "System.Runtime.Extensions, {System.Runtime.Extensions, 4.3.0}", "System.Runtime.Handles, {System.Runtime.Handles, 4.0.1}", "System.Runtime.InteropServices, {System.Runtime.InteropServices, 4.1.0}", "System.Runtime.InteropServices.RuntimeInformation, {System.Runtime.InteropServices.RuntimeInformation, 4.0.0}", "System.Security.AccessControl, {System.Security.AccessControl, 4.5.0}", "System.Security.Permissions, {System.Security.Permissions, 4.5.0}", "System.Security.Principal.Windows, {System.Security.Principal.Windows, 4.5.0}", "System.Text.Encoding, {System.Text.Encoding, 4.3.0}", "System.Text.Encodings.Web, {System.Text.Encodings.Web, 4.4.0}", "System.Threading, {System.Threading, 4.3.0}", "System.Threading.Tasks, {System.Threading.Tasks, 4.3.0}", "Unity.Abstractions, {Unity.Abstractions, 3.3.1}", "Unity.Interception, {Unity.Interception, 5.5.6}"], "sync": true, "targetFramework": "netstandard2.0", "typeReuseMode": "All"}}