using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using Anete.Common.Data.Interface.AppServices;
using Anete.Log4Net.Core;
using Anete.Data;
using System.Data;
using Anete.Common.Data.Interface.Business.CashDeskOffice;
using System.Xml.Linq;
using Anete.Utils;
using Anete.Utils.Extensions;
using System.ComponentModel;
using Anete.Utils.ComponentModel;

namespace Anete.Common.Data.Business.IssueSlip.IssueSlipGen
{

	/// <summary>
	/// Service pro generovani vydejky
	/// </summary>
	public class IssueSlipGenService : IIssueSlipGenService
	{

		#region private fields...
		private static readonly ILogEx _log = LogManagerEx.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
		private readonly IKreditDbConnectionProvider _kreditDbConnectionProvider;
		private readonly ICashDeskSalesDeliveryService _cashDeskSalesDeliveryService;
		private readonly ILinkedServerSPService _linkedServerSPService;
		#endregion

		#region constructors...
		public IssueSlipGenService(IKreditDbConnectionProvider kreditDbConnectionProvider, ICashDeskSalesDeliveryService cashDeskSalesDeliveryService,
			ILinkedServerSPService linkedServerSPService)
		{
			_linkedServerSPService = linkedServerSPService;
			_cashDeskSalesDeliveryService = cashDeskSalesDeliveryService;
			_kreditDbConnectionProvider = kreditDbConnectionProvider;
		}
		#endregion

		#region IssueSlipGenService members...
		/// <summary>
		/// Nacteni cenovych kategorii
		/// </summary>
		/// <param name="kreditConn"></param>
		/// <returns></returns>
		public IEnumerable<ValueNameItem<short>> LoadPriceCategories(SqlConnection kreditConn)
		{
			using (SqlCommand command = kreditConn.CreateCommand())
			{
				command.CommandType = CommandType.Text;
				command.CommandText = "SELECT ID, Popis FROM [dba].[SCC_CenoveKategorie]";
				_log.Debug(command.GetLogText());

				using (SqlDataReader reader = command.ExecuteReader())
				{
					while (reader.Read())
					{
						yield return new ValueNameItem<short>(reader.CastDBValue<short>("ID"), reader.CastDBValue<string>("Popis"));
					}
				}
			}
		}

		/// <summary>
		/// Nacte dalsi idVydejka
		/// </summary>
		/// <param name="kreditTrans"></param>
		public short GetIdVydejka(IssueSlipSource issueSlipSource, SqlConnection kreditConn, SqlTransaction kreditTrans)
		{
			_log.Debug($"GetIdVydejka(issueSlipSource={issueSlipSource})");

			using (SqlCommand command = kreditConn.CreateCommand())
			{
				command.CommandType = CommandType.Text;
				command.Transaction = kreditTrans;
				if (issueSlipSource.AppInstallationId.HasValue)
				{
					command.CommandText =
	@"SELECT 
	ISNULL(
		(SELECT TOP 1 id_vydejka + 1 
			FROM dba.KAS_Vydejky 
		WHERE 
			id_zarizeni = @IdZarizeni
		ORDER BY 
			id_vydejka DESC), 
	1)";
					command.Parameters.Add("@IdZarizeni", SqlDbType.SmallInt).Value = issueSlipSource.AppInstallationId.Value;
				}
				else
				{
					command.CommandText =
	@"SELECT 
	ISNULL(
		(SELECT TOP 1 id_vydejka + 1 
			FROM dba.KAS_Vydejky 
		WHERE 
			id_vydejna = @IdVydejna
		ORDER BY 
			id_vydejka DESC), 
	1)";
					command.Parameters.Add("@IdVydejna", SqlDbType.SmallInt).Value = issueSlipSource.CanteenId.Value;
				}
				_log.Debug(command.GetLogText());
				short result = DbUtils.DbToShort(command.ExecuteScalar());
				_log.Debug($"GetIdVydejka() result={result}");
				return result;
			}
		}

		/// <summary>
		/// Vraci seznam datumu, pro ktere generovat vydejky. Vydejku je mozne generovat pouze pro jeden den, viz #2961.
		/// Pro kazdy datum se pak zavola generovani vydejky.
		/// </summary>
		/// <param name="kreditConn">The kredit conn.</param>
		/// <param name="forMeals">Pokud je true, zjistuje se pro jidla. Pokud false, pro zbozi</param>
		/// <returns></returns>
		public DateTime GetLastVydejkaDate(IssueSlipSource issueSlipSource, SqlConnection kreditConn, bool forMeals)
		{
			_log.Debug($"GetLastVydejkaDate(issueSlipSource={issueSlipSource}, forMeals={forMeals})");
			DateTime result = ExecGetLastVydejkaDate(issueSlipSource, forMeals);
			DateTime? prevAccountBalance = _cashDeskSalesDeliveryService.GetPrevAccountBalanceDate();

			// redmine #5440 Domluva s Peš: Někde se generují výdejky jídel a zboží, přitom se na Kase zboží nevydává. Při každém 
			// generování výdejek se pak pokouší vydat zboží od poslední výdejky, což může být i 2 roky zpětně. To děsně trvá. 
			// Proto upravit generování tak, aby když je datum poslední výdejky menší než datum předposlední uzávěrky Kredit, 
			// pak se jako počáteční datum vzal datum předposlední uzávěrky Kredit.
			if (prevAccountBalance != null)
			{
				// generuje se od dalsiho dne nez je uzaverka. Uzaverka byva zapsana jako 31.1.2016 00:00
				DateTime newVydejkaDate = prevAccountBalance.Value.Date.AddDays(1);

				if (result < newVydejkaDate)
				{
					result = newVydejkaDate;
				}
			}

			_log.Debug($"GetLastVydejkaDate(), result={result}");
			return result;
		}

		/// <summary>
		/// Vytvoreni zaznamu v Kas_Vydejky
		/// </summary>
		/// <param name="issueSlipId"></param>
		/// <param name="appInstallationId"></param>
		/// <param name="startDate"></param>
		/// <param name="endDate"></param>
		/// <param name="forMeals"></param>
		/// <param name="xmlResult"></param>
		/// <param name="conn"></param>
		/// <param name="kreditTrans"></param>
		public void CreateIssueSlip(short issueSlipId, IssueSlipSource issueSlipSource, short? deviceControllerId, DateTime startDate, DateTime endDate, bool forMeals, string xmlResult, SqlConnection conn, SqlTransaction kreditTrans)
		{
			using (SqlCommand command = conn.CreateCommand())
			{
				command.CommandType = CommandType.Text;
				command.Transaction = kreditTrans;
				command.CommandText =
@"INSERT INTO [dba].[KAS_Vydejky] ([id_vydejka], [id_zarizeni], [id_vydejna], [id_ctecka], [od], [do], [jidla], [vysledek]) 
VALUES 
	(@id_vydejka, @id_zarizeni, @id_vydejna, @id_ctecka, @od, @do, @jidla, @vysledek)";
				command.Parameters.Add("@id_vydejka", SqlDbType.SmallInt).Value = issueSlipId;
				command.Parameters.Add("@id_zarizeni", SqlDbType.SmallInt).Value = (object)issueSlipSource.AppInstallationId ?? DBNull.Value;
				command.Parameters.Add("@id_vydejna", SqlDbType.SmallInt).Value = (object)issueSlipSource.CanteenId ?? DBNull.Value;
				// v Db je not null protoze je soucasti primarniho klice
				command.Parameters.Add("@id_ctecka", SqlDbType.SmallInt).Value = deviceControllerId ?? 0;
				command.Parameters.Add("@od", SqlDbType.DateTime).Value = startDate;
				command.Parameters.Add("@do", SqlDbType.DateTime).Value = endDate;
				command.Parameters.Add("@jidla", SqlDbType.Bit).Value = forMeals;
				command.Parameters.Add("@vysledek", SqlDbType.Xml).Value = xmlResult;

				_log.Debug(command.GetLogText());
				command.ExecuteNonQuery();
			}
		}
		#endregion

		#region private methods...
		/// <summary>
		/// Z Items vytvori Xml string vhodny pro SP VytvorPohyby
		/// </summary>
		private static string CreateVytvorPohybyXmlData(IEnumerable<ZboziItem> items)
		{
			IEnumerable<XElement> resultItems = items
				.OrderBy(zi => zi.MovementType)
				.Select(zi =>
				{
					XElement result = new XElement("Z",
						GetZAttributes(zi).ToArray()
					);

					if (zi.MovementType == ZboziMovementType.Receptura)
					{
						result.Add(
							new XAttribute("MnozProdej", zi.MnozProdano ?? 0m),
							new XAttribute("MnozVydej", zi.MnozVydat)
						);
					}

					return result;
				});

			return resultItems.ToDelimitedString(Environment.NewLine);
		}

		private static IEnumerable<object> GetZAttributes(ZboziItem zi)
		{
			yield return new XAttribute("KodZbozi", zi.KodZbozi);
			yield return new XAttribute("IdSluzby", zi.IdSluzby);
			yield return new XAttribute("CenaKat", zi.CenaKat);
			yield return new XAttribute("Kod", (char)zi.MovementType);
			if (zi.FinZdroj != null)
			{
				yield return new XAttribute("FinZdroj", zi.FinZdroj);
			}
			if (zi.JidloDruh != null)
			{
				yield return new XAttribute("jidlo_druh", zi.JidloDruh);
			}

			foreach (BaleniItem bi in zi.BaleniItems.Where(bi => bi.BaleniDoVydejky != null && bi.BaleniDoVydejky != 0))
			{
				yield return new XElement("B",
					new XAttribute("BaleniId", bi.BaleniId),
					new XAttribute("MnozProdej", bi.BaleniDoVydejkyOriginal ?? 0m),
					new XAttribute("MnozVydej", bi.BaleniDoVydejky ?? 0m),
					new XAttribute("Kod", (char)bi.Parent.MovementType));
			}
		}

		/// <summary>
		/// Volani SP pro pripraveno pohybu
		/// </summary>
		/// <returns>Výsledky načtené ze SP</returns>
		public IEnumerable<ZboziItem> PrepareMovements(DateTime date, short kodSkladu, string xmlData, SqlConnectionLinkedServerConnection skladyConn, ZboziCalc zboziCalc,
			IEnumerable<ValueNameItem<short>> priceCategories, Func<MovementItem, string> findMovementName)
		{
			_log.DebugFormat($"ExecPripravPohyby(date={date:d}, kodSkladu={kodSkladu})");
			using (SqlCommand command = skladyConn.SqlConnection.CreateCommand())
			{
				command.CommandTimeout = 10 * TimeConst.SecondsInMinute;
				// Pozor: Parametry musi byt ve stejnem poradi, jako jsou deklarovane v SP. Pri volani pres linkovane servery jinak nefunguje
				command.Parameters.Add("@SKLAD_ID", SqlDbType.SmallInt).Value = kodSkladu;
				command.Parameters.Add("@DATUM_POHYBU", SqlDbType.DateTime).Value = date.Date;
				// zde je treba pouzit NVarChar. SP pouziva Xml typ, ale ten nejde predat na linkovane servery. Pokud se pouzije NVarChar,
				// zkonvertuje se pri volani spravne na Xml
				command.Parameters.Add("@x", SqlDbType.NVarChar, -1).Value = xmlData;
				command.CommandType = CommandType.Text;
				command.CommandText = _linkedServerSPService.GetCommandText("[dba].[KREDIT_KAS8_PripravPohyby]", command.Parameters,
					skladyConn.SqlCommandPrefix);

				_log.DebugFormat(command.GetLogText());

				string result = DbUtils.DbToString(command.ExecuteScalar());
				_log.Debug($"result: {result}");

				return ParsePripravPohybyResult(result, zboziCalc, priceCategories, findMovementName);
			}
		}

		/// <summary>
		/// Volani SP pro vytvoreni pohybu
		/// </summary>
		/// <returns>Výsledky načtené ze SP</returns>
		/// <param name="skladyTrans"></param>
		public VytvorPohybyResult VytvorPohyby(short kodSkladu, IssueSlipSource issueSlipSource, short idVydejka, DateTime date, bool forMeals,
			IEnumerable<ZboziItem> items, SqlConnectionLinkedServerConnection skladyConn, SqlTransaction skladyTrans)
		{
			string vytvorPohybyXmlData = CreateVytvorPohybyXmlData(items);
			return ExecVytvorPohyby(kodSkladu, issueSlipSource, idVydejka, date, forMeals, vytvorPohybyXmlData, skladyConn, skladyTrans);
		}
		#endregion

		#region private methods...
		/// <summary>
		/// Volani SP pro vytvoreni pohybu
		/// </summary>
		/// <returns>Výsledky načtené ze SP</returns>
		/// <param name="skladyTrans"></param>
		private VytvorPohybyResult ExecVytvorPohyby(short kodSkladu, IssueSlipSource issueSlipSource, short idVydejka, DateTime date,
			bool forMeals, string vytvorPohybyXmlData, SqlConnectionLinkedServerConnection skladyConn, SqlTransaction skladyTrans)
		{
			_log.Debug($"ExecVytvorPohyby(kodSkladu={kodSkladu}, issueSlipSource={issueSlipSource}, idVydejka={idVydejka}, date={date}, " +
				$"forMeals={forMeals}, vytvorPohybyXmlData={vytvorPohybyXmlData})");
			using (SqlCommand command = skladyConn.SqlConnection.CreateCommand())
			{
				command.CommandTimeout = 10 * TimeConst.SecondsInMinute;
				command.Transaction = skladyTrans;
				// Pozor: Parametry musi byt ve stejnem poradi, jako jsou deklarovane v SP. Pri volani pres linkovane servery jinak nefunguje
				command.Parameters.Add("@id_zarizeni", SqlDbType.SmallInt).Value = (object)issueSlipSource.AppInstallationId ?? DBNull.Value;
				command.Parameters.Add("@id_vydejna", SqlDbType.SmallInt).Value = (object)issueSlipSource.CanteenId ?? DBNull.Value;
				command.Parameters.Add("@id_v", SqlDbType.SmallInt).Value = idVydejka;
				command.Parameters.Add("@SKLAD_ID", SqlDbType.SmallInt).Value = kodSkladu;
				command.Parameters.Add("@DATUM_POHYBU", SqlDbType.DateTime).Value = date.Date;
				command.Parameters.Add("@jidla", SqlDbType.Bit).Value = forMeals;
				// zde je treba pouzit NVarChar. SP pouziva Xml typ, ale ten nejde predat na linkovane servery. Pokud se pouzije NVarChar,
				// zkonvertuje se pri volani spravne na Xml
				command.Parameters.Add("@x", SqlDbType.NVarChar, -1).Value = vytvorPohybyXmlData;
				command.CommandType = CommandType.Text;
				command.CommandText = _linkedServerSPService.GetCommandText("[dba].[KREDIT_KAS8_Vytvor_Pohyby]", command.Parameters,
					skladyConn.SqlCommandPrefix);

				_log.DebugFormat(command.GetLogText());
				string resultXml = DbUtils.DbToString(command.ExecuteScalar());
				_log.Debug($"ExecVytvorPohyby(), resultXml: {resultXml}");
				return new VytvorPohybyResult(resultXml);
			}
		}

		/// <summary>
		/// Nacte datum a cas posledni vydejky. Pokud zadna neni, vraci aktualni datum
		/// </summary>
		private DateTime ExecGetLastVydejkaDate(IssueSlipSource issueSlipSource, bool forMeals)
		{
			_log.Debug($"ExecGetLastVydejkaDate(issueSlipSource={issueSlipSource}, forMeals={forMeals})");

			using (SqlConnection sqlConn = _kreditDbConnectionProvider.CreateConnection())
			{
				sqlConn.Open();

				using (SqlCommand command = sqlConn.CreateCommand())
				{
					command.CommandType = CommandType.Text;

					// jidla muzu generovat maximalne pro vcerejsi den
					string defaultDateQuery = forMeals ? "dateadd(day,datediff(day,1,GETDATE()),0)" : "GETDATE()";

					if (issueSlipSource.CanteenId.HasValue)
					{
						command.CommandText = $"SELECT COALESCE((SELECT TOP 1 Do FROM dba.KAS_Vydejky WHERE id_vydejna = @idVydejna AND jidla = @proJidla ORDER BY id_vydejka DESC), {defaultDateQuery})";
						command.Parameters.Add("@idVydejna", SqlDbType.SmallInt).Value = issueSlipSource.CanteenId;
					}
					else
					{
						command.CommandText =
$@"SELECT COALESCE((SELECT TOP 1 Do FROM dba.KAS_Vydejky WHERE id_zarizeni = @idZarizeni AND jidla = @proJidla ORDER BY id_vydejka DESC), 
	(SELECT TOP 1 dba.date(tstamp) FROM dba.Paragony WHERE id_zarizeni = @idZarizeni ORDER BY tstamp ASC), {defaultDateQuery})";
						command.Parameters.Add("@idZarizeni", SqlDbType.SmallInt).Value = issueSlipSource.AppInstallationId;
					}

					command.Parameters.Add("proJidla", SqlDbType.Bit).Value = forMeals;

					_log.Debug(command.GetLogText());
					DateTime? result = DbUtils.DbToNullable<DateTime>(command.ExecuteScalar());
					_log.Debug($"ExecGetLastVydejkaDate() result={result}");
					return result.Value;
				}
			}
		}

		private IEnumerable<ZboziItem> ParsePripravPohybyResult(string pripravPohybyResult, ZboziCalc zboziCalc, IEnumerable<ValueNameItem<short>> priceCategories,
			Func<MovementItem, string> findMovementName)
		{
			XElement rootElement = XElement.Parse("<root>" + pripravPohybyResult + "</root>");
			IEnumerable<XElement> zboziElements = rootElement
				.Elements("P")
				.SelectMany(pElement => pElement.Elements("Z"));

			var zboziGroups = zboziElements
				.GroupBy(e =>
				{
					string kodChyby = (string)e.Element("E")?.Attribute("KOD");

					// pokud neni chyba u zbozi, jeste muze byt u baleni. 
					if (kodChyby.IsNullOrEmpty())
					{
						kodChyby = (string)e.Element("B")?.Element("E")?.Attribute("KOD");
					}

					return new
					{
						KodZbozi = (string)e.Attribute("KOD_ZBOZI"),
						TypPohybu = (ZboziMovementType)(char.Parse((string)e.Attribute("kod"))),
						KodChyby = kodChyby,
						// tyto dve hodnoty musi byt taky soucasti klice, protoze dle ViS nemuzu slucovat stejne zbozi s ruznymi IdSluzby a CenaKat
						IdSluzby = (int)e.Parent.Attribute("IdSluzby"),
						CenaKat = (int)e.Parent.Attribute("CenaKat"),
						// dle konzultace s ViS se nove musi groupovat tak dle FinZdroj a jidlo druh
						FinZdroj = (short?)e.Parent.Attribute("FinZdroj"),
						JidloDruh = (short?)e.Parent.Attribute("jidlo_druh")
					};
				});

			IEnumerable<ZboziItem> zboziItems = zboziGroups
				.Select(g =>
				{
					XElement firstElement = g.First();
					ZboziItem zboziItem = new ZboziItem(zboziCalc)
					{
						KodZbozi = g.Key.KodZbozi,
						MovementType = g.Key.TypPohybu,
						PopisZbozi = (string)firstElement.Attribute("NAZEV"),
						MerJedn = (string)firstElement.Attribute("MJ"),
						MnozProdano = g.Sum(e => (decimal?)e.Attribute("mnozstvi")),
						IdSluzby = g.Key.IdSluzby,
						CenaKat = g.Key.CenaKat,
						CenaKatNazev = priceCategories.Single(pc => pc.Value == g.Key.CenaKat).Name,
						ErrorCode = g.Key.KodChyby,
						JidloDruh = g.Key.JidloDruh,
						FinZdroj = g.Key.FinZdroj
					};

					// ke kazdemu zbozi seznam originalnich pohybu, ze kterych pochazi
					zboziItem.MovementItems = g.Select(e => ParseMovementItem(e, findMovementName)).ToList();

					// ke kazdemu zbozi seznam baleni
					IEnumerable<XElement> baleniElements = g.SelectMany(zElement => zElement.Elements("B"));
					zboziItem.BaleniItems = new BindingList<BaleniItem>(ParseBaleniItems(zboziItem, baleniElements).ToList());

					return zboziItem;
				});

			return zboziItems;
		}

		private MovementItem ParseMovementItem(XElement zboziElement, Func<MovementItem, string> findMovementName)
		{
			XElement parentElement = zboziElement.Parent;

			MovementItem movementItem = new MovementItem()
			{
				Id = (int)parentElement.Attribute("id"),
				JidloDruh = (short?)parentElement.Attribute("jidlo_druh"),
				JidloAlt = (short?)parentElement.Attribute("jidlo_alt"),
				IdJidelna = (short?)parentElement.Attribute("id_jidelna"),
				KodZbozi = (string)parentElement.Attribute("KOD_ZBOZI"),
				BaleniId = (int?)parentElement.Attribute("BALENI_ID"),
				RecepturaId = (int?)parentElement.Attribute("RECEPTURA_ID"),
				VariantaId = (int?)parentElement.Attribute("VARIANTA_ID"),
				Mnozstvi = (decimal)parentElement.Attribute("mnozstvi"),
				IdSluzby = (int)parentElement.Attribute("IdSluzby"),
				CenaKat = (int)parentElement.Attribute("CenaKat"),
				FinZdroj = (short?)parentElement.Attribute("FinZdroj")
			};

			movementItem.Popis = findMovementName(movementItem);
			return movementItem;
		}

		private static IEnumerable<BaleniItem> ParseBaleniItems(ZboziItem zboziItem, IEnumerable<XElement> baleniElements)
		{
			List<BaleniItem> baleniItems = new List<BaleniItem>();

			foreach (XElement baleniElement in baleniElements)
			{
				BaleniItem baleniItem = new BaleniItem()
				{
					BaleniId = (int)baleniElement.Attribute("BALENI_ID"),
					// oper: 36781
					// vyskytl se element, ktery nema zadne dalsi atributy. Stalo se to, kdyz vydali zbozi a pak ho smazali.
					// <Z KOD_ZBOZI="7513        " kod="Z">  
					//  < E KOD = "ErrUnknownVAT" />
					//  < B BALENI_ID = "1" mnozstvi = "3.000000" kod = "Z" >
					//	< E KOD = "NotInStock" />
					//  </ B >
					//</ Z >
					Popis = (string)baleniElement.Attribute("BALENI"),
					MnozstviVBaleni = (decimal?)baleniElement.Attribute("VEL_BALENI"),
					BaleniDoVydejky = (decimal?)baleniElement.Attribute("mnozstvi"),
					BaleniDoVydejkyOriginal = (decimal?)baleniElement.Attribute("mnozstvi"),
					// zasoba by sice vzdy mela byt obsazena, ale stalo se mi, ze nebyla. Proto osetruji takto
					MnozSklad = (decimal?)baleniElement.Attribute("zasoba") ?? 0,
					Delitelnost = (int?)baleniElement.Attribute("delitelnost"),
					ErrorCode = (string)baleniElement.Element("E")?.Attribute("KOD"),
					Parent = zboziItem
				};

				// pokud uz stejne baleni existuje, prictu mnozstvi, jinak vlozim do seznamu
				BaleniItem existingItem = baleniItems.FirstOrDefault(bi => bi.BaleniId == baleniItem.BaleniId &&
				  bi.ErrorCode == baleniItem.ErrorCode);
				if (existingItem != null)
				{
					existingItem.MnozDoVydejky += baleniItem.MnozDoVydejky;
					// Pridano ToJ, puvodne zde nebylo 
					// Mozna resi problem https://easyredmine.anete.com/issues/41731
					existingItem.BaleniDoVydejkyOriginal += baleniItem.BaleniDoVydejkyOriginal;
				}
				else
				{
					baleniItems.Add(baleniItem);
				}
			}

			return baleniItems;
		}
		#endregion

	}
}
