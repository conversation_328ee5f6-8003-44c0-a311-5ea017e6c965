// -----------------------------------------------------------------------------------
// Use it as you please, but keep this header.
// Author : <PERSON>, 2006
// Web    : www.yaowi.com
// Email  : <EMAIL>
// -----------------------------------------------------------------------------------
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.Serialization;
using System.Xml;
using Anete.Utils.AppServices.Resolvers;
using Anete.Utils.Extensions;
using Unity;
using Anete.Log.Core.Log4NetProxy;
using System.Collections.Concurrent;
using System.Xml.Linq;

namespace Anete.Utils.Xml.XmlSerializerEx
{

    /// <summary>
    /// Deserializes complex objects serialized with the XmlSerializer.
	/// 
	/// Puvodne implementovat IDisposable. Puvodni kod je v DisposeUnmanagedResources.
    /// </summary>
	public class XmlDeserializer : IXmlDeserializer
    {
#pragma warning disable 1591

        #region private static fields...
        
        private static readonly ILogEx _log = LogManagerEx.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        /// <summary>
        /// Cache pro ukladani metod volanych pri deserializaci - oznacenych atributem OnDeserializing a OnDeserialized
        /// </summary>
        private static readonly ConcurrentDictionary<Type, MethodInfo[]> _onDeserializingMethodsCache = new ConcurrentDictionary<Type, MethodInfo[]>();
        private static readonly ConcurrentDictionary<Type, MethodInfo[]> _onDeserializedMethodsCache = new ConcurrentDictionary<Type, MethodInfo[]>();
        #endregion

        #region private fields...
        /// <summary>
        /// Vytvarec typu
        /// </summary>
        private readonly ITypeCreator _typeCreator;
        
        /// <summary>
        /// Parsed Types
        /// </summary>
        private Hashtable _typeDictionary = new Hashtable();

        /// <summary>
        /// used TypeConverters
        /// </summary>
        private readonly ConcurrentDictionary<Type, TypeConverter> _typeConverterCache = new ConcurrentDictionary<Type, TypeConverter>();
        #endregion

        #region constructors...
        /// <summary>
        /// Vytvori novou instanci.
        /// Metody oznacene [OnDeserialized] a [OnDeserializing] jsou volany
        /// </summary>		
        public XmlDeserializer(ITypeCreator typeCreator)
            : this(true, typeCreator)
        {
        }

        /// <summary>
        /// Vytvori novou instanci
        /// </summary>
        /// <param name="callOnDeserializedMethods">Volat pri deserializaci metody oznacene [OnDeserialized] a [OnDeserializing]</param>
        /// <param name="typeCreator">The type creator.</param>
        protected XmlDeserializer(bool callOnDeserializedMethods, ITypeCreator typeCreator)
        {
            Guard.ArgumentNotNull(typeCreator, "typeCreator");

            _typeCreator = typeCreator;
            EnableDeserializationMethodsCall = callOnDeserializedMethods;
        }
        #endregion

        #region XmlDeserializer Properties
        private IXmlSerializationTag taglib = new XmlSerializationTag();
        /// <summary>
        /// Knihovna tagu
        /// </summary>
        public IXmlSerializationTag TagLib
        {
            get
            {
                return taglib;
            }
            set
            {
                taglib = value;
            }
        }

        /// <summary>
        /// Gets whether the current root node provides a type dictionary.
        /// </summary>
        protected bool HasTypeDictionary
        {
            get
            {
                if (_typeDictionary != null && _typeDictionary.Count > 0)
                {
                    return true;
                }

                return false;
            }
        }

        /// <summary>
        /// Gets or sets whether creation errors shall be ignored.
        /// Creation errors can occur if e.g. a type has no parameterless constructor
        /// and an instance cannot be instantiated from String.
        /// </summary>
        [Description("Gets or sets whether creation errors shall be ignored.")]
        public bool IgnoreCreationErrors { get; set; }

        /// <summary>
        /// Volat pri deserializaci metody oznacene [OnDeserialized] a [OnDeserializing]
        /// </summary>
        public bool EnableDeserializationMethodsCall { get; set; }
        #endregion XmlDeserializer Properties

        #region Deserialize
        /// <summary>
        /// Deserialzes an object from a xml file.
        /// </summary>
        /// <param name="filename">The filename.</param>
        /// <returns></returns>
        public object Deserialize(string filename)
        {
            return Deserialize(filename, null);
        }

        /// <summary>
        /// Deserialzes an object from a xml file.
        /// </summary>
        /// <param name="filename">The filename.</param>
        /// <param name="state">Objekt, ktery se pak predava metode OnDeserialized. Vhodne pro dodani dat, ktere potrebuji na deserializaci</param>
        /// <returns></returns>
        public object Deserialize(string filename, object state)
        {
            XmlDocument doc = new XmlDocument();
            doc.Load(filename);
            return Deserialize(doc, state);
        }

        /// <summary>
        /// Deserialzes an object from XmlDocument.
        /// </summary>
        /// <param name="document">The document.</param>
        /// <returns></returns>
        public object Deserialize(XmlDocument document)
        {
            return Deserialize(document, null);
        }

#if !FW_Ver2
        /// <summary>
        /// Deserialzes an object from XmlDocument.
        /// </summary>
        /// <param name="document">The document.</param>
        /// <returns></returns>
        public object Deserialize(XDocument document)
        {
            return Deserialize(document.ToXmlDocument(), null);
        }

        /// <summary>
        /// Deserialzes an object from XmlDocument.
        /// </summary>
        /// <param name="element">The document.</param>
        /// <returns></returns>
        public object Deserialize(XElement element)
        {
            return Deserialize(element.ToXmlDocument(), null); 
        }
#endif

        /// <summary>
        /// Deserialzes an object from XmlDocument.
        /// </summary>
        /// <param name="document">The document.</param>
        /// <param name="state">Objekt, ktery se pak predava metode OnDeserialized. Vhodne pro dodani dat, ktere potrebuji na deserializaci</param>
        /// <returns></returns>
        public object Deserialize(XmlDocument document, object state)
        {
            XmlNode node = document.SelectSingleNode(taglib.OBJECT_TAG);
            return Deserialize(node, state);
        }

        /// <summary>
        /// Deserializes an Object from the specified XmlNode.
        /// </summary>
        /// <param name="node">The node.</param>
        /// <returns></returns>
        public object Deserialize(XmlNode node)
        {
            return Deserialize(node, null);
        }

        /// <summary>
        /// Deserializes an Object from the specified XmlNode.
        /// </summary>
        /// <param name="node">The node.</param>
        /// <param name="state">Objekt, ktery se pak predava metode OnDeserialized. Vhodne pro dodani dat, ktere potrebuji na deserializaci</param>
        /// <returns></returns>
        public object Deserialize(XmlNode node, object state)
        {
            // Clear previous collections
            Reset();

            XmlNode rootnode = node;
            if (!rootnode.Name.Equals(taglib.OBJECT_TAG))
            {
                rootnode = node.SelectSingleNode(taglib.OBJECT_TAG);

                if (rootnode == null)
                {
                    throw new ArgumentException(String.Format("Invalid node. The specified node or its direct children do not contain a {0} tag.", taglib.OBJECT_TAG), "XmlNode node");
                }
            }

            // Load TypeDictionary
            _typeDictionary = ParseTypeDictionary(rootnode, state);

            // Get the Object
            object obj = GetObject(rootnode);

            if (obj != null)
            {
                if (EnableDeserializationMethodsCall)
                {
                    CallOnDeserializing(obj, state);
                }

                GetProperties(obj, rootnode, state);

                if (EnableDeserializationMethodsCall)
                {
                    CallOnDeserialized(obj, state);
                }
            }
            else
            {
                Console.WriteLine("Object is null");
            }

            return obj;
        }

        /// <summary>
        /// Parses the TypeDictionary (if given).
        /// </summary>
        /// <param name="parentNode">The parent node.</param>
        /// <param name="state">The state.</param>
        /// <returns></returns>
        /// <remarks>
        /// The TypeDictionary is Hashtable in which TypeInfo items are stored.
        /// </remarks>
        protected Hashtable ParseTypeDictionary(XmlNode parentNode, object state)
        {
            Hashtable dict = new Hashtable();

            XmlNode dictNode = parentNode.SelectSingleNode(taglib.TYPE_DICTIONARY_TAG);
            if (dictNode == null)
                return dict;

            object obj = GetObject(dictNode);

            if (obj != null && typeof(Hashtable).IsAssignableFrom(obj.GetType()))
            {
                dict = (Hashtable)obj;
                GetProperties(dict, dictNode, state);
            }

            return dict;
        }
        #endregion Deserialize

        #region Properties & values
        /// <summary>
        /// Reads the properties of the specified node and sets them an the parent object.
        /// </summary>
        /// <param name="parent">The parent.</param>
        /// <param name="node">The node.</param>
        /// <param name="state">Objekt, ktery se pak predava metode OnDeserialized. Vhodne pro dodani dat, ktere potrebuji na deserializaci</param>
        /// <remarks>
        /// This is the central method which is called recursivly!
        /// </remarks>
        protected void GetProperties(object parent, XmlNode node, object state)
        {
            if (parent == null)
            {
                return;
            }

            // Get the properties
            XmlNodeList nodeList = node.SelectNodes(taglib.PROPERTIES_TAG + "/" + taglib.PROPERTY_TAG);

            // Properties found?
            if (nodeList == null || nodeList.Count == 0)
            {
                // No properties found... perhaps a collection?
                if (TypeInfo.IsCollection(parent.GetType()))
                {
                    SetCollectionValues((ICollection)parent, node, state);
                }
                else
                {
                    // Nothing to do here
                    return;
                }
            }

            // Loop the properties found
            foreach (XmlNode prop in nodeList)
            {
                // Collect the nodes type information about the property to deserialize
                ObjectInfo objectInfo = GetObjectInfo(prop);

                // Instanci objektu vytvorim pouze v pripade, ze parent danou property obsahuje
                // Resi se tim problem, kdy jsou vytvareny instance jiz neexistujicich typu
                if (!parent.GetType().GetProperties().Any(propInfo => propInfo.Name == objectInfo.Name))
                {
                    // pokud property neexistuje, preskocim ji
                    _log.DebugFormat("Property {0} typu {1} se jiz ve tride {2} nenachazi. Nebudu ji deserializovat.",
                        objectInfo.Name, objectInfo.Type, parent.GetType());
                    continue;
                }

                // Enough info?
                if (objectInfo.IsSufficient && !String.IsNullOrEmpty(objectInfo.Name))
                {
                    object obj = null;

                    // Create an instance, but note: arrays always need the size for instantiation
                    if (TypeInfo.IsArray(objectInfo.Type))
                    {
                        int c = GetArrayLength(prop);
                        obj = CreateArrayInstance(objectInfo, c);
                    }
                    else
                    {
                        obj = CreateInstance(objectInfo);
                    }

                    // Process the property's properties (recursive call of this method)
                    if (obj != null)
                    {
                        // Kak: pred zacatkem nastavovani property objektu zavolame OnDeserializing metody
                        if (EnableDeserializationMethodsCall)
                        {
                            CallOnDeserializing(obj, state);
                        }

                        GetProperties(obj, prop, state);

                        // Kak: po konci nastavovani property objektu zavolame OnDeserialized metody
                        if (EnableDeserializationMethodsCall)
                        {
                            CallOnDeserialized(obj, state);
                        }
                    }

                    // Setting the instance (or null) as the property's value
                    PropertyInfo pi = parent.GetType().GetProperty(objectInfo.Name);
                    if (obj != null && pi != null)
                    {
                        DecryptValue(ref obj, pi);
                        pi.SetValue(parent, obj, null);
                    }

                }
            }

            return;
        }

        #region Collections

        /// <summary>
        /// Sets the entries on an ICollection implementation.
        /// </summary>
        /// <param name="coll">The coll.</param>
        /// <param name="parentNode">The parent node.</param>
        /// <param name="state">The state.</param>
        protected void SetCollectionValues(ICollection coll, XmlNode parentNode, object state)
        {
            if (typeof(IDictionary).IsAssignableFrom(coll.GetType()))
            {
                // IDictionary
                SetDictionaryValues((IDictionary)coll, parentNode, state);
                return;
            }
            else if (typeof(IList).IsAssignableFrom(coll.GetType()))
            {
                // IList
                SetListValues((IList)coll, parentNode, state);
                return;
            }
        }

        /// <summary>
        /// Sets the entries on an IList implementation.
        /// </summary>
        /// <param name="list">The list.</param>
        /// <param name="parentNode">The parent node.</param>
        /// <param name="state">The state.</param>
        protected void SetListValues(IList list, XmlNode parentNode, object state)
        {
            // Get the item nodes
            XmlNodeList nlitems = parentNode.SelectNodes(taglib.ITEMS_TAG + "/" + taglib.ITEM_TAG);

            // Loop them
            for (int i = 0; i < nlitems.Count; i++)
            {
                XmlNode nodeitem = nlitems[i];

                // Create an instance
                object obj = GetObject(nodeitem);

                if (EnableDeserializationMethodsCall)
                {
                    CallOnDeserializing(obj, state);
                }

                // Process the properties
                GetProperties(obj, nodeitem, state);

                if (EnableDeserializationMethodsCall)
                {
                    CallOnDeserialized(obj, state);
                }

                if (list.IsFixedSize)
                {
                    list[i] = obj;
                }
                else
                {
                    list.Add(obj);
                }
            }
        }

        /// <summary>
        /// Sets the entries of an IDictionary implementation.
        /// </summary>
        /// <param name="dictionary">The dictionary.</param>
        /// <param name="parentNode">The parent node.</param>
        /// <param name="state">The state.</param>
        protected void SetDictionaryValues(IDictionary dictionary, XmlNode parentNode, object state)
        {
            // Get the item nodes
            XmlNodeList nlitems = parentNode.SelectNodes(taglib.ITEMS_TAG + "/" + taglib.ITEM_TAG);

            // Loop them
            for (int i = 0; i < nlitems.Count; i++)
            {
                XmlNode nodeitem = nlitems[i];

                // Retrieve the single property
                string path = taglib.PROPERTIES_TAG + "/" + taglib.PROPERTY_TAG + "[@" + taglib.NAME_TAG + "='" + taglib.NAME_ATT_KEY_TAG + "']";
                XmlNode nodekey = nodeitem.SelectSingleNode(path);

                path = taglib.PROPERTIES_TAG + "/" + taglib.PROPERTY_TAG + "[@" + taglib.NAME_TAG + "='" + taglib.NAME_ATT_VALUE_TAG + "']";
                XmlNode nodeval = nodeitem.SelectSingleNode(path);

                // Create an instance of the key
                object objkey = GetObject(nodekey);
                object objval = null;

                // Try to get the value
                if (nodeval != null)
                {
                    objval = GetObject(nodeval);
                }

                // Set the entry if the key is not null
                if (objkey != null)
                {
                    // Set the entry's value if its is not null and process its properties
                    if (objval != null)
                    {
                        if (EnableDeserializationMethodsCall)
                        {
                            CallOnDeserializing(objval, state);
                        }

                        GetProperties(objval, nodeval, state);

                        if (EnableDeserializationMethodsCall)
                        {
                            CallOnDeserialized(objval, state);
                        }
                    }

                    dictionary.Add(objkey, objval);
                }
            }
        }
        #endregion Collections

        #endregion Properties & values

        #region public static methods...
        /// <summary>
        /// Zavola vsechny metody oznacene attributem OnDeserializing.
        /// </summary>
        /// <param name="obj">The obj.</param>
        /// <param name="state">Objekt, ktery se pak predava metode OnDeserialized. Vhodne pro dodani dat, ktere potrebuji na deserializaci</param>
        public static void CallOnDeserializing(object obj, object state)
        {
            CallDeserializationMethod(obj, typeof(OnDeserializingAttribute), _onDeserializingMethodsCache, state);
        }

        /// <summary>
        /// Zavola vsechny metody oznacene attributem OnDeserialized.
        /// </summary>
        /// <param name="obj">The obj.</param>
        /// <param name="state">Objekt, ktery se pak predava metode OnDeserialized. Vhodne pro dodani dat, ktere potrebuji na deserializaci</param>
        public static void CallOnDeserialized(object obj, object state)
        {
            CallDeserializationMethod(obj, typeof(OnDeserializedAttribute), _onDeserializedMethodsCache, state);
        }
        #endregion

        #region Creating instances and types

        /// <summary>
        /// Creates an instance by the contents of the given XmlNode.
        /// </summary>
        /// <param name="node"></param>
        protected object GetObject(XmlNode node)
        {
            ObjectInfo oi = GetObjectInfo(node);

            if (TypeInfo.IsArray(oi.Type))
            {
                int c = GetArrayLength(node);
                return CreateArrayInstance(oi, c);
            }

            return CreateInstance(oi);
        }

        /// <summary>
        /// Creates a type from the specified assembly and type names included in the TypeInfo parameter.
        /// In case of failure null will be returned.
        /// </summary>
        /// <param name="info"></param>
        /// <returns></returns>
        protected Type CreateType(TypeInfo info)
        {
            return CreateType(info.AssemblyName, info.TypeName);
        }


        /// <summary>
        /// Vytvoreni typu.
        /// Pokud se typ nepodari vytvorit, vyvolava vyjimky.
        /// </summary>
        /// <param name="assemblyName">Name of the assembly.</param>
        /// <param name="typeName">The type.</param>
        /// <returns></returns>
        protected Type CreateType(string assemblyName, string typeName)
        {
            return _typeCreator.Create(assemblyName, typeName);
        }

        /// <summary>
        /// Creates a type from the specified assembly and type names. 
        /// In case of failure null will be returned.
        /// </summary>
        /// <param name="assemblyName"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        protected Type TryCreateType(string assemblyName, string type)
        {
            Type result = null;

            try
            {
                result = CreateType(assemblyName, type);
            }
            catch (Exception ex)
            {
                if (!ExcUtils.IsCatchableExceptionType(ex))
                {
                    throw;
                }

                // ok, we did not get the Type - will return null
            }

            return result;
        }

        /// <summary>
        /// Creates an instance of an Array by the specified ObjectInfo.
        /// </summary>
        /// <param name="info"></param>
        /// <param name="length"></param>
        /// <returns></returns>
        private Array CreateArrayInstance(ObjectInfo info, int length)
        {
            // The Type name of an array ends with "[]". Exclude this to get the real Type
            string typename = info.Type.Substring(0, info.Type.Length - 2);

            try
            {
                return Array.CreateInstance(CreateType(info.Assembly, typename), length);
            }
            catch (Exception ex)
            {
                if (!ExcUtils.IsCatchableExceptionType(ex))
                {
                    throw;
                }

                if (IgnoreCreationErrors)
                {
                    return null;
                }
                else
                {
                    string msg = XmlDeserializerSR.CreationOfInstanceFailedFormat(info.Type, info.Assembly);
                    throw new XmlDeserializerException(msg, ex);
                }
            }
        }

        /// <summary>
        /// Creates an instance by the specified ObjectInfo.
        /// </summary>
        /// <param name="info"></param>
        /// <returns></returns>
        private object CreateInstance(ObjectInfo info)
        {
            try
            {
                // Enough information to create an instance?
                if (!info.IsSufficient)
                {
                    return null;
                }

                object obj = null;

                Type type = CreateType(info.Assembly, info.Type);

                // Is there a binary constructor?
                if (!String.IsNullOrEmpty(info.ConstructorParamType))
                {
                    Object ctorparam = null;
                    byte[] barr = null;

                    if (!String.IsNullOrEmpty(info.Value))
                    {
                        barr = System.Convert.FromBase64String(info.Value);

                        Type ctorparamtype = TryCreateType(info.ConstructorParamAssembly, info.ConstructorParamType);

                        // What type of parameter is needed?
                        if (typeof(Stream).IsAssignableFrom(ctorparamtype))
                        {
                            // Stream
                            ctorparam = new MemoryStream(barr);
                        }
                        else if (typeof(byte[]).IsAssignableFrom(ctorparamtype))
                        {
                            // byte[]
                            ctorparam = barr;
                        }
                    }

                    obj = Activator.CreateInstance(type, new object[] { ctorparam });
                    return obj;
                }

                // Until now only properties with binary data support constructors with parameters

                // Problem: only parameterless constructors or constructors with one parameter
                // which can be converted from String are supported.
                // Failure Example:
                // string s = new string();
                // string s = new string("");
                // This cannot be compiled, but the follwing works;
                // string s = new string("".ToCharArray());
                // The TypeConverter provides a way to instantite objects by non-parameterless 
                // constructors if they can be converted fro String
                TypeConverter tc = GetConverter(type);
                if (tc.CanConvertFrom(typeof(string)))
                {
                    obj = tc.ConvertFrom(null, CultureInfo.InvariantCulture, info.Value);
                    return obj;
                }

                // XmlDeserializer neumi deserializovat typ Version. Problem je v absenci TypeConvertery, ktery by
                // umel vytvorit ze stringu typ Version.
                if (type == typeof(Version))
                {
                    obj = new Version(info.Value);
                    return obj;
                }

                obj = Activator.CreateInstance(type);

                if (obj == null)
                {
                    throw new XmlDeserializerException(XmlDeserializerSR.ActivatorCallFailed);
                }

                return obj;
            }
            catch (Exception ex)
            {
                if (!ExcUtils.IsCatchableExceptionType(ex))
                {
                    throw;
                }

                if (IgnoreCreationErrors)
                {
                    return null;
                }
                else
                {
                    string msg = XmlDeserializerSR.CreationOfInstanceFailedFormat(info.Type, info.Assembly);
                    throw new XmlDeserializerException(msg, ex);
                }
            }
        }

        #endregion Creating instances and types

        #region private methods...
        /// <summary>
        /// Volani metod pro serializaci nebo deserializaci. Pro objekt obj vyhleda vsechny metody oznacene atributem
        /// daneho typu a zavola je. Vysledky se cachuji, protoze bez cachovani dochazelo k mnohonasobnemu zpomaleni
        /// </summary>
        /// <param name="obj">Typ objektu</param>
        /// <param name="attributeType">Typ atributu</param>
        /// <param name="cache">Cache</param>
        /// <param name="state">Objekt, ktery se pak predava metode OnDeserialized. Vhodne pro dodani dat, ktere potrebuji na deserializaci</param>
        private static void CallDeserializationMethod(object obj, Type attributeType, ConcurrentDictionary<Type, MethodInfo[]> cache, object state)
        {
            Type objType = obj.GetType();

            if (!IsValidToCallDeserializationMetod(objType))
            {
                return;
            }

			Func<Type, MethodInfo[]> valueFactory = (Type objtype) =>
				{

					List<MethodInfo> methodList = new List<MethodInfo>();
					MethodInfo[] methods = objType.GetMethods(BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);

					foreach (MethodInfo method in methods)
					{
						// je metoda oznacena attribute OnDeserialized
						if (method.GetCustomAttributes(attributeType, false).Length > 0)
						{
							ParameterInfo[] paramInfo = method.GetParameters();
							// Kak: 9.3.2011  || paramInfo[0].ParameterType != typeof(object) 
							// zrusena kontrola na typ parametru. Napr. pri deserializaci Margins je parametr metody StreamingContext context
							// a vse se preda dobre
							if (paramInfo.Length != 1)
							{
								throw new Exception(string.Format("Method {0}.{1} having {2} attribute must have exactly one parameter.",
									objType.FullName, method.Name, attributeType));
							}
							methodList.Add(method);
						}
					}

					// pokud neni co volat, pridavame do cache null 
					return methodList.Count > 0 ? methodList.ToArray() : null;
				};

			MethodInfo[] methodsToCall = cache.GetOrAdd(objType, valueFactory);

            // null = neni co volat
            if (methodsToCall != null)
            {
                foreach (MethodInfo method in methodsToCall)
                {
                    method.Invoke(obj, new object[] { state });
                }
            }
        }

        /// <summary>
        /// Pokud je propInfo ozna�eno atributem CryptValueBaseAttribute, pak dektyptuje hodnotu v obj
        /// </summary>
        /// <param name="obj"></param>
        /// <param name="propInfo"></param>
        private static void DecryptValue(ref object obj, PropertyInfo propInfo)
        {
            // pokud je definovat cryptovaci attribut, nejprve nutno property decryptovat
            foreach (Attribute attribute in propInfo.GetCustomAttributes(typeof(CryptValueBaseAttribute), true))
            {
                CryptValueBaseAttribute cryptValueAttribute = (CryptValueBaseAttribute)attribute;
                obj = cryptValueAttribute.DecryptValue(obj);
            }
        }

        /// <summary>
        /// Vraci true, pokud na tomto typu ma smysl volat deserializacni metody
        /// </summary>
        /// <param name="objType"></param>
        /// <returns></returns>
        private static bool IsValidToCallDeserializationMetod(Type objType)
        {
            // typy, ktere nelze dedit a urcite k nim nemuzu pridat metody s atributem OnDeserializing
            return !objType.IsPrimitive && objType != typeof(string) && objType != typeof(DateTime) && objType != typeof(Color);
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        private TypeInfo TranslateTypeByKey(String key)
        {
            if (HasTypeDictionary)
            {
                if (_typeDictionary.ContainsKey(key))
                {
                    TypeInfo ti = (TypeInfo)_typeDictionary[key];

                    return ti;
                }
            }

            return null;
        }

        /// <summary>
        /// Gets an ObjectInfo instance by the attributes of the specified XmlNode.
        /// </summary>
        /// <param name="node"></param>
        /// <returns></returns>
        private ObjectInfo GetObjectInfo(XmlNode node)
        {
            ObjectInfo oi = new ObjectInfo();

            String typekey = GetAttributeValue(node, taglib.TYPE_TAG);
            TypeInfo ti = TranslateTypeByKey(typekey);

            if (ti != null)
            {
                oi.Type = ti.TypeName;
                oi.Assembly = ti.AssemblyName;
            }

            // If a TypeDictionary is given, did we find the necessary information to create an instance?
            // If not, try to get information by the Node itself
            if (!oi.IsSufficient)
            {
                oi.Type = GetAttributeValue(node, taglib.TYPE_TAG);
                oi.Assembly = GetAttributeValue(node, taglib.ASSEMBLY_TAG);
            }

            // Name and Value
            oi.Name = GetAttributeValue(node, taglib.NAME_TAG);
            oi.Value = node.InnerText;

            // Binary Constructor
            ti = GetBinaryConstructorType(node);

            if (ti != null)
            {
                // Binary constructor info given
                oi.ConstructorParamType = ti.TypeName;
                oi.ConstructorParamAssembly = ti.AssemblyName;

                // Make sure to read the value from the binary data Node (setting oi.Value = node.InnerText as above is a bit dirty)
                XmlNode datanode = node.SelectSingleNode(taglib.BINARY_DATA_TAG);
                if (datanode != null)
                {
                    oi.Value = datanode.InnerText;
                }
                else
                {
                    datanode = node.SelectSingleNode(taglib.CONSTRUCTOR_TAG);
                    if (datanode != null)
                    {
                        datanode = datanode.SelectSingleNode(taglib.BINARY_DATA_TAG);
                        if (datanode != null)
                        {
                            oi.Value = datanode.InnerText;
                        }
                    }
                }
            }

            return oi;
        }

        /// <summary>
        /// Returns the length of the array of a arry-XmlNode.
        /// </summary>
        /// <param name="parent"></param>
        /// <returns></returns>
        protected int GetArrayLength(XmlNode parent)
        {
            XmlNodeList nl = parent.SelectNodes(taglib.ITEMS_TAG + "/" + taglib.ITEM_TAG);
            int c = 0;

            if (nl != null)
                c = nl.Count;

            return c;
        }

        /// <summary>
        /// Returns the value or the attribute with the specified name from the given node if it is not null or empty.
        /// </summary>
        /// <param name="node"></param>
        /// <param name="name"></param>
        /// <returns></returns>
        protected string GetAttributeValue(XmlNode node, string name)
        {
            if (node == null || String.IsNullOrEmpty(name))
                return null;

            String val = null;
            XmlAttribute att = node.Attributes[name];

            if (att != null)
            {
                val = att.Value;
                if (val.Equals(""))
                    val = null;
            }
            return val;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="node"></param>
        /// <returns></returns>
        protected TypeInfo GetBinaryConstructorType(XmlNode node)
        {
            if (node == null)
                return null;

            XmlNode ctornode = node.SelectSingleNode(taglib.CONSTRUCTOR_TAG);
            if (ctornode == null)
                return null;

            String typekey = GetAttributeValue(ctornode, taglib.TYPE_TAG);

            TypeInfo ti = TranslateTypeByKey(typekey);

            return ti;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="node"></param>
        /// <returns></returns>
        protected bool HasBinaryConstructor(XmlNode node)
        {
            if (node == null)
                return false;

            XmlNode ctornode = node.SelectSingleNode(taglib.CONSTRUCTOR_TAG);
            if (ctornode == null)
                return false;

            XmlNode binnode = ctornode.SelectSingleNode(taglib.BINARY_DATA_TAG);
            if (binnode == null)
                return false;

            return true;
        }

        /// <summary>
        /// Returns the TypeConverter of a Type.
        /// </summary>
        /// <param name="objType"></param>
        /// <returns></returns>
        protected TypeConverter GetConverter(Type type)
        {
			Func<Type, TypeConverter> valueFactory = (objType) =>
			{
				return TypeDescriptor.GetConverter(objType);
			};

            return _typeConverterCache.GetOrAdd(type, valueFactory);
        }


        /// <summary>
        /// Clears the typedictionary collection.
        /// </summary>
        public void Reset()
        {
			if (_typeDictionary != null)
			{
				_typeDictionary.Clear();
			}
		}
		#endregion Misc

	}
}
