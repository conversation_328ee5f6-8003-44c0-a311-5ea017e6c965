using System;
using System.Collections.Generic;
using System.Linq;
using Anete.Common.Data.Interface.Enums;

namespace Anete.Common.Data.Interface.AppServices
{

	/// <summary>
	/// Formatovat radku pro zobrazeni a tisk paragonu
	/// </summary>
	public interface ISalesSlipFormatter
	{

		/// <summary>
		/// Formatovani retezce 1 x 23.60 nebo 1.005 kg x 30 Kč/kg podle dělitenosti zboží
		/// Muze vratit vice radku
		/// </summary>
		IEnumerable<string> FormatAmountAndPriceString(GoodsUnitType unitType, decimal amount, string unit, decimal unitPrice, WeightType? weightType, decimal? tara, bool isStorno, bool printWeightTypeOnStorno, bool duplicateTaraOnLastLine);

	}

}
