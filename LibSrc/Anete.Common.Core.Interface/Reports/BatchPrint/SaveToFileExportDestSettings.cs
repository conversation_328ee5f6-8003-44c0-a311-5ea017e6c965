using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;

namespace Anete.Common.Core.Interface.Reports.BatchPrint
{

	/// <summary>
	/// Nastaveni pro export do souboru
	/// </summary>
	[DataContract(Namespace = ReportsNamespace.DefaultNamespace)]
	public class SaveToFileExportDestSettings : ReportDestSettingsBase, ISaveToFileExportDestSettings
	{

		[DataMember]
		public string FileName { get; set; }

	}

}
