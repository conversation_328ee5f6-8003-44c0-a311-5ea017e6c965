using System;
using System.Configuration;
using System.Reflection;
using Anete.Resources;
using Anete.Log.Core.Log4NetProxy;
using System.ComponentModel;

namespace Anete.Utils.Configuration
{

	/// <summary>
	/// Aktualizace nastaveni v app.Config, odpovidajici Settings.
	/// </summary>
	/// <remarks>Pouziva se pri instalaci programu pro pocatecni modifikaci app.config
	/// Pozor pokud se pouzije z prostredi a ne z EXE tak se zmeny provedou pouze
	/// v spoboru Kasa8.vshost.exe.config, ktery je ale po ukonceni prepsan
	/// kasa8.config takze o zmeny se ihned prijde.</remarks>
    [Obsolete("Pouzijte ConfigFileManager - umi aktualizovat AppSettings i UserSettings zaroven")]
    public class ConfigUpdater
	{

        #region private fields
		private static readonly ILogEx _log = LogManagerEx.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
		private System.Configuration.Configuration _config;
		private ClientSettingsSection _settingsSection;
		private string _groupName;
		#endregion

		#region constructors
		/// <summary>
		/// Initializes a new instance of the configUpdater class
		/// </summary>
		/// <param name="sectionName">Jmeno sekce, obycejne My.Settings</param>
		/// <remarks>Otevre urcenou sekci z apllicattionSettings pomoci ConfigurationManageru
		/// a do elements nacte jednotlive settings elementy.  </remarks>
		public ConfigUpdater(string sectionName): this(sectionName, "applicationSettings")
		{
		}

		/// <summary>
		/// Initializes a new instance of the configUpdater class
		/// </summary>
		/// <param name="sectionName">Jmeno sekce, obycejne My.MySettings</param>
		/// <param name="groupName">Jmeno skupiny, pro aplikacni nastaveni "applicationSettings",
		/// pro user nastaveni "userSettings"</param>
		/// <remarks >Otevre urcenou sekci pomoci ConfigurationManageru a do elements nacte
		/// jednotlive settings elementy.  </remarks>
		public ConfigUpdater(string sectionName, string groupName)
		{
			_log.DebugFormat("New, sectionName={0}, groupName={1}", sectionName, groupName);
			_config = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);
			_configFileName = "<default>";
			_groupName = groupName;
			// zde je nutne nastavovat property SectionName
			SectionName = sectionName;
		}

		/// <summary>
		/// Initializes a new instance of the configUpdater class
		/// </summary>
		/// <param name="sectionName">Jmeno sekce, obycejne nazev programu.Properties.Settings</param>
		/// <param name="groupName">Jmeno skupiny, pro aplikacni nastaveni "applicationSettings",
		/// pro user nastaveni "userSettings"</param>
		/// <param name="filePath">Jmeno .config souboru</param>
		/// <remarks >Otevre urcenou sekci pomoci ConfigurationManageru a do elements nacte
		/// jednotlive settings elementy.  </remarks>
		public ConfigUpdater(string sectionName, string groupName, string filePath)
		{
			_log.DebugFormat("New, sectionName={0}, groupName={1}, filePath={2}", sectionName, groupName, filePath);
			ExeConfigurationFileMap fileMap = new ExeConfigurationFileMap();
			fileMap.ExeConfigFilename = filePath;
			_config = ConfigurationManager.OpenMappedExeConfiguration(fileMap, ConfigurationUserLevel.None);
			_configFileName = filePath;
			_groupName = groupName;
			SectionName = sectionName;
		}

		#endregion

		#region public properties...
        private string _configFileName;
        /// <summary>
        /// Nazev konfiguracniho souboru
        /// </summary>
        public string ConfigFileName
        {
            get
            {
                return _configFileName;
            }
        }

        private string _sectionName;
        /// <summary>
		/// Jmeno sekce, obycejne My.Settings
		/// </summary>
		/// <value></value>
		/// <returns></returns>
		/// <remarks></remarks>
		public string SectionName
		{
			get
			{
				return _sectionName;
			}
			set
			{
				if (_sectionName == value)
				{
					return;
				}
				_sectionName = value;
				ConfigurationSectionGroup group = _config.SectionGroups[_groupName];
				if (group == null)
				{
					throw (new ConfigurationErrorsException(SysRes.sConfigGroupNotFoundFormat(_groupName, _configFileName)));
				}
				_settingsSection = (ClientSettingsSection)group.Sections[_sectionName];
				if (_settingsSection == null)
				{
					throw (new ConfigurationErrorsException(SysRes.sConfigSectionNotFoundFormat(_sectionName, _configFileName)));
				}
			}
		}

		#endregion

		#region public methods
        /// <summary>
        /// Nacte promennou AppInstallationId = IdZarizeni
        /// </summary>
        /// <returns></returns>
        public short GetAppInstallationId()
        {
            return GetSettingsValue<short>("IdZarizeni");
        }

		/// <summary>
		/// Vraci element s danym nazvem. Pri nenalezeni vyvola vyjimku
		/// </summary>
		/// <param name="name">Nazev elementu</param>
		/// <returns>Nalezeny element</returns>
		/// <remarks></remarks>
		public SettingElement GetSettingsElement(string name)
		{
			SettingElement result = FindSettingsElement(name);
			if (result == null)
			{
				throw (new ConfigurationErrorsException(SysRes.sConfigItemtNotFoundFormat(name, _configFileName)));
			}
			return result;
		}


		/// <summary>
		/// Vraci element s danym nazvem nebo Nothing
		/// </summary>
		/// <param name="name">Nazev elementu</param>
		/// <returns>Nalezeny element nebo Nothing pokud element neexistuje</returns>
		/// <remarks></remarks>
		public SettingElement FindSettingsElement(string name)
		{
			SettingElementCollection elements = _settingsSection.Settings;
			foreach (SettingElement element in elements)
			{
				if (StrUtils.IsSame(element.Name, name))
				{
					return element;
				}
			}
			return null;
		}


        /// <summary>
        /// Ziska hodnotu pro dany klic
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key">Klic</param>
        /// <returns></returns>
        public T GetSettingsValue<T>(string key)
        {
            TypeConverter converter = TypeDescriptor.GetConverter(typeof(T));
            return GetSettingsValue<T>(key, converter);
        }

        /// <summary>
        /// Ziska hodnotu pro dany klic
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="name">Klic</param>
        /// <param name="converter">Type converter</param>
        /// <returns></returns>
        public T GetSettingsValue<T>(string name, TypeConverter converter)
        {
            string result = GetSettingsElement(name).Value.ValueXml.InnerText;
            _log.DebugFormat("GetSettingsValue(name={0}), result={1}", name, result);
            return (T)converter.ConvertFromInvariantString(result);
        }

        /// <summary>
        /// Vraci hodnotu pro dany element
        /// </summary>
        /// <param name="name">Nazev elementu</param>
        /// <returns>Hodnotu elementu</returns>
        /// <remarks></remarks>
        public string GetCryptSettingsValue(string name)
        {
            return GetSettingsValue<string>(name);
        }

		/// <summary>
		/// Vraci hodnotu pro dany element
		/// </summary>
		/// <param name="name">Nazev elementu</param>
		/// <returns>Hodnotu elementu</returns>
		/// <remarks></remarks>
		public string GetSettingsValue(string name)
		{
			return GetSettingsValue<string>(name);
		}

		/// <summary>
		/// Vraci hodnotu pro dany element
		/// </summary>
		/// <param name="name">Nazev elementu</param>
		/// <returns>Hodnota elementu</returns>
		/// <remarks></remarks>
        [Obsolete("Pouzijte generickou verzi")]
        public bool GetSettingsValueBool(string name)
        {
            return GetSettingsValue<bool>(name);
        }

		/// <summary>
		/// Vraci hodnotu pro dany element
		/// </summary>
		/// <param name="name">Nazev elementu</param>
		/// <returns>Hodnota elementu</returns>
		/// <remarks></remarks>
        [Obsolete("Pouzijte generickou verzi")]
        public int GetSettingsValueInt(string name)
        {
            return GetSettingsValue<int>(name);
        }

		/// <summary>
		/// Vraci hodnotu pro dany element
		/// </summary>
		/// <param name="name">Nazev elementu</param>
		/// <returns>Hodnota elementu</returns>
		/// <remarks></remarks>
        [Obsolete("Pouzijte generickou verzi")]
        public decimal GetSettingsValueDec(string name)
        {
            return GetSettingsValue<decimal>(name);
        }

        /// <summary>
        /// Nastavi Id zarizeni
        /// </summary>
        /// <param name="appInstallationId"></param>
        public void SetAppInstallationId(short appInstallationId)
        {
            SetSettingsValue("IdZarizeni", appInstallationId);
        }

        /// <summary>
        /// Nastavi hodnotu pro dany element
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="name">Nazev promenne</param>
        /// <param name="value">Hodnota promenne</param>
        /// <remarks>Postupne prochazi vsechny elemnty a pro danou sekci
        /// a pokud jej najde zmeni hodnotu InnerText na sectionValue</remarks>
        public void SetSettingsValue<T>(string name, T value)
        {
            SetSettingsValue<T>(name, value, null);
        }

        /// <summary>
        /// Nastavi hodnotu pro dany element
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="name">Nazev promenne</param>
        /// <param name="value">Hodnota promenne</param>
        /// <param name="converter">Konverter, ktery muze byt pouzit ke konverzi. Pokud je null, pouzije se ToString</param>
        /// <remarks>Postupne prochazi vsechny elemnty a pro danou sekci
        /// a pokud jej najde zmeni hodnotu InnerText na sectionValue</remarks>
        public void SetSettingsValue<T>(string name, T value, TypeConverter converter)
        {
            string stringValue = converter != null ? converter.ConvertToInvariantString(value) : value.ToString();
            _log.DebugFormat("SetSettingsValue name={0}, value={1}", name, stringValue);
            GetSettingsElement(name).Value.ValueXml.InnerText = stringValue;
        }

		/// <summary>
		/// Nastavi hodnotu pro connStr
		/// </summary>
		/// <param name="connStrName"></param>
		/// <param name="value"></param>
		/// <remarks>Obdoba SetSectionValue pro connectionStrings</remarks>
		public void SetConnStrValue(string connStrName, string value)
		{
			_log.DebugFormat("SetConnStrValue connStrName={0}, value={1}", connStrName, value);
			foreach (ConnectionStringSettings connStr in _config.ConnectionStrings.ConnectionStrings)
			{
				if (StrUtils.IsSame(connStr.Name, connStrName))
				{
					connStr.ConnectionString = value;
					return;
				}
			}
			throw (new ConfigurationErrorsException(SysRes.sConfigItemtNotFoundFormat(connStrName, _configFileName)));
		}

        /// <summary>
        /// Vycte hodnotu connection stringu.
        /// </summary>
        /// <param name="connStrName">Nazev connection stringu</param>
        /// <returns>Hodnota connection stringu</returns>
        public string GetConnStrValue(string connStrName)
        {
            _log.DebugFormat("GetConnStrValue connStrName={0}", connStrName);
            foreach (ConnectionStringSettings connStr in _config.ConnectionStrings.ConnectionStrings)
            {
                if (StrUtils.IsSame(connStr.Name, connStrName))
                {
                    return connStr.ConnectionString;
                }
            }
            throw (new ConfigurationErrorsException(SysRes.sConfigItemtNotFoundFormat(connStrName, _configFileName)));		
        }

		///<summary>
		///Ulozeni zmen v configu do souboru
		/// </summary>
		/// <remarks>Nastavi ForceSave pro danou section a provede Full save.</remarks>
		public void SaveConfig()
		{
            _log.Debug("SaveConfig()");
			// bez tohoto nastaveni se zmeny neulozily
			_config.ConnectionStrings.SectionInformation.ForceSave = true;
			_settingsSection.SectionInformation.ForceSave = true;
			_config.Save(ConfigurationSaveMode.Full);
		}
		#endregion

	}

}

