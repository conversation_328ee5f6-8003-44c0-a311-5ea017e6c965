using System;
using Anete.Utils.ComponentModel.Attributes;

namespace Anete.Config.Core.Categories
{
    /// <summary>
    /// Lokalizacni atribut pro enum <c>CategoryLevel</c>.
    /// </summary>
    public class CategoryLevelLocalizationAttribute : ResourceEnumAttribute
    {
        /// <summary>
        /// Create a new instance of the converter using translations from the given resource manager
        /// </summary>
        public CategoryLevelLocalizationAttribute()
            : base(CategoryLevelSR.ResourceManager)
        {

        }
    }
}
