using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Anete.Utils
{
    /// <summary>
    /// <PERSON><PERSON>, ktera ma implementovan GetHashCode a Equals tak, aby zohlednoval pouze parametry predane v konstruktoru.
    /// Pouziva se pro kontrolu duplicity primarnich klicu.
    /// </summary>
    public class GetHashCodeGenericEntity
    {
        private readonly object[] _args;
        /// <summary>
        /// Initializes a new instance of the GetHashCodeGenericEntity class.
        /// </summary>
        public GetHashCodeGenericEntity(params object[] args)
        {
            _args = args;            
        }

        public override int GetHashCode()
        {
            return HashCodeUtils.GetHashCode(_args);
        }

        public override bool Equals(object obj)
        {
            GetHashCodeGenericEntity other = obj as GetHashCodeGenericEntity;
            if (obj == null)
            {
                return false;
            }

			return _args.SequenceEqual(other._args);
        }
    }
}
