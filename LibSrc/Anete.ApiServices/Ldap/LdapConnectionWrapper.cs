using Anete.Config.Configs.Core.Global.Synchronization;
using Anete.Config.Configs.Core.Shared;
using Anete.Utils.Patterns;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.DirectoryServices.Protocols;
using System.Linq;
using System.Net;
using System.Text;

namespace Anete.ApiServices.Ldap
{
	public class LdapConnectionWrapper : Disposable
	{
		private readonly ILogger _logger;
		private readonly LdapConnection _connection;

		public LdapConnectionWrapper(ILogger logger, LdapServerConfig config)
		{
			_logger = logger;

			var server = new LdapDirectoryIdentifier(config.Address, config.GetPort());
			_connection = new LdapConnection(server);

			_connection.SessionOptions.ProtocolVersion = config.ProtocolVersion;
			_connection.AuthType = config.AuthType;
			_connection.SessionOptions.SecureSocketLayer = config.UseSsl;

			_connection.SessionOptions.VerifyServerCertificate = (c, certificate) => true;
			_connection.SessionOptions.QueryClientCertificate = (c, trustedCas) => null;

			_connection.SessionOptions.Signing = _connection.SessionOptions.Sealing = config.EnableKerberosEncryption;
		}

		protected override void DisposeUnmanagedResources()
		{
			_connection.Dispose();
		}

		public void Connect(NetworkCredential credential)
		{
			_connection.Bind(credential);
			_logger.LogDebug("Ldap: user bind successful");
		}

		public void Connect(LdapServiceCredentialsConfig config)
		{
			_connection.Bind(new NetworkCredential(config.UserName, config.Password, config.Domain));
			_logger.LogDebug("Ldap: user bind successful");
		}

		public void Connect(NetworkCredential credential, AuthType authType)
		{
			_connection.AuthType = authType;
			if (credential == null)
			{
				_connection.Bind();
			}
			else
			{
				_connection.Bind(credential);
			}
			_logger.LogDebug("Ldap: user bind successful");
		}

		public bool TryConnect(NetworkCredential credential, AuthType authType)
		{
			try
			{
				Connect(credential, authType);
				return true;
			}
			catch (LdapException ex) when (ex.ErrorCode == 49)//LDAP_INVALID_CREDENTIALS
			{
				_logger.LogWarning(ex, "Error when veryfing credentials");
				return false;
			}
		}

		public bool TryConnect(string userName, string password, string domain, AuthType authType)
		{
			return TryConnect(new NetworkCredential(userName, password, domain), authType);
		}

		public LdapResultItemDto FindFirst(string baseDN, string filter, string attribute)
		{
			var result = FindAll(baseDN, filter, attribute);
			return result?.FirstOrDefault();
		}

		public LdapResultDto FindAll(IEnumerable<SyncLdapSearchRequestConfig> configs, params string[] attributes)
		{
			var result = Enumerable.Empty<LdapResultItemDto>();
			var messages = new List<string>();
			foreach (var config in configs)
			{
				try
				{
					var items = FindAll(config.BaseDN, config.SearchRequest, attributes);
					result = result.Concat(items);
				}
				catch (Exception ex)
				{
					var sb = new StringBuilder();
					sb.AppendLine($"{nameof(config.BaseDN)}: {config.BaseDN}");
					sb.AppendLine($"{nameof(config.SearchRequest)}: {config.SearchRequest}");
					if (attributes != null)
					{
						sb.AppendLine($"{nameof(attributes)}: {string.Join(", ", attributes)}");
					}
					sb.AppendLine();
					if (ex is DirectoryOperationException ldapException && ldapException.Response != null)
					{
						sb.AppendLine($"{ldapException.Response.ResultCode}: {ldapException.Response.ErrorMessage}");
					}
					sb.AppendLine(ex.ToString());
					messages.Add(sb.ToString());
				}
			}
			return new LdapResultDto(result.ToArray(), messages);
		}

		private LdapResultItemDto[] FindAll(string baseDN, string filter, params string[] attributes)
		{
			var request = new SearchRequest(baseDN, filter, SearchScope.Subtree, attributes);
			var response = _connection.SendRequest(request);
			var searchResponse = response as SearchResponse;

			if (searchResponse?.Entries?.Count == null || searchResponse.Entries.Count == 0)
			{
				_logger.LogWarning($"LDAP search request BaseDN='{baseDN}' Filter='{filter}' returned an empty result, ErrorMessage='{response?.ErrorMessage}'");
				return Array.Empty<LdapResultItemDto>();
			}

			var entries = searchResponse.Entries.OfType<SearchResultEntry>();
			var items = entries.Select(GetResultItem).ToArray();
			return items.ToArray();
		}

		private static LdapResultItemDto GetResultItem(SearchResultEntry result)
		{
			var values = result.Attributes.Values
				.OfType<DirectoryAttribute>()
				.SelectMany(x => x
					.GetValues(typeof(string))
					.OfType<string>()
					.Select(y => new LdapResultItemAttributeDto(x.Name, y))
				);
			return new LdapResultItemDto(result.DistinguishedName, values.ToArray());
		}
	}
}