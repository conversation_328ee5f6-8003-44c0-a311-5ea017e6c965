using Anete.Common.Core.Interface.Enums;
using Anete.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Unity;

namespace Anete.Infrastructure.Core
{
	/// <summary>
	/// Popisuje danou autorizaci
	/// </summary>
	public abstract class AuthenticationInfoBase
	{
		public AuthenticationInfoBase(AppAuthenticationType appAuthenticationType)
		{
			AppAuthenticationType = appAuthenticationType;
		}
		public AppAuthenticationType AppAuthenticationType { get; }		
	}
}
