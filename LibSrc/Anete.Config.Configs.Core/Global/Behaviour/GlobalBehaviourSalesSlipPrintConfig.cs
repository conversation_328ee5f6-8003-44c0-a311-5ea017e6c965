using System;
using System.ComponentModel;
using Anete.Config.Core.Attributes;
using Anete.Config.Core.Categories;
using Anete.Config.Core.CFSystKonfig;
using Anete.Config.Core.Internals;
using Anete.Common.Core.Interface.Enums;
using System.Drawing.Design;
using Anete.Utils.ComponentModel.TypeConverters;

namespace Anete.Config.Configs.Core.Global.Behaviour
{
	/// <summary>
	/// Parametry pro tisk paragonu.
	/// </summary>
	[GlobalBehaviourConfigDBKeyAttribute("SalesSlipPrint")]
	[ConfigVersion("*******")]
	[ConfigClassSRDescription(typeof(GlobalBehaviourSalesSlipPrintConfig))]
	[DefaultConfigInstance(false)]
	[CashDeskCategory(CategoryLevel.Basic, CategoryLevel.Advanced)]
	[OfficeCategory(CategoryLevel.Advanced)]
	[OfflineConfig(ApplicationType.CashDesk)]
	[ApplicationCategory(ApplicationType.Office8)]
	[ApplicationCategory(ApplicationType.CashDesk)]
	public class GlobalBehaviourSalesSlipPrintConfig : ConfigBase
	{

		private const string _footerDefValue = "";
		private string _footer = _footerDefValue;
		/// <summary>
		/// Text, ktery se tiskne na patickach paragonu.
		/// Je ve forme radku oddelenych NewLine. Muze obsahovat funkce - vice viz PosPrinter.WriteFormattedHeader
		/// </summary>
		[DefaultValue(_footerDefValue)]
		[ConfigSRDisplayName("Footer", typeof(GlobalBehaviourSalesSlipPrintConfig))]
		[ConfigSRDescription("Footer", typeof(GlobalBehaviourSalesSlipPrintConfig))]
		[Editor("System.ComponentModel.Design.MultilineStringEditor, System.Design, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a", typeof(System.Drawing.Design.UITypeEditor))]
		[MapStringToSemicolonStringPropertyToCFSystKonfigAttribute("parag_Pata", MapToCFSystKonfigOption.ReadFromCFSystKonfig)]
		public string Footer
		{
			get
			{
				return _footer;
			}
			set
			{
				if (_footer == value)
				{
					return;
				}
				_footer = value;
				OnPropertyChanged("Footer");
			}
		}

		private const string _headerDefValue = "Název\r\nUlice\r\nPSČ\r\nMěsto\r\nIČ: x";
		private string _header = _headerDefValue;
		/// <summary>
		/// Text, ktery se tiskne v hlavickach paragonu.
		/// Je ve forme radku oddelenych NewLine. Muze obsahovat funkce - vice viz PosPrinter.WriteFormattedHeader
		/// </summary>
		[DefaultValue(_headerDefValue)]
		[ConfigSRDisplayName("Header", typeof(GlobalBehaviourSalesSlipPrintConfig))]
		[ConfigSRDescription("Header", typeof(GlobalBehaviourSalesSlipPrintConfig))]
		[Editor("System.ComponentModel.Design.MultilineStringEditor, System.Design, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a", typeof(System.Drawing.Design.UITypeEditor))]
		[MapStringToSemicolonStringPropertyToCFSystKonfigAttribute("parag_Hlava", MapToCFSystKonfigOption.ReadFromCFSystKonfig)]
		public string Header
		{
			get
			{
				return _header;
			}
			set
			{
				if (_header == value)
				{
					return;
				}
				_header = value;
				OnPropertyChanged("Header");
			}
		}

		private const bool _printLogoDefValue = false;
		private bool _printLogo = _printLogoDefValue;
		/// <summary>
		/// Tisk loga na paragonu
		/// </summary>
		[DefaultValue(_printLogoDefValue)]
		[ConfigSRDisplayName("PrintLogo", typeof(GlobalBehaviourSalesSlipPrintConfig))]
		[ConfigSRDescription("PrintLogo", typeof(GlobalBehaviourSalesSlipPrintConfig))]
		public bool PrintLogo
		{
			get { return _printLogo; }
			set
			{
				if (_printLogo == value)
				{
					return;
				}
				_printLogo = value;
				OnPropertyChanged("PrintLogo");
			}
		}

		private const bool _printAccountBallanceDefValue = true;
		private bool _printAccountBallance = _printAccountBallanceDefValue;
		/// <summary>
		/// Povoluje kase tisk počátečního a koncového stavu účtu na každém paragonu.
		/// </summary>
		[DefaultValue(_printAccountBallanceDefValue)]
		[ConfigSRCategory("LoggedInClient", typeof(GlobalBehaviourSalesSlipPrintConfig))]
		[ConfigSRDisplayName("PrintAccountBallance", typeof(GlobalBehaviourSalesSlipPrintConfig))]
		[ConfigSRDescription("PrintAccountBallance", typeof(GlobalBehaviourSalesSlipPrintConfig))]
		[MapOldBoolPropertyToCFSystKonfig("povol_tiskuctu", MapToCFSystKonfigOption.ReadFromCFSystKonfig)]
		public bool PrintAccountBallance
		{
			get
			{
				return _printAccountBallance;
			}
			set
			{
				if (_printAccountBallance == value)
				{
					return;
				}
				_printAccountBallance = value;
				OnPropertyChanged("PrintAccountBallance");
			}
		}

		private const bool _printVoucherCountDefValue = true;
		private bool _printVoucherCount = _printVoucherCountDefValue;
		/// <summary>
		/// Povoluje kase tisk zbývajícího stavu bonů na každém paragonu z kasy.
		/// </summary>
		[DefaultValue(_printVoucherCountDefValue)]
		[ConfigSRCategory("LoggedInClient", typeof(GlobalBehaviourSalesSlipPrintConfig))]
		[ConfigSRDisplayName("PrintVoucherCount", typeof(GlobalBehaviourSalesSlipPrintConfig))]
		[ConfigSRDescription("PrintVoucherCount", typeof(GlobalBehaviourSalesSlipPrintConfig))]
		[MapOldBoolPropertyToCFSystKonfig("povol_tiskbonu", MapToCFSystKonfigOption.ReadFromCFSystKonfig)]
		public bool PrintVoucherCount
		{
			get
			{
				return _printVoucherCount;
			}
			set
			{
				if (_printVoucherCount == value)
				{
					return;
				}
				_printVoucherCount = value;
				OnPropertyChanged("PrintVoucherCount");
			}
		}

		private const bool _printGoodsLimitBalanceDefValue = false;
		private bool _printGoodsLimitBalance = _printGoodsLimitBalanceDefValue;
		/// <summary>
		/// Tisknout zustatek limitu pro sortiment - pouziva CSA, DANFOSS
		/// </summary>
		[DefaultValue(_printGoodsLimitBalanceDefValue)]
		[ConfigSRCategory("LoggedInClient", typeof(GlobalBehaviourSalesSlipPrintConfig))]
		[ConfigSRDisplayName("PrintGoodsLimitBalance", typeof(GlobalBehaviourSalesSlipPrintConfig))]
		[ConfigSRDescription("PrintGoodsLimitBalance", typeof(GlobalBehaviourSalesSlipPrintConfig))]
		public bool PrintGoodsLimitBalance
		{
			get
			{
				return _printGoodsLimitBalance;
			}
			set
			{
				if (_printGoodsLimitBalance == value)
				{
					return;
				}
				_printGoodsLimitBalance = value;
				OnPropertyChanged("PrintGoodsLimitBalance");
			}
		}

		private const bool _printMealLimitBalanceDefValue = false;
		private bool _printMealLimitBalance = _printMealLimitBalanceDefValue;
		/// <summary>
		/// Tisknout zustatek limitu pro jidla - pouziva CSA, DANFOSS
		/// </summary>
		[DefaultValue(_printMealLimitBalanceDefValue)]
		[ConfigSRCategory("LoggedInClient", typeof(GlobalBehaviourSalesSlipPrintConfig))]
		[ConfigSRDisplayName("PrintMealLimitBalance", typeof(GlobalBehaviourSalesSlipPrintConfig))]
		[ConfigSRDescription("PrintMealLimitBalance", typeof(GlobalBehaviourSalesSlipPrintConfig))]
		public bool PrintMealLimitBalance
		{
			get
			{
				return _printMealLimitBalance;
			}
			set
			{
				if (_printMealLimitBalance == value)
				{
					return;
				}
				_printMealLimitBalance = value;
				OnPropertyChanged("PrintMealLimitBalance");
			}
		}

		private const bool _printSubsidiesDefValue = false;
		private bool _printSubsidies = _printSubsidiesDefValue;
		/// <summary>
		/// Tisknout prispevky z fondu FKSP - pouziva CSA, DANFOSS
		/// </summary>
		[DefaultValue(_printSubsidiesDefValue)]
		[ConfigSRCategory("LoggedInClient", typeof(GlobalBehaviourSalesSlipPrintConfig))]
		[ConfigSRDisplayName("PrintSubsidies", typeof(GlobalBehaviourSalesSlipPrintConfig))]
		[ConfigSRDescription("PrintSubsidies", typeof(GlobalBehaviourSalesSlipPrintConfig))]
		public bool PrintSubsidies
		{
			get
			{
				return _printSubsidies;
			}
			set
			{
				if (_printSubsidies == value)
				{
					return;
				}
				_printSubsidies = value;
				OnPropertyChanged("PrintSubsidies");
			}
		}

		private const bool _printPriceListComponentsDefValue = false;
		private bool _printPriceListComponents = _printPriceListComponentsDefValue;
		/// <summary>
		/// Tisknout cenikove slozky na paragonu
		/// </summary>
		[DefaultValue(_printPriceListComponentsDefValue)]
		[ConfigSRCategory("LoggedInClient", typeof(GlobalBehaviourSalesSlipPrintConfig))]
		[ConfigSRDisplayName("PrintPriceListComponents", typeof(GlobalBehaviourSalesSlipPrintConfig))]
		[ConfigSRDescription("PrintPriceListComponents", typeof(GlobalBehaviourSalesSlipPrintConfig))]
		public bool PrintPriceListComponents
		{
			get { return _printPriceListComponents; }
			set
			{
				if (_printPriceListComponents == value)
				{
					return;
				}
				_printPriceListComponents = value;
				OnPropertyChanged("PrintPriceListComponents");
			}
		}

		private int[] _priceListComponentIds = new int[0];
		/// <summary>
		/// Seznam cenikovych slozek, ktere by se mely tisknout, pokud je zapnuto PrintPriceListComponents
		/// Atributem Editor(UITypeEditor) rikam, ze uz nechci editaci pres ArrayEditor - tak bych nebyl schopen kontrolovat duplicitu
		/// </summary>
		[ConfigSRCategory("LoggedInClient", typeof(GlobalBehaviourSalesSlipPrintConfig))]
		[ConfigSRDisplayName("PriceListComponentIds", typeof(GlobalBehaviourSalesSlipPrintConfig))]
		[ConfigSRDescription("PriceListComponentIds", typeof(GlobalBehaviourSalesSlipPrintConfig))]
		[TypeConverter(typeof(UniqueCommaArrayAsStringConverter<int>))]
		[Editor(typeof(UITypeEditor), typeof(UITypeEditor))]
		public int[] PriceListComponentIds
		{
			get { return _priceListComponentIds; }
			set
			{
				if (_priceListComponentIds == value)
				{
					return;
				}
				_priceListComponentIds = value;
				OnPropertyChanged("PriceListComponentIds");
			}
		}

		private ESalesSlipMode _eSalesSlipMode = ESalesSlipMode.Disabled;
		/// <summary>
		/// Mod pro elektronicke uctenky
		/// </summary>
		[ConfigSRCategory("LoggedInClient", typeof(GlobalBehaviourSalesSlipPrintConfig))]
		[DefaultValue(typeof(ESalesSlipMode), nameof(ESalesSlipMode.Disabled))]
		[ConfigSRDisplayName(nameof(ESalesSlipMode), typeof(GlobalBehaviourSalesSlipPrintConfig))]
		[ConfigSRDescriptionWithEnumDescription(nameof(ESalesSlipMode), typeof(ESalesSlipMode), typeof(GlobalBehaviourSalesSlipPrintConfig))]
		public ESalesSlipMode ESalesSlipMode
		{
			get { return _eSalesSlipMode; }
			set
			{
				if (_eSalesSlipMode == value)
				{
					return;
				}
				_eSalesSlipMode = value;
				OnPropertyChanged(nameof(ESalesSlipMode));
			}
		}

		private const bool _printSalesSlipClientNameDefValue = true;
		private bool _printSalesSlipClientName = _printSalesSlipClientNameDefValue;
		/// <summary>
		/// Tisknout jmeno klienta na paragon.
		/// </summary>
		[DefaultValue(_printSalesSlipClientNameDefValue)]
		[ConfigSRCategory("LoggedInClient", typeof(GlobalBehaviourSalesSlipPrintConfig))]
		[ConfigSRDisplayName(nameof(PrintSalesSlipClientName), typeof(GlobalBehaviourSalesSlipPrintConfig))]
		[ConfigSRDescription(nameof(PrintSalesSlipClientName), typeof(GlobalBehaviourSalesSlipPrintConfig))]
		public bool PrintSalesSlipClientName
		{
			get { return _printSalesSlipClientName; }
			set
			{
				if (_printSalesSlipClientName == value)
				{
					return;
				}
				_printSalesSlipClientName = value;
				OnPropertyChanged(nameof(PrintSalesSlipClientName));
			}
		}					

		private const bool _printSalesSlipClientPersonalIdDefValue = false;
		private bool _printSalesSlipClientPersonalId = _printSalesSlipClientPersonalIdDefValue;
		/// <summary>
		/// Tisknout Ocs na paragon.
		/// </summary>
		[DefaultValue(_printSalesSlipClientPersonalIdDefValue)]
		[ConfigSRCategory("LoggedInClient", typeof(GlobalBehaviourSalesSlipPrintConfig))]
		[ConfigSRDisplayName(nameof(PrintSalesSlipClientPersonalId), typeof(GlobalBehaviourSalesSlipPrintConfig))]
		[ConfigSRDescription(nameof(PrintSalesSlipClientPersonalId), typeof(GlobalBehaviourSalesSlipPrintConfig))]
		public bool PrintSalesSlipClientPersonalId
		{
			get { return _printSalesSlipClientPersonalId; }
			set
			{
				if (_printSalesSlipClientPersonalId == value)
				{
					return;
				}
				_printSalesSlipClientPersonalId = value;
				OnPropertyChanged(nameof(PrintSalesSlipClientPersonalId));
			}
		}

		private const bool _printSalesSlipClientCardNumberDefValue = false;
		private bool _printSalesSlipClientCardNumber = _printSalesSlipClientCardNumberDefValue;
		/// <summary>
		/// Tisknout čísla karet na paragon.
		/// </summary>
		[DefaultValue(_printSalesSlipClientCardNumberDefValue)]
		[ConfigSRCategory("LoggedInClient", typeof(GlobalBehaviourSalesSlipPrintConfig))]
		[ConfigSRDisplayName(nameof(PrintSalesSlipClientCardNumber), typeof(GlobalBehaviourSalesSlipPrintConfig))]
		[ConfigSRDescription(nameof(PrintSalesSlipClientCardNumber), typeof(GlobalBehaviourSalesSlipPrintConfig))]
		public bool PrintSalesSlipClientCardNumber
		{
			get { return _printSalesSlipClientCardNumber; }
			set
			{
				if (_printSalesSlipClientCardNumber == value)
				{
					return;
				}
				_printSalesSlipClientCardNumber = value;
				OnPropertyChanged(nameof(PrintSalesSlipClientCardNumber));
			}
		}

		private const bool _printSalesSlipCashierNameDefValue = true;
		private bool _printSalesSlipCashierName = _printSalesSlipCashierNameDefValue;
		/// <summary>
		/// Tisknout jmeno pokladní na paragon.
		/// </summary>
		[DefaultValue(_printSalesSlipCashierNameDefValue)]
		[ConfigSRCategory("LoggedInClient", typeof(GlobalBehaviourSalesSlipPrintConfig))]
		[ConfigSRDisplayName(nameof(PrintSalesSlipCashierName), typeof(GlobalBehaviourSalesSlipPrintConfig))]
		[ConfigSRDescription(nameof(PrintSalesSlipCashierName), typeof(GlobalBehaviourSalesSlipPrintConfig))]
		public bool PrintSalesSlipCashierName
		{
			get { return _printSalesSlipCashierName; }
			set
			{
				if (_printSalesSlipCashierName == value)
				{
					return;
				}
				_printSalesSlipCashierName = value;
				OnPropertyChanged(nameof(PrintSalesSlipCashierName));
			}
		}

		private const bool _isStackingPrintEnabledDefValue = false;
		private bool _isStackingPrintEnabled = _isStackingPrintEnabledDefValue;
		/// Tisknutí položeš sloučeně
		/// </summary>
		[DefaultValue(_isStackingPrintEnabledDefValue)]
		[ConfigSRCategory("LoggedInClient", typeof(GlobalBehaviourSalesSlipPrintConfig))]
		[ConfigSRDisplayName(nameof(IsStackingPrintEnabled), typeof(GlobalBehaviourSalesSlipPrintConfig))]
		[ConfigSRDescription(nameof(IsStackingPrintEnabled), typeof(GlobalBehaviourSalesSlipPrintConfig))]
		public bool IsStackingPrintEnabled
		{
			get { return _isStackingPrintEnabled; }
			set
			{
				if (_isStackingPrintEnabled == value)
				{
					return;
				}
				_isStackingPrintEnabled = value;
				OnPropertyChanged(nameof(IsStackingPrintEnabled));
			}
		}

		private const bool _isPrintWebKreditUsernameEnabledDefValue = false;
		private bool _isPrintWebKreditUsernameEnabled = _isPrintWebKreditUsernameEnabledDefValue;
		/// Tisknout uživatelské jméno dle webkreditu
		/// </summary>
		[DefaultValue(_isPrintWebKreditUsernameEnabledDefValue)]
		[ConfigSRCategory("LoggedInClient", typeof(GlobalBehaviourSalesSlipPrintConfig))]
		[ConfigSRDisplayName(nameof(IsPrintWebKreditUsernameEnabled), typeof(GlobalBehaviourSalesSlipPrintConfig))]
		[ConfigSRDescription(nameof(IsPrintWebKreditUsernameEnabled), typeof(GlobalBehaviourSalesSlipPrintConfig))]
		public bool IsPrintWebKreditUsernameEnabled
		{
			get => _isPrintWebKreditUsernameEnabled;
			set
			{
				if (_isPrintWebKreditUsernameEnabled == value)
				{
					return;
				}
				_isPrintWebKreditUsernameEnabled = value;
				OnPropertyChanged(nameof(IsPrintWebKreditUsernameEnabled));
			}
		}

		private const bool _printSalesSlipTableSectionDefValue = false;
		private bool _printSalesSlipTableSection = _printSalesSlipTableSectionDefValue;
		/// <summary>
		/// Tisknout jmeno pokladní na paragon.
		/// </summary>
		[DefaultValue(_printSalesSlipTableSectionDefValue)]
		[ConfigSRCategory("LoggedInClient", typeof(GlobalBehaviourSalesSlipPrintConfig))]
		[ConfigSRDisplayName(nameof(PrintSalesSlipTableSection), typeof(GlobalBehaviourSalesSlipPrintConfig))]
		[ConfigSRDescription(nameof(PrintSalesSlipTableSection), typeof(GlobalBehaviourSalesSlipPrintConfig))]
		public bool PrintSalesSlipTableSection
		{
			get => _printSalesSlipTableSection;
			set
			{
				if (_printSalesSlipTableSection == value)
				{
					return;
				}
				_printSalesSlipTableSection = value;
				OnPropertyChanged(nameof(PrintSalesSlipTableSection));
			}
		}

		private const bool _printTotalSaleDefValue = false;
		private bool _printTotalSale = _printTotalSaleDefValue;
		/// <summary>
		/// Tisknout celkovou vysi slevy? Problematicke u dotovanych jidel, kdy se sleva vubec nevycisluje
		/// </summary>
		[DefaultValue(_printTotalSaleDefValue)]
		[ConfigSRDisplayName("PrintTotalSale", typeof(GlobalBehaviourSalesSlipPrintConfig))]
		[ConfigSRDescription("PrintTotalSale", typeof(GlobalBehaviourSalesSlipPrintConfig))]
		public bool PrintTotalSale
		{
			get
			{
				return _printTotalSale;
			}
			set
			{
				if (_printTotalSale == value)
				{
					return;
				}
				_printTotalSale = value;
				OnPropertyChanged();
			}
		}
	}
}