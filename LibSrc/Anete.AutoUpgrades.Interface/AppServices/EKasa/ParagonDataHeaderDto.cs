using Anete.Common.Data.Interface.Business.EKasa;
using Anete.Resources;
using System;
using System.Linq;
using System.Runtime.Serialization;

namespace Anete.AutoUpgrades.Interface.AppServices.EKasa
{
	[DataContract(Namespace = AneteNamespace.DefaultNamespace)]
	public class ParagonDataHeaderDto : IHeaderParagonData
	{
		public ParagonDataHeaderDto(string appInstallationDesc, short appInstallationId, DateTime datumUzp, string dic, string dkp, int idParagon, short idUop, string paragonNumber, string pokladni, DateTime saleDateTime, bool vatPayer,
			string originalParagonNumberIfItsStorno, string tableChair)
		{
			AppInstallationDesc = appInstallationDesc;
			AppInstallationId = appInstallationId;
			DatumUzp = datumUzp;
			Dic = dic;
			Dkp = dkp;
			IdParagon = idParagon;
			IdUop = idUop;
			ParagonNumber = paragonNumber;
			Pokladni = pokladni;
			SaleDateTime = saleDateTime;
			VatPayer = vatPayer;
			OriginalParagonNumberIfItsStorno = originalParagonNumberIfItsStorno;
			TableChair = tableChair;
		}

		[DataMember(Order = 0)]
		public string AppInstallationDesc { get; private set; }

		[DataMember(Order = 1)]
		public short AppInstallationId { get; private set; }

		[DataMember(Order = 2)]
		public DateTime DatumUzp { get; private set; }

		[DataMember(Order = 3)]
		public string Dic { get; private set; }

		[DataMember(Order = 4)]
		public string Dkp { get; private set; }

		[DataMember(Order = 5)]
		public int IdParagon { get; private set; }

		[DataMember(Order = 6)]

		public short IdUop { get; private set; }

		[DataMember(Order = 7)]
		public string ParagonNumber { get; private set; }

		[DataMember(Order = 8)]
		public string Pokladni { get; private set; }

		[DataMember(Order = 9)]
		public DateTime SaleDateTime { get; private set; }

		[DataMember(Order = 10)]
		public bool VatPayer { get; private set; }

		[DataMember(Order = 11)]
		public string OriginalParagonNumberIfItsStorno { get; private set; }

		[DataMember(Order = 12)]
		public string TableChair { get; private set; }
	}
}
