using Anete.Resources;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace Anete.Log4Net.Core
{

	/// <summary>
	/// Konfiguracni data pro Log4NetAppConfig
	/// </summary>
    [DataContract(Namespace = AneteNamespace.DefaultNamespace)]
	public class Log4NetAppConfigData : IEquatable<Log4NetAppConfigData>
	{

		/// <summary>
		/// Implicitni logovaci adresar
		/// </summary>
		public const string DefaultLogDir = @"{commonappdata}\Anete\Logs\{appname}\{user}";

		#region constructors...
		public Log4NetAppConfigData()
		{
		}

		public Log4NetAppConfigData(LogLevel rootLogLevel)
		{
			RootLogLevel = rootLogLevel;
		}

		public Log4NetAppConfigData(int maximumDirectoryCount, string logDir, LogLevel rootLogLevel)
		{
			if (string.IsNullOrEmpty(logDir))
			{
				throw new ArgumentException($"{nameof(logDir)} is null or empty.", nameof(logDir));
			}			

			MaximumDirectoryCount = maximumDirectoryCount;
			LogDir = logDir;
			RootLogLevel = rootLogLevel;
		}
        #endregion

        #region properties...
        /// <summary>
        /// Maximalni pocet logovacich adresaru
        /// </summary>
        [DataMember(Order = 0)]
        public int MaximumDirectoryCount { get; private set; } = 30;


        /// <summary>
        /// Logovaci adresar
        /// </summary>
        [DataMember(Order = 1)]
        public string LogDir { get; private set; } = DefaultLogDir;

        /// <summary>
        /// Logovaci uroven korenoveho logovace
        /// </summary>
        [DataMember(Order = 2)]
        public LogLevel RootLogLevel { get; private set; } = LogLevel.Debug;
		#endregion

		#region public overrides...
		public override bool Equals(object obj)
		{
			if (obj is Log4NetAppConfigData)
			{
				return Equals((Log4NetAppConfigData)obj);
			}

			return base.Equals(obj);
		}

		public bool Equals(Log4NetAppConfigData other)
		{
			if (ReferenceEquals(null, other))
			{
				return false;
			}

			if (ReferenceEquals(this, other))
			{
				return true;
			}

			return MaximumDirectoryCount.Equals(other.MaximumDirectoryCount) && Equals(LogDir, other.LogDir) && Equals(RootLogLevel, other.RootLogLevel);
		}

		public override int GetHashCode()
		{
			return MaximumDirectoryCount.GetHashCode() ^ LogDir.GetHashCode() ^ RootLogLevel.GetHashCode();
		}

		public override string ToString()
		{
			return $"MaximumDirectoryCount={MaximumDirectoryCount}, LogDir={LogDir}, RootLogLevel={RootLogLevel}";
		}
		#endregion

	}

}
