//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Anete.Common.Data.Nh.Entities.Sklady
{

    /// <summary>
    /// Tabulka: dba.SKC_SKLADY
    /// </summary>
    public partial class SkcStock : Anete.Data.Nh.NhEntityBase, INotifyPropertyChanging, INotifyPropertyChanged {

        private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(System.String.Empty);

        private short _StockId;

        private string _Name;

        private short _REPLIKAID;

        private int? _BARVA;

        private short _KATEGORIEID;

        private short _SKLCENASDPH;

        private short? _ObjSkupinaID;

        private short? _RegionID;

        private string _ProdejniDnyKasy;

        private bool? _KONTROLAPCPV;

        private bool _Active;

        private ISet<NomReceiptGroup> _NomReceiptGroup;

        private SkcResort _SkcResort;

        private ISet<SklGoodsBasic> _SklGoods;

        private ISet<SkcAccountingPeriod> _AccountingPeriods;
    
        #region Extensibility Method Definitions
        
        partial void OnCreated();

        public override bool Equals(object obj)
        {
          SkcStock toCompare = obj as SkcStock;
          if (toCompare == null)
          {
            return false;
          }

          if (!Object.Equals(this.StockId, toCompare.StockId))
            return false;
         // pokud mam default primary key, znamena to, ze jedna o nove vytvorenou entitu, ktera jeste nema key prirazen. 
        // V takove pripade musim porovnat podle instance
        bool isDefault = true;
        if (this.StockId != default(short))
        {
          isDefault = false;
        }
     

          if (isDefault)
          {
            return object.ReferenceEquals(toCompare, this);
          }     
          return true;
        }

	private int? _hashCode = null;
        public override int GetHashCode()
        {
	if (_hashCode == null)
          {
          _hashCode = 13;
          _hashCode = (_hashCode * 7) + StockId.GetHashCode();
}
          return _hashCode.Value;
        }
        partial void OnStockIdChanging(short value);
        
        partial void OnStockIdChanged();
        partial void OnNameChanging(string value);
        
        partial void OnNameChanged();
        partial void OnREPLIKAIDChanging(short value);
        
        partial void OnREPLIKAIDChanged();
        partial void OnBARVAChanging(int? value);
        
        partial void OnBARVAChanged();
        partial void OnKATEGORIEIDChanging(short value);
        
        partial void OnKATEGORIEIDChanged();
        partial void OnSKLCENASDPHChanging(short value);
        
        partial void OnSKLCENASDPHChanged();
        partial void OnObjSkupinaIDChanging(short? value);
        
        partial void OnObjSkupinaIDChanged();
        partial void OnRegionIDChanging(short? value);
        
        partial void OnRegionIDChanged();
        partial void OnProdejniDnyKasyChanging(string value);
        
        partial void OnProdejniDnyKasyChanged();
        partial void OnKONTROLAPCPVChanging(bool? value);
        
        partial void OnKONTROLAPCPVChanged();
        partial void OnActiveChanging(bool value);
        
        partial void OnActiveChanged();
        partial void OnSkcResortChanging(SkcResort value);

        partial void OnSkcResortChanged();
        
        #endregion
        public SkcStock()
        {
            this._REPLIKAID = 1;
            this._KATEGORIEID = 1;
            this._KONTROLAPCPV = false;
            this._Active = true;
            this._NomReceiptGroup = new HashSet<NomReceiptGroup>();
            this._SklGoods = new HashSet<SklGoodsBasic>();
            this._AccountingPeriods = new HashSet<SkcAccountingPeriod>();
            OnCreated();
        }

    
        /// <summary>
        /// Sloupec: SKLAD_ID
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("StockId", typeof(SkcStockSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(SkcStock), Tag="StockId")]
        public virtual short StockId
        {
            get
            {
                return this._StockId;
            }
            set
            {
                if (this._StockId != value)
                {
                    this.OnStockIdChanging(value);
                    this.SendPropertyChanging();
                    this._StockId = value;
                    this.SendPropertyChanged("StockId");
                    this.OnStockIdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: POPIS
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Name", typeof(SkcStockSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(SkcStock), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 50,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="Name")]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(SkcStock), Tag="Name")]
        public virtual string Name
        {
            get
            {
                return this._Name;
            }
            set
            {
                if (this._Name != value)
                {
                    this.OnNameChanging(value);
                    this.SendPropertyChanging();
                    this._Name = value;
                    this.SendPropertyChanged("Name");
                    this.OnNameChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: REPLIKA_ID
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("REPLIKAID", typeof(SkcStockSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(SkcStock), Tag="REPLIKAID")]
        public virtual short REPLIKAID
        {
            get
            {
                return this._REPLIKAID;
            }
            set
            {
                if (this._REPLIKAID != value)
                {
                    this.OnREPLIKAIDChanging(value);
                    this.SendPropertyChanging();
                    this._REPLIKAID = value;
                    this.SendPropertyChanged("REPLIKAID");
                    this.OnREPLIKAIDChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: BARVA
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("BARVA", typeof(SkcStockSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual int? BARVA
        {
            get
            {
                return this._BARVA;
            }
            set
            {
                if (this._BARVA != value)
                {
                    this.OnBARVAChanging(value);
                    this.SendPropertyChanging();
                    this._BARVA = value;
                    this.SendPropertyChanged("BARVA");
                    this.OnBARVAChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: KATEGORIE_ID
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("KATEGORIEID", typeof(SkcStockSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(SkcStock), Tag="KATEGORIEID")]
        public virtual short KATEGORIEID
        {
            get
            {
                return this._KATEGORIEID;
            }
            set
            {
                if (this._KATEGORIEID != value)
                {
                    this.OnKATEGORIEIDChanging(value);
                    this.SendPropertyChanging();
                    this._KATEGORIEID = value;
                    this.SendPropertyChanged("KATEGORIEID");
                    this.OnKATEGORIEIDChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: SKL_CENA_S_DPH
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("SKLCENASDPH", typeof(SkcStockSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(SkcStock), Tag="SKLCENASDPH")]
        public virtual short SKLCENASDPH
        {
            get
            {
                return this._SKLCENASDPH;
            }
            set
            {
                if (this._SKLCENASDPH != value)
                {
                    this.OnSKLCENASDPHChanging(value);
                    this.SendPropertyChanging();
                    this._SKLCENASDPH = value;
                    this.SendPropertyChanged("SKLCENASDPH");
                    this.OnSKLCENASDPHChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: objSkupinaID
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("ObjSkupinaID", typeof(SkcStockSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual short? ObjSkupinaID
        {
            get
            {
                return this._ObjSkupinaID;
            }
            set
            {
                if (this._ObjSkupinaID != value)
                {
                    this.OnObjSkupinaIDChanging(value);
                    this.SendPropertyChanging();
                    this._ObjSkupinaID = value;
                    this.SendPropertyChanged("ObjSkupinaID");
                    this.OnObjSkupinaIDChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: regionID
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("RegionID", typeof(SkcStockSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual short? RegionID
        {
            get
            {
                return this._RegionID;
            }
            set
            {
                if (this._RegionID != value)
                {
                    this.OnRegionIDChanging(value);
                    this.SendPropertyChanging();
                    this._RegionID = value;
                    this.SendPropertyChanged("RegionID");
                    this.OnRegionIDChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: prodejniDnyKasy
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("ProdejniDnyKasy", typeof(SkcStockSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(SkcStock), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 100,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="ProdejniDnyKasy")]
        public virtual string ProdejniDnyKasy
        {
            get
            {
                return this._ProdejniDnyKasy;
            }
            set
            {
                if (this._ProdejniDnyKasy != value)
                {
                    this.OnProdejniDnyKasyChanging(value);
                    this.SendPropertyChanging();
                    this._ProdejniDnyKasy = value;
                    this.SendPropertyChanged("ProdejniDnyKasy");
                    this.OnProdejniDnyKasyChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: KONTROLA_PC_PV
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("KONTROLAPCPV", typeof(SkcStockSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual bool? KONTROLAPCPV
        {
            get
            {
                return this._KONTROLAPCPV;
            }
            set
            {
                if (this._KONTROLAPCPV != value)
                {
                    this.OnKONTROLAPCPVChanging(value);
                    this.SendPropertyChanging();
                    this._KONTROLAPCPV = value;
                    this.SendPropertyChanged("KONTROLAPCPV");
                    this.OnKONTROLAPCPVChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: AKTIVNI
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Active", typeof(SkcStockSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(SkcStock), Tag="Active")]
        public virtual bool Active
        {
            get
            {
                return this._Active;
            }
            set
            {
                if (this._Active != value)
                {
                    this.OnActiveChanging(value);
                    this.SendPropertyChanging();
                    this._Active = value;
                    this.SendPropertyChanged("Active");
                    this.OnActiveChanged();
                }
            }
        }

        [Anete.Data.Attributes.EntitySRDisplayName("NomReceiptGroup", typeof(SkcStockSR))]
        [Anete.Data.Attributes.DbCollectionProperty]
        public virtual ISet<NomReceiptGroup> NomReceiptGroup
        {
            get
            {
                return this._NomReceiptGroup;
            }
            set
            {
                this._NomReceiptGroup = value;
            }
        }

    
        /// <summary>
        /// Sloupec: STREDISKO_ID
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("SkcResort", typeof(SkcStockSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual SkcResort SkcResort
        {
            get
            {
                return this._SkcResort;
            }
            set
            {
                if (this._SkcResort != value)
                {
                    this.OnSkcResortChanging(value);
                    this.SendPropertyChanging();
                    this._SkcResort = value;
                    this.SendPropertyChanged("SkcResort");
                    this.OnSkcResortChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: KOD_ZBOZI
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("SklGoods", typeof(SkcStockSR))]
        [Anete.Data.Attributes.DbCollectionProperty]
        public virtual ISet<SklGoodsBasic> SklGoods
        {
            get
            {
                return this._SklGoods;
            }
            set
            {
                this._SklGoods = value;
            }
        }

        [Anete.Data.Attributes.EntitySRDisplayName("AccountingPeriods", typeof(SkcStockSR))]
        [Anete.Data.Attributes.DbCollectionProperty]
        public virtual ISet<SkcAccountingPeriod> AccountingPeriods
        {
            get
            {
                return this._AccountingPeriods;
            }
            set
            {
                this._AccountingPeriods = value;
            }
        }
   
        public virtual event PropertyChangingEventHandler PropertyChanging;

	// Mam v bazove tride, nema smysl generovat
	// public virtual event PropertyChangedEventHandler PropertyChanged;

        protected virtual void SendPropertyChanging()
        {
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, emptyChangingEventArgs);
        }

        protected virtual void SendPropertyChanging(System.String propertyName) 
        {   	
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, new PropertyChangingEventArgs(propertyName));
        }

        protected virtual void SendPropertyChanged(System.String propertyName)
        {    
		// mam v bazove tride, proto musim volat takto jednoduse
		OnPropertyChanged(propertyName);
/*		        var handler = this.PropertyChanged;
            if (handler != null)
                handler(this, new PropertyChangedEventArgs(propertyName));*/
        }
    }

}
