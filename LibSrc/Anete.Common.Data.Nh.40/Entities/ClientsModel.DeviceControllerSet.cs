//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Anete.Common.Data.Nh.Entities
{

    /// <summary>
    /// Tabulka: dba.CFseznamSad
    /// </summary>
    public partial class DeviceControllerSet : Anete.Data.Nh.NhEntityBase, INotifyPropertyChanging, INotifyPropertyChanged {

        private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(System.String.Empty);

        private short _DeviceControllerSetId;

        private string _Name;

        private short _AppInstallationId;

        private string _Note;

        private ISet<DeviceController> _DeviceControllers;

        private ISet<OrderingSchema> _OrderingSchemas;

        private ISet<ServingSchema> _ServingSchemata;
    
        #region Extensibility Method Definitions
        
        partial void OnCreated();

        public override bool Equals(object obj)
        {
          DeviceControllerSet toCompare = obj as DeviceControllerSet;
          if (toCompare == null)
          {
            return false;
          }

          if (!Object.Equals(this.DeviceControllerSetId, toCompare.DeviceControllerSetId))
            return false;
         // pokud mam default primary key, znamena to, ze jedna o nove vytvorenou entitu, ktera jeste nema key prirazen. 
        // V takove pripade musim porovnat podle instance
        bool isDefault = true;
        if (this.DeviceControllerSetId != default(short))
        {
          isDefault = false;
        }
     

          if (isDefault)
          {
            return object.ReferenceEquals(toCompare, this);
          }     
          return true;
        }

	private int? _hashCode = null;
        public override int GetHashCode()
        {
	if (_hashCode == null)
          {
          _hashCode = 13;
          _hashCode = (_hashCode * 7) + DeviceControllerSetId.GetHashCode();
}
          return _hashCode.Value;
        }
        partial void OnDeviceControllerSetIdChanging(short value);
        
        partial void OnDeviceControllerSetIdChanged();
        partial void OnNameChanging(string value);
        
        partial void OnNameChanged();
        partial void OnAppInstallationIdChanging(short value);
        
        partial void OnAppInstallationIdChanged();
        partial void OnNoteChanging(string value);
        
        partial void OnNoteChanged();
        
        #endregion
        public DeviceControllerSet()
        {
            this._DeviceControllerSetId = 0;
            this._AppInstallationId = 0;
            this._DeviceControllers = new HashSet<DeviceController>();
            this._OrderingSchemas = new HashSet<OrderingSchema>();
            this._ServingSchemata = new HashSet<ServingSchema>();
            OnCreated();
        }

    
        /// <summary>
        /// Sloupec: id_sada
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("DeviceControllerSetId", typeof(DeviceControllerSetSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(DeviceControllerSet), Tag="DeviceControllerSetId")]
        public virtual short DeviceControllerSetId
        {
            get
            {
                return this._DeviceControllerSetId;
            }
            set
            {
                if (this._DeviceControllerSetId != value)
                {
                    this.OnDeviceControllerSetIdChanging(value);
                    this.SendPropertyChanging();
                    this._DeviceControllerSetId = value;
                    this.SendPropertyChanged("DeviceControllerSetId");
                    this.OnDeviceControllerSetIdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: popis
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Name", typeof(DeviceControllerSetSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(DeviceControllerSet), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 50,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="Name")]
        public virtual string Name
        {
            get
            {
                return this._Name;
            }
            set
            {
                if (this._Name != value)
                {
                    this.OnNameChanging(value);
                    this.SendPropertyChanging();
                    this._Name = value;
                    this.SendPropertyChanged("Name");
                    this.OnNameChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: id_zarizeni
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("AppInstallationId", typeof(DeviceControllerSetSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(DeviceControllerSet), Tag="AppInstallationId")]
        public virtual short AppInstallationId
        {
            get
            {
                return this._AppInstallationId;
            }
            set
            {
                if (this._AppInstallationId != value)
                {
                    this.OnAppInstallationIdChanging(value);
                    this.SendPropertyChanging();
                    this._AppInstallationId = value;
                    this.SendPropertyChanged("AppInstallationId");
                    this.OnAppInstallationIdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: poznamka
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Note", typeof(DeviceControllerSetSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(DeviceControllerSet), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 250,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="Note")]
        public virtual string Note
        {
            get
            {
                return this._Note;
            }
            set
            {
                if (this._Note != value)
                {
                    this.OnNoteChanging(value);
                    this.SendPropertyChanging();
                    this._Note = value;
                    this.SendPropertyChanged("Note");
                    this.OnNoteChanged();
                }
            }
        }

        [Anete.Data.Attributes.EntitySRDisplayName("DeviceControllers", typeof(DeviceControllerSetSR))]
        [Anete.Data.Attributes.DbCollectionProperty]
        public virtual ISet<DeviceController> DeviceControllers
        {
            get
            {
                return this._DeviceControllers;
            }
            set
            {
                this._DeviceControllers = value;
            }
        }

        [Anete.Data.Attributes.EntitySRDisplayName("OrderingSchemas", typeof(DeviceControllerSetSR))]
        [Anete.Data.Attributes.DbCollectionProperty]
        public virtual ISet<OrderingSchema> OrderingSchemas
        {
            get
            {
                return this._OrderingSchemas;
            }
            set
            {
                this._OrderingSchemas = value;
            }
        }

        [Anete.Data.Attributes.EntitySRDisplayName("ServingSchemata", typeof(DeviceControllerSetSR))]
        [Anete.Data.Attributes.DbCollectionProperty]
        public virtual ISet<ServingSchema> ServingSchemata
        {
            get
            {
                return this._ServingSchemata;
            }
            set
            {
                this._ServingSchemata = value;
            }
        }
   
        public virtual event PropertyChangingEventHandler PropertyChanging;

	// Mam v bazove tride, nema smysl generovat
	// public virtual event PropertyChangedEventHandler PropertyChanged;

        protected virtual void SendPropertyChanging()
        {
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, emptyChangingEventArgs);
        }

        protected virtual void SendPropertyChanging(System.String propertyName) 
        {   	
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, new PropertyChangingEventArgs(propertyName));
        }

        protected virtual void SendPropertyChanged(System.String propertyName)
        {    
		// mam v bazove tride, proto musim volat takto jednoduse
		OnPropertyChanged(propertyName);
/*		        var handler = this.PropertyChanged;
            if (handler != null)
                handler(this, new PropertyChangedEventArgs(propertyName));*/
        }
    }

}
