using System;
using System.Collections.Generic;

namespace Anete.Utils.ComponentModel
{
    /// <summary>
    /// Extension metody pro IEntitySynchronization
    /// </summary>
    public static class IEntitySynchronizationLoggerExt
    {
        /// <summary>
        /// <PERSON><PERSON>i text, ktery je vhodny k zalogovani
        /// </summary>
        /// <param name="logger"></param>
        /// <returns></returns>
        public static IEnumerable<string> GetLogLines(this IEntitySynchronizationLogger logger)
        {
            // po synchronizaci zobrazim log            
            foreach (KeyValuePair<object, IEnumerable<IEntityAction>> action in logger.Actions)
            {
                yield return string.Empty;
                yield return action.Key.ToString();
                yield return "-------------------------------------------------------------------------------------------";
                foreach (IEntityAction item in action.Value)
                {
                	yield return item.ToString();
                }                
            }
        }
    }
}
