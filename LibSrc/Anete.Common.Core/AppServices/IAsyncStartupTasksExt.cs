using System;
using System.Collections.Generic;
using System.Linq;
using Anete.Common.Core.Interface.AppServices;

namespace Anete.Common.Core.AppServices
{
    /// <summary>
    /// Extension metody pro AsyncStartupTasks
    /// </summary>
    public static class IAsyncStartupTasksExt
	{

		/// <summary>
		/// Registrace tasku a jeho okamzite spusteni pomoci delegata
		/// </summary>
		/// <param name="startupTask">Task</param>
		/// <param name="name">Název tasku, pomuze identifikovat tisk pri logovani</param>
		public static void Start(this IAsyncStartupTasks tasks, Action actionDelegate, string name = null)
		{
			tasks.Start(new DelegatedStartupTask(actionDelegate, name));
		}

	}
}
