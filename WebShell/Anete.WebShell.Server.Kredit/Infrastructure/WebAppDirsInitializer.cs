using Anete.Common.Core.AppUtils;
using Anete.Utils.AppServices;
using System.IO;

namespace Anete.WebShell.Server.Kredit.Infrastructure
{
	//nelze pouzit DesktopAppDirsInitializer, pri behu v iis to nic nevrati pro local/roaming appdata
	//a do systemtempdir nemaji uzivatele iis pristup
	internal class WebAppDirsInitializer : AppDirsInitializerBase
	{
		public WebAppDirsInitializer(IAppDirsService appDirsService)
			: base(appDirsService)
		{
		}

		protected override void InitializeInt()
		{
			try { Directory.Delete(AppDirsService.CommonAppTempDir, true); }
			catch { }

			InitReadWriteDirs(
				AppDirsService.CommonAppDataDir,
				AppDirsService.CommonAppLogDir,
				AppDirsService.CommonAppTempDir
			);
		}
	}
}