using System;
using System.Data;
using System.Reflection;
using Anete.Resources;
using log4net;
using Anete.Log4Net.Core;
using System.Data.SqlClient;
using Anete.Utils.AppServices;
using Unity;
using Anete.Common.Data.AppServices;
using Anete.Common.Data.Interface;
using Anete.Common.Data.Interface.AppServices;

namespace Anete.Common.Data
{
    /// <summary>
    /// Adapter pro jednoduche natazeni dat pomoci TableAdapteru do tabulky 
    /// Pozor: Metoda Fill table adapteru musi mit prave jeden parametr typu TTable.
    /// Automaticky pouziva pripojeni k databazi Kredit
    /// </summary>
    /// <typeparam name="TTable">Typ tabulky</typeparam>
    /// <typeparam name="TTableAdapter">Typ table adapteru</typeparam>
    public static class KreditSimpleDataTableLoader<TTable, TTableAdapter>
        where TTableAdapter : IDisposable, new()
        where TTable : DataTable
    {

        #region public static methods...
        /// <summary>
        /// Genericka metoda pro natazeni dat pomoci TableAdapteru do tabulky.
        /// Automaticky pouziva pripojeni k databazi Kredit
        /// Pozor: Metoda Fill table adapteru nesmi mit zadne parametry a table adapter musi byt podeden od SqlTransTableAdapter
        /// nebo mit property Connection.
        /// </summary>
        /// <param name="dataTable">Datova tabulka, do ktere natahnout data.</param>
        /// <returns></returns>
        public static int LoadData(TTable dataTable)
        {
            using (SqlConnection conn = DependencyContainer.Instance.Resolve<IDbConnectionProvider>().CreateKreditConnection())
            {
                return SimpleDataTableLoader<TTable, TTableAdapter>.LoadData(dataTable, conn);
            }
        }
        #endregion
    }
}
