using System;

namespace Anete.Common.Core.Interface.Validators
{
    /// <summary>
    /// Atributem se oznaci metoda, ktera slouzi pro SelfValidaci. Rika, jake Tagy dana metoda poskytuje. Pomoci tagu lze provazat vysledek validace s UI.
    /// </summary>
    [AttributeUsage(AttributeTargets.Method, AllowMultiple = true, Inherited = true)]
    public class SelfValidationTagsAttribute : Attribute
    {
        /// <summary>
        /// Initializes a new instance of the SelfValidationTags class.
        /// </summary>
        public SelfValidationTagsAttribute(params string[] validationTags)
        {
            ValidationTags = validationTags;
        }

        /// <summary>
        /// Seznam ValidationTag
        /// </summary>
        public string[] ValidationTags { get; private set; }
    }
}
