using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Anete.Common.Core.Interface.Business.InstallationParameters.ReportGroups
{
	/// <summary>
	/// Skupina pro vytvoreni reportu instalacniho parametru
	/// </summary>
	public abstract class InstallationParameterReportGroupBase
	{
		public InstallationParameterReportGroupBase(string caption, int order)
		{
			Order = order;
			Caption = caption;
		}

		public string Caption { get; }
		public int Order { get; }

		public virtual bool Visible => true;

		public override int GetHashCode()
		{
			// trida je imutable, budu se ridit primo datovym typem
			return this.GetType().GetHashCode();
		}

		public override bool Equals(object obj)
		{
			return obj?.GetType() == this.GetType();
		}
	}
}
