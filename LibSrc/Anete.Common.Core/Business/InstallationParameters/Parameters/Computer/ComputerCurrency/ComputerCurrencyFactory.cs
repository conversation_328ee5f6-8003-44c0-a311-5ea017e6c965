using Anete.Common.Core.Business.InstallationParameters.Factories;
using Anete.Common.Core.Interface.Business.InstallationParameters.Factories;
using Anete.Common.Core.Interface.Business.InstallationParameters.Parameters;
using System;
using System.Globalization;
using System.Linq;

namespace Anete.Common.Core.Business.InstallationParameters.Parameters.Computer.ComputerCurrency
{
		public class ComputerCurrencyFactory : InstallationParameterFactoryBase, IComputerParameterFactory
    {
        public IComputerInstallationParameter CreateFromComputer(Guid computerId)
        {
            string currencySymbol = NumberFormatInfo.CurrentInfo.CurrencySymbol;
            return new ComputerCurrencyParameter(computerId, new ComputerCurrencyParameterValue(currencySymbol));
        }


    }
}
