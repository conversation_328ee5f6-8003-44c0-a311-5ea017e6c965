//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Anete.Common.Data.Nh.Entities
{

    /// <summary>
    /// Tabulka: dba.Skupiny
    /// </summary>
    public partial class ClientGroup : Anete.Data.Nh.NhEntityBase, INotifyPropertyChanging, INotifyPropertyChanged {

        private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(System.String.Empty);

        private int _ClientGroupId;

        private string _Name;

        private short _KMigrace;

        private short? _DefaultVoucherCount;

        private short _VouchersAreVisible;

        private short _DefaultSubsidyCountDayLimit;

        private string _Mapagrafu;

        private short? _MealSize;

        private byte _DepositVatId;

        private string _Note;

        private bool _Dietary;

        private bool _EetEnabled;

        private bool _ElectronicReceiptEnabled;

        private bool _IsAnonymous;

        private bool _UnissuedSecSubsidy;

        private bool _Active;

        private Canteen _DefaultCanteen;

        private ISet<ClientRegister> _ClientRegisters;

        private CanteenAccessCategory _CanteenAccessCategory;

        private SubsidyCategory _PrimarySubsidyCategory;

        private SubsidyCategory _SecondarySubsidyCategory;

        private DayAccessCategory _DayAccessCategory;

        private DeviceControllerSetAccessCategory _DeviceControllerSetAccessCategory;

        private MenuCategory _MenuCategory;

        private PriceCategory _PriceCategory;

        private AccountTemplate _AccountTemplate;

        private MealRestriction _MealRestriction;

        private ISet<ClientRegisterHistory> _ClientRegisterHistories;

        private ISet<ProviderGroupCategory> _ProviderGroupCategories;

        private ISet<MonthRestriction> _MonthRestrictions;

        private ISet<AgeGroup> _AgeGroups;

        private ISet<CampaignClientGroup> _CampaignClientGroups;
    
        #region Extensibility Method Definitions
        
        partial void OnCreated();

        public override bool Equals(object obj)
        {
          ClientGroup toCompare = obj as ClientGroup;
          if (toCompare == null)
          {
            return false;
          }

          if (!Object.Equals(this.ClientGroupId, toCompare.ClientGroupId))
            return false;
         // pokud mam default primary key, znamena to, ze jedna o nove vytvorenou entitu, ktera jeste nema key prirazen. 
        // V takove pripade musim porovnat podle instance
        bool isDefault = true;
        if (this.ClientGroupId != default(int))
        {
          isDefault = false;
        }
     

          if (isDefault)
          {
            return object.ReferenceEquals(toCompare, this);
          }     
          return true;
        }

	private int? _hashCode = null;
        public override int GetHashCode()
        {
	if (_hashCode == null)
          {
          _hashCode = 13;
          _hashCode = (_hashCode * 7) + ClientGroupId.GetHashCode();
}
          return _hashCode.Value;
        }
        partial void OnClientGroupIdChanging(int value);
        
        partial void OnClientGroupIdChanged();
        partial void OnNameChanging(string value);
        
        partial void OnNameChanged();
        partial void OnKMigraceChanging(short value);
        
        partial void OnKMigraceChanged();
        partial void OnDefaultVoucherCountChanging(short? value);
        
        partial void OnDefaultVoucherCountChanged();
        partial void OnVouchersAreVisibleChanging(short value);
        
        partial void OnVouchersAreVisibleChanged();
        partial void OnDefaultSubsidyCountDayLimitChanging(short value);
        
        partial void OnDefaultSubsidyCountDayLimitChanged();
        partial void OnMapagrafuChanging(string value);
        
        partial void OnMapagrafuChanged();
        partial void OnMealSizeChanging(short? value);
        
        partial void OnMealSizeChanged();
        partial void OnDepositVatIdChanging(byte value);
        
        partial void OnDepositVatIdChanged();
        partial void OnNoteChanging(string value);
        
        partial void OnNoteChanged();
        partial void OnDietaryChanging(bool value);
        
        partial void OnDietaryChanged();
        partial void OnEetEnabledChanging(bool value);
        
        partial void OnEetEnabledChanged();
        partial void OnElectronicReceiptEnabledChanging(bool value);
        
        partial void OnElectronicReceiptEnabledChanged();
        partial void OnIsAnonymousChanging(bool value);
        
        partial void OnIsAnonymousChanged();
        partial void OnUnissuedSecSubsidyChanging(bool value);
        
        partial void OnUnissuedSecSubsidyChanged();
        partial void OnActiveChanging(bool value);
        
        partial void OnActiveChanged();
        partial void OnDefaultCanteenChanging(Canteen value);

        partial void OnDefaultCanteenChanged();
        partial void OnCanteenAccessCategoryChanging(CanteenAccessCategory value);

        partial void OnCanteenAccessCategoryChanged();
        partial void OnPrimarySubsidyCategoryChanging(SubsidyCategory value);

        partial void OnPrimarySubsidyCategoryChanged();
        partial void OnSecondarySubsidyCategoryChanging(SubsidyCategory value);

        partial void OnSecondarySubsidyCategoryChanged();
        partial void OnDayAccessCategoryChanging(DayAccessCategory value);

        partial void OnDayAccessCategoryChanged();
        partial void OnDeviceControllerSetAccessCategoryChanging(DeviceControllerSetAccessCategory value);

        partial void OnDeviceControllerSetAccessCategoryChanged();
        partial void OnMenuCategoryChanging(MenuCategory value);

        partial void OnMenuCategoryChanged();
        partial void OnPriceCategoryChanging(PriceCategory value);

        partial void OnPriceCategoryChanged();
        partial void OnAccountTemplateChanging(AccountTemplate value);

        partial void OnAccountTemplateChanged();
        partial void OnMealRestrictionChanging(MealRestriction value);

        partial void OnMealRestrictionChanged();
        
        #endregion
        public ClientGroup()
        {
            this._KMigrace = 1;
            this._VouchersAreVisible = 0;
            this._DefaultSubsidyCountDayLimit = 1;
            this._DepositVatId = 0;
            this._EetEnabled = false;
            this._ElectronicReceiptEnabled = false;
            this._IsAnonymous = false;
            this._UnissuedSecSubsidy = false;
            this._Active = true;
            this._ClientRegisters = new HashSet<ClientRegister>();
            this._ClientRegisterHistories = new HashSet<ClientRegisterHistory>();
            this._ProviderGroupCategories = new HashSet<ProviderGroupCategory>();
            this._MonthRestrictions = new HashSet<MonthRestriction>();
            this._AgeGroups = new HashSet<AgeGroup>();
            this._CampaignClientGroups = new HashSet<CampaignClientGroup>();
            OnCreated();
        }

    
        /// <summary>
        /// Sloupec: id_sk
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("ClientGroupId", typeof(ClientGroupSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(ClientGroup), Tag="ClientGroupId")]
        public virtual int ClientGroupId
        {
            get
            {
                return this._ClientGroupId;
            }
            set
            {
                if (this._ClientGroupId != value)
                {
                    this.OnClientGroupIdChanging(value);
                    this.SendPropertyChanging();
                    this._ClientGroupId = value;
                    this.SendPropertyChanged("ClientGroupId");
                    this.OnClientGroupIdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: popis
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Name", typeof(ClientGroupSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(ClientGroup), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 50,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="Name")]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(ClientGroup), Tag="Name")]
        public virtual string Name
        {
            get
            {
                return this._Name;
            }
            set
            {
                if (this._Name != value)
                {
                    this.OnNameChanging(value);
                    this.SendPropertyChanging();
                    this._Name = value;
                    this.SendPropertyChanged("Name");
                    this.OnNameChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: k_migrace
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("KMigrace", typeof(ClientGroupSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(ClientGroup), Tag="KMigrace")]
        public virtual short KMigrace
        {
            get
            {
                return this._KMigrace;
            }
            set
            {
                if (this._KMigrace != value)
                {
                    this.OnKMigraceChanging(value);
                    this.SendPropertyChanging();
                    this._KMigrace = value;
                    this.SendPropertyChanged("KMigrace");
                    this.OnKMigraceChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: maxdp
        /// Implicicitní počet bonů/den
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("DefaultVoucherCount", typeof(ClientGroupSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual short? DefaultVoucherCount
        {
            get
            {
                return this._DefaultVoucherCount;
            }
            set
            {
                if (this._DefaultVoucherCount != value)
                {
                    this.OnDefaultVoucherCountChanging(value);
                    this.SendPropertyChanging();
                    this._DefaultVoucherCount = value;
                    this.SendPropertyChanged("DefaultVoucherCount");
                    this.OnDefaultVoucherCountChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: povol_zobrazenibonu
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("VouchersAreVisible", typeof(ClientGroupSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(ClientGroup), Tag="VouchersAreVisible")]
        public virtual short VouchersAreVisible
        {
            get
            {
                return this._VouchersAreVisible;
            }
            set
            {
                if (this._VouchersAreVisible != value)
                {
                    this.OnVouchersAreVisibleChanging(value);
                    this.SendPropertyChanging();
                    this._VouchersAreVisible = value;
                    this.SendPropertyChanged("VouchersAreVisible");
                    this.OnVouchersAreVisibleChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: dlimit_kd1
        /// Implicitní denní počet dotací
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("DefaultSubsidyCountDayLimit", typeof(ClientGroupSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual short DefaultSubsidyCountDayLimit
        {
            get
            {
                return this._DefaultSubsidyCountDayLimit;
            }
            set
            {
                if (this._DefaultSubsidyCountDayLimit != value)
                {
                    this.OnDefaultSubsidyCountDayLimitChanging(value);
                    this.SendPropertyChanging();
                    this._DefaultSubsidyCountDayLimit = value;
                    this.SendPropertyChanged("DefaultSubsidyCountDayLimit");
                    this.OnDefaultSubsidyCountDayLimitChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: mapagrafu
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Mapagrafu", typeof(ClientGroupSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(ClientGroup), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 100,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="Mapagrafu")]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(ClientGroup), Tag="Mapagrafu")]
        public virtual string Mapagrafu
        {
            get
            {
                return this._Mapagrafu;
            }
            set
            {
                if (this._Mapagrafu != value)
                {
                    this.OnMapagrafuChanging(value);
                    this.SendPropertyChanging();
                    this._Mapagrafu = value;
                    this.SendPropertyChanged("Mapagrafu");
                    this.OnMapagrafuChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: strav_limit
        /// Definuje velikost porce v rozsahu 1..4. V Kreditu se porce oznacuji pismeny ABCD.
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("MealSize", typeof(ClientGroupSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual short? MealSize
        {
            get
            {
                return this._MealSize;
            }
            set
            {
                if (this._MealSize != value)
                {
                    this.OnMealSizeChanging(value);
                    this.SendPropertyChanging();
                    this._MealSize = value;
                    this.SendPropertyChanged("MealSize");
                    this.OnMealSizeChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: id_dph_zal
        /// Sazba DPH u záloh. Už se nepoužívá.
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("DepositVatId", typeof(ClientGroupSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(ClientGroup), Tag="DepositVatId")]
        public virtual byte DepositVatId
        {
            get
            {
                return this._DepositVatId;
            }
            set
            {
                if (this._DepositVatId != value)
                {
                    this.OnDepositVatIdChanging(value);
                    this.SendPropertyChanging();
                    this._DepositVatId = value;
                    this.SendPropertyChanged("DepositVatId");
                    this.OnDepositVatIdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: poznamka
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Note", typeof(ClientGroupSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(ClientGroup), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 250,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="Note")]
        public virtual string Note
        {
            get
            {
                return this._Note;
            }
            set
            {
                if (this._Note != value)
                {
                    this.OnNoteChanging(value);
                    this.SendPropertyChanging();
                    this._Note = value;
                    this.SendPropertyChanged("Note");
                    this.OnNoteChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: dietni
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Dietary", typeof(ClientGroupSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(ClientGroup), Tag="Dietary")]
        public virtual bool Dietary
        {
            get
            {
                return this._Dietary;
            }
            set
            {
                if (this._Dietary != value)
                {
                    this.OnDietaryChanging(value);
                    this.SendPropertyChanging();
                    this._Dietary = value;
                    this.SendPropertyChanged("Dietary");
                    this.OnDietaryChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: EetZalohy
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("EetEnabled", typeof(ClientGroupSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(ClientGroup), Tag="EetEnabled")]
        public virtual bool EetEnabled
        {
            get
            {
                return this._EetEnabled;
            }
            set
            {
                if (this._EetEnabled != value)
                {
                    this.OnEetEnabledChanging(value);
                    this.SendPropertyChanging();
                    this._EetEnabled = value;
                    this.SendPropertyChanged("EetEnabled");
                    this.OnEetEnabledChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: ElektrUctenky
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("ElectronicReceiptEnabled", typeof(ClientGroupSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(ClientGroup), Tag="ElectronicReceiptEnabled")]
        public virtual bool ElectronicReceiptEnabled
        {
            get
            {
                return this._ElectronicReceiptEnabled;
            }
            set
            {
                if (this._ElectronicReceiptEnabled != value)
                {
                    this.OnElectronicReceiptEnabledChanging(value);
                    this.SendPropertyChanging();
                    this._ElectronicReceiptEnabled = value;
                    this.SendPropertyChanged("ElectronicReceiptEnabled");
                    this.OnElectronicReceiptEnabledChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: pro_anonimizovane
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("IsAnonymous", typeof(ClientGroupSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(ClientGroup), Tag="IsAnonymous")]
        public virtual bool IsAnonymous
        {
            get
            {
                return this._IsAnonymous;
            }
            set
            {
                if (this._IsAnonymous != value)
                {
                    this.OnIsAnonymousChanging(value);
                    this.SendPropertyChanging();
                    this._IsAnonymous = value;
                    this.SendPropertyChanged("IsAnonymous");
                    this.OnIsAnonymousChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: nevydaneDoSecKD
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("UnissuedSecSubsidy", typeof(ClientGroupSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(ClientGroup), Tag="UnissuedSecSubsidy")]
        public virtual bool UnissuedSecSubsidy
        {
            get
            {
                return this._UnissuedSecSubsidy;
            }
            set
            {
                if (this._UnissuedSecSubsidy != value)
                {
                    this.OnUnissuedSecSubsidyChanging(value);
                    this.SendPropertyChanging();
                    this._UnissuedSecSubsidy = value;
                    this.SendPropertyChanged("UnissuedSecSubsidy");
                    this.OnUnissuedSecSubsidyChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: aktivni
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Active", typeof(ClientGroupSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(ClientGroup), Tag="Active")]
        public virtual bool Active
        {
            get
            {
                return this._Active;
            }
            set
            {
                if (this._Active != value)
                {
                    this.OnActiveChanging(value);
                    this.SendPropertyChanging();
                    this._Active = value;
                    this.SendPropertyChanged("Active");
                    this.OnActiveChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: id_vydejna
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("DefaultCanteen", typeof(ClientGroupSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual Canteen DefaultCanteen
        {
            get
            {
                return this._DefaultCanteen;
            }
            set
            {
                if (this._DefaultCanteen != value)
                {
                    this.OnDefaultCanteenChanging(value);
                    this.SendPropertyChanging();
                    this._DefaultCanteen = value;
                    this.SendPropertyChanged("DefaultCanteen");
                    this.OnDefaultCanteenChanged();
                }
            }
        }

        [Anete.Data.Attributes.EntitySRDisplayName("ClientRegisters", typeof(ClientGroupSR))]
        [Anete.Data.Attributes.DbCollectionProperty]
        public virtual ISet<ClientRegister> ClientRegisters
        {
            get
            {
                return this._ClientRegisters;
            }
            set
            {
                this._ClientRegisters = value;
            }
        }

    
        /// <summary>
        /// Sloupec: k_migracevydejny
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("CanteenAccessCategory", typeof(ClientGroupSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual CanteenAccessCategory CanteenAccessCategory
        {
            get
            {
                return this._CanteenAccessCategory;
            }
            set
            {
                if (this._CanteenAccessCategory != value)
                {
                    this.OnCanteenAccessCategoryChanging(value);
                    this.SendPropertyChanging();
                    this._CanteenAccessCategory = value;
                    this.SendPropertyChanged("CanteenAccessCategory");
                    this.OnCanteenAccessCategoryChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: k_dotace
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("PrimarySubsidyCategory", typeof(ClientGroupSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual SubsidyCategory PrimarySubsidyCategory
        {
            get
            {
                return this._PrimarySubsidyCategory;
            }
            set
            {
                if (this._PrimarySubsidyCategory != value)
                {
                    this.OnPrimarySubsidyCategoryChanging(value);
                    this.SendPropertyChanging();
                    this._PrimarySubsidyCategory = value;
                    this.SendPropertyChanged("PrimarySubsidyCategory");
                    this.OnPrimarySubsidyCategoryChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: k_dotace2
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("SecondarySubsidyCategory", typeof(ClientGroupSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual SubsidyCategory SecondarySubsidyCategory
        {
            get
            {
                return this._SecondarySubsidyCategory;
            }
            set
            {
                if (this._SecondarySubsidyCategory != value)
                {
                    this.OnSecondarySubsidyCategoryChanging(value);
                    this.SendPropertyChanging();
                    this._SecondarySubsidyCategory = value;
                    this.SendPropertyChanged("SecondarySubsidyCategory");
                    this.OnSecondarySubsidyCategoryChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: k_pristup
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("DayAccessCategory", typeof(ClientGroupSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual DayAccessCategory DayAccessCategory
        {
            get
            {
                return this._DayAccessCategory;
            }
            set
            {
                if (this._DayAccessCategory != value)
                {
                    this.OnDayAccessCategoryChanging(value);
                    this.SendPropertyChanging();
                    this._DayAccessCategory = value;
                    this.SendPropertyChanged("DayAccessCategory");
                    this.OnDayAccessCategoryChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: k_pristupMisto
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("DeviceControllerSetAccessCategory", typeof(ClientGroupSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual DeviceControllerSetAccessCategory DeviceControllerSetAccessCategory
        {
            get
            {
                return this._DeviceControllerSetAccessCategory;
            }
            set
            {
                if (this._DeviceControllerSetAccessCategory != value)
                {
                    this.OnDeviceControllerSetAccessCategoryChanging(value);
                    this.SendPropertyChanging();
                    this._DeviceControllerSetAccessCategory = value;
                    this.SendPropertyChanged("DeviceControllerSetAccessCategory");
                    this.OnDeviceControllerSetAccessCategoryChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: k_jidelnicek
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("MenuCategory", typeof(ClientGroupSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual MenuCategory MenuCategory
        {
            get
            {
                return this._MenuCategory;
            }
            set
            {
                if (this._MenuCategory != value)
                {
                    this.OnMenuCategoryChanging(value);
                    this.SendPropertyChanging();
                    this._MenuCategory = value;
                    this.SendPropertyChanged("MenuCategory");
                    this.OnMenuCategoryChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: CenovaKategorie
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("PriceCategory", typeof(ClientGroupSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual PriceCategory PriceCategory
        {
            get
            {
                return this._PriceCategory;
            }
            set
            {
                if (this._PriceCategory != value)
                {
                    this.OnPriceCategoryChanging(value);
                    this.SendPropertyChanging();
                    this._PriceCategory = value;
                    this.SendPropertyChanged("PriceCategory");
                    this.OnPriceCategoryChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: typ_uctu
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("AccountTemplate", typeof(ClientGroupSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual AccountTemplate AccountTemplate
        {
            get
            {
                return this._AccountTemplate;
            }
            set
            {
                if (this._AccountTemplate != value)
                {
                    this.OnAccountTemplateChanging(value);
                    this.SendPropertyChanging();
                    this._AccountTemplate = value;
                    this.SendPropertyChanged("AccountTemplate");
                    this.OnAccountTemplateChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: PovoleneKombinaceJidel
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("MealRestriction", typeof(ClientGroupSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual MealRestriction MealRestriction
        {
            get
            {
                return this._MealRestriction;
            }
            set
            {
                if (this._MealRestriction != value)
                {
                    this.OnMealRestrictionChanging(value);
                    this.SendPropertyChanging();
                    this._MealRestriction = value;
                    this.SendPropertyChanged("MealRestriction");
                    this.OnMealRestrictionChanged();
                }
            }
        }

        [Anete.Data.Attributes.EntitySRDisplayName("ClientRegisterHistories", typeof(ClientGroupSR))]
        [Anete.Data.Attributes.DbCollectionProperty]
        public virtual ISet<ClientRegisterHistory> ClientRegisterHistories
        {
            get
            {
                return this._ClientRegisterHistories;
            }
            set
            {
                this._ClientRegisterHistories = value;
            }
        }

        [Anete.Data.Attributes.EntitySRDisplayName("ProviderGroupCategories", typeof(ClientGroupSR))]
        [Anete.Data.Attributes.DbCollectionProperty]
        public virtual ISet<ProviderGroupCategory> ProviderGroupCategories
        {
            get
            {
                return this._ProviderGroupCategories;
            }
            set
            {
                this._ProviderGroupCategories = value;
            }
        }

        [Anete.Data.Attributes.EntitySRDisplayName("MonthRestrictions", typeof(ClientGroupSR))]
        [Anete.Data.Attributes.DbCollectionProperty]
        public virtual ISet<MonthRestriction> MonthRestrictions
        {
            get
            {
                return this._MonthRestrictions;
            }
            set
            {
                this._MonthRestrictions = value;
            }
        }

        [Anete.Data.Attributes.EntitySRDisplayName("AgeGroups", typeof(ClientGroupSR))]
        [Anete.Data.Attributes.DbCollectionProperty]
        public virtual ISet<AgeGroup> AgeGroups
        {
            get
            {
                return this._AgeGroups;
            }
            set
            {
                this._AgeGroups = value;
            }
        }

        [Anete.Data.Attributes.EntitySRDisplayName("CampaignClientGroups", typeof(ClientGroupSR))]
        [Anete.Data.Attributes.DbCollectionProperty]
        public virtual ISet<CampaignClientGroup> CampaignClientGroups
        {
            get
            {
                return this._CampaignClientGroups;
            }
            set
            {
                this._CampaignClientGroups = value;
            }
        }
   
        public virtual event PropertyChangingEventHandler PropertyChanging;

	// Mam v bazove tride, nema smysl generovat
	// public virtual event PropertyChangedEventHandler PropertyChanged;

        protected virtual void SendPropertyChanging()
        {
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, emptyChangingEventArgs);
        }

        protected virtual void SendPropertyChanging(System.String propertyName) 
        {   	
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, new PropertyChangingEventArgs(propertyName));
        }

        protected virtual void SendPropertyChanged(System.String propertyName)
        {    
		// mam v bazove tride, proto musim volat takto jednoduse
		OnPropertyChanged(propertyName);
/*		        var handler = this.PropertyChanged;
            if (handler != null)
                handler(this, new PropertyChangedEventArgs(propertyName));*/
        }
    }

}
