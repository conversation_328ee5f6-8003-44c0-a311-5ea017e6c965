using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Reflection;
using System.Runtime.Serialization;
using System.Xml.Serialization;
using System.Linq;
using Anete.Utils.ComponentModel.Attributes;
using System.Collections;
using Anete.Utils.Extensions;
#if !FW_Ver2
using System.Linq.Expressions;
#endif

namespace Anete.Utils.Reflection.ClassReports
{

    /// <summary>
    /// Operace s ItemReport
    /// </summary>
    public static class ItemReportUtils
    {

        /// <summary>
        /// Vraci seznam vsech IItemReport, ktere patri dane konfiguracni tride nebo vnorene tride.
        /// </summary>
        /// <param name="itemObject">Konfiguracni trida nebo vnorena trida</param>
        /// <returns></returns>
        public static IEnumerable<IItemReport> GetItemsFromObject(object itemObject)
        {
            return GetItemsFromObject(itemObject, ClassReportOptions.None);
        }

        /// <summary>
        /// Vraci seznam vsech IItemReport, ktere patri dane konfiguracni tride nebo vnorene tride.
        /// </summary>
        /// <param name="itemObject">Konfiguracni trida nebo vnorena trida</param>
        /// <param name="options">Parametry generovani</param>
        /// <returns></returns>
        public static IEnumerable<IItemReport> GetItemsFromObject(object itemObject, ClassReportOptions options)
        {
            if (itemObject == null)
            {
                yield break;
            }

            // pro kazdou property musim vytvorit PropertyReport
            // PropertyPath.GetAllPropertiesPath(itemObject, true)
            foreach (PropertyInfo propertyInfo in itemObject.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance))
            {
                //object propertyOwner;
                //PropertyInfo propertyInfo = PropertyUtils.GetPropertyInfo(propertyName, itemObject, out propertyOwner);
                // do seznamu pridam pouze property, ktere nemaji atribut NotReportedAttribute
                if (propertyInfo.GetAttribute<NotReportedAttribute>(true) == null)
                {
                    BrowsableAttribute browsable = propertyInfo.GetAttribute<BrowsableAttribute>();
                    if ((options & ClassReportOptions.IncludeNonBrowsableProperty) == 0 &&
                        browsable != null && browsable.Browsable == false)
                    {
                        // property, ktera ma browsable false prekakuju
                        continue;
                    }

                    if ((options & ClassReportOptions.IncludeReadOnlyProperty) == 0 && !propertyInfo.CanWrite)
                    {
                        continue;
                    }

                    // vynecha property, kde metoda set neni public => parametr false
                    if ((options & ClassReportOptions.IncludeReadOnlyProperty) == 0 && propertyInfo.GetSetMethod(false) == null)
                    {
                        continue;
                    }

					// vynech� property, kde get nen� public (z takov� property nelze pre��st hodnotu � skon�� v�jimkou)
					if (propertyInfo.GetGetMethod(false) == null)
					{
						continue;
					}

                    // vynecha indexovane property
                    if (propertyInfo.GetIndexParameters().Length > 0)
                    {
                        continue;
                    }

                    yield return GetItemReportForPropInfo(itemObject, propertyInfo);
                }

            }

            // nutno projit i enumeraci, pokud bych chtel report z kolekce
            if (itemObject is IEnumerable)
            {
                yield return new CollectionReport((IEnumerable)itemObject);
            }
        }

        /// <summary>
        /// Vraci seznam vsech IItemReport, ktere patri dane konfiguracni tride nebo vnorene tride.
        /// Poznamka: Parametry musi byt takto, aby se poznalo od dalsich overloadu a abych vynutil alespon jeden vyraz.
        /// </summary>
        /// <typeparam name="TObject">Typ objektu</typeparam>
        /// <param name="itemObject">Konfiguracni trida nebo vnorena trida</param>
        /// <param name="firstProperty">Prvni lambda vyraz</param>
        /// <param name="properties">Lambda vyrazy, ktere vyjmenuji seznam property, ktere chci reportovat</param>
        /// <returns></returns>
        /// <remarks>
        /// Priklad pouziti:
        /// var actual = ItemReportUtils.GetItemsFromObject(itemObject,
        ///     o => o.SimpleString,
        ///     o => o.ReadOnlyString,
        ///     o => o.Nested)
        /// </remarks>
        public static IEnumerable<IItemReport> GetItemsFromObject<TObject>(TObject itemObject,
            Expression<Func<TObject, object>> firstProperty, params Expression<Func<TObject, object>>[] properties)
        {
            // spojeny prvni a dalsi parametry
            var completeParams = new Expression<Func<TObject, object>>[] { firstProperty }.Concat(properties);
            IEnumerable<string> propNames = ExpressionUtils.GetPropertyNames<TObject>(completeParams.ToArray());
            Type objType = ((object)itemObject).GetType();

            foreach (string propName in propNames)
            {
                yield return GetItemReportForPropInfo(itemObject, objType.GetProperty(propName));
            }
        }

        /// <summary>
        /// Vraci IItemReport pro dane propInfo
        /// </summary>
        /// <param name="itemObject"></param>
        /// <param name="propertyInfo"></param>
        /// <returns></returns>
        public static IItemReport GetItemReportForPropInfo(object itemObject, PropertyInfo propertyInfo)
        {
			Guard.ArgumentNotNull(propertyInfo, "propertyInfo");

            if (propertyInfo.GetAttribute<NestedClassAttribute>(true) != null)
            {
                return new NestedClassReport(propertyInfo.Name, propertyInfo.GetValue(itemObject, null));
            }
            else
            {
                return new PropertyReport(propertyInfo.Name, itemObject);
            }
        }

    }
}