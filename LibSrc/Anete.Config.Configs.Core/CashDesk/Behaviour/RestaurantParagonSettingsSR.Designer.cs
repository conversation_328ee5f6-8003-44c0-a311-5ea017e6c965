//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Anete.Config.Configs.Core.CashDesk.Behaviour {
    using System;
    
    
    /// <summary>
    /// A strongly-typed resource class, for looking up localized strings, formatting them, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilderEx class via the ResXFileCodeGeneratorEx custom tool.
    // To add or remove a member, edit your .ResX file then rerun the ResXFileCodeGeneratorEx custom tool or rebuild your VS.NET project.
    // Copyright (c) Dmytro Kryvko 2006-2021 (http://dmytro.kryvko.googlepages.com/)
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("DMKSoftware.CodeGenerators.Tools.StronglyTypedResourceBuilderEx", "2.9.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Naming", "CA1724:TypeNamesShouldNotMatchNamespaces")]
    public partial class RestaurantParagonSettingsSR {
        
        private static global::System.Resources.ResourceManager _resourceManager;
        
        private static object _internalSyncObject;
        
        private static global::System.Globalization.CultureInfo _resourceCulture;
        
        /// <summary>
        /// Initializes a RestaurantParagonSettingsSR object.
        /// </summary>
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        public RestaurantParagonSettingsSR() {
        }
        
        /// <summary>
        /// Thread safe lock object used by this class.
        /// </summary>
        public static object InternalSyncObject {
            get {
                if (object.ReferenceEquals(_internalSyncObject, null)) {
                    global::System.Threading.Interlocked.CompareExchange(ref _internalSyncObject, new object(), null);
                }
                return _internalSyncObject;
            }
        }
        
        /// <summary>
        /// Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(_resourceManager, null)) {
                    global::System.Threading.Monitor.Enter(InternalSyncObject);
                    try {
                        if (object.ReferenceEquals(_resourceManager, null)) {
                            global::System.Threading.Interlocked.Exchange(ref _resourceManager, new global::System.Resources.ResourceManager("Anete.Config.Configs.Core.CashDesk.Behaviour.RestaurantParagonSettingsSR", typeof(RestaurantParagonSettingsSR).Assembly));
                        }
                    }
                    finally {
                        global::System.Threading.Monitor.Exit(InternalSyncObject);
                    }
                }
                return _resourceManager;
            }
        }
        
        /// <summary>
        /// Overrides the current thread's CurrentUICulture property for all
        /// resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return _resourceCulture;
            }
            set {
                _resourceCulture = value;
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Zakázaný'.
        /// </summary>
        public static string RestaurantParagonSettings_Forbiden {
            get {
                return ResourceManager.GetString(ResourceNames.RestaurantParagonSettings_Forbiden, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Více paragonů'.
        /// </summary>
        public static string RestaurantParagonSettings_More {
            get {
                return ResourceManager.GetString(ResourceNames.RestaurantParagonSettings_More, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Jeden paragon'.
        /// </summary>
        public static string RestaurantParagonSettings_One {
            get {
                return ResourceManager.GetString(ResourceNames.RestaurantParagonSettings_One, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Lists all the resource names as constant string fields.
        /// </summary>
        public class ResourceNames {
            
            /// <summary>
            /// Stores the resource name 'RestaurantParagonSettings_Forbiden'.
            /// </summary>
            public const string RestaurantParagonSettings_Forbiden = "RestaurantParagonSettings_Forbiden";
            
            /// <summary>
            /// Stores the resource name 'RestaurantParagonSettings_More'.
            /// </summary>
            public const string RestaurantParagonSettings_More = "RestaurantParagonSettings_More";
            
            /// <summary>
            /// Stores the resource name 'RestaurantParagonSettings_One'.
            /// </summary>
            public const string RestaurantParagonSettings_One = "RestaurantParagonSettings_One";
        }
    }
}
