using System;
using System.Collections.Generic;
using System.Linq;
using Anete.Common.Core.Interface.AppServices;
using Anete.Common.Data.Nh.Entities;
using Anete.Common.Data.Nh.Interface.Services;
using Anete.Log4Net.Core;
using Anete.Utils;
using Anete.Utils.ComponentModel;
using Anete.Utils.Patterns;
using NHibernate;
using NHibernate.Linq;
using Anete.Utils.Extensions;
namespace Anete.Common.Data.Nh.Services
{

	/// <summary>
	/// Pravidelne kontroluje zpravy v Sms_Zpravy pro toto zarizeni. Nove zpravy pridava do ISystemMessagesAggregator
	/// </summary>
	public class KreditMessagesChecker : Disposable, IKreditMessagesChecker
	{

		#region private static fields...
		private static readonly ILogEx _log = LogManagerEx.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
		#endregion

		#region private fields...
		private System.Threading.Timer _timer;
		private readonly IAppInstallationIdProvider _appInstallationIdProvider;
		private readonly ISystemMessagesAggregator _messagesAggregator;
		private readonly IKreditSessionFactoryProvider _factoryProvider;
		#endregion

		#region constructors...
		/// <summary>
		/// Initializes a new instance
		/// </summary>
		public KreditMessagesChecker(ISystemMessagesAggregator messagesAggregator, IAppInstallationIdProvider appInstallationIdProvider,
			IKreditSessionFactoryProvider factoryProvider)
		{
			Guard.ArgumentNotNull(messagesAggregator, "messagesAggregator");
			Guard.ArgumentNotNull(appInstallationIdProvider, "appInstallationIdProvider");
			Guard.ArgumentNotNull(factoryProvider, "factoryProvider");

			_messagesAggregator = messagesAggregator;
			_appInstallationIdProvider = appInstallationIdProvider;
			_factoryProvider = factoryProvider;

			_timer = new System.Threading.Timer(Timer_Tick, null, TimeSpan.FromSeconds(0), TimeSpan.FromMinutes(1));

		}
		#endregion

		#region IKreditMessagesChecker Members
		/// <summary>
		/// Oznaci hlaseni jako prectene
		/// </summary>
		public void MarkAsRead(int kreditMessageId)
		{
			_log.DebugFormat("MarkAsRead(kreditMessageId={0})", kreditMessageId);
			using (ISession session = _factoryProvider.SessionFactory.OpenSession())
			using (ITransaction tx = session.BeginTransaction())
			{
				// pouziji HQL proto, abych nemusel znovu nacitat entity do pameti. Puvodni session uz neexistuje
				// V dotazu se pouzivaji nazvy z modelu, ne z tabulek
				IQuery query = session.CreateQuery("UPDATE KreditMessage SET ReadDate = :readDate WHERE KreditMessageId = :kreditMessageId");
				query.SetParameter("readDate", DateTime.Now);
				query.SetParameter("kreditMessageId", kreditMessageId);
				query.ExecuteUpdate();

				tx.Commit();
			}
		}
		#endregion

		#region protected overrides...
		protected override void DisposeUnmanagedResources()
		{
			SysUtils.DisposeAndNull(ref _timer);
		}
		#endregion

		#region private methods...
		private void Timer_Tick(object state)
		{
			_log.Debug("Checking for new Kredit messages");
			int[] unreadSystemMessages = _messagesAggregator.Items
				.OfType<KreditMessageUserMessageItem>()
				.Where(msg => !msg.Read)
				.Select(item => item.KreditMessageId).ToArray();

			IStatelessSession session = _factoryProvider.SessionFactory.OpenStatelessSession();
			try
			{
				using (ITransaction tx = session.BeginTransaction())
				{
					// v _messagesAggregator jsou stale zpravy, ktere nejsou prectene. Ale ty pri dalsi kontrole uz znovu nacitat nechci
					var messages = session.Query<KreditMessage>()
						.Where(msg => msg.RecipientAppInstallation != null &&
							msg.RecipientAppInstallation.AppInstallationId == _appInstallationIdProvider.AppInstallationId &&
							msg.ReadDate == null &&
							!unreadSystemMessages.Contains(msg.KreditMessageId))
						.Select(msg =>
							new
							{
								msg.KreditMessageId,
								msg.SentDate,
								msg.Message
							})
						.ToArray();

					tx.Commit();

					if (messages.Length > 0)
					{
						_log.InfoFormat("{0} new Kredit messages", messages.Length);
						// dulezite je pridat hlaseni najednou, aby se nevolal zbytecne nekolikrat notifikace zmen
						_messagesAggregator.Add(messages
							.Select(msg => new KreditMessageUserMessageItem(this, KreditMessagesCheckerSR.KreditMessageFormat(msg.Message),
								msg.SentDate, MessageType.Information, msg.KreditMessageId)).ToArray());
					}
				}
			}
			catch (Exception ex) when (ex.IsCatchable())
			{
				_log.Error("Error while checking Kredit messages", ex);
			}
			finally
			{
				SysUtils.DisposeAndNull(ref session);
			}
		}
		#endregion

	}

}
