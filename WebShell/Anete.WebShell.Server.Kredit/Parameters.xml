<?xml version="1.0" encoding="utf-8" ?>
<parameters>
	<parameter name="AppServer"
			   friendlyName="Aplikační server">
		<parameterValidation kind="AllowEmpty" />
		<parameterEntry kind="TextFile"
						scope="\.config\.xml$"
						match="(?&lt;=&lt;AppServer&gt;)(.*)(?=&lt;/AppServer&gt;)" />
	</parameter>
	<parameter name="AppInstallationId"
			   friendlyName="Id zařízení">
		<parameterValidation kind="AllowEmpty,RegularExpression"
							 validationString="^[0-9]+$" />
		<parameterEntry kind="TextFile"
						scope="\.config\.xml$"
						match="(?&lt;=&lt;AppInstallationId&gt;)(.*)(?=&lt;/AppInstallationId&gt;)" />
	</parameter>

	<parameter name="Database Server"
			   tags="SQL,dbServer">
		<parameterValidation kind="AllowEmpty" />
		<parameterEntry kind="TextFile"
						scope="\.config\.xml$"
						match="(?&lt;=&lt;DbServer&gt;)(.*)(?=&lt;/DbServer&gt;)" />
	</parameter>
	<parameter name="Database Name"
			   tags="SQL,dbName">
		<parameterValidation kind="AllowEmpty" />
		<parameterEntry kind="TextFile"
						scope="\.config\.xml$"
						match="(?&lt;=&lt;DbDatabase&gt;)(.*)(?=&lt;/DbDatabase&gt;)" />
	</parameter>

	<parameter name="Database Username"
			   tags="SQL,dbUsername">
		<parameterValidation kind="AllowEmpty" />
		<parameterEntry kind="TextFile"
						scope="\.config\.xml$"
						match="(?&lt;=&lt;DbUser&gt;)(.*)(?=&lt;/DbUser&gt;)" />
	</parameter>
	<parameter name="Database Password"
			   tags="SQL,dbUserPassword">
		<parameterValidation kind="AllowEmpty" />
		<parameterEntry kind="TextFile"
						scope="\.config\.xml$"
						match="(?&lt;=&lt;DbPassword&gt;)(.*)(?=&lt;/DbPassword&gt;)" />
	</parameter>
</parameters>