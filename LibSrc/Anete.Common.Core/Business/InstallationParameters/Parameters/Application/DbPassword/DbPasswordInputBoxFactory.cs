using Anete.Common.Core.Business.InstallationParameters.Factories;
using Anete.Common.Core.Interface.Business.InstallationParameters.Parameters;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace Anete.Common.Core.Business.InstallationParameters.Parameters.Application.DbPassword
{
	public class DbPasswordInputBoxFactory : InstallationParameterInputBoxFactoryBase<string>
	{	
		protected override IInstallationParameterValue FromInputBoxInt(string value)
		{
			DbPasswordParameterValue parameterValue = new DbPasswordParameterValue((string)value);
			return parameterValue;
		}

		protected override IEnumerable<string> GetAvailableValuesExactTypeInt()
		{
			yield break;
		}

		protected override string ToInputBoxExactTypeInt(IInstallationParameterValue parameterValue)
		{
			return parameterValue.TextValue;
		}

		protected override IEnumerable<Attribute> GetPropertyAttributesInt()
		{
			yield return new DataTypeAttribute(DataType.Password);
		}
	}
}
