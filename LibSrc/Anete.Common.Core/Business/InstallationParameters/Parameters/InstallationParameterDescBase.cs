using Anete.Common.Core.Business.InstallationParameters.Groups;
using Anete.Common.Core.Interface.Business.InstallationParameters.Factories;
using Anete.Common.Core.Interface.Enums;
using System;
using System.Linq;
using Anete.Common.Core.Interface.Business.InstallationParameters.Parameters;
using Anete.Common.Core.Interface.Business.InstallationParameters.Serializers;
using Unity;
using System.Collections.Generic;
using Anete.Common.Core.Interface.Business.InstallationParameters.Groups;
using Anete.Utils.Extensions;
using Anete.Common.Core.Interface.Business.InstallationParameters.ReportGroups;
using System.Threading;

namespace Anete.Common.Core.Business.InstallationParameters.Parameters
{
	/// <summary>
	/// Trida, ktera popisuje dany typ parametru.
	/// </summary>
	/// <typeparam name="TReportGroup">Definuje skupinu pro vytvorenni souhrnne sestavy</typeparam>
	public abstract class InstallationParameterDescBase : IInstallationParameterDesc					
	{
		private readonly InstallationParameterGroupFactory _groupFactory;	
		private readonly Lazy<IInstallationParameterValueSerializer> _serializer;
		private readonly Lazy<IInstallationParameterFactory> _factory;
		private readonly Lazy<IInstallationParameterValueBasedFactory> _valueBasedFactory;

		public InstallationParameterDescBase(InstallationParameterTargets installationParameterTargets, InstallationParameterId installationParameter,
			InstallationParameterGroupFactory groupFactory)
		{
			_groupFactory = groupFactory;
			InstallationParameter = installationParameter;
			InstallationParameterTargets = installationParameterTargets;

			// potrebuju cachovat, pri nacita dat se vola pro kazdy zaznam, muze byt i 20 000 polozek
			_serializer = new Lazy<IInstallationParameterValueSerializer>(() => CreateSerializerInt(), LazyThreadSafetyMode.PublicationOnly);
			_factory = new Lazy<IInstallationParameterFactory>(() => CreateFactoryInt(), LazyThreadSafetyMode.PublicationOnly);
			_valueBasedFactory = new Lazy<IInstallationParameterValueBasedFactory>(() => CreateValueBasedFactoryInt(), LazyThreadSafetyMode.PublicationOnly);
		}

		public string Description
		{
			get
			{
				return GetDescriptionInt();
			}
		}

		public string Caption
		{
			get
			{
				return InstallationParameter.ToLocalizedString();
			}
		}

		/// <summary>
		/// Implicitne zadne HD role nevyzaduju
		/// </summary>
		public virtual string[] RequireHelpDeskRoles => null;

		public virtual bool AllowOnCustomerComputer => true;

		public InstallationParameterTargets InstallationParameterTargets { get; }

		public InstallationParameterId InstallationParameter { get; }

		public IEnumerable<InstallationParameterGroup> Groups
		{
			get
			{
				return GetGroupsInt().Select(g => _groupFactory.Create(g));
			}
		}

		public bool InputBoxFactorySupport => GetInputBoxFactorySupport();

		private InstallationParameterReportGroupBase _reportGroup;
		public InstallationParameterReportGroupBase ReportGroup
		{ 
			get
			{
				if(_reportGroup == null)
				{
					_reportGroup = CreateReportGroupInt();
				}

				return _reportGroup;
			}
		}

		public IInstallationParameterFactory CreateFactory()
		{
			return _factory.Value;
		}
		
		public IInstallationParameterValueSerializer CreateSerializer()
		{
			return _serializer.Value;
		}

		public IInstallationParameterValueBasedFactory CreateValueBasedFactory()
		{
			return _valueBasedFactory.Value;
		}

		public IInstalationParameterValueByInputBoxFactory CreateInputBoxFactory()
		{
			if (!InputBoxFactorySupport)
			{
				throw new InvalidOperationException("InputBox factory nelze vytvorit. Pro dany parametr neni podporovana.");
			}
			return CreateInputBoxFactoryInt();
		}

		protected abstract IInstallationParameterValueBasedFactory CreateValueBasedFactoryInt();

		protected abstract IInstalationParameterValueByInputBoxFactory CreateInputBoxFactoryInt();

		protected abstract IInstallationParameterFactory CreateFactoryInt();

		protected abstract IInstallationParameterValueSerializer CreateSerializerInt();

		protected abstract IEnumerable<InstallationParameterGroupId> GetGroupsInt();

		protected abstract string GetDescriptionInt();

		protected abstract bool GetInputBoxFactorySupport();

		protected abstract InstallationParameterReportGroupBase CreateReportGroupInt();
	}
}
