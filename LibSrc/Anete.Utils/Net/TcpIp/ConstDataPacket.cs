using System;
using System.Collections.Generic;
using System.Text;

namespace Anete.Utils.Net.TcpIp
{  
    /// <summary>
    /// Packet obsahujici ciste jen konstantni data. Nesklada se z zadnych dalsich podrizenych packetu.
    /// </summary>
    public abstract class ConstDataPacket : BasePacket, IParsedPacket
    {
        private byte[] _data;

        /// <summary>
        /// Initializes a new instance of the DataPacket class.
        /// </summary>
        /// <param name="data">Konstantni data, ktere packet obsahuje.</param>
        public ConstDataPacket(byte[] data) : base(true)
        {
            _data = data;
            MinimumSize = data.Length;
            MaximumSize = data.Length;
        }


        /// <summary>
        /// Data urcena k odeslani.
        /// </summary>
        /// <returns></returns>
        public override byte[] GetData()
        {
            // vrati pouze konstantni data predana jiz v konstruktoru
            return _data;            
        }      

        #region IParsedPacket Members

        /// <summary>
        /// Nastavi jednotlive property Packetu podle prijatych dat tak, aby volani GetData vratilo stejne data jako v argumentu metody Parse.
        /// </summary>
        /// <param name="data">Prijata data.</param>
        public void Parse(byte[] data)
        {
            _data = data;    
        }

        #endregion
    }
}
