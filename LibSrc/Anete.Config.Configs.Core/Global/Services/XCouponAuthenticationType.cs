using Anete.Config.Core.Internals;
using System.ComponentModel;
using System.Runtime.Serialization;
using Anete.Resources;
using Anete.Config.Configs.Core.Global.Hw;
using Anete.Utils.ComponentModel.Attributes;

namespace Anete.Config.Configs.Core.Global.Services
{
	[ConfigResourceEnum]
	[TypeConverter(typeof(ConfigEnumConverter<XCouponAuthenticationType>))]
	[DataContract(Namespace = AneteNamespace.DefaultNamespace)]
	public enum XCouponAuthenticationType
	{

		[EnumBasedFactory(typeof(XCouponApiKeyAuthenticationSettings))]
		[EnumMember]
		ApiKey,
		[EnumBasedFactory(typeof(XCouponUsernamePasswordAuthenticationSettings))]
		[EnumMember]
		UsernamePassword
	}
}