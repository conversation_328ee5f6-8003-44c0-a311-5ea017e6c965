<Project Sdk="Microsoft.NET.Sdk">
  <Import Project="..\Shared\Common.targets" />

  <PropertyGroup>
    <Configurations>Debug;Release;Internal</Configurations>
  </PropertyGroup>

	<PropertyGroup Condition="'$(TargetFramework)' != 'net472'">
		<DebugType>portable</DebugType>
		<DebugSymbols>true</DebugSymbols>
	</PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="6.0.1" />
    <PackageReference Include="Unity.Container" Version="5.11.11" />
    <PackageReference Include="Unity.ServiceLocation" Version="5.11.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Anete.Resources\Anete.Resources.40.csproj" />
  </ItemGroup>
</Project>