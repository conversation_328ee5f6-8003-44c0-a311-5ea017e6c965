//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Anete.Common.Data.Nh.Entities
{

    /// <summary>
    /// Tabulka: dba.EXP_EXPORT_COLUMNS
    /// Seznam sloupců, které se exportují
    /// </summary>
    public partial class ExportDefColumn : Anete.Data.Nh.NhEntityBase, INotifyPropertyChanging, INotifyPropertyChanged {

        private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(System.String.Empty);

        private short _ExportDefId;

        private short _Order;

        private ExportDefColumnSection _Section;

        private string _Name;

        private string _Alias;

        private string _Format;

        private char? _Alignment;

        private short? _PadLen;

        private char? _PadChar;

        private ExportDef _ExportDef;
    
        #region Extensibility Method Definitions
        
        partial void OnCreated();

        public override bool Equals(object obj)
        {
          ExportDefColumn toCompare = obj as ExportDefColumn;
          if (toCompare == null)
          {
            return false;
          }

          if (!Object.Equals(this.ExportDefId, toCompare.ExportDefId))
            return false;
          if (!Object.Equals(this.Order, toCompare.Order))
            return false;
          if (!Object.Equals(this.Section, toCompare.Section))
            return false;
         // pokud mam default primary key, znamena to, ze jedna o nove vytvorenou entitu, ktera jeste nema key prirazen. 
        // V takove pripade musim porovnat podle instance
        bool isDefault = true;
        if (this.ExportDefId != default(short))
        {
          isDefault = false;
        }
     
        if (this.Order != default(short))
        {
          isDefault = false;
        }
     
        if (this.Section != default(ExportDefColumnSection))
        {
          isDefault = false;
        }
     

          if (isDefault)
          {
            return object.ReferenceEquals(toCompare, this);
          }     
          return true;
        }

	private int? _hashCode = null;
        public override int GetHashCode()
        {
	if (_hashCode == null)
          {
          _hashCode = 13;
          _hashCode = (_hashCode * 7) + ExportDefId.GetHashCode();
          _hashCode = (_hashCode * 7) + Order.GetHashCode();
          _hashCode = (_hashCode * 7) + Section.GetHashCode();
}
          return _hashCode.Value;
        }
        partial void OnExportDefIdChanging(short value);
        
        partial void OnExportDefIdChanged();
        partial void OnOrderChanging(short value);
        
        partial void OnOrderChanged();
        partial void OnSectionChanging(ExportDefColumnSection value);
        
        partial void OnSectionChanged();
        partial void OnNameChanging(string value);
        
        partial void OnNameChanged();
        partial void OnAliasChanging(string value);
        
        partial void OnAliasChanged();
        partial void OnFormatChanging(string value);
        
        partial void OnFormatChanged();
        partial void OnAlignmentChanging(char? value);
        
        partial void OnAlignmentChanged();
        partial void OnPadLenChanging(short? value);
        
        partial void OnPadLenChanged();
        partial void OnPadCharChanging(char? value);
        
        partial void OnPadCharChanged();
        partial void OnExportDefChanging(ExportDef value);

        partial void OnExportDefChanged();
        
        #endregion
        public ExportDefColumn()
        {
            this._Section = (Anete.Common.Data.Nh.Entities.ExportDefColumnSection)Enum.Parse(typeof(Anete.Common.Data.Nh.Entities.ExportDefColumnSection), "1");
            OnCreated();
        }

    
        /// <summary>
        /// Sloupec: EXPORT_ID
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("ExportDefId", typeof(ExportDefColumnSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(ExportDefColumn), Tag="ExportDefId")]
        public virtual short ExportDefId
        {
            get
            {
                return this._ExportDefId;
            }
            set
            {
                if (this._ExportDefId != value)
                {
                    this.OnExportDefIdChanging(value);
                    this.SendPropertyChanging();
                    this._ExportDefId = value;
                    this.SendPropertyChanged("ExportDefId");
                    this.OnExportDefIdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: C_ORDER
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Order", typeof(ExportDefColumnSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(ExportDefColumn), Tag="Order")]
        public virtual short Order
        {
            get
            {
                return this._Order;
            }
            set
            {
                if (this._Order != value)
                {
                    this.OnOrderChanging(value);
                    this.SendPropertyChanging();
                    this._Order = value;
                    this.SendPropertyChanged("Order");
                    this.OnOrderChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: EXPORT_SECTION
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Section", typeof(ExportDefColumnSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(ExportDefColumn), Tag="Section")]
        public virtual ExportDefColumnSection Section
        {
            get
            {
                return this._Section;
            }
            set
            {
                if (this._Section != value)
                {
                    this.OnSectionChanging(value);
                    this.SendPropertyChanging();
                    this._Section = value;
                    this.SendPropertyChanged("Section");
                    this.OnSectionChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: C_NAME
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Name", typeof(ExportDefColumnSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(ExportDefColumn), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 200,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="Name")]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(ExportDefColumn), Tag="Name")]
        public virtual string Name
        {
            get
            {
                return this._Name;
            }
            set
            {
                if (this._Name != value)
                {
                    this.OnNameChanging(value);
                    this.SendPropertyChanging();
                    this._Name = value;
                    this.SendPropertyChanged("Name");
                    this.OnNameChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: C_ALIAS
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Alias", typeof(ExportDefColumnSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(ExportDefColumn), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 30,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="Alias")]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(ExportDefColumn), Tag="Alias")]
        public virtual string Alias
        {
            get
            {
                return this._Alias;
            }
            set
            {
                if (this._Alias != value)
                {
                    this.OnAliasChanging(value);
                    this.SendPropertyChanging();
                    this._Alias = value;
                    this.SendPropertyChanged("Alias");
                    this.OnAliasChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: C_FORMAT
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Format", typeof(ExportDefColumnSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(ExportDefColumn), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 100,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="Format")]
        public virtual string Format
        {
            get
            {
                return this._Format;
            }
            set
            {
                if (this._Format != value)
                {
                    this.OnFormatChanging(value);
                    this.SendPropertyChanging();
                    this._Format = value;
                    this.SendPropertyChanged("Format");
                    this.OnFormatChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: C_ALIGN
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Alignment", typeof(ExportDefColumnSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual char? Alignment
        {
            get
            {
                return this._Alignment;
            }
            set
            {
                if (this._Alignment != value)
                {
                    this.OnAlignmentChanging(value);
                    this.SendPropertyChanging();
                    this._Alignment = value;
                    this.SendPropertyChanged("Alignment");
                    this.OnAlignmentChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: C_PAD_LEN
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("PadLen", typeof(ExportDefColumnSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual short? PadLen
        {
            get
            {
                return this._PadLen;
            }
            set
            {
                if (this._PadLen != value)
                {
                    this.OnPadLenChanging(value);
                    this.SendPropertyChanging();
                    this._PadLen = value;
                    this.SendPropertyChanged("PadLen");
                    this.OnPadLenChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: C_PAD_CHAR
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("PadChar", typeof(ExportDefColumnSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual char? PadChar
        {
            get
            {
                return this._PadChar;
            }
            set
            {
                if (this._PadChar != value)
                {
                    this.OnPadCharChanging(value);
                    this.SendPropertyChanging();
                    this._PadChar = value;
                    this.SendPropertyChanged("PadChar");
                    this.OnPadCharChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: EXPORT_ID
        /// </summary>
        /// <remark>
        /// Pozor! Tato property se musi definovat, jinak se negeneruje Inverse u Parenta ve fluent mapování!
        /// </remark>
        [Anete.Data.Attributes.EntitySRDisplayName("ExportDef", typeof(ExportDefColumnSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual ExportDef ExportDef
        {
            get
            {
                return this._ExportDef;
            }
            set
            {
                if (this._ExportDef != value)
                {
                    this.OnExportDefChanging(value);
                    this.SendPropertyChanging();
                    this._ExportDef = value;
                    this.SendPropertyChanged("ExportDef");
                    this.OnExportDefChanged();
                }
            }
        }
   
        public virtual event PropertyChangingEventHandler PropertyChanging;

	// Mam v bazove tride, nema smysl generovat
	// public virtual event PropertyChangedEventHandler PropertyChanged;

        protected virtual void SendPropertyChanging()
        {
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, emptyChangingEventArgs);
        }

        protected virtual void SendPropertyChanging(System.String propertyName) 
        {   	
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, new PropertyChangingEventArgs(propertyName));
        }

        protected virtual void SendPropertyChanged(System.String propertyName)
        {    
		// mam v bazove tride, proto musim volat takto jednoduse
		OnPropertyChanged(propertyName);
/*		        var handler = this.PropertyChanged;
            if (handler != null)
                handler(this, new PropertyChangedEventArgs(propertyName));*/
        }
    }

}
