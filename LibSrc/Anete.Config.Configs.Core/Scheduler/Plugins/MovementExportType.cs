using Anete.Config.Core.Internals;
using System.ComponentModel;

namespace Anete.Config.Configs.Core.Scheduler.Plugins
{
	/// <summary>
	/// Typ exportu
	/// </summary>
	[ConfigResourceEnum]
	[TypeConverter(typeof(ConfigEnumConverter<MovementExportType>))]
	public enum MovementExportType
	{
		/// <summary>
		/// Importovat vsechny pohyby - bez provizí
		/// </summary>
		All,
		/// <summary>
		/// Importovat vsechny pohyby - s provizemi
		/// </summary>
		CommisionsAll,
		/// <summary>
		/// Ignorovat zboží nepoužívané pro FBS - bez provizí
		/// </summary>
		FbsOnly,
		/// <summary>
		/// Ignorovat zboží nepoužívané pro FBS - s provizemi
		/// </summary>
		CommisionsFbsOnly
	}
}