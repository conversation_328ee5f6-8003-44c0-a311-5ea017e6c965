using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.Linq;

namespace Anete.Utils
{
	/// <summary>
	/// Trieda pre pracu s obrazkom
	/// </summary>
	public static class ImageUtils
	{
		/// <summary>
		/// Ureze dany obraok podla obdlznika
		/// </summary>
		/// <param name="oldImage">povodny obrazok</param>
		/// <param name="rect">stvorec pre orezanie</param>
		/// <returns>Urezany obrazok</returns>
		public static Image Crop(Image oldImage, Rectangle rect)
		{
			Bitmap bitmap = (Bitmap)oldImage;
			Bitmap newImage = bitmap.Clone(rect, bitmap.PixelFormat);

			return (Image)newImage;
		}

		/// <summary>
		/// Urece dany obrazok podla stvorca s dlzkou strany
		/// </summary>
		/// <param name="oldImage">povodny obrazok</param>
		/// <param name="squareCropSize">velkost strany stvorca</param>
		/// <returns></returns>
		public static Image CropToSquare(Image oldImage, int squareCropSize)
		{
			int x = 0, y = 0, width = squareCropSize, height = squareCropSize;

			if (squareCropSize >= oldImage.Height)
			{
				height = oldImage.Height;
			}
			else
			{
				y = (oldImage.Height - squareCropSize) / 2;
			}

			if (squareCropSize >= oldImage.Width)
			{
				width = oldImage.Width;
			}
			else
			{
				x = (oldImage.Width - squareCropSize) / 2;
			}

			Rectangle rect = new Rectangle(x, y, width, height);
			Image newImage = Crop(oldImage, rect);

			if (newImage.Width <= rect.Width || newImage.Height <= rect.Height)
			{
				Bitmap imgBgr = new Bitmap(squareCropSize, squareCropSize, PixelFormat.Format24bppRgb);
				imgBgr.SetResolution(newImage.HorizontalResolution,
					oldImage.VerticalResolution);
				Graphics g = Graphics.FromImage(imgBgr);
				g.Clear(Color.White);
				g.DrawImage(newImage,
							new Rectangle((squareCropSize - newImage.Width) / 2, (squareCropSize - newImage.Height) / 2, newImage.Width, newImage.Height),
							new Rectangle(0, 0, newImage.Width, newImage.Height),
							GraphicsUnit.Pixel);
				g.Dispose();
				newImage = (Image)imgBgr;
			}
			return newImage;
		}

		/// <summary>
		/// Zmeni velkost obrazka na zadane rozmery. Zachovava aspect ratio
		/// </summary>
		/// <param name="imgToResize">povodony obrazok</param>
		/// <param name="size">nove rozmery</param>
		/// <returns></returns>
		public static Image Resize(Image imgToResize, Size size)
		{
			int sourceWidth = imgToResize.Width;
			int sourceHeight = imgToResize.Height;

			float percent = 0;
			float percentWidth = 0;
			float percentHeight = 0;

			percentWidth = ((float)size.Width / (float)sourceWidth);
			percentHeight = ((float)size.Height / (float)sourceHeight);

			if (percentHeight < percentWidth && percentHeight != 0.0)
			{
				percent = percentHeight;
			}
			else if (percentWidth != 0.0)
			{
				percent = percentWidth;
			}

			if (percent == 0.0)
			{
				// Kak: 11.4.2013 Zmeneno po dohode s VaH. Vracelo null, coz je hovadina a stejne to nebylo nikde osetreno
				return imgToResize;
			}

			int destWidth = (int)(sourceWidth * percent);
			int destHeight = (int)(sourceHeight * percent);

			Bitmap b = new Bitmap(destWidth, destHeight);
			Graphics g = Graphics.FromImage((Image)b);
			g.InterpolationMode = InterpolationMode.HighQualityBicubic;

			g.DrawImage(imgToResize, 0, 0, destWidth, destHeight);
			g.Dispose();

			// je - li vysledny obrazek mensi nez zadany, zvetsime
			if ((b.Width <= size.Width || b.Height <= size.Height) && (size.Width > 0 && size.Height > 0))
			{
				Bitmap imgBgr = new Bitmap(size.Width, size.Height, PixelFormat.Format24bppRgb);
				imgBgr.SetResolution(b.HorizontalResolution, imgToResize.VerticalResolution);
				Graphics bg = Graphics.FromImage(imgBgr);
				bg.Clear(Color.White);
				bg.DrawImage(b,
					new Rectangle((size.Width - b.Width) / 2, (size.Height - b.Height) / 2, b.Width, b.Height),
					new Rectangle(0, 0, b.Width, b.Height),
					GraphicsUnit.Pixel);
				bg.Dispose();
				b = imgBgr;
			}

			return (Image)b;
		}

		/// <summary>
		/// Zmeni velkost obrazka na zadane rozmery
		/// </summary>
		/// <param name="imgToResize">povodony obrazok</param>
		/// <param name="width">sirka</param>
		/// <param name="height">vyska</param>
		/// <returns></returns>
		public static Image Resize(Image imgToResize, int width, int height)
		{
			Size size = new Size(width, height);
			return Resize(imgToResize, size);
		}

		/// <summary>
		/// Zmeni velkost obrazka na zadanu sirku
		/// </summary>
		/// <param name="imgToResize">povodony obrazok</param>
		/// <param name="width">sirka</param>
		/// <returns></returns>
		public static Image ResizeToWidth(Image imgToResize, int width)
		{
			Size size = new Size(width, 0);
			return Resize(imgToResize, size);
		}

		/// <summary>
		/// Zmeni velkost obrazka na zadanu vysku
		/// </summary>
		/// <param name="imgToResize">povodony obrazok</param>
		/// <param name="height">sirka</param>
		/// <returns></returns>
		public static Image ResizeToHeight(Image imgToResize, int height)
		{
			Size size = new Size(0, height);
			return Resize(imgToResize, size);
		}

		/// <summary>
		/// Vytvori Bitmapu z obrazku Images tak, aby byly rozvonemerne rozpostreny ve ctvercove mrize.
		/// Kazdy obrazek z kolekce Images zabira ve vysledku stejny prostor.
		/// Vytvoreno pro zobrazeni Piktogramu, ale da se pouzit i obecne.
		/// </summary>
		/// <param name="finalSize"></param>
		/// <param name="images"></param>
		/// <returns></returns>
		public static Bitmap Combine(Size finalSize, params Image[] images)
		{
			Bitmap finalImage = new Bitmap(finalSize.Width, finalSize.Height);
			if (images.Length == 0)
			{
				return finalImage;
			}

			// musim vypocitat velikost vysledneho obrazku
			int rowsCount = (int)System.Math.Ceiling((double)images.Length / (double)2);
			int columnCount = images.Length / rowsCount;
			// velikost mrizky, ta je vzdy ctvercova
			int gridDimension = System.Math.Max(rowsCount, columnCount);
			Size imageSize = new Size(finalSize.Width / gridDimension, finalSize.Height / gridDimension);			
			
			using (Graphics g = Graphics.FromImage(finalImage))
			{				
				g.Clear(Color.Transparent);
				
				int x = 0;
				// zajisti, ze pokud budu mit zobrazeny 2 obrazky v mrizce 2x2, budou zobrazeny uprostred
				int y = (finalSize.Height - imageSize.Height * rowsCount) / 2;
				foreach (Bitmap image in images)
				{
					g.DrawImage(image,
								new Rectangle(x, y, imageSize.Width, imageSize.Height));
					x += imageSize.Width;
					if (x >= finalSize.Width)
					{
						x = 0;
						y += imageSize.Height;
					}
				}
			}

			return finalImage;
		}

		/// <summary>
		/// Seradi obrazky zleva doprava vedle sebe. Jejich velikost zustava beze zmeny.
		/// </summary>
		/// <param name="images"></param>
		/// <returns></returns>
		public static Bitmap CombineToLine(params Image[] images)
		{			
			Bitmap finalImage = new Bitmap(images.Sum(i=>i.Width), images.Max(i=>i.Height));
			if (images.Length == 0)
			{
				return finalImage;
			}									

			using (Graphics g = Graphics.FromImage(finalImage))
			{
				g.Clear(Color.Transparent);

				int x = 0;				
				foreach (Bitmap image in images)
				{					
					g.DrawImage(image,
								new Rectangle(x, 0, image.Width, image.Height));
					x += image.Width;					
				}
			}

			return finalImage;
		}
	}
}
