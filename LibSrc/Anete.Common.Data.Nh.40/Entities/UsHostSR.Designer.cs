//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Anete.Common.Data.Nh.Entities {
    using System;
    
    
    /// <summary>
    /// A strongly-typed resource class, for looking up localized strings, formatting them, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilderEx class via the InternalResXFileCodeGeneratorEx custom tool.
    // To add or remove a member, edit your .ResX file then rerun the InternalResXFileCodeGeneratorEx custom tool or rebuild your VS.NET project.
    // Copyright (c) Dmytro <PERSON> 2006-2018 (http://dmytro.kryvko.googlepages.com/)
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("DMKSoftware.CodeGenerators.Tools.StronglyTypedResourceBuilderEx", "*******")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Naming", "CA1724:TypeNamesShouldNotMatchNamespaces")]
    internal partial class UsHostSR {
        
        private static global::System.Resources.ResourceManager _resourceManager;
        
        private static object _internalSyncObject;
        
        private static global::System.Globalization.CultureInfo _resourceCulture;
        
        /// <summary>
        /// Initializes a UsHostSR object.
        /// </summary>
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal UsHostSR() {
        }
        
        /// <summary>
        /// Thread safe lock object used by this class.
        /// </summary>
        internal static object InternalSyncObject {
            get {
                if (object.ReferenceEquals(_internalSyncObject, null)) {
                    global::System.Threading.Interlocked.CompareExchange(ref _internalSyncObject, new object(), null);
                }
                return _internalSyncObject;
            }
        }
        
        /// <summary>
        /// Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(_resourceManager, null)) {
                    global::System.Threading.Monitor.Enter(InternalSyncObject);
                    try {
                        if (object.ReferenceEquals(_resourceManager, null)) {
                            global::System.Threading.Interlocked.Exchange(ref _resourceManager, new global::System.Resources.ResourceManager("Anete.Common.Data.Nh.Entities.UsHostSR", typeof(UsHostSR).Assembly));
                        }
                    }
                    finally {
                        global::System.Threading.Monitor.Exit(InternalSyncObject);
                    }
                }
                return _resourceManager;
            }
        }
        
        /// <summary>
        /// Overrides the current thread's CurrentUICulture property for all
        /// resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return _resourceCulture;
            }
            set {
                _resourceCulture = value;
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to '#Email#'.
        /// </summary>
        internal static string Email {
            get {
                return ResourceManager.GetString(ResourceNames.Email, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to '#Id#'.
        /// </summary>
        internal static string Id {
            get {
                return ResourceManager.GetString(ResourceNames.Id, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to '#Nazev#'.
        /// </summary>
        internal static string Nazev {
            get {
                return ResourceManager.GetString(ResourceNames.Nazev, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to '#Popis#'.
        /// </summary>
        internal static string Popis {
            get {
                return ResourceManager.GetString(ResourceNames.Popis, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to '#Rezervaces#'.
        /// </summary>
        internal static string Rezervaces {
            get {
                return ResourceManager.GetString(ResourceNames.Rezervaces, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to '#Telefon#'.
        /// </summary>
        internal static string Telefon {
            get {
                return ResourceManager.GetString(ResourceNames.Telefon, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to '#UbytovanyHosts#'.
        /// </summary>
        internal static string UbytovanyHosts {
            get {
                return ResourceManager.GetString(ResourceNames.UbytovanyHosts, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Lists all the resource names as constant string fields.
        /// </summary>
        internal class ResourceNames {
            
            /// <summary>
            /// Stores the resource name 'Email'.
            /// </summary>
            internal const string Email = "Email";
            
            /// <summary>
            /// Stores the resource name 'Id'.
            /// </summary>
            internal const string Id = "Id";
            
            /// <summary>
            /// Stores the resource name 'Nazev'.
            /// </summary>
            internal const string Nazev = "Nazev";
            
            /// <summary>
            /// Stores the resource name 'Popis'.
            /// </summary>
            internal const string Popis = "Popis";
            
            /// <summary>
            /// Stores the resource name 'Rezervaces'.
            /// </summary>
            internal const string Rezervaces = "Rezervaces";
            
            /// <summary>
            /// Stores the resource name 'Telefon'.
            /// </summary>
            internal const string Telefon = "Telefon";
            
            /// <summary>
            /// Stores the resource name 'UbytovanyHosts'.
            /// </summary>
            internal const string UbytovanyHosts = "UbytovanyHosts";
        }
    }
}
