using Anete.Common.Core.Interface.AppServices.AppStorage;
using Anete.Utils.AppServices.TypeAdapterLocators;
using Anete.Utils.ComponentModel.ComponentPersistence.Adapters;

namespace Anete.Common.Core.AppServices.AppStorage.Adapters
{

    /// <summary>
    /// Registrace vsech adapteru, ktere jsou pritomny v tomto namespace
    /// </summary>
    public class AdapterRegistrator : AdapterRegistratorBase<IAppStorageAdapterFactory>
    {

        /// <summary>
        /// Vytvori novou instanci
        /// </summary>
        public AdapterRegistrator(IAppStorageAdapterFactory locatorService)
            : base(locatorService)
        {
            
        }

        /// <summary>
        /// Registrace vsech adapteru, ktere jsou pritomny v tomto namespace
        /// </summary>
        public override void Register()
        {
            LocatorService.RegisterAdapter<object, XmlSerializerAppStorageAdapter>();
			LocatorService.RegisterAdapter<LocalFileComputerId, DataContractSerializerAppStorageAdapter>();
        }
    }
}
