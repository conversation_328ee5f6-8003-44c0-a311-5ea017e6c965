using System;
using System.Collections.Generic;
using System.Text;

namespace Anete.Utils.Net.TcpIp
{
    /// <summary>
    /// Packet, ktery na zaklade prijimanych dat rozhodne, kdy nastane jeho konec
    /// </summary>
    public interface ICanDeterminateEndOfPacket : IParsedPacket
    {
        /// <summary>
        /// Metoda je postupne volana pro prichazejici data.
        /// </summary>
        /// <param name="data">Prichazejici data</param>
        /// <param name="index">Poradi precteneho bytu v ramci jednoho pozadavku</param>
        /// <returns>
        /// Jakmile packet rozhodne, ze ma data kompletni, vraci true.
        /// Jinak false.
        /// </returns>
        bool IsEnd(byte data, int index);
    }
}
