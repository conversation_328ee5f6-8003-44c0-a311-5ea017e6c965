using System;
using System.Collections.Generic;
using System.Text;
using System.ComponentModel;
using Anete.Config.Core.Internals;

namespace Anete.Config.Configs.Core.Office.Behaviour
{
    /// <summary>
    /// Zpusob rekapitulace v sestavach uzaverky
    /// Určuje způsob rozlišení rekap. Nákladů v rámci druhu
    /// </summary>
    [ConfigResourceEnum]
    [TypeConverter(typeof(ConfigEnumConverter<ReportRecapitulationType>))]
    public enum ReportRecapitulationType
    {
        /// <summary>
        /// V rámci druhu se rozlišují dotace
        /// </summary>
        BySubsidy = -1,
        /// <summary>
        /// V rámci druhu se rozlišují ceny stravenky
        /// </summary>
        ByMealTicketPrice = 0,
    }
}
