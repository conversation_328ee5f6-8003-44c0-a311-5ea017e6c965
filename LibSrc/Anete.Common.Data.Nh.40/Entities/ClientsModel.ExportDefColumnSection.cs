//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Anete.Common.Data.Nh.Entities
{

    /// <summary>
    /// Typ sekce pri exportu radku
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace=Anete.Resources.AneteNamespace.DefaultNamespace)]
    [Anete.Utils.ComponentModel.Attributes.ResXEnum(typeof(ExportDefColumnSectionSR))]
    public enum ExportDefColumnSection : short
 {
        [System.Runtime.Serialization.EnumMember]
        Detail = 1,            [System.Runtime.Serialization.EnumMember]
        Header = 2,            [System.Runtime.Serialization.EnumMember]
        Footer = 3
    }

}
