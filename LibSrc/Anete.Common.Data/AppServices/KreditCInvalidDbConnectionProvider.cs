using System;
using System.Collections.Generic;
using Anete.Common.Data.Interface.AppServices;
using Anete.Common.Core.Interface.ProductInfo;

namespace Anete.Common.Data.AppServices
{
	/// <summary>
	/// Implementace IKreditCConnectionProvider pro neplatna spojeni
	/// </summary>
	public class KreditCInvalidDbConnectionProvider : InvalidDbConnectionProvider, IKreditCDbConnectionProvider, IKreditCDbConnectionProviderConfig
	{
		/// <summary>
		/// Initializes a new instance of the KreditCDbConnectionProvider class.
		/// </summary>
		public KreditCInvalidDbConnectionProvider()
			: base(ConfigConnectionStringNames.KreditC)
		{

		}
	}
}
