//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Anete.Common.Data.Nh.Entities {
    using System;
    
    
    /// <summary>
    /// A strongly-typed resource class, for looking up localized strings, formatting them, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilderEx class via the InternalResXFileCodeGeneratorEx custom tool.
    // To add or remove a member, edit your .ResX file then rerun the InternalResXFileCodeGeneratorEx custom tool or rebuild your VS.NET project.
    // Copyright (c) Dmytro <PERSON> 2006-2018 (http://dmytro.kryvko.googlepages.com/)
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("DMKSoftware.CodeGenerators.Tools.StronglyTypedResourceBuilderEx", "2.6.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Naming", "CA1724:TypeNamesShouldNotMatchNamespaces")]
    internal partial class AccountBalanceDetailSR {
        
        private static global::System.Resources.ResourceManager _resourceManager;
        
        private static object _internalSyncObject;
        
        private static global::System.Globalization.CultureInfo _resourceCulture;
        
        /// <summary>
        /// Initializes a AccountBalanceDetailSR object.
        /// </summary>
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal AccountBalanceDetailSR() {
        }
        
        /// <summary>
        /// Thread safe lock object used by this class.
        /// </summary>
        internal static object InternalSyncObject {
            get {
                if (object.ReferenceEquals(_internalSyncObject, null)) {
                    global::System.Threading.Interlocked.CompareExchange(ref _internalSyncObject, new object(), null);
                }
                return _internalSyncObject;
            }
        }
        
        /// <summary>
        /// Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(_resourceManager, null)) {
                    global::System.Threading.Monitor.Enter(InternalSyncObject);
                    try {
                        if (object.ReferenceEquals(_resourceManager, null)) {
                            global::System.Threading.Interlocked.Exchange(ref _resourceManager, new global::System.Resources.ResourceManager("Anete.Common.Data.Nh.Entities.AccountBalanceDetailSR", typeof(AccountBalanceDetailSR).Assembly));
                        }
                    }
                    finally {
                        global::System.Threading.Monitor.Exit(InternalSyncObject);
                    }
                }
                return _resourceManager;
            }
        }
        
        /// <summary>
        /// Overrides the current thread's CurrentUICulture property for all
        /// resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return _resourceCulture;
            }
            set {
                _resourceCulture = value;
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Uzávěrka'.
        /// </summary>
        internal static string AccountBalance {
            get {
                return ResourceManager.GetString(ResourceNames.AccountBalance, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Id uzávěrky'.
        /// </summary>
        internal static string AccountBalanceId {
            get {
                return ResourceManager.GetString(ResourceNames.AccountBalanceId, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Účtárna'.
        /// </summary>
        internal static string AccountingDepartment {
            get {
                return ResourceManager.GetString(ResourceNames.AccountingDepartment, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Účtárna - Id'.
        /// </summary>
        internal static string AccountingDepartmentId {
            get {
                return ResourceManager.GetString(ResourceNames.AccountingDepartmentId, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Účtárna - název'.
        /// </summary>
        internal static string AccountingDepartmentName {
            get {
                return ResourceManager.GetString(ResourceNames.AccountingDepartmentName, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Typ účtu'.
        /// </summary>
        internal static string AccountType {
            get {
                return ResourceManager.GetString(ResourceNames.AccountType, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Rozšiřující informace'.
        /// </summary>
        internal static string AddInfo {
            get {
                return ResourceManager.GetString(ResourceNames.AddInfo, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Kód karty'.
        /// </summary>
        internal static string CardCode {
            get {
                return ResourceManager.GetString(ResourceNames.CardCode, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Přenos'.
        /// </summary>
        internal static string Carry {
            get {
                return ResourceManager.GetString(ResourceNames.Carry, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Suma vkladů a výběrů v hotovosti'.
        /// </summary>
        internal static string Cash {
            get {
                return ResourceManager.GetString(ResourceNames.Cash, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Klient'.
        /// </summary>
        internal static string Client {
            get {
                return ResourceManager.GetString(ResourceNames.Client, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Klient - rodné číslo'.
        /// </summary>
        internal static string ClientBirthId {
            get {
                return ResourceManager.GetString(ResourceNames.ClientBirthId, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Klient - titul'.
        /// </summary>
        internal static string ClientDegree {
            get {
                return ResourceManager.GetString(ResourceNames.ClientDegree, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Klient - jméno'.
        /// </summary>
        internal static string ClientFirstName {
            get {
                return ResourceManager.GetString(ResourceNames.ClientFirstName, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Skupina'.
        /// </summary>
        internal static string ClientGroup {
            get {
                return ResourceManager.GetString(ResourceNames.ClientGroup, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Skupina - id'.
        /// </summary>
        internal static string ClientGroupId {
            get {
                return ResourceManager.GetString(ResourceNames.ClientGroupId, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Klient - id'.
        /// </summary>
        internal static string ClientId {
            get {
                return ResourceManager.GetString(ResourceNames.ClientId, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Klient - příjmení'.
        /// </summary>
        internal static string ClientLastName {
            get {
                return ResourceManager.GetString(ResourceNames.ClientLastName, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Klient - osobní číslo'.
        /// </summary>
        internal static string ClientPersonalId {
            get {
                return ResourceManager.GetString(ResourceNames.ClientPersonalId, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Počet jídel'.
        /// </summary>
        internal static string MealCount {
            get {
                return ResourceManager.GetString(ResourceNames.MealCount, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Celková cena za jídla'.
        /// </summary>
        internal static string MealPrice {
            get {
                return ResourceManager.GetString(ResourceNames.MealPrice, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Organizace'.
        /// </summary>
        internal static string Organization {
            get {
                return ResourceManager.GetString(ResourceNames.Organization, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Organizace - Id'.
        /// </summary>
        internal static string OrganizationId {
            get {
                return ResourceManager.GetString(ResourceNames.OrganizationId, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Organizace - název'.
        /// </summary>
        internal static string OrganizationName {
            get {
                return ResourceManager.GetString(ResourceNames.OrganizationName, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Ostatní'.
        /// </summary>
        internal static string Others {
            get {
                return ResourceManager.GetString(ResourceNames.Others, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Středisko'.
        /// </summary>
        internal static string Resort {
            get {
                return ResourceManager.GetString(ResourceNames.Resort, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Středisko - kód'.
        /// </summary>
        internal static string ResortCode {
            get {
                return ResourceManager.GetString(ResourceNames.ResortCode, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Středisko - Id'.
        /// </summary>
        internal static string ResortId {
            get {
                return ResourceManager.GetString(ResourceNames.ResortId, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Středisko - název'.
        /// </summary>
        internal static string ResortName {
            get {
                return ResourceManager.GetString(ResourceNames.ResortName, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Srážka ze mzdy'.
        /// </summary>
        internal static string SalaryDrawback {
            get {
                return ResourceManager.GetString(ResourceNames.SalaryDrawback, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Počet dotovaných jídel'.
        /// </summary>
        internal static string SubsidedMealCount {
            get {
                return ResourceManager.GetString(ResourceNames.SubsidedMealCount, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Celková cena za dotovaná jídla'.
        /// </summary>
        internal static string SubsidedMealPrice {
            get {
                return ResourceManager.GetString(ResourceNames.SubsidedMealPrice, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Převod'.
        /// </summary>
        internal static string Transfer {
            get {
                return ResourceManager.GetString(ResourceNames.Transfer, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Vklad převodem'.
        /// </summary>
        internal static string VkladPrevod {
            get {
                return ResourceManager.GetString(ResourceNames.VkladPrevod, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Lists all the resource names as constant string fields.
        /// </summary>
        internal class ResourceNames {
            
            /// <summary>
            /// Stores the resource name 'AccountBalance'.
            /// </summary>
            internal const string AccountBalance = "AccountBalance";
            
            /// <summary>
            /// Stores the resource name 'AccountBalanceId'.
            /// </summary>
            internal const string AccountBalanceId = "AccountBalanceId";
            
            /// <summary>
            /// Stores the resource name 'AccountingDepartment'.
            /// </summary>
            internal const string AccountingDepartment = "AccountingDepartment";
            
            /// <summary>
            /// Stores the resource name 'AccountingDepartmentId'.
            /// </summary>
            internal const string AccountingDepartmentId = "AccountingDepartmentId";
            
            /// <summary>
            /// Stores the resource name 'AccountingDepartmentName'.
            /// </summary>
            internal const string AccountingDepartmentName = "AccountingDepartmentName";
            
            /// <summary>
            /// Stores the resource name 'AccountType'.
            /// </summary>
            internal const string AccountType = "AccountType";
            
            /// <summary>
            /// Stores the resource name 'AddInfo'.
            /// </summary>
            internal const string AddInfo = "AddInfo";
            
            /// <summary>
            /// Stores the resource name 'CardCode'.
            /// </summary>
            internal const string CardCode = "CardCode";
            
            /// <summary>
            /// Stores the resource name 'Carry'.
            /// </summary>
            internal const string Carry = "Carry";
            
            /// <summary>
            /// Stores the resource name 'Cash'.
            /// </summary>
            internal const string Cash = "Cash";
            
            /// <summary>
            /// Stores the resource name 'Client'.
            /// </summary>
            internal const string Client = "Client";
            
            /// <summary>
            /// Stores the resource name 'ClientBirthId'.
            /// </summary>
            internal const string ClientBirthId = "ClientBirthId";
            
            /// <summary>
            /// Stores the resource name 'ClientDegree'.
            /// </summary>
            internal const string ClientDegree = "ClientDegree";
            
            /// <summary>
            /// Stores the resource name 'ClientFirstName'.
            /// </summary>
            internal const string ClientFirstName = "ClientFirstName";
            
            /// <summary>
            /// Stores the resource name 'ClientGroup'.
            /// </summary>
            internal const string ClientGroup = "ClientGroup";
            
            /// <summary>
            /// Stores the resource name 'ClientGroupId'.
            /// </summary>
            internal const string ClientGroupId = "ClientGroupId";
            
            /// <summary>
            /// Stores the resource name 'ClientId'.
            /// </summary>
            internal const string ClientId = "ClientId";
            
            /// <summary>
            /// Stores the resource name 'ClientLastName'.
            /// </summary>
            internal const string ClientLastName = "ClientLastName";
            
            /// <summary>
            /// Stores the resource name 'ClientPersonalId'.
            /// </summary>
            internal const string ClientPersonalId = "ClientPersonalId";
            
            /// <summary>
            /// Stores the resource name 'MealCount'.
            /// </summary>
            internal const string MealCount = "MealCount";
            
            /// <summary>
            /// Stores the resource name 'MealPrice'.
            /// </summary>
            internal const string MealPrice = "MealPrice";
            
            /// <summary>
            /// Stores the resource name 'Organization'.
            /// </summary>
            internal const string Organization = "Organization";
            
            /// <summary>
            /// Stores the resource name 'OrganizationId'.
            /// </summary>
            internal const string OrganizationId = "OrganizationId";
            
            /// <summary>
            /// Stores the resource name 'OrganizationName'.
            /// </summary>
            internal const string OrganizationName = "OrganizationName";
            
            /// <summary>
            /// Stores the resource name 'Others'.
            /// </summary>
            internal const string Others = "Others";
            
            /// <summary>
            /// Stores the resource name 'Resort'.
            /// </summary>
            internal const string Resort = "Resort";
            
            /// <summary>
            /// Stores the resource name 'ResortCode'.
            /// </summary>
            internal const string ResortCode = "ResortCode";
            
            /// <summary>
            /// Stores the resource name 'ResortId'.
            /// </summary>
            internal const string ResortId = "ResortId";
            
            /// <summary>
            /// Stores the resource name 'ResortName'.
            /// </summary>
            internal const string ResortName = "ResortName";
            
            /// <summary>
            /// Stores the resource name 'SalaryDrawback'.
            /// </summary>
            internal const string SalaryDrawback = "SalaryDrawback";
            
            /// <summary>
            /// Stores the resource name 'SubsidedMealCount'.
            /// </summary>
            internal const string SubsidedMealCount = "SubsidedMealCount";
            
            /// <summary>
            /// Stores the resource name 'SubsidedMealPrice'.
            /// </summary>
            internal const string SubsidedMealPrice = "SubsidedMealPrice";
            
            /// <summary>
            /// Stores the resource name 'Transfer'.
            /// </summary>
            internal const string Transfer = "Transfer";
            
            /// <summary>
            /// Stores the resource name 'VkladPrevod'.
            /// </summary>
            internal const string VkladPrevod = "VkladPrevod";
        }
    }
}
