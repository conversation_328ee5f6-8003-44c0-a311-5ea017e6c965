using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using System.Reflection;

namespace Anete.Utils.AppServices.Resolvers
{
    /// <summary>
    /// Service, pouzivany pro ziskani assembly z jejich nazvu a typu, ktery se ma v assembly nachazet.
    /// Vyuziva se prozatim pri deserializaci, kdy je mozne zajistit, aby se nacetly i tridy, ktere byly prejmenovany
    /// nebo premisteny do jine assembly.
    /// </summary>
    public interface IAssemblyResolver
    {
        /// <summary>
        /// Pro dany typ a assembly vrati assembly, v ktere je typ umisten.
        /// Seznam se vraci protoze nelze predem rici, jaka assembly bude vhodna a teoreticky se typ
        /// muze na zaklade stejneho pravidla nachazet ve vice assembly. 
        /// Typicky se jedna napriklad o assembly pro ruzne verze FW.
        /// </summary>
        /// <param name="assemblyName">Nazev assembly</param>
        /// <param name="typeName">Ocekavany typ, ktery by se mel v assembly nachazet. Jedna se o pomocny udaj.</param>
        /// <returns>Seznam nazvu assembly, pro ktere bylo nalezeno pravidlo</returns>
        IEnumerable<string> ResolveAssembly(string assemblyName, string typeName);
    }
}
