//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate Fluent Mapping template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;
using FluentNHibernate.Mapping;
using FluentNHibernate.MappingModel.Collections;

namespace Anete.Common.Data.Nh.Entities
{
    /// <summary>
    /// There are no comments for KpsNotifyUserMap in the schema.
    /// </summary>
    public partial class KpsNotifyUserMap : ClassMap<KpsNotifyUser>
    {
        /// <summary>
        /// There are no comments for KpsNotifyUserMap constructor in the schema.
        /// </summary>
        public KpsNotifyUserMap()
        {
              Schema(@"dba");
              Table(@"KPS_Notifikace");
              LazyLoad();
              Id(x => x.KpsNotifyUserId)
                .Column("Id")
                .CustomType("Int32")
                .Access.Property()                
                .GeneratedBy.Identity();
              References(x => x.Workplace)
                .Class<Workplace>()
                .Access.Property()
                .Cascade.None()
                .LazyLoad()
                .Columns("id_jidelna");
              References(x => x.User)
                .Class<User>()
                .Access.Property()
                .Cascade.None()
                .LazyLoad()
                .Columns("IdUzivatel");
              References(x => x.MessageType)
                .Class<KpsMessageType>()
                .Access.Property()
                .Cascade.None()
                .LazyLoad()
                .Columns("typdotazu_id");
              ExtendMapping();
        }

        #region Partial Methods

        partial void ExtendMapping();

        #endregion
    }

}
