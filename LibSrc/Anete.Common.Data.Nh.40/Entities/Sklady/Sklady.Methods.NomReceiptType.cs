//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate Fluent Mapping template.
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;
using FluentNHibernate.Mapping;
using FluentNHibernate.MappingModel.Collections;

namespace Anete.Common.Data.Nh.Entities.Sklady
{
  public partial class NomReceiptType
  {
               /// <summary>
		       /// Nastavi novou hodnotu navigacni property se zachovanim konzistentnich udaju ve vazebnich kolekcich
		       /// </summary>
           public virtual void SetNomReceipt(NomReceipt value)
           {
              NomReceipt oldValue = NomReceipt;
              NomReceipt = value;

              // uprava vazby
              if (value != null)
              {
                value.ReceiptTypes.Add(this);
              }
              if (oldValue != null)
              {
                oldValue.ReceiptTypes.Remove(this);
              }
           }
                      /// <summary>
		       /// Nastavi novou hodnotu navigacni property se zachovanim konzistentnich udaju ve vazebnich kolekcich
		       /// </summary>
           public virtual void SetUnit(SccUnit value)
           {
              SccUnit oldValue = Unit;
              Unit = value;

              // uprava vazby
              if (value != null)
              {
                value.NomReceiptTypes.Add(this);
              }
              if (oldValue != null)
              {
                oldValue.NomReceiptTypes.Remove(this);
              }
           }
             }
}
