using System;
using System.ComponentModel;
using System.Diagnostics;
using Anete.Config.Core.Attributes;
using Anete.Config.Core.Categories;
using Anete.Config.Core.CFSystKonfig;
using Anete.Config.Core.Internals;
using Anete.Common.Core.Interface.Enums;
using Anete.Config.Configs.Core.Shared;

namespace Anete.Config.Configs.Core.CashDesk.Behaviour
{
    
    /// <summary>
    /// Nastaveni uzivatelske<PERSON> rozhrani
    /// </summary>
    [CashDeskBehaviourConfigDBKey("UserInterface")]
    [ConfigVersion("*******")]
    [ConfigClassSRDescription(typeof(CashDeskBehaviourUserInterfaceConfig))]
    [DefaultConfigInstance(false)]
    [CashDeskCategory(CategoryLevel.Basic, CategoryLevel.Advanced)]
    [OfflineConfig(true, ApplicationType.CashDesk)]
    public class CashDeskBehaviourUserInterfaceConfig : ConfigBase
    {

        private const InterfaceType _interfaceTypeDefValue = InterfaceType.Touch;
        private InterfaceType _interfaceType = _interfaceTypeDefValue;
        /// <summary>
        /// Zpusob ovladani Kasy
        /// </summary>
		[ConfigSRCategory("Sale", typeof(CashDeskBehaviourUserInterfaceConfig))]
		[DefaultValue(_interfaceTypeDefValue)]
        [ConfigSRDisplayName("InterfaceType", typeof(CashDeskBehaviourUserInterfaceConfig))]
        [ConfigSRDescription("InterfaceType", typeof(CashDeskBehaviourUserInterfaceConfig))]
        [InterfaceTypeMapToCFSystKonfigAttribute("KASA:Ovladani.Rezim", MapToCFSystKonfigOption.ReadFromCFSystKonfig)]
        public InterfaceType InterfaceType
        {
			get { return _interfaceType; }
            set
            {
                if (_interfaceType == value)
                {
                    return;
                }
                _interfaceType = value;
                OnPropertyChanged("InterfaceType");
            }
        }

        private const bool _fullScreenDefValue = true;
        private bool _fullScreen = _fullScreenDefValue;
        /// <summary>
        /// Cela obrazovka
        /// </summary>
		[ConfigSRCategory("Sale", typeof(CashDeskBehaviourUserInterfaceConfig))]
        [DefaultValue(_fullScreenDefValue)]
        [ConfigSRDisplayName("FullScreen", typeof(CashDeskBehaviourUserInterfaceConfig))]
        [ConfigSRDescription("FullScreen", typeof(CashDeskBehaviourUserInterfaceConfig))]
        public bool FullScreen
        {
			get { return _fullScreen; }
            set
            {
                if (_fullScreen == value)
                {
                    return;
                }
                _fullScreen = value;
                OnPropertyChanged("FullScreen");
            }
        }

        private const KeepAppFocus _keepAppFocusDefValue = KeepAppFocus.OnlyForKeyboard;
        private KeepAppFocus _keepAppFocus = _keepAppFocusDefValue;
        /// <summary>
        /// Udrzovat focus trvale v aplikaci
        /// </summary>
		[ConfigSRCategory("Sale", typeof(CashDeskBehaviourUserInterfaceConfig))]
		[DefaultValue(typeof(KeepAppFocus), "OnlyForKeyboard")]
        [ConfigSRDisplayName("KeepAppFocus", typeof(CashDeskBehaviourUserInterfaceConfig))]
        [ConfigSRDescriptionWithEnumDescription("KeepAppFocus", typeof(KeepAppFocus), typeof(CashDeskBehaviourUserInterfaceConfig))]
        public KeepAppFocus KeepAppFocus
        {
			get { return _keepAppFocus; }
            set
            {
                if (_keepAppFocus == value)
                {
                    return;
                }
                _keepAppFocus = value;
                OnPropertyChanged("KeepAppFocus");
            }
        }

		private const ManagerInterfaceType _managerInterfaceTypeDefValue = ManagerInterfaceType.NonTouch;
		private ManagerInterfaceType _managerInterfaceType = _managerInterfaceTypeDefValue;
		/// <summary>
		/// Typ rozhrani managera
		/// </summary>
		[ConfigSRCategory("Manager", typeof(CashDeskBehaviourUserInterfaceConfig))]
		[DefaultValue(typeof(ManagerInterfaceType), "NonTouch")]
		[ConfigSRDisplayName("ManagerInterfaceType", typeof(CashDeskBehaviourUserInterfaceConfig))]
		[ConfigSRDescriptionWithEnumDescription("ManagerInterfaceType", typeof(ManagerInterfaceType), typeof(CashDeskBehaviourUserInterfaceConfig))]
		public ManagerInterfaceType ManagerInterfaceType
		{
			get { return _managerInterfaceType; }
			set
			{
				if (_managerInterfaceType == value)
				{
					return;
				}
				_managerInterfaceType = value;
				OnPropertyChanged("ManagerInterfaceType");
			}
		}

		private const ManagerFullScreenType _managerFullScreenTypeDefValue = ManagerFullScreenType.Auto;
		private ManagerFullScreenType _managerFullScreenType = _managerFullScreenTypeDefValue;
		/// <summary>
		/// Zobrazit manager full screen
		/// </summary>
		[ConfigSRCategory("Manager", typeof(CashDeskBehaviourUserInterfaceConfig))]
		[DefaultValue(typeof(ManagerFullScreenType), "Auto")]
		[ConfigSRDisplayName("ManagerFullScreenType", typeof(CashDeskBehaviourUserInterfaceConfig))]
		[ConfigSRDescriptionWithEnumDescription("ManagerFullScreenType", typeof(ManagerFullScreenType), typeof(CashDeskBehaviourUserInterfaceConfig))]
		public ManagerFullScreenType ManagerFullScreenType
		{
			get { return _managerFullScreenType; }
			set
			{
				if (_managerFullScreenType == value)
				{
					return;
				}
				_managerFullScreenType = value;
				OnPropertyChanged("ManagerFullScreenType");
			}
		}

		private const TouchDeviceType _touchDeviceTypeDefValue = TouchDeviceType.PcWithTouchAndKeyboard;
		private TouchDeviceType _touchDeviceType = _touchDeviceTypeDefValue;
		/// <summary>
		/// Typ zarizeni. Tento typ zarizeni mel puvodne inicializovat specialni settings pro kazde zarizeni, ale pak jsem si rekl,
		/// ze by to zpusobilo spis zmatek a ze bude lepe parametry UI nastavit primo v programu.
		/// </summary>
		[DefaultValue(typeof(TouchDeviceType), "PcWithTouchAndKeyboard")]
		[ConfigSRDisplayName("TouchDeviceType", typeof(CashDeskBehaviourUserInterfaceConfig))]
		[ConfigSRDescriptionWithEnumDescription("TouchDeviceType", typeof(TouchDeviceType), typeof(CashDeskBehaviourUserInterfaceConfig))]
		public TouchDeviceType TouchDeviceType
		{
			get { return _touchDeviceType; }
			set
			{
				if (_touchDeviceType == value)
				{
					return;
				}
				_touchDeviceType = value;
				OnPropertyChanged("TouchDeviceType");
			}
		}

    }
}
