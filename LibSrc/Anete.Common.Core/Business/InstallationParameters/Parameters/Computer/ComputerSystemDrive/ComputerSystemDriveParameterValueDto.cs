using System;
using System.Linq;
using System.Runtime.Serialization;

namespace Anete.Common.Core.Business.InstallationParameters.Parameters.Computer.ComputerSystemDrive
{
    [DataContract(Namespace = Anete.Resources.AneteNamespace.DefaultNamespace)]
    public class ComputerSystemDriveParameterValueDto
    {
        public ComputerSystemDriveParameterValueDto(long capacity, long freeSpace)
        {
            FreeSpace = freeSpace;
            Capacity = capacity;         
        }

        [DataMember(Order = 0)]
        public long Capacity { get; private set; }

        [DataMember(Order = 1)]
        public long FreeSpace { get; private set; }
    }
}
