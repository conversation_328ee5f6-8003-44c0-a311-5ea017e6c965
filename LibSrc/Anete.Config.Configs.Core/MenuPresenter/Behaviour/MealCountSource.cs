using System;
using System.ComponentModel;
using Anete.Config.Core.Internals;
using Anete.Utils.ComponentModel.Attributes;

namespace Anete.Config.Configs.Core.MenuPresenter.Behaviour
{
    /// <summary>
    /// zdroj poctu jidel
    /// </summary>
    [ConfigResourceEnum]
    [TypeConverter(typeof(ConfigEnumConverter<MealCountSource>))]
    public enum MealCountSource
    {
        /// <summary>
        /// Z vydejen
        /// </summary>
        [EnumBasedFactory(typeof(FromCanteensSettings))]
        FromCanteens,
        /// <summary>
        /// Z kas. Zakaznicka uprava pro Slovnaft.
        /// </summary>
        [EnumBasedFactory(typeof(FromCashdesksSettings))]
        FromCashdesks
    }
}
