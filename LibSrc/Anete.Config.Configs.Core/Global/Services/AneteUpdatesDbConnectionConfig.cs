using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Anete.Utils.Extensions;
using Anete.Config.Configs.Core.Shared;
using System.ComponentModel;
using Anete.Config.Core.Attributes;
using Anete.Common.Data.Interface.AppServices.ConnectionString;

namespace Anete.Config.Configs.Core.Global.Services
{
    /// <summary>
    /// Konfigurace pro pripojeni k AneteUpdates
    /// </summary>
    public class AneteUpdatesDbConnectionConfig : DbConnectionConfig, ILinkedServerDbConnectionConfig
	{

        /// <summary>
        /// Initializes a new instance
        /// </summary>
        public AneteUpdatesDbConnectionConfig()
        {
            Database = _databaseDefValue;
        }

        private const string _databaseDefValue = "AneteUpdates";
        /// <summary>
        /// Databaze
        /// </summary>
        /// <value></value>
        [DefaultValue(_databaseDefValue)]
        public override string Database
        {
            get
            {
                return base.Database;
            }
            set
            {
                base.Database = value;
            }
        }

		private const bool _useLinkedServerForReplicationDefValue = false;
		private bool _useLinkedServerForReplication = _useLinkedServerForReplicationDefValue;
		/// <summary>
		/// Pouzije se pro počítače připojeného k replice linkovaný server?
		///  Tzn. že připojeni k databázi AneteUpdates proběhne přes databázi Kredit a pro každý vygenerovaný dotaz se sestaví prefix v podobě [server].[název databáze AneteUpdates].
		///  Pokud je tato volba povolena, využívá se z celé konfigurace připojení k AneteUpdates pouze název databáze.
		/// </summary>
		[DefaultValue(_useLinkedServerForReplicationDefValue)]
		[ConfigSRDisplayName("UseLinkedServerForReplication", typeof(AneteUpdatesDbConnectionConfig))]
		[ConfigSRDescription("UseLinkedServerForReplication", typeof(AneteUpdatesDbConnectionConfig))]
		public bool UseLinkedServerForReplication
		{
			get { return _useLinkedServerForReplication; }
			set
			{
				if (_useLinkedServerForReplication == value)
				{
					return;
				}
				_useLinkedServerForReplication = value;
				OnPropertyChanged("UseLinkedServerForReplication");
			}
		}

		[Browsable(false)]
		bool ILinkedServerDbConnectionConfig.UseLinkedServer
		{
			get
			{
				return UseLinkedServerForReplication;
			}
		}
	}
}
