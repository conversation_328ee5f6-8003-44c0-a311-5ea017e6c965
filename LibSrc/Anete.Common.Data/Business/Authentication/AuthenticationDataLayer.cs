using Anete.Common.Data.Interface.Enums;
using Anete.Utils;
using System;
using System.Data.SqlClient;
using System.Linq;
using System.Xml.Linq;
using Anete.Log4Net.Core;
using Anete.Config.Core;
using Anete.Config.Configs.Core.Global.Behaviour;
using Anete.Common.Core.Interface.Enums;
using Anete.Common.Data.AppServices;
using Anete.Common.Data.Business.MealServing;

namespace Anete.Common.Data.Business.Authentication
{
    public class AuthenticationDataLayer : AuthenticationDataLayerBase
    {
  
        private readonly MealSizeHelper _mealSizeHelper;
        private readonly SqlCommandXmlHelper _sqlCommandXmlHelper;
        private readonly ClientDisplayedIdentType _clientIdentification;
        private readonly IKreditDbConnectionFactory _kreditDbConnectionFactory;

        public AuthenticationDataLayer(MealSizeHelper mealSizeHelper,
            SqlCommandXmlHelper sqlCommandXmlHelper, 
            IConfigManager configManager, IKreditDbConnectionFactory kreditDbConnectionFactory)
        {
            _kreditDbConnectionFactory = kreditDbConnectionFactory; 
            _sqlCommandXmlHelper = sqlCommandXmlHelper;
            _mealSizeHelper = mealSizeHelper;          

            _clientIdentification = configManager.GetConfig<GlobalBehaviourClientConfig>().ClientIdentification;
        }

        protected override void InitLogInt(ILogEx log)
        {
            base.InitLogInt(log);

            _sqlCommandXmlHelper.InitLog(log);
        }

        protected override ProcessCardResult ProcessCardInt(short appInstallationId, short deviceId, string cardCode)
        {
            string xmlResult = "";
            using (SqlConnection connection = _kreditDbConnectionFactory.CreateConnection())
            {
                connection.Open();
                using (SqlCommand commnad = _kreditDbConnectionFactory.CreateCommand(connection))
                {                    
                    commnad.CommandText = "[dbo].VYM8_Prihlaseni";
                    commnad.CommandType = System.Data.CommandType.StoredProcedure;
                    commnad.Parameters.Add("id_zarizeni", System.Data.SqlDbType.SmallInt).Value = appInstallationId;
                    commnad.Parameters.Add("id_ctecka", System.Data.SqlDbType.SmallInt).Value = deviceId;
                    commnad.Parameters.Add("kod", System.Data.SqlDbType.VarChar, 32).Value = cardCode;
                    xmlResult = _sqlCommandXmlHelper.GetSqlCommandXmlResult(commnad);
                }
            }

            if (string.IsNullOrEmpty(xmlResult))
            {
                // Tato SP musi vzdy neco vratit
                throw new ArgumentNullException("xmlResult", "SP [dbo].VYM8_Prihlaseni musi vzdy vratit xml");
            }

            string xml = string.Format("<root>{0}</root>", xmlResult);
            XElement root = XElement.Parse(xml);
            // SP musi vratit vzdy pouze jeden element
            XElement xNode = root.Elements().Single();
            if (xNode.Name == "Error")
            {
                // typ chyby musim priradit enumu
                CardState cardInfoState = EnumUtils.ParseWithAliases<CardState>((string)xNode.Attribute("Code"), false);
                return new ProcessCardResult(null, cardInfoState);
            }
            else if (xNode.Name == "Syscard")
            {
                FunkceKaret funkceKaret = (FunkceKaret)(int)xNode.Attribute("Funkce");
                return new ProcessCardResult(new SystemCard(cardCode, funkceKaret, (int)xNode.Attribute("Parametr")), CardState.Ok);
            }
            else if (xNode.Name == "Client")
            {
                // tag VelPorce je volitelny
                short? mealSizeShort = (short?)xNode.Attribute("VelPorce");
                MealSize mealSize;
                if (mealSizeShort != null)
                {
                    mealSize = _mealSizeHelper.MealSizeFromDb(mealSizeShort);
                }
                else
                {
                    mealSize = MealSize.NotDefined;
                }

                // kategorie dotace me nezajima, proto mam natvrdo 1
                Client client = new Client((string)xNode.Attribute("Titul"), (string)xNode.Attribute("Jmeno"), (string)xNode.Attribute("Prijmeni"),
                    (int)xNode.Attribute("EvCislo"), (string)xNode.Attribute("OCS"), (string)xNode.Attribute("RCS"),
                    (decimal)xNode.Attribute("DispCastka"), (string)xNode.Attribute("Skupina"), (string)xNode.Attribute("Organizace"),
                    (string)xNode.Attribute("Stredisko"), mealSize, (int)xNode.Attribute("StavBonu"), 0, (decimal)xNode.Attribute("StavUctu"), 1, _clientIdentification,
                    (string)xNode.Attribute("KodKarty"), cardCode);
                
                ApplicationLanguage jidJazyk = LoadJidJazyk(xNode);
                ApplicationLanguage uiLanguage;
                // muze byt null na datech verze 17.1 nebo pokud neni aktualizovana procka
                XAttribute appJazykAttribute = xNode.Attribute("AppJazyk");
                if (appJazykAttribute != null)
                {
                    uiLanguage = LoadMenuLanguage(appJazykAttribute);
                }
                else
                {
                    // vezmu nastaveni jidelnicku
                    uiLanguage = jidJazyk; // ApplicationLanguageExt.GetFromThreadUiCulture();
                }

                client.InitLanguage(jidJazyk, uiLanguage);

                return new ProcessCardResult(new ClientCard(cardCode, client), CardState.Ok);
            }
            else
            {
                throw ExcUtils.ArgumentOutOfRange("xNode.Name", xNode.Name);
            }
        }

        protected virtual ApplicationLanguage LoadMenuLanguage(XAttribute appJazykAttribute)
        {
            return (ApplicationLanguage)(short)appJazykAttribute;
        }

        protected virtual ApplicationLanguage LoadJidJazyk(XElement xNode)
        {
            return (ApplicationLanguage)(short)xNode.Attribute("JidJazyk");
        }
    }
}
