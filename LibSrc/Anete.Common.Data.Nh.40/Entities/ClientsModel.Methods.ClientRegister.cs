//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate Fluent Mapping template.
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;
using FluentNHibernate.Mapping;
using FluentNHibernate.MappingModel.Collections;

namespace Anete.Common.Data.Nh.Entities
{
  public partial class ClientRegister
  {
               /// <summary>
		       /// Nastavi novou hodnotu navigacni property se zachovanim konzistentnich udaju ve vazebnich kolekcich
		       /// </summary>
           public virtual void SetResort(Resort value)
           {
              Resort oldValue = Resort;
              Resort = value;

              // uprava vazby
              if (value != null)
              {
                value.ClientRegisters.Add(this);
              }
              if (oldValue != null)
              {
                oldValue.ClientRegisters.Remove(this);
              }
           }
                      /// <summary>
		       /// Nastavi novou hodnotu navigacni property se zachovanim konzistentnich udaju ve vazebnich kolekcich
		       /// </summary>
           public virtual void SetClientGroup(ClientGroup value)
           {
              ClientGroup oldValue = ClientGroup;
              ClientGroup = value;

              // uprava vazby
              if (value != null)
              {
                value.ClientRegisters.Add(this);
              }
              if (oldValue != null)
              {
                oldValue.ClientRegisters.Remove(this);
              }
           }
                      /// <summary>
		       /// Nastavi novou hodnotu navigacni property se zachovanim konzistentnich udaju ve vazebnich kolekcich
		       /// </summary>
           public virtual void SetLanguage(Language value)
           {
              Language oldValue = Language;
              Language = value;

              // uprava vazby
              if (value != null)
              {
                value.ClientRegisters.Add(this);
              }
              if (oldValue != null)
              {
                oldValue.ClientRegisters.Remove(this);
              }
           }
                      /// <summary>
		       /// Nastavi novou hodnotu navigacni property se zachovanim konzistentnich udaju ve vazebnich kolekcich
		       /// </summary>
           public virtual void SetApplicationLanguage(Language value)
           {
              Language oldValue = ApplicationLanguage;
              ApplicationLanguage = value;

              // uprava vazby
              if (value != null)
              {
                value.ClientRegisters1.Add(this);
              }
              if (oldValue != null)
              {
                oldValue.ClientRegisters1.Remove(this);
              }
           }
             }
}
