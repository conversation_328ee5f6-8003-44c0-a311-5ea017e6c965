using System;
using System.Collections.Generic;
using System.Linq;

namespace Anete.AutoUpgrades.Admin.Services.UpgradeChecker
{	
	/// <summary>
	/// J<PERSON>ym zpusobem se bude aktualizovat vzhledem k jiz existujicim prubehu instalaci na danem pocitaci
	/// </summary>
	public enum UpdateByPastUpdates
	{
		/// <summary>
		/// Aplikace se bude instalovat, pokud jeste neni nainstalovana a zaroven nebyla nainstalovana chybne
		/// </summary>
		InstallIfNotInstalledAndNotFailed,
		/// <summary>
		/// Aplikace se bude instalovat, pokud jeste neni nainstalovana a to i presto, ze jiz byl ucinen pokud o nezdarilou instalaci
		/// </summary>
		InstallIfNotInstalled
	}	
}
