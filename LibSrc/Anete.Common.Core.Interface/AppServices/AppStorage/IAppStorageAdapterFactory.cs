using System;
using System.Collections.Generic;
using Anete.Common.Core.Interface.AppServices.AppStorage.Adapters;
using Anete.Utils.AppServices.TypeAdapterLocators;

namespace Anete.Common.Core.Interface.AppServices.AppStorage
{
	/// <summary>
	/// Service pro registraci a ziskani trid pro ulozeni a nacteni layoutu jednotlivych controls.
	/// Kazdy layout se registruje pomoci typu Control, pro kterou patri.
	/// </summary>
	public interface IAppStorageAdapterFactory : ITypeAdapterFactoryRegistrator
	{

		/// <summary>
		/// Vyhleda spravne adaptery podle typu komponenty a vytvori je.
		/// Umoznuji mit pouze jeden adapter pro jeden typ - pokud bych jich mel vice, bude se zbytecne ukladat jedna instance
		/// pomoci vice adapteru. Pak stejne nebudu schopen nacist jednu instanci vicekrat.
		/// </summary>
		/// <param name="componentType">Typ komponenty, pro kterou hledam adapter</param>
		/// <returns>Seznam vytvorenych adapteru</returns>
		IAppStorageAdapter CreateAdapter(Type componentType);

		/// <summary>
		/// Vytvori adapter podle daneho jmena typu
		/// </summary>
		/// <param name="adapterType">Typ adapteru</param>
		/// <param name="componentType">Typ nacitane komponenty. Pouziva se pouze pro adaptery implementujici IInstanceCreator</param>
		/// <returns></returns>
		IAppStorageAdapter CreateAdapterByTypeName(string adapterType, Type componentType);

	}
}
