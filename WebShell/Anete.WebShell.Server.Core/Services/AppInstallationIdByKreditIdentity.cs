using Anete.ApiServices.Authentication;
using Anete.Common.Core.Interface.AppServices;
using Anete.Common.Data.Interface.AppServices;
using Anete.Utils.EventArgs;
using System;

namespace Anete.WebShell.Server.Core.Services
{
	public class AppInstallationIdByKreditIdentity : IAppInstallationIdProvider
	{
		private readonly IAppInstallationIdProvider _appInstallationIdProvider;
		private readonly ICurrentIdentityProvider _identityProvider;

		public AppInstallationIdByKreditIdentity(IAppInstallationIdProvider appInstallationIdProvider, ICurrentIdentityProvider identityProvider)
		{
			_appInstallationIdProvider = appInstallationIdProvider;
			_identityProvider = identityProvider;
		}

		public short AppInstallationId => _identityProvider.FindIdentity<IKreditIdentity>()?.AppInstallationId ?? _appInstallationIdProvider.AppInstallationId;

		public event EventHandler<SimpleDataEventArgs<short>> AppInstallationIdChanged
		{
			add { }
			remove { }
		}
	}
}