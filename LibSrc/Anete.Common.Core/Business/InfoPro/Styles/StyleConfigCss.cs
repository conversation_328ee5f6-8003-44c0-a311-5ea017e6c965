using Anete.Utils.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Anete.Common.Core.Business.InfoPro.Styles
{
	public class StyleConfigCss : StyleConfigBase
	{
		private IEnumerable<EnumBasedParameter<StyleAttributeType, StyleAttributeBase>> _attributes = Array.Empty<EnumBasedParameter<StyleAttributeType, StyleAttributeBase>>();
		/// <summary>
		/// CSS atributy
		/// </summary>
		public IEnumerable<EnumBasedParameter<StyleAttributeType, StyleAttributeBase>> Attributes
		{
			get
			{
				return _attributes;
			}
			set
			{
				if (_attributes != value)
				{
					_attributes = value;
					OnPropertyChanged();
				}
			}
		}

		private EnumBasedParameter<StyleSelectorType, StyleSelectorBase> _selector;
		/// <summary>
		/// Selektor
		/// </summary>
		public EnumBasedParameter<StyleSelectorType, StyleSelectorBase> Selector
		{
			get
			{
				return _selector;
			}
			set
			{
				if (_selector != value)
				{
					_selector = value;
					OnPropertyChanged();
				}
			}
		}

		public override string ToCss()
		{
			var formattedAttributes = Attributes
				.OrderBy(x => x.Type)
				.Select(x => x.Value.ToCss(x.Type));

			return $"{Selector.Value?.ToCss()} {{\n{formattedAttributes.ToDelimitedString(";\n")}\n}}";
		}
	}
}