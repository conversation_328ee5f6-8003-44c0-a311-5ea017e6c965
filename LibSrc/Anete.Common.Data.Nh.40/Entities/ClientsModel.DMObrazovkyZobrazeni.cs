//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Anete.Common.Data.Nh.Entities
{

    /// <summary>
    /// Tabulka: dba.DM_Obrazovky_Zobrazeni
    /// </summary>
    public partial class DMObrazovkyZobrazeni : Anete.Data.Nh.NhEntityBase, INotifyPropertyChanging, INotifyPropertyChanged {

        private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(System.String.Empty);

        private int _IdZobrazeni;

        private System.DateTime _CasOd;

        private System.DateTime? _CasDo;

        private AppInstallation _AppInstallation;

        private DMObrazovky _DMObrazovky;
    
        #region Extensibility Method Definitions
        
        partial void OnCreated();

        public override bool Equals(object obj)
        {
          DMObrazovkyZobrazeni toCompare = obj as DMObrazovkyZobrazeni;
          if (toCompare == null)
          {
            return false;
          }

          if (!Object.Equals(this.IdZobrazeni, toCompare.IdZobrazeni))
            return false;
         // pokud mam default primary key, znamena to, ze jedna o nove vytvorenou entitu, ktera jeste nema key prirazen. 
        // V takove pripade musim porovnat podle instance
        bool isDefault = true;
        if (this.IdZobrazeni != default(int))
        {
          isDefault = false;
        }
     

          if (isDefault)
          {
            return object.ReferenceEquals(toCompare, this);
          }     
          return true;
        }

	private int? _hashCode = null;
        public override int GetHashCode()
        {
	if (_hashCode == null)
          {
          _hashCode = 13;
          _hashCode = (_hashCode * 7) + IdZobrazeni.GetHashCode();
}
          return _hashCode.Value;
        }
        partial void OnIdZobrazeniChanging(int value);
        
        partial void OnIdZobrazeniChanged();
        partial void OnCasOdChanging(System.DateTime value);
        
        partial void OnCasOdChanged();
        partial void OnCasDoChanging(System.DateTime? value);
        
        partial void OnCasDoChanged();
        partial void OnAppInstallationChanging(AppInstallation value);

        partial void OnAppInstallationChanged();
        partial void OnDMObrazovkyChanging(DMObrazovky value);

        partial void OnDMObrazovkyChanged();
        
        #endregion
        public DMObrazovkyZobrazeni()
        {
            OnCreated();
        }

    
        /// <summary>
        /// Sloupec: id_zobrazeni
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("IdZobrazeni", typeof(DMObrazovkyZobrazeniSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(DMObrazovkyZobrazeni), Tag="IdZobrazeni")]
        public virtual int IdZobrazeni
        {
            get
            {
                return this._IdZobrazeni;
            }
            set
            {
                if (this._IdZobrazeni != value)
                {
                    this.OnIdZobrazeniChanging(value);
                    this.SendPropertyChanging();
                    this._IdZobrazeni = value;
                    this.SendPropertyChanged("IdZobrazeni");
                    this.OnIdZobrazeniChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: cas_od
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("CasOd", typeof(DMObrazovkyZobrazeniSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(DMObrazovkyZobrazeni), Tag="CasOd")]
        public virtual System.DateTime CasOd
        {
            get
            {
                return this._CasOd;
            }
            set
            {
                if (this._CasOd != value)
                {
                    this.OnCasOdChanging(value);
                    this.SendPropertyChanging();
                    this._CasOd = value;
                    this.SendPropertyChanged("CasOd");
                    this.OnCasOdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: cas_do
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("CasDo", typeof(DMObrazovkyZobrazeniSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual System.DateTime? CasDo
        {
            get
            {
                return this._CasDo;
            }
            set
            {
                if (this._CasDo != value)
                {
                    this.OnCasDoChanging(value);
                    this.SendPropertyChanging();
                    this._CasDo = value;
                    this.SendPropertyChanged("CasDo");
                    this.OnCasDoChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: id_zarizeni
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("AppInstallation", typeof(DMObrazovkyZobrazeniSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual AppInstallation AppInstallation
        {
            get
            {
                return this._AppInstallation;
            }
            set
            {
                if (this._AppInstallation != value)
                {
                    this.OnAppInstallationChanging(value);
                    this.SendPropertyChanging();
                    this._AppInstallation = value;
                    this.SendPropertyChanged("AppInstallation");
                    this.OnAppInstallationChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: id_obrazovka
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("DMObrazovky", typeof(DMObrazovkyZobrazeniSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual DMObrazovky DMObrazovky
        {
            get
            {
                return this._DMObrazovky;
            }
            set
            {
                if (this._DMObrazovky != value)
                {
                    this.OnDMObrazovkyChanging(value);
                    this.SendPropertyChanging();
                    this._DMObrazovky = value;
                    this.SendPropertyChanged("DMObrazovky");
                    this.OnDMObrazovkyChanged();
                }
            }
        }
   
        public virtual event PropertyChangingEventHandler PropertyChanging;

	// Mam v bazove tride, nema smysl generovat
	// public virtual event PropertyChangedEventHandler PropertyChanged;

        protected virtual void SendPropertyChanging()
        {
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, emptyChangingEventArgs);
        }

        protected virtual void SendPropertyChanging(System.String propertyName) 
        {   	
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, new PropertyChangingEventArgs(propertyName));
        }

        protected virtual void SendPropertyChanged(System.String propertyName)
        {    
		// mam v bazove tride, proto musim volat takto jednoduse
		OnPropertyChanged(propertyName);
/*		        var handler = this.PropertyChanged;
            if (handler != null)
                handler(this, new PropertyChangedEventArgs(propertyName));*/
        }
    }

}
