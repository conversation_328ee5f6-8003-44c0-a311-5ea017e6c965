//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Anete.Common.Data.Nh.Entities
{

    /// <summary>
    /// Tabulka: dba.CFJidelny
    /// Provozovna
    /// jiné názvy:
    /// Jídelna (zastaralé)
    /// Vývařovna (zastaralé)
    /// </summary>
    public partial class Workplace : Anete.Data.Nh.NhEntityBase, INotifyPropertyChanging, INotifyPropertyChanged {

        private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(System.String.Empty);

        private short _WorkplaceId;

        private string _Name;

        private string _Address;

        private string _TelefonModem;

        private string _ShortName;

        private string _KontaktniOsoba;

        private bool _Dietary;

        private string _Note;

        private ISet<Canteen> _Canteens;

        private Provider _Provider;

        private Stock _Stock;

        private ISet<PriceVatRate> _PriceVatRates;

        private ISet<RodinaDietVybery> _RodinaDietVybery;

        private ISet<MealTemplate> _MealTemplates;

        private ISet<MenuRowEan> _MenuRowEans;

        private ISet<Price> _Prices;

        private ISet<MenuTemplate> _MenuTemplates;

        private ISet<MealKind> _MealKinds;

        private ISet<Order> _Orders;

        private ISet<MenuGroup> _MenuGroups;

        private ISet<ServingPeriod> _ServingPeriods;

        private ISet<KpsNotifyUser> _KpsNotifyUsers;

        private ISet<MealRestriction> _MealRestriction;

        private ISet<Cook> _Cooks;

        private ISet<DeliveryOrder> _DeliveryOrders;

        private ISet<VydAutomatyMapovaniPozice> _VydAutomatyMapovaniPozice;
    
        #region Extensibility Method Definitions
        
        partial void OnCreated();

        public override bool Equals(object obj)
        {
          Workplace toCompare = obj as Workplace;
          if (toCompare == null)
          {
            return false;
          }

          if (!Object.Equals(this.WorkplaceId, toCompare.WorkplaceId))
            return false;
         // pokud mam default primary key, znamena to, ze jedna o nove vytvorenou entitu, ktera jeste nema key prirazen. 
        // V takove pripade musim porovnat podle instance
        bool isDefault = true;
        if (this.WorkplaceId != default(short))
        {
          isDefault = false;
        }
     

          if (isDefault)
          {
            return object.ReferenceEquals(toCompare, this);
          }     
          return true;
        }

	private int? _hashCode = null;
        public override int GetHashCode()
        {
	if (_hashCode == null)
          {
          _hashCode = 13;
          _hashCode = (_hashCode * 7) + WorkplaceId.GetHashCode();
}
          return _hashCode.Value;
        }
        partial void OnWorkplaceIdChanging(short value);
        
        partial void OnWorkplaceIdChanged();
        partial void OnNameChanging(string value);
        
        partial void OnNameChanged();
        partial void OnAddressChanging(string value);
        
        partial void OnAddressChanged();
        partial void OnTelefonModemChanging(string value);
        
        partial void OnTelefonModemChanged();
        partial void OnShortNameChanging(string value);
        
        partial void OnShortNameChanged();
        partial void OnKontaktniOsobaChanging(string value);
        
        partial void OnKontaktniOsobaChanged();
        partial void OnDietaryChanging(bool value);
        
        partial void OnDietaryChanged();
        partial void OnNoteChanging(string value);
        
        partial void OnNoteChanged();
        partial void OnProviderChanging(Provider value);

        partial void OnProviderChanged();
        partial void OnStockChanging(Stock value);

        partial void OnStockChanged();
        
        #endregion
        public Workplace()
        {
            this._Canteens = new HashSet<Canteen>();
            this._PriceVatRates = new HashSet<PriceVatRate>();
            this._RodinaDietVybery = new HashSet<RodinaDietVybery>();
            this._MealTemplates = new HashSet<MealTemplate>();
            this._MenuRowEans = new HashSet<MenuRowEan>();
            this._Prices = new HashSet<Price>();
            this._MenuTemplates = new HashSet<MenuTemplate>();
            this._MealKinds = new HashSet<MealKind>();
            this._Orders = new HashSet<Order>();
            this._MenuGroups = new HashSet<MenuGroup>();
            this._ServingPeriods = new HashSet<ServingPeriod>();
            this._KpsNotifyUsers = new HashSet<KpsNotifyUser>();
            this._MealRestriction = new HashSet<MealRestriction>();
            this._Cooks = new HashSet<Cook>();
            this._DeliveryOrders = new HashSet<DeliveryOrder>();
            this._VydAutomatyMapovaniPozice = new HashSet<VydAutomatyMapovaniPozice>();
            OnCreated();
        }

    
        /// <summary>
        /// Sloupec: id_jidelna
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("WorkplaceId", typeof(WorkplaceSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableRangeValidator(typeof(Workplace), (Int16)(0),  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, (Int16)(32767),  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="WorkplaceId")]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(Workplace), Tag="WorkplaceId")]
        public virtual short WorkplaceId
        {
            get
            {
                return this._WorkplaceId;
            }
            set
            {
                if (this._WorkplaceId != value)
                {
                    this.OnWorkplaceIdChanging(value);
                    this.SendPropertyChanging();
                    this._WorkplaceId = value;
                    this.SendPropertyChanged("WorkplaceId");
                    this.OnWorkplaceIdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: nazev
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Name", typeof(WorkplaceSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(Workplace), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 90,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="Name")]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(Workplace), Tag="Name")]
        public virtual string Name
        {
            get
            {
                return this._Name;
            }
            set
            {
                if (this._Name != value)
                {
                    this.OnNameChanging(value);
                    this.SendPropertyChanging();
                    this._Name = value;
                    this.SendPropertyChanged("Name");
                    this.OnNameChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: adresa
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Address", typeof(WorkplaceSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(Workplace), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 50,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="Address")]
        public virtual string Address
        {
            get
            {
                return this._Address;
            }
            set
            {
                if (this._Address != value)
                {
                    this.OnAddressChanging(value);
                    this.SendPropertyChanging();
                    this._Address = value;
                    this.SendPropertyChanged("Address");
                    this.OnAddressChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: telefon_modem
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("TelefonModem", typeof(WorkplaceSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(Workplace), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 20,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="TelefonModem")]
        public virtual string TelefonModem
        {
            get
            {
                return this._TelefonModem;
            }
            set
            {
                if (this._TelefonModem != value)
                {
                    this.OnTelefonModemChanging(value);
                    this.SendPropertyChanging();
                    this._TelefonModem = value;
                    this.SendPropertyChanged("TelefonModem");
                    this.OnTelefonModemChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: zkraceny_nazev
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("ShortName", typeof(WorkplaceSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(Workplace), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 20,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="ShortName")]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(Workplace), Tag="ShortName")]
        public virtual string ShortName
        {
            get
            {
                return this._ShortName;
            }
            set
            {
                if (this._ShortName != value)
                {
                    this.OnShortNameChanging(value);
                    this.SendPropertyChanging();
                    this._ShortName = value;
                    this.SendPropertyChanged("ShortName");
                    this.OnShortNameChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: kontaktni_osoba
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("KontaktniOsoba", typeof(WorkplaceSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(Workplace), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 20,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="KontaktniOsoba")]
        public virtual string KontaktniOsoba
        {
            get
            {
                return this._KontaktniOsoba;
            }
            set
            {
                if (this._KontaktniOsoba != value)
                {
                    this.OnKontaktniOsobaChanging(value);
                    this.SendPropertyChanging();
                    this._KontaktniOsoba = value;
                    this.SendPropertyChanged("KontaktniOsoba");
                    this.OnKontaktniOsobaChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: dietni
        /// Jedna se o dietni provozovnu? Pro takovou se zobrazuje specificky jidelnicek.
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Dietary", typeof(WorkplaceSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(Workplace), Tag="Dietary")]
        public virtual bool Dietary
        {
            get
            {
                return this._Dietary;
            }
            set
            {
                if (this._Dietary != value)
                {
                    this.OnDietaryChanging(value);
                    this.SendPropertyChanging();
                    this._Dietary = value;
                    this.SendPropertyChanged("Dietary");
                    this.OnDietaryChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: poznamka
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Note", typeof(WorkplaceSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(Workplace), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 250,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="Note")]
        public virtual string Note
        {
            get
            {
                return this._Note;
            }
            set
            {
                if (this._Note != value)
                {
                    this.OnNoteChanging(value);
                    this.SendPropertyChanging();
                    this._Note = value;
                    this.SendPropertyChanged("Note");
                    this.OnNoteChanged();
                }
            }
        }

        [Anete.Data.Attributes.EntitySRDisplayName("Canteens", typeof(WorkplaceSR))]
        [Anete.Data.Attributes.DbCollectionProperty]
        public virtual ISet<Canteen> Canteens
        {
            get
            {
                return this._Canteens;
            }
            set
            {
                this._Canteens = value;
            }
        }

    
        /// <summary>
        /// Sloupec: id_provozovatel
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Provider", typeof(WorkplaceSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual Provider Provider
        {
            get
            {
                return this._Provider;
            }
            set
            {
                if (this._Provider != value)
                {
                    this.OnProviderChanging(value);
                    this.SendPropertyChanging();
                    this._Provider = value;
                    this.SendPropertyChanged("Provider");
                    this.OnProviderChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: id_sklad
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Stock", typeof(WorkplaceSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual Stock Stock
        {
            get
            {
                return this._Stock;
            }
            set
            {
                if (this._Stock != value)
                {
                    this.OnStockChanging(value);
                    this.SendPropertyChanging();
                    this._Stock = value;
                    this.SendPropertyChanged("Stock");
                    this.OnStockChanged();
                }
            }
        }

        [Anete.Data.Attributes.EntitySRDisplayName("PriceVatRates", typeof(WorkplaceSR))]
        [Anete.Data.Attributes.DbCollectionProperty]
        public virtual ISet<PriceVatRate> PriceVatRates
        {
            get
            {
                return this._PriceVatRates;
            }
            set
            {
                this._PriceVatRates = value;
            }
        }

        [Anete.Data.Attributes.EntitySRDisplayName("RodinaDietVybery", typeof(WorkplaceSR))]
        [Anete.Data.Attributes.DbCollectionProperty]
        public virtual ISet<RodinaDietVybery> RodinaDietVybery
        {
            get
            {
                return this._RodinaDietVybery;
            }
            set
            {
                this._RodinaDietVybery = value;
            }
        }

        [Anete.Data.Attributes.EntitySRDisplayName("MealTemplates", typeof(WorkplaceSR))]
        [Anete.Data.Attributes.DbCollectionProperty]
        public virtual ISet<MealTemplate> MealTemplates
        {
            get
            {
                return this._MealTemplates;
            }
            set
            {
                this._MealTemplates = value;
            }
        }

        [Anete.Data.Attributes.EntitySRDisplayName("MenuRowEans", typeof(WorkplaceSR))]
        [Anete.Data.Attributes.DbCollectionProperty]
        public virtual ISet<MenuRowEan> MenuRowEans
        {
            get
            {
                return this._MenuRowEans;
            }
            set
            {
                this._MenuRowEans = value;
            }
        }

        [Anete.Data.Attributes.EntitySRDisplayName("Prices", typeof(WorkplaceSR))]
        [Anete.Data.Attributes.DbCollectionProperty]
        public virtual ISet<Price> Prices
        {
            get
            {
                return this._Prices;
            }
            set
            {
                this._Prices = value;
            }
        }

        [Anete.Data.Attributes.EntitySRDisplayName("MenuTemplates", typeof(WorkplaceSR))]
        [Anete.Data.Attributes.DbCollectionProperty]
        public virtual ISet<MenuTemplate> MenuTemplates
        {
            get
            {
                return this._MenuTemplates;
            }
            set
            {
                this._MenuTemplates = value;
            }
        }

        [Anete.Data.Attributes.EntitySRDisplayName("MealKinds", typeof(WorkplaceSR))]
        [Anete.Data.Attributes.DbCollectionProperty]
        public virtual ISet<MealKind> MealKinds
        {
            get
            {
                return this._MealKinds;
            }
            set
            {
                this._MealKinds = value;
            }
        }

        [Anete.Data.Attributes.EntitySRDisplayName("Orders", typeof(WorkplaceSR))]
        [Anete.Data.Attributes.DbCollectionProperty]
        public virtual ISet<Order> Orders
        {
            get
            {
                return this._Orders;
            }
            set
            {
                this._Orders = value;
            }
        }

        [Anete.Data.Attributes.EntitySRDisplayName("MenuGroups", typeof(WorkplaceSR))]
        [Anete.Data.Attributes.DbCollectionProperty]
        public virtual ISet<MenuGroup> MenuGroups
        {
            get
            {
                return this._MenuGroups;
            }
            set
            {
                this._MenuGroups = value;
            }
        }

        [Anete.Data.Attributes.EntitySRDisplayName("ServingPeriods", typeof(WorkplaceSR))]
        [Anete.Data.Attributes.DbCollectionProperty]
        public virtual ISet<ServingPeriod> ServingPeriods
        {
            get
            {
                return this._ServingPeriods;
            }
            set
            {
                this._ServingPeriods = value;
            }
        }

        [Anete.Data.Attributes.EntitySRDisplayName("KpsNotifyUsers", typeof(WorkplaceSR))]
        [Anete.Data.Attributes.DbCollectionProperty]
        public virtual ISet<KpsNotifyUser> KpsNotifyUsers
        {
            get
            {
                return this._KpsNotifyUsers;
            }
            set
            {
                this._KpsNotifyUsers = value;
            }
        }

        [Anete.Data.Attributes.EntitySRDisplayName("MealRestriction", typeof(WorkplaceSR))]
        [Anete.Data.Attributes.DbCollectionProperty]
        public virtual ISet<MealRestriction> MealRestriction
        {
            get
            {
                return this._MealRestriction;
            }
            set
            {
                this._MealRestriction = value;
            }
        }

        [Anete.Data.Attributes.EntitySRDisplayName("Cooks", typeof(WorkplaceSR))]
        [Anete.Data.Attributes.DbCollectionProperty]
        public virtual ISet<Cook> Cooks
        {
            get
            {
                return this._Cooks;
            }
            set
            {
                this._Cooks = value;
            }
        }

        [Anete.Data.Attributes.EntitySRDisplayName("DeliveryOrders", typeof(WorkplaceSR))]
        [Anete.Data.Attributes.DbCollectionProperty]
        public virtual ISet<DeliveryOrder> DeliveryOrders
        {
            get
            {
                return this._DeliveryOrders;
            }
            set
            {
                this._DeliveryOrders = value;
            }
        }

        [Anete.Data.Attributes.EntitySRDisplayName("VydAutomatyMapovaniPozice", typeof(WorkplaceSR))]
        [Anete.Data.Attributes.DbCollectionProperty]
        public virtual ISet<VydAutomatyMapovaniPozice> VydAutomatyMapovaniPozice
        {
            get
            {
                return this._VydAutomatyMapovaniPozice;
            }
            set
            {
                this._VydAutomatyMapovaniPozice = value;
            }
        }
   
        public virtual event PropertyChangingEventHandler PropertyChanging;

	// Mam v bazove tride, nema smysl generovat
	// public virtual event PropertyChangedEventHandler PropertyChanged;

        protected virtual void SendPropertyChanging()
        {
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, emptyChangingEventArgs);
        }

        protected virtual void SendPropertyChanging(System.String propertyName) 
        {   	
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, new PropertyChangingEventArgs(propertyName));
        }

        protected virtual void SendPropertyChanged(System.String propertyName)
        {    
		// mam v bazove tride, proto musim volat takto jednoduse
		OnPropertyChanged(propertyName);
/*		        var handler = this.PropertyChanged;
            if (handler != null)
                handler(this, new PropertyChangedEventArgs(propertyName));*/
        }
    }

}
