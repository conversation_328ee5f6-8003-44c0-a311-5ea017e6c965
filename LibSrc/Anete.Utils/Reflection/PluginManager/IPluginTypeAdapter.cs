using System;
using System.Collections.Generic;
using System.Text;

namespace Anete.Utils.Reflection.PluginManager
{
	/// <summary>
	/// Negenericky interface. Existuje tu proto, abych mohl ulozit seznam TypeAdapteru do listu nebo dictionary.
	/// Diky kovarianci generickych typu by IPluginTypeAdapter(T) nesel
	/// </summary>
	public interface IPluginTypeAdapter
	{

	}

	/// <summary>
	/// Genericke interface pro PluginTypeAdapter.
	/// </summary>
	/// <typeparam name="T">Typ rozhrani pluginu, se kterym se pracuje. Napr. IDocumentPlugin</typeparam>
	public interface IPluginTypeAdapter<T> : IPluginTypeAdapter
	{

		/// <summary>
		/// Seznam natazenych pluginu
		/// </summary>
		IEnumerable<T> Plugins { get; }

		/// <summary>
		/// Seznam natazenych pluginu
		/// </summary>
		IEnumerable<IPluginFactory<T>> PluginFactories { get; }

		/// <summary>
		/// Delegat pro vyDelegat pro vytvoreni instance, pokud je treba predat konstruktoru nejake parametry
		/// </summary>
		Func<Type, T> CreateInstanceDelegate { get; set; }

		/// <summary>
		/// Vraci plugin - predpoklada se, ze existuje prave jeden. Pokud ne, pak vyvola vyjimku
		/// </summary>
		/// <returns></returns>
		T GetSinglePlugin();

	}
}
