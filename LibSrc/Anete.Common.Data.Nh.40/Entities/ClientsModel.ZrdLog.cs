//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Anete.Common.Data.Nh.Entities
{

    /// <summary>
    /// Tabulka: dba.ZRD
    /// </summary>
    public partial class ZrdLog : Anete.Data.Nh.NhEntityBase, INotifyPropertyChanging, INotifyPropertyChanged {

        private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(System.String.Empty);

        private int _ZrdLogId;

        private string _Veta;

        private string _PersonalId;

        private string _BirthId;

        private string _LastName;

        private string _FirstName;

        private string _Title;

        private string _OrganizationCode;

        private string _ResortCode;

        private string _AccountingDepartmentCode;

        private string _CardCode;

        private string _CardNumber;

        private short? _State;

        private System.DateTime? _ValidFrom;

        private System.DateTime? _ValidTo;

        private int? _GroupId;

        private AccountType? _AccountType;

        private int? _MinBalance;

        private int? _MaxPaymentPerDay;

        private string _AccountNumber;

        private int? _EqualizeLevelMax;

        private int? _EqualizeLevelMin;

        private string _Canteen;

        private decimal? _SuCus;

        private decimal? _SuAd;

        private int? _ClientId;

        private System.DateTime? _TimeStamp;

        private System.DateTime? _TimeStampProcessed;

        private short? _RecordType;

        private int? _BatchId;

        private int? _Error;

        private string _ErrorText;

        private decimal? _EqualizeLevel;

        private string _LoginName;

        private System.DateTime? _ChangeDateResort;

        private System.DateTime? _ChangeDateGroup;

        private string _VarSymbol;

        private string _PhoneNumber;

        private string _Email;

        private string _TitleAfter;
    
        #region Extensibility Method Definitions
        
        partial void OnCreated();

        public override bool Equals(object obj)
        {
          ZrdLog toCompare = obj as ZrdLog;
          if (toCompare == null)
          {
            return false;
          }

          if (!Object.Equals(this.ZrdLogId, toCompare.ZrdLogId))
            return false;
         // pokud mam default primary key, znamena to, ze jedna o nove vytvorenou entitu, ktera jeste nema key prirazen. 
        // V takove pripade musim porovnat podle instance
        bool isDefault = true;
        if (this.ZrdLogId != default(int))
        {
          isDefault = false;
        }
     

          if (isDefault)
          {
            return object.ReferenceEquals(toCompare, this);
          }     
          return true;
        }

	private int? _hashCode = null;
        public override int GetHashCode()
        {
	if (_hashCode == null)
          {
          _hashCode = 13;
          _hashCode = (_hashCode * 7) + ZrdLogId.GetHashCode();
}
          return _hashCode.Value;
        }
        partial void OnZrdLogIdChanging(int value);
        
        partial void OnZrdLogIdChanged();
        partial void OnVetaChanging(string value);
        
        partial void OnVetaChanged();
        partial void OnPersonalIdChanging(string value);
        
        partial void OnPersonalIdChanged();
        partial void OnBirthIdChanging(string value);
        
        partial void OnBirthIdChanged();
        partial void OnLastNameChanging(string value);
        
        partial void OnLastNameChanged();
        partial void OnFirstNameChanging(string value);
        
        partial void OnFirstNameChanged();
        partial void OnTitleChanging(string value);
        
        partial void OnTitleChanged();
        partial void OnOrganizationCodeChanging(string value);
        
        partial void OnOrganizationCodeChanged();
        partial void OnResortCodeChanging(string value);
        
        partial void OnResortCodeChanged();
        partial void OnAccountingDepartmentCodeChanging(string value);
        
        partial void OnAccountingDepartmentCodeChanged();
        partial void OnCardCodeChanging(string value);
        
        partial void OnCardCodeChanged();
        partial void OnCardNumberChanging(string value);
        
        partial void OnCardNumberChanged();
        partial void OnStateChanging(short? value);
        
        partial void OnStateChanged();
        partial void OnValidFromChanging(System.DateTime? value);
        
        partial void OnValidFromChanged();
        partial void OnValidToChanging(System.DateTime? value);
        
        partial void OnValidToChanged();
        partial void OnGroupIdChanging(int? value);
        
        partial void OnGroupIdChanged();
        partial void OnAccountTypeChanging(AccountType? value);
        
        partial void OnAccountTypeChanged();
        partial void OnMinBalanceChanging(int? value);
        
        partial void OnMinBalanceChanged();
        partial void OnMaxPaymentPerDayChanging(int? value);
        
        partial void OnMaxPaymentPerDayChanged();
        partial void OnAccountNumberChanging(string value);
        
        partial void OnAccountNumberChanged();
        partial void OnEqualizeLevelMaxChanging(int? value);
        
        partial void OnEqualizeLevelMaxChanged();
        partial void OnEqualizeLevelMinChanging(int? value);
        
        partial void OnEqualizeLevelMinChanged();
        partial void OnCanteenChanging(string value);
        
        partial void OnCanteenChanged();
        partial void OnSuCusChanging(decimal? value);
        
        partial void OnSuCusChanged();
        partial void OnSuAdChanging(decimal? value);
        
        partial void OnSuAdChanged();
        partial void OnClientIdChanging(int? value);
        
        partial void OnClientIdChanged();
        partial void OnTimeStampChanging(System.DateTime? value);
        
        partial void OnTimeStampChanged();
        partial void OnTimeStampProcessedChanging(System.DateTime? value);
        
        partial void OnTimeStampProcessedChanged();
        partial void OnRecordTypeChanging(short? value);
        
        partial void OnRecordTypeChanged();
        partial void OnBatchIdChanging(int? value);
        
        partial void OnBatchIdChanged();
        partial void OnErrorChanging(int? value);
        
        partial void OnErrorChanged();
        partial void OnErrorTextChanging(string value);
        
        partial void OnErrorTextChanged();
        partial void OnEqualizeLevelChanging(decimal? value);
        
        partial void OnEqualizeLevelChanged();
        partial void OnLoginNameChanging(string value);
        
        partial void OnLoginNameChanged();
        partial void OnChangeDateResortChanging(System.DateTime? value);
        
        partial void OnChangeDateResortChanged();
        partial void OnChangeDateGroupChanging(System.DateTime? value);
        
        partial void OnChangeDateGroupChanged();
        partial void OnVarSymbolChanging(string value);
        
        partial void OnVarSymbolChanged();
        partial void OnPhoneNumberChanging(string value);
        
        partial void OnPhoneNumberChanged();
        partial void OnEmailChanging(string value);
        
        partial void OnEmailChanged();
        partial void OnTitleAfterChanging(string value);
        
        partial void OnTitleAfterChanged();
        
        #endregion
        public ZrdLog()
        {
            this._Veta = @"";
            this._TimeStamp = DateTime.Now;
            this._RecordType = 0;
            OnCreated();
        }

    
        /// <summary>
        /// Sloupec: pc
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("ZrdLogId", typeof(ZrdLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(ZrdLog), Tag="ZrdLogId")]
        public virtual int ZrdLogId
        {
            get
            {
                return this._ZrdLogId;
            }
            set
            {
                if (this._ZrdLogId != value)
                {
                    this.OnZrdLogIdChanging(value);
                    this.SendPropertyChanging();
                    this._ZrdLogId = value;
                    this.SendPropertyChanged("ZrdLogId");
                    this.OnZrdLogIdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: veta
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Veta", typeof(ZrdLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(ZrdLog), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 330,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="Veta")]
        public virtual string Veta
        {
            get
            {
                return this._Veta;
            }
            set
            {
                if (this._Veta != value)
                {
                    this.OnVetaChanging(value);
                    this.SendPropertyChanging();
                    this._Veta = value;
                    this.SendPropertyChanged("Veta");
                    this.OnVetaChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: ocs
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("PersonalId", typeof(ZrdLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(ZrdLog), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 256,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="PersonalId")]
        public virtual string PersonalId
        {
            get
            {
                return this._PersonalId;
            }
            set
            {
                if (this._PersonalId != value)
                {
                    this.OnPersonalIdChanging(value);
                    this.SendPropertyChanging();
                    this._PersonalId = value;
                    this.SendPropertyChanged("PersonalId");
                    this.OnPersonalIdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: rcs
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("BirthId", typeof(ZrdLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(ZrdLog), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 15,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="BirthId")]
        public virtual string BirthId
        {
            get
            {
                return this._BirthId;
            }
            set
            {
                if (this._BirthId != value)
                {
                    this.OnBirthIdChanging(value);
                    this.SendPropertyChanging();
                    this._BirthId = value;
                    this.SendPropertyChanged("BirthId");
                    this.OnBirthIdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: prijmeni
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("LastName", typeof(ZrdLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(ZrdLog), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 50,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="LastName")]
        public virtual string LastName
        {
            get
            {
                return this._LastName;
            }
            set
            {
                if (this._LastName != value)
                {
                    this.OnLastNameChanging(value);
                    this.SendPropertyChanging();
                    this._LastName = value;
                    this.SendPropertyChanged("LastName");
                    this.OnLastNameChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: jmeno
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("FirstName", typeof(ZrdLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(ZrdLog), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 50,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="FirstName")]
        public virtual string FirstName
        {
            get
            {
                return this._FirstName;
            }
            set
            {
                if (this._FirstName != value)
                {
                    this.OnFirstNameChanging(value);
                    this.SendPropertyChanging();
                    this._FirstName = value;
                    this.SendPropertyChanged("FirstName");
                    this.OnFirstNameChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: titul
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Title", typeof(ZrdLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(ZrdLog), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 25,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="Title")]
        public virtual string Title
        {
            get
            {
                return this._Title;
            }
            set
            {
                if (this._Title != value)
                {
                    this.OnTitleChanging(value);
                    this.SendPropertyChanging();
                    this._Title = value;
                    this.SendPropertyChanged("Title");
                    this.OnTitleChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: KodOrg
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("OrganizationCode", typeof(ZrdLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(ZrdLog), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 10,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="OrganizationCode")]
        public virtual string OrganizationCode
        {
            get
            {
                return this._OrganizationCode;
            }
            set
            {
                if (this._OrganizationCode != value)
                {
                    this.OnOrganizationCodeChanging(value);
                    this.SendPropertyChanging();
                    this._OrganizationCode = value;
                    this.SendPropertyChanged("OrganizationCode");
                    this.OnOrganizationCodeChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: KodStr
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("ResortCode", typeof(ZrdLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(ZrdLog), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 10,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="ResortCode")]
        public virtual string ResortCode
        {
            get
            {
                return this._ResortCode;
            }
            set
            {
                if (this._ResortCode != value)
                {
                    this.OnResortCodeChanging(value);
                    this.SendPropertyChanging();
                    this._ResortCode = value;
                    this.SendPropertyChanged("ResortCode");
                    this.OnResortCodeChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: KodUct
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("AccountingDepartmentCode", typeof(ZrdLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(ZrdLog), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 10,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="AccountingDepartmentCode")]
        public virtual string AccountingDepartmentCode
        {
            get
            {
                return this._AccountingDepartmentCode;
            }
            set
            {
                if (this._AccountingDepartmentCode != value)
                {
                    this.OnAccountingDepartmentCodeChanging(value);
                    this.SendPropertyChanging();
                    this._AccountingDepartmentCode = value;
                    this.SendPropertyChanged("AccountingDepartmentCode");
                    this.OnAccountingDepartmentCodeChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: KodKarty
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("CardCode", typeof(ZrdLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(ZrdLog), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 32,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="CardCode")]
        public virtual string CardCode
        {
            get
            {
                return this._CardCode;
            }
            set
            {
                if (this._CardCode != value)
                {
                    this.OnCardCodeChanging(value);
                    this.SendPropertyChanging();
                    this._CardCode = value;
                    this.SendPropertyChanged("CardCode");
                    this.OnCardCodeChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: CisloKarty
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("CardNumber", typeof(ZrdLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(ZrdLog), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 32,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="CardNumber")]
        public virtual string CardNumber
        {
            get
            {
                return this._CardNumber;
            }
            set
            {
                if (this._CardNumber != value)
                {
                    this.OnCardNumberChanging(value);
                    this.SendPropertyChanging();
                    this._CardNumber = value;
                    this.SendPropertyChanged("CardNumber");
                    this.OnCardNumberChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: stav
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("State", typeof(ZrdLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual short? State
        {
            get
            {
                return this._State;
            }
            set
            {
                if (this._State != value)
                {
                    this.OnStateChanging(value);
                    this.SendPropertyChanging();
                    this._State = value;
                    this.SendPropertyChanged("State");
                    this.OnStateChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: PlatiOd
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("ValidFrom", typeof(ZrdLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual System.DateTime? ValidFrom
        {
            get
            {
                return this._ValidFrom;
            }
            set
            {
                if (this._ValidFrom != value)
                {
                    this.OnValidFromChanging(value);
                    this.SendPropertyChanging();
                    this._ValidFrom = value;
                    this.SendPropertyChanged("ValidFrom");
                    this.OnValidFromChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: PlatiDo
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("ValidTo", typeof(ZrdLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual System.DateTime? ValidTo
        {
            get
            {
                return this._ValidTo;
            }
            set
            {
                if (this._ValidTo != value)
                {
                    this.OnValidToChanging(value);
                    this.SendPropertyChanging();
                    this._ValidTo = value;
                    this.SendPropertyChanged("ValidTo");
                    this.OnValidToChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: idSkupina
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("GroupId", typeof(ZrdLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual int? GroupId
        {
            get
            {
                return this._GroupId;
            }
            set
            {
                if (this._GroupId != value)
                {
                    this.OnGroupIdChanging(value);
                    this.SendPropertyChanging();
                    this._GroupId = value;
                    this.SendPropertyChanged("GroupId");
                    this.OnGroupIdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: srazet
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("AccountType", typeof(ZrdLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual AccountType? AccountType
        {
            get
            {
                return this._AccountType;
            }
            set
            {
                if (this._AccountType != value)
                {
                    this.OnAccountTypeChanging(value);
                    this.SendPropertyChanging();
                    this._AccountType = value;
                    this.SendPropertyChanged("AccountType");
                    this.OnAccountTypeChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: mzu
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("MinBalance", typeof(ZrdLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual int? MinBalance
        {
            get
            {
                return this._MinBalance;
            }
            set
            {
                if (this._MinBalance != value)
                {
                    this.OnMinBalanceChanging(value);
                    this.SendPropertyChanging();
                    this._MinBalance = value;
                    this.SendPropertyChanged("MinBalance");
                    this.OnMinBalanceChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: mdp
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("MaxPaymentPerDay", typeof(ZrdLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual int? MaxPaymentPerDay
        {
            get
            {
                return this._MaxPaymentPerDay;
            }
            set
            {
                if (this._MaxPaymentPerDay != value)
                {
                    this.OnMaxPaymentPerDayChanging(value);
                    this.SendPropertyChanging();
                    this._MaxPaymentPerDay = value;
                    this.SendPropertyChanged("MaxPaymentPerDay");
                    this.OnMaxPaymentPerDayChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: csu
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("AccountNumber", typeof(ZrdLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(ZrdLog), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 30,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="AccountNumber")]
        public virtual string AccountNumber
        {
            get
            {
                return this._AccountNumber;
            }
            set
            {
                if (this._AccountNumber != value)
                {
                    this.OnAccountNumberChanging(value);
                    this.SendPropertyChanging();
                    this._AccountNumber = value;
                    this.SendPropertyChanged("AccountNumber");
                    this.OnAccountNumberChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: hduMax
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("EqualizeLevelMax", typeof(ZrdLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual int? EqualizeLevelMax
        {
            get
            {
                return this._EqualizeLevelMax;
            }
            set
            {
                if (this._EqualizeLevelMax != value)
                {
                    this.OnEqualizeLevelMaxChanging(value);
                    this.SendPropertyChanging();
                    this._EqualizeLevelMax = value;
                    this.SendPropertyChanged("EqualizeLevelMax");
                    this.OnEqualizeLevelMaxChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: hduMin
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("EqualizeLevelMin", typeof(ZrdLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual int? EqualizeLevelMin
        {
            get
            {
                return this._EqualizeLevelMin;
            }
            set
            {
                if (this._EqualizeLevelMin != value)
                {
                    this.OnEqualizeLevelMinChanging(value);
                    this.SendPropertyChanging();
                    this._EqualizeLevelMin = value;
                    this.SendPropertyChanged("EqualizeLevelMin");
                    this.OnEqualizeLevelMinChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: Vydejna
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Canteen", typeof(ZrdLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(ZrdLog), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 10,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="Canteen")]
        public virtual string Canteen
        {
            get
            {
                return this._Canteen;
            }
            set
            {
                if (this._Canteen != value)
                {
                    this.OnCanteenChanging(value);
                    this.SendPropertyChanging();
                    this._Canteen = value;
                    this.SendPropertyChanged("Canteen");
                    this.OnCanteenChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: su_cus
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("SuCus", typeof(ZrdLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
       [Anete.Common.Core.Interface.Validators.DecimalValidator(typeof(ZrdLog), 8, 2, Tag = "SuCus")]
        public virtual decimal? SuCus
        {
            get
            {
                return this._SuCus;
            }
            set
            {
                if (this._SuCus != value)
                {
                    this.OnSuCusChanging(value);
                    this.SendPropertyChanging();
                    this._SuCus = value;
                    this.SendPropertyChanged("SuCus");
                    this.OnSuCusChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: su_ad
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("SuAd", typeof(ZrdLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
       [Anete.Common.Core.Interface.Validators.DecimalValidator(typeof(ZrdLog), 8, 2, Tag = "SuAd")]
        public virtual decimal? SuAd
        {
            get
            {
                return this._SuAd;
            }
            set
            {
                if (this._SuAd != value)
                {
                    this.OnSuAdChanging(value);
                    this.SendPropertyChanging();
                    this._SuAd = value;
                    this.SendPropertyChanged("SuAd");
                    this.OnSuAdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: id_lk
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("ClientId", typeof(ZrdLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual int? ClientId
        {
            get
            {
                return this._ClientId;
            }
            set
            {
                if (this._ClientId != value)
                {
                    this.OnClientIdChanging(value);
                    this.SendPropertyChanging();
                    this._ClientId = value;
                    this.SendPropertyChanged("ClientId");
                    this.OnClientIdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: TStamp
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("TimeStamp", typeof(ZrdLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual System.DateTime? TimeStamp
        {
            get
            {
                return this._TimeStamp;
            }
            set
            {
                if (this._TimeStamp != value)
                {
                    this.OnTimeStampChanging(value);
                    this.SendPropertyChanging();
                    this._TimeStamp = value;
                    this.SendPropertyChanged("TimeStamp");
                    this.OnTimeStampChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: TStampZprac
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("TimeStampProcessed", typeof(ZrdLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual System.DateTime? TimeStampProcessed
        {
            get
            {
                return this._TimeStampProcessed;
            }
            set
            {
                if (this._TimeStampProcessed != value)
                {
                    this.OnTimeStampProcessedChanging(value);
                    this.SendPropertyChanging();
                    this._TimeStampProcessed = value;
                    this.SendPropertyChanged("TimeStampProcessed");
                    this.OnTimeStampProcessedChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: Druh
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("RecordType", typeof(ZrdLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual short? RecordType
        {
            get
            {
                return this._RecordType;
            }
            set
            {
                if (this._RecordType != value)
                {
                    this.OnRecordTypeChanging(value);
                    this.SendPropertyChanging();
                    this._RecordType = value;
                    this.SendPropertyChanged("RecordType");
                    this.OnRecordTypeChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: id_davka
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("BatchId", typeof(ZrdLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual int? BatchId
        {
            get
            {
                return this._BatchId;
            }
            set
            {
                if (this._BatchId != value)
                {
                    this.OnBatchIdChanging(value);
                    this.SendPropertyChanging();
                    this._BatchId = value;
                    this.SendPropertyChanged("BatchId");
                    this.OnBatchIdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: Err
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Error", typeof(ZrdLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual int? Error
        {
            get
            {
                return this._Error;
            }
            set
            {
                if (this._Error != value)
                {
                    this.OnErrorChanging(value);
                    this.SendPropertyChanging();
                    this._Error = value;
                    this.SendPropertyChanged("Error");
                    this.OnErrorChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: Err_text
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("ErrorText", typeof(ZrdLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(ZrdLog), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 500,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="ErrorText")]
        public virtual string ErrorText
        {
            get
            {
                return this._ErrorText;
            }
            set
            {
                if (this._ErrorText != value)
                {
                    this.OnErrorTextChanging(value);
                    this.SendPropertyChanging();
                    this._ErrorText = value;
                    this.SendPropertyChanged("ErrorText");
                    this.OnErrorTextChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: hdu
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("EqualizeLevel", typeof(ZrdLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
       [Anete.Common.Core.Interface.Validators.DecimalValidator(typeof(ZrdLog), 8, 2, Tag = "EqualizeLevel")]
        public virtual decimal? EqualizeLevel
        {
            get
            {
                return this._EqualizeLevel;
            }
            set
            {
                if (this._EqualizeLevel != value)
                {
                    this.OnEqualizeLevelChanging(value);
                    this.SendPropertyChanging();
                    this._EqualizeLevel = value;
                    this.SendPropertyChanged("EqualizeLevel");
                    this.OnEqualizeLevelChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: SAN
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("LoginName", typeof(ZrdLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(ZrdLog), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 40,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="LoginName")]
        public virtual string LoginName
        {
            get
            {
                return this._LoginName;
            }
            set
            {
                if (this._LoginName != value)
                {
                    this.OnLoginNameChanging(value);
                    this.SendPropertyChanging();
                    this._LoginName = value;
                    this.SendPropertyChanged("LoginName");
                    this.OnLoginNameChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: DatZmenyStr
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("ChangeDateResort", typeof(ZrdLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual System.DateTime? ChangeDateResort
        {
            get
            {
                return this._ChangeDateResort;
            }
            set
            {
                if (this._ChangeDateResort != value)
                {
                    this.OnChangeDateResortChanging(value);
                    this.SendPropertyChanging();
                    this._ChangeDateResort = value;
                    this.SendPropertyChanged("ChangeDateResort");
                    this.OnChangeDateResortChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: DatZmenySk
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("ChangeDateGroup", typeof(ZrdLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual System.DateTime? ChangeDateGroup
        {
            get
            {
                return this._ChangeDateGroup;
            }
            set
            {
                if (this._ChangeDateGroup != value)
                {
                    this.OnChangeDateGroupChanging(value);
                    this.SendPropertyChanging();
                    this._ChangeDateGroup = value;
                    this.SendPropertyChanged("ChangeDateGroup");
                    this.OnChangeDateGroupChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: VarSymbol
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("VarSymbol", typeof(ZrdLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(ZrdLog), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 10,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="VarSymbol")]
        public virtual string VarSymbol
        {
            get
            {
                return this._VarSymbol;
            }
            set
            {
                if (this._VarSymbol != value)
                {
                    this.OnVarSymbolChanging(value);
                    this.SendPropertyChanging();
                    this._VarSymbol = value;
                    this.SendPropertyChanged("VarSymbol");
                    this.OnVarSymbolChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: tel
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("PhoneNumber", typeof(ZrdLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(ZrdLog), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 50,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="PhoneNumber")]
        public virtual string PhoneNumber
        {
            get
            {
                return this._PhoneNumber;
            }
            set
            {
                if (this._PhoneNumber != value)
                {
                    this.OnPhoneNumberChanging(value);
                    this.SendPropertyChanging();
                    this._PhoneNumber = value;
                    this.SendPropertyChanged("PhoneNumber");
                    this.OnPhoneNumberChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: email
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Email", typeof(ZrdLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(ZrdLog), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 50,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="Email")]
        public virtual string Email
        {
            get
            {
                return this._Email;
            }
            set
            {
                if (this._Email != value)
                {
                    this.OnEmailChanging(value);
                    this.SendPropertyChanging();
                    this._Email = value;
                    this.SendPropertyChanged("Email");
                    this.OnEmailChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: titul_za
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("TitleAfter", typeof(ZrdLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(ZrdLog), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 20,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="TitleAfter")]
        public virtual string TitleAfter
        {
            get
            {
                return this._TitleAfter;
            }
            set
            {
                if (this._TitleAfter != value)
                {
                    this.OnTitleAfterChanging(value);
                    this.SendPropertyChanging();
                    this._TitleAfter = value;
                    this.SendPropertyChanged("TitleAfter");
                    this.OnTitleAfterChanged();
                }
            }
        }
   
        public virtual event PropertyChangingEventHandler PropertyChanging;

	// Mam v bazove tride, nema smysl generovat
	// public virtual event PropertyChangedEventHandler PropertyChanged;

        protected virtual void SendPropertyChanging()
        {
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, emptyChangingEventArgs);
        }

        protected virtual void SendPropertyChanging(System.String propertyName) 
        {   	
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, new PropertyChangingEventArgs(propertyName));
        }

        protected virtual void SendPropertyChanged(System.String propertyName)
        {    
		// mam v bazove tride, proto musim volat takto jednoduse
		OnPropertyChanged(propertyName);
/*		        var handler = this.PropertyChanged;
            if (handler != null)
                handler(this, new PropertyChangedEventArgs(propertyName));*/
        }
    }

}
