using Anete.Log.Core;
using log4net.Core;
using log4net.Filter;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Anete.Log4Net.Core.Filters
{
    public class DisableSqlLogFilter : FilterSkeleton
    {
        private const string _key = "DisableSqlLog";
        private const string _activateValue = "true";

        public DisableSqlLogFilter()
        {
        }

        public override FilterDecision Decide(LoggingEvent loggingEvent)
        {            
            object msgObj = loggingEvent.LookupProperty(_key);            
            string msg = loggingEvent.Repository.RendererMap.FindAndRender(msgObj);

            // Pro Trace a nizsi uroven tento filtr jednoduse ignoruju, chci mit vzdy veskere dotazy zalogovane
            if(loggingEvent.Level < Level.Debug)
            {
                return FilterDecision.Neutral;
            }

            // Zaroven chci, aby se toto nastaveni tykalo pouze SQL loggeru, zadneho jineho
            if(loggingEvent.LoggerName != NhLoggerNames.Sql)
            {
                return FilterDecision.Neutral;
            }

            // pokud ma logger prirazen priznak DisableSqlLog, nebudu logovat
            FilterDecision result = msg == _activateValue ? FilterDecision.Deny : FilterDecision.Neutral;
            return result;
        }

        public static IDisposable StartNoSqlTransaction()
        {
            return log4net.ThreadContext.Stacks[_key].Push(_activateValue);
        }
    }
}
