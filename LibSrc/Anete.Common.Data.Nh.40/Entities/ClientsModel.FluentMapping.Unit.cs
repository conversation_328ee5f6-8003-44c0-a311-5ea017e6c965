//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate Fluent Mapping template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;
using FluentNHibernate.Mapping;
using FluentNHibernate.MappingModel.Collections;

namespace Anete.Common.Data.Nh.Entities
{
    /// <summary>
    /// There are no comments for UnitMap in the schema.
    /// </summary>
    public partial class UnitMap : ClassMap<Unit>
    {
        /// <summary>
        /// There are no comments for UnitMap constructor in the schema.
        /// </summary>
        public UnitMap()
        {
              Schema(@"dba");
              Table(@"UbytovaniL");
              DynamicInsert();
              DynamicUpdate();
              LazyLoad();
              Id(x => x.UnitId)
                .Column("id_ubytovaniL")
                .CustomType("Int16")
                .Access.Property().CustomSqlType("smallint")
                .Not.Nullable()
                .Precision(5)                
                .GeneratedBy.Identity();
              Map(x => x.Code)    
                .Column("kod")
                .CustomType("String")
                .Access.Property()
                .Generated.Never().CustomSqlType("char(5)")
                .Not.Nullable()
                .Length(5)
                .Unique();
              Map(x => x.Name)    
                .Column("popis")
                .CustomType("String")
                .Access.Property()
                .Generated.Never().CustomSqlType("varchar(50)")
                .Not.Nullable()
                .Length(50);
              HasMany<Floor>(x => x.Floors)
                .Access.Property()
                .AsSet()
                .Cascade.None()
                .LazyLoad()
                // .OptimisticLock.Version() /*bug (or missing feature) in Fluent NHibernate*/
                .Inverse()
                .Generic()
                .KeyColumns.Add("id_ubytovaniL", mapping => mapping.Name("id_ubytovaniL")
                                                                     .SqlType("smallint")
                                                                     .Not.Nullable());
              ExtendMapping();
        }

        #region Partial Methods

        partial void ExtendMapping();

        #endregion
    }

}
