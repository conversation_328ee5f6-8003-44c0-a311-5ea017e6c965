using System;
using System.Reflection;
using System.Collections.Generic;
using Anete.Utils.Exceptions;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.ComponentModel;

namespace Anete.Utils.Extensions
{
    /// <summary>
    /// Rozsireni pro ICustomAttributeProvider
    /// </summary>
    /// <remarks>Pozor, toto je treba vzit do uvahy:
    /// Pozor: U PropertyInfo a EventInfo je nutno volat Attribute.GetCustomAttribute, protoze propertyInfo.GetAttribute 
    /// nevraci atributy definovane v predkovi, i kdyz dokumentace tvrdi neco jineho.
    /// Viz http://hyperthink.net/blog/getcustomattributes-gotcha/
    /// </remarks>
    public static class ICustomAttributeProviderExt
    {
        /// <summary>
        /// Pokud tento type definuje atribut attribute, vraci jeho instanci. Neprohledava hierarchii k predkum.
        /// Jinak null.
        /// </summary>
        /// <param name="provider">The type.</param>
        /// <param name="attribute">Type hledaneho attributu</param>
        /// <param name="options">The options.</param>
        /// <returns></returns>
        public static Attribute GetAttribute(this ICustomAttributeProvider provider, Type attribute, GetAttributeOptions options = GetAttributeOptions.None)
        {
            return GetAttribute(provider, attribute, false, options);
        }

        /// <summary>
        /// Pokud tento type definuje atribut T, vraci jeho instanci. Neprohledava hierarchii k predkum.
        /// Jinak null.
        /// </summary>
        /// <typeparam name="T">Typ hledaneho atributu</typeparam>
        /// <param name="provider">The type.</param>
        /// <param name="options">The options.</param>
        /// <returns></returns>
        public static T GetAttribute<T>(this ICustomAttributeProvider provider, GetAttributeOptions options = GetAttributeOptions.None) where T : Attribute
        {
            return (T)GetAttribute(provider, typeof(T), false, options);
        }

        /// <summary>
        /// Pokud tento type definuje atribut T, vraci jeho instanci.
        /// Jinak null.
        /// </summary>
        /// <typeparam name="T">Typ hledaneho atributu</typeparam>
        /// <param name="provider">The type.</param>
        /// <param name="inherit">Pokud je true, prohledava se hierarchie dedicnosti smerem k predkum</param>
        /// <param name="options">The options.</param>
        /// <returns></returns>
        public static T GetAttribute<T>(this ICustomAttributeProvider provider, bool inherit, GetAttributeOptions options = GetAttributeOptions.None) where T : Attribute
        {
            return (T)GetAttribute(provider, typeof(T), inherit, options);
        }

        /// <summary>
        /// Pokud tento type definuje atribut attribute, vraci jeho instanci.
        /// Jinak null.
        /// Pokud je atribut pro dany typ definovan vicekrat, vyvola vyjimku.
        /// </summary>
        /// <param name="provider">Typ objektu, na kterem ma byt atribut definovan</param>
        /// <param name="attribute">Type hledaneho attributu</param>
        /// <param name="inherit">Pokud je true, prohledava se hierarchie dedicnosti smerem k predkum</param>
        /// <param name="options">The options.</param>
        /// <returns>
        /// Nalezeny atribut nebo null v pripade, ze neexistuje
        /// </returns>
        /// <exception cref="ArgumentException">Pokud je attribut pro dany typ definovan vicekrat.</exception>
        public static Attribute GetAttribute(this ICustomAttributeProvider provider, Type attribute, bool inherit, GetAttributeOptions options = GetAttributeOptions.None)
        {
            return GetAttribute(provider, attribute, inherit, false, options);
        }

        /// <summary>
        /// Pokud tento type definuje atribut attribute, vraci jeho instanci.
        /// Jinak null.
        /// Pokud je atribut pro dany typ definovan vicekrat, vyvola vyjimku.
        /// </summary>
        /// <param name="provider">Typ objektu, na kterem ma byt atribut definovan</param>
        /// <param name="attribute">Type hledaneho attributu</param>
        /// <param name="inherit">Pokud je true, prohledava se hierarchie dedicnosti smerem k predkum</param>
        /// <param name="isRequired">Pokud je nastaveno na true, bude vyvolana vyjimka kdyz atribut neexistuje</param>
        /// <param name="options">Volby pro ziskani parametru. Popis je soucasti enumu.</param>
        /// <returns>Nalezeny atribut</returns>
        /// <exception cref="AttributeException">Pokud je attribut pro dany typ definovan vicekrat nebo
        /// pokud neni dany atribut nalezen a zaroven je isRequired true</exception>
        public static Attribute GetAttribute(this ICustomAttributeProvider provider, Type attribute, bool inherit, bool isRequired, GetAttributeOptions options = GetAttributeOptions.None)
        {
            if ((options & GetAttributeOptions.LoadMetadaFromPropertyInfoProvider) == GetAttributeOptions.LoadMetadaFromPropertyInfoProvider)
            {
                Guard.ArgumentIsOfType(provider, typeof(PropertyInfo), "provider");
            }

            List<object> attr = provider.GetCustomAttributes(attribute, inherit).ToList();

            if ((options & GetAttributeOptions.LoadMetadaFromPropertyInfoProvider) == GetAttributeOptions.LoadMetadaFromPropertyInfoProvider)
            {
                PropertyInfo pi = (PropertyInfo)provider;
#if !NETSTANDARD2_0
                MetadataTypeAttribute metadataTypeAttribute = (MetadataTypeAttribute)pi.DeclaringType.GetAttribute(typeof(MetadataTypeAttribute), true, false);
                if (metadataTypeAttribute != null)
                {
                    // mam definovany metadata. musim v nich najit odpovidaji property	
                    PropertyInfo metadataPi = metadataTypeAttribute.MetadataClassType.GetProperty(pi.Name);
                    if (metadataPi != null)
                    {                     
                        attr.AddRange(Attribute.GetCustomAttributes(metadataPi, attribute, inherit));
                    }
                }
#endif
            }

            if (attr.Count() > 1 && !options.Contains(GetAttributeOptions.IgnoreDuplicates))
            {
                throw new AttributeException(ICustomAttributeProviderExtSR.AmbiguousAttributeFormat(provider.GetType(), attribute.FullName),
                    attribute, provider.GetType());
            }

            Attribute result = attr.Count() == 0 ? null : (Attribute)attr[0];

            if (result == null && isRequired)
            {
                throw ExcUtils.AttributeNotFound(attribute, provider);
            }

            return result;
        }

        /// <summary>
        /// Jako Type.GetCustomAttributes ale genericke
        /// </summary>
        /// <typeparam name="T">Typ atributu, ktery potrebuji</typeparam>
        /// <param name="provider">The provider.</param>
        /// <param name="inherit">Pokud je true, prohledava se dedicnost</param>
        /// <returns>Seznam atributu definovanych na danemprvku</returns>
        public static T[] GetAttributes<T>(this ICustomAttributeProvider provider, bool inherit)
            where T : Attribute
        {
            return (T[])provider.GetCustomAttributes(typeof(T), inherit);
        }

        /// <summary>
        /// Vraci instanci vyzadovaneho atributu. Neprohledava hierarchii k predkum.
        /// </summary>
        /// <param name="provider">Typ objektu, na kterem ma byt atribut definovan</param>
        /// <param name="attribute">Type hledaneho attributu</param>
        /// <returns>Nalezeny atribut</returns>
        /// <exception cref="ArgumentException">Pokud je attribut pro dany typ definovan vicekrat.</exception>
        /// <exception cref="AttributeException">Pokud neni dany atribut nalezen</exception>
        public static Attribute GetRequiredAttribute(this ICustomAttributeProvider provider, Type attribute)
        {
            return GetRequiredAttribute(provider, attribute, false);
        }

		/// <summary>
		/// Vraci instanci vyzadovaneho atributu.
		/// </summary>
		/// <param name="provider">Typ objektu, na kterem ma byt atribut definovan</param>
		/// <param name="attribute">Type hledaneho attributu</param>
		/// <param name="inherit">Pokud je true, prohledava se hierarchie dedicnosti smerem k predkum</param>
		/// <param name="options">The options.</param>
		/// <returns>
		/// Nalezeny atribut
		/// </returns>
		/// <exception cref="ArgumentException">Pokud je attribut pro dany typ definovan vicekrat.</exception>
		/// <exception cref="AttributeException">Pokud neni dany atribut nalezen</exception>
		public static Attribute GetRequiredAttribute(this ICustomAttributeProvider provider, Type attribute, bool inherit, 
			GetAttributeOptions options = GetAttributeOptions.None)
        {
            return GetAttribute(provider, attribute, inherit, true, options);
        }

		/// <summary>
		/// Vraci instanci vyzadovaneho atributu.
		/// </summary>
		/// <typeparam name="T">Type hledaneho attributu</typeparam>
		/// <param name="provider">Typ objektu, na kterem ma byt atribut definovan</param>
		/// <param name="inherit">Pokud je true, prohledava se hierarchie dedicnosti smerem k predkum</param>
		/// <param name="options">The options.</param>
		/// <returns>
		/// Nalezeny atribut
		/// </returns>
		/// <exception cref="ArgumentException">Pokud je attribut pro dany typ definovan vicekrat.</exception>
		/// <exception cref="AttributeException">Pokud neni dany atribut nalezen</exception>
        public static T GetRequiredAttribute<T>(this ICustomAttributeProvider provider, bool inherit, 
			GetAttributeOptions options = GetAttributeOptions.None)
            where T : Attribute
        {
            return (T)GetRequiredAttribute(provider, typeof(T), inherit, options);
        }

        /// <summary>
        /// Vraci instanci vyzadovaneho atributu. Neprohledava hierarchii k predkum.
        /// </summary>
        /// <typeparam name="T">Type hledaneho attributu</typeparam>
        /// <param name="provider">Typ objektu, na kterem ma byt atribut definovan</param>
        /// <returns>Nalezeny atribut</returns>
        /// <exception cref="ArgumentException">Pokud je attribut pro dany typ definovan vicekrat.</exception>
        /// <exception cref="AttributeException">Pokud neni dany atribut nalezen</exception>
        public static T GetRequiredAttribute<T>(this ICustomAttributeProvider provider)
            where T : Attribute
        {
            return GetRequiredAttribute<T>(provider, false);
        }
    }
}
