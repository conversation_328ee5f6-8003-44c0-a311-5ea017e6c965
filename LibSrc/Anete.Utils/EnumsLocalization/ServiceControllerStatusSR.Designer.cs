//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.1
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Anete.Utils.EnumsLocalization {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class ServiceControllerStatusSR {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ServiceControllerStatusSR() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Anete.Utils.EnumsLocalization.ServiceControllerStatusSR", typeof(ServiceControllerStatusSR).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pokračuji.
        /// </summary>
        internal static string ServiceControllerStatus_ContinuePending {
            get {
                return ResourceManager.GetString("ServiceControllerStatus_ContinuePending", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pozastaveno.
        /// </summary>
        internal static string ServiceControllerStatus_Paused {
            get {
                return ResourceManager.GetString("ServiceControllerStatus_Paused", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pozastavuji.
        /// </summary>
        internal static string ServiceControllerStatus_PausePending {
            get {
                return ResourceManager.GetString("ServiceControllerStatus_PausePending", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Spuštěno.
        /// </summary>
        internal static string ServiceControllerStatus_Running {
            get {
                return ResourceManager.GetString("ServiceControllerStatus_Running", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Spouštím.
        /// </summary>
        internal static string ServiceControllerStatus_StartPending {
            get {
                return ResourceManager.GetString("ServiceControllerStatus_StartPending", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Zastaveno.
        /// </summary>
        internal static string ServiceControllerStatus_Stopped {
            get {
                return ResourceManager.GetString("ServiceControllerStatus_Stopped", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Zastavuju.
        /// </summary>
        internal static string ServiceControllerStatus_StopPending {
            get {
                return ResourceManager.GetString("ServiceControllerStatus_StopPending", resourceCulture);
            }
        }
    }
}
