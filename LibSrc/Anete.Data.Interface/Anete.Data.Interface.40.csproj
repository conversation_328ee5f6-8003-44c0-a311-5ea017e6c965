<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">
  <Import Project="..\Shared\Common.targets" />

  <PropertyGroup>
    <ProjectGuid>{9B4499D6-EB21-413A-95F4-4D7D382C3543}</ProjectGuid>
    <RootNamespace>Anete.Data.Interface</RootNamespace>
    <AssemblyName>Anete.Data.Interface.40</AssemblyName>
    <Configurations>Debug;Release;Internal</Configurations>
  </PropertyGroup>

	<PropertyGroup Condition="'$(TargetFramework)' != 'net472'">
		<DebugType>portable</DebugType>
		<DebugSymbols>true</DebugSymbols>
	</PropertyGroup>

  <ItemGroup Condition="'$(TargetFramework)' == 'net472'">
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>

  <ItemGroup Condition="'$(TargetFramework)' != 'net472'">
    <PackageReference Include="System.Data.SqlClient" Version="4.8.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Anete.Resources\Anete.Resources.40.csproj">
      <Project>{e26d589a-d293-4030-bebe-0b133ef3d255}</Project>
      <Name>Anete.Resources.40</Name>
    </ProjectReference>
    <ProjectReference Include="..\Anete.Utils\Anete.Utils.40.csproj">
      <Project>{552841C5-6034-4D41-9BC5-2DB29A8952D5}</Project>
      <Name>Anete.Utils.40</Name>
    </ProjectReference>
  </ItemGroup>
</Project>