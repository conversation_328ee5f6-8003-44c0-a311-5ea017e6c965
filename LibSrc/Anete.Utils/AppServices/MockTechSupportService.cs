using System;
using System.Collections.Generic;
using System.Web;
using Anete.Utils.AppServices.SupportMessage;

namespace Anete.Utils.AppServices
{
    /// <summary>
    /// Mockovací implementace <see cref="ITechSupportService"/> kter<PERSON> nic nedě<PERSON>.
    /// Např. pro WebKredit 
    /// </summary>
    public class MockTechSupportService : ITechSupportService
    {
        #region ITechSupportService Members
        /// <summary>
        /// Odeslani zpravy od uzivatele
        /// </summary>
        public void ComposeAndSendUserMessage(Exception ex = null)
        {
            
        }

        /// <summary>
        /// Odeslani zpravy od uzivatele
        /// </summary>
        /// <param name="userData">Data zadana uzivatelem ve formulari</param>
        /// <param name="ex">Vyjimka popisujici problem</param>
        public void SendUserMessage(ComposeUserTechSupportMessageData userData, Exception ex = null)
        {
        }

        /// <summary>
        /// Zobrazeni formulare s informacemi o technicke podpore
        /// </summary>
        public void ShowSupportForm()
        {
            
        }
        #endregion

    }
}