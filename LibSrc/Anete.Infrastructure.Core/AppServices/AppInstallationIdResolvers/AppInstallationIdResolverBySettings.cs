using System;
using System.Configuration;
using System.Linq;
using Anete.Utils.Configuration;
using Anete.Common.Core.Interface.Enums;
using Anete.Utils.AppServices;

namespace Anete.Infrastructure.Core.AppServices.AppInstallationIdResolvers
{
    /// <summary>
    /// Vraci id zarizeni na zaklade nastaveni v Settings
    /// </summary>
    public class AppInstallationIdResolverBySettings : AppInstallationIdResolverBase
    {
        public AppInstallationIdResolverBySettings(Lazy<IAppDirsService> appDirsService) : base(appDirsService)
        {
        }

        protected override AppInstallationIdResolverResult ResolveInt(ApplicationSettingsBase settings, string[] args, UpdateTypeId updateTypeId, Guid computerId, Guid? computerPassword)
        {         
            return new AppInstallationIdResolverResult(settings.GetShort("IdZarizeni"), AppInstallationIdSource.ConfigFile);
        }
    }
}
