using System;

namespace Anete.Infrastructure.Core.AppServices
{
	public enum UserInterfaceType
	{
		/// <summary>
		/// <PERSON><PERSON><PERSON> aplikace - ma pri chybe restartovat, uzivatel ma mit co nejmensi moznost zasahu
		/// </summary>
		Kiosk,

		/// <summary>
		/// Aplikace spoustena uzivatelem - neni treba automaticky restart, funkcnost ridi uzivatel
		/// </summary>
		LoginDialog,

		/// <summary>
		/// Tray aplikace, nema prihlasovaci dialog, spousti se automaticky po startu
		/// </summary>
		Tray
	}
}
