using Anete.Log.Core.Layout;
using System.Collections.Generic;
using System.Linq;
using System.Xml.Linq;

namespace Anete.Common.Data.Interface.Business.Paragon
{
	/// <summary>
	/// Mapa pro mapovani originalnich a stornovanych polozek na paragonu
	/// </summary>
	public class StornoRefMap : IToXElementSerializable
	{

		public StornoRefMap(XElement element)
		{
			// pokud se jedna o paragon, ktery nema vubec zapsany element StornoRefMap, chybi napr. po upgrade na vyssi verzi
			if (element != null)
			{
				IEnumerable<StornoRefMapItem> mapItems = element
				.Descendants("Item")
				.Select(e => new StornoRefMapItem(e));

				_items = new List<StornoRefMapItem>(mapItems);
			}
		}

		public StornoRefMap(IEnumerable<StornoRefMapItem> items)
		{
			_items = items.ToList();
		}

		private readonly List<StornoRefMapItem> _items = new List<StornoRefMapItem>();
		public IEnumerable<StornoRefMapItem> Items
		{
			get { return _items; }
		}

		public XElement Serialize()
		{
			return new XElement("StornoRefMap", _items
				.Select(item => item.Serialize()));
		}
	}
}
