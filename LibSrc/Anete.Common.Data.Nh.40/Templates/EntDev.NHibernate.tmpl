<#
// NHibernate template for Devart Entity Developer C# code generation.
// Copyright (c) 2008-2012 Devart. All rights reserved.
#>
<#@ template language="C#" #>
<#@ include file="Validation.tmpl" #>
<#@ assembly name="System.Core" #>
<#@ import namespace="System.Collections" #>
<#@ import namespace="System.Collections.Generic" #>
<#@ import namespace="System.Linq" #>
<#@ import namespace="EntityDeveloper.Mapping" #>
<#@ property name="FilePerClass" category="Output" default="True" type="System.Boolean" description="If it is set to True, each model class will be placed to the separate file when generating code, otherwise, all model classes will be placed into a single file." #>
<#@ property name="HeaderTimestampVersionControlTag" category="Generation" type="System.String" description="If this option is set, the standard date/time-stamp in the file header will be replaced with the specified tag (e.g. a version control tag for Subversion, Git, etc.)" #>
<#@ property name="EntitiesOutput" category="Output" type="OutputInfo" editor="OutputInfoEditor" description="Specifies output for the generated entity classes." #>
<#@ property name="PropertyChangeNotifiers" category="Generation" type="System.Boolean" description="If it is set to True, each model class will implement the INotifyPropertyChanging, INotifyPropertyChanged interfaces." #>
<#@ property name="PropertyChangePartialMethods" category="Generation" type="System.Boolean" description="If it is set to True, the extensibility partial void On<property name>Changing and partial void On<property name>Changed methods will be generated. These methods handle changes of each object property." #>
<#@ property name="ImplementValidatable" category="Generation" type="System.Boolean" description="If it is set to True, each entity class will implement the IValidatable interface." #>
<#@ property name="ImplementEquals" category="Generation" type="System.Boolean" description="If it is set to True, each entity class will implement their own Equals and GetHashCode methods." #>
<#@ property name="ImplementCloneable" category="Generation" type="System.Boolean" description="If it is set to True, each entity class will implement the ICloneable interface." #>
<#@ property name="GeneratePartialClass" category="Output" type="System.Boolean" default="False" description="If it is set to True, then, for each class in the model, a partial class will be generated, in which the user can add code that is not overwritten by the designer." #>
<#@ property name="GenerateDataContracts" category="Generation" type="System.Boolean" default="False" description="Determines whether the DataContract/DataMember attributes used for the serialization of entities should be generated." #>
<#@ extended name="GenerateDataContractAttribute" owner="Class" type="System.Boolean" default="True" description="Determines whether the DataContract attribute used for the serialization of entities should be generated." #>
<#@ extended name="GenerateDataContractAttribute" owner="ComplexType" type="System.Boolean" default="True" description="Determines whether the DataContract attribute used for the serialization of types should be generated." #>
<#@ extended name="GenerateDataContractAttribute" owner="EnumType" type="System.Boolean" default="True" description="Determines whether the DataContract attribute used for the serialization of types should be generated." #>
<#@ extended name="GenerateDataMemberAttribute" owner="Property" type="System.Boolean" default="True" description="Determines whether the DataMember attribute used for the serialization of entities should be generated." #>
<#@ extended name="GenerateDataMemberAttribute" owner="RelationProperty" type="System.Boolean" default="True" description="Determines whether the DataMember attribute used for the serialization of entities should be generated." #>
<#@ extended name="GenerateEnumMemberAttribute" owner="EnumTypeMember" type="System.Boolean" default="True" description="Determines whether the EnumMember attribute used for the serialization of enum should be generated." #>
<#@ property name="XmlMappingOutput" category="Xml Mapping" type="OutputInfo" editor="OutputInfoEditor" description="Specifies output for the generated mapping." #>
<#@ property name="XmlMappingAction" category="Xml Mapping" type="MetadataArtifactProcessing" default="CopyToOutputDirectory" description="Defines whether mapping will be copied to the folder specified in the Xml Mapping Output parameter, or to the folder storing the model file in case Xml Mapping Output parameter value is not specified, or it will be added to the project as an embedded resource." #>
<#@ property name="XmlMappingFilePerClass" category="Xml Mapping" default="True" type="System.Boolean" description="If it is set to True, separate XML mapping file will be created for each class in the model, otherwise, all mapping will be placed into a single file." #>
<#@ extended name="EntityBase" owner="ContextModel" type="System.String" description="Base class or interface for the entity classes." #>
<#@ extended name="EntityBase" owner="Class" type="System.String" description="Base class or interface for the entity class." #>
<#@ extended name="ConstructorModifier" owner="Class" type="MemberAccess" description="Determines access for the class constructor." #>
<#@ extended name="ConstructorModifier" owner="ComplexType" type="MemberAccess" description="Determines access for the class constructor." #>
<#@ extended name="Unimplemented" owner="Property" type="System.Boolean" Default="False" description="Determines whether to exclude the definition and initialization code of the corresponding property in the class." #>
<#@ extended name="Unimplemented" owner="RelationProperty" type="System.Boolean" Default="False" description="Determines whether to exclude the definition and initialization code of the corresponding property in the class." #>
<#
  

  // Settings
  baseFileName = model.FileName;
  output.Extension = ".cs";

  // Begin generation
  if (!FilePerClass) {
    output.PushOutputRedirection(EntitiesOutput, baseFileName + ".Designer");
    GenerateFileHeader(true);
  }

  string defaultNamespace = codeProvider.GetValidIdentifier(model.GetDefaultNamespace());

  //------------------------------------------------------------------------------
  // Class generation for entities
  //------------------------------------------------------------------------------
  var namespaces = from cls in model.Classes.Cast<HibernateClass>()
    let namespaceName = !String.IsNullOrEmpty(cls.Namespace) ? codeProvider.GetValidIdentifier(cls.Namespace) : defaultNamespace
    group cls by namespaceName;

  foreach (var _namespace in namespaces) {
    if (!FilePerClass) {
#>

namespace <#= _namespace.Key #>
{
<#
    }
    foreach (HibernateClass cls in _namespace) {
      if (FilePerClass) {
        string rootFileName = baseFileName + "." + cls.Name;
        if (GeneratePartialClass) {
          output.Extension = ".cs";
          output.PushOutputRedirection(EntitiesOutput, "", rootFileName, OverwriteMode.None);
          GenerateFileHeader(false);
#>

namespace <#= _namespace.Key #>
{

    <#= codeProvider.FormatClassAccess(cls.Access) #> partial class <#= codeProvider.GetValidIdentifier(cls.Name) #>
    {
    }
}
<#
          output.PopOutputRedirection();
        }

        output.Extension = ".cs";
        if (GeneratePartialClass)
          output.PushOutputRedirection(EntitiesOutput, rootFileName, rootFileName + ".Generated");
        else
          output.PushOutputRedirection(EntitiesOutput, rootFileName);
        GenerateFileHeader(true);
#>

namespace <#= _namespace.Key #>
{
<#
      }

      int propertyCounter = 0;
      string baseList = "";
      bool implementPropertyChanging = PropertyChangeNotifiers && cls.BaseInheritance == null;
      if (cls.BaseInheritance != null)
        baseList = cls.BaseInheritance.BaseClass.Name;
      else {
        if (implementPropertyChanging)
          baseList = "INotifyPropertyChanging, INotifyPropertyChanged";

        string entityBase = cls.GetProperty("EntityBase") as string;
        if (string.IsNullOrEmpty(entityBase))
           entityBase = model.GetProperty("EntityBase") as string;
        if (!string.IsNullOrEmpty(entityBase))
          baseList = string.IsNullOrEmpty(baseList) ? entityBase : entityBase + ", " + baseList;
      }

      // IValidatable
      if (ImplementValidatable)
        baseList = string.IsNullOrEmpty(baseList) ? "IValidatable" : baseList + ", IValidatable";
      // ICloneable
      if (ImplementCloneable)
        baseList = string.IsNullOrEmpty(baseList) ? "ICloneable" : baseList + ", ICloneable";

      GenerateDocumentation(cls.Documentation, cls.FullName); 

      string inheritanceModifier = cls.InheritanceModifier == ClassInheritanceModifier.None ? "" : " " + codeProvider.FormatClassInheritanceModifier(cls.InheritanceModifier);

      if (GenerateDataContracts && (bool)cls.GetProperty("GenerateDataContractAttribute")) {
#>
    [DataContract(IsReference = true)]
<#
      }

      foreach(AttributeValue attribute in cls.Attributes) {
        output.AddReference(attribute.Constructor.AttributeType.Assembly.Name);     
#>
    <#= codeProvider.FormatAttributeValue(attribute) #>
<#
      }
#>
    <#= codeProvider.FormatClassAccess(cls.Access) #><#= inheritanceModifier #> partial class <#= codeProvider.GetValidIdentifier(cls.Name) #><# if (!string.IsNullOrEmpty(baseList)) { #> : <#= baseList #><# } #>
 {
<#

      // PropertyChangingEventArgs emptyChangingEventArgs
      if (implementPropertyChanging) {
#>

        private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(System.String.Empty);
<#
     }

      
      // Class fields
      foreach (HibernateProperty property in cls.Properties) {
  	    if (!(bool)property.GetProperty("Unimplemented")) {
#>

        private <#= GetPropertyTypeName(property, _namespace.Key, defaultNamespace) #> _<#= property.Name #>;
<#
        }
      }
   
      // Class navigation fields
      foreach (HibernateRelationProperty relationProperty in cls.RelationProperties) {
        if (relationProperty.Generate && !(bool)relationProperty.GetProperty("Unimplemented")) {
#>

        private <#= GetRelationPropertyTypeName(relationProperty, _namespace.Key, defaultNamespace) #> _<#= relationProperty.Name #>;
<#
        }
      }

      // extensibility method definitions
      bool implementEquals = ImplementEquals || (cls.Properties.Count(p => p.PrimaryKey) > 1 || cls.Properties.Any(p => p.PrimaryKey && p.IsComplexType));
      GenerateExtensibilityMethodDefinitions(cls, _namespace.Key, implementEquals);

      // class constructor
#>

        <#= codeProvider.FormatMemberAccess((MemberAccess)cls.GetProperty("ConstructorModifier")) #> <#= codeProvider.GetValidIdentifier(cls.Name) #>()
        {
<#
      foreach (HibernateProperty property in cls.Properties) {
        if (property.IsComplexType && !(bool)property.GetProperty("Unimplemented")) {
          string nspace = !string.IsNullOrEmpty(((HibernateComplexType)property.Type).Namespace) ? codeProvider.GetValidIdentifier(((HibernateComplexType)property.Type).Namespace) : defaultNamespace;
          string propertyDataType = codeProvider.GetValidIdentifier(property.Type.ToString());
          if (nspace != _namespace.Key)
            propertyDataType = nspace + "." + propertyDataType;
#>
            this._<#= property.Name #> = new <#= propertyDataType #>();
<#
        }
      }

      foreach (HibernateRelationProperty relationProperty in cls.RelationProperties) {
        if (relationProperty.Generate && !(bool)relationProperty.GetProperty("Unimplemented")) {
          if (relationProperty.Multiplicity == Multiplicity.Many) {
#>
            this._<#= relationProperty.Name #> = new <#= GetRelationPropertyCollectionInitialization(relationProperty, _namespace.Key, defaultNamespace) #>;
<#
          }
        }
      }
#>
            OnCreated();
        }
<#
      // Class properties
      foreach (HibernateProperty property in cls.Properties) {
	    if (!(bool)property.GetProperty("Unimplemented")) {
          if ((bool)property.GetProperty("GenerateDataMemberAttribute"))
            propertyCounter++;
          GenerateProperty(property, propertyCounter, PropertyChangeNotifiers, _namespace.Key);
		}
      }

      // Class navigation properties  
      foreach (HibernateRelationProperty relationProperty in cls.RelationProperties) {
        if (relationProperty.Generate) {
          if ((bool)relationProperty.GetProperty("GenerateDataMemberAttribute") && (relationProperty.Multiplicity == Multiplicity.Many || !relationProperty.OppositeRelationProperty.Generate)) 
            propertyCounter ++;
          if(!(bool)relationProperty.GetProperty("Unimplemented"))
            GenerateRelationProperty(relationProperty, propertyCounter, PropertyChangeNotifiers, _namespace.Key);
        }
      }


      // IValidatable
      if (ImplementValidatable)
        GenerateIValidatable();
      // ICloneable
      if (ImplementCloneable)
        GenerateICloneable(cls, _namespace.Key, defaultNamespace);

      if (implementPropertyChanging) {
#>
   
        public virtual event PropertyChangingEventHandler PropertyChanging;

        public virtual event PropertyChangedEventHandler PropertyChanged;

        protected virtual void SendPropertyChanging()
        {
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, emptyChangingEventArgs);
        }

        protected virtual void SendPropertyChanging(System.String propertyName) 
        {    
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, new PropertyChangingEventArgs(propertyName));
        }

        protected virtual void SendPropertyChanged(System.String propertyName)
        {    
		        var handler = this.PropertyChanged;
            if (handler != null)
                handler(this, new PropertyChangedEventArgs(propertyName));
        }
<#
      }
#>
    }
<#
      if (FilePerClass) {
#>

}
<#
        output.PopOutputRedirection();
      }
    
    } // End of class generation

    if (!FilePerClass) {
#>

}
<#
    }
  } // End of namespace generation

  //------------------------------------------------------------------------------
  // Complex Type class generation
  //------------------------------------------------------------------------------
  var typeNamespaces = from type in model.ComplexTypes.Cast<HibernateComplexType>()
    let namespaceName = !String.IsNullOrEmpty(type.Namespace) ? codeProvider.GetValidIdentifier(type.Namespace) : defaultNamespace
    group type by namespaceName;

  foreach (var _namespace in typeNamespaces) {
    if (!FilePerClass) {
#>

namespace <#= _namespace.Key #>
{
<#
    }
    foreach (HibernateComplexType type in _namespace) {
      if (FilePerClass) {
        string rootFileName = baseFileName + "." + type.Name;
        if (GeneratePartialClass) {
          output.Extension = ".cs";
          output.PushOutputRedirection(EntitiesOutput, "", rootFileName, OverwriteMode.None);
          GenerateFileHeader(false);
#>

namespace <#= _namespace.Key #>
{

    <#= codeProvider.FormatClassAccess(type.Access) #> partial class <#= codeProvider.GetValidIdentifier(type.Name) #>
    {
    }
}
<#
          output.PopOutputRedirection();
        }

        output.Extension = ".cs";
        if (GeneratePartialClass)
          output.PushOutputRedirection(EntitiesOutput, rootFileName, rootFileName + ".Generated");
        else
          output.PushOutputRedirection(EntitiesOutput, rootFileName);
        GenerateFileHeader(true);
#>

namespace <#= _namespace.Key #>
{
<#
      }

      int propertyCounter = 0;
      GenerateDocumentation(type.Documentation, type.FullName);

      if (GenerateDataContracts && (bool)type.GetProperty("GenerateDataContractAttribute")) {
#>
    [DataContract(IsReference = true)]
<#
      }

      foreach(AttributeValue attribute in type.Attributes) {
        output.AddReference(attribute.Constructor.AttributeType.Assembly.Name);     
#>
    <#= codeProvider.FormatAttributeValue(attribute) #>
<#
      }

      string baseList = "";
      // (PropertyChangeNotifiers
      if (PropertyChangeNotifiers)
        baseList = "INotifyPropertyChanging, INotifyPropertyChanged";
      // ICloneable
      if (ImplementCloneable)
        baseList = string.IsNullOrEmpty(baseList) ? "ICloneable" : baseList + ", ICloneable";
#>
    <#= codeProvider.FormatClassAccess(type.Access) #> partial class <#= codeProvider.GetValidIdentifier(type.Name) #><# if (!string.IsNullOrEmpty(baseList)) { #> : <#= baseList #><# } #>
 {
<#
      if (PropertyChangeNotifiers) {
#>

        private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(System.String.Empty);
<#    
      }
#>
<# 
      // type fields
      foreach (HibernateProperty property in type.Properties) {
	      if (!(bool)property.GetProperty("Unimplemented")) {
#>

        private <#= GetPropertyTypeName(property, _namespace.Key, defaultNamespace) #> _<#= property.Name #>;
<#
        }
	    }

      // Type navigation fields
      foreach (HibernateRelationProperty relationProperty in type.RelationProperties) {
        if (relationProperty.Generate && !(bool)relationProperty.GetProperty("Unimplemented")) {
#>

        private <#= GetRelationPropertyTypeName(relationProperty, _namespace.Key, defaultNamespace) #> _<#= relationProperty.Name #>;
<#
        }
      }

      // extensibility method definitions
      bool implementEquals = ImplementEquals || model.Classes.Any(cls => cls.Properties.Any(p => p.PrimaryKey && p.Type == type));
      GenerateExtensibilityMethodDefinitions(type, _namespace.Key, implementEquals);

      // type constructor
#>

        <#= codeProvider.FormatMemberAccess((MemberAccess)type.GetProperty("ConstructorModifier")) #> <#= codeProvider.GetValidIdentifier(type.Name) #>()
        {
<#
      // Type component fields initialization
      foreach (HibernateProperty property in type.Properties) {
        if (property.IsComplexType && !(bool)property.GetProperty("Unimplemented")) {
          string nspace = !string.IsNullOrEmpty(((HibernateComplexType)property.Type).Namespace) ? codeProvider.GetValidIdentifier(((HibernateComplexType)property.Type).Namespace) : defaultNamespace;
          string propertyDataType = codeProvider.GetValidIdentifier(property.Type.ToString());
          if (nspace != _namespace.Key)
            propertyDataType = nspace + "." + propertyDataType;
#>
            this._<#= property.Name #> = new <#= propertyDataType #>();
<#
        }
      }

      // Type navigation fields initialization
      foreach (HibernateRelationProperty relationProperty in type.RelationProperties) {
        if (relationProperty.Generate && !(bool)relationProperty.GetProperty("Unimplemented")) {
          if (relationProperty.Multiplicity == Multiplicity.Many) {
#>
            this._<#= relationProperty.Name #> = new <#= GetRelationPropertyCollectionInitialization(relationProperty, _namespace.Key, defaultNamespace) #>;
<#
          }
        }
      }
#>
            OnCreated();
        }
<#
      // properties
      foreach (HibernateProperty property in type.Properties) {
	      if (!(bool)property.GetProperty("Unimplemented")) {
            if ((bool)property.GetProperty("GenerateDataMemberAttribute"))
              propertyCounter++;
            GenerateProperty(property, propertyCounter, PropertyChangeNotifiers, _namespace.Key);
		    }
      }

      // Type navigation properties  
      foreach (HibernateRelationProperty relationProperty in type.RelationProperties) {
        if (relationProperty.Generate) {
          if ((bool)relationProperty.GetProperty("GenerateDataMemberAttribute") && (relationProperty.Multiplicity == Multiplicity.Many || !relationProperty.OppositeRelationProperty.Generate)) 
            propertyCounter ++;
          if(!(bool)relationProperty.GetProperty("Unimplemented"))
            GenerateRelationProperty(relationProperty, propertyCounter, PropertyChangeNotifiers, _namespace.Key);
        }
      }

      // parent property generation
      if (type.Parent.Generate) {

        output.Indent++;
        GenerateDocumentation(type.Parent.Documentation, type.Parent.Name);
        output.Indent--;

        string propertyAccess = string.Empty;
        string getAccess = string.Empty;
        string setAccess = string.Empty;
        GetMemberAccessModifiers(type.Parent.GetterModifier, type.Parent.SetterModifier, ref propertyAccess, ref getAccess, ref setAccess);
        string propName = codeProvider.GetValidIdentifier(type.Parent.Name);
        List<Class> owners = type.GetOwners();
        string dataType = "object";
        if (owners.Count == 1) {
          dataType = codeProvider.GetValidIdentifier(owners[0].Name);
          string nspace = !string.IsNullOrEmpty(((IHibernateClass)owners[0]).Namespace) ? codeProvider.GetValidIdentifier(((IHibernateClass)owners[0]).Namespace) : defaultNamespace;
          if (nspace != _namespace.Key)
            dataType = nspace + "." + dataType;
        }
#>
        <#= propertyAccess #> <#= dataType #> <#= propName #>
        {
            <#= getAccess #>get;
            <#= setAccess #>set;
        }
<#        
      }

      // References to many-to-many associations ends (if association component)
      foreach (HibernateAssociation association in type.GetDependentManyToManyAssociations()) { 
#>

        #region Ends of the many-to-many association '<#= association.Name #>'
<#
        if (association.Parent.Generate) {
          if ((bool)association.Parent.GetProperty("GenerateDataMemberAttribute"))
            propertyCounter ++;
          if(!(bool)association.Parent.GetProperty("Unimplemented"))
            GenerateManyToManyComponentRelationProperty((HibernateRelationProperty)association.Parent, propertyCounter, _namespace.Key);
        }

        if (association.Child.Generate) {
          if ((bool)association.Child.GetProperty("GenerateDataMemberAttribute"))
            propertyCounter ++;
          if(!(bool)association.Child.GetProperty("Unimplemented"))
            GenerateManyToManyComponentRelationProperty((HibernateRelationProperty)association.Child, propertyCounter, _namespace.Key);
        }
#>

        #endregion
<#
      }

      // ICloneable
      if (ImplementCloneable)
        GenerateICloneable(type, _namespace.Key, defaultNamespace);

      if (PropertyChangeNotifiers) {
#>
   
        public virtual event PropertyChangingEventHandler PropertyChanging;

        public virtual event PropertyChangedEventHandler PropertyChanged;

        protected virtual void SendPropertyChanging()
        {
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, emptyChangingEventArgs);
        }

        protected virtual void SendPropertyChanging(System.String propertyName) 
        {    
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, new PropertyChangingEventArgs(propertyName));
        }

        protected virtual void SendPropertyChanged(System.String propertyName)
        {    
		        var handler = this.PropertyChanged;
            if (handler != null)
                handler(this, new PropertyChangedEventArgs(propertyName));
        }
<#    
      }
#>
    }
<#
      if (FilePerClass) {
#>

}
<#
        output.PopOutputRedirection();
      }
    
    } // End of complex type generation

    if (!FilePerClass) {
#>

}
<#
    }
  } // End of namespace generation


  //------------------------------------------------------------------------------
  // Enum types generation
  //------------------------------------------------------------------------------
  var enumTypeNamespaces = from type in model.EnumTypes.Cast<HibernateEnumType>()
    let namespaceName = !String.IsNullOrEmpty(type.Namespace) ? codeProvider.GetValidIdentifier(type.Namespace) : defaultNamespace
    group type by namespaceName;

  foreach (var _namespace in enumTypeNamespaces) {
    if (!FilePerClass) {
#>

namespace <#= _namespace.Key #>
{
<#
    }
    foreach (HibernateEnumType enumType in _namespace) {
      if (FilePerClass) {
        output.Extension = ".cs";
        output.PushOutputRedirection(EntitiesOutput, baseFileName + "." + enumType.Name);
        GenerateFileHeader(true);
#>

namespace <#= _namespace.Key #>
{
<#
      }
      GenerateDocumentation(enumType.Documentation, enumType.FullName);

      if (GenerateDataContracts && (bool)enumType.GetProperty("GenerateDataContractAttribute")) {
#>
    [DataContract()]
<#
      }

      foreach(AttributeValue attribute in enumType.Attributes) {
        output.AddReference(attribute.Constructor.AttributeType.Assembly.Name);
#>
    <#= codeProvider.FormatAttributeValue(attribute) #>
<#
      }

      if (enumType.IsFlag) {
#>
    [Flags]
<#
      }
#>
    <#= codeProvider.FormatClassAccess(enumType.Access) #> enum <#= codeProvider.GetValidIdentifier(enumType.Name) #> : <#= codeProvider.GetNullableType(false, enumType.HibernateUnderlyingDataType) #>
 {
<# 
     // enum type members
    if (enumType.Members.Count > 0) {
      foreach (EnumTypeMember member in enumType.Members) {

	    output.Indent++;
        GenerateDocumentation(member.Documentation, member.ParentEnum.Name + "." + member.Name);
	    output.Indent--;

	      if (GenerateDataContracts && (bool)member.GetProperty("GenerateEnumMemberAttribute")) {
#>
        [EnumMember]
<# 
        }
#>
        <#= codeProvider.GetValidIdentifier(member.Name) #><# if (member.Value != null) { #> = <#= member.Value.ToString() #><# } #><# if (member != enumType.Members.Last()) { #>,<# } #>
<#       
      }
    } // End of enum type members generation 
#>

    }
<#
      if (FilePerClass) {
#>

}
<#
        output.PopOutputRedirection();
      }
    
    } // End of enum type generation

    if (!FilePerClass) {
#>

}
<#
    }
  } // End of namespace generation


  //------------------------------------------------------------------------------
  // Model methods execution helper generation
  //------------------------------------------------------------------------------
  if (model.Methods.Count > 0) { 
    string helperClassName = baseFileName + "MethodsExecutor";
    if (FilePerClass) {
        output.Extension = ".cs";
        output.PushOutputRedirection(EntitiesOutput, baseFileName + ".Extensions.MethodsExecutor");
        GenerateFileHeader(true);
    }
#>

namespace <#= defaultNamespace #>
{

    public partial class <#= codeProvider.GetValidIdentifier(helperClassName) #>
    {

        private NHibernate.ISession session;

        public <#= codeProvider.GetValidIdentifier(helperClassName) #>(NHibernate.ISession session)
        {
          this.session = session;
        }
<# 
      foreach (HibernateMethod method in model.Methods) {

        GenerateHelperMethod(method, false);
        GenerateHelperMethod(method, true);
      }
#>
    }
<#
      if (FilePerClass) {
#>

}
<#
        output.PopOutputRedirection();
      }
    if (!FilePerClass) {
#>

}
<#
    }
  } // End: model method execution helper generation

  output.PopOutputRedirection();
  // Xml mapping generation
  if (XmlMappingAction != MetadataArtifactProcessing.DoNotGenerateMappingFiles) {
    output.Extension = ".hbm.xml";
    if (!XmlMappingFilePerClass) {
      if (XmlMappingAction == MetadataArtifactProcessing.CopyToOutputDirectory)
        output.PushOutputRedirection(XmlMappingOutput, model.FileName, BuildAction.None, CopyToOutputDirectory.CopyAlways);
      else
        output.PushOutputRedirection(XmlMappingOutput, model.FileName, BuildAction.EmbeddedResource, CopyToOutputDirectory.DoNotCopy);
#>
<#= ((EntityDeveloper.NHibernate.Serialization.HibernateModelSerializer)model.GetModelSerializer()).GetXmlMappingText(true) #><#
    output.PopOutputRedirection();
    }
    else {
      foreach(HibernateClass cls in model.Classes) {
        if (XmlMappingAction == MetadataArtifactProcessing.CopyToOutputDirectory)
          output.PushOutputRedirection(XmlMappingOutput, baseFileName + "." + cls.Name, BuildAction.None, CopyToOutputDirectory.CopyAlways);
        else
          output.PushOutputRedirection(XmlMappingOutput, baseFileName + "." + cls.Name, BuildAction.EmbeddedResource, CopyToOutputDirectory.DoNotCopy);
#>
<#= ((EntityDeveloper.NHibernate.Serialization.HibernateModelSerializer)model.GetModelSerializer()).GetClassXmlMappingText(cls) #><#
      output.PopOutputRedirection();
      }

      // save additional model mappings (filters and methods)
      if (model.Filters.Count > 0 || model.TypeDefs.Count > 0 || model.Methods.Count > 0) {
	  
	      if (XmlMappingAction == MetadataArtifactProcessing.CopyToOutputDirectory)
          output.PushOutputRedirection(XmlMappingOutput, model.FileName, BuildAction.None, CopyToOutputDirectory.CopyAlways);
        else
          output.PushOutputRedirection(XmlMappingOutput, model.FileName, BuildAction.EmbeddedResource, CopyToOutputDirectory.DoNotCopy);
#>
<#= ((EntityDeveloper.NHibernate.Serialization.HibernateModelSerializer)model.GetModelSerializer()).GetXmlMappingText(false) #><#
        output.PopOutputRedirection();

      }
    }
  }

  // End of generation
#>
<#+
  private string baseFileName = string.Empty;

  //////////////////////////////////////////////////////////////////////////////////
  //
  // Method GenerateFileHeader()
  // Comments and namespaces for each generated file.
  //
  //////////////////////////////////////////////////////////////////////////////////
  private void GenerateFileHeader(bool generateWarning) {
#>
<#+ 
   if (generateWarning) {
#>
//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by Entity Developer tool using NHibernate template.
// <#= String.IsNullOrEmpty(HeaderTimestampVersionControlTag) ? "Code is generated on: " + DateTime.Now : HeaderTimestampVersionControlTag #>
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

<#+
   }
#>
using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;
<#+ 
   if (ImplementValidatable) {
#>
using NHibernate.Classic;
<#+
   }
#>
<#+ 
   if (GenerateDataContracts) {
#>
using System.Runtime.Serialization;
<#+
   }
#>
<#+
  }
#>
<#+ 
  //////////////////////////////////////////////////////////////////////////////////
  //
  // Method GenerateExtensibilityMethodDefinitions()
  // Class extensibility method definitions generation.
  //
  //////////////////////////////////////////////////////////////////////////////////
  private void GenerateExtensibilityMethodDefinitions(BaseClass type, string _namespace, bool implementEquals) {
#>
    
        #region Extensibility Method Definitions
        
        partial void OnCreated();
<#+
    if (implementEquals) { 
#>

        public override bool Equals(object obj)
        {
<#+
      if (type is Class && ((Class)type).BaseInheritance != null) {
#>
          if (!base.Equals(obj))
          {
            return false;
          }

<#+
      }
#>
          <#= codeProvider.GetValidIdentifier(type.Name) #> toCompare = obj as <#= codeProvider.GetValidIdentifier(type.Name) #>;
          if (toCompare == null)
          {
            return false;
          }

<#+
      foreach (HibernateProperty prop in (type.Properties.Where(p => p.PrimaryKey).Any() ? type.Properties.Where(p => p.PrimaryKey) : type.Properties)) { 
#>
          if (!Object.Equals(this.<#= codeProvider.GetValidIdentifier(prop.Name) #>, toCompare.<#= codeProvider.GetValidIdentifier(prop.Name) #>))
            return false;
<#+
      }
#>
          
          return true;
        }

        public override int GetHashCode()
        {
          int hashCode = 13;
<#+
      foreach (HibernateProperty prop in (type.Properties.Where(p => p.PrimaryKey).Any() ? type.Properties.Where(p => p.PrimaryKey) : type.Properties.Where(p => !p.Nullable))) { 
#>
          hashCode = (hashCode * 7) + <#= codeProvider.GetValidIdentifier(prop.Name) #>.GetHashCode();
<#+
      }
#>
          return hashCode;
        }
<#+
    }

    if (PropertyChangePartialMethods) {
      string defaultNamespace = codeProvider.GetValidIdentifier(model.GetDefaultNamespace());
      foreach (HibernateProperty property in type.Properties) {
	    if (!(bool)property.GetProperty("Unimplemented")) {
#> 
        partial void On<#= property.Name #>Changing(<#= GetPropertyTypeName(property, _namespace, defaultNamespace) #> value);
        partial void On<#= property.Name #>Changed();
<#+
        }
	  }
#>
<#+
      Class cls = type as Class;
      if (cls != null) {
        foreach (HibernateRelationProperty property in cls.RelationProperties ) {
          if (!property.Generate || property.Multiplicity == Multiplicity.Many || (bool)property.GetProperty("Unimplemented"))
            continue;
            string nspace = !string.IsNullOrEmpty(((IHibernateClass)property.RelationClass).Namespace) ? codeProvider.GetValidIdentifier(((IHibernateClass)property.RelationClass).Namespace) : defaultNamespace;
            string className = codeProvider.GetValidIdentifier(property.RelationClass.Name);
            if (nspace != _namespace)
              className = nspace + "." + className;
#> 
        partial void On<#= property.Name #>Changing(<#= className #> value);
        partial void On<#= property.Name #>Changed();
<#+
        }
      }
    }
#>
        
        #endregion
<#+
   }
#>
<#+
  //////////////////////////////////////////////////////////////////////////////////
  //
  // Method GenerateProperty()
  // Property generation for classes.
  //
  //////////////////////////////////////////////////////////////////////////////////
  private void GenerateProperty(HibernateProperty property, int propertyOrder, bool supportPropertyChanging, string _namespace) {
#>

<#+
    string propertyAccess = string.Empty;
    string getAccess = string.Empty;
    string setAccess = string.Empty;
    GetMemberAccessModifiers(property.GetterModifier, property.SetterModifier, ref propertyAccess, ref getAccess, ref setAccess);

    string propType = GetPropertyTypeName(property, _namespace, codeProvider.GetValidIdentifier(model.GetDefaultNamespace()));
    string propName = codeProvider.GetValidIdentifier(property.Name);

    output.Indent++;
    GenerateDocumentation(property.Documentation, property.Name);
    output.Indent--;

    if (GenerateDataContracts && (bool)property.GetProperty("GenerateDataMemberAttribute")) {
#>
        [DataMember(Order=<#= propertyOrder #>)]
<#+
    }

    foreach(AttributeValue attribute in property.Attributes) {
      output.AddReference(attribute.Constructor.AttributeType.Assembly.Name);     
#>
        <#= codeProvider.FormatAttributeValue(attribute) #>
<#+
    }

    // Generate validation attributes for property
    GeneratePropertyValidationAttributes(property);
#>
        <#= propertyAccess == "private" ? propertyAccess : propertyAccess + " virtual" #> <#= propType #> <#= propName #>
        {
            <#= getAccess #>get
            {
                return this._<#= property.Name #>;
            }
            <#= setAccess #>set
            {
<#+
    if (supportPropertyChanging || PropertyChangePartialMethods) {
#>
                if (this._<#= property.Name #> != value)
                {
<#+
      if (PropertyChangePartialMethods) {
#>
                    this.On<#= property.Name #>Changing(value);
<#+
      }
      if (supportPropertyChanging) {
#>
                    this.SendPropertyChanging();
<#+
      }
      output.Indent++;
    }
#>
                this._<#= property.Name #> = value;
<#+
    if (supportPropertyChanging || PropertyChangePartialMethods) {
      output.Indent--;
      if (supportPropertyChanging) {
#>
                    this.SendPropertyChanged("<#= codeProvider.GetValidIdentifier(property.Name) #>");
<#+
      }
      if (PropertyChangePartialMethods) {
#>
                    this.On<#= property.Name #>Changed();
<#+
      }
#>
                }
<#+
    }
#>
            }
        }
<#+
  }
#>
<#+
  //////////////////////////////////////////////////////////////////////////////////
  //
  // Method GenerateRelationProperty()
  // Navigation property generation for entity classes.
  //
  //////////////////////////////////////////////////////////////////////////////////
  private void GenerateRelationProperty(HibernateRelationProperty relationProperty, int propertyOrder, bool supportPropertyChanging, string _namespace) { #>

<#+
    string propertyAccess = string.Empty;
    string getAccess = string.Empty;
    string setAccess = string.Empty;
    GetMemberAccessModifiers(relationProperty.GetterModifier, relationProperty.SetterModifier, ref propertyAccess, ref getAccess, ref setAccess);

    string propName = codeProvider.GetValidIdentifier(relationProperty.Name);
    string defaultNamespace = codeProvider.GetValidIdentifier(model.GetDefaultNamespace());
    string dataType = GetRelationPropertyTypeName(relationProperty, _namespace, defaultNamespace);

    output.Indent++;
    GenerateDocumentation(relationProperty.Documentation, relationProperty.Name);
    output.Indent--;

    if (GenerateDataContracts && (bool)relationProperty.GetProperty("GenerateDataMemberAttribute") && (relationProperty.Multiplicity == Multiplicity.Many || (!relationProperty.OppositeRelationProperty.Generate || (relationProperty.OppositeRelationProperty.Multiplicity != Multiplicity.Many && relationProperty.Association.Child == relationProperty)))) {
#>
        [DataMember(Order=<#= propertyOrder #>, EmitDefaultValue=false)]
<#+ }

    foreach(AttributeValue attribute in relationProperty.Attributes) {
      output.AddReference(attribute.Constructor.AttributeType.Assembly.Name);     
#>
        <#= codeProvider.FormatAttributeValue(attribute) #>
<#+
    }
#>
        <#= propertyAccess == "private" ? propertyAccess : propertyAccess + " virtual" #> <#= dataType #> <#= propName #>
        {
            <#= getAccess #>get
            {
                return this._<#= relationProperty.Name #>;
            }
            <#= setAccess #>set
            {
<#+
      if (relationProperty.Multiplicity != Multiplicity.Many && (supportPropertyChanging || PropertyChangePartialMethods)) {
#>
                if (this._<#= relationProperty.Name #> != value)
                {
<#+
        if (PropertyChangePartialMethods) {
#>
                    this.On<#= relationProperty.Name #>Changing(value);
<#+
        }
        if (supportPropertyChanging) {
#>
                    this.SendPropertyChanging();
<#+
        }
        output.Indent++;
      }
#>
                this._<#= relationProperty.Name #> = value;
<#+
      if (relationProperty.Multiplicity != Multiplicity.Many && (supportPropertyChanging || PropertyChangePartialMethods)) {
        output.Indent--;
        if (supportPropertyChanging) {
#>
                    this.SendPropertyChanged("<#= codeProvider.GetValidIdentifier(relationProperty.Name) #>");
<#+
        }
        if (PropertyChangePartialMethods) {
#>
                    this.On<#= relationProperty.Name #>Changed();
<#+
        }
#>
                }
<#+
      }
#>
            }
        }
<#+
  }
#>
<#+
  //////////////////////////////////////////////////////////////////////////////////
  //
  // Method GenerateManyToManyComponentRelationProperty()
  // Navigation property generation for many-to-many association component.
  //
  //////////////////////////////////////////////////////////////////////////////////
  private void GenerateManyToManyComponentRelationProperty(HibernateRelationProperty relationProperty, int propertyOrder, string _namespace) { #>

<#+
    string propertyAccess = string.Empty;
    string getAccess = string.Empty;
    string setAccess = string.Empty;
    GetMemberAccessModifiers(relationProperty.GetterModifier, relationProperty.SetterModifier, ref propertyAccess, ref getAccess, ref setAccess);

    string defaultNamespace = codeProvider.GetValidIdentifier(model.GetDefaultNamespace());
    string dataType = codeProvider.GetValidIdentifier(relationProperty.RelationClass.Name);
    string nspace = !string.IsNullOrEmpty(((IHibernateClass)relationProperty.RelationClass).Namespace) ? codeProvider.GetValidIdentifier(((IHibernateClass)relationProperty.RelationClass).Namespace) : defaultNamespace;
    if (nspace != _namespace)
      dataType = nspace + "." + dataType;


    output.Indent++;
    GenerateDocumentation(relationProperty.Documentation, relationProperty.Name);
    output.Indent--;

    if (GenerateDataContracts && (bool)relationProperty.GetProperty("GenerateDataMemberAttribute")) {
#>
        [DataMember(Order=<#= propertyOrder #>, EmitDefaultValue=false)]
<#+ }
#>
        <#= propertyAccess #> <#= dataType #> <#= codeProvider.GetValidIdentifier(relationProperty.Name) #>
        {
            <#= getAccess #>get;
            <#= setAccess #>set;
        }
<#+
  }
#>
<#+
  //////////////////////////////////////////////////////////////////////////////////
  //
  // Method GenerateHelperMethod(HibernateMethod method, bool isStatic)
  // Helper class method generation.
  //
  //////////////////////////////////////////////////////////////////////////////////
  private void GenerateHelperMethod(HibernateMethod method, bool isStatic) {

    List<string> lst = new List<string>();
    if (isStatic)
      lst.Add("NHibernate.ISession session");
    foreach (HibernateParameter parameter in method.Parameters) {
      string prefix = string.Empty;
      if (parameter.Direction == ParameterDirection.InputOutput)
        prefix+= "ref ";
      else
        if (parameter.Direction == ParameterDirection.Output)
          prefix+= "out ";

      lst.Add(string.Format("{0}{1} {2}", prefix, codeProvider.GetNullableType(true, parameter.Type), codeProvider.GetValidIdentifier(parameter.Name)));
    }

    string returnType;
    switch(method.MethodType) {
      case MethodType.Procedure:
        returnType = "void";
      break;
      case MethodType.ScalarResult:
        returnType = codeProvider.GetNullableType(true, method.ReturnType);
      break;
      case MethodType.EntityResult:
      case MethodType.ValueTypeResult:
        string nspace = !string.IsNullOrEmpty(((IHibernateClass)method.ReturnType).Namespace) ? codeProvider.GetValidIdentifier(((IHibernateClass)method.ReturnType).Namespace) : codeProvider.GetValidIdentifier(model.GetDefaultNamespace());
        string dataType = nspace + "." + codeProvider.GetValidIdentifier(((BaseClass)method.ReturnType).Name);
        returnType = String.Format("IList<{0}>", dataType);
      break;
      default:
        return;
    }
#>

<#+
    output.Indent++;
    GenerateDocumentation(method.Documentation, method.Name);
    output.Indent--;

    foreach(AttributeValue attribute in method.Attributes) {
      output.AddReference(attribute.Constructor.AttributeType.Assembly.Name);     
#>
        <#= codeProvider.FormatAttributeValue(attribute) #>
<#+
      }
#>
        public <#+ if(isStatic) { #>static <#+ } #><#= returnType #> <#= codeProvider.GetValidIdentifier(method.Name) #>(<#= codeProvider.Join(", ", lst.ToArray()) #>)
        {

            NHibernate.IQuery query = session.GetNamedQuery(@"<#= method.Name #>");
<#+
    foreach (HibernateParameter parameter in method.Parameters) {
#>
            query.SetParameter(@"<#= parameter.Name #>", <#= codeProvider.GetValidIdentifier(parameter.Name) #>);
<#+
    }

    switch(method.MethodType) {
      case MethodType.Procedure:
#>
            query.List();
<#+
      break;
      case MethodType.ScalarResult:
#>
            return ((<#= returnType #>)(query.UniqueResult()));
<#+
      break;
      case MethodType.EntityResult:
      case MethodType.ValueTypeResult:
        string nspace = !string.IsNullOrEmpty(((IHibernateClass)method.ReturnType).Namespace) ? codeProvider.GetValidIdentifier(((IHibernateClass)method.ReturnType).Namespace) : codeProvider.GetValidIdentifier(model.GetDefaultNamespace());
        string dataType = nspace + "." + codeProvider.GetValidIdentifier(method.ReturnType.ToString());
#>
            return query.List<<#= dataType #>>();
<#+
      break;
    }
#> 
        }
<#+
  }
#>
<#+
  //////////////////////////////////////////////////////////////////////////////////
  //
  // Method GenerateDocumentation()
  // Documentation comments generation for classes, properties and methods.
  //
  //////////////////////////////////////////////////////////////////////////////////
  private void GenerateDocumentation(Documentation doc, string name) {
#>

    /// <summary>
<#+
    if (!string.IsNullOrEmpty(doc.Summary)) {
      foreach (string str in doc.Summary.Split('\n')) {
#>
    /// <#= str.TrimEnd('\r') #>
<#+
      }
    }
    else {
#>
    /// There are no comments for <#= name #> in the schema.
<#+
    }
#>
    /// </summary>
<#+
    if (!string.IsNullOrEmpty(doc.LongDescription)) {
#>
    /// <LongDescription>
<#+
      foreach (string str in doc.LongDescription.Split('\n')) {
#>
    /// <#= str.TrimEnd('\r') #>
<#+
      }
#>
    /// </LongDescription>
<#+
    }
  }
#>
<#+ 
  //////////////////////////////////////////////////////////////////////////////////
  //
  // Method GenerateIValidatable()
  // IValidatable implementation 
  //
  //////////////////////////////////////////////////////////////////////////////////
  private void GenerateIValidatable() {
#>
    
        #region IValidatable Members

        public virtual void Validate()
        {
            OnValidate();
        }

        partial void OnValidate();

        #endregion
<#+
   }
#>
<#+ 
  //////////////////////////////////////////////////////////////////////////////////
  //
  // Method GenerateICloneable()
  // ICloneable implementation 
  //
  //////////////////////////////////////////////////////////////////////////////////
  private void GenerateICloneable(BaseClass baseClass, string parentNamespace, string defaultNamespace) {
#>
    
        #region ICloneable Members

        public <#+ if (baseClass is Class && ((Class)baseClass).BaseInheritance != null) { #>override<#+ } else { #>virtual<#+ } #> object Clone()
        {
            <#= codeProvider.GetValidIdentifier(baseClass.Name) #> obj = new <#= codeProvider.GetValidIdentifier(baseClass.Name) #>();
<#+
      List<Property> properties = new List<Property>();
      if (baseClass is Class)
        properties.AddRange(((Class)baseClass).BaseProperties);
      properties.AddRange(baseClass.Properties);

      foreach (Property prop in properties.Where(p => !(bool)p.GetProperty("Unimplemented"))) {
        if (prop.IsComplexType) {
          string nspace = !string.IsNullOrEmpty(((IHibernateClass)prop.Type).Namespace) ? codeProvider.GetValidIdentifier(((IHibernateClass)prop.Type).Namespace) : defaultNamespace;
          string propertyDataType = codeProvider.GetValidIdentifier(prop.Type.ToString());
          if (nspace != parentNamespace)
            propertyDataType = nspace + "." + propertyDataType;

#>
            obj.<#= codeProvider.GetValidIdentifier(prop.Name) #> = (<#= propertyDataType #>)<#= codeProvider.GetValidIdentifier(prop.Name) #>.Clone();
<#+
        }
        else {
#>
            obj.<#= codeProvider.GetValidIdentifier(prop.Name) #> = <#= codeProvider.GetValidIdentifier(prop.Name) #>;
<#+
        }
      }
#>
            return obj;
        }

        #endregion
<#+
   }
#>
<#+ 
  //////////////////////////////////////////////////////////////////////////////////
  //
  // Method GetRelationPropertyCollectionInitialization(HebirnateRelationProperty relationProperty, string parentNamespace, string defaultNamespace)
  // Retuns the string to create a .NET collection instance for translating NHibernate collections.
  //
  //////////////////////////////////////////////////////////////////////////////////
  private string GetRelationPropertyCollectionInitialization(HibernateRelationProperty relationProperty, string parentNamespace, string defaultNamespace) {

    BaseClass relationClass = relationProperty.RelationClass;
    if ((relationProperty.Multiplicity == Multiplicity.Many && relationProperty.OppositeRelationProperty.Multiplicity == Multiplicity.Many) &&
        ((HibernateAssociation)relationProperty.Association).ManyToManyComponent != null)
      relationClass = ((HibernateAssociation)relationProperty.Association).ManyToManyComponent;

    string relationClassName = codeProvider.GetValidIdentifier(relationClass.Name);
    string nspace = !string.IsNullOrEmpty(((IHibernateClass)relationClass).Namespace) ? codeProvider.GetValidIdentifier(((IHibernateClass)relationClass).Namespace) : defaultNamespace;
    if (nspace != parentNamespace)
      relationClassName = nspace + "." + relationClassName;

    switch (relationProperty.CollectionType) { 
      case HibernateCollectionType.Set:
        if (relationProperty.Generic){
		  if (!string.IsNullOrEmpty(relationProperty.Sort) && !relationProperty.Sort.Equals("unsorted"))
		    return string.Format("Iesi.Collections.Generic.SortedSet<{0}>()", relationClassName);
		  if (!string.IsNullOrEmpty(relationProperty.OrderBy))
		    return string.Format("Iesi.Collections.Generic.OrderedSet<{0}>()", relationClassName);
          return string.Format("Iesi.Collections.Generic.HashedSet<{0}>()", relationClassName);
		}
        else {
		     if (!string.IsNullOrEmpty(relationProperty.Sort) && !relationProperty.Sort.Equals("unsorted"))
		       return "Iesi.Collections.SortedSet()";
		     if (!string.IsNullOrEmpty(relationProperty.OrderBy))
		       return "Iesi.Collections.ListSet()";
             return "Iesi.Collections.HashedSet()";
		     }
      case HibernateCollectionType.List:
      case HibernateCollectionType.Bag:
      case HibernateCollectionType.Idbag:
        if (relationProperty.Generic)
          return string.Format("List<{0}>()", relationClassName);
        else
          return "ArrayList()";
      case HibernateCollectionType.Map:
        if (relationProperty.Generic) {
          string indexType;
          if (relationProperty.IndexType is BaseClass) { 
            indexType = codeProvider.GetValidIdentifier(((BaseClass)relationProperty.IndexType).Name);
            string classTypeNamespace = !string.IsNullOrEmpty(((IHibernateClass)relationProperty.IndexType).Namespace) ? codeProvider.GetValidIdentifier(((IHibernateClass)relationProperty.IndexType).Namespace) : defaultNamespace;
            if (classTypeNamespace != parentNamespace)
              indexType = classTypeNamespace + "." + indexType;
          }
          else 
            indexType = codeProvider.GetNullableType(false, relationProperty.IndexType);
                    string dataType = "Dictionary";
          return string.Format("Dictionary<{0},{1}>()", indexType, relationClassName);
        }
        else
          return "Hashtable()";
      case HibernateCollectionType.Array:
        return relationClassName + "[0]";
      default:
        throw new NotSupportedException(string.Format("CollectionType is not supported on {0}.", relationProperty.Name));
    }
   }
#>
<#+ 
  //////////////////////////////////////////////////////////////////////////////////
  //
  // Method GetRelationPropertyTypeName(HebirnateRelationProperty relationProperty, string parentNamespace, string defaultNamespace)
  // Returns the interface or type name of navigation property.
  //
  //////////////////////////////////////////////////////////////////////////////////
  private string GetRelationPropertyTypeName(HibernateRelationProperty relationProperty, string parentNamespace, string defaultNamespace) {

    BaseClass relationClass = relationProperty.RelationClass;
    if ((relationProperty.Multiplicity == Multiplicity.Many && relationProperty.OppositeRelationProperty.Multiplicity == Multiplicity.Many) &&
       ((HibernateAssociation)relationProperty.Association).ManyToManyComponent != null)
      relationClass = ((HibernateAssociation)relationProperty.Association).ManyToManyComponent;

    string relationClassName = codeProvider.GetValidIdentifier(relationClass.Name);
    string nspace = !string.IsNullOrEmpty(((IHibernateClass)relationClass).Namespace) ? codeProvider.GetValidIdentifier(((IHibernateClass)relationClass).Namespace) : defaultNamespace;
    if (nspace != parentNamespace)
      relationClassName = nspace + "." + relationClassName;

    if (relationProperty.Multiplicity != Multiplicity.Many)
      return relationClassName;

    switch (relationProperty.CollectionType) { 
      case HibernateCollectionType.Set:
        if (relationProperty.Generic)
          return string.Format("Iesi.Collections.Generic.ISet<{0}>", relationClassName);
        else
          return "Iesi.Collections.ISet";
      case HibernateCollectionType.List:
      case HibernateCollectionType.Bag:
      case HibernateCollectionType.Idbag:
        if (relationProperty.Generic)
          return string.Format("IList<{0}>", relationClassName);
        else
          return "IList";
      case HibernateCollectionType.Map:
        if (relationProperty.Generic) {
          string indexType;
          if (relationProperty.IndexType is BaseClass) { 
            indexType = codeProvider.GetValidIdentifier(((BaseClass)relationProperty.IndexType).Name);
            string classTypeNamespace = !string.IsNullOrEmpty(((IHibernateClass)relationProperty.IndexType).Namespace) ? codeProvider.GetValidIdentifier(((IHibernateClass)relationProperty.IndexType).Namespace) : defaultNamespace;
            if (classTypeNamespace != parentNamespace)
              indexType = classTypeNamespace + "." + indexType;
          }
          else 
            indexType = codeProvider.GetNullableType(false, relationProperty.IndexType);
          return string.Format("IDictionary<{0},{1}>", indexType, relationClassName);
        }
        else
          return "IDictionary";
      case HibernateCollectionType.Array:
        return relationClassName + "[]";
      default:
        throw new NotSupportedException(string.Format("CollectionType is not supported on {0}.", relationProperty.Name));
    }
   }
#>
<#+ 
  //////////////////////////////////////////////////////////////////////////////////
  //
  // Method GetRelationTypeName(HebirnateProperty property, string parentNamespace, string defaultNamespace)
  // Returns the type name of property.
  //
  //////////////////////////////////////////////////////////////////////////////////
  private string GetPropertyTypeName(HibernateProperty property, string parentNamespace, string defaultNamespace) {

        string propertyDataType = string.Empty;
        if (property.IsComplexType || property.IsEnumType) {
          string nspace = !string.IsNullOrEmpty(((IHibernateClass)property.Type).Namespace) ? codeProvider.GetValidIdentifier(((IHibernateClass)property.Type).Namespace) : defaultNamespace;
          propertyDataType = codeProvider.GetValidIdentifier(property.Type.ToString());
          if (nspace != parentNamespace)
            propertyDataType = nspace + "." + propertyDataType;
          }
        else
          propertyDataType = codeProvider.GetNullableType(property.Nullable, property.Type);

     return propertyDataType;
   }
#>
<#+
  // Method GetMemberAccessModifiers()
  private void GetMemberAccessModifiers(MemberAccess propertyGetter, MemberAccess propertySetter, ref string propertyAccess, ref string getAccess, ref string setAccess) {

    if ((int)propertyGetter < (int)propertySetter) {
      propertyAccess = codeProvider.FormatMemberAccess(propertyGetter);
      setAccess = codeProvider.FormatMemberAccess(propertySetter) + " ";
    }
    else
      if (propertyGetter == propertySetter) {
        propertyAccess = codeProvider.FormatMemberAccess(propertyGetter);
      }
      else {
        propertyAccess = codeProvider.FormatMemberAccess(propertySetter);
        getAccess = codeProvider.FormatMemberAccess(propertyGetter) + " ";
      }
  }
#>