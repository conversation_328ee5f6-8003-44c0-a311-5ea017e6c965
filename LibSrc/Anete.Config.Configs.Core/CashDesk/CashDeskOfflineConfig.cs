using System;
using System.ComponentModel;
using System.Diagnostics;
using Anete.Config.Core.Attributes;
using Anete.Config.Core.Categories;
using Anete.Config.Core.CFSystKonfig;
using Anete.Config.Core.Internals;
using Anete.Common.Core.Interface.Enums;
using Anete.Utils;
using Anete.Utils.ComponentModel.Attributes;
using Anete.Utils.ComponentModel.TypeConverters;
using System.Drawing.Design;

namespace Anete.Config.Configs.Core.CashDesk
{

	/// <summary>
	/// Nastaveni uzivatelskeho rozhrani
	/// </summary>
	[CashDeskBehaviorConfigDBKey("Offline")]
	[ConfigVersion("*******")]
	[ConfigClassSRDescription(typeof(CashDeskOfflineConfig))]
	[DefaultConfigInstance(false)]
	[OfflineConfig(ApplicationType.CashDesk)]
	[CashDeskCategory(CategoryLevel.Basic)]
	public class CashDeskOfflineConfig : ConfigBase
	{

		/// <summary>
		/// Initializes a new instance
		/// </summary>
		public CashDeskOfflineConfig()
		{
			_lanQualitySettings = EnumBasedFactory<LanQualityType, LanQualitySettingsBase>.CreateInstance(_lanQualityDefValue);
		}

		private const bool _enabledDefValue = false;
		private bool _enabled = _enabledDefValue;
		/// <summary>
		/// Je offline aktivovan
		/// </summary>
		[CashDeskCategory(CategoryLevel.Basic, CategoryLevel.Advanced)]
		[DefaultValue(_enabledDefValue)]
		[ConfigSRDisplayName("Enabled", typeof(CashDeskOfflineConfig))]
		[ConfigSRDescription("Enabled", typeof(CashDeskOfflineConfig))]
		[MapBoolPropertyToCFSystKonfig("KASA:Modul.Offline", MapToCFSystKonfigOption.ReadFromCFSystKonfig)]
		public bool Enabled
		{
			get { return _enabled; }
			set
			{
				if (_enabled == value)
				{
					return;
				}
				_enabled = value;
				OnPropertyChanged("Enabled");
			}
		}

		private const int _replicationIntervalDefValue = 60;
		private int _replicationInterval = _replicationIntervalDefValue;
		/// <summary>
		/// Interval v min, po kterem se budou replikovat data kasy do offline
		/// </summary>
		[CashDeskCategory(CategoryLevel.Advanced)]
		[DefaultValue(_replicationIntervalDefValue)]
		[ConfigSRDisplayName("ReplicationInterval", typeof(CashDeskOfflineConfig))]
		[ConfigSRDescription("ReplicationInterval", typeof(CashDeskOfflineConfig))]
		[MapPropertyToCFSystKonfig("KASA:OffLine.RefreshInterval", MapToCFSystKonfigOption.ReadFromCFSystKonfig)]
		public int ReplicationInterval
		{
			get { return _replicationInterval; }
			set
			{
				if (_replicationInterval == value)
				{
					return;
				}
				_replicationInterval = value;
				OnPropertyChanged("ReplicationInterval");
			}
		}

		private const int _replicationRangeDefValue = 30;
		private int _replicationRange = _replicationRangeDefValue;
		/// <summary>
		/// Pocet dni, pro ktere se budou natahovat data dopredu (napr. jidelnicek)
		/// </summary>
		[CashDeskCategory(CategoryLevel.Advanced)]
		[DefaultValue(_replicationRangeDefValue)]
		[ConfigSRDisplayName("ReplicationRange", typeof(CashDeskOfflineConfig))]
		[ConfigSRDescription("ReplicationRange", typeof(CashDeskOfflineConfig))]
		[MapPropertyToCFSystKonfig("KASA:OffLine.RefreshRange", MapToCFSystKonfigOption.ReadFromCFSystKonfig)]
		public int ReplicationRange
		{
			get { return _replicationRange; }
			set
			{
				if (_replicationRange == value)
				{
					return;
				}
				_replicationRange = value;
				OnPropertyChanged("ReplicationRange");
			}
		}

		private const bool _calcMealPriceBySubsidyCategoryDefValue = false;
		private bool _calcMealPriceBySubsidyCategory = _calcMealPriceBySubsidyCategoryDefValue;
		/// <summary>
		/// Aktivovat vypocet ceny jidel v offline podle kategorie dotace. Dela se jen pro ZSMV. 
		/// V takovem pripade se replikuje do offline tabulka Cenik a k vypoctu se pouziva KategorieDotace pro daneho klienta
		/// #redmine: 774
		/// 
		/// Konzultace s JoK: Tento parametr se jiz dlouhou dobu pouziva obecne. Neni to pro ZSMV. Jde o to, zda zakaznik chce mit vypocitace ceny v offline dle kategorie dotace nebo ne.
		/// </summary>
		[DefaultValue(_calcMealPriceBySubsidyCategoryDefValue)]
		[ConfigSRDisplayName("CalcMealPriceBySubsidyCategory", typeof(CashDeskOfflineConfig))]
		[ConfigSRDescription("CalcMealPriceBySubsidyCategory", typeof(CashDeskOfflineConfig))]
		[CashDeskCategory(CategoryLevel.Advanced)]
		public bool CalcMealPriceBySubsidyCategory
		{
			get { return _calcMealPriceBySubsidyCategory; }
			set
			{
				if (_calcMealPriceBySubsidyCategory == value)
				{
					return;
				}
				_calcMealPriceBySubsidyCategory = value;
				OnPropertyChanged("CalcMealPriceBySubsidyCategory");
			}
		}

		private int[] _mealPriceBySubsidyClientGroupIds = new int[] { };
		/// <summary>
		/// Seznam Id skupin stravniku, pro ktere se bude v offline pocitat cena podle kategorie dotace. 
		/// Aktivni pouze pokud je zapnute CalcMealPriceBySubsidyCategory.
		/// Pokud neni vyplnena zadna skupina, pak se pocita cena pro vsechny stravniky
		/// </summary>
		[ConfigSRDisplayName(nameof(MealPriceBySubsidyClientGroupIds), typeof(CashDeskOfflineConfig))]
		[ConfigSRDescription(nameof(MealPriceBySubsidyClientGroupIds), typeof(CashDeskOfflineConfig))]
		[TypeConverter(typeof(UniqueCommaArrayAsStringConverter<int>))]
		[Editor(typeof(UITypeEditor), typeof(UITypeEditor))]
		public int[] MealPriceBySubsidyClientGroupIds
		{
			get { return _mealPriceBySubsidyClientGroupIds; }
			set
			{
				if (_mealPriceBySubsidyClientGroupIds == value)
				{
					return;
				}
				_mealPriceBySubsidyClientGroupIds = value;
				OnPropertyChanged(nameof(MealPriceBySubsidyClientGroupIds));
			}
		}

		private const LanQualityType _lanQualityDefValue = LanQualityType.High;
		private LanQualityType _lanQuality = _lanQualityDefValue;
		/// <summary>
		/// Kvalita site
		/// redmine #4967
		/// </summary>
		[DefaultValue(typeof(LanQualityType), "High")]
		[ConfigSRDisplayName("LanQuality", typeof(CashDeskOfflineConfig))]
		[ConfigSRDescriptionWithEnumDescription("LanQuality", typeof(LanQualityType), typeof(CashDeskOfflineConfig))]
		[RefreshProperties(RefreshProperties.All)]
		public LanQualityType LanQuality
		{
			get { return _lanQuality; }
			set
			{
				if (_lanQuality == value)
				{
					return;
				}
				LanQualitySettings = EnumBasedFactory<LanQualityType, LanQualitySettingsBase>.CreateInstance(value);
				_lanQuality = value;
				OnPropertyChanged("LanQuality");
			}
		}

		private LanQualitySettingsBase _lanQualitySettings = null;
		/// <summary>
		/// Nastaveni kvality site
		/// </summary>
		[ConfigSRDisplayName("LanQualitySettings", typeof(CashDeskOfflineConfig))]
		[ConfigSRDescription("LanQualitySettings", typeof(CashDeskOfflineConfig))]
		[NestedClass]
		[Inherit(false)]
		public LanQualitySettingsBase LanQualitySettings
		{
			get { return _lanQualitySettings; }
			set
			{
				if (_lanQualitySettings == value)
				{
					return;
				}
				_lanQualitySettings = value;
				OnPropertyChanged("LanQualitySettings");
			}
		}
	}
}
