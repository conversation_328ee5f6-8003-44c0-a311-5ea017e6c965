using Anete.Common.Core.Business.InstallationParameters.Factories;
using Anete.Common.Core.Interface.Business.InstallationParameters.Factories;
using Anete.Common.Core.Interface.Business.InstallationParameters.Parameters;
using System;
using System.Linq;

namespace Anete.Common.Core.Business.InstallationParameters.Parameters.SystemSqlServerVersion
{
	public class SystemSqlServerVersionValueBasedFactory : InstallationParameterValueBasedFactoryBase, ISystemParameterFactoryFromValue
	{
		public ISystemInstallationParameter CreateFromParameterValue(IInstallationParameterValue value)
		{
			return new SystemSqlServerVersionParameter((SystemSqlServerVersionParameterValue)value);
		}
	}
}
