using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Anete.Config.Configs.Core.Global.Behaviour
{
	/// <summary>
	/// Pomocny atribut pro ApplicationCountry pro specifikaci pridruzeneho kodu statu
	/// </summary>
	[AttributeUsage(AttributeTargets.Enum | AttributeTargets.Field, AllowMultiple = false)]
	internal class ApplicationCountryAttribute : Attribute
	{
        #region constructors...
        /// <summary>
        /// Initializes a new instance
        /// </summary>
		public ApplicationCountryAttribute(string countryCode)
        {
			CountryCode = countryCode;
        }
        #endregion

        #region properties...
        /// <summary>
        /// Nazev kodu statu
        /// </summary>
		public string CountryCode
        {
            get;
            private set;
        }
        #endregion
	}
}
