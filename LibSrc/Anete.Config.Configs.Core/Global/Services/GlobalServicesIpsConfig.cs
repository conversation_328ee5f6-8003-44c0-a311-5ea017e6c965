using Anete.Common.Core.Interface.Enums;
using Anete.Config.Core.Attributes;
using Anete.Config.Core.Categories;
using Anete.Config.Core.Internals;
using System.ComponentModel;

namespace Anete.Config.Configs.Core.Global.Services
{
	/// <summary>
	/// Nastaveni interniho platebniho systemu
	/// </summary>
	[GlobalServicesConfigDBKey("Ips")]
	[ConfigVersion("*******")]
	[ConfigClassSRDescription(typeof(GlobalServicesIpsConfig))]
	[ApplicationCategory(ApplicationType.PresPoint)]
	[ApplicationCategory(ApplicationType.WebKredit2)]
	[DefaultConfigInstance(true)]
	[OfflineConfig(true, ApplicationType.CashDesk)]
	public class GlobalServicesIpsConfig : ConfigBase
	{

		private const bool _enabledDefValue = false;
		private bool _enabled = _enabledDefValue;
		/// <summary>
		/// Povoleni interniho platebniho systemu.
		/// Ovlivni chovani aplikaci, ktere budou nasledne nabizet formulare, ktere s IPS pracuji.
		/// </summary>
		[DefaultValue(_enabledDefValue)]
		[ConfigSRDisplayName(nameof(Enabled), typeof(GlobalServicesIpsConfig))]
		[ConfigSRDescription(nameof(Enabled), typeof(GlobalServicesIpsConfig))]
		public bool Enabled
		{
			get { return _enabled; }
			set
			{
				if (_enabled == value)
				{
					return;
				}
				_enabled = value;
				OnPropertyChanged(nameof(Enabled));
			}
		}

		private const decimal _maxTransferableAmountDefValue = 1000;
		private decimal _maxTransferableAmount = _maxTransferableAmountDefValue;
		/// <summary>
		/// Maximalni castka k prevodu
		/// </summary>
		[DefaultValue(typeof(decimal), "1000")]
		[ConfigSRDisplayName("MaxTransferableAmount", typeof(GlobalServicesIpsConfig))]
		[ConfigSRDescription("MaxTransferableAmount", typeof(GlobalServicesIpsConfig))]
		public decimal MaxTransferableAmount
		{
			get { return _maxTransferableAmount; }
			set
			{
				if (_maxTransferableAmount == value)
				{
					return;
				}
				_maxTransferableAmount = value;
				OnPropertyChanged();
			}
		}

		private const decimal _minTransferableAmountDefValue = 50;
		private decimal _minTransferableAmount = _minTransferableAmountDefValue;
		/// <summary>
		/// Minimalni castka k prevodu
		/// </summary>
		[DefaultValue(typeof(decimal), "50")]
		[ConfigSRDisplayName("MinTransferableAmount", typeof(GlobalServicesIpsConfig))]
		[ConfigSRDescription("MinTransferableAmount", typeof(GlobalServicesIpsConfig))]
		public decimal MinTransferableAmount
		{
			get { return _minTransferableAmount; }
			set
			{
				if (_minTransferableAmount == value)
				{
					return;
				}
				_minTransferableAmount = value;
				OnPropertyChanged();
			}
		}

		private const decimal _maxKreditBalanceAfterEncashmentDefValue = 10000;
		private decimal _maxKreditBalanceAfterEncashment = _maxKreditBalanceAfterEncashmentDefValue;
		/// <summary>
		/// Maximalna castka po dorovnani
		/// </summary>
		[DefaultValue(typeof(decimal), "10000")]
		[ConfigSRDisplayName("MaxKreditBalanceAfterEncashment", typeof(GlobalServicesIpsConfig))]
		[ConfigSRDescription("MaxKreditBalanceAfterEncashment", typeof(GlobalServicesIpsConfig))]
		public decimal MaxKreditBalanceAfterEncashment
		{
			get { return _maxKreditBalanceAfterEncashment; }
			set
			{
				if (_maxKreditBalanceAfterEncashment == value)
				{
					return;
				}
				_maxKreditBalanceAfterEncashment = value;
				OnPropertyChanged();
			}
		}

		private const decimal _minKreditBalanceAfterEncashmentDefValue = 100;
		private decimal _minKreditBalanceAfterEncashment = _minKreditBalanceAfterEncashmentDefValue;
		/// <summary>
		/// Minimalnu castka po dorovnani
		/// </summary>
		[DefaultValue(typeof(decimal), "100")]
		[ConfigSRDisplayName("MinKreditBalanceAfterEncashment", typeof(GlobalServicesIpsConfig))]
		[ConfigSRDescription("MinKreditBalanceAfterEncashment", typeof(GlobalServicesIpsConfig))]
		public decimal MinKreditBalanceAfterEncashment
		{
			get { return _minKreditBalanceAfterEncashment; }
			set
			{
				if (_minKreditBalanceAfterEncashment == value)
				{
					return;
				}
				_minKreditBalanceAfterEncashment = value;
				OnPropertyChanged();
			}
		}

		private const int _transactionHistoryMaxRowsDefValue = 20;
		private int _transactionHistoryMaxRows = _transactionHistoryMaxRowsDefValue;
		/// <summary>
		/// Maximalni pocet radku historie transakci. Vyuziva prozatim jen WebKredit
		/// </summary>
		[DefaultValue(_transactionHistoryMaxRowsDefValue)]
		[ConfigSRDisplayName("TransactionHistoryMaxRows", typeof(GlobalServicesIpsConfig))]
		[ConfigSRDescription("TransactionHistoryMaxRows", typeof(GlobalServicesIpsConfig))]
		public int TransactionHistoryMaxRows
		{
			get { return _transactionHistoryMaxRows; }
			set
			{
				if (_transactionHistoryMaxRows == value)
				{
					return;
				}
				_transactionHistoryMaxRows = value;
				OnPropertyChanged();
			}
		}

		private const string _displayNameDefValue = "SUPO";
		private string _displayName = _displayNameDefValue;
		/// <summary>
		/// Pod jakým názvem se má tahle funkčnost zobrazovat (SUPO pro MU, IPS pro JCU). Pro WebKredit.
		/// </summary>
		[DefaultValue(_displayNameDefValue)]
		[ConfigSRDisplayName(nameof(DisplayName), typeof(GlobalServicesIpsConfig))]
		[ConfigSRDescription(nameof(DisplayName), typeof(GlobalServicesIpsConfig))]
		public string DisplayName
		{
			get { return _displayName; }
			set
			{
				if (_displayName == value)
				{
					return;
				}
				_displayName = value;
				OnPropertyChanged();
			}
		}

		private const bool _showHistoryDefValue = true;
		private bool _showHistory = _showHistoryDefValue;
		/// <summary>
		/// Zobrazit historii (zapnuto pro MU, vypnuto pro JCU). Pro WebKredit.
		/// </summary>
		[DefaultValue(_showHistoryDefValue)]
		[ConfigSRDisplayName(nameof(ShowHistory), typeof(GlobalServicesIpsConfig))]
		[ConfigSRDescription(nameof(ShowHistory), typeof(GlobalServicesIpsConfig))]
		public bool ShowHistory
		{
			get { return _showHistory; }
			set
			{
				if (_showHistory == value)
				{
					return;
				}
				_showHistory = value;
				OnPropertyChanged();
			}
		}
	}
}