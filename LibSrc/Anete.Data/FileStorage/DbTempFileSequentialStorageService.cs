using Anete.Data.Interface.FileStorage;
using Anete.Log.Core;
using Anete.Log.Core.Log4NetProxy;
using System;
using System.Data;
using System.Data.SqlClient;
using System.IO;

namespace Anete.Data.FileStorage
{
	/// <summary>
	/// <PERSON><PERSON>zi sekven<PERSON>ne Stream do predem urcene tabulky.
	/// </summary>
	public class DbTempFileSequentialStorageService : IDbTempFileSequentialStorageService
	{
		#region private fields...
		private readonly static int _bufferSize = 8040 * 64;
		private static readonly ILogEx _log = LogManagerEx.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
		private static readonly ILogEx _nHibernateLog = LogManagerEx.GetLogger(NhLoggerNames.Sql);
		#endregion

		#region IDbTempFileSequentialStorageService members...
		public bool SaveFile(string folderName, string fileName, Stream stream, SqlTransaction transaction, Action<long> totalBytesSaved)
		{
			_log.DebugFormat($"SaveFile(tableName=[dbo].[File], streamSize={stream.Length}, fileName={fileName})");

			InsertFileInfo(folderName, fileName, transaction);
			UpdateSequentialData(fileName, stream, transaction, totalBytesSaved);

			return true;
		}

		public void DeleteFile(string folderName, string fileName, SqlTransaction transaction)
		{
			using (SqlCommand cmd = new SqlCommand())
			{
				cmd.CommandText = string.Format(@"DELETE FROM [dbo].[File] WHERE [folder] = '{0}' and [filename] = '{1}'", folderName, fileName);
				cmd.Transaction = transaction;
				cmd.Connection = transaction.Connection;

				_nHibernateLog.Debug(cmd.GetLogText());

				cmd.ExecuteNonQuery();
			}
		}
		#endregion

		#region private methods...
		/// <summary>
		/// Vytvori zaznam v tabulce s potrebnyma informacema o nahravanem souboru. Sloupec data se naplni hodnotou 0x.
		/// </summary>
		/// <param name="folderName"></param>
		/// <param name="fileName"></param>
		/// <param name="transaction"></param>
		private void InsertFileInfo(string folderName, string fileName, SqlTransaction transaction)
		{
			// nejprve musim do sloupce zapsat 0
			using (SqlCommand cmd = new SqlCommand())
			{
				cmd.CommandText = string.Format(@"INSERT INTO [dbo].[File]
([folder], [filename], [data])
VALUES('{0}','{1}',{2})", folderName, fileName, "0x");

				cmd.Transaction = transaction;
				cmd.Connection = transaction.Connection;

				_nHibernateLog.Debug(cmd.GetLogText());

				cmd.ExecuteNonQuery();
			}
		}

		/// <summary>
		/// Zacne vkladat sekvence stream do sloupce data.
		/// </summary>
		/// <param name="fileName"></param>
		/// <param name="stream"></param>
		/// <param name="transaction"></param>
		/// <param name="totalBytesSaved"></param>
		private void UpdateSequentialData(string fileName, Stream stream, SqlTransaction transaction, Action<long> totalBytesSaved)
		{
			byte[] buffer = new byte[_bufferSize];

			// pote jiz muzu provest zapis
			long readed;
			long position = 0;
			while (true)
			{
				readed = stream.Read(buffer, 0, buffer.Length);
				if (readed == 0)
				{
					break;
				}

				if (readed == buffer.Length)
				{
					// muzu zapsat jednoduse cely buffer
					WriteBuffer(transaction, fileName, position, buffer);
				}
				else
				{
					// musim vytvorit novy buffer se spravnou velikosti
					byte[] restOfBuffer = new byte[readed];
					Array.Copy(buffer, restOfBuffer, readed);
					WriteBuffer(transaction, fileName, position, restOfBuffer);
				}

				float percent = (float)(position + readed) / (float)stream.Length * 100;
				_log.TraceFormat("Written {0}B of {1}B. Total {2:f1}%", position + readed, stream.Length, percent);
				position += readed;

				totalBytesSaved?.Invoke(position);
			}
		}

		private static void WriteBuffer(SqlTransaction transaction, string fileName, long position, byte[] buffer)
		{
			using (SqlCommand cmd = new SqlCommand())
			{
				cmd.Transaction = transaction;
				cmd.Connection = transaction.Connection;
				// nastavenim posledniho parametru na NULL rikam, ze od pozice offset budou data vzdy vymazana
				cmd.CommandText = "UPDATE [dbo].[File] SET [data].WRITE(@buffer, @offset, NULL) WHERE filename = @fileName";

				var bufferParam = cmd.Parameters.Add("@buffer", SqlDbType.VarBinary, buffer.Length);
				var offsetParam = cmd.Parameters.Add("@offset", SqlDbType.BigInt);
				cmd.Parameters.Add("@fileName", SqlDbType.VarChar).Value = fileName;

				bufferParam.Value = buffer;
				offsetParam.Value = position;

				_nHibernateLog.Debug(cmd.GetLogText());

				// write chunk
				int affected = cmd.ExecuteNonQuery();
			}
		}
		#endregion
	}
}