using Anete.Common.Core.Business.InstallationParameters.Factories;
using Anete.Common.Core.Interface.Business.InstallationParameters.Factories;
using Anete.Common.Core.Interface.Business.InstallationParameters.Parameters;

namespace Anete.Common.Core.Business.InstallationParameters.Parameters.System.SystemActiveWebClients
{
	public class SystemActiveWebClientsFactory : InstallationParameterFactoryBase, ISystemParameterFactory
	{
		private readonly ISystemActiveWebClientsInstallationParameterFactory _factory;

		public SystemActiveWebClientsFactory(ISystemActiveWebClientsInstallationParameterFactory factory)
		{
			_factory = factory;
		}

		public ISystemInstallationParameter CreateFromSystem()
		{
			return _factory.CreateFromSystem();
		}
	}
}