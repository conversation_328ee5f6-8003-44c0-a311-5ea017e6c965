using System;
using System.Collections.Generic;
using System.Text;
using Anete.Config.Core.Attributes;
using System.ComponentModel;
using Anete.Config.Core.CFSystKonfig;
using Anete.Config.Core.Categories;
using Anete.Config.Core.Internals;
using Anete.Common.Core.Interface.Enums;

namespace Anete.Config.Configs.Core.PresPoint.Behaviour
{

	/// <summary>
	/// Nastaveni jidelnicku
	/// </summary>
	[PresPointBehaviourConfigDBKey("Menu")]
    [ConfigVersion("*******")]
    [ConfigClassSRDescription(typeof(PresPointBehaviourMenuConfig))]
    [DefaultConfigInstance(false)]
	[ApplicationCategory(ApplicationType.PresPoint)]
	public class PresPointBehaviourMenuConfig : ConfigBase
    {	
		private const MenuFor _menuForDefValue = MenuFor.Today;
        private MenuFor _menuFor = _menuForDefValue;
        /// <summary>
        /// Na kdy se bude zobrazvoat jidelnicek
        /// </summary>
        [DefaultValue(_menuForDefValue)]
        [ConfigSRDisplayName("MenuFor", typeof(PresPointBehaviourMenuConfig))]
        [ConfigSRDescriptionWithEnumDescription("MenuFor", typeof(MenuFor), typeof(PresPointBehaviourMenuConfig))]
        [MapEnumToIntPropertyToCFSystKonfigAttribute("PM_ZOBRAZJIDELNICEK", MapToCFSystKonfigOption.ReadFromCFSystKonfig)]
		[ApplicationCategory(ApplicationType.PresPoint)]
		public MenuFor MenuFor
        {
            get
            {
                return _menuFor;
            }
            set
            {
                if (_menuFor == value)
                {
                    return;
                }
                _menuFor = value;
                OnPropertyChanged("MenuFor");
            }
        }

        private const NotePosition DefValue = NotePosition.OnDetail;
        private NotePosition _notePosition  = DefValue;
        /// <summary>
        /// Zobrazeni poznamky
        /// </summary>
        [DefaultValue(DefValue)]
        [ConfigSRDisplayName("NotePosition", typeof(PresPointBehaviourMenuConfig))]
        [ConfigSRDescriptionWithEnumDescription("NotePosition", typeof(NotePosition), typeof(PresPointBehaviourMenuConfig))]
		[ApplicationCategory(ApplicationType.PresPoint)]
		public NotePosition NotePosition
        {
            get { return _notePosition; }
            set
            {
                if (_notePosition == value)
                {
                    return;
                }
                _notePosition = value;
                OnPropertyChanged("NotePosition");
            }
        }

		private const bool _allowAltMenuDefValue = false;
		private bool _allowAltMenu = _allowAltMenuDefValue;
		/// <summary>
		/// Povolit zobrazit alternativni jidelnicek?
		/// </summary>
		[DefaultValue(_allowAltMenuDefValue)]
		[ConfigSRDisplayName("AllowAltMenu", typeof(PresPointBehaviourMenuConfig))]
		[ConfigSRDescription("AllowAltMenu", typeof(PresPointBehaviourMenuConfig))]
		public bool AllowAltMenu
		{
			get { return _allowAltMenu; }
			set
			{
				if (_allowAltMenu == value)
				{
					return;
				}
				_allowAltMenu = value;
				OnPropertyChanged("AllowAltMenu");
			}
		}

        private const bool _showOrderedMealCountDefValue = false;
        private bool _showOrderedMealCount = _showOrderedMealCountDefValue;
        /// <summary>
        /// Bude se zobrazovat pocet jiz objednanych jidel? Viz operativa 14934.
        /// </summary>
        [DefaultValue(_showOrderedMealCountDefValue)]
        [ConfigSRDisplayName("ShowOrderedMealCount", typeof(PresPointBehaviourMenuConfig))]
        [ConfigSRDescription("ShowOrderedMealCount", typeof(PresPointBehaviourMenuConfig))]
        public bool ShowOrderedMealCount
        {
            get { return _showOrderedMealCount; }
            set
            {
                if (_showOrderedMealCount == value)
                {
                    return;
                }
                _showOrderedMealCount = value;
                OnPropertyChanged("ShowOrderedMealCount");
            }
        }
        
        private decimal _ordersMidnightOffset = 0;
        /// <summary>
        /// Posunuti pulnoci pro zobrazeni objednavek, udava se v hodinach. Viz operativa 14994.
        /// Pouziva se pro tisk stravenky. Umozni stravnikovi videt objednana jidla ze vcerejsiho dne a tim padem mit moznost pro ne vytisknout stravenku.
        /// </summary>
        [DefaultValue(typeof(decimal), "0")]
        [ConfigSRDisplayName("OrdersMidnightOffset", typeof(PresPointBehaviourMenuConfig))]
        [ConfigSRDescription("OrdersMidnightOffset", typeof(PresPointBehaviourMenuConfig))]
        public decimal OrdersMidnightOffset
        {
            get { return _ordersMidnightOffset; }
            set
            {
                if (_ordersMidnightOffset == value)
                {
                    return;
                }
                _ordersMidnightOffset = value;
                OnPropertyChanged("OrdersMidnightOffset");
            }
        }

        private const bool _hideDisabledMenuItemsDefValue = false;
        private bool _hideDisabledMenuItems = _hideDisabledMenuItemsDefValue;
        /// <summary>
        /// Budou se skryvat polozky jidelnicku, ktere nelze objednat? Viz op. 36643
        /// </summary>
        [DefaultValue(_hideDisabledMenuItemsDefValue)]
        [ConfigSRDisplayName("HideDisabledMenuItems", typeof(PresPointBehaviourMenuConfig))]
        [ConfigSRDescription("HideDisabledMenuItems", typeof(PresPointBehaviourMenuConfig))]
        public bool HideDisabledMenuItems
        {
            get { return _hideDisabledMenuItems; }
            set
            {
                if (_hideDisabledMenuItems == value)
                {
                    return;
                }
                _hideDisabledMenuItems = value;
                OnPropertyChanged();
            }
        }

        private const bool _hideDisabledCanteensDefValue = false;
        private bool _hideDisabledCanteens = _hideDisabledCanteensDefValue;
        /// <summary>
        /// Budou se skryvat vydejny, ke kterym nema stravnik pristup? Viz op. 36643
        /// </summary>
        [DefaultValue(_hideDisabledCanteensDefValue)]
        [ConfigSRDisplayName("HideDisabledCanteens", typeof(PresPointBehaviourMenuConfig))]
        [ConfigSRDescription("HideDisabledCanteens", typeof(PresPointBehaviourMenuConfig))]
        public bool HideDisabledCanteens
        {
            get { return _hideDisabledCanteens; }
            set
            {
                if (_hideDisabledCanteens == value)
                {
                    return;
                }
                _hideDisabledCanteens = value;
                OnPropertyChanged();
            }
        }

        private const bool _hideForeingCanteensDefValue = false;
        private bool _hideForeingCanteens = _hideForeingCanteensDefValue;
        /// <summary>
        /// Budou se skryvat vydejny, ktere nepatri danemu PM? To znamena ze prihlaseny uzivatel bude mit dostupnout vzdy jen jednu vydejnu. Viz. Op41179
        /// </summary>
        [DefaultValue(_hideForeingCanteensDefValue)]
        [ConfigSRDisplayName("HideForeignCanteens", typeof(PresPointBehaviourMenuConfig))]
        [ConfigSRDescription("HideForeignCanteens", typeof(PresPointBehaviourMenuConfig))]
        public bool HideForeignCanteens
        {
            get { return _hideForeingCanteens; }
            set
            {
                if (_hideForeingCanteens == value)
                {
                    return;
                }
                _hideForeingCanteens = value;
                OnPropertyChanged();
            }
        }


		private const bool _restrictExchangeToWorkplaceDefValue = false;
		private bool _restrictExchangeToWorkplace = _restrictExchangeToWorkplaceDefValue;
		/// <summary>
		/// Omezí burzu dle provozovny
		/// </summary>
		[DefaultValue(_restrictExchangeToWorkplaceDefValue)]
		[ConfigSRDisplayName("RestrictExchangeToWorkplace", typeof(PresPointBehaviourMenuConfig))]
		[ConfigSRDescription("RestrictExchangeToWorkplace", typeof(PresPointBehaviourMenuConfig))]
		public bool RestrictExchangeToWorkplace
		{
			get
			{
				return _restrictExchangeToWorkplace;
			}
			set
			{
				if (_restrictExchangeToWorkplace == value)
				{
					return;
				}
				_restrictExchangeToWorkplace = value;
				OnPropertyChanged("RestrictExchangeToWorkplace");
			}
		}

		private const bool _showDetailWithoutNoteDefaultValue = false;
        private bool _showDetailWithoutNote = _showDetailWithoutNoteDefaultValue;
        /// <summary>
        /// Budou se skryvat vydejny, ktere nepatri danemu PM? To znamena ze prihlaseny uzivatel bude mit dostupnout vzdy jen jednu vydejnu. Viz. Op41179
        /// </summary>
        [DefaultValue(_showDetailWithoutNoteDefaultValue)]
        [ConfigSRDisplayName("ShowDetailWithoutNote", typeof(PresPointBehaviourMenuConfig))]
        [ConfigSRDescription("ShowDetailWithoutNote", typeof(PresPointBehaviourMenuConfig))]
        public bool ShowDetailWithoutNote
        {
            get { return _showDetailWithoutNote; }
            set
            {
                if (_showDetailWithoutNote == value)
                {
                    return;
    }
                _showDetailWithoutNote = value;
                OnPropertyChanged();
}
        }
    }
}
