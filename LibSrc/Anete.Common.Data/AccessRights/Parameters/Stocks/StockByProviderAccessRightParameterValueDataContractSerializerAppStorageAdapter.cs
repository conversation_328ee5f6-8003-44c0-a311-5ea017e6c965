using Anete.Common.Data.Interface.Business.AccessRights.Services;
using Anete.Common.Data.Interface.Entities;
using System;
using System.Linq;

namespace Anete.Common.Data.AccessRights.Parameters.Stocks
{
	public class StockByProviderAccessRightParameterValueDataContractSerializerAppStorageAdapter : AccessRightParameterValueDataContractSerializerAppStorageAdapter<Stock, StockByProviderAccessRightParameterValueSerializableData>
	{
		public StockByProviderAccessRightParameterValueDataContractSerializerAppStorageAdapter(ICustomAccessRightParameterValueFactoryAggregator<Stock> factories)
			: base(factories)
		{

		}

	}
}