using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Xml;
using Anete.Common.Core.Interface.AppServices.AppStorage;
using Anete.Common.Core.Interface.AppServices.AppStorage.Adapters;
using Anete.Log4Net.Core;

namespace Anete.Common.Core.AppServices.AppStorage
{

	/// <summary>
	/// Zapisovac dat pro AppStorage
	/// </summary>
	public class AppStorageXmlTextWriter : AppStorageXmlTextReaderWriterBase, IAppStorageWriter
	{

		#region private static fields...
		private static readonly ILogEx _log = LogManagerEx.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
		#endregion

		#region constructors...
		/// <summary>
		/// Inicializace nove instance
		/// </summary>
		/// <param name="adapterFactory">Factory pro adaptery</param>
		/// <param name="stream">Stream, do ktereho se zapisuje nebo cte</param>
		/// <param name="storageVersion">Verze uloziste</param>
		public AppStorageXmlTextWriter(IAppStorageAdapterFactory adapterFactory, Stream stream, Version storageVersion)
			: base(adapterFactory, stream, storageVersion)
		{

		}
		#endregion

		#region public methods...
		/// <summary>
		/// Ulozi data do streamu
		/// </summary>
		/// <param name="data">Data k ulozeni</param>
		/// <param name="dataVersion">Verze dat</param>
		public void Write(object data, Version dataVersion)
		{
			// nemuzu volat dispose XmlTextWriteru, protoze by uzavrel i stream
			XmlTextWriter xmlWriter = new XmlTextWriter(Stream, Encoding.Unicode);

			xmlWriter.Formatting = Formatting.Indented;
			xmlWriter.WriteStartDocument();
			xmlWriter.WriteStartElement(NodeNames.PersistentData);
			xmlWriter.WriteAttributeString(AttributeNames.StorageVersion, StorageVersion.ToString());
			xmlWriter.WriteAttributeString(AttributeNames.Description, "AppStorage persistent data");
			SaveComponentData(data, xmlWriter, dataVersion);
			xmlWriter.WriteEndElement();
			xmlWriter.WriteEndDocument();
			xmlWriter.Flush();
		}
		#endregion

		#region private methods...
		/// <summary>
		/// Ulozi data pro komponentu component
		/// </summary>
		/// <param name="data">The data.</param>
		/// <param name="xmlWriter">The text writer.</param>
		/// <param name="dataVersion">The data version.</param>
		private void SaveComponentData(object data, XmlTextWriter xmlWriter, Version dataVersion)
		{
			IAppStorageAdapter adapter = AdapterFactory.CreateAdapter(data.GetType());
            _log.TraceFormat("Saving data for componentType='{0}', adapterType'{1}'", data.GetType().FullName,
			  adapter.GetType().FullName);
			xmlWriter.WriteStartElement(NodeNames.Data);
			// tento atribut se pouziva pro zkonstruovani puvodniho typu
			xmlWriter.WriteAttributeString(AttributeNames.DataType, data.GetType().FullName);
			xmlWriter.WriteAttributeString(AttributeNames.DataAssembly, data.GetType().Assembly.FullName);
			xmlWriter.WriteAttributeString(AttributeNames.DataVersion, dataVersion.ToString());
			xmlWriter.WriteAttributeString(AttributeNames.AdapterType, adapter.GetType().FullName);
			xmlWriter.WriteAttributeString(AttributeNames.AdapterVersion, adapter.GetVersion().ToString());

			using (StringWriter writer = new StringWriter())
			{
				adapter.Write(data, writer);
				xmlWriter.WriteRaw(writer.ToString());
			}

			xmlWriter.WriteEndElement();
		}
		#endregion
	}
}
