using System;
using System.Data;
using System.IO;
using Anete.Common.Core.AppServices.AppStorage.Adapters;

namespace Anete.Common.Data.AppServices.AppStorage.Adapters
{
    /// <summary>
    /// Adapter, ktery zapisuje dataset do AppStorage
    /// </summary>
    public class DataSetAppStorageAdapter: AppStorageInstanceCreatorAdapterBase<DataSet>
    {

        #region protected overrides...
		/// <summary>
		/// Nacteni objektu z readeru
		/// </summary>
		/// <param name="reader">Reader</param>
		/// <returns>Nacteny objekt</returns>
		protected override DataSet ReadInt(TextReader reader)
		{
			DataSet result = CreateInstance();
            result.ReadXml(reader);
			// s timto si nejsem jisty, ale nechci vracet vsechny radky ve stavu Added
			result.AcceptChanges();
            return result;
		}

        /// <summary>
        /// Zapis objektu do writer
        /// </summary>
        /// <param name="data">Data k zapsano</param>
        /// <param name="writer">Writer, do ktereho se zapisuje</param>
        protected override void WriteInt(DataSet data, TextWriter writer)
        {
            data.WriteXml(writer);
        }
        #endregion
     
    }
}
