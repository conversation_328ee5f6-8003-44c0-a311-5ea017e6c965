using System;
using System.Collections;
using System.Linq;
using System.Collections.Generic;

namespace Anete.Utils.Collections
{
    /// <summary>
    /// Pravidlo pro vlozeni polozku pred nekterou z polozek uvedenou v seznamu.
    /// </summary>
    public abstract class IntertItemBeforeAnotherItemsBase : InsertItemToCollectionRuleBase
    {
        /// <summary>
        /// Initializes a new instance of the IntertItemBeforeAnotherItemsBase class.
        /// </summary>
        public IntertItemBeforeAnotherItemsBase(params string[] nextItemsNames)
        {
            NextItemsNames = nextItemsNames;
        }

        /// <summary>
        /// Nazvy polozek, pred kterymi ma byt tato polozka umistena.
        /// Pouziva se v pripade, ze nektere polozka neni nalezena. Pote se pokracuje s dalsi v poradi.
        /// </summary>
        public IEnumerable<string> NextItemsNames { get; private set; }

        /// <summary>
        /// R<PERSON>, zda staci, aby polozka byla proste jen pred ostatnima nebo presne pred ostatnima (index mensi pouze o 1).
        /// </summary>
        public bool ExactBefore { get; set; }

        protected override int GetItemIndexInt(ICollection collection, object item)
        {
            object nextItem = null;
            int nextItemIndex = 0;
            foreach (string nextItemName in NextItemsNames)
            {
                // jdu najit v kolekci patricny item
                nextItemIndex = 0;
                foreach (object itemInCollection in collection)
                {
                    if (String.Compare(GetItemName(itemInCollection), nextItemName, false) == 0)
                    {
                        nextItem = itemInCollection;
						break;
                    }
                    nextItemIndex++;
                }
            }

            if (nextItem == null)
            {
                return 0;
                // vyjimku nema smysl vyvolavat. Vim, pokud nemam polozku kde zaradit, patri nejpravdepodobneji na zacatek kolekce.
                //throw new ArgumentException(string.Format("V kolekci nebyla nalezena zadna polozka s nekterym z jmen {0}", NextItemsNames.ToCommaSpaceDelimitedString()));
            }

            int actualIndex = collection.Cast<object>().ToList().IndexOf(item);
            if (ExactBefore)
            {
                return System.Math.Max(0, nextItemIndex - 1);
            }
            else
            {
                if (actualIndex < nextItemIndex)
                {
                    return actualIndex;
                }
                else
                {
                    return System.Math.Max(0, nextItemIndex - 1);
                }
            }
        }

        /// <summary>
        /// Metoda pro zjisteni nazvu item
        /// </summary>
        protected abstract string GetItemName(object item);
    }
}
