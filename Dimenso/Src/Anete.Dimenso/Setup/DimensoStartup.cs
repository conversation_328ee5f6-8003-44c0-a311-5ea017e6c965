using Anete.ApiServices.DependencyInjection;
using Anete.Config.Configs.Core.Global.Services;
using Anete.Config.Configs.Core.Shared;
using Anete.Config.Core;
using Anete.WebShell.Server.Core.Infrastructure;
using Anete.WebShell.Server.Kredit.Infrastructure;
using Anete.WebShell.Server.Kredit.Middlewares;
using Microsoft.AspNetCore.Authentication.Certificate;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.AspNetCore.Server.Kestrel.Https;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using System;
using System.Linq;
using System.Security.Claims;
using System.Security.Cryptography.X509Certificates;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Anete.Dimenso.Setup
{
	public class DimensoStartup : WebShellStartupKreditBase<DimensoApplication>
	{
		public static void Main(string[] args)
		{
			WebShellHost.Run<DimensoStartup, DimensoApplication>(args);
		}

		public DimensoStartup(IDependencyContainer container) : base(container) { }

		protected override IWebServerConfig GetWebServerConfig(IServiceProvider serviceProvider)
		{
			return serviceProvider.GetRequiredService<IOptions<GlobalServicesDimensoConnectionConfig>>().Value;
		}

		public override void ConfigureServices(IServiceCollection services)
		{
			base.ConfigureServices(services);

			services.AddControllersWithViews(x => x.Filters.Add(new ResponseCacheAttribute { NoStore = true }))
				.AddJsonOptions(x =>
				{
					x.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
					x.JsonSerializerOptions.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
				})
				.AddControllersAsServices();

			services.AddMemoryCache();
			services.AddSwaggerGen();

			services.AddAuthentication()
				.AddCertificate("DimensoOutbound", x => ConfigureCertificateOptions(x, y => y.OutboundBindingTypeSettings))
				.AddCertificate("DimensoInbound", x => ConfigureCertificateOptions(x, y => y.InboundBindingTypeSettings));

			services.Configure<KestrelServerOptions>(x =>
			{
				x.ConfigureHttpsDefaults(y => y.ClientCertificateMode = ClientCertificateMode.RequireCertificate);
			});
		}

		private static void ConfigureCertificateOptions(CertificateAuthenticationOptions x, Func<GlobalServicesDimensoConnectionConfig, NetTcpSecureUserAuthBindingTypeSettings> getter)
		{
			x.AllowedCertificateTypes = CertificateTypes.All;
			x.RevocationMode = X509RevocationMode.NoCheck;
			x.Events = new CertificateAuthenticationEvents
			{
				OnCertificateValidated = context =>
				{
					// Check if certificate has Organizational Unit equal to "ES"
					var organizationalUnit = GetOrganizationalUnitFromCertificate(context.ClientCertificate);
					if (organizationalUnit != "ES")
					{
						context.Fail($"Invalid certificate: Organizational Unit must be 'ES', but was '{organizationalUnit}'");
						return Task.CompletedTask;
					}

					var configManager = context.HttpContext.RequestServices.GetRequiredService<IConfigManager>();
					var config = configManager.GetConfig<GlobalServicesDimensoConnectionConfig>();
					var settings = getter(config);

					using var store = new X509Store(settings.StoreName, StoreLocation.LocalMachine);
					store.Open(OpenFlags.ReadOnly);

					var certificates = store.Certificates.Find(settings.FindType, settings.FindValue, false);
					if (certificates.Count == 0)
					{
						context.Fail("Certificate not found");
						return Task.CompletedTask;
					}

					using var chain = new X509Chain();
					chain.ChainPolicy.RevocationMode = X509RevocationMode.NoCheck;
					if (!chain.Build(context.ClientCertificate))
					{
						context.Fail($"Invalid chain: {context.ClientCertificate.ToString(true)}");
						return Task.CompletedTask;
					}

					foreach (var element in chain.ChainElements)
					{
						if (element.Certificate.Thumbprint == certificates[0].Thumbprint)
						{
							context.Principal = new ClaimsPrincipal(new ClaimsIdentity("Certificate"));
							context.Success();
							return Task.CompletedTask;
						}
					}

					context.Fail($"Invalid certificate: {context.ClientCertificate.ToString(true)}");
					return Task.CompletedTask;
				}
			};
		}

		private static string GetOrganizationalUnitFromCertificate(X509Certificate2 certificate)
		{
			if (certificate?.SubjectName == null)
				return null;

			// Use the proper API to enumerate relative distinguished names
			foreach (var rdn in certificate.SubjectName.EnumerateRelativeDistinguishedNames())
			{
				foreach (var attribute in rdn)
				{
					// Check for Organizational Unit OID (********)
					if (attribute.Oid?.Value == "********")
					{
						return attribute.Value;
					}
				}
			}

			return null;
		}

		protected override void ConfigureDebug(IApplicationBuilder app) { }

		public override void Configure(IApplicationBuilder app)
		{
			app.UseSwagger();
			app.UseSwaggerUI();

			base.Configure(app);

			app.UseMiddleware<KreditExceptionMiddleware>();

			app.UseEndpoints(x => x.MapControllers());
		}
	}
}