using System;
using System.Collections.Generic;
using System.Text;
using System.ComponentModel;
using Anete.Config.Core.Attributes;
using Anete.Utils.ComponentModel.Attributes;
using Anete.Config.Configs.Core.Global.Hw;

namespace Anete.Config.Configs.Core.CashDesk
{

    /// <summary>
    /// Settings pro Bowa Paegas FM 6 v.2
    /// </summary>
    public class BowaPaegasFm6v2FiscalModuleSettings : FiscalModuleSettingsBase
    {

        private BowaPaegasFm6v2SerialDeviceSettings _serialDeviceSettings = new BowaPaegasFm6v2SerialDeviceSettings();
        /// <summary>
        /// Nastavení seriového zarizeni
        /// </summary>
        [NestedClass]
        [Inherit(false)]
        [ConfigSRDisplayName("SerialDeviceSettings", typeof(BowaPaegasFm6v2SerialDeviceSettings))]
        [ConfigSRDescription("SerialDeviceSettings", typeof(BowaPaegasFm6v2SerialDeviceSettings))]
        public BowaPaegasFm6v2SerialDeviceSettings SerialDeviceSettings
        {
            get
            {
                return _serialDeviceSettings;
            }
            set
            {
                if (_serialDeviceSettings == value)
                {
                    return;
                }
                _serialDeviceSettings = value;
                OnPropertyChanged("SerialDeviceSettings");
            }
        }

    }
}
