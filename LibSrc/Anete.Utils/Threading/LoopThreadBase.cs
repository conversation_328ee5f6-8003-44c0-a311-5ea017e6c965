using Anete.Log.Core.Log4NetProxy;
using System;
using Anete.Utils.Extensions;
using System.Threading.Tasks;
using System.Threading;

namespace Anete.Utils.Threading
{
	/// <summary>
	/// V<PERSON>no, jehoz kod neustale bezi ve smycce a ma schopnost se v pripade chyby restartovat.
	/// </summary>
	public abstract class LoopThreadBase : ThreadBase
	{
		#region constructors...
		/// <summary>
		/// Initializes a new instance of the LoopThreadBase class.
		/// </summary>
		public LoopThreadBase()
			: this(TimeSpan.FromSeconds(60), TimeSpan.FromMinutes(1))
		{

		}

		/// <summary>
		/// Initializes a new instance of the LoopThreadBase class.
		/// </summary>
		public LoopThreadBase(TimeSpan loopDelay, TimeSpan restartDelay)
		{
			_loopDelay = loopDelay;
			_restartDelay = restartDelay;
		}
		#endregion

		#region private fields...
		private static readonly ILogEx _log = LogManagerEx.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
		#endregion

		#region public properties...
		private bool _autoRestartOnNonSystemException = true;
		/// <summary>
		/// Bude se vlakno automaticky restartovat pri vzniku nesystemove vyjimky?
		/// </summary>
		public bool AutoRestartOnNonSystemException
		{
			get { return _autoRestartOnNonSystemException; }
			set
			{
				CheckPropertyCanBeSet(this.GetPropertyName(p => p.AutoRestartOnNonSystemException));
				_autoRestartOnNonSystemException = value;
			}
		}

		private TimeSpan _restartDelay;
		/// <summary>
		/// Doba, po kterou se ceka na restartovani vlakno po vzniku nesystemove vyjimky.
		/// </summary>
		public TimeSpan RestartDelay
		{
			get { return _restartDelay; }
			set
			{
				CheckPropertyCanBeSet(this.GetPropertyName(p => p.RestartDelay));
				_restartDelay = value;
			}
		}

		private readonly object _loopDelaySync = new object();
		private TimeSpan _loopDelay = TimeSpan.FromMinutes(5);
		/// <summary>
		/// Doba, po kterou se ceka, nez se zopakuje smycka. Lze prenastavit i po spusteni threadu.
		/// </summary>
		public TimeSpan LoopDelay
		{
			get
			{
				lock (_loopDelaySync)
				{
					return _loopDelay;
				}
			}
			set
			{
				lock (_loopDelaySync)
				{
					_loopDelay = value;
				}
			}
		}
		#endregion

		#region protected overrides...
		/// <summary>
		/// Vykonny kod vlakna
		/// </summary>
		/// <param name="startParam"></param>
		protected sealed override void DoWorkInt(object startParam)
		{
			while (!Terminated && AutoRestartOnNonSystemException)
			{
				try
				{
					LoopWork(startParam);
					_log.TraceFormat("Waiting for {0} sec", LoopDelay.TotalSeconds);
					TerminateableWait(LoopDelay);
				}
				catch (Exception ex) when (ExcUtils.IsCatchableExceptionType(ex))
				{
					_log.Error("Unexpected exception", ex);
					ProcessUnexpectedException(ex);

					if (!AutoRestartOnNonSystemException)
					{
						throw;
					}

					_log.InfoFormat("Thread will be restarted after {0} sec", RestartDelay.TotalSeconds);
					TerminateableWait(RestartDelay);
				}
			}
		}
		#endregion

		#region protected virtual methods...
		/// <summary>
		/// Hlavni metoda vlakna, ktera stale bezi ve smycce.
		/// </summary>
		/// <param name="startParam"></param>
		protected abstract void LoopWork(object startParam);

		/// <summary>
		/// Pripadne zpracovani pro neosetrenou vyjimku u potomku
		/// </summary>
		/// <param name="ex">Neocekavana vyjimka</param>
		protected virtual void ProcessUnexpectedException(Exception ex)
		{

		}
		#endregion
	}
}
