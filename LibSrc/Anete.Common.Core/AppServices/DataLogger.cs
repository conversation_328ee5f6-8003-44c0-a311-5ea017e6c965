using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Anete.Utils;
using Anete.Common.Core.Interface.AppServices;
using log4net.Core;
using Microsoft.Extensions.Logging;

namespace Anete.Common.Core.AppServices
{
	/// <summary>
	/// Implementace IDataLogger
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <remarks>
	/// Pozor: Nazev loggeru v .log4net.config musi byt stejny jako nazev datoveho typu T, ktery se loguje. Jinak se nezapisuje,
	/// zadne hlaseni se nevytvori.
	/// </remarks>
	public class DataLogger<T> : IDataLogger<T>
	{

		#region private fields...
		/// <summary>
		/// Citac, aby se BeginTrans mohlo volat opakovane
		/// </summary>
		private int _transCount;

		/// <summary>
		/// Sem se zapisuji data pri transakci
		/// </summary>
		private readonly Queue<LogEntry> _transQueue = new Queue<LogEntry>();

		/// <summary>
		/// Zapisovac logu
		/// </summary>
		private readonly IDataLoggerWriter<T> _logWriter;

		private readonly object _transQueueLock = new object();
		private readonly object _transLock = new object();
		#endregion

		#region constructors...
		/// <summary>
		/// Inicializace nove instance
		/// </summary>
		/// <param name="logWriter">The log writer.</param>
		public DataLogger(IDataLoggerWriter<T> logWriter)
		{
			Guard.ArgumentNotNull(logWriter, "logWriter");

			_logWriter = logWriter;
		}
		#endregion

		#region inner classes...
		/// <summary>
		/// Zaznam pro uchovavani ve fronte
		/// </summary>
		private class LogEntry
		{
			public string Message { get; set; }
			public LogLevel Level { get; set; }
			public T Data { get; set; }
			public Exception Ex { get; set; }
		}
		#endregion

		#region properties...
		/// <summary>
		/// Vraci true, pokud je transakce aktivni
		/// </summary>
		protected bool IsTransActive
		{
			get
			{
				lock (_transLock)
				{
					return _transCount > 0;
				}
			}
		}
		#endregion

		#region IDataLogger<T> members...
		/// <summary>
		/// Zacatek transakce
		/// </summary>
		public void BeginTrans()
		{
			lock (_transLock)
			{
				_transCount++;
			}
		}

		/// <summary>
		/// Potvrzeni transakce - zapis na disk
		/// </summary>
		public void CommitTrans()
		{
			lock (_transLock)
			{
				if (_transCount == 0)
				{
					throw new InvalidOperationException("Transakce nen� aktivn�. Nen� mo�no volat CommitTrans()");
				}

				_transCount--;

				if (_transCount == 0)
				{
					WriteTransData();
				}
			}
		}

		/// <summary>
		/// Rollback - zruseni vsech polozek, ktere byly pridany 
		/// </summary>
		public void RollbackTrans()
		{
			lock (_transLock)
			{
				// Jenomze kdyz mam zanorene transakce, RollbackTrans() zavolany uprostred muze byt
				// v konfliktu s CommitTrans(). Zatim nedoreseno, nepouzivam vice urovni zanoreni
				_transCount = 0;

				lock (_transQueueLock)
				{
					_transQueue.Clear();
				}
			}
		}

		/// <summary>
		/// Zapis polozky
		/// </summary>
		/// <param name="message">The message.</param>
		/// <param name="level">The level.</param>
		/// <param name="data">The data.</param>
		/// <param name="args">The args.</param>
		public void LogFormat(string message, LogLevel level, T data, params object[] args)
		{
			Log(string.Format(message, args), level, data, null);
		}

		/// <summary>
		/// Zapis polozky
		/// </summary>
		/// <param name="message">The message.</param>
		/// <param name="level">The level.</param>
		/// <param name="data">The data.</param>
		public void Log(string message, LogLevel level, T data, Exception ex)
		{
			if (IsTransActive)
			{
				lock (_transQueueLock)
				{
					_transQueue.Enqueue(new LogEntry() { Message = message, Level = level, Data = data, Ex = ex });
				}
			}
			else
			{
				_logWriter.Write(message, level, data, ex);
			}
		}
		#endregion

		#region private methods...
		/// <summary>
		/// Zapsani dat nastradanych v transakci
		/// </summary>
		private void WriteTransData()
		{
			lock (_transQueueLock)
			{
				while (_transQueue.Count > 0)
				{
					LogEntry entry = _transQueue.Dequeue();
					_logWriter.Write(entry.Message, entry.Level, entry.Data);
				}
			}
		}
		#endregion
	}
}
