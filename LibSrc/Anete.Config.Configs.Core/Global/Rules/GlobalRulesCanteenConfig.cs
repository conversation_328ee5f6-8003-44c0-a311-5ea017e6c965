using System;
using System.Collections.Generic;
using System.Text;
using Anete.Config.Core.Categories;
using Anete.Config.Core.Attributes;
using System.ComponentModel;
using Anete.Config.Core.CFSystKonfig;
using Anete.Config.Core.Internals;
using Anete.Common.Core.Interface.Enums;

namespace Anete.Config.Configs.Core.Global.Rules
{
    /// <summary>
    /// Konfigurace vydejny
    /// </summary>
    [GlobalRulesConfigDBKey("Canteen")]
    [ConfigVersion("*******")]
    [ConfigClassSRDescription(typeof(GlobalRulesCanteenConfig))]
    [DefaultConfigInstance(false)]
	[ApplicationCategory(ApplicationType.PresPoint)]
	public class GlobalRulesCanteenConfig : ConfigBase
    {
        private const CanteenSelectionLogic _canteenSelectionLogicDefValue = CanteenSelectionLogic.None;
        private CanteenSelectionLogic _canteenSelectionLogic = _canteenSelectionLogicDefValue;
        /// <summary>
        /// Logika vyberu impliticni vydejny pro stravnika. CanteenSelectionLogic je bitove mapovany enum, jez se mapuje
        /// na 3 hodnoty CFSystKonfig. Kazdy bit se mapuje na prislusnou hodnotu.
        /// </summary>
        [DefaultValue(typeof(CanteenSelectionLogic), "None")]
        [ConfigSRDisplayName("CanteenSelectionLogic", typeof(GlobalRulesCanteenConfig))]
        [ConfigSRDescription("CanteenSelectionLogic", typeof(GlobalRulesCanteenConfig))]
        [MapCanteenSelectionLogicMapToCFSystKonfig("vydejnapodleskupiny", MapToCFSystKonfigOption.ReadFromCFSystKonfig, CanteenSelectionLogic.Group)]
        [MapCanteenSelectionLogicMapToCFSystKonfig("vydejnapodlestrav", MapToCFSystKonfigOption.ReadFromCFSystKonfig, CanteenSelectionLogic.Client)]
        [MapCanteenSelectionLogicMapToCFSystKonfig("vydejnapodlestred", MapToCFSystKonfigOption.ReadFromCFSystKonfig, CanteenSelectionLogic.Resort)]
        public CanteenSelectionLogic CanteenSelectionLogic
        {
            get
            {
                return _canteenSelectionLogic;
            }
            set
            {
                if (_canteenSelectionLogic == value)
                {
                    return;
                }
                _canteenSelectionLogic = value;
                OnPropertyChanged("CanteenSelectionLogic");
            }
        }
    }
}
