<?xml version="1.0" encoding="utf-8"?>
<EntityDeveloper Version="6.10.1145.0">
  <ModelSettings xmlns:ed="http://devart.com/schemas/EntityDeveloper/1.0" ConnectionStringInAppConfig="False" DetectTPTInheritance="True" DefaultUnicode="True" IncludeForeignKeysInModel="true">
    <Connection ConnectionString="Data Source=ovswdb;Initial Catalog=Sklady8;Integrated Security=False;Persist Security Info=True;User ID=sa;Password=*****" Provider="System.Data.SqlClient" />
    <Generation>
      <GeneratedFiles>
        <File Name="Sklady8.Sklad.cs" />
        <File Name="Sklady8.ProvozniJednotka.cs" />
        <File Name="Sklady8.Kredit.cs" />
        <File Name="Sklady8.KRE_Uzivatel.cs" />
        <File Name="Sklady8.SkladovaPoloha.cs" />
        <File Name="Sklady8.Stredisko.cs" />
        <File Name="Sklady8.KRE_CFSazbyDph.cs" />
        <File Name="Sklady8.KRE_SCC_Alergeny.cs" />
        <File Name="Sklady8.KRE_SCC_AlergenyPopis.cs" />
        <File Name="Sklady8.KRE_SkupinyDphFull.cs" />
        <File Name="Sklady8.Sklad.FluentMapping.cs" />
        <File Name="Sklady8.ProvozniJednotka.FluentMapping.cs" />
        <File Name="Sklady8.Kredit.FluentMapping.cs" />
        <File Name="Sklady8.KRE_Uzivatel.FluentMapping.cs" />
        <File Name="Sklady8.SkladovaPoloha.FluentMapping.cs" />
        <File Name="Sklady8.Stredisko.FluentMapping.cs" />
        <File Name="Sklady8.KRE_CFSazbyDph.FluentMapping.cs" />
        <File Name="Sklady8.KRE_SCC_Alergeny.FluentMapping.cs" />
        <File Name="Sklady8.KRE_SCC_AlergenyPopis.FluentMapping.cs" />
        <File Name="Sklady8.KRE_SkupinyDphFull.FluentMapping.cs" />
      </GeneratedFiles>
    </Generation>
    <generator class="assigned" />
    <DatabaseFirstNamingRules>
      <EntitySet PluralizationName="Pluralize" />
      <Class UseSchemaAsPrefix="False" PluralizeCollectionNavigationPropertyName="True" AddConstraintColumnsToNavigationPropertyName="False" RemoveUnderscores="True" AddUnderscores="False" RemoveInvalidCharacters="True" CodeCase="FirstLetterUppercase" AddPrefix="" AddSuffix="" RemovePrefixes="" RemoveSuffixes="" PluralizationName="Singularize" />
      <Property RemoveUnderscores="True" AddUnderscores="False" RemoveInvalidCharacters="True" CodeCase="FirstLetterUppercase" AddPrefix="" AddSuffix="" RemovePrefixes="" RemoveSuffixes="" PluralizationName="Unchanged" />
    </DatabaseFirstNamingRules>
    <ModelFirst StorageSynchronizationEnabled="False" TargetSchema="dbo" TargetProviderName="System.Data.SqlClient">
      <TargetServer Server="SQL Server" ServerVersion="2019" />
      <ModelFirstNamingRules>
        <Table RemoveUnderscores="False" AddUnderscores="False" RemoveInvalidCharacters="True" CodeCase="Unchanged" AddPrefix="" AddSuffix="" RemovePrefixes="" RemoveSuffixes="" PluralizationName="Unchanged" />
        <Column RemoveUnderscores="False" AddUnderscores="False" RemoveInvalidCharacters="True" CodeCase="Unchanged" AddPrefix="" AddSuffix="" RemovePrefixes="" RemoveSuffixes="" PluralizationName="Unchanged" />
      </ModelFirstNamingRules>
    </ModelFirst>
    <Diagrams>
      <Diagram Name="Ciselniky" DefaultDiagram="True" />
      <Diagram Name="Kredit replikace" />
      <Diagram Name="Kredit kopie" />
    </Diagrams>
    <Templates>
      <Template Name="NHibernate" Description="Use this template to generate classes and xml mappings for an NHibernate model." Enabled="True" PredefinedTemplateName="NHibernate C#">
        <ed:Property Name="ValidationFramework" Type="EntityDeveloper.TemplateEngine.ValidationFramework, EntityDeveloper.Common, Version=6.10.1145.0, Culture=neutral, PublicKeyToken=09af7300eec23701">
          <ValidationFramework>None</ValidationFramework>
        </ed:Property>
        <ed:Property Name="ValidationErrorMessages" Type="EntityDeveloper.TemplateEngine.ValidationErrorMessages, EntityDeveloper.Common, Version=6.10.1145.0, Culture=neutral, PublicKeyToken=09af7300eec23701">
          <ValidationErrorMessages xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" />
        </ed:Property>
        <ed:Property Name="FilePerClass" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>true</boolean>
        </ed:Property>
        <ed:Property Name="ModelNameAsFilesPrefix" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>true</boolean>
        </ed:Property>
        <ed:Property Name="EntitiesOutput" Type="EntityDeveloper.TemplateEngine.OutputInfo, EntityDeveloper.Common, Version=6.10.1145.0, Culture=neutral, PublicKeyToken=09af7300eec23701">
          <OutputInfo xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
            <Project />
            <ProjectFolder />
            <DestinationFolder />
          </OutputInfo>
        </ed:Property>
        <ed:Property Name="GeneratePartialClass" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>false</boolean>
        </ed:Property>
        <ed:Property Name="HeaderTimestampVersionControlTag" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <string d6p1:nil="true" xmlns:d6p1="http://www.w3.org/2001/XMLSchema-instance" />
        </ed:Property>
        <ed:Property Name="NHibernateV3Compatible" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>false</boolean>
        </ed:Property>
        <ed:Property Name="PropertyChangeNotifiers" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>false</boolean>
        </ed:Property>
        <ed:Property Name="PropertyChangePartialMethods" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>false</boolean>
        </ed:Property>
        <ed:Property Name="ImplementValidatable" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>false</boolean>
        </ed:Property>
        <ed:Property Name="ImplementEquals" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>false</boolean>
        </ed:Property>
        <ed:Property Name="ImplementCloneable" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>false</boolean>
        </ed:Property>
        <ed:Property Name="GenerateDataContracts" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>false</boolean>
        </ed:Property>
        <ed:Property Name="GenerateDummyComments" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>false</boolean>
        </ed:Property>
        <ed:Property Name="GenerateSerializableAttributes" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>false</boolean>
        </ed:Property>
        <ed:Property Name="PrimitiveDefaultValueGeneration" Type="EntityDeveloper.DefaultValueBehavior, EntityDeveloper.Orm.Common, Version=6.10.1145.0, Culture=neutral, PublicKeyToken=09af7300eec23701">
          <DefaultValueBehavior>Literal</DefaultValueBehavior>
        </ed:Property>
        <ed:Property Name="NullableReferenceTypes" Type="EntityDeveloper.ReferenceTypeNullability, EntityDeveloper.Orm.Common, Version=6.10.1145.0, Culture=neutral, PublicKeyToken=09af7300eec23701">
          <ReferenceTypeNullability>Default</ReferenceTypeNullability>
        </ed:Property>
        <ed:Property Name="XmlMappingOutput" Type="EntityDeveloper.TemplateEngine.OutputInfo, EntityDeveloper.Common, Version=6.10.1145.0, Culture=neutral, PublicKeyToken=09af7300eec23701">
          <OutputInfo xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
            <Project />
            <ProjectFolder />
            <DestinationFolder />
          </OutputInfo>
        </ed:Property>
        <ed:Property Name="XmlMappingAction" Type="EntityDeveloper.MetadataArtifactProcessing, EntityDeveloper.Orm.Common, Version=6.10.1145.0, Culture=neutral, PublicKeyToken=09af7300eec23701">
          <MetadataArtifactProcessing>DoNotGenerateMappingFiles</MetadataArtifactProcessing>
        </ed:Property>
        <ed:Property Name="XmlMappingFilePerClass" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>true</boolean>
        </ed:Property>
      </Template>
      <Template Name="Fluent NHibernate" Description="Use this template to generate a Fluent NHibernate Mapping classes." Enabled="True" PredefinedTemplateName="NHibernate FluentMapping C#">
        <ed:Property Name="FilePerClass" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>true</boolean>
        </ed:Property>
        <ed:Property Name="GeneratePartialClass" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>false</boolean>
        </ed:Property>
        <ed:Property Name="ModelNameAsFilesPrefix" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>true</boolean>
        </ed:Property>
        <ed:Property Name="HeaderTimestampVersionControlTag" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <string d6p1:nil="true" xmlns:d6p1="http://www.w3.org/2001/XMLSchema-instance" />
        </ed:Property>
        <ed:Property Name="Output" Type="EntityDeveloper.TemplateEngine.OutputInfo, EntityDeveloper.Common, Version=6.10.1145.0, Culture=neutral, PublicKeyToken=09af7300eec23701">
          <OutputInfo xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
            <Project />
            <ProjectFolder />
            <DestinationFolder />
          </OutputInfo>
        </ed:Property>
      </Template>
    </Templates>
    <AttributeAssemblies />
    <Configuration SeparateConfigFile="False" />
    <UpdateFromDatabaseExcludes>
      <Exclude Name="id_dph" Table="dbo.KRE_CFSazbyDPH" Type="Column" />
      <Exclude Name="popis" Table="dbo.KRE_CFSazbyDPH" Type="Column" />
      <Exclude Name="IdKredit" Table="dbo.Kredit" Type="Column" />
      <Exclude Name="Server" Table="dbo.Kredit" Type="Column" />
      <Exclude Name="Databaze" Table="dbo.Kredit" Type="Column" />
      <Exclude Name="PK_Kredit" Table="dbo.Kredit" Type="Constraint" />
      <Exclude Name="IdProvozniJednotka" Table="dbo.ProvozniJednotka" Type="Column" />
      <Exclude Name="Nazev" Table="dbo.ProvozniJednotka" Type="Column" />
      <Exclude Name="IdKredit" Table="dbo.ProvozniJednotka" Type="Column" />
      <Exclude Name="PK_ProvozniJednotka" Table="dbo.ProvozniJednotka" Type="Constraint" />
      <Exclude Name="FK_ProvozniJednotka_Kredit_0" Table="dbo.ProvozniJednotka" Type="Constraint" />
      <Exclude Name="IdSklad" Table="dbo.Sklad" Type="Column" />
      <Exclude Name="Nazev" Table="dbo.Sklad" Type="Column" />
      <Exclude Name="IdProvozniJednotka" Table="dbo.Sklad" Type="Column" />
      <Exclude Name="IdStredisko" Table="dbo.Sklad" Type="Column" />
      <Exclude Name="PK_Sklad" Table="dbo.Sklad" Type="Constraint" />
      <Exclude Name="FK_Sklad_ProvozniJednotka_0" Table="dbo.Sklad" Type="Constraint" />
      <Exclude Name="FK_Sklad_Stredisko_1" Table="dbo.Sklad" Type="Constraint" />
      <Exclude Name="PK_KRE_CFSazbyDPH" Table="dbo.KRE_CFSazbyDPH" Type="Constraint" />
      <Exclude Name="Centralni" Table="dbo.Kredit" Type="Column" />
      <Exclude Name="CenyBezDph" Table="dbo.Sklad" Type="Column" />
    </UpdateFromDatabaseExcludes>
  </ModelSettings>
</EntityDeveloper>