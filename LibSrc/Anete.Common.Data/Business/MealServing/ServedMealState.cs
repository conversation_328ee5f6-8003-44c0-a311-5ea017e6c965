using Anete.Resources;
using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Anete.Common.Data.Business.MealServing
{
    /// <summary>
    /// Stav jednoho vydani jidla
    /// </summary>
    [DataContract(Namespace = AneteNamespace.DefaultNamespace)]
    public enum ServedMealState
    {
        /// <summary>
        /// Zadne objednavky
        /// </summary>
        [EnumMember]
        NoOrders,
        /// <summary>
        /// Zadne objednavky
        /// </summary>
        [EnumMember]
        NoOtherOrders,
        /// <summary>
        /// Vse jiz bylo vydano
        /// </summary>
        [EnumMember]
        AllServed,
        /// <summary>
        /// Zpracovano. Byl ucinen vydej nebo byl vracen seznam mist, na kterych je mozne provest objednavku.
        /// </summary>
        [EnumMember]
        Proceed
    }
}
