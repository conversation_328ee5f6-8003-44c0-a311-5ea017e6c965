//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Anete.Common.Data.Business.Eet {
    using System;
    
    
    /// <summary>
    /// A strongly-typed resource class, for looking up localized strings, formatting them, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilderEx class via the InternalResXFileCodeGeneratorEx custom tool.
    // To add or remove a member, edit your .ResX file then rerun the InternalResXFileCodeGeneratorEx custom tool or rebuild your VS.NET project.
    // Copyright (c) Dmy<PERSON> 2006-2018 (http://dmytro.kryvko.googlepages.com/)
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("DMKSoftware.CodeGenerators.Tools.StronglyTypedResourceBuilderEx", "2.6.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Naming", "CA1724:TypeNamesShouldNotMatchNamespaces")]
    internal partial class TestEetCertificateDataValidatorSR {
        
        private static global::System.Resources.ResourceManager _resourceManager;
        
        private static object _internalSyncObject;
        
        private static global::System.Globalization.CultureInfo _resourceCulture;
        
        /// <summary>
        /// Initializes a TestEetCertificateDataValidatorSR object.
        /// </summary>
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal TestEetCertificateDataValidatorSR() {
        }
        
        /// <summary>
        /// Thread safe lock object used by this class.
        /// </summary>
        internal static object InternalSyncObject {
            get {
                if (object.ReferenceEquals(_internalSyncObject, null)) {
                    global::System.Threading.Interlocked.CompareExchange(ref _internalSyncObject, new object(), null);
                }
                return _internalSyncObject;
            }
        }
        
        /// <summary>
        /// Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(_resourceManager, null)) {
                    global::System.Threading.Monitor.Enter(InternalSyncObject);
                    try {
                        if (object.ReferenceEquals(_resourceManager, null)) {
                            global::System.Threading.Interlocked.Exchange(ref _resourceManager, new global::System.Resources.ResourceManager("Anete.Common.Data.Business.Eet.TestEetCertificateDataValidatorSR", typeof(TestEetCertificateDataValidatorSR).Assembly));
                        }
                    }
                    finally {
                        global::System.Threading.Monitor.Exit(InternalSyncObject);
                    }
                }
                return _resourceManager;
            }
        }
        
        /// <summary>
        /// Overrides the current thread's CurrentUICulture property for all
        /// resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return _resourceCulture;
            }
            set {
                _resourceCulture = value;
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Certifikát EET je určen pro jiný server a databázi než na které běží. Ostré požadavky nepůjdou odesílat.
        ///Certifikát: {0}.
        ///EET certifikát je určen pro server {1} a databázi {2}.
        ///Aktuální server je však {3}, databáze {4}.
        ///
        ///Řešení: 
        ///1. Opravit přihlašovací údaje aplikačního serveru, aby měl použit server {1} a databázi {2}.
        ///2. Nebo spustit Konfigurátor/Nástroje/Aktualizace EET certifikátu po migraci dat.
        ///3. Nebo nově naimportovat certifikát pod správnými přihlašovacími údaji.'.
        /// </summary>
        internal static string EetDataServerChanged {
            get {
                return ResourceManager.GetString(ResourceNames.EetDataServerChanged, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Certifikát EET je určen pro jiný server a databázi než na které běží. Ostré požadavky nepůjdou odesílat.
        ///Certifikát: {0}.
        ///EET certifikát je určen pro server {1} a databázi {2}.
        ///Aktuální server je však {3}, databáze {4}.
        ///
        ///Řešení: 
        ///1. Opravit přihlašovací údaje aplikačního serveru, aby měl použit server {1} a databázi {2}.
        ///2. Nebo spustit Konfigurátor/Nástroje/Aktualizace EET certifikátu po migraci dat.
        ///3. Nebo nově naimportovat certifikát pod správnými přihlašovacími údaji.'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <param name="arg1">An object (1) to format.</param>
        /// <param name="arg2">An object (2) to format.</param>
        /// <param name="arg3">An object (3) to format.</param>
        /// <param name="arg4">An object (4) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1025:ReplaceRepetitiveArgumentsWithParamsArray")]
        internal static string EetDataServerChangedFormat(object arg0, object arg1, object arg2, object arg3, object arg4) {
            return string.Format(_resourceCulture, EetDataServerChanged, arg0, arg1, arg2, arg3, arg4);
        }
        
        /// <summary>
        /// Lists all the resource names as constant string fields.
        /// </summary>
        internal class ResourceNames {
            
            /// <summary>
            /// Stores the resource name 'EetDataServerChanged'.
            /// </summary>
            internal const string EetDataServerChanged = "EetDataServerChanged";
        }
    }
}
