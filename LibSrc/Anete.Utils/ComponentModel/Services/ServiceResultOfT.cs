using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Anete.Utils.ComponentModel.Services
{
    /// <summary>
    /// Rozšiřuje <see cref="ServiceResult"/> o výsledek <c>Result</c>
    /// </summary>
    /// <typeparam name="T">Typ určující typ výsleku</typeparam>
    public class ServiceResult<T> : ServiceResult
	{
        /// <summary>
        /// Objekt výsledku
        /// </summary>
        public T Result { get; set; }

        /// <summary>
        /// Implements the operator +.
        /// S<PERSON>ž<PERSON> položky z výsledku <paramref name="result1"/> typu <see cref="ServiceResult{T}"/> 
        /// a výsledku <paramref name="result2"/> typu <see cref="ServiceResult"/> do nového objektu.
        /// Do nového objetku je předána i hodnota <c>Result</c> z <paramref name="result1"/>.
        /// </summary>
        /// <param name="result1">The result1.</param>
        /// <param name="result2">The result2.</param>
        /// <returns>The result of the operator.</returns>
        public static ServiceResult<T> operator +(ServiceResult<T> result1, ServiceResult result2)
        {
            Guard.ArgumentNotNull(result1, "result1");
            Guard.ArgumentNotNull(result2, "result2");

            var result = new ServiceResult<T> { Result = result1.Result };
            result.Add(result1);
            result.Add(result2);
            return result;
        }

    }
}
