using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;
using Anete.Utils;

namespace Anete.Common.Data.Nh.Entities
{
	/// <summary>
	/// Pomocna trida pro praci s kompozitnim klicem k Cashier. Usnadnuje zobrazeni pokladni v gridu.
	/// </summary>
	public static class CashierCompositeIdUtils
	{
		public static int GetCompositeId(Cashier cashier)
		{
			return GetCompositeId(cashier.AppInstallation.AppInstallationId, cashier.CashierCode);
		}

		public static int GetCompositeId(short appInstallationId, byte cashierCode)
		{
			return BitwiseUtils.IntFrom2Short(appInstallationId, cashierCode);
		}
	}
}
