using System;
using System.Collections.Generic;
using Anete.Utils.Patterns;
using Anete.Utils.ComponentModel;
using System.ComponentModel;
using Anete.Utils.Extensions;

namespace Anete.Utils.Collections
{
	/// <summary>
	/// Bazova trida, ktera zajisti, ze se zmeny v BindingListu propisou do libovolneho uloziste.
	/// </summary>
	/// <typeparam name="TItem"></typeparam>
	public abstract class BindingListToStorageAdapterBase<TItem> : Disposable, ICheckIsInitialized where TItem : class, new()
	{
		private readonly BindingList<TItem> _bindingList;
		private List<TItem> _myCollection;

		/// <summary>
		/// Initializes a new instance of the BindingListToStorageAdapterBase class.
		/// </summary>
		public BindingListToStorageAdapterBase(BindingList<TItem> bindingList)
		{
			_bindingList = bindingList;
			Active = true;
		}

		#region public properties...
		/// <summary>
		/// Je adapter aktivni?
		/// Pri neaktivite neprobiha prenaseni zmen z BindingListu do repository.
		/// </summary>
		public bool Active { get; set; }
		#endregion

		#region public methods...
		/// <summary>
		/// Inicializace
		/// </summary>
		public void Initialize()
		{
			// Potrebuju mit vlastni kolekci, ktera odpovida BindingListu, abych byl schopen dohledat smazanou polozku, protoze BindingList posila pouze jeji index, ktery v te dobe
			// jiz v BindlingList neni.
			// Alternativou by bylo vytvorit potomka od BindingList, ktery me posle i instanci smazane polozky.
			_myCollection = new List<TItem>(_bindingList);
			_bindingList.ListChanged += _bindingList_ListChanged;
		}
		#endregion

		#region protected overrides...
		/// <summary>
		/// Tato metoda musi uvolnit vsechny nespravovane prostredky.
		/// </summary>
		/// <remarks>Pokud ji zajima, zda je volana z Dispose, je mozno testovat flag IsDisposing</remarks>
		protected override void DisposeUnmanagedResources()
		{
			_bindingList.ListChanged -= _bindingList_ListChanged;
		}
		#endregion

		#region protected virtual methods...
		/// <summary>
		/// Prida polozku do uloziste
		/// </summary>
		/// <param name="item"></param>
		protected abstract void AddItemToStorage(TItem item);

		/// <summary>
		/// Odebere polozku z uloziste
		/// </summary>
		/// <param name="item"></param>
		protected abstract void RemoveItemFromStorage(TItem item);

		/// <summary>
		/// Vycisti cele uloziste.
		/// </summary>
		protected abstract void ClearStorage();
		#endregion

		#region event handlers...
		void _bindingList_ListChanged(object sender, ListChangedEventArgs e)
		{
			switch (e.ListChangedType)
			{
				case ListChangedType.ItemAdded:
					TItem addedItem = _bindingList[e.NewIndex];
					_myCollection.Add(addedItem);
					if (Active)
					{
						AddItemToStorage(addedItem);
					}

					//_myCollection.Insert(e.NewIndex, addedItem);
					break;

				case ListChangedType.ItemDeleted:
					TItem deletedItem = _myCollection[e.NewIndex];
					_myCollection.RemoveAt(e.NewIndex);
					if (Active)
					{
						RemoveItemFromStorage(deletedItem);
					}
					break;

				case ListChangedType.ItemMoved:
					_myCollection.Swap(e.NewIndex, e.OldIndex);
					break;

				case ListChangedType.ItemChanged:
					break;

				case ListChangedType.Reset:
					_myCollection = new List<TItem>(_bindingList);
					if (Active)
					{
						ClearStorage();
					}
					break;

				default:
					throw ExcUtils.ArgumentOutOfRange("e.ListChangedType", e.ListChangedType);
			}
		}
		#endregion

		#region ICheckIsInitialized Members
		/// <summary>
		/// Zkontroluje, zda byl objekt inicializovan. Pokud ne, vyvola vyjimku
		/// </summary>
		/// <exception cref="InvalidOperationException">Pokud nebyl objekt inicializovan.</exception>
		public void CheckIsInitialized()
		{
			if (!IsInitialized)
			{
				throw ExcUtils.ClassNotInitialized(this);
			}
		}
		#endregion

		#region IIsInitialized Members

		/// <summary>
		/// Udava, zda byl objekt inicializovan - zda byla zavolana metoda Initialize
		/// </summary>
		/// <value></value>
		public bool IsInitialized { get; private set; }

		#endregion
	}
}
