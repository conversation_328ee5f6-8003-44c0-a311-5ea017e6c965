using System;
using Anete.Common.Core.Interface.Enums;
using System.Collections.Generic;
using Anete.Config.Core.Attributes;
using Anete.Utils;
using Anete.Utils.Collections.Tree;

namespace Anete.Config.Core.Categories
{
    /// <summary>
    /// Atribut urcujici modul Kancelare, v kterem se konfigurace vyuziva.
    /// </summary>
    public class Office8ModuleCategoryAttribute : ApplicationCategoryAttribute
    {
        private readonly Office8Module[] _modules;
        /// <summary>
        /// Initializes a new instance of the Office8ModuleCategory class.
        /// </summary>
        public Office8ModuleCategoryAttribute(params Office8Module[] modules)
            : base(ApplicationType.Office8)
        {
            _modules = modules;
        }

        /// <summary>
        /// Retezec urcujici kategorii.
        /// Kategorie ma stromovou strukturu, jako oddelovac se pouziva zpetne lomitko.
        /// </summary>
        /// <value></value>
        public override IEnumerable<ConfigCategory> Categories
        {
            get
            {
                foreach (Office8Module level in _modules)
                {
                    string popis = EnumUtils.ToLocalizedString(level, typeof(Office8Module));
                    yield return new ConfigCategory(KeyUtils.Combine(ApplicationPrefix, popis), ApplicationType);
                }
            }
        }   
    }
}
