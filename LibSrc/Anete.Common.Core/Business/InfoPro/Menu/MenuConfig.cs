using Anete.Utils.ComponentModel.UI;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;

namespace Anete.Common.Core.Business.InfoPro.Menu
{
	[JsonObject(MemberSerialization.OptOut)]
	public class MenuConfig : NotifyPropertyChangedBase
	{
		private short _workplaceId;
		/// <summary>
		/// id výdejny (dle ní se zís<PERSON>ává jídelna)
		/// </summary>
		public short WorkplaceId
		{
			get
			{
				return _workplaceId;
			}
			set
			{
				if (_workplaceId == value)
				{
					return;
				}

				_workplaceId = value;

				OnPropertyChanged();
			}
		}

		private IEnumerable<short> _canteenIds = Array.Empty<short>();
		/// <summary>
		/// id zobrazovaných výdejen
		/// </summary>
		public IEnumerable<short> CanteenIds
		{
			get
			{
				return _canteenIds;
			}
			set
			{
				if (_canteenIds == value)
				{
					return;
				}

				_canteenIds = value;
				OnPropertyChanged();
			}
		}

		private MenuDateType _dateStart;
		/// <summary>
		/// počáteční den jídelníčku
		/// </summary>
		public MenuDateType DateStart
		{
			get
			{
				return _dateStart;
			}
			set
			{
				if (_dateStart == value)
				{
					return;
				}

				_dateStart = value;
				OnPropertyChanged();
			}
		}

		private const int _dateDaysDef = 1;
		private int _dateDays = _dateDaysDef;
		/// <summary>
		/// počet dní jídelníčku
		/// </summary>
		[DefaultValue(_dateDaysDef)]
		public int DateDays
		{
			get
			{
				return _dateDays;
			}
			set
			{
				if (_dateDays == value)
				{
					return;
				}

				_dateDays = value;
				OnPropertyChanged();
			}
		}

		private bool _ignoreVisibilitySettings;
		/// <summary>
		/// ignorovat příznak Zobrazit v jídelčníku
		/// </summary>
		public bool IgnoreVisibilitySettings
		{
			get
			{
				return _ignoreVisibilitySettings;
			}
			set
			{
				if (_ignoreVisibilitySettings == value)
				{
					return;
				}

				_ignoreVisibilitySettings = value;
				OnPropertyChanged();
			}
		}

		private bool _hideWhenAllServed;
		/// <summary>
		/// skrýt jídlo pokud neexistují žádné nevydané objednávky (počet nevydaných objednávek == 0)
		/// </summary>
		public bool HideWhenAllServed
		{
			get => _hideWhenAllServed;
			set
			{
				if (_hideWhenAllServed != value)
				{
					_hideWhenAllServed = value;
					OnPropertyChanged();
				}
			}
		}

		private bool _hideWhenAllSold;
		/// <summary>
		/// skrýt jídlo pokud je již vyprodáno (limit - počet nevydaných objednávek - počet vydaných objednávek == 0)
		/// </summary>
		public bool HideWhenAllSold
		{
			get => _hideWhenAllSold;
			set
			{
				if (_hideWhenAllSold != value)
				{
					_hideWhenAllSold = value;
					OnPropertyChanged();
				}
			}
		}

		private bool _hideWhenNoRemaining;
		/// <summary>
		/// skrýt jídlo pokud již nejsou porce (limit - počet vydaných objednávek == 0)
		/// </summary>
		public bool HideWhenNoRemaining
		{
			get => _hideWhenNoRemaining;
			set
			{
				if (_hideWhenNoRemaining != value)
				{
					_hideWhenNoRemaining = value;
					OnPropertyChanged();
				}
			}
		}

		private bool _crossOutWhenAllServed;
		/// <summary>
		/// přeškrtnout jídlo pokud neexistují žádné nevydané objednávky (počet nevydaných objednávek == 0)
		/// </summary>
		public bool CrossOutWhenAllServed
		{
			get => _crossOutWhenAllServed;
			set
			{
				if (_crossOutWhenAllServed != value)
				{
					_crossOutWhenAllServed = value;
					OnPropertyChanged();
				}
			}
		}

		private bool _crossOutWhenAllSold;
		/// <summary>
		/// přeškrtnout jídlo pokud je již vyprodáno (limit - počet nevydaných objednávek - počet vydaných objednávek == 0)
		/// </summary>
		public bool CrossOutWhenAllSold
		{
			get => _crossOutWhenAllSold;
			set
			{
				if (_crossOutWhenAllSold != value)
				{
					_crossOutWhenAllSold = value;
					OnPropertyChanged();
				}
			}
		}

		private bool _crossOutWhenNoRemaining;
		/// <summary>
		/// přeškrtnout jídlo pokud již nejsou porce (limit - počet vydaných objednávek == 0)
		/// </summary>
		public bool CrossOutWhenNoRemaining
		{
			get => _crossOutWhenNoRemaining;
			set
			{
				if (_crossOutWhenNoRemaining != value)
				{
					_crossOutWhenNoRemaining = value;
					OnPropertyChanged();
				}
			}
		}

		private bool _ignoreHideWhenAllowOrderInAdvance;
		/// <summary>
		/// neskrývat jídlo, pokud lze objednat dopředu
		/// </summary>
		public bool IgnoreHideWhenAllowOrderInAdvance
		{
			get => _ignoreHideWhenAllowOrderInAdvance;
			set
			{
				if (_ignoreHideWhenAllowOrderInAdvance != value)
				{
					_ignoreHideWhenAllowOrderInAdvance = value;
					OnPropertyChanged();
				}
			}
		}

		private Color? _menuHeaderBackgroundColor;
		/// <summary>
		/// Barva pozadí hlavičky sloupců
		/// </summary>
		public Color? MenuHeaderBackgroundColor
		{
			get => _menuHeaderBackgroundColor;
			set
			{
				if (_menuHeaderBackgroundColor != value)
				{
					_menuHeaderBackgroundColor = value;
					OnPropertyChanged();
				}
			}
		}

		private Color? _groupByBackgroundColor;
		/// <summary>
		/// Barva pozadí sloupců
		/// </summary>
		public Color? GroupByBackgroundColor
		{
			get => _groupByBackgroundColor;
			set
			{
				if (_groupByBackgroundColor != value)
				{
					_groupByBackgroundColor = value;
					OnPropertyChanged();
				}
			}
		}

		private bool _firstColumnDetailIsSameFontSize;
		/// <summary>
		/// První text pod názvem jídla má stejnou velikost fontu jako název jídla
		/// </summary>
		public bool FirstColumnDetailIsSameFontSize
		{
			get => _firstColumnDetailIsSameFontSize;
			set
			{
				if (_firstColumnDetailIsSameFontSize != value)
				{
					_firstColumnDetailIsSameFontSize = value;
					OnPropertyChanged();
				}
			}
		}

		private FontSettings _fontMenuHeader = new FontSettings();
		/// <summary>
		/// Nastavení písma hlavičky sloupců
		/// </summary>
		public FontSettings FontMenuHeader
		{
			get => _fontMenuHeader;
			set
			{
				if (_fontMenuHeader != value)
				{
					_fontMenuHeader = value;
					OnPropertyChanged();
				}
			}
		}

		private FontSettings _fontColumnAlternativeName = new FontSettings();
		/// <summary>
		/// Nastavení písma názvu jídla
		/// </summary>
		public FontSettings FontColumnAlterativeName
		{
			get => _fontColumnAlternativeName;
			set
			{
				if (_fontColumnAlternativeName != value)
				{
					_fontColumnAlternativeName = value;
					OnPropertyChanged();
				}
			}
		}

		private FontSettings _fontGroupBy = new FontSettings();
		/// <summary>
		/// Nastavení písma seskupení
		/// </summary>
		public FontSettings FontGroupBy
		{
			get => _fontGroupBy;
			set
			{
				if (_fontGroupBy != value)
				{
					_fontGroupBy = value;
					OnPropertyChanged();
				}
			}
		}

		private MenuPadding _paddingColumns = new MenuPadding();
		/// <summary>
		/// Odsazení sloupců
		/// </summary>
		public MenuPadding PaddingColumns
		{
			get => _paddingColumns;
			set
			{
				if (_paddingColumns != value)
				{
					_paddingColumns = value;
					OnPropertyChanged();
				}
			}
		}

		private MenuPadding _paddingRows = new MenuPadding();
		/// <summary>
		/// Odsazení řádků
		/// </summary>
		public MenuPadding PaddingRows
		{
			get => _paddingRows;
			set
			{
				if (_paddingRows != value)
				{
					_paddingRows = value;
					OnPropertyChanged();
				}
			}
		}

		private BindingList<MenuFilter> _filter = new BindingList<MenuFilter>();
		/// <summary>
		/// seznam druhů jídla, které se budou zobrazovat
		/// pokud je poleprázdné, zobrazuje se všechno
		/// </summary>
		public BindingList<MenuFilter> Filter
		{
			get
			{
				return _filter;
			}
			set
			{
				if (_filter == value)
				{
					return;
				}

				_filter = value;
				OnPropertyChanged();
			}
		}
	}
}