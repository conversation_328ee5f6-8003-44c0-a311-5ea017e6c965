//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Anete.Common.Data.Nh.Entities
{

    /// <summary>
    /// Tabulka: dba.KJidelnicekOmezeni
    /// </summary>
    public partial class MenuCategoryRestriction : Anete.Data.Nh.NhEntityBase, INotifyPropertyChanging, INotifyPropertyChanged {

        private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(System.String.Empty);

        private short _MenuCategoryId;

        private short _MenuGroupNameId;

        private short _CountPrimary;

        private bool _CanCancel;

        private bool _CanReorder;

        private bool _CanOrder;

        private short _CountSecondary;

        private bool _SubstituteByAttendance;

        private MenuGroupName _MenuGroupName;

        private MenuCategory _MenuCategory;
    
        #region Extensibility Method Definitions
        
        partial void OnCreated();

        public override bool Equals(object obj)
        {
          MenuCategoryRestriction toCompare = obj as MenuCategoryRestriction;
          if (toCompare == null)
          {
            return false;
          }

          if (!Object.Equals(this.MenuCategoryId, toCompare.MenuCategoryId))
            return false;
          if (!Object.Equals(this.MenuGroupNameId, toCompare.MenuGroupNameId))
            return false;
         // pokud mam default primary key, znamena to, ze jedna o nove vytvorenou entitu, ktera jeste nema key prirazen. 
        // V takove pripade musim porovnat podle instance
        bool isDefault = true;
        if (this.MenuCategoryId != default(short))
        {
          isDefault = false;
        }
     
        if (this.MenuGroupNameId != default(short))
        {
          isDefault = false;
        }
     

          if (isDefault)
          {
            return object.ReferenceEquals(toCompare, this);
          }     
          return true;
        }

	private int? _hashCode = null;
        public override int GetHashCode()
        {
	if (_hashCode == null)
          {
          _hashCode = 13;
          _hashCode = (_hashCode * 7) + MenuCategoryId.GetHashCode();
          _hashCode = (_hashCode * 7) + MenuGroupNameId.GetHashCode();
}
          return _hashCode.Value;
        }
        partial void OnMenuCategoryIdChanging(short value);
        
        partial void OnMenuCategoryIdChanged();
        partial void OnMenuGroupNameIdChanging(short value);
        
        partial void OnMenuGroupNameIdChanged();
        partial void OnCountPrimaryChanging(short value);
        
        partial void OnCountPrimaryChanged();
        partial void OnCanCancelChanging(bool value);
        
        partial void OnCanCancelChanged();
        partial void OnCanReorderChanging(bool value);
        
        partial void OnCanReorderChanged();
        partial void OnCanOrderChanging(bool value);
        
        partial void OnCanOrderChanged();
        partial void OnCountSecondaryChanging(short value);
        
        partial void OnCountSecondaryChanged();
        partial void OnSubstituteByAttendanceChanging(bool value);
        
        partial void OnSubstituteByAttendanceChanged();
        partial void OnMenuGroupNameChanging(MenuGroupName value);

        partial void OnMenuGroupNameChanged();
        partial void OnMenuCategoryChanging(MenuCategory value);

        partial void OnMenuCategoryChanged();
        
        #endregion
        public MenuCategoryRestriction()
        {
            this._CountPrimary = 0;
            this._CanCancel = false;
            this._CanReorder = false;
            this._CanOrder = false;
            this._CountSecondary = 0;
            this._SubstituteByAttendance = false;
            OnCreated();
        }

    
        /// <summary>
        /// Sloupec: k_jidelnicek
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("MenuCategoryId", typeof(MenuCategoryRestrictionSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(MenuCategoryRestriction), Tag="MenuCategoryId")]
        public virtual short MenuCategoryId
        {
            get
            {
                return this._MenuCategoryId;
            }
            set
            {
                if (this._MenuCategoryId != value)
                {
                    this.OnMenuCategoryIdChanging(value);
                    this.SendPropertyChanging();
                    this._MenuCategoryId = value;
                    this.SendPropertyChanged("MenuCategoryId");
                    this.OnMenuCategoryIdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: id_skupinaJ
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("MenuGroupNameId", typeof(MenuCategoryRestrictionSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(MenuCategoryRestriction), Tag="MenuGroupNameId")]
        public virtual short MenuGroupNameId
        {
            get
            {
                return this._MenuGroupNameId;
            }
            set
            {
                if (this._MenuGroupNameId != value)
                {
                    this.OnMenuGroupNameIdChanging(value);
                    this.SendPropertyChanging();
                    this._MenuGroupNameId = value;
                    this.SendPropertyChanged("MenuGroupNameId");
                    this.OnMenuGroupNameIdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: pocet
        /// Počet jídel, které lze objednat v primární kategorii dotace
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("CountPrimary", typeof(MenuCategoryRestrictionSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(MenuCategoryRestriction), Tag="CountPrimary")]
        public virtual short CountPrimary
        {
            get
            {
                return this._CountPrimary;
            }
            set
            {
                if (this._CountPrimary != value)
                {
                    this.OnCountPrimaryChanging(value);
                    this.SendPropertyChanging();
                    this._CountPrimary = value;
                    this.SendPropertyChanged("CountPrimary");
                    this.OnCountPrimaryChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: ruseni_povoleno
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("CanCancel", typeof(MenuCategoryRestrictionSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(MenuCategoryRestriction), Tag="CanCancel")]
        public virtual bool CanCancel
        {
            get
            {
                return this._CanCancel;
            }
            set
            {
                if (this._CanCancel != value)
                {
                    this.OnCanCancelChanging(value);
                    this.SendPropertyChanging();
                    this._CanCancel = value;
                    this.SendPropertyChanged("CanCancel");
                    this.OnCanCancelChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: preobjednani_povoleno
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("CanReorder", typeof(MenuCategoryRestrictionSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(MenuCategoryRestriction), Tag="CanReorder")]
        public virtual bool CanReorder
        {
            get
            {
                return this._CanReorder;
            }
            set
            {
                if (this._CanReorder != value)
                {
                    this.OnCanReorderChanging(value);
                    this.SendPropertyChanging();
                    this._CanReorder = value;
                    this.SendPropertyChanged("CanReorder");
                    this.OnCanReorderChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: objednani_povoleno
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("CanOrder", typeof(MenuCategoryRestrictionSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(MenuCategoryRestriction), Tag="CanOrder")]
        public virtual bool CanOrder
        {
            get
            {
                return this._CanOrder;
            }
            set
            {
                if (this._CanOrder != value)
                {
                    this.OnCanOrderChanging(value);
                    this.SendPropertyChanging();
                    this._CanOrder = value;
                    this.SendPropertyChanged("CanOrder");
                    this.OnCanOrderChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: pocet2
        /// Počet jídel, které lze objednat v sekundární kategorii dotace
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("CountSecondary", typeof(MenuCategoryRestrictionSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(MenuCategoryRestriction), Tag="CountSecondary")]
        public virtual short CountSecondary
        {
            get
            {
                return this._CountSecondary;
            }
            set
            {
                if (this._CountSecondary != value)
                {
                    this.OnCountSecondaryChanging(value);
                    this.SendPropertyChanging();
                    this._CountSecondary = value;
                    this.SendPropertyChanged("CountSecondary");
                    this.OnCountSecondaryChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: NahraditDochazkou
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("SubstituteByAttendance", typeof(MenuCategoryRestrictionSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(MenuCategoryRestriction), Tag="SubstituteByAttendance")]
        public virtual bool SubstituteByAttendance
        {
            get
            {
                return this._SubstituteByAttendance;
            }
            set
            {
                if (this._SubstituteByAttendance != value)
                {
                    this.OnSubstituteByAttendanceChanging(value);
                    this.SendPropertyChanging();
                    this._SubstituteByAttendance = value;
                    this.SendPropertyChanged("SubstituteByAttendance");
                    this.OnSubstituteByAttendanceChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: id_skupinaJ
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("MenuGroupName", typeof(MenuCategoryRestrictionSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual MenuGroupName MenuGroupName
        {
            get
            {
                return this._MenuGroupName;
            }
            set
            {
                if (this._MenuGroupName != value)
                {
                    this.OnMenuGroupNameChanging(value);
                    this.SendPropertyChanging();
                    this._MenuGroupName = value;
                    this.SendPropertyChanged("MenuGroupName");
                    this.OnMenuGroupNameChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: k_jidelnicek
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("MenuCategory", typeof(MenuCategoryRestrictionSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual MenuCategory MenuCategory
        {
            get
            {
                return this._MenuCategory;
            }
            set
            {
                if (this._MenuCategory != value)
                {
                    this.OnMenuCategoryChanging(value);
                    this.SendPropertyChanging();
                    this._MenuCategory = value;
                    this.SendPropertyChanged("MenuCategory");
                    this.OnMenuCategoryChanged();
                }
            }
        }
   
        public virtual event PropertyChangingEventHandler PropertyChanging;

	// Mam v bazove tride, nema smysl generovat
	// public virtual event PropertyChangedEventHandler PropertyChanged;

        protected virtual void SendPropertyChanging()
        {
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, emptyChangingEventArgs);
        }

        protected virtual void SendPropertyChanging(System.String propertyName) 
        {   	
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, new PropertyChangingEventArgs(propertyName));
        }

        protected virtual void SendPropertyChanged(System.String propertyName)
        {    
		// mam v bazove tride, proto musim volat takto jednoduse
		OnPropertyChanged(propertyName);
/*		        var handler = this.PropertyChanged;
            if (handler != null)
                handler(this, new PropertyChangedEventArgs(propertyName));*/
        }
    }

}
