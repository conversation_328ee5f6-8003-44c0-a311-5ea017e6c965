using System;
using System.Collections.Generic;
using System.Linq;
using Anete.Common.Data.Business.NutritionalValues;
using System.Globalization;
using Anete.Common.Core.Interface.Enums;

namespace Anete.Common.Data.Business.Allergens
{
	public interface INoteTextResolver
	{
		/// <summary>
		/// Hodi se pro zobrazeni na vice radku.
		/// </summary>
		/// <param name="note">Poznamka</param>
		/// <param name="allergensXml">XML se seznamem alergenu</param>
		/// <param name="culture"><PERSON><PERSON>ura, ktera se pouzije pro lokalizaci dat ulozenych v DB</param>
		/// <param name="languageId">Id jazyka z tabulky CFJazyky</param>
		/// <param name="nutrialValues">Nutricni hodnoty, ktere se maji zobrazit</param>
		/// <param name="displayAllergenAs">Zpusob zobrazeni alergenu</param>
		IEnumerable<string> GetNoteLines(string note, string allergensXml, ApplicationLanguage languageId, DisplayAllergensAs displayAllergenAs, NutritionalSimpleValueDto[] nutrialValues, CultureInfo culture = null);

		/// <summary>
		/// Hodi se pro zobrazeni na jeden radek.
		/// </summary>
		string GetNoteLine(string note, string allergensXml, ApplicationLanguage languageId, DisplayAllergensAs displayAllergenAs, string nutritionalValuesXml, CultureInfo culture = null);
	}
}
