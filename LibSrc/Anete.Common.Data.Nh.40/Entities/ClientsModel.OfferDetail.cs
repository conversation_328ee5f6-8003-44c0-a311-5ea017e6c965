//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Anete.Common.Data.Nh.Entities
{

    /// <summary>
    /// Tabulka: dba.NAB_NabidkaDetail
    /// </summary>
    public partial class OfferDetail : Anete.Data.Nh.NhEntityBase, INotifyPropertyChanging, INotifyPropertyChanged {

        private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(System.String.Empty);

        private short _OfferId;

        private byte _OfferDetailRowId;

        private short _StockId;

        private int _GoodsId;

        private string _Description;

        private decimal _PricePerUnit;

        private decimal _Amount;

        private decimal _Price;

        private Offer _Offer;
    
        #region Extensibility Method Definitions
        
        partial void OnCreated();

        public override bool Equals(object obj)
        {
          OfferDetail toCompare = obj as OfferDetail;
          if (toCompare == null)
          {
            return false;
          }

          if (!Object.Equals(this.OfferId, toCompare.OfferId))
            return false;
          if (!Object.Equals(this.OfferDetailRowId, toCompare.OfferDetailRowId))
            return false;
         // pokud mam default primary key, znamena to, ze jedna o nove vytvorenou entitu, ktera jeste nema key prirazen. 
        // V takove pripade musim porovnat podle instance
        bool isDefault = true;
        if (this.OfferId != default(short))
        {
          isDefault = false;
        }
     
        if (this.OfferDetailRowId != default(byte))
        {
          isDefault = false;
        }
     

          if (isDefault)
          {
            return object.ReferenceEquals(toCompare, this);
          }     
          return true;
        }

	private int? _hashCode = null;
        public override int GetHashCode()
        {
	if (_hashCode == null)
          {
          _hashCode = 13;
          _hashCode = (_hashCode * 7) + OfferId.GetHashCode();
          _hashCode = (_hashCode * 7) + OfferDetailRowId.GetHashCode();
}
          return _hashCode.Value;
        }
        partial void OnOfferIdChanging(short value);
        
        partial void OnOfferIdChanged();
        partial void OnOfferDetailRowIdChanging(byte value);
        
        partial void OnOfferDetailRowIdChanged();
        partial void OnStockIdChanging(short value);
        
        partial void OnStockIdChanged();
        partial void OnGoodsIdChanging(int value);
        
        partial void OnGoodsIdChanged();
        partial void OnDescriptionChanging(string value);
        
        partial void OnDescriptionChanged();
        partial void OnPricePerUnitChanging(decimal value);
        
        partial void OnPricePerUnitChanged();
        partial void OnAmountChanging(decimal value);
        
        partial void OnAmountChanged();
        partial void OnPriceChanging(decimal value);
        
        partial void OnPriceChanged();
        partial void OnOfferChanging(Offer value);

        partial void OnOfferChanged();
        
        #endregion
        public OfferDetail()
        {
            OnCreated();
        }

    
        /// <summary>
        /// Sloupec: id
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("OfferId", typeof(OfferDetailSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(OfferDetail), Tag="OfferId")]
        public virtual short OfferId
        {
            get
            {
                return this._OfferId;
            }
            set
            {
                if (this._OfferId != value)
                {
                    this.OnOfferIdChanging(value);
                    this.SendPropertyChanging();
                    this._OfferId = value;
                    this.SendPropertyChanged("OfferId");
                    this.OnOfferIdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: id_polozka
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("OfferDetailRowId", typeof(OfferDetailSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(OfferDetail), Tag="OfferDetailRowId")]
        public virtual byte OfferDetailRowId
        {
            get
            {
                return this._OfferDetailRowId;
            }
            set
            {
                if (this._OfferDetailRowId != value)
                {
                    this.OnOfferDetailRowIdChanging(value);
                    this.SendPropertyChanging();
                    this._OfferDetailRowId = value;
                    this.SendPropertyChanged("OfferDetailRowId");
                    this.OnOfferDetailRowIdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: id_sklad
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("StockId", typeof(OfferDetailSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(OfferDetail), Tag="StockId")]
        public virtual short StockId
        {
            get
            {
                return this._StockId;
            }
            set
            {
                if (this._StockId != value)
                {
                    this.OnStockIdChanging(value);
                    this.SendPropertyChanging();
                    this._StockId = value;
                    this.SendPropertyChanged("StockId");
                    this.OnStockIdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: id_sortiment
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("GoodsId", typeof(OfferDetailSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(OfferDetail), Tag="GoodsId")]
        public virtual int GoodsId
        {
            get
            {
                return this._GoodsId;
            }
            set
            {
                if (this._GoodsId != value)
                {
                    this.OnGoodsIdChanging(value);
                    this.SendPropertyChanging();
                    this._GoodsId = value;
                    this.SendPropertyChanged("GoodsId");
                    this.OnGoodsIdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: popis
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Description", typeof(OfferDetailSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(OfferDetail), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 300,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="Description")]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(OfferDetail), Tag="Description")]
        public virtual string Description
        {
            get
            {
                return this._Description;
            }
            set
            {
                if (this._Description != value)
                {
                    this.OnDescriptionChanging(value);
                    this.SendPropertyChanging();
                    this._Description = value;
                    this.SendPropertyChanged("Description");
                    this.OnDescriptionChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: cena_jednotky
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("PricePerUnit", typeof(OfferDetailSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
       [Anete.Common.Core.Interface.Validators.DecimalValidator(typeof(OfferDetail), 9, 2, Tag = "PricePerUnit")]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(OfferDetail), Tag="PricePerUnit")]
        public virtual decimal PricePerUnit
        {
            get
            {
                return this._PricePerUnit;
            }
            set
            {
                if (this._PricePerUnit != value)
                {
                    this.OnPricePerUnitChanging(value);
                    this.SendPropertyChanging();
                    this._PricePerUnit = value;
                    this.SendPropertyChanged("PricePerUnit");
                    this.OnPricePerUnitChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: mnozstvi
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Amount", typeof(OfferDetailSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
       [Anete.Common.Core.Interface.Validators.DecimalValidator(typeof(OfferDetail), 9, 3, Tag = "Amount")]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(OfferDetail), Tag="Amount")]
        public virtual decimal Amount
        {
            get
            {
                return this._Amount;
            }
            set
            {
                if (this._Amount != value)
                {
                    this.OnAmountChanging(value);
                    this.SendPropertyChanging();
                    this._Amount = value;
                    this.SendPropertyChanged("Amount");
                    this.OnAmountChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: cena
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Price", typeof(OfferDetailSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
       [Anete.Common.Core.Interface.Validators.DecimalValidator(typeof(OfferDetail), 9, 2, Tag = "Price")]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(OfferDetail), Tag="Price")]
        public virtual decimal Price
        {
            get
            {
                return this._Price;
            }
            set
            {
                if (this._Price != value)
                {
                    this.OnPriceChanging(value);
                    this.SendPropertyChanging();
                    this._Price = value;
                    this.SendPropertyChanged("Price");
                    this.OnPriceChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: id
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Offer", typeof(OfferDetailSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual Offer Offer
        {
            get
            {
                return this._Offer;
            }
            set
            {
                if (this._Offer != value)
                {
                    this.OnOfferChanging(value);
                    this.SendPropertyChanging();
                    this._Offer = value;
                    this.SendPropertyChanged("Offer");
                    this.OnOfferChanged();
                }
            }
        }
   
        public virtual event PropertyChangingEventHandler PropertyChanging;

	// Mam v bazove tride, nema smysl generovat
	// public virtual event PropertyChangedEventHandler PropertyChanged;

        protected virtual void SendPropertyChanging()
        {
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, emptyChangingEventArgs);
        }

        protected virtual void SendPropertyChanging(System.String propertyName) 
        {   	
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, new PropertyChangingEventArgs(propertyName));
        }

        protected virtual void SendPropertyChanged(System.String propertyName)
        {    
		// mam v bazove tride, proto musim volat takto jednoduse
		OnPropertyChanged(propertyName);
/*		        var handler = this.PropertyChanged;
            if (handler != null)
                handler(this, new PropertyChangedEventArgs(propertyName));*/
        }
    }

}
