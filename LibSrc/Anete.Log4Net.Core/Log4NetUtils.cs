using System;
using System.Diagnostics;
using System.IO;
using System.Linq;
using log4net;
using log4net.Appender;
using log4net.Core;
using log4net.Layout;
using log4net.Repository;
using log4net.Repository.Hierarchy;
using log4net.Util;
using Anete.Resources;
using Anete.Log4Net.Core.Appenders;
using System.Collections;
using Anete.Log4Net.Core.Utils;
using System.Data.SqlClient;

namespace Anete.Log4Net.Core
{

	/// <summary>
	/// Utility pro runtime praci s Log4Net. Umoznuje pracovat s appendery a loggery v runtime.
	/// </summary>
	/// <remarks>Udelano na zaklade rad z http://www.l4ndash.com/Blogs/tabid/65/EntryID/18/Default.aspx</remarks>
	public static class Log4NetUtils
	{
		#region public static fields...
		/// <summary>
		/// Pripona Log4Net konfiguracniho souboru 
		/// Pozor: Musi byt konstanta, pouziva se v atributech
		/// </summary>
		public const string ConfigFileExt = "log4net.config";

		/// <summary>
		/// Format, podle ktereho se vytvareji adresare pro RollingDirAppender
		/// </summary>
		public static readonly string RollingAppenderDateFormat = "yyyy-MM-dd";
		#endregion

		/// <summary>
		/// Nalezene Appender podle jmena.
		/// </summary>
		/// <param name="name"></param>
		/// <returns>Nalezeny appender nebo Nothing v pripade nenalezeni</returns>
		/// <remarks></remarks>
		public static IAppender FindAppenderByName(string name)
		{
			ILoggerRepository rootRep = LogManagerUtils.GetRepository();
			foreach (IAppender iApp in rootRep.GetAppenders())
			{
				if (string.Compare(name, iApp.Name, true) == 0)
				{
					return iApp;
				}
			}
			return null;
		}

		/// <summary>
		/// Nalezne Appender podle jmena
		/// </summary>
		/// <param name="name"></param>
		/// <returns></returns>
		/// <remarks>V pripade nenalezeni vyvola vyjimku.</remarks>
		public static IAppender GetAppenderByName(string name)
		{
			IAppender result = FindAppenderByName(name);
			if (result == null)
			{
				throw (new ArgumentException(SysRes.sAppenderNotFoundFormat(name)));
			}
			return result;
		}

		/// <summary>
		/// Zavola Flush u vsech appenderu, ktere jsou odvozeny od BufferingAppenderSkeleton
		/// </summary>
		/// <remarks></remarks>
		public static void FlushAppenders()
		{
			ILoggerRepository rootRep = LogManagerUtils.GetRepository();
			foreach (IAppender iApp in rootRep.GetAppenders())
			{
				if ((iApp) is BufferingAppenderSkeleton)
				{
					((BufferingAppenderSkeleton)iApp).Flush();
				}
			}
		}

		/// <summary>
		/// Pro vsechny fileAppendery zmeni logovaci adresar. Z definice appenderu si vezme pouze
		/// jmeno souboru
		/// </summary>
		/// <param name="logDir">Nazev adresare</param>
		/// <remarks>Tento zpusob ma jeden problem: uz pri XmlHierarchyConfigurator se vytvori
		/// vsechny Appendery a ty si vytvori soubory. Takze nejprve vzniknou soubory v puvodnim
		/// adresari a pak soubory v novem adresari.</remarks>
		public static void ChangeLogDir(string logDir)
		{
			foreach (FileAppender fileAppender in LogManagerUtils.GetRepository().GetAppenders().OfType<FileAppender>())
			{
				try
				{
					string fileName = Path.GetFileName(fileAppender.File);
					Trace.Assert(fileName != string.Empty, "fileName <> String.Empty");
					fileAppender.File = Path.Combine(logDir, fileName);
					fileAppender.ActivateOptions();
				}
				catch (Exception ex)
				{
					string msg = String.Format("ChangeLogDir: change dir for appender {0} failed.", fileAppender.Name);
					LogLog.Error(typeof(Log4NetUtils), msg, ex);
				}
			}
		}

		/// <summary>
		/// Nutno zavolat po zmene logovaciho adresare pres AppConfig, aby se o tom dozvedely appendery
		/// </summary>
		public static void ActivateOptionsForRollingAppenders()
		{
			foreach (FileAppender fileAppender in LogManagerUtils.GetRepository().GetAppenders().OfType<RollingDirFileAppender>())
			{
				try
				{
					fileAppender.ActivateOptions();
				}
				catch (Exception ex)
				{
					string msg = String.Format("ChangeLogDir: change dir for appender {0} failed.", fileAppender.Name);
					LogLog.Error(typeof(Log4NetUtils), msg, ex);
				}
			}
		}

		/// <summary>
		/// Zkusi vytvorit logovaci adresar. Nevyvolava vyjimky ale vraci result
		/// </summary>
		/// <param name="logDir"></param>
		/// <returns></returns>
		public static bool TryCreateLogDir(string logDir)
		{
			try
			{
				if (Directory.Exists(logDir))
					return true;
				Directory.CreateDirectory(logDir);
				return true;
			}
			catch (Exception)
			{
				return false;
			}
		}

		/// <summary>
		/// Zmeni nazev souboru pro dany appender.
		/// </summary>
		/// <param name="appenderName">Nazev appenderu, tak jak je uvedeny v konfiguraci</param>
		/// <param name="pathName">Cesta k novemu souboru</param>
		/// <param name="fileName">Nove jmeno souboru BEZ cesty</param>
		/// <remarks>Pozor: soubory appenderu se vytvareji uz pri XmlHierarchyConfigurator.Configure.
		/// Aby tato metoda fungovala a zaroven XmlHierarchyConfigurator nevytvoril zadny soubor,
		/// je treba, aby mely appendery nastavene jmeno souboru na nul. Vice viz
		/// http://www.l4ndash.com/Blogs/tabid/65/EntryID/18/Default.aspx
		/// </remarks>
		public static void SetAppenderFileName(string appenderName, string pathName, string fileName)
		{
			SetAppenderFileName(appenderName, Path.Combine(pathName, fileName));
		}

		/// <summary>
		/// Zmeni nazev souboru pro dany appender.
		/// </summary>
		/// <param name="appenderName">Nazev appenderu, tak jak je uvedeny v konfiguraci</param>
		/// <param name="fileName">Nove jmeno souboru vcetne cesty</param>
		/// <remarks>Pozor: soubory appenderu se vytvareji uz pri XmlHierarchyConfigurator.Configure.
		/// Aby tato metoda fungovala a zaroven XmlHierarchyConfigurator nevytvoril zadny soubor,
		/// je treba, aby mely appendery nastavene jmeno souboru na nul. Vice viz
		/// http://www.l4ndash.com/Blogs/tabid/65/EntryID/18/Default.aspx
		/// </remarks>
		public static void SetAppenderFileName(string appenderName, string fileName)
		{
			ILoggerRepository rootRep = LogManagerUtils.GetRepository();
			foreach (IAppender iApp in rootRep.GetAppenders())
			{
				if ((iApp) is FileAppender && string.Compare(iApp.Name, appenderName, true) == 0)
				{
					try
					{
						FileAppender appender;
						appender = (FileAppender)iApp;
						appender.File = fileName;
						appender.ActivateOptions();
						return;
					}
					catch (Exception ex)
					{
						string msg = String.Format("SetAppenderFileName: change filename to {0} failed.",
							fileName);
						LogLog.Error(typeof(Log4NetUtils), msg, ex);
						return;
					}

				}
			}
			throw (new ArgumentException(Log4Res.sAppenderNotFoundFormat(appenderName)));
		}

#if NETFRAMEWORK
		/// <summary>
		/// Nastavi connection string pro appender
		/// </summary>
		/// <param name="appenderName">Jmeno appenderu</param>
		/// <param name="connectionString">Novy connection string</param>
		public static void SetAppenderConnectionString(string appenderName, string connectionString, string commandText = null, Type connectionType = null)
		{
			IAppender appender = GetAppenderByName(appenderName);


			if (!(appender is AdoNetAppender))
			{
				throw new ArgumentException(Log4Res.AppenderIsNotOfTypeFormat(appenderName, typeof(AdoNetAppender)));
			}

			try
			{
				AdoNetAppender adoAppender = (AdoNetAppender)appender;
				adoAppender.ConnectionString = connectionString;				
				if(commandText != null)
				{
					adoAppender.CommandText = commandText;
				}
				if (connectionType != null)
				{
					//adoAppender.ConnectionType = typeof(SqlConnection).AssemblyQualifiedName;
					adoAppender.ConnectionType = connectionType.AssemblyQualifiedName;
				}
				adoAppender.ActivateOptions();
				return;
			}
			catch (Exception ex)
			{
				string msg = String.Format("SetAppenderConnectionString: set connection string to {0} failed.",
					connectionString);
				LogLog.Error(typeof(Log4NetUtils), msg, ex);
				return;
			}

		}
#endif

		/// <summary>
		/// Vytvori RollingDirFileAppender a prida jej do prislusneho loggeru.
		/// </summary>
		/// <param name="loggerName">Nazev loggeru.</param>
		/// <param name="fileName">Nazev souboru, do ktereho bude appender logovat.</param>
		/// <param name="pattern">Pattern.</param>
		/// <returns>Nove vytvorenou instanci RollingDirFileAppender.</returns>
		public static RollingDirFileAppender AddRollingDirFileAppender(string loggerName, string fileName, string pattern)
		{
			// vytvoreni loggeru
			Hierarchy hierarchy = (Hierarchy)LogManagerUtils.GetRepository();
			Logger newLogger = (Logger)hierarchy.GetLogger(loggerName);

			// vytvoreni layout
			PatternLayout layout = new PatternLayout(pattern);

			// konfigurace appenderu
			RollingDirFileAppender roller = new RollingDirFileAppender();
			roller.Name = loggerName;
			roller.File = fileName;
			roller.AppendToFile = true;
			roller.Layout = layout;
			roller.ActivateOptions();

			// pridani appenderu
			newLogger.AddAppender(roller);

			return roller;
		}

		/// <summary>
		/// Odstrani appender.
		/// </summary>
		/// <param name="log">Log, ktery appender vyuziva.</param>
		/// <param name="appender">Appender, ktery se ma odstranit.</param>
		public static void RemoveAppender(ILog log, IAppender appender)
		{
			// odstraneni appenderu                
			IAppenderAttachable connectionAppender = (IAppenderAttachable)log.Logger;
			connectionAppender.RemoveAppender(appender);
			// je nutne zavolat, aby se uvolnil handle souboru
			appender.Close();
		}

		/// <summary>
		/// Odstrani appender.
		/// </summary>
		/// <param name="logger">Log, ktery appender vyuziva.</param>
		/// <param name="appender">Appender, ktery se ma odstranit.</param>
		public static void RemoveAppender(ILogger logger, IAppender appender)
		{
			// odstraneni appenderu                
			((IAppenderAttachable)logger).RemoveAppender(appender);
			// je nutne zavolat, aby se uvolnil handle souboru
			appender.Close();
		}

		/// <summary>
		/// Vraci logger, ktery je dan pouzitym typem a pozadovanym suffixem.
		/// Vyuzivaji Ctecky, kdyz tvori Log typu Informacni.1.TCPIP
		/// </summary>
		/// <param name="type"></param>
		/// <param name="suffix"></param>
		/// <returns></returns>
		public static ILogEx GetLogFromTypeAndSuffix(Type type, string suffix)
		{
			return LogManagerEx.GetLogger(String.Format("{0}.{1}", type.FullName, suffix));
		}

		/// <summary>
		/// Nastavi uroven logovani na korenovem logovaci
		/// </summary>
		/// <param name="level"></param>
		public static void SetRootLevel(log4net.Core.Level level)
		{
			Hierarchy hierarchy = (log4net.Repository.Hierarchy.Hierarchy)LogManagerUtils.GetRepository();
			hierarchy.Root.Level = level;
			hierarchy.RaiseConfigurationChanged(EventArgs.Empty);
		}
	}

}


//Here's some sample code from my application:
//
//The need here was to generate a separate log file for each device
//connection.  Override of ILog allows separate message data string to be
//passed when calling the logging functions and then added as a property.
//
//Each connection adds its own appender with unique file name and NDC
//filter.  Logging by each connection uses NDC.  So each connection's
//appender filters for its own entries and directs them to the proper
//file.
//
//In application config file I send all entries to the debug screen.
//There could also be another file which logs all entries.
//
//Some default settings are stored in the application config file for use
//in configuring each appender.
//
//
///// <summary>
///// dataLog
///// </summary>
//protected static readonly IDeviceCommunicationsLog dataLog =
//DeviceCommunicationsLogManager.GetLogger("LIS3.Data");
//
//
//Each connection adds and removes a file appender programmatically:
//
///// <summary>
///// add connection specific appender
///// </summary>
//void AddAppender()
//{
//	// check if logging is endabled
//	if( this.IsLoggingEnabled() )
//	{
//		try
//		{
//			// get the interface
//			IAppenderAttachable connectionAppender =
//(IAppenderAttachable)this.DataLog.Logger;
//			// need some application configuration settings
//			NameValueCollection appSettings =
//ConfigurationSettings.AppSettings;
//			// get the layout string
//			string log4netLayoutString =
//appSettings["log4net.LIS3.LayoutString"];
//			if( log4netLayoutString == null )
//			{
//				// use default setting
//				log4netLayoutString = "%d [%x]%n   %m%n
//%P{MessageData}%n%n";
//			}
//			// get logging path
//			string log4netPath =
//appSettings["log4net.Path"];
//			if( log4netPath == null )
//			{
//				// use default path
//				log4netPath = ".\\";
//			}
//			// create the appender
//			this.rollingFileAppender = new
//RollingFileAppender();
//			// setup the appender
//			this.rollingFileAppender.MaxFileSize = 10000000;
//			this.rollingFileAppender.MaxSizeRollBackups = 2;
//			this.rollingFileAppender.RollingStyle =
//RollingFileAppender.RollingMode.Size;
//			this.rollingFileAppender.StaticLogFileName =
//true;
//			string appenderPath = LogSourceName + ".log";
//			// log source name may have a colon - if so
//replace with underscore
//			appenderPath = appenderPath.Replace( ':', '_' );
//			// now add to log4net path
//			appenderPath = Path.Combine( log4netPath,
//appenderPath );
//			// update file property of appender
//			this.rollingFileAppender.File = appenderPath;
//			// add the layout
//			PatternLayout patternLayout = new PatternLayout(
//log4netLayoutString );
//			this.rollingFileAppender.Layout = patternLayout;
//			// add the filter for the log source
//			NDCFilter sourceFilter = new NDCFilter();
//			sourceFilter.StringToMatch = this.LogSourceName;
//			this.rollingFileAppender.AddFilter( sourceFilter
//);
//			// now add the deny all filter to end of the
//chain
//			DenyAllFilter denyAllFilter = new
//DenyAllFilter();
//			this.rollingFileAppender.AddFilter(
//denyAllFilter );
//			// activate the options
//			this.rollingFileAppender.ActivateOptions();
//			// add the appender
//			connectionAppender.AddAppender(
//this.rollingFileAppender );
//		}
//		catch( Exception x )
//		{
//			this.ErrorLog.Error( "Error creating LIS3 data
//log appender for " + LogSourceName, x );
//		}
//	}
//}
///// <summary>
///// remove connection specific appender
///// </summary>
//void RemoveAppender()
//{
//	// check if we have one
//	if( this.rollingFileAppender != null )
//	{
//		// cast to required interface
//		IAppenderAttachable connectionAppender =
//(IAppenderAttachable)this.DataLog.Logger;
//		// remove the appendier
//		connectionAppender.RemoveAppender( rollingFileAppender
//);
//		// set to null
//		this.rollingFileAppender = null;
//	}
//}
