using Anete.Config.Core.Attributes;
using System.ComponentModel;

namespace Anete.Config.Configs.Core.MenuPresenter.Behaviour
{
	/// <summary>
	/// Nastaveni specificke pro dennu menu vcetne obrazku
	/// </summary>
	public class DayMenuWithPicturesSetting : DayMenuSettings
	{
		private const int _rowCountDefValue = 3;
		private int _rowCount = _rowCountDefValue;
		/// <summary>
		/// Pocet radku
		/// </summary>
		[DefaultValue(_rowCountDefValue)]
		[ConfigSRDisplayName(nameof(RowCount), typeof(DayMenuWithPicturesSetting))]
		[ConfigSRDescription(nameof(RowCount), typeof(DayMenuWithPicturesSetting))]
		public int RowCount
		{
			get { return _rowCount; }
			set
			{
				if (_rowCount == value)
				{
					return;
				}
				_rowCount = value;
				OnPropertyChanged();
			}
		}

		private const int _columnCountDefValue = 4;
		private int _columnCount = _columnCountDefValue;
		/// <summary>
		/// Pocet sloupcu
		/// </summary>
		[DefaultValue(_columnCountDefValue)]
		[ConfigSRDisplayName(nameof(ColumnCount), typeof(DayMenuWithPicturesSetting))]
		[ConfigSRDescription(nameof(ColumnCount), typeof(DayMenuWithPicturesSetting))]
		public int ColumnCount
		{
			get { return _columnCount; }
			set
			{
				if (_columnCount == value)
				{
					return;
				}
				_columnCount = value;
				OnPropertyChanged();
			}
		}
	}
}