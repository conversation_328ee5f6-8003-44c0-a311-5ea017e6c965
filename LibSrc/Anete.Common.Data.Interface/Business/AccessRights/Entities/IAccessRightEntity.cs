using System;
using Anete.Data.Interface;
using System.ComponentModel;

namespace Anete.Common.Data.Interface.Business.AccessRights.Entities
{
    /// <summary>
    /// Entita s pristupovymi pravy. Vyuzivam pouze pro snadnejsi urcite argumentu nekterych metod,
    /// kdy potrebuju explicitne rict, ze se metoda tyka pouze entit s pristupovymi pravy.
    /// </summary>
    public interface IAccessRightEntity : IEntity, INotifyPropertyChanged
    {		
    }
}
