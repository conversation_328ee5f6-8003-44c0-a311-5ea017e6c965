using System;
using System.Linq;
using System.Text;

namespace Anete.Utils.Net.TcpIp.Logger
{
    public class ByteArrayUtf8Logger : IByteArrayLogger
    {
        public byte[] GetData(string log)
        {
            return UTF8Encoding.UTF8.GetBytes(log);
        }

        public string GetLog(byte[] data, int length)
        {
            return UTF8Encoding.UTF8.GetString(data, 0, length);
        }
    }
}
