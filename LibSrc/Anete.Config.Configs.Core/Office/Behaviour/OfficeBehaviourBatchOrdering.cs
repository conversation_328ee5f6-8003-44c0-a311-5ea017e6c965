using System;
using System.ComponentModel;
using Anete.Config.Core.Attributes;
using Anete.Config.Core.Categories;
using Anete.Config.Core.CFSystKonfig;
using Anete.Config.Core.Internals;

namespace Anete.Config.Configs.Core.Office.Behaviour
{
    /// <summary>
    /// Nastaveni hromadneho objednavani
    /// </summary>
    [OfficeBehaviourDBKey("BatchOrdering")]
    [ConfigVersion("*******")]
    [ConfigClassSRDescription(typeof(OfficeBehaviourBatchOrdering))]
    [DefaultConfigInstance(false)]
    [OfficeCategory(CategoryLevel.Basic, CategoryLevel.Advanced)]
    public class OfficeBehaviourBatchOrdering : ConfigBase
    {

        private const bool _enableBatchOperationsDefValue = false;
        private bool _enableBatchOperations = _enableBatchOperationsDefValue;
        /// <summary>
        /// Povolit hromadne operace v nahradnim objednavani
        /// </summary>
        [DefaultValue(_enableBatchOperationsDefValue)]
        [ConfigSRDisplayName("EnableBatchOperations", typeof(OfficeBehaviourBatchOrdering))]
        [ConfigSRDescription("EnableBatchOperations", typeof(OfficeBehaviourBatchOrdering))]
        [MapOldBoolPropertyToCFSystKonfigAttribute("povol_HOOperace", MapToCFSystKonfigOption.ReadFromCFSystKonfig)]
        public bool EnableBatchOperations
        {
            get
            {
                return _enableBatchOperations;
            }
            set
            {
                if (_enableBatchOperations == value)
                {
                    return;
                }
                _enableBatchOperations = value;
                OnPropertyChanged("EnableBatchOperations");
            }
        }

        private const bool _conformToRulesDefValue = false;
        private bool _conformToRules = _conformToRulesDefValue;
        /// <summary>
        /// True - respektovat standardní objednací pravidla systému. Pokud je nastavena hodnota false, provede náhradní objednávání i 
        /// objednávky, které objednací pravidla porušují.
        /// </summary>
        [DefaultValue(_conformToRulesDefValue)]
        [ConfigSRDisplayName("ConformToRules", typeof(OfficeBehaviourBatchOrdering))]
        [ConfigSRDescription("ConformToRules", typeof(OfficeBehaviourBatchOrdering))]
        [MapOldBoolPropertyToCFSystKonfig("HORespObjPrav", MapToCFSystKonfigOption.ReadFromCFSystKonfig)]
        public bool ConformToRules
        {
            get
            {
                return _conformToRules;
            }
            set
            {
                if (_conformToRules == value)
                {
                    return;
                }
                _conformToRules = value;
                OnPropertyChanged("ConformToRules");
            }
        }

		private const bool _keepSelectedAltDefValue = false;
		private bool _keepSelectedAlt = _keepSelectedAltDefValue;
		/// <summary>
		/// Bude se pri zmene druhu jidla udrzovat zvolena alternativa?
		/// </summary>
		[DefaultValue(_conformToRulesDefValue)]
		[ConfigSRDisplayName("KeepSelectedAlt", typeof(OfficeBehaviourBatchOrdering))]
		[ConfigSRDescription("KeepSelectedAlt", typeof(OfficeBehaviourBatchOrdering))]	
		public bool KeepSelectedAlt
		{
			get
			{
				return _keepSelectedAlt;
			}
			set
			{
				if (_keepSelectedAlt == value)
				{
					return;
				}
				_keepSelectedAlt = value;
				OnPropertyChanged();
			}
		}
	}
}
