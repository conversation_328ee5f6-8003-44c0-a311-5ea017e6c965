using Anete.Common.Data.Nh.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;
using Anete.Utils.ComponentModel.Attributes;

namespace Anete.Common.Data.Nh.Entities
{
	[XmlType("Item")]
	/// <summary>
	/// Polozka rozsirenych udaju poptavky
	/// </summary>
	public class InquiryExtendedDataItem
	{
		[ResXDisplayNameAttribute(nameof(MealType), typeof(InquiryExtendedDataItemSR))]
		[XmlElement(ElementName = "MealType")]
		public InquiryExtendedDataMealType MealType { get; set; }

		[ResXDisplayNameAttribute(nameof(Hot), typeof(InquiryExtendedDataItemSR))]
		[XmlElement(ElementName = "Hot")]
		public short? Hot { get; set; }

		[ResXDisplayNameAttribute(nameof(Shocked), typeof(InquiryExtendedDataItemSR))]
		[XmlElement(ElementName = "Shocked")]
		public short? Shocked { get; set; }

		[ResXDisplayNameAttribute(nameof(Cold), typeof(InquiryExtendedDataItemSR))]
		[XmlElement(ElementName = "Cold")]
		public short? Cold { get; set; }

		[ResXDisplayNameAttribute(nameof(Frozen), typeof(InquiryExtendedDataItemSR))]
		[XmlElement(ElementName = "Frozen")]
		public short? Frozen { get; set; }
	}
}
