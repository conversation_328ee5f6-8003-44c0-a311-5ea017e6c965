//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Anete.Common.Core.Business.InfoPro.Columns {
    using System;
    
    
    /// <summary>
    /// A strongly-typed resource class, for looking up localized strings, formatting them, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilderEx class via the ResXFileCodeGeneratorEx custom tool.
    // To add or remove a member, edit your .ResX file then rerun the ResXFileCodeGeneratorEx custom tool or rebuild your VS.NET project.
    // Copyright (c) Dmytro <PERSON> 2006-2023 (http://dmytro.kryvko.googlepages.com/)
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("DMKSoftware.CodeGenerators.Tools.StronglyTypedResourceBuilderEx", "2.9.1.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Naming", "CA1724:TypeNamesShouldNotMatchNamespaces")]
    public partial class ColumnTypeSR {
        
        private static global::System.Resources.ResourceManager _resourceManager;
        
        private static object _internalSyncObject;
        
        private static global::System.Globalization.CultureInfo _resourceCulture;
        
        /// <summary>
        /// Initializes a ColumnTypeSR object.
        /// </summary>
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        public ColumnTypeSR() {
        }
        
        /// <summary>
        /// Thread safe lock object used by this class.
        /// </summary>
        public static object InternalSyncObject {
            get {
                if (object.ReferenceEquals(_internalSyncObject, null)) {
                    global::System.Threading.Interlocked.CompareExchange(ref _internalSyncObject, new object(), null);
                }
                return _internalSyncObject;
            }
        }
        
        /// <summary>
        /// Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(_resourceManager, null)) {
                    global::System.Threading.Monitor.Enter(InternalSyncObject);
                    try {
                        if (object.ReferenceEquals(_resourceManager, null)) {
                            global::System.Threading.Interlocked.Exchange(ref _resourceManager, new global::System.Resources.ResourceManager("Anete.Common.Core.Business.InfoPro.Columns.ColumnTypeSR", typeof(ColumnTypeSR).Assembly));
                        }
                    }
                    finally {
                        global::System.Threading.Monitor.Exit(InternalSyncObject);
                    }
                }
                return _resourceManager;
            }
        }
        
        /// <summary>
        /// Overrides the current thread's CurrentUICulture property for all
        /// resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return _resourceCulture;
            }
            set {
                _resourceCulture = value;
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Alergeny'.
        /// </summary>
        public static string ColumnType_Allergens {
            get {
                return ResourceManager.GetString(ResourceNames.ColumnType_Allergens, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Číslo alternativy'.
        /// </summary>
        public static string ColumnType_AlternativeId {
            get {
                return ResourceManager.GetString(ResourceNames.ColumnType_AlternativeId, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Název alternativy'.
        /// </summary>
        public static string ColumnType_AlternativeName {
            get {
                return ResourceManager.GetString(ResourceNames.ColumnType_AlternativeName, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Množství (gramáž)'.
        /// </summary>
        public static string ColumnType_Amount {
            get {
                return ResourceManager.GetString(ResourceNames.ColumnType_Amount, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Výdejna'.
        /// </summary>
        public static string ColumnType_Canteen {
            get {
                return ResourceManager.GetString(ResourceNames.ColumnType_Canteen, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Datum'.
        /// </summary>
        public static string ColumnType_Date {
            get {
                return ResourceManager.GetString(ResourceNames.ColumnType_Date, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Počet v burze'.
        /// </summary>
        public static string ColumnType_InExchange {
            get {
                return ResourceManager.GetString(ResourceNames.ColumnType_InExchange, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Název druhu jídla'.
        /// </summary>
        public static string ColumnType_MealKind {
            get {
                return ResourceManager.GetString(ResourceNames.ColumnType_MealKind, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Poznámka'.
        /// </summary>
        public static string ColumnType_Note {
            get {
                return ResourceManager.GetString(ResourceNames.ColumnType_Note, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Počet nevydaných jídel'.
        /// </summary>
        public static string ColumnType_NotServed {
            get {
                return ResourceManager.GetString(ResourceNames.ColumnType_NotServed, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Počet neprodaných jídel'.
        /// </summary>
        public static string ColumnType_NotSold {
            get {
                return ResourceManager.GetString(ResourceNames.ColumnType_NotSold, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nutriční hodnoty'.
        /// </summary>
        public static string ColumnType_NutritionalValues {
            get {
                return ResourceManager.GetString(ResourceNames.ColumnType_NutritionalValues, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Piktogram'.
        /// </summary>
        public static string ColumnType_Pictogram {
            get {
                return ResourceManager.GetString(ResourceNames.ColumnType_Pictogram, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Cena'.
        /// </summary>
        public static string ColumnType_Price {
            get {
                return ResourceManager.GetString(ResourceNames.ColumnType_Price, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Počet zbývajících jídel'.
        /// </summary>
        public static string ColumnType_Remaining {
            get {
                return ResourceManager.GetString(ResourceNames.ColumnType_Remaining, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Lists all the resource names as constant string fields.
        /// </summary>
        public class ResourceNames {
            
            /// <summary>
            /// Stores the resource name 'ColumnType_Allergens'.
            /// </summary>
            public const string ColumnType_Allergens = "ColumnType_Allergens";
            
            /// <summary>
            /// Stores the resource name 'ColumnType_AlternativeId'.
            /// </summary>
            public const string ColumnType_AlternativeId = "ColumnType_AlternativeId";
            
            /// <summary>
            /// Stores the resource name 'ColumnType_AlternativeName'.
            /// </summary>
            public const string ColumnType_AlternativeName = "ColumnType_AlternativeName";
            
            /// <summary>
            /// Stores the resource name 'ColumnType_Amount'.
            /// </summary>
            public const string ColumnType_Amount = "ColumnType_Amount";
            
            /// <summary>
            /// Stores the resource name 'ColumnType_Canteen'.
            /// </summary>
            public const string ColumnType_Canteen = "ColumnType_Canteen";
            
            /// <summary>
            /// Stores the resource name 'ColumnType_Date'.
            /// </summary>
            public const string ColumnType_Date = "ColumnType_Date";
            
            /// <summary>
            /// Stores the resource name 'ColumnType_InExchange'.
            /// </summary>
            public const string ColumnType_InExchange = "ColumnType_InExchange";
            
            /// <summary>
            /// Stores the resource name 'ColumnType_MealKind'.
            /// </summary>
            public const string ColumnType_MealKind = "ColumnType_MealKind";
            
            /// <summary>
            /// Stores the resource name 'ColumnType_Note'.
            /// </summary>
            public const string ColumnType_Note = "ColumnType_Note";
            
            /// <summary>
            /// Stores the resource name 'ColumnType_NotServed'.
            /// </summary>
            public const string ColumnType_NotServed = "ColumnType_NotServed";
            
            /// <summary>
            /// Stores the resource name 'ColumnType_NotSold'.
            /// </summary>
            public const string ColumnType_NotSold = "ColumnType_NotSold";
            
            /// <summary>
            /// Stores the resource name 'ColumnType_NutritionalValues'.
            /// </summary>
            public const string ColumnType_NutritionalValues = "ColumnType_NutritionalValues";
            
            /// <summary>
            /// Stores the resource name 'ColumnType_Pictogram'.
            /// </summary>
            public const string ColumnType_Pictogram = "ColumnType_Pictogram";
            
            /// <summary>
            /// Stores the resource name 'ColumnType_Price'.
            /// </summary>
            public const string ColumnType_Price = "ColumnType_Price";
            
            /// <summary>
            /// Stores the resource name 'ColumnType_Remaining'.
            /// </summary>
            public const string ColumnType_Remaining = "ColumnType_Remaining";
        }
    }
}
