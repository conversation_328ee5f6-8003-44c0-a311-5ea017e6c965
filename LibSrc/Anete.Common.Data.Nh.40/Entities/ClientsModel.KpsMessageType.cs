//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Anete.Common.Data.Nh.Entities
{

    /// <summary>
    /// Tabulka: dba.KPS_TypDotazu
    /// </summary>
    public partial class KpsMessageType : Anete.Data.Nh.NhEntityBase, INotifyPropertyChanging, INotifyPropertyChanged {

        private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(System.String.Empty);

        private byte _MessageTypeId;

        private string _Name;

        private ISet<KpsNotifyUser> _NotifiedUsers;

        private ISet<KpsMessage> _Messages;
    
        #region Extensibility Method Definitions
        
        partial void OnCreated();

        public override bool Equals(object obj)
        {
          KpsMessageType toCompare = obj as KpsMessageType;
          if (toCompare == null)
          {
            return false;
          }

          if (!Object.Equals(this.MessageTypeId, toCompare.MessageTypeId))
            return false;
         // pokud mam default primary key, znamena to, ze jedna o nove vytvorenou entitu, ktera jeste nema key prirazen. 
        // V takove pripade musim porovnat podle instance
        bool isDefault = true;
        if (this.MessageTypeId != default(byte))
        {
          isDefault = false;
        }
     

          if (isDefault)
          {
            return object.ReferenceEquals(toCompare, this);
          }     
          return true;
        }

	private int? _hashCode = null;
        public override int GetHashCode()
        {
	if (_hashCode == null)
          {
          _hashCode = 13;
          _hashCode = (_hashCode * 7) + MessageTypeId.GetHashCode();
}
          return _hashCode.Value;
        }
        partial void OnMessageTypeIdChanging(byte value);
        
        partial void OnMessageTypeIdChanged();
        partial void OnNameChanging(string value);
        
        partial void OnNameChanged();
        
        #endregion
        public KpsMessageType()
        {
            this._NotifiedUsers = new HashSet<KpsNotifyUser>();
            this._Messages = new HashSet<KpsMessage>();
            OnCreated();
        }

    
        /// <summary>
        /// Sloupec: id
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("MessageTypeId", typeof(KpsMessageTypeSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(KpsMessageType), Tag="MessageTypeId")]
        public virtual byte MessageTypeId
        {
            get
            {
                return this._MessageTypeId;
            }
            set
            {
                if (this._MessageTypeId != value)
                {
                    this.OnMessageTypeIdChanging(value);
                    this.SendPropertyChanging();
                    this._MessageTypeId = value;
                    this.SendPropertyChanged("MessageTypeId");
                    this.OnMessageTypeIdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: popis
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Name", typeof(KpsMessageTypeSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(KpsMessageType), 1,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, 50,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="Name")]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(KpsMessageType), Tag="Name")]
        public virtual string Name
        {
            get
            {
                return this._Name;
            }
            set
            {
                if (this._Name != value)
                {
                    this.OnNameChanging(value);
                    this.SendPropertyChanging();
                    this._Name = value;
                    this.SendPropertyChanged("Name");
                    this.OnNameChanged();
                }
            }
        }

        [Anete.Data.Attributes.EntitySRDisplayName("NotifiedUsers", typeof(KpsMessageTypeSR))]
        [Anete.Data.Attributes.DbCollectionProperty]
        public virtual ISet<KpsNotifyUser> NotifiedUsers
        {
            get
            {
                return this._NotifiedUsers;
            }
            set
            {
                this._NotifiedUsers = value;
            }
        }

        [Anete.Data.Attributes.EntitySRDisplayName("Messages", typeof(KpsMessageTypeSR))]
        [Anete.Data.Attributes.DbCollectionProperty]
        public virtual ISet<KpsMessage> Messages
        {
            get
            {
                return this._Messages;
            }
            set
            {
                this._Messages = value;
            }
        }
   
        public virtual event PropertyChangingEventHandler PropertyChanging;

	// Mam v bazove tride, nema smysl generovat
	// public virtual event PropertyChangedEventHandler PropertyChanged;

        protected virtual void SendPropertyChanging()
        {
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, emptyChangingEventArgs);
        }

        protected virtual void SendPropertyChanging(System.String propertyName) 
        {   	
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, new PropertyChangingEventArgs(propertyName));
        }

        protected virtual void SendPropertyChanged(System.String propertyName)
        {    
		// mam v bazove tride, proto musim volat takto jednoduse
		OnPropertyChanged(propertyName);
/*		        var handler = this.PropertyChanged;
            if (handler != null)
                handler(this, new PropertyChangedEventArgs(propertyName));*/
        }
    }

}
