using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Anete.Utils.Extensions;

namespace Anete.Wcf
{
	/// <summary>
	/// Utility pro UttpRequst. Mozna presunout jinam?
	/// </summary>
	public static class HttpRequestUtils
	{

		/// <summary>
		/// Vybrakovano z HttpRequest
		/// </summary>
		/// <param name="s"></param>
		/// <returns></returns>
		public static string[] ParseMultivalueHeader(string s)
		{
			int num = (s != null) ? s.Length : 0;

			if (num == 0)
			{
				return null;
			}

			List<string> list = new List<string>();
			int startIndex = 0;

			while (startIndex < num)
			{
				int index = s.IndexOf(',', startIndex);
				if (index < 0)
				{
					index = num;
				}
				list.Add(s.Substring(startIndex, index - startIndex));
				startIndex = index + 1;
				if ((startIndex < num) && (s[startIndex] == ' '))
				{
					startIndex++;
				}
			}

			int count = list.Count;

			if (count == 0)
			{
				return null;
			}

			string[] array = new string[count];
			list.CopyTo(0, array, 0, count);
			return array;
		}

	}
}
