//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate Fluent Mapping template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;
using FluentNHibernate.Mapping;
using FluentNHibernate.MappingModel.Collections;

namespace Anete.Common.Data.Nh.Entities
{
    /// <summary>
    /// There are no comments for ActivityLogMap in the schema.
    /// </summary>
    public partial class ActivityLogMap : ClassMap<ActivityLog>
    {
        /// <summary>
        /// There are no comments for ActivityLogMap constructor in the schema.
        /// </summary>
        public ActivityLogMap()
        {
              Schema(@"dba");
              Table(@"LogAktivita");
              LazyLoad();
              Id(x => x.ActivityLogId)
                .Column("pc")
                .CustomType("Int32")
                .Access.Property().CustomSqlType("int")
                .Not.Nullable()
                .Precision(10)
                .Unique()                
                .GeneratedBy.Identity();
              Map(x => x.TimeStamp)    
                .Column("tstamp")
                .CustomType("DateTime")
                .Access.Property()
                .Not.Insert()
                .Not.Update()
                .Generated.Always()
                .Default(@"getdate()").CustomSqlType("datetime")
                .Not.Nullable();
              Map(x => x.AppInstallationId)    
                .Column("id_zarizeni")
                .CustomType("Int32")
                .Access.Property()
                .Generated.Never().CustomSqlType("int")
                .Not.Nullable()
                .Precision(10);
              Map(x => x.UserName)    
                .Column("userloginname")
                .CustomType("String")
                .Access.Property()
                .Generated.Never().CustomSqlType("varchar(30)")
                .Length(30);
              Map(x => x.Alias)    
                .Column("alias")
                .CustomType("String")
                .Access.Property()
                .Generated.Never().CustomSqlType("varchar(25)")
                .Length(25);
              Map(x => x.Description)    
                .Column("popis")
                .CustomType("String")
                .Access.Property()
                .Generated.Never().CustomSqlType("varchar(500)")
                .Length(500);
              ExtendMapping();
        }

        #region Partial Methods

        partial void ExtendMapping();

        #endregion
    }

}
