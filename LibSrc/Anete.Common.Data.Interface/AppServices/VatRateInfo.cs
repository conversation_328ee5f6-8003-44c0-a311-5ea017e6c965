using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Anete.Common.Data.Interface.Entities;
using Anete.Common.Data.Interface.Enums;
using Anete.Utils;

namespace Anete.Common.Data.Interface.AppServices
{

	/// <summary>
	/// Informace ke konkretni sazbe DPH
	/// Kak: 23.02.2017 IVatGroup je zde implementovano proto, aby se dalo pouzit i v business objektech, ktere toto potrebuji
	/// </summary>
	public class VatRateInfo : IVatGroup
	{
		
		public VatRateInfo(byte id, decimal percentage, string name, string text, string textExtended)
		{
			Id = id;
			Percentage = percentage;
			Name = name;
			Text = text;
			TextExtended = textExtended;
		}

		public byte Id { get; private set; }
		public decimal Percentage { get; private set; }
		public string Name { get; private set; }
		public string Text { get; private set; }
		public string TextExtended { get; private set; }

		/// <summary>
		/// Jedna se o programove pridanou sazbu DPH. Pouziva se pri prechodnych obdobich na specifickych mistech v kodu. Vyvoreno pri zruseni druhe snizene sazby DPH v roce 2024.
		/// </summary>
		public bool BuildIn { get; set; }

		#region IVatGroup
		/// <summary>
		/// Zde volim implicitni implementaci rozhrani, muze se hodit i nekde jinde
		/// </summary>
		public IdDph IdDph
		{
			get { return EnumUtils.FromInt<IdDph>(Id); }
		}

		decimal? IVatGroup.VatValue
		{
			get { return Percentage; }
		}

		string IVatGroup.Text
		{
			get { return Text; }
		}
		#endregion

	}
}
