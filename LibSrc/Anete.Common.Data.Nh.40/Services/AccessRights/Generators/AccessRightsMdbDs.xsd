<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="AccessRightsDataSet" targetNamespace="http://tempuri.org/AccessRightsDataSet.xsd" xmlns:mstns="http://tempuri.org/AccessRightsDataSet.xsd" xmlns="http://tempuri.org/AccessRightsDataSet.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="Settings" AppSettingsPropertyName="AccessRightsMdbConnectionString" IsAppSettingsProperty="true" Modifier="Assembly" Name="AccessRightsMdbConnectionString (Settings)" PropertyReference="ApplicationSettings.Anete.MsBuild.Properties.Settings.GlobalReference.Default.AccessRightsMdbConnectionString" Provider="System.Data.OleDb" />
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ACC_KodPravaTableAdapter" GeneratorDataComponentClassName="ACC_KodPravaTableAdapter" Name="ACC_KodPrava" UserDataComponentName="ACC_KodPravaTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="AccessRightsMdbConnectionString (Settings)" DbObjectName="ACC_KodPrava" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM `ACC_KodPrava` WHERE ((`IdKodPrava` = ?))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_IdKodPrava" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdKodPrava" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO `ACC_KodPrava` (`IdKodPrava`, `TypAplikace`, `KodPravaGuid`, `KodPrava`) VALUES (?, ?, ?, ?)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IdKodPrava" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdKodPrava" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="TypAplikace" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="TypAplikace" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="KodPravaGuid" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="KodPravaGuid" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="KodPrava" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="KodPrava" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT IdKodPrava, TypAplikace, KodPravaGuid, KodPrava FROM ACC_KodPrava</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE `ACC_KodPrava` SET `IdKodPrava` = ?, `TypAplikace` = ?, `KodPravaGuid` = ?, `KodPrava` = ? WHERE ((`IdKodPrava` = ?))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IdKodPrava" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdKodPrava" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="TypAplikace" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="TypAplikace" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="KodPravaGuid" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="KodPravaGuid" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="KodPrava" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="KodPrava" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_IdKodPrava" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdKodPrava" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="IdKodPrava" DataSetColumn="IdKodPrava" />
              <Mapping SourceColumn="TypAplikace" DataSetColumn="TypAplikace" />
              <Mapping SourceColumn="KodPravaGuid" DataSetColumn="KodPravaGuid" />
              <Mapping SourceColumn="KodPrava" DataSetColumn="KodPrava" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ACC_KodPravaParametryTableAdapter" GeneratorDataComponentClassName="ACC_KodPravaParametryTableAdapter" Name="ACC_KodPravaParametry" UserDataComponentName="ACC_KodPravaParametryTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="AccessRightsMdbConnectionString (Settings)" DbObjectName="ACC_KodPravaParametry" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO `ACC_KodPravaParametry` (`IdKodPrava`, `IdParametr`) VALUES (?, ?)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IdKodPrava" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdKodPrava" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IdParametr" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdParametr" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT IdKodPrava, IdParametr FROM ACC_KodPravaParametry</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="IdKodPrava" DataSetColumn="IdKodPrava" />
              <Mapping SourceColumn="IdParametr" DataSetColumn="IdParametr" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ACC_ParametrTableAdapter" GeneratorDataComponentClassName="ACC_ParametrTableAdapter" Name="ACC_Parametr" UserDataComponentName="ACC_ParametrTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="AccessRightsMdbConnectionString (Settings)" DbObjectName="ACC_Parametr" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM `ACC_Parametr` WHERE ((`IdParametr` = ?))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_IdParametr" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdParametr" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO `ACC_Parametr` (`IdParametr`, `Popis`, `Implementace`, `Assembly`) VALUES (?, ?, ?, ?)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IdParametr" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdParametr" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Popis" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Popis" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Implementace" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Implementace" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Assembly" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Assembly" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT        IdParametr, Popis, Implementace, Assembly
FROM            ACC_Parametr</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE `ACC_Parametr` SET `IdParametr` = ?, `Popis` = ?, `Implementace` = ?, `Assembly` = ? WHERE ((`IdParametr` = ?))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IdParametr" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdParametr" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Popis" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Popis" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Implementace" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Implementace" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Assembly" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Assembly" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_IdParametr" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdParametr" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="IdParametr" DataSetColumn="IdParametr" />
              <Mapping SourceColumn="Popis" DataSetColumn="Popis" />
              <Mapping SourceColumn="Implementace" DataSetColumn="Implementace" />
              <Mapping SourceColumn="Assembly" DataSetColumn="Assembly" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ACC_PravaSkupinTableAdapter" GeneratorDataComponentClassName="ACC_PravaSkupinTableAdapter" Name="ACC_PravaSkupin" UserDataComponentName="ACC_PravaSkupinTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="AccessRightsMdbConnectionString (Settings)" DbObjectName="ACC_PravaSkupin" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO `ACC_PravaSkupin` (`IdSkupina`, `IdPravo`) VALUES (?, ?)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IdSkupina" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdSkupina" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IdPravo" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdPravo" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT IdSkupina, IdPravo FROM ACC_PravaSkupin</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="IdSkupina" DataSetColumn="IdSkupina" />
              <Mapping SourceColumn="IdPravo" DataSetColumn="IdPravo" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ACC_PravoTableAdapter" GeneratorDataComponentClassName="ACC_PravoTableAdapter" Name="ACC_Pravo" UserDataComponentName="ACC_PravoTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="AccessRightsMdbConnectionString (Settings)" DbObjectName="ACC_Pravo" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM `ACC_Pravo` WHERE ((`IdPravo` = ?))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_IdPravo" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdPravo" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO `ACC_Pravo` (`IdPravo`, `IdKodPrava`, `Kod`, `Popis`) VALUES (?, ?, ?, ?)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IdPravo" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdPravo" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IdKodPrava" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdKodPrava" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Kod" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Kod" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Popis" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Popis" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT IdPravo, IdKodPrava, Kod, Popis FROM ACC_Pravo</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE `ACC_Pravo` SET `IdPravo` = ?, `IdKodPrava` = ?, `Kod` = ?, `Popis` = ? WHERE ((`IdPravo` = ?))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IdPravo" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdPravo" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IdKodPrava" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdKodPrava" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Kod" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Kod" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Popis" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Popis" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_IdPravo" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdPravo" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="IdPravo" DataSetColumn="IdPravo" />
              <Mapping SourceColumn="IdKodPrava" DataSetColumn="IdKodPrava" />
              <Mapping SourceColumn="Kod" DataSetColumn="Kod" />
              <Mapping SourceColumn="Popis" DataSetColumn="Popis" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="APP_ObjektTableAdapter" GeneratorDataComponentClassName="APP_ObjektTableAdapter" Name="APP_Objekt" UserDataComponentName="APP_ObjektTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="AccessRightsMdbConnectionString (Settings)" DbObjectName="APP_Objekt" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM `APP_Objekt` WHERE ((`IdObjekt` = ?))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_IdObjekt" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdObjekt" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO `APP_Objekt` (`IdObjekt`, `Assembly`, `Implementace`, `Popis`, `ObjektGuid`) VALUES (?, ?, ?, ?, ?)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IdObjekt" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdObjekt" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Assembly" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Assembly" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Implementace" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Implementace" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Popis" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Popis" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="ObjektGuid" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="ObjektGuid" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT IdObjekt, Assembly, Implementace, Popis, ObjektGuid FROM APP_Objekt</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE `APP_Objekt` SET `IdObjekt` = ?, `Assembly` = ?, `Implementace` = ?, `Popis` = ?, `ObjektGuid` = ? WHERE ((`IdObjekt` = ?))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IdObjekt" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdObjekt" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Assembly" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Assembly" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Implementace" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Implementace" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Popis" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Popis" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="ObjektGuid" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="ObjektGuid" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_IdObjekt" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdObjekt" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="IdObjekt" DataSetColumn="IdObjekt" />
              <Mapping SourceColumn="Assembly" DataSetColumn="Assembly" />
              <Mapping SourceColumn="Implementace" DataSetColumn="Implementace" />
              <Mapping SourceColumn="Popis" DataSetColumn="Popis" />
              <Mapping SourceColumn="ObjektGuid" DataSetColumn="ObjektGuid" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ACC_KodPravaObjektyTableAdapter" GeneratorDataComponentClassName="ACC_KodPravaObjektyTableAdapter" Name="ACC_KodPravaObjekty" UserDataComponentName="ACC_KodPravaObjektyTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="AccessRightsMdbConnectionString (Settings)" DbObjectName="ACC_KodPravaObjekty" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO `ACC_KodPravaObjekty` (`IdKodPrava`, `IdObjekt`) VALUES (?, ?)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IdKodPrava" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdKodPrava" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IdObjekt" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdObjekt" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT IdKodPrava, IdObjekt FROM ACC_KodPravaObjekty</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="IdKodPrava" DataSetColumn="IdKodPrava" />
              <Mapping SourceColumn="IdObjekt" DataSetColumn="IdObjekt" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ACC_SkupinaTableAdapter" GeneratorDataComponentClassName="ACC_SkupinaTableAdapter" Name="ACC_Skupina" UserDataComponentName="ACC_SkupinaTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="AccessRightsMdbConnectionString (Settings)" DbObjectName="ACC_Skupina" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM `ACC_Skupina` WHERE ((`IdSkupina` = ?) AND ((? = 1 AND `Kod` IS NULL) OR (`Kod` = ?)) AND ((? = 1 AND `Popis` IS NULL) OR (`Popis` = ?)) AND ((? = 1 AND `Typ` IS NULL) OR (`Typ` = ?)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_IdSkupina" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdSkupina" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_Kod" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Kod" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Original_Kod" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Kod" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_Popis" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Popis" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Original_Popis" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Popis" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_Typ" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Typ" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_Typ" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Typ" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO `ACC_Skupina` (`IdSkupina`, `Kod`, `Popis`, `Typ`) VALUES (?, ?, ?, ?)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IdSkupina" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdSkupina" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Kod" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Kod" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Popis" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Popis" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Typ" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Typ" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT IdSkupina, Kod, Popis, Typ FROM ACC_Skupina</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE `ACC_Skupina` SET `IdSkupina` = ?, `Kod` = ?, `Popis` = ?, `Typ` = ? WHERE ((`IdSkupina` = ?) AND ((? = 1 AND `Kod` IS NULL) OR (`Kod` = ?)) AND ((? = 1 AND `Popis` IS NULL) OR (`Popis` = ?)) AND ((? = 1 AND `Typ` IS NULL) OR (`Typ` = ?)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IdSkupina" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdSkupina" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Kod" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Kod" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Popis" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Popis" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Typ" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Typ" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_IdSkupina" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdSkupina" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_Kod" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Kod" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Original_Kod" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Kod" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_Popis" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Popis" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Original_Popis" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Popis" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_Typ" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Typ" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_Typ" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Typ" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="IdSkupina" DataSetColumn="IdSkupina" />
              <Mapping SourceColumn="Kod" DataSetColumn="Kod" />
              <Mapping SourceColumn="Popis" DataSetColumn="Popis" />
              <Mapping SourceColumn="Typ" DataSetColumn="Typ" />
              <Mapping SourceColumn="Poznamka" DataSetColumn="Poznamka" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ACC_HiearchieSkupinTableAdapter" GeneratorDataComponentClassName="ACC_HiearchieSkupinTableAdapter" Name="ACC_HiearchieSkupin" UserDataComponentName="ACC_HiearchieSkupinTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="AccessRightsMdbConnectionString (Settings)" DbObjectName="ACC_HiearchieSkupin" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO `ACC_HiearchieSkupin` (`IdSkupina`, `IdParent`) VALUES (?, ?)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IdSkupina" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdSkupina" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IdParent" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdParent" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT IdSkupina, IdParent FROM ACC_HiearchieSkupin</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="IdSkupina" DataSetColumn="IdSkupina" />
              <Mapping SourceColumn="IdParent" DataSetColumn="IdParent" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ACC_RoleTableAdapter" GeneratorDataComponentClassName="ACC_RoleTableAdapter" Name="ACC_Role" UserDataComponentName="ACC_RoleTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="AccessRightsMdbConnectionString (Settings)" DbObjectName="ACC_Role" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM `ACC_Role` WHERE ((`IdRole` = ?))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_IdRole" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdRole" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO `ACC_Role` (`IdRole`, `Kod`, `Popis`, `DbRole`, `Poznamka`, `VyzadujePokladni`, `VyzadujeSklUzivatel`) VALUES (?, ?, ?, ?, ?, ?, ?)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IdRole" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdRole" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Kod" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Kod" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Popis" Precision="0" ProviderType="LongVarWChar" Scale="0" Size="0" SourceColumn="Popis" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="DbRole" Precision="0" ProviderType="LongVarWChar" Scale="0" Size="0" SourceColumn="DbRole" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Poznamka" Precision="0" ProviderType="LongVarWChar" Scale="0" Size="0" SourceColumn="Poznamka" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="VyzadujePokladni" Precision="0" ProviderType="Boolean" Scale="0" Size="0" SourceColumn="VyzadujePokladni" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="VyzadujeSklUzivatel" Precision="0" ProviderType="Boolean" Scale="0" Size="0" SourceColumn="VyzadujeSklUzivatel" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT IdRole, Kod, Popis, DbRole, Poznamka, VyzadujePokladni, VyzadujeSklUzivatel FROM ACC_Role</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE `ACC_Role` SET `IdRole` = ?, `Kod` = ?, `Popis` = ?, `DbRole` = ?, `Poznamka` = ?, `VyzadujePokladni` = ?, `VyzadujeSklUzivatel` = ? WHERE ((`IdRole` = ?))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IdRole" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdRole" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Kod" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Kod" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Popis" Precision="0" ProviderType="LongVarWChar" Scale="0" Size="0" SourceColumn="Popis" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="DbRole" Precision="0" ProviderType="LongVarWChar" Scale="0" Size="0" SourceColumn="DbRole" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Poznamka" Precision="0" ProviderType="LongVarWChar" Scale="0" Size="0" SourceColumn="Poznamka" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="VyzadujePokladni" Precision="0" ProviderType="Boolean" Scale="0" Size="0" SourceColumn="VyzadujePokladni" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="VyzadujeSklUzivatel" Precision="0" ProviderType="Boolean" Scale="0" Size="0" SourceColumn="VyzadujeSklUzivatel" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_IdRole" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdRole" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="IdRole" DataSetColumn="IdRole" />
              <Mapping SourceColumn="Kod" DataSetColumn="Kod" />
              <Mapping SourceColumn="Popis" DataSetColumn="Popis" />
              <Mapping SourceColumn="DbRole" DataSetColumn="DbRole" />
              <Mapping SourceColumn="Poznamka" DataSetColumn="Poznamka" />
              <Mapping SourceColumn="VyzadujePokladni" DataSetColumn="VyzadujePokladni" />
              <Mapping SourceColumn="VyzadujeSklUzivatel" DataSetColumn="VyzadujeSklUzivatel" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ACC_SkupinyRoleTableAdapter" GeneratorDataComponentClassName="ACC_SkupinyRoleTableAdapter" Name="ACC_SkupinyRole" UserDataComponentName="ACC_SkupinyRoleTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="AccessRightsMdbConnectionString (Settings)" DbObjectName="ACC_SkupinyRole" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM `ACC_SkupinyRole` WHERE ((`IdSkupina` = ?) AND (`IdRole` = ?))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_IdSkupina" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdSkupina" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_IdRole" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdRole" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO `ACC_SkupinyRole` (`IdSkupina`, `IdRole`) VALUES (?, ?)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IdSkupina" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdSkupina" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IdRole" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdRole" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT IdSkupina, IdRole FROM ACC_SkupinyRole</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE `ACC_SkupinyRole` SET `IdSkupina` = ?, `IdRole` = ? WHERE ((`IdSkupina` = ?) AND (`IdRole` = ?))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IdSkupina" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdSkupina" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IdRole" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdRole" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_IdSkupina" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdSkupina" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_IdRole" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdRole" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="IdSkupina" DataSetColumn="IdSkupina" />
              <Mapping SourceColumn="IdRole" DataSetColumn="IdRole" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ACC_InstalaceTableAdapter" GeneratorDataComponentClassName="ACC_InstalaceTableAdapter" Name="ACC_Instalace" UserDataComponentName="ACC_InstalaceTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="AccessRightsMdbConnectionString (Settings)" DbObjectName="ACC_Instalace" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM `ACC_Instalace` WHERE ((`IdInstalace` = ?) AND ((? = 1 AND `Prefix` IS NULL) OR (`Prefix` = ?)) AND ((? = 1 AND `Nazev` IS NULL) OR (`Nazev` = ?)) AND ((? = 1 AND `Ulice` IS NULL) OR (`Ulice` = ?)) AND ((? = 1 AND `Misto` IS NULL) OR (`Misto` = ?)) AND ((? = 1 AND `Psc` IS NULL) OR (`Psc` = ?)) AND ((? = 1 AND `Stat` IS NULL) OR (`Stat` = ?)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_IdInstalace" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdInstalace" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_Prefix" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Prefix" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Original_Prefix" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Prefix" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_Nazev" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Nazev" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Original_Nazev" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Nazev" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_Ulice" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Ulice" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Original_Ulice" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Ulice" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_Misto" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Misto" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Original_Misto" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Misto" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_Psc" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Psc" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Original_Psc" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Psc" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_Stat" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Stat" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Original_Stat" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Stat" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO `ACC_Instalace` (`IdInstalace`, `Prefix`, `Nazev`, `Ulice`, `Misto`, `Psc`, `Stat`) VALUES (?, ?, ?, ?, ?, ?, ?)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IdInstalace" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdInstalace" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Prefix" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Prefix" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Nazev" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Nazev" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Ulice" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Ulice" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Misto" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Misto" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Psc" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Psc" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Stat" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Stat" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT IdInstalace, Prefix, Nazev, Ulice, Misto, Psc, Stat FROM ACC_Instalace</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE `ACC_Instalace` SET `IdInstalace` = ?, `Prefix` = ?, `Nazev` = ?, `Ulice` = ?, `Misto` = ?, `Psc` = ?, `Stat` = ? WHERE ((`IdInstalace` = ?) AND ((? = 1 AND `Prefix` IS NULL) OR (`Prefix` = ?)) AND ((? = 1 AND `Nazev` IS NULL) OR (`Nazev` = ?)) AND ((? = 1 AND `Ulice` IS NULL) OR (`Ulice` = ?)) AND ((? = 1 AND `Misto` IS NULL) OR (`Misto` = ?)) AND ((? = 1 AND `Psc` IS NULL) OR (`Psc` = ?)) AND ((? = 1 AND `Stat` IS NULL) OR (`Stat` = ?)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IdInstalace" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdInstalace" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Prefix" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Prefix" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Nazev" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Nazev" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Ulice" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Ulice" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Misto" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Misto" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Psc" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Psc" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Stat" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Stat" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_IdInstalace" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdInstalace" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_Prefix" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Prefix" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Original_Prefix" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Prefix" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_Nazev" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Nazev" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Original_Nazev" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Nazev" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_Ulice" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Ulice" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Original_Ulice" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Ulice" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_Misto" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Misto" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Original_Misto" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Misto" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_Psc" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Psc" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Original_Psc" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Psc" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_Stat" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="Stat" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Original_Stat" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Stat" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="IdInstalace" DataSetColumn="IdInstalace" />
              <Mapping SourceColumn="Prefix" DataSetColumn="Prefix" />
              <Mapping SourceColumn="Nazev" DataSetColumn="Nazev" />
              <Mapping SourceColumn="Ulice" DataSetColumn="Ulice" />
              <Mapping SourceColumn="Misto" DataSetColumn="Misto" />
              <Mapping SourceColumn="Psc" DataSetColumn="Psc" />
              <Mapping SourceColumn="Stat" DataSetColumn="Stat" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ACC_InstalaceRoleTableAdapter" GeneratorDataComponentClassName="ACC_InstalaceRoleTableAdapter" Name="ACC_InstalaceRole" UserDataComponentName="ACC_InstalaceRoleTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="AccessRightsMdbConnectionString (Settings)" DbObjectName="ACC_InstalaceRole" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM `ACC_InstalaceRole` WHERE ((`IdInstalace` = ?) AND (`IdRole` = ?))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_IdInstalace" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdInstalace" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_IdRole" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdRole" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO `ACC_InstalaceRole` (`IdInstalace`, `IdRole`) VALUES (?, ?)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IdInstalace" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdInstalace" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IdRole" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdRole" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT IdInstalace, IdRole FROM ACC_InstalaceRole</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE `ACC_InstalaceRole` SET `IdInstalace` = ?, `IdRole` = ? WHERE ((`IdInstalace` = ?) AND (`IdRole` = ?))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IdInstalace" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdInstalace" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IdRole" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdRole" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_IdInstalace" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdInstalace" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_IdRole" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="IdRole" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="IdInstalace" DataSetColumn="IdInstalace" />
              <Mapping SourceColumn="IdRole" DataSetColumn="IdRole" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="VerzeTableAdapter" GeneratorDataComponentClassName="VerzeTableAdapter" Name="Verze" UserDataComponentName="VerzeTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="AccessRightsMdbConnectionString (Settings)" DbObjectName="Verze" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM `Verze` WHERE ((`FunkceZarizeni` = ?))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_FunkceZarizeni" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="FunkceZarizeni" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO `Verze` (`Aplikace`, `Verze`, `FunkceZarizeni`) VALUES (?, ?, ?)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Aplikace" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Aplikace" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Verze" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Verze" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="FunkceZarizeni" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="FunkceZarizeni" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT * FROM Verze</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE `Verze` SET `Aplikace` = ?, `Verze` = ?, `FunkceZarizeni` = ? WHERE ((`FunkceZarizeni` = ?))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Aplikace" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Aplikace" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="Verze" Precision="0" ProviderType="VarWChar" Scale="0" Size="0" SourceColumn="Verze" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="FunkceZarizeni" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="FunkceZarizeni" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="Original_FunkceZarizeni" Precision="0" ProviderType="Integer" Scale="0" Size="0" SourceColumn="FunkceZarizeni" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="Aplikace" DataSetColumn="Aplikace" />
              <Mapping SourceColumn="Verze" DataSetColumn="Verze" />
              <Mapping SourceColumn="FunkceZarizeni" DataSetColumn="FunkceZarizeni" />
            </Mappings>
            <Sources />
          </TableAdapter>
        </Tables>
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="AccessRightsDataSet" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:EnableTableAdapterManager="True" msprop:Generator_DataSetName="AccessRightsDataSet" msprop:Generator_UserDSName="AccessRightsDataSet">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="ACC_KodPrava" msprop:Generator_TableClassName="ACC_KodPravaDataTable" msprop:Generator_TableVarName="tableACC_KodPrava" msprop:Generator_TablePropName="ACC_KodPrava" msprop:Generator_RowDeletingName="ACC_KodPravaRowDeleting" msprop:Generator_RowChangingName="ACC_KodPravaRowChanging" msprop:Generator_RowEvHandlerName="ACC_KodPravaRowChangeEventHandler" msprop:Generator_RowDeletedName="ACC_KodPravaRowDeleted" msprop:Generator_UserTableName="ACC_KodPrava" msprop:Generator_RowChangedName="ACC_KodPravaRowChanged" msprop:Generator_RowEvArgName="ACC_KodPravaRowChangeEvent" msprop:Generator_RowClassName="ACC_KodPravaRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="IdKodPrava" msprop:Generator_ColumnVarNameInTable="columnIdKodPrava" msprop:Generator_ColumnPropNameInRow="IdKodPrava" msprop:Generator_ColumnPropNameInTable="IdKodPravaColumn" msprop:Generator_UserColumnName="IdKodPrava" type="xs:int" />
              <xs:element name="TypAplikace" msprop:Generator_ColumnVarNameInTable="columnTypAplikace" msprop:Generator_ColumnPropNameInRow="TypAplikace" msprop:Generator_ColumnPropNameInTable="TypAplikaceColumn" msprop:Generator_UserColumnName="TypAplikace" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="KodPravaGuid" msprop:Generator_ColumnVarNameInTable="columnKodPravaGuid" msprop:Generator_ColumnPropNameInRow="KodPravaGuid" msprop:Generator_ColumnPropNameInTable="KodPravaGuidColumn" msprop:Generator_UserColumnName="KodPravaGuid" type="xs:string" minOccurs="0" />
              <xs:element name="KodPrava" msprop:Generator_ColumnVarNameInTable="columnKodPrava" msprop:Generator_ColumnPropNameInRow="KodPrava" msprop:Generator_ColumnPropNameInTable="KodPravaColumn" msprop:Generator_UserColumnName="KodPrava" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="ACC_KodPravaParametry" msprop:Generator_TableClassName="ACC_KodPravaParametryDataTable" msprop:Generator_TableVarName="tableACC_KodPravaParametry" msprop:Generator_TablePropName="ACC_KodPravaParametry" msprop:Generator_RowDeletingName="ACC_KodPravaParametryRowDeleting" msprop:Generator_RowChangingName="ACC_KodPravaParametryRowChanging" msprop:Generator_RowEvHandlerName="ACC_KodPravaParametryRowChangeEventHandler" msprop:Generator_RowDeletedName="ACC_KodPravaParametryRowDeleted" msprop:Generator_UserTableName="ACC_KodPravaParametry" msprop:Generator_RowChangedName="ACC_KodPravaParametryRowChanged" msprop:Generator_RowEvArgName="ACC_KodPravaParametryRowChangeEvent" msprop:Generator_RowClassName="ACC_KodPravaParametryRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="IdKodPrava" msprop:Generator_ColumnVarNameInTable="columnIdKodPrava" msprop:Generator_ColumnPropNameInRow="IdKodPrava" msprop:Generator_ColumnPropNameInTable="IdKodPravaColumn" msprop:Generator_UserColumnName="IdKodPrava" type="xs:int" minOccurs="0" />
              <xs:element name="IdParametr" msprop:Generator_ColumnVarNameInTable="columnIdParametr" msprop:Generator_ColumnPropNameInRow="IdParametr" msprop:Generator_ColumnPropNameInTable="IdParametrColumn" msprop:Generator_UserColumnName="IdParametr" type="xs:int" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="ACC_Parametr" msprop:Generator_TableClassName="ACC_ParametrDataTable" msprop:Generator_TableVarName="tableACC_Parametr" msprop:Generator_TablePropName="ACC_Parametr" msprop:Generator_RowDeletingName="ACC_ParametrRowDeleting" msprop:Generator_RowChangingName="ACC_ParametrRowChanging" msprop:Generator_RowEvHandlerName="ACC_ParametrRowChangeEventHandler" msprop:Generator_RowDeletedName="ACC_ParametrRowDeleted" msprop:Generator_UserTableName="ACC_Parametr" msprop:Generator_RowChangedName="ACC_ParametrRowChanged" msprop:Generator_RowEvArgName="ACC_ParametrRowChangeEvent" msprop:Generator_RowClassName="ACC_ParametrRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="IdParametr" msprop:Generator_ColumnVarNameInTable="columnIdParametr" msprop:Generator_ColumnPropNameInRow="IdParametr" msprop:Generator_ColumnPropNameInTable="IdParametrColumn" msprop:Generator_UserColumnName="IdParametr" type="xs:int" />
              <xs:element name="Popis" msprop:Generator_ColumnVarNameInTable="columnPopis" msprop:Generator_ColumnPropNameInRow="Popis" msprop:Generator_ColumnPropNameInTable="PopisColumn" msprop:Generator_UserColumnName="Popis" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Implementace" msprop:Generator_ColumnVarNameInTable="columnImplementace" msprop:Generator_ColumnPropNameInRow="Implementace" msprop:Generator_ColumnPropNameInTable="ImplementaceColumn" msprop:Generator_UserColumnName="Implementace" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Assembly" msprop:Generator_ColumnVarNameInTable="columnAssembly" msprop:Generator_ColumnPropNameInRow="Assembly" msprop:Generator_ColumnPropNameInTable="AssemblyColumn" msprop:Generator_UserColumnName="Assembly" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="255" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="ACC_PravaSkupin" msprop:Generator_TableClassName="ACC_PravaSkupinDataTable" msprop:Generator_TableVarName="tableACC_PravaSkupin" msprop:Generator_TablePropName="ACC_PravaSkupin" msprop:Generator_RowDeletingName="ACC_PravaSkupinRowDeleting" msprop:Generator_RowChangingName="ACC_PravaSkupinRowChanging" msprop:Generator_RowEvHandlerName="ACC_PravaSkupinRowChangeEventHandler" msprop:Generator_RowDeletedName="ACC_PravaSkupinRowDeleted" msprop:Generator_UserTableName="ACC_PravaSkupin" msprop:Generator_RowChangedName="ACC_PravaSkupinRowChanged" msprop:Generator_RowEvArgName="ACC_PravaSkupinRowChangeEvent" msprop:Generator_RowClassName="ACC_PravaSkupinRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="IdSkupina" msprop:Generator_ColumnVarNameInTable="columnIdSkupina" msprop:Generator_ColumnPropNameInRow="IdSkupina" msprop:Generator_ColumnPropNameInTable="IdSkupinaColumn" msprop:Generator_UserColumnName="IdSkupina" type="xs:int" minOccurs="0" />
              <xs:element name="IdPravo" msprop:Generator_ColumnVarNameInTable="columnIdPravo" msprop:Generator_ColumnPropNameInRow="IdPravo" msprop:Generator_ColumnPropNameInTable="IdPravoColumn" msprop:Generator_UserColumnName="IdPravo" type="xs:int" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="ACC_Pravo" msprop:Generator_TableClassName="ACC_PravoDataTable" msprop:Generator_TableVarName="tableACC_Pravo" msprop:Generator_TablePropName="ACC_Pravo" msprop:Generator_RowDeletingName="ACC_PravoRowDeleting" msprop:Generator_RowChangingName="ACC_PravoRowChanging" msprop:Generator_RowEvHandlerName="ACC_PravoRowChangeEventHandler" msprop:Generator_RowDeletedName="ACC_PravoRowDeleted" msprop:Generator_UserTableName="ACC_Pravo" msprop:Generator_RowChangedName="ACC_PravoRowChanged" msprop:Generator_RowEvArgName="ACC_PravoRowChangeEvent" msprop:Generator_RowClassName="ACC_PravoRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="IdPravo" msprop:Generator_ColumnVarNameInTable="columnIdPravo" msprop:Generator_ColumnPropNameInRow="IdPravo" msprop:Generator_ColumnPropNameInTable="IdPravoColumn" msprop:Generator_UserColumnName="IdPravo" type="xs:int" />
              <xs:element name="IdKodPrava" msprop:Generator_ColumnVarNameInTable="columnIdKodPrava" msprop:Generator_ColumnPropNameInRow="IdKodPrava" msprop:Generator_ColumnPropNameInTable="IdKodPravaColumn" msprop:Generator_UserColumnName="IdKodPrava" type="xs:int" minOccurs="0" />
              <xs:element name="Kod" msprop:Generator_ColumnVarNameInTable="columnKod" msprop:Generator_ColumnPropNameInRow="Kod" msprop:Generator_ColumnPropNameInTable="KodColumn" msprop:Generator_UserColumnName="Kod" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Popis" msprop:Generator_ColumnVarNameInTable="columnPopis" msprop:Generator_ColumnPropNameInRow="Popis" msprop:Generator_ColumnPropNameInTable="PopisColumn" msprop:Generator_UserColumnName="Popis" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="255" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="APP_Objekt" msprop:Generator_TableClassName="APP_ObjektDataTable" msprop:Generator_TableVarName="tableAPP_Objekt" msprop:Generator_TablePropName="APP_Objekt" msprop:Generator_RowDeletingName="APP_ObjektRowDeleting" msprop:Generator_RowChangingName="APP_ObjektRowChanging" msprop:Generator_RowEvHandlerName="APP_ObjektRowChangeEventHandler" msprop:Generator_RowDeletedName="APP_ObjektRowDeleted" msprop:Generator_UserTableName="APP_Objekt" msprop:Generator_RowChangedName="APP_ObjektRowChanged" msprop:Generator_RowEvArgName="APP_ObjektRowChangeEvent" msprop:Generator_RowClassName="APP_ObjektRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="IdObjekt" msprop:Generator_ColumnVarNameInTable="columnIdObjekt" msprop:Generator_ColumnPropNameInRow="IdObjekt" msprop:Generator_ColumnPropNameInTable="IdObjektColumn" msprop:Generator_UserColumnName="IdObjekt" type="xs:int" />
              <xs:element name="Assembly" msprop:Generator_ColumnVarNameInTable="columnAssembly" msprop:Generator_ColumnPropNameInRow="Assembly" msprop:Generator_ColumnPropNameInTable="AssemblyColumn" msprop:Generator_UserColumnName="Assembly" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="255" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Implementace" msprop:Generator_ColumnVarNameInTable="columnImplementace" msprop:Generator_ColumnPropNameInRow="Implementace" msprop:Generator_ColumnPropNameInTable="ImplementaceColumn" msprop:Generator_UserColumnName="Implementace" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="255" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Popis" msprop:Generator_ColumnVarNameInTable="columnPopis" msprop:Generator_ColumnPropNameInRow="Popis" msprop:Generator_ColumnPropNameInTable="PopisColumn" msprop:Generator_UserColumnName="Popis" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="255" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ObjektGuid" msprop:Generator_ColumnVarNameInTable="columnObjektGuid" msprop:Generator_ColumnPropNameInRow="ObjektGuid" msprop:Generator_ColumnPropNameInTable="ObjektGuidColumn" msprop:Generator_UserColumnName="ObjektGuid" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="255" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="ACC_KodPravaObjekty" msprop:Generator_TableClassName="ACC_KodPravaObjektyDataTable" msprop:Generator_TableVarName="tableACC_KodPravaObjekty" msprop:Generator_TablePropName="ACC_KodPravaObjekty" msprop:Generator_RowDeletingName="ACC_KodPravaObjektyRowDeleting" msprop:Generator_RowChangingName="ACC_KodPravaObjektyRowChanging" msprop:Generator_RowEvHandlerName="ACC_KodPravaObjektyRowChangeEventHandler" msprop:Generator_RowDeletedName="ACC_KodPravaObjektyRowDeleted" msprop:Generator_UserTableName="ACC_KodPravaObjekty" msprop:Generator_RowChangedName="ACC_KodPravaObjektyRowChanged" msprop:Generator_RowEvArgName="ACC_KodPravaObjektyRowChangeEvent" msprop:Generator_RowClassName="ACC_KodPravaObjektyRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="IdKodPrava" msprop:Generator_ColumnVarNameInTable="columnIdKodPrava" msprop:Generator_ColumnPropNameInRow="IdKodPrava" msprop:Generator_ColumnPropNameInTable="IdKodPravaColumn" msprop:Generator_UserColumnName="IdKodPrava" type="xs:int" minOccurs="0" />
              <xs:element name="IdObjekt" msprop:Generator_ColumnVarNameInTable="columnIdObjekt" msprop:Generator_ColumnPropNameInRow="IdObjekt" msprop:Generator_ColumnPropNameInTable="IdObjektColumn" msprop:Generator_UserColumnName="IdObjekt" type="xs:int" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="ACC_Skupina" msprop:Generator_TableClassName="ACC_SkupinaDataTable" msprop:Generator_TableVarName="tableACC_Skupina" msprop:Generator_TablePropName="ACC_Skupina" msprop:Generator_RowDeletingName="ACC_SkupinaRowDeleting" msprop:Generator_RowChangingName="ACC_SkupinaRowChanging" msprop:Generator_RowEvHandlerName="ACC_SkupinaRowChangeEventHandler" msprop:Generator_RowDeletedName="ACC_SkupinaRowDeleted" msprop:Generator_UserTableName="ACC_Skupina" msprop:Generator_RowChangedName="ACC_SkupinaRowChanged" msprop:Generator_RowEvArgName="ACC_SkupinaRowChangeEvent" msprop:Generator_RowClassName="ACC_SkupinaRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="IdSkupina" msprop:Generator_ColumnVarNameInTable="columnIdSkupina" msprop:Generator_ColumnPropNameInRow="IdSkupina" msprop:Generator_ColumnPropNameInTable="IdSkupinaColumn" msprop:Generator_UserColumnName="IdSkupina" type="xs:int" />
              <xs:element name="Kod" msprop:Generator_ColumnVarNameInTable="columnKod" msprop:Generator_ColumnPropNameInRow="Kod" msprop:Generator_ColumnPropNameInTable="KodColumn" msprop:Generator_UserColumnName="Kod" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Popis" msprop:Generator_ColumnVarNameInTable="columnPopis" msprop:Generator_ColumnPropNameInRow="Popis" msprop:Generator_ColumnPropNameInTable="PopisColumn" msprop:Generator_UserColumnName="Popis" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Typ" msprop:Generator_ColumnVarNameInTable="columnTyp" msprop:Generator_ColumnPropNameInRow="Typ" msprop:Generator_ColumnPropNameInTable="TypColumn" msprop:Generator_UserColumnName="Typ" type="xs:int" minOccurs="0" />
              <xs:element name="Poznamka" msprop:Generator_ColumnVarNameInTable="columnPoznamka" msprop:Generator_ColumnPropNameInRow="Poznamka" msprop:Generator_ColumnPropNameInTable="PoznamkaColumn" msprop:Generator_UserColumnName="Poznamka" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="ACC_HiearchieSkupin" msprop:Generator_TableClassName="ACC_HiearchieSkupinDataTable" msprop:Generator_TableVarName="tableACC_HiearchieSkupin" msprop:Generator_TablePropName="ACC_HiearchieSkupin" msprop:Generator_RowDeletingName="ACC_HiearchieSkupinRowDeleting" msprop:Generator_RowChangingName="ACC_HiearchieSkupinRowChanging" msprop:Generator_RowEvHandlerName="ACC_HiearchieSkupinRowChangeEventHandler" msprop:Generator_RowDeletedName="ACC_HiearchieSkupinRowDeleted" msprop:Generator_UserTableName="ACC_HiearchieSkupin" msprop:Generator_RowChangedName="ACC_HiearchieSkupinRowChanged" msprop:Generator_RowEvArgName="ACC_HiearchieSkupinRowChangeEvent" msprop:Generator_RowClassName="ACC_HiearchieSkupinRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="IdSkupina" msprop:Generator_ColumnVarNameInTable="columnIdSkupina" msprop:Generator_ColumnPropNameInRow="IdSkupina" msprop:Generator_ColumnPropNameInTable="IdSkupinaColumn" msprop:Generator_UserColumnName="IdSkupina" type="xs:int" minOccurs="0" />
              <xs:element name="IdParent" msprop:Generator_ColumnVarNameInTable="columnIdParent" msprop:Generator_ColumnPropNameInRow="IdParent" msprop:Generator_ColumnPropNameInTable="IdParentColumn" msprop:Generator_UserColumnName="IdParent" type="xs:int" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="ACC_Role" msprop:Generator_TableClassName="ACC_RoleDataTable" msprop:Generator_TableVarName="tableACC_Role" msprop:Generator_TablePropName="ACC_Role" msprop:Generator_RowDeletingName="ACC_RoleRowDeleting" msprop:Generator_RowChangingName="ACC_RoleRowChanging" msprop:Generator_RowEvHandlerName="ACC_RoleRowChangeEventHandler" msprop:Generator_RowDeletedName="ACC_RoleRowDeleted" msprop:Generator_UserTableName="ACC_Role" msprop:Generator_RowChangedName="ACC_RoleRowChanged" msprop:Generator_RowEvArgName="ACC_RoleRowChangeEvent" msprop:Generator_RowClassName="ACC_RoleRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="IdRole" msprop:Generator_ColumnVarNameInTable="columnIdRole" msprop:Generator_ColumnPropNameInRow="IdRole" msprop:Generator_ColumnPropNameInTable="IdRoleColumn" msprop:Generator_UserColumnName="IdRole" type="xs:int" />
              <xs:element name="Kod" msprop:Generator_ColumnVarNameInTable="columnKod" msprop:Generator_ColumnPropNameInRow="Kod" msprop:Generator_ColumnPropNameInTable="KodColumn" msprop:Generator_UserColumnName="Kod" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Popis" msprop:Generator_ColumnVarNameInTable="columnPopis" msprop:Generator_ColumnPropNameInRow="Popis" msprop:Generator_ColumnPropNameInTable="PopisColumn" msprop:Generator_UserColumnName="Popis" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="DbRole" msprop:Generator_ColumnVarNameInTable="columnDbRole" msprop:Generator_ColumnPropNameInRow="DbRole" msprop:Generator_ColumnPropNameInTable="DbRoleColumn" msprop:Generator_UserColumnName="DbRole" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="500" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Poznamka" msprop:Generator_ColumnVarNameInTable="columnPoznamka" msprop:Generator_ColumnPropNameInRow="Poznamka" msprop:Generator_ColumnPropNameInTable="PoznamkaColumn" msprop:Generator_UserColumnName="Poznamka" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="800" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="VyzadujePokladni" msprop:Generator_ColumnVarNameInTable="columnVyzadujePokladni" msprop:Generator_ColumnPropNameInRow="VyzadujePokladni" msprop:Generator_ColumnPropNameInTable="VyzadujePokladniColumn" msprop:Generator_UserColumnName="VyzadujePokladni" type="xs:boolean" minOccurs="0" />
              <xs:element name="VyzadujeSklUzivatel" msprop:Generator_ColumnVarNameInTable="columnVyzadujeSklUzivatel" msprop:Generator_ColumnPropNameInRow="VyzadujeSklUzivatel" msprop:Generator_ColumnPropNameInTable="VyzadujeSklUzivatelColumn" msprop:Generator_UserColumnName="VyzadujeSklUzivatel" type="xs:boolean" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="ACC_SkupinyRole" msprop:Generator_TableClassName="ACC_SkupinyRoleDataTable" msprop:Generator_TableVarName="tableACC_SkupinyRole" msprop:Generator_TablePropName="ACC_SkupinyRole" msprop:Generator_RowDeletingName="ACC_SkupinyRoleRowDeleting" msprop:Generator_RowChangingName="ACC_SkupinyRoleRowChanging" msprop:Generator_RowEvHandlerName="ACC_SkupinyRoleRowChangeEventHandler" msprop:Generator_RowDeletedName="ACC_SkupinyRoleRowDeleted" msprop:Generator_UserTableName="ACC_SkupinyRole" msprop:Generator_RowChangedName="ACC_SkupinyRoleRowChanged" msprop:Generator_RowEvArgName="ACC_SkupinyRoleRowChangeEvent" msprop:Generator_RowClassName="ACC_SkupinyRoleRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="IdSkupina" msprop:Generator_ColumnVarNameInTable="columnIdSkupina" msprop:Generator_ColumnPropNameInRow="IdSkupina" msprop:Generator_ColumnPropNameInTable="IdSkupinaColumn" msprop:Generator_UserColumnName="IdSkupina" type="xs:int" />
              <xs:element name="IdRole" msprop:Generator_ColumnVarNameInTable="columnIdRole" msprop:Generator_ColumnPropNameInRow="IdRole" msprop:Generator_ColumnPropNameInTable="IdRoleColumn" msprop:Generator_UserColumnName="IdRole" type="xs:int" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="ACC_Instalace" msprop:Generator_TableClassName="ACC_InstalaceDataTable" msprop:Generator_TableVarName="tableACC_Instalace" msprop:Generator_TablePropName="ACC_Instalace" msprop:Generator_RowDeletingName="ACC_InstalaceRowDeleting" msprop:Generator_RowChangingName="ACC_InstalaceRowChanging" msprop:Generator_RowEvHandlerName="ACC_InstalaceRowChangeEventHandler" msprop:Generator_RowDeletedName="ACC_InstalaceRowDeleted" msprop:Generator_UserTableName="ACC_Instalace" msprop:Generator_RowChangedName="ACC_InstalaceRowChanged" msprop:Generator_RowEvArgName="ACC_InstalaceRowChangeEvent" msprop:Generator_RowClassName="ACC_InstalaceRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="IdInstalace" msprop:Generator_ColumnVarNameInTable="columnIdInstalace" msprop:Generator_ColumnPropNameInRow="IdInstalace" msprop:Generator_ColumnPropNameInTable="IdInstalaceColumn" msprop:Generator_UserColumnName="IdInstalace" type="xs:int" />
              <xs:element name="Prefix" msprop:Generator_ColumnVarNameInTable="columnPrefix" msprop:Generator_ColumnPropNameInRow="Prefix" msprop:Generator_ColumnPropNameInTable="PrefixColumn" msprop:Generator_UserColumnName="Prefix" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Nazev" msprop:Generator_ColumnVarNameInTable="columnNazev" msprop:Generator_ColumnPropNameInRow="Nazev" msprop:Generator_ColumnPropNameInTable="NazevColumn" msprop:Generator_UserColumnName="Nazev" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="60" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Ulice" msprop:Generator_ColumnVarNameInTable="columnUlice" msprop:Generator_ColumnPropNameInRow="Ulice" msprop:Generator_ColumnPropNameInTable="UliceColumn" msprop:Generator_UserColumnName="Ulice" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="60" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Misto" msprop:Generator_ColumnVarNameInTable="columnMisto" msprop:Generator_ColumnPropNameInRow="Misto" msprop:Generator_ColumnPropNameInTable="MistoColumn" msprop:Generator_UserColumnName="Misto" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="60" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Psc" msprop:Generator_ColumnVarNameInTable="columnPsc" msprop:Generator_ColumnPropNameInRow="Psc" msprop:Generator_ColumnPropNameInTable="PscColumn" msprop:Generator_UserColumnName="Psc" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="12" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Stat" msprop:Generator_ColumnVarNameInTable="columnStat" msprop:Generator_ColumnPropNameInRow="Stat" msprop:Generator_ColumnPropNameInTable="StatColumn" msprop:Generator_UserColumnName="Stat" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="3" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="ACC_InstalaceRole" msprop:Generator_TableClassName="ACC_InstalaceRoleDataTable" msprop:Generator_TableVarName="tableACC_InstalaceRole" msprop:Generator_TablePropName="ACC_InstalaceRole" msprop:Generator_RowDeletingName="ACC_InstalaceRoleRowDeleting" msprop:Generator_RowChangingName="ACC_InstalaceRoleRowChanging" msprop:Generator_RowEvHandlerName="ACC_InstalaceRoleRowChangeEventHandler" msprop:Generator_RowDeletedName="ACC_InstalaceRoleRowDeleted" msprop:Generator_UserTableName="ACC_InstalaceRole" msprop:Generator_RowChangedName="ACC_InstalaceRoleRowChanged" msprop:Generator_RowEvArgName="ACC_InstalaceRoleRowChangeEvent" msprop:Generator_RowClassName="ACC_InstalaceRoleRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="IdInstalace" msprop:Generator_ColumnVarNameInTable="columnIdInstalace" msprop:Generator_ColumnPropNameInRow="IdInstalace" msprop:Generator_ColumnPropNameInTable="IdInstalaceColumn" msprop:Generator_UserColumnName="IdInstalace" type="xs:int" />
              <xs:element name="IdRole" msprop:Generator_ColumnVarNameInTable="columnIdRole" msprop:Generator_ColumnPropNameInRow="IdRole" msprop:Generator_ColumnPropNameInTable="IdRoleColumn" msprop:Generator_UserColumnName="IdRole" type="xs:int" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Verze" msprop:Generator_TableClassName="VerzeDataTable" msprop:Generator_TableVarName="tableVerze" msprop:Generator_RowChangedName="VerzeRowChanged" msprop:Generator_TablePropName="Verze" msprop:Generator_RowDeletingName="VerzeRowDeleting" msprop:Generator_RowChangingName="VerzeRowChanging" msprop:Generator_RowEvHandlerName="VerzeRowChangeEventHandler" msprop:Generator_RowDeletedName="VerzeRowDeleted" msprop:Generator_RowClassName="VerzeRow" msprop:Generator_UserTableName="Verze" msprop:Generator_RowEvArgName="VerzeRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Aplikace" msprop:Generator_ColumnVarNameInTable="columnAplikace" msprop:Generator_ColumnPropNameInRow="Aplikace" msprop:Generator_ColumnPropNameInTable="AplikaceColumn" msprop:Generator_UserColumnName="Aplikace" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Verze" msprop:Generator_ColumnVarNameInTable="columnVerze" msprop:Generator_ColumnPropNameInRow="Verze" msprop:Generator_ColumnPropNameInTable="VerzeColumn" msprop:Generator_UserColumnName="Verze" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="FunkceZarizeni" msprop:Generator_ColumnVarNameInTable="columnFunkceZarizeni" msprop:Generator_ColumnPropNameInRow="FunkceZarizeni" msprop:Generator_ColumnPropNameInTable="FunkceZarizeniColumn" msprop:Generator_UserColumnName="FunkceZarizeni" type="xs:int" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="KodPravaGuidContraint">
      <xs:selector xpath=".//mstns:ACC_KodPrava" />
      <xs:field xpath="mstns:KodPravaGuid" />
    </xs:unique>
    <xs:unique name="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:ACC_KodPrava" />
      <xs:field xpath="mstns:IdKodPrava" />
    </xs:unique>
    <xs:unique name="Constraint2">
      <xs:selector xpath=".//mstns:ACC_KodPrava" />
      <xs:field xpath="mstns:KodPrava" />
    </xs:unique>
    <xs:unique name="ACC_Parametr_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:ACC_Parametr" />
      <xs:field xpath="mstns:IdParametr" />
    </xs:unique>
    <xs:unique name="ACC_Pravo_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:ACC_Pravo" />
      <xs:field xpath="mstns:IdPravo" />
    </xs:unique>
    <xs:unique name="APP_Objekt_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:APP_Objekt" />
      <xs:field xpath="mstns:IdObjekt" />
    </xs:unique>
    <xs:unique name="APP_Objekt_Constraint2" msdata:ConstraintName="Constraint2">
      <xs:selector xpath=".//mstns:APP_Objekt" />
      <xs:field xpath="mstns:ObjektGuid" />
    </xs:unique>
    <xs:unique name="ACC_Skupina_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:ACC_Skupina" />
      <xs:field xpath="mstns:IdSkupina" />
    </xs:unique>
    <xs:unique name="ACC_Skupina_Constraint2" msdata:ConstraintName="Constraint2">
      <xs:selector xpath=".//mstns:ACC_Skupina" />
      <xs:field xpath="mstns:Kod" />
    </xs:unique>
    <xs:unique name="ACC_Role_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:ACC_Role" />
      <xs:field xpath="mstns:IdRole" />
    </xs:unique>
    <xs:unique name="ACC_SkupinyRole_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:ACC_SkupinyRole" />
      <xs:field xpath="mstns:IdSkupina" />
      <xs:field xpath="mstns:IdRole" />
    </xs:unique>
    <xs:unique name="ACC_Instalace_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:ACC_Instalace" />
      <xs:field xpath="mstns:IdInstalace" />
    </xs:unique>
    <xs:unique name="ACC_InstalaceRole_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:ACC_InstalaceRole" />
      <xs:field xpath="mstns:IdInstalace" />
      <xs:field xpath="mstns:IdRole" />
    </xs:unique>
    <xs:unique name="Verze_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Verze" />
      <xs:field xpath="mstns:FunkceZarizeni" />
    </xs:unique>
  </xs:element>
  <xs:annotation>
    <xs:appinfo>
      <msdata:Relationship name="ACC_KodPravaACC_KodPravaParametry" msdata:parent="ACC_KodPrava" msdata:child="ACC_KodPravaParametry" msdata:parentkey="IdKodPrava" msdata:childkey="IdKodPrava" msprop:Generator_UserChildTable="ACC_KodPravaParametry" msprop:Generator_ChildPropName="GetACC_KodPravaParametryRows" msprop:Generator_UserRelationName="ACC_KodPravaACC_KodPravaParametry" msprop:Generator_ParentPropName="ACC_KodPravaRow" msprop:Generator_RelationVarName="relationACC_KodPravaACC_KodPravaParametry" msprop:Generator_UserParentTable="ACC_KodPrava" />
      <msdata:Relationship name="ACC_ParametrACC_PravoParametry" msdata:parent="ACC_Parametr" msdata:child="ACC_KodPravaParametry" msdata:parentkey="IdParametr" msdata:childkey="IdParametr" msprop:Generator_UserChildTable="ACC_KodPravaParametry" msprop:Generator_ChildPropName="GetACC_KodPravaParametryRows" msprop:Generator_UserRelationName="ACC_ParametrACC_PravoParametry" msprop:Generator_ParentPropName="ACC_ParametrRow" msprop:Generator_RelationVarName="relationACC_ParametrACC_PravoParametry" msprop:Generator_UserParentTable="ACC_Parametr" />
      <msdata:Relationship name="ACC_PravoACC_PravaSkupin" msdata:parent="ACC_Pravo" msdata:child="ACC_PravaSkupin" msdata:parentkey="IdPravo" msdata:childkey="IdPravo" msprop:Generator_UserChildTable="ACC_PravaSkupin" msprop:Generator_ChildPropName="GetACC_PravaSkupinRows" msprop:Generator_UserRelationName="ACC_PravoACC_PravaSkupin" msprop:Generator_ParentPropName="ACC_PravoRow" msprop:Generator_RelationVarName="relationACC_PravoACC_PravaSkupin" msprop:Generator_UserParentTable="ACC_Pravo" />
      <msdata:Relationship name="ACC_KodPravaACC_Pravo" msdata:parent="ACC_KodPrava" msdata:child="ACC_Pravo" msdata:parentkey="IdKodPrava" msdata:childkey="IdKodPrava" msprop:Generator_UserChildTable="ACC_Pravo" msprop:Generator_ChildPropName="GetACC_PravoRows" msprop:Generator_UserRelationName="ACC_KodPravaACC_Pravo" msprop:Generator_ParentPropName="ACC_KodPravaRow" msprop:Generator_RelationVarName="relationACC_KodPravaACC_Pravo" msprop:Generator_UserParentTable="ACC_KodPrava" />
      <msdata:Relationship name="ACC_KodPravaACC_KodPravaObjekty" msdata:parent="ACC_KodPrava" msdata:child="ACC_KodPravaObjekty" msdata:parentkey="IdKodPrava" msdata:childkey="IdKodPrava" msprop:Generator_UserChildTable="ACC_KodPravaObjekty" msprop:Generator_ChildPropName="GetACC_KodPravaObjektyRows" msprop:Generator_UserRelationName="ACC_KodPravaACC_KodPravaObjekty" msprop:Generator_ParentPropName="ACC_KodPravaRow" msprop:Generator_RelationVarName="relationACC_KodPravaACC_KodPravaObjekty" msprop:Generator_UserParentTable="ACC_KodPrava" />
      <msdata:Relationship name="APP_ObjektACC_KodPravaObjekty" msdata:parent="APP_Objekt" msdata:child="ACC_KodPravaObjekty" msdata:parentkey="IdObjekt" msdata:childkey="IdObjekt" msprop:Generator_UserChildTable="ACC_KodPravaObjekty" msprop:Generator_ChildPropName="GetACC_KodPravaObjektyRows" msprop:Generator_UserRelationName="APP_ObjektACC_KodPravaObjekty" msprop:Generator_ParentPropName="APP_ObjektRow" msprop:Generator_RelationVarName="relationAPP_ObjektACC_KodPravaObjekty" msprop:Generator_UserParentTable="APP_Objekt" />
      <msdata:Relationship name="ACC_SkupinaACC_PravaSkupin" msdata:parent="ACC_Skupina" msdata:child="ACC_PravaSkupin" msdata:parentkey="IdSkupina" msdata:childkey="IdSkupina" msprop:Generator_UserChildTable="ACC_PravaSkupin" msprop:Generator_ChildPropName="GetACC_PravaSkupinRows" msprop:Generator_UserRelationName="ACC_SkupinaACC_PravaSkupin" msprop:Generator_ParentPropName="ACC_SkupinaRow" msprop:Generator_RelationVarName="relationACC_SkupinaACC_PravaSkupin" msprop:Generator_UserParentTable="ACC_Skupina" />
      <msdata:Relationship name="ACC_Skupina_ACC_HiearchieSkupinParent" msdata:parent="ACC_Skupina" msdata:child="ACC_HiearchieSkupin" msdata:parentkey="IdSkupina" msdata:childkey="IdParent" msprop:Generator_UserChildTable="ACC_HiearchieSkupin" msprop:Generator_ChildPropName="GetACC_HiearchieSkupinRowsByACC_Skupina_ACC_HiearchieSkupinParent" msprop:Generator_UserRelationName="ACC_Skupina_ACC_HiearchieSkupinParent" msprop:Generator_ParentPropName="ACC_SkupinaRowByACC_Skupina_ACC_HiearchieSkupinParent" msprop:Generator_RelationVarName="relationACC_Skupina_ACC_HiearchieSkupinParent" msprop:Generator_UserParentTable="ACC_Skupina" />
      <msdata:Relationship name="ACC_SkupinaACC_HiearchieSkupinSkupina" msdata:parent="ACC_Skupina" msdata:child="ACC_HiearchieSkupin" msdata:parentkey="IdSkupina" msdata:childkey="IdSkupina" msprop:Generator_UserChildTable="ACC_HiearchieSkupin" msprop:Generator_ChildPropName="GetACC_HiearchieSkupinRowsByACC_SkupinaACC_HiearchieSkupinSkupina" msprop:Generator_UserRelationName="ACC_SkupinaACC_HiearchieSkupinSkupina" msprop:Generator_ParentPropName="ACC_SkupinaRowByACC_SkupinaACC_HiearchieSkupinSkupina" msprop:Generator_RelationVarName="relationACC_SkupinaACC_HiearchieSkupinSkupina" msprop:Generator_UserParentTable="ACC_Skupina" />
      <msdata:Relationship name="ACC_RoleACC_SkupinyRole" msdata:parent="ACC_Role" msdata:child="ACC_SkupinyRole" msdata:parentkey="IdRole" msdata:childkey="IdRole" msprop:Generator_UserChildTable="ACC_SkupinyRole" msprop:Generator_ChildPropName="GetACC_SkupinyRoleRows" msprop:Generator_UserRelationName="ACC_RoleACC_SkupinyRole" msprop:Generator_ParentPropName="ACC_RoleRow" msprop:Generator_RelationVarName="relationACC_RoleACC_SkupinyRole" msprop:Generator_UserParentTable="ACC_Role" />
      <msdata:Relationship name="ACC_SkupinaACC_SkupinyRole" msdata:parent="ACC_Skupina" msdata:child="ACC_SkupinyRole" msdata:parentkey="IdSkupina" msdata:childkey="IdSkupina" msprop:Generator_UserChildTable="ACC_SkupinyRole" msprop:Generator_ChildPropName="GetACC_SkupinyRoleRows" msprop:Generator_UserRelationName="ACC_SkupinaACC_SkupinyRole" msprop:Generator_ParentPropName="ACC_SkupinaRow" msprop:Generator_RelationVarName="relationACC_SkupinaACC_SkupinyRole" msprop:Generator_UserParentTable="ACC_Skupina" />
      <msdata:Relationship name="ACC_InstalaceACC_InstalaceRole" msdata:parent="ACC_Instalace" msdata:child="ACC_InstalaceRole" msdata:parentkey="IdInstalace" msdata:childkey="IdInstalace" msprop:Generator_UserChildTable="ACC_InstalaceRole" msprop:Generator_ChildPropName="GetACC_InstalaceRoleRows" msprop:Generator_UserRelationName="ACC_InstalaceACC_InstalaceRole" msprop:Generator_ParentPropName="ACC_InstalaceRow" msprop:Generator_RelationVarName="relationACC_InstalaceACC_InstalaceRole" msprop:Generator_UserParentTable="ACC_Instalace" />
      <msdata:Relationship name="ACC_RoleACC_InstalaceRole" msdata:parent="ACC_Role" msdata:child="ACC_InstalaceRole" msdata:parentkey="IdRole" msdata:childkey="IdRole" msprop:Generator_UserChildTable="ACC_InstalaceRole" msprop:Generator_ChildPropName="GetACC_InstalaceRoleRows" msprop:Generator_UserRelationName="ACC_RoleACC_InstalaceRole" msprop:Generator_ParentPropName="ACC_RoleRow" msprop:Generator_RelationVarName="relationACC_RoleACC_InstalaceRole" msprop:Generator_UserParentTable="ACC_Role" />
    </xs:appinfo>
  </xs:annotation>
</xs:schema>