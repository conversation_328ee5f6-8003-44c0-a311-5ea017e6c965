using Anete.ApiServices;
using Anete.AutoUpgrades.Interface.AppServices;
using Anete.Common.Core.Interface.AppServices.SupportMessage.MessageQueue;
using Anete.Common.Wcf;
using Anete.Utils.AppServices.SupportMessage.MessageQueue;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.ServiceModel;
using System.ServiceModel.Security;

namespace Anete.AutoUpgrades.AppServices.SupportMessage.MessageQueue
{
	/// <summary>
	/// Transport hlaseni do AutoUpdater pres WCF sluzbu
	/// </summary>
	public class AutoUpdaterTechSupportMessageTransport : MessageTransportBase<ITechSupportMessageEntry>
	{
		#region private static fields...
		/// <summary>
		/// Seznam ocekavanych vyjimek
		/// </summary>
		private readonly IEnumerable<Type> _expectedExceptions = new Type[]
		{
			typeof(MessageSecurityException),
			typeof(CommunicationException),
			typeof(TimeoutException),
			// uz se nepouziva WCF, vyjimka v pripade vypadku spojeni je ApiServiceConnectionException
			typeof(ApiServiceConnectionException)
		};
		#endregion

		#region protected overrides...
		/// <summary>
		/// Jedna se o beznou provozni vyjimku?
		/// </summary>
		/// <param name="ex">Vyjimka</param>
		/// <param name="nextTryDelay">Po jake prodleve v mSec opakovat prenos</param>
		/// <returns>
		/// Vraci true, pokud jde o vyjimku, ktera je povazovana za beznou provozni. Takove vyjimky se jenom zaloguji a
		/// transport se opakuje. Pokud vraci false, zaloguje se neocekavana vyjimka a restartuje se odesilaci thread.
		/// </returns>
		protected override bool IsExpectedExceptionInt(Exception ex, out TimeSpan nextTryDelay)
		{
			if (_expectedExceptions.Any(excType => excType.IsAssignableFrom(ex.GetType())))
			{
				nextTryDelay = TimeSpan.FromSeconds(30);
				return true;
			}

			nextTryDelay = TimeSpan.MinValue;
			return false;
		}

		/// <summary>
		/// Odeslani hlaseni, interni metoda
		/// </summary>
		/// <param name="message">Hlaseni, ktere se ma odesilat</param>
		protected override void SendInt(ITechSupportMessageEntry message)
		{
#if !NETFRAMEWORK
			throw new PlatformNotSupportedException();
#else
			string adress = AutoUpgradesServiceConfig.GetTechSupportMessageServiceAdress();
			var techSupportBinding = TechSupportMessageServiceNetNamedPipeBindingFactory.CreateBinding();
			// ToJNOTE: 28.08.2015 Zde bylo puvodne new NetNamedPipeBinding() a promenna techSupportBinding nebyla vyuzita. Pokladam za chybu.

			WcfProxyCaller.Call<ITechSupportMessageService>(null, adress, techSupportBinding,
				proxy =>
				{
					proxy.SendMessage(message);
				});
#endif
		}
		#endregion
	}
}