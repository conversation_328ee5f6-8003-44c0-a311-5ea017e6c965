//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Anete.Common.Data.Interface.AppServices.ConnectionString {
    using System;
    
    
    /// <summary>
    /// A strongly-typed resource class, for looking up localized strings, formatting them, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilderEx class via the ResXFileCodeGeneratorEx custom tool.
    // To add or remove a member, edit your .ResX file then rerun the ResXFileCodeGeneratorEx custom tool or rebuild your VS.NET project.
    // Copyright (c) Dmytro Kryvko 2006-2018 (http://dmytro.kryvko.googlepages.com/)
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("DMKSoftware.CodeGenerators.Tools.StronglyTypedResourceBuilderEx", "2.6.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Naming", "CA1724:TypeNamesShouldNotMatchNamespaces")]
    public partial class AuthenticationTypeSR {
        
        private static global::System.Resources.ResourceManager _resourceManager;
        
        private static object _internalSyncObject;
        
        private static global::System.Globalization.CultureInfo _resourceCulture;
        
        /// <summary>
        /// Initializes a AuthenticationTypeSR object.
        /// </summary>
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        public AuthenticationTypeSR() {
        }
        
        /// <summary>
        /// Thread safe lock object used by this class.
        /// </summary>
        public static object InternalSyncObject {
            get {
                if (object.ReferenceEquals(_internalSyncObject, null)) {
                    global::System.Threading.Interlocked.CompareExchange(ref _internalSyncObject, new object(), null);
                }
                return _internalSyncObject;
            }
        }
        
        /// <summary>
        /// Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(_resourceManager, null)) {
                    global::System.Threading.Monitor.Enter(InternalSyncObject);
                    try {
                        if (object.ReferenceEquals(_resourceManager, null)) {
                            global::System.Threading.Interlocked.Exchange(ref _resourceManager, new global::System.Resources.ResourceManager("Anete.Common.Data.Interface.AppServices.ConnectionString.AuthenticationTypeSR", typeof(AuthenticationTypeSR).Assembly));
                        }
                    }
                    finally {
                        global::System.Threading.Monitor.Exit(InternalSyncObject);
                    }
                }
                return _resourceManager;
            }
        }
        
        /// <summary>
        /// Overrides the current thread's CurrentUICulture property for all
        /// resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return _resourceCulture;
            }
            set {
                _resourceCulture = value;
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Windows autentizace'.
        /// </summary>
        public static string AuthenticationType_IntegratedSecurity {
            get {
                return ResourceManager.GetString(ResourceNames.AuthenticationType_IntegratedSecurity, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Použít Windows autentizaci.'.
        /// </summary>
        public static string AuthenticationType_IntegratedSecurity_Desc {
            get {
                return ResourceManager.GetString(ResourceNames.AuthenticationType_IntegratedSecurity_Desc, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Použít přihlašovací údaje z primárního spojení'.
        /// </summary>
        public static string AuthenticationType_PrimaryCredentials {
            get {
                return ResourceManager.GetString(ResourceNames.AuthenticationType_PrimaryCredentials, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Pro přihlášení se použijí přihlašovací údaje z existujícího (primárního) spojení.'.
        /// </summary>
        public static string AuthenticationType_PrimaryCredentials_Desc {
            get {
                return ResourceManager.GetString(ResourceNames.AuthenticationType_PrimaryCredentials_Desc, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Uživatelské jméno a heslo'.
        /// </summary>
        public static string AuthenticationType_UsernameAndPassword {
            get {
                return ResourceManager.GetString(ResourceNames.AuthenticationType_UsernameAndPassword, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Použít autentizaci pomocí uživatelského jména a hesla.'.
        /// </summary>
        public static string AuthenticationType_UsernameAndPassword_Desc {
            get {
                return ResourceManager.GetString(ResourceNames.AuthenticationType_UsernameAndPassword_Desc, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Lists all the resource names as constant string fields.
        /// </summary>
        public class ResourceNames {
            
            /// <summary>
            /// Stores the resource name 'AuthenticationType_IntegratedSecurity'.
            /// </summary>
            public const string AuthenticationType_IntegratedSecurity = "AuthenticationType_IntegratedSecurity";
            
            /// <summary>
            /// Stores the resource name 'AuthenticationType_IntegratedSecurity_Desc'.
            /// </summary>
            public const string AuthenticationType_IntegratedSecurity_Desc = "AuthenticationType_IntegratedSecurity_Desc";
            
            /// <summary>
            /// Stores the resource name 'AuthenticationType_PrimaryCredentials'.
            /// </summary>
            public const string AuthenticationType_PrimaryCredentials = "AuthenticationType_PrimaryCredentials";
            
            /// <summary>
            /// Stores the resource name 'AuthenticationType_PrimaryCredentials_Desc'.
            /// </summary>
            public const string AuthenticationType_PrimaryCredentials_Desc = "AuthenticationType_PrimaryCredentials_Desc";
            
            /// <summary>
            /// Stores the resource name 'AuthenticationType_UsernameAndPassword'.
            /// </summary>
            public const string AuthenticationType_UsernameAndPassword = "AuthenticationType_UsernameAndPassword";
            
            /// <summary>
            /// Stores the resource name 'AuthenticationType_UsernameAndPassword_Desc'.
            /// </summary>
            public const string AuthenticationType_UsernameAndPassword_Desc = "AuthenticationType_UsernameAndPassword_Desc";
        }
    }
}
