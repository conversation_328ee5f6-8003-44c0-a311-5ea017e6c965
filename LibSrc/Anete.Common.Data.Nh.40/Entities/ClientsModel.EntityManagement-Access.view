<?xml version="1.0" encoding="utf-8"?>
<EntityDeveloperDiagram>
  <Diagram Version="1.22.3.0">
    <DiagramModel>
      <Model xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xsi:type="ContextVwModel">
        <CustomProperties>
          <OID>0</OID>
          <BackgroundColor>Window</BackgroundColor>
        </CustomProperties>
        <Children>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>1</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>2</OID>
                  <Parent>1</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>3</OID>
                      <Parent>2</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>280 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>09102ece-57ac-44ec-8f5e-6424ba1d1cb9</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>4</OID>
                      <Parent>2</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>280 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>cf050459-c152-4ed4-b2d0-2702ff52e62e</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>5</OID>
                      <Parent>2</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>280 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>51262071-4e51-4893-8ff6-08434fdc0a9b</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>281 px</Width>
                  <Height>55 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>55 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>6</OID>
                  <Parent>1</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>7</OID>
                      <Parent>6</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>280 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>5e144004-e140-4b64-8e49-bff7aedc9bdd</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>54 px</Y>
                </Location>
                <Size>
                  <Width>281 px</Width>
                  <Height>37 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>37 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>-112 px</X>
              <Y>-48 px</Y>
            </Location>
            <Size>
              <Width>286 px</Width>
              <Height>140 px</Height>
            </Size>
            <MinSize>
              <Width>120 px</Width>
              <Height>140 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>140 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>17a86b93-829f-4354-9232-d7a1c49fb151</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>8</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>9</OID>
                  <Parent>8</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>10</OID>
                      <Parent>9</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>280 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>c75649ec-dbaa-4e5b-b049-d49adc70d824</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>11</OID>
                      <Parent>9</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>280 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>25aadb15-c69b-40e6-af90-7b38f5fb295a</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>12</OID>
                      <Parent>9</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>280 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>f87ab9b7-c07f-43e8-9f4c-dfb2289ffd2b</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>281 px</Width>
                  <Height>55 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>55 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>13</OID>
                  <Parent>8</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>14</OID>
                      <Parent>13</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>280 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>0f633d77-1146-4b69-8bf2-3244b34c41ad</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>54 px</Y>
                </Location>
                <Size>
                  <Width>281 px</Width>
                  <Height>37 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>37 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>-72 px</X>
              <Y>-232 px</Y>
            </Location>
            <Size>
              <Width>286 px</Width>
              <Height>140 px</Height>
            </Size>
            <MinSize>
              <Width>120 px</Width>
              <Height>140 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>140 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>571ae8dc-508d-4d15-823a-52152ee02cf8</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>15</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>16</OID>
                  <Parent>15</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>17</OID>
                      <Parent>16</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>244 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>403c43d0-8f8d-49ab-b25a-713971719377</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>18</OID>
                      <Parent>16</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>244 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>3fbdd257-63e0-40b2-a779-65bda0f1ca6b</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>19</OID>
                      <Parent>16</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>244 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>3805de7f-221e-4b4e-bc50-22d25faef033</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>20</OID>
                      <Parent>16</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>54.5 px</Y>
                    </Location>
                    <Size>
                      <Width>244 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>566fcfe1-0341-4559-8999-8ea1afc27bb4</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>21</OID>
                      <Parent>16</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>72.5 px</Y>
                    </Location>
                    <Size>
                      <Width>244 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>da96a70d-57ad-4318-b296-031a580038a8</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>22</OID>
                      <Parent>16</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>90.5 px</Y>
                    </Location>
                    <Size>
                      <Width>244 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>77b7c803-74fc-4205-a701-ed4372fbb0a8</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>23</OID>
                      <Parent>16</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>108.5 px</Y>
                    </Location>
                    <Size>
                      <Width>244 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>1cf868d0-0b81-456d-9a45-721ef4d405cb</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>24</OID>
                      <Parent>16</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>126.5 px</Y>
                    </Location>
                    <Size>
                      <Width>244 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>9841237d-0ed5-4d02-82e9-63ba98c42ef0</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>25</OID>
                      <Parent>16</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>144.5 px</Y>
                    </Location>
                    <Size>
                      <Width>244 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>7c680770-06c0-4de0-9ea3-f2f7e4c6557f</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>26</OID>
                      <Parent>16</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>162.5 px</Y>
                    </Location>
                    <Size>
                      <Width>244 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>b3baf854-e39c-4551-aaeb-48c482ba3077</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>27</OID>
                      <Parent>16</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>180.5 px</Y>
                    </Location>
                    <Size>
                      <Width>244 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>c0c01060-eb03-4391-beb4-28202bafed95</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>28</OID>
                      <Parent>16</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>198.5 px</Y>
                    </Location>
                    <Size>
                      <Width>244 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>030e88b7-1507-46bb-9cae-66f1d988f42d</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>29</OID>
                      <Parent>16</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>216.5 px</Y>
                    </Location>
                    <Size>
                      <Width>244 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>7139cbfd-68aa-497e-9b77-7ced8a3cb6e2</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>30</OID>
                      <Parent>16</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>234.5 px</Y>
                    </Location>
                    <Size>
                      <Width>244 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>099c702e-4142-4c43-b6ce-346324731165</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>31</OID>
                      <Parent>16</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>252.5 px</Y>
                    </Location>
                    <Size>
                      <Width>244 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>946809be-5fbc-4044-936f-5d35636e1f5c</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>32</OID>
                      <Parent>16</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>270.5 px</Y>
                    </Location>
                    <Size>
                      <Width>244 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>5e8a080b-138b-4595-8311-e59fb12150ae</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>245 px</Width>
                  <Height>289 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>289 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>33</OID>
                  <Parent>15</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>34</OID>
                      <Parent>33</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>244 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>4728dff5-b105-4aa8-a653-463d76b3b424</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>35</OID>
                      <Parent>33</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>18 px</Y>
                    </Location>
                    <Size>
                      <Width>244 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>43634e7b-d0ab-4b41-889e-78d732bf7043</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>36</OID>
                      <Parent>33</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>36 px</Y>
                    </Location>
                    <Size>
                      <Width>244 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>e764bb59-8011-4fe2-9742-cfd4103a19d7</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>37</OID>
                      <Parent>33</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>54 px</Y>
                    </Location>
                    <Size>
                      <Width>244 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>0c8a6f94-8928-442e-9ebe-b4c788509794</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>38</OID>
                      <Parent>33</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>72 px</Y>
                    </Location>
                    <Size>
                      <Width>244 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>3e4a5cfa-461a-4936-a3af-f73d22831412</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>39</OID>
                      <Parent>33</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>90 px</Y>
                    </Location>
                    <Size>
                      <Width>244 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>67900fc2-9a8b-456a-9e0d-286b122fff73</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>40</OID>
                      <Parent>33</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>108 px</Y>
                    </Location>
                    <Size>
                      <Width>244 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>6617ff69-6ec6-4292-842d-dde4a4d3e81c</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>41</OID>
                      <Parent>33</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>126 px</Y>
                    </Location>
                    <Size>
                      <Width>244 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>2ecb8d59-6cc3-4be4-b50c-06e785114473</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>42</OID>
                      <Parent>33</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>144 px</Y>
                    </Location>
                    <Size>
                      <Width>244 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>f7b11e52-6dec-4fc7-8fa9-d51d60eadc2f</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>43</OID>
                      <Parent>33</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>162 px</Y>
                    </Location>
                    <Size>
                      <Width>244 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>4d307d36-56d3-4424-928a-de7412b51ad9</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>44</OID>
                      <Parent>33</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>180 px</Y>
                    </Location>
                    <Size>
                      <Width>244 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>7a13f738-a19b-4237-adf2-4ac03a62a3db</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>45</OID>
                      <Parent>33</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>198 px</Y>
                    </Location>
                    <Size>
                      <Width>244 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>81ed8a01-6a48-4b8d-b0a0-6faf4d901f8c</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>46</OID>
                      <Parent>33</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>216 px</Y>
                    </Location>
                    <Size>
                      <Width>244 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>c8f30a0b-d258-4d2c-8836-f8cadb09f997</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>47</OID>
                      <Parent>33</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>234 px</Y>
                    </Location>
                    <Size>
                      <Width>244 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>d6af905e-f51a-4087-8547-fd6c7666c705</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>48</OID>
                      <Parent>33</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>252 px</Y>
                    </Location>
                    <Size>
                      <Width>244 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>36dbe5ec-4834-42d8-9424-003031b1a9dc</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>49</OID>
                      <Parent>33</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>34ce8692-4ff6-4edb-bec6-49e6f2b5a045</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>50</OID>
                      <Parent>33</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>286fa47a-ff46-4f0a-a7ad-d2d3148ce259</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>51</OID>
                      <Parent>33</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>27cc73c4-bf52-4d63-a872-4da98955341e</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>52</OID>
                      <Parent>33</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>3a8ef8a4-79eb-4812-930f-570bae5199e2</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>53</OID>
                      <Parent>33</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>8409ad2d-4033-4f53-8ac4-80a2465f95fc</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>288 px</Y>
                </Location>
                <Size>
                  <Width>245 px</Width>
                  <Height>289 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>289 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>248 px</X>
              <Y>8 px</Y>
            </Location>
            <Size>
              <Width>250 px</Width>
              <Height>626 px</Height>
            </Size>
            <MinSize>
              <Width>120 px</Width>
              <Height>626 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>626 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>3fbc212a-5f6c-4e0b-b62e-dc15a6fe8dbf</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>54</OID>
              <OutModel>8</OutModel>
              <InModel>15</InModel>
              <OutPort>55</OutPort>
              <InPort>56</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>82.9425270094678 px</X>
                  <Y>15.1724134907097 px</Y>
                </Location>
                <Anchors>None</Anchors>
                <Align>Top</Align>
                <CustomProperties>
                  <OID>56</OID>
                  <PortType>FixedAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>152.459769673952 px</X>
                  <Y>Undefined</Y>
                </Location>
                <Anchors>Left Right</Anchors>
                <Align>Bottom</Align>
                <CustomProperties>
                  <OID>55</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <BendPoints>
              <PointD>
                <X>80.4597696739516 px</X>
                <Y>-64 px</Y>
              </PointD>
              <PointD>
                <X>330.942527009468 px</X>
                <Y>-64 px</Y>
              </PointD>
            </BendPoints>
            <IsManuallyRouted>true</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>b31283a0-f871-4247-bef8-5af79e73197f</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>57</OID>
              <OutModel>1</OutModel>
              <InModel>15</InModel>
              <OutPort>58</OutPort>
              <InPort>59</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>42 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Left</Align>
                <CustomProperties>
                  <OID>59</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>Undefined</X>
                  <Y>98 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Right</Align>
                <CustomProperties>
                  <OID>58</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>53639444-76ff-4ecf-8dc1-9d2abc8ee838</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>60</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>61</OID>
                  <Parent>60</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>62</OID>
                      <Parent>61</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>234 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>90de3589-504a-47fc-a31c-bd55344a8ba2</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>63</OID>
                      <Parent>61</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>234 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>7589b4b9-26a6-4147-baaf-fd49a583175d</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>64</OID>
                      <Parent>61</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>234 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>45b51528-6ff2-4a5c-97a9-cfb82f414b3b</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>235 px</Width>
                  <Height>55 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>55 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>65</OID>
                  <Parent>60</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>66</OID>
                      <Parent>65</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>234 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>16d11510-00b5-44d3-a48f-f2ec06356d60</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>54 px</Y>
                </Location>
                <Size>
                  <Width>235 px</Width>
                  <Height>37 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>37 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>792 px</X>
              <Y>56 px</Y>
            </Location>
            <Size>
              <Width>240 px</Width>
              <Height>140 px</Height>
            </Size>
            <MinSize>
              <Width>120 px</Width>
              <Height>140 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>140 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>a829141a-2dcc-48b0-813f-992ce6783bf7</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>67</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>68</OID>
                  <Parent>67</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>69</OID>
                      <Parent>68</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>74af40ef-e5b7-4d3b-a520-b18ee737cbf5</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>70</OID>
                      <Parent>68</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>46cdbc70-6fd8-4757-a4e6-d9ab7e4f362f</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>71</OID>
                      <Parent>68</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>1ef25272-4a9f-4795-8be9-40cfab4ac600</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>72</OID>
                      <Parent>68</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>54.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>db6829e4-8bb1-432e-897a-b86203c39794</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>73</OID>
                      <Parent>68</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>72.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>a267e94a-03ea-4126-bb06-bc7e260a01b9</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>74</OID>
                      <Parent>68</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>90.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>c3741c7e-966b-496b-8b4b-c1e8fbd5f5b8</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>75</OID>
                      <Parent>68</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>108.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>14133290-be4d-4617-bdfd-b3b3037a7f37</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>76</OID>
                      <Parent>68</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>126.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>01cd939f-dac5-418b-a8b9-e99f0e9b96ff</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>77</OID>
                      <Parent>68</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>144.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>5509ba85-081a-4ed9-a6a1-30fd9d5aadd7</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>78</OID>
                      <Parent>68</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>162.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>c9346b9f-c0ba-42b6-aae8-2ea5ad2329cd</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>79</OID>
                      <Parent>68</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>180.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>4d684bc7-acc2-467f-a4a3-a78585734685</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>80</OID>
                      <Parent>68</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>198.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>5037f04a-a786-4e62-8adf-5b2ab066cfba</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>81</OID>
                      <Parent>68</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>216.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>7a361909-cdbd-4ace-98d3-5a80da117b0e</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>145 px</Width>
                  <Height>235 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>235 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>82</OID>
                  <Parent>67</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>83</OID>
                      <Parent>82</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>a7308b7c-f647-4c0a-b075-c1a34f063f53</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>84</OID>
                      <Parent>82</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>18 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>11473565-a19c-4c9b-9a5d-f8098088b560</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>85</OID>
                      <Parent>82</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>36 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>6a41959f-238d-4279-b841-57b6352440b0</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>86</OID>
                      <Parent>82</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>54 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>f2df13df-f548-4a7b-a733-92f665d4dc6c</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>87</OID>
                      <Parent>82</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>72 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>735a3ca7-45fb-4097-acfd-db149ab2a9f7</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>88</OID>
                      <Parent>82</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>90 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>83ef368d-5ba9-4bc7-a25b-d45ff4fbd9bf</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>89</OID>
                      <Parent>82</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>108 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>f2e5ce90-4578-48fb-910a-31c6866cc201</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>90</OID>
                      <Parent>82</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>126 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>4d8d092e-af3f-4e0e-8e89-c01181880289</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>91</OID>
                      <Parent>82</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>144 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>82f02ebd-fbfd-493b-ba6f-c7268fcabeeb</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>92</OID>
                      <Parent>82</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>162 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>572bdab1-a582-4292-a7ff-6cc83089eaf9</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>93</OID>
                      <Parent>82</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>180 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>b918e267-99f5-44f2-9430-f17a14fc24dd</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>94</OID>
                      <Parent>82</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>198 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>3b9c6fd8-2d96-4ff0-9b4a-735e7f6ccad8</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>95</OID>
                      <Parent>82</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>216 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>5de98ee8-db74-4a3c-8784-5fa1172d9619</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>96</OID>
                      <Parent>82</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>234 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>750cb860-7ced-43bf-adea-b83cf6854113</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>97</OID>
                      <Parent>82</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>252 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>a1dd1a1d-5270-462e-89b3-fb0763b43115</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>98</OID>
                      <Parent>82</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>270 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>ef0a2191-4750-40a1-8850-c6d1ca6898a7</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>99</OID>
                      <Parent>82</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>288 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>f837309a-e2da-4b77-bc88-052263e08a11</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>100</OID>
                      <Parent>82</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>306 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>5f251fa3-78a3-4e16-846a-67ab605a30b9</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>101</OID>
                      <Parent>82</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>324 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>b3e0f7cd-30de-4a81-8ff1-37c8e0c76c45</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>102</OID>
                      <Parent>82</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>342 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>40bdcd99-6485-4f33-a1e8-1fdc660dec32</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>103</OID>
                      <Parent>82</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>360 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>8665dc8e-4200-445f-8b50-b6b1394128c8</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>104</OID>
                      <Parent>82</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>378 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>1c10ddc4-6fd1-4649-8fe2-2cdb37452edb</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>105</OID>
                      <Parent>82</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>396 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>cddaaa5a-5dfa-473a-9ba2-577dd32cf09b</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>106</OID>
                      <Parent>82</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>414 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>b36aa688-7045-47cb-90e7-b3f6d8d369c4</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>107</OID>
                      <Parent>82</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>66af73f2-7ed5-4b96-abe7-411dad9febf6</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>234 px</Y>
                </Location>
                <Size>
                  <Width>145 px</Width>
                  <Height>451 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>451 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>560 px</X>
              <Y>48 px</Y>
            </Location>
            <Size>
              <Width>150 px</Width>
              <Height>734 px</Height>
            </Size>
            <MinSize>
              <Width>120 px</Width>
              <Height>734 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>734 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>f6e459a3-5359-4669-b2a5-d6e12a23a819</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>108</OID>
              <OutModel>67</OutModel>
              <InModel>15</InModel>
              <OutPort>109</OutPort>
              <InPort>110</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>479.5 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Right</Align>
                <CustomProperties>
                  <OID>110</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>Undefined</X>
                  <Y>439.5 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Left</Align>
                <CustomProperties>
                  <OID>109</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>a3d089ad-792b-422f-88d1-5c96e4acfb29</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>111</OID>
              <OutModel>67</OutModel>
              <InModel>60</InModel>
              <OutPort>112</OutPort>
              <InPort>113</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>70 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Left</Align>
                <CustomProperties>
                  <OID>113</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>Undefined</X>
                  <Y>80.064705882353 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Right</Align>
                <CustomProperties>
                  <OID>112</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <BendPoints>
              <PointD>
                <X>730 px</X>
                <Y>128.064705882353 px</Y>
              </PointD>
              <PointD>
                <X>730 px</X>
                <Y>126 px</Y>
              </PointD>
            </BendPoints>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>292eda0c-f5d0-4731-b368-246d5594dd09</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>114</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>115</OID>
                  <Parent>114</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>116</OID>
                      <Parent>115</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>224 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>1a2d3513-80df-462b-b667-a80e04517f78</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>117</OID>
                      <Parent>115</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>224 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>44cbfd15-0da6-43e2-b951-45bdbf098ffd</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>118</OID>
                      <Parent>115</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>224 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>08e06a9f-7fad-480f-9f51-260988dc463b</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>225 px</Width>
                  <Height>55 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>55 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>119</OID>
                  <Parent>114</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>120</OID>
                      <Parent>119</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>224 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>ef3b0918-904a-450a-9620-6ca593d07116</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>121</OID>
                      <Parent>119</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>18 px</Y>
                    </Location>
                    <Size>
                      <Width>224 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>b40daaf6-2a94-4233-adce-199391677906</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>54 px</Y>
                </Location>
                <Size>
                  <Width>225 px</Width>
                  <Height>55 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>55 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>-40 px</X>
              <Y>240 px</Y>
            </Location>
            <Size>
              <Width>230 px</Width>
              <Height>158 px</Height>
            </Size>
            <MinSize>
              <Width>120 px</Width>
              <Height>158 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>158 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>b7c58829-9738-4c3f-a559-add59218b2ba</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>122</OID>
              <OutModel>15</OutModel>
              <InModel>114</InModel>
              <OutPort>123</OutPort>
              <InPort>124</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>79 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Right</Align>
                <CustomProperties>
                  <OID>124</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>Undefined</X>
                  <Y>311 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Left</Align>
                <CustomProperties>
                  <OID>123</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>3e551c0d-2f21-42a1-88a1-2cc3e77fdd51</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="EnumTypeVwModel">
            <CustomProperties>
              <OID>125</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>126</OID>
                  <Parent>125</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="EnumTypeMemberVwModel">
                    <CustomProperties>
                      <OID>127</OID>
                      <Parent>126</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>226 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>d1d95067-bdc4-4166-8f27-fb7c3ee67aa4</Path>
                      <TypeName>EntityDeveloper.EnumTypeMember</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="EnumTypeMemberVwModel">
                    <CustomProperties>
                      <OID>128</OID>
                      <Parent>126</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>226 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>df0cdf63-e07c-44f7-b359-9134a4e511e3</Path>
                      <TypeName>EntityDeveloper.EnumTypeMember</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="EnumTypeMemberVwModel">
                    <CustomProperties>
                      <OID>129</OID>
                      <Parent>126</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>226 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>2fb5ea44-05b3-4654-9ba6-ea70a78a1656</Path>
                      <TypeName>EntityDeveloper.EnumTypeMember</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>227 px</Width>
                  <Height>54 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>55 px</Height>
                </MaxSize>
              </Model>
            </Children>
            <Location>
              <X>264 px</X>
              <Y>-208 px</Y>
            </Location>
            <Size>
              <Width>232 px</Width>
              <Height>103 px</Height>
            </Size>
            <MinSize>
              <Width>120 px</Width>
              <Height>103 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>103 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>86ac3792-28d8-4963-96f7-ed1591a44d17</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateEnumType</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>130</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>131</OID>
                  <Parent>130</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>132</OID>
                      <Parent>131</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>20d38a6b-6e52-4fc7-a28a-c35947459910</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>133</OID>
                      <Parent>131</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>9331d498-6e05-402c-97bd-3156f1022516</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>134</OID>
                      <Parent>131</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>69c423a5-e343-4df3-8c84-e11d7c7f5bb0</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>135</OID>
                      <Parent>131</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>54.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>957a110a-7348-42b2-b012-1571428ee408</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>136</OID>
                      <Parent>131</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>72.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>4ba9d703-ec89-42e7-b18c-3ac49b2dff15</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>137</OID>
                      <Parent>131</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>90.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>10531ff0-1517-4490-ac4f-e58a83ec89d9</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>138</OID>
                      <Parent>131</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>108.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>198a1874-6e77-48c3-a1df-a8ba884953f8</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>139</OID>
                      <Parent>131</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>126.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>0616f50d-f219-4a57-ac56-992af387d035</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>140</OID>
                      <Parent>131</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>144.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>818862b3-7647-4b0c-82b5-9c1032bb649a</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>141</OID>
                      <Parent>131</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>162.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>9911c301-3de1-4643-8cfe-0f61db58c186</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>142</OID>
                      <Parent>131</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>180.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>3ddd1cea-aa3f-4e56-9888-69040a35cf44</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>143</OID>
                      <Parent>131</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>198.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>20b653fc-ad09-4ac7-a10f-84f3e231a225</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>145 px</Width>
                  <Height>217 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>217 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>144</OID>
                  <Parent>130</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>145</OID>
                      <Parent>144</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>52776ab7-12a8-4559-9a8e-7059bfbee01c</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>146</OID>
                      <Parent>144</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>18 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>e56f4fb5-1cb5-4a9c-9b31-31c70a28caa3</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>147</OID>
                      <Parent>144</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>36 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>3f2bbd2e-95d9-400b-bdbc-95d696296ebf</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>148</OID>
                      <Parent>144</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>54 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>8fb342d5-178d-4d13-bca0-354de8c64ba2</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>149</OID>
                      <Parent>144</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>72 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>59fb9874-7e66-4213-b45c-66165e320bdd</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>150</OID>
                      <Parent>144</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>6c0bc7bf-4fb6-47a6-921b-b260154fc4de</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>151</OID>
                      <Parent>144</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>5dc46813-b63e-4a69-aa14-73adff805af0</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>216 px</Y>
                </Location>
                <Size>
                  <Width>145 px</Width>
                  <Height>109 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>109 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>-272 px</X>
              <Y>152 px</Y>
            </Location>
            <Size>
              <Width>150 px</Width>
              <Height>374 px</Height>
            </Size>
            <MinSize>
              <Width>120 px</Width>
              <Height>374 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>374 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>94d52ec2-cff6-4f60-83c8-fcc12b3a4477</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>152</OID>
              <OutModel>130</OutModel>
              <InModel>114</InModel>
              <OutPort>153</OutPort>
              <InPort>154</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>79 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Left</Align>
                <CustomProperties>
                  <OID>154</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>Undefined</X>
                  <Y>87 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Right</Align>
                <CustomProperties>
                  <OID>153</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <BendPoints>
              <PointD>
                <X>-78 px</X>
                <Y>239 px</Y>
              </PointD>
              <PointD>
                <X>-78 px</X>
                <Y>319 px</Y>
              </PointD>
            </BendPoints>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>e1c04e62-d2d0-41a3-96c5-07cd0ee05965</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>155</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>156</OID>
                  <Parent>155</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>157</OID>
                      <Parent>156</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>218 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>e4af984e-5f57-41a7-b8ed-69e058b520b5</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>158</OID>
                      <Parent>156</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>218 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>4edf791a-58e4-4a96-af06-390a52f002c7</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>219 px</Width>
                  <Height>37 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>37 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>159</OID>
                  <Parent>155</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>160</OID>
                      <Parent>159</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>218 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>d16888e7-64d3-4aee-808d-f5dd55cb6296</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>161</OID>
                      <Parent>159</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>18 px</Y>
                    </Location>
                    <Size>
                      <Width>218 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>317fe3e5-8919-46a8-9da3-40e6f2d06e4c</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>36 px</Y>
                </Location>
                <Size>
                  <Width>219 px</Width>
                  <Height>55 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>55 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>-40 px</X>
              <Y>104 px</Y>
            </Location>
            <Size>
              <Width>224 px</Width>
              <Height>140 px</Height>
            </Size>
            <MinSize>
              <Width>120 px</Width>
              <Height>140 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>140 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>a9385b14-a0b1-4bc7-8ab2-02d10298dc37</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>162</OID>
              <OutModel>155</OutModel>
              <InModel>15</InModel>
              <OutPort>163</OutPort>
              <InPort>164</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>70 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Right</Align>
                <CustomProperties>
                  <OID>163</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>166 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Left</Align>
                <CustomProperties>
                  <OID>164</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>9ede5870-48fe-4c62-a606-e94ac28c3812</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>165</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>166</OID>
                  <Parent>165</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>167</OID>
                      <Parent>166</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>210 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>425cf1d6-0a26-4e43-8538-efcd36270a15</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>168</OID>
                      <Parent>166</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>210 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>1a505ddd-e2af-4860-b86b-bdbf34f50078</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>169</OID>
                      <Parent>166</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>210 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>66b065a6-2d37-4500-b11f-6673c9147908</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>170</OID>
                      <Parent>166</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>54.5 px</Y>
                    </Location>
                    <Size>
                      <Width>210 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>a0b5fa77-b50a-422e-80a1-819871453a9e</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>171</OID>
                      <Parent>166</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>72.5 px</Y>
                    </Location>
                    <Size>
                      <Width>210 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>cf7fcb80-3174-4fb9-9b7b-6d7ab05d5825</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>172</OID>
                      <Parent>166</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>90.5 px</Y>
                    </Location>
                    <Size>
                      <Width>210 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>2b0d5360-08a8-4597-ad61-ce1de355f92e</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>173</OID>
                      <Parent>166</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>108.5 px</Y>
                    </Location>
                    <Size>
                      <Width>210 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>d42223e6-033d-497c-b34a-357ed1b42bf7</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>174</OID>
                      <Parent>166</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>126.5 px</Y>
                    </Location>
                    <Size>
                      <Width>210 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>ce71a82b-7e01-4f5d-a438-f4166bb5d0b1</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>175</OID>
                      <Parent>166</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>144.5 px</Y>
                    </Location>
                    <Size>
                      <Width>210 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>f803c750-5641-41a3-8186-68067c1c2adc</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>176</OID>
                      <Parent>166</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>162.5 px</Y>
                    </Location>
                    <Size>
                      <Width>210 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>1276906d-502e-47be-ae65-15f0b7837bb2</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>177</OID>
                      <Parent>166</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>180.5 px</Y>
                    </Location>
                    <Size>
                      <Width>210 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>4d70304e-f2f1-447c-a65a-2fac19061f61</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>178</OID>
                      <Parent>166</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>198.5 px</Y>
                    </Location>
                    <Size>
                      <Width>210 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>713dd58a-5972-4f2e-9f27-9ff74bef51c7</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>179</OID>
                      <Parent>166</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>216.5 px</Y>
                    </Location>
                    <Size>
                      <Width>210 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>78194169-f137-402e-a977-2a8348e843e7</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>180</OID>
                      <Parent>166</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>234.5 px</Y>
                    </Location>
                    <Size>
                      <Width>210 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>08b69120-3a0c-46db-9629-d611a5daf8d5</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>211 px</Width>
                  <Height>253 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>253 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>181</OID>
                  <Parent>165</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>182</OID>
                      <Parent>181</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>210 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>cc36b0d1-9cc8-4757-82aa-82dca2001204</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>252 px</Y>
                </Location>
                <Size>
                  <Width>211 px</Width>
                  <Height>37 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>37 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>-40 px</X>
              <Y>648 px</Y>
            </Location>
            <Size>
              <Width>216 px</Width>
              <Height>338 px</Height>
            </Size>
            <MinSize>
              <Width>120 px</Width>
              <Height>338 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>338 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>eb900d6d-1884-47c6-b3cc-d117e94f15ae</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>183</OID>
              <OutModel>15</OutModel>
              <InModel>165</InModel>
              <OutPort>184</OutPort>
              <InPort>185</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>226.206895531145 px</X>
                  <Y>137.057466961557 px</Y>
                </Location>
                <Anchors>None</Anchors>
                <Align>Right</Align>
                <CustomProperties>
                  <OID>185</OID>
                  <PortType>FixedAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>Undefined</X>
                  <Y>585.103445025129 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Left</Align>
                <CustomProperties>
                  <OID>184</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <BendPoints>
              <PointD>
                <X>228 px</X>
                <Y>593.103445025129 px</Y>
              </PointD>
              <PointD>
                <X>228 px</X>
                <Y>785.057466961557 px</Y>
              </PointD>
            </BendPoints>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>afc0a2a1-cf1d-41b3-b3a9-d1bce3f6f433</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>186</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>187</OID>
                  <Parent>186</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>188</OID>
                      <Parent>187</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>234 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>ff4620c6-ca85-481b-b48b-6ae1a9833fb7</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>189</OID>
                      <Parent>187</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>234 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>6ca68ed4-e8c0-42dc-8fbe-2c074ebefa18</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>190</OID>
                      <Parent>187</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>234 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>a8fd05be-3834-4a0b-89ff-182a6ce9e94b</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>235 px</Width>
                  <Height>55 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>55 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>191</OID>
                  <Parent>186</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>192</OID>
                      <Parent>191</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>234 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>a03d60e1-4a31-4447-bd6a-d3a8e828904c</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>54 px</Y>
                </Location>
                <Size>
                  <Width>235 px</Width>
                  <Height>37 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>37 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>-64 px</X>
              <Y>480 px</Y>
            </Location>
            <Size>
              <Width>240 px</Width>
              <Height>140 px</Height>
            </Size>
            <MinSize>
              <Width>120 px</Width>
              <Height>140 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>140 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>a54b7afa-c262-40bf-b51f-159bd56d3d65</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>193</OID>
              <OutModel>186</OutModel>
              <InModel>15</InModel>
              <OutPort>194</OutPort>
              <InPort>195</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>470 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Left</Align>
                <CustomProperties>
                  <OID>195</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>Undefined</X>
                  <Y>30 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Right</Align>
                <CustomProperties>
                  <OID>194</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <BendPoints>
              <PointD>
                <X>196 px</X>
                <Y>510 px</Y>
              </PointD>
              <PointD>
                <X>196 px</X>
                <Y>478 px</Y>
              </PointD>
            </BendPoints>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>acaac436-cbd1-4c7b-9757-bffe5254c94f</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>196</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>197</OID>
                  <Parent>196</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>198</OID>
                      <Parent>197</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>e113e676-c57a-4de8-bbd3-97872caadf41</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>199</OID>
                      <Parent>197</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>3e4f23f3-f972-40bf-abab-06239e6437be</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>200</OID>
                      <Parent>197</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>61a35ccb-1a54-4571-be4d-559660d33f1b</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>201</OID>
                      <Parent>197</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>54.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>390156f6-63dc-4505-8fef-e4fd6f96e223</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>202</OID>
                      <Parent>197</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>72.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>ae301683-d3cf-4ef9-8332-8c583fb5c385</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>203</OID>
                      <Parent>197</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>90.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>97b7024f-937b-4ed5-b8ad-601fc6e83283</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>145 px</Width>
                  <Height>109 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>109 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>204</OID>
                  <Parent>196</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>205</OID>
                      <Parent>204</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>2aca8757-5acd-425b-a0f8-7c25bad696f2</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>206</OID>
                      <Parent>204</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>18 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>46ece67d-7f68-47a6-a866-4f27b823671f</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>207</OID>
                      <Parent>204</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>36 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>25344ec4-da11-4762-b3c7-b4902dbf29a9</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>108 px</Y>
                </Location>
                <Size>
                  <Width>145 px</Width>
                  <Height>73 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>73 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>752 px</X>
              <Y>672 px</Y>
            </Location>
            <Size>
              <Width>150 px</Width>
              <Height>230 px</Height>
            </Size>
            <MinSize>
              <Width>120 px</Width>
              <Height>230 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>230 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>0c5db00e-4f6a-470f-b08b-87a3dcab8539</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>208</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>209</OID>
                  <Parent>208</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>210</OID>
                      <Parent>209</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>1b218026-28a6-4173-8507-0a3a520d7393</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>211</OID>
                      <Parent>209</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>1e3fe479-9402-4dbd-906e-eb48e0ffc07f</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>212</OID>
                      <Parent>209</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>adec3b8e-c249-4154-8bde-3fef6b7fe743</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>213</OID>
                      <Parent>209</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>54.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>640c0689-a317-44e5-a1a9-1c594bba4801</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>214</OID>
                      <Parent>209</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>72.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>dd0e6736-f02a-4c8c-8dcc-749402da319e</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>215</OID>
                      <Parent>209</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>90.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>2d20b5d6-99ee-435b-b6a7-72af16df75cc</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>216</OID>
                      <Parent>209</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>108.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>61cf3a76-46f1-4bd4-b4af-16beb95e0741</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>217</OID>
                      <Parent>209</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>126.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>4ef5d8cd-22c5-456c-9fe8-7d23bc65f906</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>145 px</Width>
                  <Height>145 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>145 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>218</OID>
                  <Parent>208</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>219</OID>
                      <Parent>218</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>4bab93db-3218-4e16-9447-7e13ec124feb</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>220</OID>
                      <Parent>218</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>18 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>14b73bfc-8489-4e40-8746-5130167f44d1</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>221</OID>
                      <Parent>218</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>36 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>032e157c-cba2-4b5c-9981-60bfd40d09ce</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>222</OID>
                      <Parent>218</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>54 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>7b10d4bd-ca69-4632-a125-5309e601b9e8</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>223</OID>
                      <Parent>218</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>72 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>3511db57-d3a8-46d8-a05c-cb9ada734fbc</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>224</OID>
                      <Parent>218</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>90 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>dbd41555-35c9-4e09-a17c-41ab2ff35906</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>225</OID>
                      <Parent>218</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>108 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>22a4b2ed-9b70-428c-96c8-be95ac97193d</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>226</OID>
                      <Parent>218</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>126 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>065814ff-92e8-439b-8233-799ecb7ebb93</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>227</OID>
                      <Parent>218</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>144 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>92a32794-a7c8-4221-a90b-73ef9ec741be</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>228</OID>
                      <Parent>218</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>162 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>9ef61477-0323-4fac-bb7c-7bdc31c2030e</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>229</OID>
                      <Parent>218</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>180 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>946379ca-ba9c-4485-aead-643e69428510</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>230</OID>
                      <Parent>218</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>198 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>d4f673eb-3c94-49b5-adce-2458e0acbb94</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>231</OID>
                      <Parent>218</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>216 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>9679355c-c364-4351-83fc-28a4727d7526</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>232</OID>
                      <Parent>218</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>234 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>49d517e1-0804-4858-a5d8-d68591c383fc</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>233</OID>
                      <Parent>218</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>252 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>7724cb37-653b-4a74-8c29-647880493c93</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>234</OID>
                      <Parent>218</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>270 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>f244c518-7548-4396-8fe9-d0a94e428f0e</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>235</OID>
                      <Parent>218</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>288 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>0b09db32-c3df-4e40-a893-7822121a6904</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>236</OID>
                      <Parent>218</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>306 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>1eed75a9-5eb8-4435-be9c-187e2b7812b2</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>237</OID>
                      <Parent>218</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>59d7d29e-ff2a-460a-bd4e-c311bd2634db</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>238</OID>
                      <Parent>218</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>0ce49a48-7e18-4613-87f7-db9fd0d34db6</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>239</OID>
                      <Parent>218</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>7b8e4b9c-c4fe-4def-884f-e46487a950f7</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>144 px</Y>
                </Location>
                <Size>
                  <Width>145 px</Width>
                  <Height>343 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>343 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>248 px</X>
              <Y>688 px</Y>
            </Location>
            <Size>
              <Width>150 px</Width>
              <Height>536 px</Height>
            </Size>
            <MinSize>
              <Width>120 px</Width>
              <Height>536 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>536 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>05068549-245a-45a7-bd8f-7dd76ba3599d</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>240</OID>
              <OutModel>130</OutModel>
              <InModel>208</InModel>
              <OutPort>241</OutPort>
              <InPort>242</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>-13.5172426644838 px</X>
                  <Y>424.367810258274 px</Y>
                </Location>
                <Anchors>None</Anchors>
                <Align>Left</Align>
                <CustomProperties>
                  <OID>242</OID>
                  <PortType>FixedAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>75 px</X>
                  <Y>0 px</Y>
                </Location>
                <Anchors>Left Right</Anchors>
                <Align>Bottom</Align>
                <CustomProperties>
                  <OID>241</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <BendPoints>
              <PointD>
                <X>-197 px</X>
                <Y>1112.36781025827 px</Y>
              </PointD>
            </BendPoints>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>6b3b97df-b6d1-4e73-8184-c0e33ba80ddc</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>243</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>244</OID>
                  <Parent>243</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>245</OID>
                      <Parent>244</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>03266e01-e9ef-4291-b468-d66c8837008b</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>246</OID>
                      <Parent>244</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>1447959f-beea-42cf-8f72-13518796c0b6</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>247</OID>
                      <Parent>244</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>c5787e88-b8f7-4e29-818a-d9924c3c59b3</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>248</OID>
                      <Parent>244</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>54.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>b8804c89-8ed8-4b27-9613-c034e311f883</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>145 px</Width>
                  <Height>73 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>73 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>249</OID>
                  <Parent>243</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>250</OID>
                      <Parent>249</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>e683e3e4-2590-4e02-b6ee-4fc5b6ff062f</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>251</OID>
                      <Parent>249</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>18 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>aedcf370-a026-4b87-a85c-b5e9e90e7fef</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>252</OID>
                      <Parent>249</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>36 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>1dec1348-3f21-4fec-8ca3-b8a22efc406e</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>253</OID>
                      <Parent>249</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>54 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>6eae9f0a-10cc-4fc5-9c93-3788bd0449df</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>254</OID>
                      <Parent>249</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>72 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>e5c12e1c-015a-49da-ac20-fad8dea1c933</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>255</OID>
                      <Parent>249</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>90 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>13cf16a8-133f-4f59-917e-717984b68bd1</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>256</OID>
                      <Parent>249</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>108 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>b3985f7f-6ee8-4e8e-a17e-4c6496049d3a</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>257</OID>
                      <Parent>249</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>126 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>4447370a-4028-466e-8995-f3ec63f0d420</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>258</OID>
                      <Parent>249</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>144 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>22851a6e-1ccb-4afd-aa4e-979a301e7acd</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>259</OID>
                      <Parent>249</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>162 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>ccda2546-c71c-46a4-b6e2-e9914d4dca18</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>260</OID>
                      <Parent>249</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>180 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>89a9cfbd-0385-4296-b650-973a6db35eba</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>261</OID>
                      <Parent>249</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>198 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>9fc1f191-dfe5-487c-b6ae-ba9d2f550377</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>262</OID>
                      <Parent>249</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>216 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>4d4664af-5c6f-4e45-ad2e-db254ce081fb</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>263</OID>
                      <Parent>249</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>234 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>14de7f4f-616b-4e51-a0a5-49614ca35a18</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>264</OID>
                      <Parent>249</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>252 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>91795900-8dd8-4181-a56a-5abb67b68607</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>265</OID>
                      <Parent>249</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>270 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>ddfc637d-8f79-4d96-9ad5-f557714b7e68</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>266</OID>
                      <Parent>249</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>288 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>9316b1a3-eea4-423b-bb72-f84949a2fb12</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>267</OID>
                      <Parent>249</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>306 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>9b1d5188-86e8-4ee0-9eb6-2c1501d2709c</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>268</OID>
                      <Parent>249</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>324 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>a0395c2b-7132-451e-b08f-6dd5e108720f</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>269</OID>
                      <Parent>249</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>342 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>d9043230-7125-49ef-b490-72af68abebbf</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>270</OID>
                      <Parent>249</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>a0683f6c-1f8a-43c6-856b-9ffdcab15504</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>271</OID>
                      <Parent>249</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>0349970b-3b75-4728-8548-55a644b70917</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>272</OID>
                      <Parent>249</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>937a6914-61dc-49ae-a014-2c12a89c63ea</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>273</OID>
                      <Parent>249</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>a1e4b79b-8874-4a00-be6c-f21fee72d5b0</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>72 px</Y>
                </Location>
                <Size>
                  <Width>145 px</Width>
                  <Height>379 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>379 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>960 px</X>
              <Y>488 px</Y>
            </Location>
            <Size>
              <Width>150 px</Width>
              <Height>500 px</Height>
            </Size>
            <MinSize>
              <Width>120 px</Width>
              <Height>500 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>500 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>41d6120e-a7a7-4f0b-a1e8-6d1d3f4064da</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>274</OID>
              <OutModel>208</OutModel>
              <InModel>243</InModel>
              <OutPort>275</OutPort>
              <InPort>276</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>154.298848369758 px</X>
                  <Y>272.919534963194 px</Y>
                </Location>
                <Anchors>None</Anchors>
                <Align>Right</Align>
                <CustomProperties>
                  <OID>275</OID>
                  <PortType>FixedAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>-1.37931559891911 px</X>
                  <Y>472.919534963194 px</Y>
                </Location>
                <Anchors>None</Anchors>
                <Align>Left</Align>
                <CustomProperties>
                  <OID>276</OID>
                  <PortType>FixedAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>0c3ea924-60e3-4f38-84f3-a1297cae1a17</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>277</OID>
              <OutModel>243</OutModel>
              <InModel>196</InModel>
              <OutPort>278</OutPort>
              <InPort>279</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>115 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Right</Align>
                <CustomProperties>
                  <OID>279</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>Undefined</X>
                  <Y>299 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Left</Align>
                <CustomProperties>
                  <OID>278</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>7425479f-e118-469e-8d74-35430a43fad2</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>280</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>281</OID>
                  <Parent>280</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>282</OID>
                      <Parent>281</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>f5d5eacd-056f-42ec-8a6f-987ab682a347</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>283</OID>
                      <Parent>281</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>8fb909ab-6683-4207-93e0-67fbd40b716f</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>284</OID>
                      <Parent>281</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>1dd2f738-d6eb-4c85-81e2-19b32e211e7d</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>285</OID>
                      <Parent>281</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>54.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>1338547f-876f-4e39-9f02-92dc5e0afbbc</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>286</OID>
                      <Parent>281</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>72.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>05935920-5092-4cba-9e7b-f56ad81443a3</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>287</OID>
                      <Parent>281</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>90.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>60a6f38a-c8e4-4169-91b7-64230956e742</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>145 px</Width>
                  <Height>109 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>109 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>288</OID>
                  <Parent>280</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>289</OID>
                      <Parent>288</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>e3014501-c8eb-4c55-8767-efdf1aeb0f54</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>290</OID>
                      <Parent>288</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>18 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>8c6380b4-4613-45af-af9d-e5328741ba6b</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>291</OID>
                      <Parent>288</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>36 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>ac330ad5-b43d-4003-85b7-66c471708b36</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>292</OID>
                      <Parent>288</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>54 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>69f48b62-da9f-4d6f-b80c-288229b1dd4e</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>293</OID>
                      <Parent>288</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>72 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>984bb9ff-7ed4-4df6-9973-4b9242fc7f56</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>294</OID>
                      <Parent>288</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>90 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>20e28701-5342-44e1-bddc-ceed86f92b90</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>295</OID>
                      <Parent>288</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>8cedfa2c-c046-453c-bf7c-9bb7026b8719</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>296</OID>
                      <Parent>288</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>bee2b930-7691-4b8c-96e2-66ff32269809</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>297</OID>
                      <Parent>288</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>2baa5f60-2803-40d4-ac13-ce4c4265e89a</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>298</OID>
                      <Parent>288</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>de5a1c52-b620-4e52-8e60-942a8c9c0f25</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>108 px</Y>
                </Location>
                <Size>
                  <Width>145 px</Width>
                  <Height>127 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>127 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>776 px</X>
              <Y>280 px</Y>
            </Location>
            <Size>
              <Width>150 px</Width>
              <Height>284 px</Height>
            </Size>
            <MinSize>
              <Width>120 px</Width>
              <Height>284 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>284 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>3732f326-a97e-4ceb-a598-c05b9b47cd2a</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>299</OID>
              <OutModel>67</OutModel>
              <InModel>280</InModel>
              <OutPort>300</OutPort>
              <InPort>301</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>142 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Left</Align>
                <CustomProperties>
                  <OID>301</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>Undefined</X>
                  <Y>374 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Right</Align>
                <CustomProperties>
                  <OID>300</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>bf88285c-53d2-46f6-8cb2-c5b82e2f4324</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>302</OID>
              <OutModel>280</OutModel>
              <InModel>196</InModel>
              <OutPort>303</OutPort>
              <InPort>304</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>87 px</X>
                  <Y>0 px</Y>
                </Location>
                <Anchors>Left Right</Anchors>
                <Align>Top</Align>
                <CustomProperties>
                  <OID>304</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>63 px</X>
                  <Y>Undefined</Y>
                </Location>
                <Anchors>Left Right</Anchors>
                <Align>Bottom</Align>
                <CustomProperties>
                  <OID>303</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>685290ae-73c6-46b6-a88f-dfbbf5941a01</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>305</OID>
              <OutModel>208</OutModel>
              <InModel>196</InModel>
              <OutPort>306</OutPort>
              <InPort>307</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>107 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Right</Align>
                <CustomProperties>
                  <OID>306</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>123 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Left</Align>
                <CustomProperties>
                  <OID>307</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>5d97ce54-035f-49dc-871e-07825c3516ab</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>308</OID>
              <OutModel>208</OutModel>
              <InModel>67</InModel>
              <OutPort>309</OutPort>
              <InPort>310</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>47 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Right</Align>
                <CustomProperties>
                  <OID>309</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>687 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Left</Align>
                <CustomProperties>
                  <OID>310</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>a9b2bede-8b43-4421-a0e3-1752fdf1cd2e</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
        </Children>
        <GridSize>8 px</GridSize>
        <ViewPort>
          <ScaleMode>Free</ScaleMode>
          <Scale>0.87</Scale>
          <Location>
            <X>-322.988503976863 px</X>
            <Y>288.505745545169 px</Y>
          </Location>
        </ViewPort>
        <Oid xsi:type="SchemaModelOID">
          <Path>9cf690cb-e5be-4b9e-9cd9-51471560c52c</Path>
          <TypeName>EntityDeveloper.NHibernate.HibernateContextModel</TypeName>
        </Oid>
      </Model>
    </DiagramModel>
  </Diagram>
  <DiagramOptions Version="v2.0">
    <Options xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xsi:type="PageOptions">
      <FooterMargin>56 in/100</FooterMargin>
      <TopLeftMargins>
        <Width>39.3700787401575 in/100</Width>
        <Height>39.3700787401575 in/100</Height>
      </TopLeftMargins>
      <BottomRightMargins>
        <Width>39.3700787401575 in/100</Width>
        <Height>56 in/100</Height>
      </BottomRightMargins>
      <PaperSize>
        <Width>827 in/100</Width>
        <Height>1169 in/100</Height>
      </PaperSize>
      <PaperSourceName>Automaticky vybrat</PaperSourceName>
    </Options>
    <Options xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xsi:type="PrintOptions" />
    <Options xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xsi:type="ViewOptions">
      <ShadowOffset>
        <X>4 px</X>
        <Y>4 px</Y>
      </ShadowOffset>
      <CustomProperties />
    </Options>
    <EdDiagramOptions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
      <PropertyDetails>NameAndType</PropertyDetails>
      <CustomProperties />
    </EdDiagramOptions>
  </DiagramOptions>
</EntityDeveloperDiagram>