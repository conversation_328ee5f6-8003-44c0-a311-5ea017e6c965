<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="MenuPresenterAppearanceBackgroundConfig_Brief" xml:space="preserve">
    <value>Vzhled pozadí</value>
  </data>
  <data name="MenuPresenterAppearanceBackgroundConfig_Detail" xml:space="preserve">
    <value>Vzhled pozadí</value>
  </data>
  <data name="MenuPresenterAppearanceLogoConfig_Brief" xml:space="preserve">
    <value>Vzhled a velikost loga</value>
  </data>
  <data name="MenuPresenterAppearanceLogoConfig_Detail" xml:space="preserve">
    <value>Vzhled a velikost loga</value>
  </data>
  <data name="MenuPresenterAppearanceMenuConfig_Brief" xml:space="preserve">
    <value>Vzhled jídelníčku</value>
  </data>
  <data name="MenuPresenterAppearanceMenuConfig_Detail" xml:space="preserve">
    <value>Vzhled jídelníčku</value>
  </data>
  <data name="MenuPresenterAppearanceMenuHeaderConfig_Brief" xml:space="preserve">
    <value>Vzhled hlavičky se sloupci v jídelníčku</value>
  </data>
  <data name="MenuPresenterAppearanceMenuHeaderConfig_Detail" xml:space="preserve">
    <value>Vzhled hlavičky se sloupci v jídelníčku</value>
  </data>
  <data name="MenuPresenterAppearanceTextConfig_Brief" xml:space="preserve">
    <value>Vzhled textů na hlavní obrazovce</value>
  </data>
  <data name="MenuPresenterAppearanceTextConfig_Detail" xml:space="preserve">
    <value>Vzhled textů na hlavní obrazovce</value>
  </data>
  <data name="MenuPresenterBehaviourColumnsConfig_Brief" xml:space="preserve">
    <value>Sloupce</value>
  </data>
  <data name="MenuPresenterBehaviourColumnsConfig_Detail" xml:space="preserve">
    <value>Nastavení jednotlivých sloupců jídelníčku. Umožňuje definovat jejich pořadí, to zda mají být zobrazeny a také konfiguraci specifickou pro každý typ sloupce.</value>
  </data>
  <data name="MenuPresenterBehaviourMenuConfig_Brief" xml:space="preserve">
    <value>Nastavení jídelníčku</value>
  </data>
  <data name="MenuPresenterBehaviourMenuConfig_Detail" xml:space="preserve">
    <value>Nastavení filtrování a dálších položek jídelníčku. Dovoluje zvolit jaké alternativy a druhy jídel mají byt zobrazeny.</value>
  </data>
  <data name="MenuPresenterBehaviourRollConfig_Brief" xml:space="preserve">
    <value>Rolování</value>
  </data>
  <data name="MenuPresenterBehaviourRollConfig_Detail" xml:space="preserve">
    <value>Rolování jídelníčku</value>
  </data>
  <data name="GlobalHwCardKbdReaderConfig_Brief" xml:space="preserve">
    <value>Klávesnicová čtečka karet</value>
  </data>
  <data name="GlobalHwCardKbdReaderConfig_Detail" xml:space="preserve">
    <value>Konfigurace klávesnicové čtečky karet</value>
  </data>
  <data name="PresPointTimeoutConfig_Brief" xml:space="preserve">
    <value>Timeouty PM</value>
  </data>
  <data name="PresPointTimeoutConfig_Detail" xml:space="preserve">
    <value>Všechny timeouty používané v PM.</value>
  </data>
  <data name="CashDeskBehaviourMiscellaneousConfig_Brief" xml:space="preserve">
    <value>Různé</value>
  </data>
  <data name="CashDeskBehaviourMiscellaneousConfig_Detail" xml:space="preserve">
    <value>Různá nastavení</value>
  </data>
  <data name="CashDeskSaleConfig_Brief" xml:space="preserve">
    <value>Prodej</value>
  </data>
  <data name="CashDeskSaleConfig_Detail" xml:space="preserve">
    <value>Konfigurace prodeje</value>
  </data>
  <data name="CashDeskBehaviourUserInterfaceConfig_Brief" xml:space="preserve">
    <value>Uživatelské rozhraní</value>
  </data>
  <data name="CashDeskBehaviourUserInterfaceConfig_Detail" xml:space="preserve">
    <value>Konfigurace typu uživatelského rozhraní</value>
  </data>
  <data name="CashDeskOfflineConfig_Brief" xml:space="preserve">
    <value>Offline</value>
  </data>
  <data name="CashDeskOfflineConfig_Detail" xml:space="preserve">
    <value>Konfigurace režimu offline</value>
  </data>
  <data name="CashDeskBehaviourTouchConfig_Brief" xml:space="preserve">
    <value>Touch</value>
  </data>
  <data name="CashDeskBehaviourTouchConfig_Detail" xml:space="preserve">
    <value>Konfigurace parametrů souvisejících s Touch klávesnicí</value>
  </data>
  <data name="CashDeskBehaviourTaraConfig_Brief" xml:space="preserve">
    <value>Tara</value>
  </data>
  <data name="CashDeskBehaviourTaraConfig_Detail" xml:space="preserve">
    <value>Konfigurace tárování</value>
  </data>
  <data name="GlobalHwEanKbdReaderConfig_Brief" xml:space="preserve">
    <value>Čtečka čárových kódů</value>
  </data>
  <data name="GlobalHwEanKbdReaderConfig_Detail" xml:space="preserve">
    <value>Konfigurace čtečky čárových kódů</value>
  </data>
  <data name="GlobalBehaviourMessageForClientsConfig_Brief" xml:space="preserve">
    <value>Zprávy pro klienty</value>
  </data>
  <data name="GlobalBehaviourMessageForClientsConfig_Detail" xml:space="preserve">
    <value>Konfigurace zpráv pro klienty</value>
  </data>
  <data name="CashDeskBehaviourLockConfig_Brief" xml:space="preserve">
    <value>Zamykání</value>
  </data>
  <data name="CashDeskBehaviourLockConfig_Detail" xml:space="preserve">
    <value>Konfigurace zamykání kasy při nečinnosti a uživatelského zamykání</value>
  </data>
  <data name="GlobalHwCardSerialReaderConfig_Brief" xml:space="preserve">
    <value>Sériová čtečka karet</value>
  </data>
  <data name="GlobalHwCardSerialReaderConfig_Detail" xml:space="preserve">
    <value>Konfigurace čtečky karet připojené k sériovému portu</value>
  </data>
  <data name="GlobalHwClientDisplayConfig_Brief" xml:space="preserve">
    <value>Zákaznický displej</value>
  </data>
  <data name="GlobalHwClientDisplayConfig_Detail" xml:space="preserve">
    <value>Konfigurace zákaznického displeje</value>
  </data>
  <data name="CashDeskAccoutingMidnightConfig_Brief" xml:space="preserve">
    <value>Půlnoc pro jídelníček</value>
  </data>
  <data name="CashDeskAccoutingMidnightConfig_Detail" xml:space="preserve">
    <value>Konfigurace parametrů souvisejících s přenačtením jídelníčku.</value>
  </data>
  <data name="PresPointAppearanceFontConfig_Brief" xml:space="preserve">
    <value>Písmo</value>
  </data>
  <data name="PresPointAppearanceFontConfig_Detail" xml:space="preserve">
    <value>Konfigurace písma</value>
  </data>
  <data name="PresPointAppearanceColorsConfig_Brief" xml:space="preserve">
    <value>Barvy</value>
  </data>
  <data name="PresPointAppearanceColorsConfig_Detail" xml:space="preserve">
    <value>Konfigurace barev</value>
  </data>
  <data name="GlobalBehaviourApplicationLanguageConfig_Brief" xml:space="preserve">
    <value>Jazyk aplikace</value>
  </data>
  <data name="GlobalBehaviourApplicationLanguageConfig_Detail" xml:space="preserve">
    <value>Konfigurace jazyka aplikace</value>
  </data>
  <data name="GlobalBehaviourPricesConfig_Brief" xml:space="preserve">
    <value>Zobrazování cen</value>
  </data>
  <data name="GlobalBehaviourPricesConfig_Detail" xml:space="preserve">
    <value>Konfigurace zobrazování cen</value>
  </data>
  <data name="GlobalBehaviourClientConfig_Brief" xml:space="preserve">
    <value>Strávník</value>
  </data>
  <data name="GlobalBehaviourClientConfig_Detail" xml:space="preserve">
    <value>Konfigurace údajů o strávníkovi</value>
  </data>
  <data name="CashDeskHwClientDisplayConfig_Brief" xml:space="preserve">
    <value>Zákaznický displej</value>
  </data>
  <data name="CashDeskHwClientDisplayConfig_Detail" xml:space="preserve">
    <value>Konfigurace zákaznického displeje</value>
  </data>
  <data name="GlobalRulesCashOperationsConfig_Brief" xml:space="preserve">
    <value>Hotovostní operace pro klienty</value>
  </data>
  <data name="GlobalRulesCashOperationsConfig_Detail" xml:space="preserve">
    <value>Konfigurace hotovostních operací pro klienty</value>
  </data>
  <data name="GlobalRulesRoundingConfig_Brief" xml:space="preserve">
    <value>Pravidla zaokrouhlování</value>
  </data>
  <data name="GlobalRulesRoundingConfig_Detail" xml:space="preserve">
    <value>Konfigurace pravidel zaokrouhlování pro paragon, položky paragonu a měnu</value>
  </data>
  <data name="GlobalBehaviourSalesSlipPrintConfig_Brief" xml:space="preserve">
    <value>Paragon</value>
  </data>
  <data name="GlobalBehaviourSalesSlipPrintConfig_Detail" xml:space="preserve">
    <value>Konfigurace údajů tištěných na paragonu</value>
  </data>
  <data name="PresPointBehaviourUserInterfaceConfig_Brief" xml:space="preserve">
    <value>Uživatelské rozhraní</value>
  </data>
  <data name="PresPointBehaviourUserInterfaceConfig_Detail" xml:space="preserve">
    <value>Konfigurace uživatelského rozhrani</value>
  </data>
  <data name="PresPointBehaviourClientInfoConfig_Brief" xml:space="preserve">
    <value>Informace o klientovi</value>
  </data>
  <data name="PresPointBehaviourClientInfoConfig_Detail" xml:space="preserve">
    <value>Konfigurace zobrazovaných informací o klientovi</value>
  </data>
  <data name="PresPointBehaviourMenuConfig_Brief" xml:space="preserve">
    <value>Jídelníček</value>
  </data>
  <data name="PresPointBehaviourMenuConfig_Detail" xml:space="preserve">
    <value>Konfigurace jídelníčku</value>
  </data>
  <data name="GlobalSystemOwnerConfig_Brief" xml:space="preserve">
    <value>Vlastník licence</value>
  </data>
  <data name="GlobalSystemOwnerConfig_Detail" xml:space="preserve">
    <value>Konfigurace parametrů vlastníka licence</value>
  </data>
  <data name="CashDeskStockModuleConfig_Brief" xml:space="preserve">
    <value>Modul sklady</value>
  </data>
  <data name="CashDeskStockModuleConfig_Detail" xml:space="preserve">
    <value>Konfigurace modulu sklady</value>
  </data>
  <data name="GlobalHwPosPrinterConfig_Brief" xml:space="preserve">
    <value>Bankovní tiskárna</value>
  </data>
  <data name="GlobalHwPosPrinterConfig_Detail" xml:space="preserve">
    <value>Nastavení bankovní tískárny</value>
  </data>
  <data name="GlobalRulesCardManagementConfig_Brief" xml:space="preserve">
    <value>Správa identifikačních karet</value>
  </data>
  <data name="GlobalRulesCardManagementConfig_Detail" xml:space="preserve">
    <value>Konfigurace identifikačních karet používaných v systému</value>
  </data>
  <data name="GlobalRulesMenuConfig_Brief" xml:space="preserve">
    <value>Jídla a jídelníček</value>
  </data>
  <data name="GlobalRulesMenuConfig_Detail" xml:space="preserve">
    <value>Konfigurace jídelníčku, jídel, dotací</value>
  </data>
  <data name="GlobalRulesOrderingConfig_Brief" xml:space="preserve">
    <value>Objednávání</value>
  </data>
  <data name="GlobalRulesOrderingConfig_Detail" xml:space="preserve">
    <value>Konfigurace objednávání jídel</value>
  </data>
  <data name="CashDeskFiscalModuleConfig_Brief" xml:space="preserve">
    <value>Fiskální modul</value>
  </data>
  <data name="CashDeskFiscalModuleConfig_Detail" xml:space="preserve">
    <value>Konfigurace fiskálního modulu</value>
  </data>
  <data name="GlobalRulesClientManagementConfig_Brief" xml:space="preserve">
    <value>Správa klientů</value>
  </data>
  <data name="GlobalRulesClientManagementConfig_Detail" xml:space="preserve">
    <value>Konfigurace správy klientů</value>
  </data>
  <data name="OfficeHwPrinterConfig_Brief" xml:space="preserve">
    <value>Bankovní tiskárna</value>
  </data>
  <data name="OfficeHwPrinterConfig_Detail" xml:space="preserve">
    <value>Konfigurace bankovní tiskárny</value>
  </data>
  <data name="GlobalHwKitchenPrinterConfig_Brief" xml:space="preserve">
    <value>Tiskárna v kuchyni</value>
  </data>
  <data name="GlobalHwKitchenPrinterConfig_Detail" xml:space="preserve">
    <value>Konfigurace tiskárny pro tisk objednávek jídel v kuchyni. Tiskne na ni Kasa.</value>
  </data>
  <data name="OfficeBehaviourBalanceConfig_Brief" xml:space="preserve">
    <value>Uzávěrka</value>
  </data>
  <data name="OfficeBehaviourBalanceConfig_Detail" xml:space="preserve">
    <value>Konfigurace uzávěrky</value>
  </data>
  <data name="OfficeBehaviourMealTicketsConfig_Brief" xml:space="preserve">
    <value>Stravenky</value>
  </data>
  <data name="OfficeBehaviourMealTicketsConfig_Detail" xml:space="preserve">
    <value>Konfigurace stravenek</value>
  </data>
  <data name="OfficeBehaviourBatchOrdering_Brief" xml:space="preserve">
    <value>Náhradní objednávání</value>
  </data>
  <data name="OfficeBehaviourBatchOrdering_Detail" xml:space="preserve">
    <value>Konfigurace náhradního objednávání</value>
  </data>
  <data name="OfficeBehaviourCalcPriceList_Brief" xml:space="preserve">
    <value>Kalkulovaný ceník</value>
  </data>
  <data name="OfficeBehaviourCalcPriceList_Detail" xml:space="preserve">
    <value>Konfigurace kalkulovaného ceníku</value>
  </data>
  <data name="OfficeBehaviourCardManagement_Brief" xml:space="preserve">
    <value>Správa karet</value>
  </data>
  <data name="OfficeBehaviourCardManagement_Detail" xml:space="preserve">
    <value>Konfigurace správy karet</value>
  </data>
  <data name="GlobalServicesSmtpConfig_Brief" xml:space="preserve">
    <value>SMTP</value>
  </data>
  <data name="GlobalServicesSmtpConfig_Detail" xml:space="preserve">
    <value>Konfigurace SMTP serveru</value>
  </data>
  <data name="GlobalServicesLoggingConfig_Brief" xml:space="preserve">
    <value>Logování</value>
  </data>
  <data name="GlobalServicesLoggingConfig_Detail" xml:space="preserve">
    <value>Konfigurace logování</value>
  </data>
  <data name="GlobalHwScaleConfig_Brief" xml:space="preserve">
    <value>Váha</value>
  </data>
  <data name="GlobalHwScaleConfig_Detail" xml:space="preserve">
    <value>Konfigurace váhy</value>
  </data>
  <data name="GlobalRulesCanteenConfig_Brief" xml:space="preserve">
    <value>Výdejna</value>
  </data>
  <data name="GlobalRulesCanteenConfig_Detail" xml:space="preserve">
    <value>Konfigurace výdejny</value>
  </data>
  <data name="MenuPresenterBehaviourUserInterfaceConfig_Brief" xml:space="preserve">
    <value>Uživatelské rozhraní</value>
  </data>
  <data name="MenuPresenterBehaviourUserInterfaceConfig_Detail" xml:space="preserve">
    <value>Konfigurace uživatelského rozhraní</value>
  </data>
  <data name="OfficeEBankingConfig_Brief" xml:space="preserve">
    <value>E-Banking</value>
  </data>
  <data name="OfficeEBankingConfig_Detail" xml:space="preserve">
    <value>Konfigurace E-Bankingu</value>
  </data>
  <data name="GlobalRulesBalanceConfig_Brief" xml:space="preserve">
    <value>Uzávěrka</value>
  </data>
  <data name="GlobalRulesBalanceConfig_Detail" xml:space="preserve">
    <value>Konfigurace uzávěrky</value>
  </data>
  <data name="AutoUpdaterBehaviourImpersonationConfig_Brief" xml:space="preserve">
    <value>Přihlašování</value>
  </data>
  <data name="AutoUpdaterBehaviourImpersonationConfig_Detail" xml:space="preserve">
    <value>Konfigurace přihlašování</value>
  </data>
  <data name="BatchDataOperationsParametersConfig_Brief" xml:space="preserve">
    <value>Parametry změnového řízení</value>
  </data>
  <data name="BatchDataOperationsParametersConfig_Detail" xml:space="preserve">
    <value>Konfigurace parametrů změnového řízení</value>
  </data>
  <data name="OfficeBehaviourSalesSlipPrintConfig_Brief" xml:space="preserve">
    <value>Tisk paragonu</value>
  </data>
  <data name="OfficeBehaviourSalesSlipPrintConfig_Detail" xml:space="preserve">
    <value>Konfigurace tisku paragonu</value>
  </data>
  <data name="PresPointBehaviourMealTicketConfig_Brief" xml:space="preserve">
    <value>Stravenky</value>
  </data>
  <data name="PresPointBehaviourMealTicketConfig_Detail" xml:space="preserve">
    <value>Konfigurace stravenek (omezení jejich tisku)</value>
  </data>
  <data name="ServePointBehaviourServeringSettingsConfig_Brief" xml:space="preserve">
    <value>Konfigurace výdeje jídel</value>
  </data>
  <data name="ServePointBehaviourServeringSettingsConfig_Detail" xml:space="preserve">
    <value>Obecné nastavení možností výdeje jídel</value>
  </data>
  <data name="ServePointAppearanceSimpleViewConfig_Brief" xml:space="preserve">
    <value>Jednoduché zobrazení</value>
  </data>
  <data name="ServePointAppearanceSimpleViewConfig_Detail" xml:space="preserve">
    <value>Nastavení vzhledu jednoduchého zobrazení</value>
  </data>
  <data name="ServePointDisplayClientPersonalDataConfig_Brief" xml:space="preserve">
    <value>Zobrazení údajů o klientovi</value>
  </data>
  <data name="ServePointDisplayClientPersonalDataConfig_Detail" xml:space="preserve">
    <value>Nastavení zobrazovaných informací o klientovi</value>
  </data>
  <data name="ServePointBehaviourUserInterfaceConfig_Brief" xml:space="preserve">
    <value>Uživatelské rozhraní</value>
  </data>
  <data name="ServePointBehaviourUserInterfaceConfig_Detail" xml:space="preserve">
    <value>Nastavení chování uživatelského rozhraní</value>
  </data>
  <data name="ServePointAppearanceExclamationButtonsConfig_Brief" xml:space="preserve">
    <value>Výstražná tlačítka</value>
  </data>
  <data name="ServePointAppearanceExclamationButtonsConfig_Detail" xml:space="preserve">
    <value>Nastavení vzhledu výstražných tlačítek</value>
  </data>
  <data name="ServePointAppearanceApplicationConfig_Brief" xml:space="preserve">
    <value>Vzhled aplikace</value>
  </data>
  <data name="ServePointAppearanceApplicationConfig_Detail" xml:space="preserve">
    <value>Nastavení vzhledu celé aplikace.</value>
  </data>
  <data name="ServePointTimeoutsConfig_Brief" xml:space="preserve">
    <value>Timeouty</value>
  </data>
  <data name="ServePointTimeoutsConfig_Detail" xml:space="preserve">
    <value>Všechny timeouty používané ve výdejním místě</value>
  </data>
  <data name="ServePointAppearanceBasicButtonsConfig_Brief" xml:space="preserve">
    <value>Základní tlačítka</value>
  </data>
  <data name="ServePointAppearanceBasicButtonsConfig_Detail" xml:space="preserve">
    <value>Nastavení vzhledu základních tlačítek</value>
  </data>
  <data name="ServePointAppearanceGridViewConfig_Brief" xml:space="preserve">
    <value>Tabulkové zobrazení</value>
  </data>
  <data name="ServePointAppearanceGridViewConfig_Detail" xml:space="preserve">
    <value>Nastavení vzhledu tabulek.</value>
  </data>
  <data name="ServePointAppearanceAdditionalButtonsConfig_Brief" xml:space="preserve">
    <value>Doplňková tlačítka</value>
  </data>
  <data name="ServePointAppearanceAdditionalButtonsConfig_Detail" xml:space="preserve">
    <value>Nastavení vzhledu doplňkových tlačítek</value>
  </data>
  <data name="GlobalSupportContactsAneteConfig_Brief" xml:space="preserve">
    <value>Support ANETE</value>
  </data>
  <data name="GlobalSupportContactsAneteConfig_Detail" xml:space="preserve">
    <value>Kontakty na support ANETE</value>
  </data>
  <data name="GlobalHwEanSerialReaderConfig_Brief" xml:space="preserve">
    <value>Sériová čtečka čárových kódů</value>
  </data>
  <data name="GlobalHwEanSerialReaderConfig_Detail" xml:space="preserve">
    <value>Konfigurace sériové čtečky čárových kódů</value>
  </data>
  <data name="GlobalSupportContactsLocalConfig_Brief" xml:space="preserve">
    <value>Support u zákazníka</value>
  </data>
  <data name="GlobalSupportContactsLocalConfig_Detail" xml:space="preserve">
    <value>Kontakty na support zákazníka</value>
  </data>
  <data name="OfficeBehaviourPricingConfig_Brief" xml:space="preserve">
    <value>Cenotvorba</value>
  </data>
  <data name="OfficeBehaviourPricingConfig_Detail" xml:space="preserve">
    <value>Konfigurace cenotvorby</value>
  </data>
  <data name="PresPointBehaviourScreenSaverConfig_Brief" xml:space="preserve">
    <value>Šetřič obrazovky</value>
  </data>
  <data name="PresPointBehaviourScreenSaverConfig_Detail" xml:space="preserve">
    <value>Konfigurace šetříče obrazovky</value>
  </data>
  <data name="GlobalBehaviourAutoUpdatesConfig_Brief" xml:space="preserve">
    <value>Automatické aktualizace</value>
  </data>
  <data name="GlobalBehaviourAutoUpdatesConfig_Detail" xml:space="preserve">
    <value>Konfigurace automatických aktualizací</value>
  </data>
  <data name="AutoUpdaterBehaviourMonitorConfig_Brief" xml:space="preserve">
    <value>Monitor aktualizací</value>
  </data>
  <data name="AutoUpdaterBehaviourMonitorConfig_Detail" xml:space="preserve">
    <value>Konfigurace monitoru aktualizací</value>
  </data>
  <data name="MenuPresenterBehaviourMenuFilterConfig_Brief" xml:space="preserve">
    <value>Filtr jídelníčku</value>
  </data>
  <data name="MenuPresenterBehaviourMenuFilterConfig_Detail" xml:space="preserve">
    <value>Filt jídelníčku</value>
  </data>
  <data name="MenuPresenterAppearanceFirstLevelHeaderConfig_Brief" xml:space="preserve">
    <value>Vzhled grupy na první úrovni</value>
  </data>
  <data name="MenuPresenterAppearanceFirstLevelHeaderConfig_Detail" xml:space="preserve">
    <value>Vzhled grupy na první úrovni</value>
  </data>
  <data name="MenuPresenterAppearanceSecondLevelHeaderConfig_Brief" xml:space="preserve">
    <value>Vzhled grupy na druhé úrovni</value>
  </data>
  <data name="MenuPresenterAppearanceSecondLevelHeaderConfig_Detail" xml:space="preserve">
    <value>Vzhled grupy na druhé úrovni. Uplatní se pouze pokud je využito tydenní menu</value>
  </data>
  <data name="MenuPresenterBehaviourMealCountsConfig_Brief" xml:space="preserve">
    <value>Nastavení počtů</value>
  </data>
  <data name="MenuPresenterBehaviourMealCountsConfig_Detail" xml:space="preserve">
    <value>Konfigurace nastavení počtů</value>
  </data>
  <data name="CashDeskPluginsGoodsExtendedInfoConfig_Brief" xml:space="preserve">
    <value>Rozšířené informace o sortimentu</value>
  </data>
  <data name="CashDeskPluginsGoodsExtendedInfoConfig_Detail" xml:space="preserve">
    <value>Konfigurace pluginu zobrazujícího rozšířené informace o sortimentu</value>
  </data>
  <data name="ServePointBehaviourSellingSettings_Brief" xml:space="preserve">
    <value>Konfigurace prodeje jídel a sortimentu</value>
  </data>
  <data name="ServePointBehaviourSellingSettings_Detail" xml:space="preserve">
    <value>Obecné nastavení možností prodeje jídel a sortimentu</value>
  </data>
  <data name="GlobalBehaviourDiscountSystemConfig_Brief" xml:space="preserve">
    <value>Slevový systém</value>
  </data>
  <data name="GlobalBehaviourDiscountSystemConfig_Detail" xml:space="preserve">
    <value>Konfigurace slevového systému</value>
  </data>
  <data name="GlobalHwEWalletConfig_Brief" xml:space="preserve">
    <value>Elektronická peněženka</value>
  </data>
  <data name="GlobalHwEWalletConfig_Detail" xml:space="preserve">
    <value>Konfigurace elektronické peněženky</value>
  </data>
  <data name="CashDeskHwEWalletConfig_Brief" xml:space="preserve">
    <value>Elektronická peněženka</value>
  </data>
  <data name="CashDeskHwEWalletConfig_Detail" xml:space="preserve">
    <value>Konfigurace elektronické peněženky</value>
  </data>
  <data name="PresPointBehaviourCurtainConfig_Brief" xml:space="preserve">
    <value>Opona</value>
  </data>
  <data name="PresPointBehaviourCurtainConfig_Detail" xml:space="preserve">
    <value>Konfigurace opony, která zabraňuje problikávání při zobrazení formuláře po přihlášení uživatele.</value>
  </data>
  <data name="GlobalServicesFbsServicesConfig_Brief" xml:space="preserve">
    <value>Služby</value>
  </data>
  <data name="GlobalServicesFbsServicesConfig_Detail" xml:space="preserve">
    <value>Konfigurace WCF služeb</value>
  </data>
  <data name="GlobalServicesFbsCredentialsConfig_Brief" xml:space="preserve">
    <value>Přihlašovací údaje</value>
  </data>
  <data name="GlobalServicesFbsCredentialsConfig_Detail" xml:space="preserve">
    <value>Přihlašovací údaje pro WCF služby. </value>
  </data>
  <data name="OfficeBehaviourAppSingleInstanceConfig_Brief" xml:space="preserve">
    <value>Vícenásobné spuštění aplikace</value>
  </data>
  <data name="OfficeBehaviourAppSingleInstanceConfig_Detail" xml:space="preserve">
    <value>Konfigurace (ne)možnosti vícenásobného spuštění dané aplikace.</value>
  </data>
  <data name="PresPointBahaviourAttendanceConfig_Brief" xml:space="preserve">
    <value>Docházka</value>
  </data>
  <data name="PresPointBahaviourAttendanceConfig_Detail" xml:space="preserve">
    <value>Konfigurace docházky</value>
  </data>
  <data name="GlobalBehaviourKitchenOrderPrintConfig_Brief" xml:space="preserve">
    <value>Objednávka do kuchyně</value>
  </data>
  <data name="GlobalBehaviourKitchenOrderPrintConfig_Detail" xml:space="preserve">
    <value>Konfigurace pro tisk objednávky do kuchyně</value>
  </data>
  <data name="AutoUpdaterDateTimeSynchronizationConfig_Brief" xml:space="preserve">
    <value>Synchronizace času</value>
  </data>
  <data name="AutoUpdaterDateTimeSynchronizationConfig_Detail" xml:space="preserve">
    <value>Konfigurace synchronizace času</value>
  </data>
  <data name="OfficeMealDistributionBehaviourInquiriesConfig_Brief" xml:space="preserve">
    <value>Poptávky distribuce jídel</value>
  </data>
  <data name="OfficeMealDistributionBehaviourInquiriesConfig_Detail" xml:space="preserve">
    <value>Konfigurace poptávek distribuce jídel</value>
  </data>
  <data name="GlobalServicesDbConnectionConfig_Brief" xml:space="preserve">
    <value>Přihlášení k databázi</value>
  </data>
  <data name="GlobalServicesDbConnectionConfig_Detail" xml:space="preserve">
    <value>Přihlašovací údaje k databázím</value>
  </data>
  <data name="GlobalBehaviourTechSupportMessagingConfig_Brief" xml:space="preserve">
    <value>Zasílání zpráv na support</value>
  </data>
  <data name="GlobalBehaviourTechSupportMessagingConfig_Detail" xml:space="preserve">
    <value>Konfigurace zasílání zpráv na support</value>
  </data>
  <data name="MobileOrderingAuthenticationConfig_Brief" xml:space="preserve">
    <value>Autorizace</value>
  </data>
  <data name="MobileOrderingAuthenticationConfig_Detail" xml:space="preserve">
    <value>Konfigurace autorizace mobilního objednávání</value>
  </data>
  <data name="OfficeClientsBehaviourEBankingImportConfig_Brief" xml:space="preserve">
    <value>E-Banking - import</value>
  </data>
  <data name="OfficeClientsBehaviourEBankingImportConfig_Detail" xml:space="preserve">
    <value>Nastavení importu plateb v E-Bankingu</value>
  </data>
  <data name="OfficeClientsBehaviourCardStockConfig_Brief" xml:space="preserve">
    <value>Sklad karet</value>
  </data>
  <data name="OfficeClientsBehaviourCardStockConfig_Detail" xml:space="preserve">
    <value>Nastavení skladu karet</value>
  </data>
  <data name="MobileOrderingBehaviorMenuConfig_Brief" xml:space="preserve">
    <value>Menu</value>
  </data>
  <data name="MobileOrderingBehaviorMenuConfig_Detail" xml:space="preserve">
    <value>Zobrazení menu mobilního objednávání</value>
  </data>
  <data name="OfficeClientsBehaviourCardIssueConfig_Brief" xml:space="preserve">
    <value>Výdej karty</value>
  </data>
  <data name="OfficeClientsBehaviourCardIssueConfig_Detail" xml:space="preserve">
    <value>Konfigurace výdeje karty</value>
  </data>
  <data name="PresPointBehaviourLoginConfig_Brief" xml:space="preserve">
    <value>Přihlášení/Odhlášení</value>
  </data>
  <data name="PresPointBehaviourLoginConfig_Detail" xml:space="preserve">
    <value>Konfigurace přihlášení a odhlášení klienta.</value>
  </data>
  <data name="OfficeMealDistributionBehaviourInquirySummaryConfig_Brief" xml:space="preserve">
    <value>Přehled poptávek</value>
  </data>
  <data name="OfficeMealDistributionBehaviourInquirySummaryConfig_Detail" xml:space="preserve">
    <value>Konfigurace přehledu poptávek</value>
  </data>
  <data name="SchedulerPluginsStockMovementSenderConfig_Brief" xml:space="preserve">
    <value>Odesílání skladových pohybů</value>
  </data>
  <data name="SchedulerPluginsStockMovementSenderConfig_Detail" xml:space="preserve">
    <value>Nastavení parametrů pro odesílání skladových pohybů na Fbs. 
Konfigurace WCF služeb je v Global.Services.Fbs.Services a Global.Services.Fbs.Credentials.
Pozor: Scheduler si přenačítá změny v konfiguraci každých 5 min. Při změně v konfiguraci nebo plánu není třeba scheduler restartovat.</value>
  </data>
  <data name="GlobalBehaviourApplicationCountryConfig_Brief" xml:space="preserve">
    <value>Stát</value>
  </data>
  <data name="GlobalBehaviourApplicationCountryConfig_Detail" xml:space="preserve">
    <value>Konfigurace státu</value>
  </data>
  <data name="GlobalHwPaymentTermConfig_Brief" xml:space="preserve">
    <value>Platební terminál</value>
  </data>
  <data name="GlobalHwPaymentTermConfig_Detail" xml:space="preserve">
    <value>Nastavení platebního terminálu</value>
  </data>
  <data name="CashDeskManagerConfig_Brief" xml:space="preserve">
    <value>Manažer</value>
  </data>
  <data name="CashDeskManagerConfig_Detail" xml:space="preserve">
    <value>Konfigurace modulů manažeru kasy</value>
  </data>
  <data name="GlobalRulesAccountingMidnightConfig_Brief" xml:space="preserve">
    <value>Účetní půlnoc</value>
  </data>
  <data name="GlobalRulesAccountingMidnightConfig_Detail" xml:space="preserve">
    <value>Konfigurace parametrů souvisejících s účetní půlnocí</value>
  </data>
  <data name="OfficePriceMakingBehaviourSaleSummaryConfig_Brief" xml:space="preserve">
    <value>Přehled prodaného zboží a jídel</value>
  </data>
  <data name="OfficePriceMakingBehaviourSaleSummaryConfig_Detail" xml:space="preserve">
    <value>Konfigurace cenových složek pro přehled (pivot) prodaného zboží a jídel</value>
  </data>
  <data name="GlobalRulesPasswordConfig_Brief" xml:space="preserve">
    <value>Složitost hesla</value>
  </data>
  <data name="GlobalRulesPasswordConfig_Detail" xml:space="preserve">
    <value>Konfigurace složitosti hesla (jeho délka, povolené znaky, atd.)</value>
  </data>
  <data name="GlobalRulesCashDeskConfig_Brief" xml:space="preserve">
    <value>Souhrn pokladny</value>
  </data>
  <data name="GlobalRulesCashDeskConfig_Detail" xml:space="preserve">
    <value>Konfigurace souhrnu pokladny</value>
  </data>
  <data name="OfficeWorkplacesBehaviourMealTypeConfig_Brief" xml:space="preserve">
    <value>Typy jídla</value>
  </data>
  <data name="OfficeWorkplacesBehaviourMealTypeConfig_Detail" xml:space="preserve">
    <value>Konfigurace typů jídel</value>
  </data>
  <data name="GlobalBehaviourClientForceChangePasswordConfig_Brief" xml:space="preserve">
    <value>Vynucení změny hesla klientem</value>
  </data>
  <data name="GlobalBehaviourClientForceChangePasswordConfig_Detail" xml:space="preserve">
    <value />
  </data>
  <data name="GlobalBehaviourClientRequireAgreementWithTermsConfig_Brief" xml:space="preserve">
    <value>Požadavek odsouhlasení podmínek užívání klientem</value>
  </data>
  <data name="GlobalBehaviourClientRequireAgreementWithTermsConfig_Detail" xml:space="preserve">
    <value />
  </data>
  <data name="OfficeWorkplacesReportsWeekMenuConfig_Brief" xml:space="preserve">
    <value>Týdenní menu</value>
  </data>
  <data name="OfficeWorkplacesReportsWeekMenuConfig_Detail" xml:space="preserve">
    <value>Konfigurace sestavy týdenní menu</value>
  </data>
  <data name="OfficeWorkplacesReportsTemplatesDayMenuConfig_Brief" xml:space="preserve">
    <value>Denní jídelníček</value>
  </data>
  <data name="OfficeWorkplacesReportsTemplatesDayMenuConfig_Detail" xml:space="preserve">
    <value>Konfigurace vzhledu denního jídelníčku</value>
  </data>
  <data name="OfficeWorkplacesReportsTemplatesWeekMenuConfig_Brief" xml:space="preserve">
    <value>Tydenní jídelníček</value>
  </data>
  <data name="OfficeWorkplacesReportsTemplatesWeekMenuConfig_Detail" xml:space="preserve">
    <value>Konfigurace vzhledu tydenního jídelníčku</value>
  </data>
  <data name="GlobalBehaviourClientAssignmentConfig_Brief" xml:space="preserve">
    <value>Zařazení klienta</value>
  </data>
  <data name="GlobalBehaviourClientAssignmentConfig_Detail" xml:space="preserve">
    <value>Konfigurace zařazení klienta</value>
  </data>
  <data name="GlobalRulesSpendingAndSubsidyConfig_Brief" xml:space="preserve">
    <value>Konfigurace sestavy - Přehled útrat a příspěvků</value>
  </data>
  <data name="GlobalRulesSpendingAndSubsidyConfig_Detail" xml:space="preserve">
    <value>Konfigurace zpřístupnění sestavy a nastavení ceníkových složek.</value>
  </data>
  <data name="GlobalBehaviourFbsMenuConfig_Brief" xml:space="preserve">
    <value>Synchronizace jídelníčku s FBS</value>
  </data>
  <data name="GlobalBehaviourFbsMenuConfig_Detail" xml:space="preserve">
    <value>Konfigurace synchronizace jídelníčku s FBS</value>
  </data>
  <data name="OfficeWorkplacesBehaviourMenuConfig_Brief" xml:space="preserve">
    <value>Editace jídelníčku</value>
  </data>
  <data name="OfficeWorkplacesBehaviourMenuConfig_Detail" xml:space="preserve">
    <value>Konfigurace editoru jídelníčku</value>
  </data>
  <data name="SchedulerPluginsFbsMenuSyncConfig_Brief" xml:space="preserve">
    <value>Synchronizace menu s Fbs</value>
  </data>
  <data name="SchedulerPluginsFbsMenuSyncConfig_Detail" xml:space="preserve">
    <value>Nastavení synchronizace menu s Fbs</value>
  </data>
  <data name="SchedulerPluginsHelpDeskSyncConfig_Brief" xml:space="preserve">
    <value>Synchronizace s HelpDesk</value>
  </data>
  <data name="SchedulerPluginsHelpDeskSyncConfig_Detail" xml:space="preserve">
    <value>Nastavení synchronizace s HelpDesk</value>
  </data>
  <data name="GlobalServicesHelpDeskCredentialsConfig_Brief" xml:space="preserve">
    <value>Přihlašovací údaje</value>
  </data>
  <data name="GlobalServicesHelpDeskCredentialsConfig_Detail" xml:space="preserve">
    <value>Přihlašovací údaje pro WCF služby.</value>
  </data>
  <data name="GlobalServicesHelpDeskServicesConfig_Brief" xml:space="preserve">
    <value>Služby</value>
  </data>
  <data name="GlobalServicesHelpDeskServicesConfig_Detail" xml:space="preserve">
    <value>Konfigurace WCF služeb</value>
  </data>
  <data name="GlobalRulesNutritionalValuesConfig_Brief" xml:space="preserve">
    <value>Nutriční hodnoty</value>
  </data>
  <data name="GlobalRulesNutritionalValuesConfig_Detail" xml:space="preserve">
    <value>Nastavení nutričních hodnot. Používá se v PM, kde se tímto nastavením řídí zobrazení nutričních hodnot v sestavě pro přihlášeného klienta. V Kan8 se dle tohoto nastavení inicializuje sestava s nutričními hodnotami pro daného klienta. Uplatní se pouze pokud existuje licence.</value>
  </data>
  <data name="GlobalBehaviorCampaignsConfig_Brief" xml:space="preserve">
    <value>Kampaně</value>
  </data>
  <data name="GlobalBehaviorCampaignsConfig_Detail" xml:space="preserve">
    <value>Nastavení pro anketní kampaně</value>
  </data>
  <data name="MenuPresenterBehaviourNoteConfig_Brief" xml:space="preserve">
    <value>Poznámka</value>
  </data>
  <data name="MenuPresenterBehaviourNoteConfig_Detail" xml:space="preserve">
    <value>Konfigurace nastavení zobrazení poznámky. Definuje se, jaké informace budou v poznámce zobrazeny.</value>
  </data>
  <data name="PresPointBehaviourNoteConfig_Brief" xml:space="preserve">
    <value>Poznámka</value>
  </data>
  <data name="PresPointBehaviourNoteConfig_Detail" xml:space="preserve">
    <value>Konfigurace zobrazení poznámky</value>
  </data>
  <data name="PresPointBehaviourStartMenuConfig_Brief" xml:space="preserve">
    <value>Jídelníček na přihlašovací obrazovce</value>
  </data>
  <data name="PresPointBehaviourStartMenuConfig_Detail" xml:space="preserve">
    <value>Konfigurace jídelníčku na přihlašovací obrazovce</value>
  </data>
  <data name="GlobalBehaviorEetConfig_Brief" xml:space="preserve">
    <value>Eet</value>
  </data>
  <data name="GlobalBehaviorEetConfig_Detail" xml:space="preserve">
    <value>Nastavení pro EET</value>
  </data>
  <data name="GlobalServicesAppServerConnectionConfig_Brief" xml:space="preserve">
    <value>Aplikační server</value>
  </data>
  <data name="GlobalServicesAppServerConnectionConfig_Detail" xml:space="preserve">
    <value>Konfigurace připojení k aplikačnímu serveru</value>
  </data>
  <data name="GlobalBehaviourClientAccommodationConfig_Brief" xml:space="preserve">
    <value>Ubytování klienta</value>
  </data>
  <data name="GlobalBehaviourClientAccommodationConfig_Detail" xml:space="preserve">
    <value>Konfigurace ubytování klienta</value>
  </data>
  <data name="GlobalBehaviourDebugConfig_Brief" xml:space="preserve">
    <value>Ladění aplikace</value>
  </data>
  <data name="GlobalBehaviourDebugConfig_Detail" xml:space="preserve">
    <value>Nastavení pro ladění aplikace</value>
  </data>
  <data name="GlobalSupportRemoteAccessConfig_Brief" xml:space="preserve">
    <value>Vzdálená správa</value>
  </data>
  <data name="GlobalSupportRemoteAccessConfig_Detail" xml:space="preserve">
    <value>Konfigurace vzdálené správy. Zjednodušuje spouštění TeamVieweru.</value>
  </data>
  <data name="SloHealthyMenuConfig_Brief" xml:space="preserve">
    <value>Zdrávé jídlo</value>
  </data>
  <data name="SloHealthyMenuConfig_Detail" xml:space="preserve">
    <value>Konfigrace zdravého jídla. Definuje, které jídlo je prezentováno jako zdravé a seznam druhů jídel, z kterých se určí složky zdravého jídla.</value>
  </data>
  <data name="CashDeskPluginsTulPluginConfig_Brief" xml:space="preserve">
    <value>Nastavení pro Tul</value>
  </data>
  <data name="CashDeskPluginsTulPluginConfig_Detail" xml:space="preserve">
    <value>Konfigurace pluginu pro Tul</value>
  </data>
  <data name="GlobalServicesWebProxyConfig_Brief" xml:space="preserve">
    <value>Nastavení odchozí HTTP proxy</value>
  </data>
  <data name="GlobalServicesWebProxyConfig_Detail" xml:space="preserve">
    <value>Konfigurace pro nastavení odchozí HTTP proxy</value>
  </data>
  <data name="CashDeskBehaviorEetConfig_Brief" xml:space="preserve">
    <value>EET</value>
  </data>
  <data name="CashDeskBehaviorEetConfig_Detail" xml:space="preserve">
    <value>Konfigurace EET</value>
  </data>
  <data name="GlobalRulesClientUserNameConfig_Brief" xml:space="preserve">
    <value>Uživatelské jméno</value>
  </data>
  <data name="GlobalRulesClientUserNameConfig_Detail" xml:space="preserve">
    <value>Konfigurace povolených znaků uživatelského jména pro WebKredit. Nastavuje se v evidenčním listu klienta.</value>
  </data>
  <data name="GlobalHwVgaClientDisplayConfig_Brief" xml:space="preserve">
    <value>VGA zákaznický displej</value>
  </data>
  <data name="GlobalHwVgaClientDisplayConfig_Detail" xml:space="preserve">
    <value>Nastavení VGA zákaznického displeje</value>
  </data>
  <data name="OfficeMealDistributionReportsDeliveryAlmedConfig_Brief" xml:space="preserve">
    <value>Dodací listy ALMED</value>
  </data>
  <data name="OfficeMealDistributionReportsDeliveryAlmedConfig_Detail" xml:space="preserve">
    <value>Konfigurace dodacího listu ALMED</value>
  </data>
  <data name="OfficeWorkplacesBehaviourOrderChangesConfig_Brief" xml:space="preserve">
    <value>Přehled změn dle objednávek</value>
  </data>
  <data name="OfficeWorkplacesBehaviourOrderChangesConfig_Detail" xml:space="preserve">
    <value>Nastavení nabízených druhu jídel ve filtru u Přehledu změn dle objednávek. </value>
  </data>
  <data name="GlobalServicesDbMaintenanceConfig_Brief" xml:space="preserve">
    <value>Údržba databáze</value>
  </data>
  <data name="GlobalServicesDbMaintenanceConfig_Detail" xml:space="preserve">
    <value>Konfigurace údržby databáze</value>
  </data>
  <data name="OfficeBehaviorEetConfig_Brief" xml:space="preserve">
    <value>EET</value>
  </data>
  <data name="OfficeBehaviorEetConfig_Detail" xml:space="preserve">
    <value>Nastavení pro EET</value>
  </data>
  <data name="GlobalHwNawiScaleConfig_Brief" xml:space="preserve">
    <value>Váha Nawi</value>
  </data>
  <data name="GlobalHwNawiScaleConfig_Detail" xml:space="preserve">
    <value>Konfigurace certifikované váhy</value>
  </data>
  <data name="GlobalRulesCollectionConfig_Brief" xml:space="preserve">
    <value>Inkaso</value>
  </data>
  <data name="GlobalRulesCollectionConfig_Detail" xml:space="preserve">
    <value>Konfigurace inkasa</value>
  </data>
  <data name="GlobalServicesIpsConfig_Brief" xml:space="preserve">
    <value>IPS</value>
  </data>
  <data name="GlobalServicesIpsConfig_Detail" xml:space="preserve">
    <value>Konfigurace interního platebního systému</value>
  </data>
  <data name="MenuPresenterHwIpCamConfig_Brief" xml:space="preserve">
    <value>IP kamera</value>
  </data>
  <data name="MenuPresenterHwIpCamConfig_Detail" xml:space="preserve">
    <value>Konfigurace IP kamery</value>
  </data>
  <data name="DevicesControllerOfflineConfig_Brief" xml:space="preserve">
    <value>Offline</value>
  </data>
  <data name="DevicesControllerOfflineConfig_Detail" xml:space="preserve">
    <value>Konfigurace režimu offline</value>
  </data>
  <data name="PresPointBehaviourCanteenConfig_Brief" xml:space="preserve">
    <value>Implicitní výdejna</value>
  </data>
  <data name="PresPointBehaviourCanteenConfig_Detail" xml:space="preserve">
    <value>Nastavení způsobu určení implicitní výdejny po přihlášení strávníka</value>
  </data>
  <data name="GlobalRulesGdprPersonalDataAgreementConfig_Brief" xml:space="preserve">
    <value>Zpracování osobních údajů</value>
  </data>
  <data name="GlobalRulesGdprPersonalDataAgreementConfig_Detail" xml:space="preserve">
    <value>Konfigurace pro souhlasy se zpracováním osobních údajů</value>
  </data>
  <data name="OfficeWorkplacesReportsImpersonationCahConfig_Brief" xml:space="preserve">
    <value>CAH - Export celkového přehledu po jednotlivcích </value>
  </data>
  <data name="OfficeWorkplacesReportsImpersonationCahConfig_Detail" xml:space="preserve">
    <value>CAH - Export celkového přehledu po jednotlivcích do formátu csv. Nastavení impersonace, názvu souboru a místa uložení souboru.</value>
  </data>
  <data name="DevicesControllerBehaviourAppInstallationsConfig_Brief" xml:space="preserve">
    <value>Obsluhovaná zařízení</value>
  </data>
  <data name="DevicesControllerBehaviourAppInstallationsConfig_Detail" xml:space="preserve">
    <value>Konfigurace seznamu obsluhovaných zařízení</value>
  </data>
  <data name="SchedulerPluginsAutoUpdaterConfig_Brief" xml:space="preserve">
    <value>Generování aktualizačních dávek</value>
  </data>
  <data name="SchedulerPluginsAutoUpdaterConfig_Detail" xml:space="preserve">
    <value>Nastavení generování aktualizačních dávek</value>
  </data>
  <data name="DevicesControllerServicesToolsConfig_Brief" xml:space="preserve">
    <value>Nástroje</value>
  </data>
  <data name="DevicesControllerServicesToolsConfig_Detail" xml:space="preserve">
    <value>Nastavení nástroju pro správu terminálu Anete</value>
  </data>
  <data name="GuestOrderingBehaviourParagonConfig_Brief" xml:space="preserve">
    <value>Paragon</value>
  </data>
  <data name="GuestOrderingBehaviourParagonConfig_Detail" xml:space="preserve">
    <value>Konfigurace vytváření paragonu</value>
  </data>
  <data name="CashDeskBehaviourServiceDisplayConfig_Brief" xml:space="preserve">
    <value>Nastavení displeje obsluhy</value>
  </data>
  <data name="CashDeskBehaviourServiceDisplayConfig_Detail" xml:space="preserve">
    <value>Nastavení displeje obsluhy (kasy)</value>
  </data>
  <data name="OfficeWorkplacesBehaviourImportNisConfig_Brief" xml:space="preserve">
    <value>Import do NISu</value>
  </data>
  <data name="OfficeWorkplacesBehaviourImportNisConfig_Detail" xml:space="preserve">
    <value>Nastavení importu do NISu</value>
  </data>
  <data name="PatientOrderingBehaviourGeneralConfig_Brief" xml:space="preserve">
    <value>Obecné</value>
  </data>
  <data name="PatientOrderingBehaviourGeneralConfig_Detail" xml:space="preserve">
    <value>Obecné</value>
  </data>
  <data name="GlobalBehaviourMealTicketConfig_Brief" xml:space="preserve">
    <value>Tisk stravenek</value>
  </data>
  <data name="GlobalBehaviourMealTicketConfig_Detail" xml:space="preserve">
    <value>Konfigurace tisku stravenek</value>
  </data>
  <data name="GlobalServicesPaymentGateConfig_Brief" xml:space="preserve">
    <value>Platební brána</value>
  </data>
  <data name="GlobalServicesPaymentGateConfig_Detail" xml:space="preserve">
    <value>Nastavení platební brány</value>
  </data>
  <data name="GlobalServicesReportServiceConnectionConfig_Brief" xml:space="preserve">
    <value />
  </data>
  <data name="GlobalServicesReportServiceConnectionConfig_Detail" xml:space="preserve">
    <value />
  </data>
  <data name="AccommodationBehaviourSystemConfig_Brief" xml:space="preserve">
    <value>Ubytovací systém</value>
  </data>
  <data name="AccommodationBehaviourSystemConfig_Detail" xml:space="preserve">
    <value>Ubytovací systém</value>
  </data>
  <data name="AccommodationAppearanceLabelColorsConfig_Brief" xml:space="preserve">
    <value>Nastavení vzhledu ubytování</value>
  </data>
  <data name="AccommodationAppearanceLabelColorsConfig_Detail" xml:space="preserve">
    <value>Nastavení vzhledu ubytování</value>
  </data>
  <data name="WebKreditAccountEBankingConfig_Brief" xml:space="preserve">
    <value>EBanking</value>
  </data>
  <data name="WebKreditAccountEBankingConfig_Detail" xml:space="preserve">
    <value>Nastavení záložky EBanking</value>
  </data>
  <data name="WebKreditAccountSettingsConfig_Brief" xml:space="preserve">
    <value>Nastavení účtu</value>
  </data>
  <data name="WebKreditAccountSettingsConfig_Detail" xml:space="preserve">
    <value>Nastavení záložky Nastavení účtu</value>
  </data>
  <data name="WebKreditAccountGdprConfig_Brief" xml:space="preserve">
    <value>Gdpr</value>
  </data>
  <data name="WebKreditAccountGdprConfig_Detail" xml:space="preserve">
    <value>Nastavení záložky Gdpr</value>
  </data>
  <data name="WebKreditBannersConfig_Brief" xml:space="preserve">
    <value>Bannery</value>
  </data>
  <data name="WebKreditBannersConfig_Detail" xml:space="preserve">
    <value>Nastavení cest k bannerům a reklamám</value>
  </data>
  <data name="WebKreditFeedbackConfig_Brief" xml:space="preserve">
    <value>Zpětná vazba</value>
  </data>
  <data name="WebKreditFeedbackConfig_Detail" xml:space="preserve">
    <value>Nastavení modulu Zpětná vazba</value>
  </data>
  <data name="WebKreditHeaderConfig_Brief" xml:space="preserve">
    <value>Hlavička</value>
  </data>
  <data name="WebKreditHeaderConfig_Detail" xml:space="preserve">
    <value>Nastavení hlavičky hlavní stránky</value>
  </data>
  <data name="WebKreditHistoryConfig_Brief" xml:space="preserve">
    <value>Historie účtu</value>
  </data>
  <data name="WebKreditHistoryConfig_Detail" xml:space="preserve">
    <value>Nastavení modulu Historie účtu</value>
  </data>
  <data name="WebKreditInformationsConfig_Brief" xml:space="preserve">
    <value>Informace</value>
  </data>
  <data name="WebKreditInformationsConfig_Detail" xml:space="preserve">
    <value>Nastavení modulu Informace</value>
  </data>
  <data name="WebKreditOrderingMenuAuthenticatedConfig_Brief" xml:space="preserve">
    <value>Jídelníček - po přihlášení</value>
  </data>
  <data name="WebKreditOrderingMenuAuthenticatedConfig_Detail" xml:space="preserve">
    <value>Nastavení záložky Jídelníček - po přihlášení</value>
  </data>
  <data name="WebKreditOrderingMenuAnonymousConfig_Brief" xml:space="preserve">
    <value>Jídelníček - před přihlášením</value>
  </data>
  <data name="WebKreditOrderingMenuAnonymousConfig_Detail" xml:space="preserve">
    <value>Nastavení záložky Jídelníček - před přihlášením</value>
  </data>
  <data name="WebKreditOrderingOrdersConfig_Brief" xml:space="preserve">
    <value>Objednávky</value>
  </data>
  <data name="WebKreditOrderingOrdersConfig_Detail" xml:space="preserve">
    <value>Nastavení záložky Objednávky</value>
  </data>
  <data name="WebKreditOrderingExchangesConfig_Brief" xml:space="preserve">
    <value>Burza</value>
  </data>
  <data name="WebKreditOrderingExchangesConfig_Detail" xml:space="preserve">
    <value>Nastavení záložky Burza</value>
  </data>
  <data name="WebKreditOrderingConfig_Brief" xml:space="preserve">
    <value>Objednávání</value>
  </data>
  <data name="WebKreditOrderingConfig_Detail" xml:space="preserve">
    <value>Nastavení modulu Objednávání</value>
  </data>
  <data name="WebKreditLocalizationConfig_Brief" xml:space="preserve">
    <value>Lokalizace</value>
  </data>
  <data name="WebKreditLocalizationConfig_Detail" xml:space="preserve">
    <value>Nastavení lokalizace</value>
  </data>
  <data name="WebKreditAuthenticationConfig_Brief" xml:space="preserve">
    <value>Autentifikace</value>
  </data>
  <data name="WebKreditAuthenticationConfig_Detail" xml:space="preserve">
    <value>Nastavení autentifikace</value>
  </data>
  <data name="AccommodationBehaviourAutoOrderingConfig_Brief" xml:space="preserve">
    <value>Automatické vytváření objednávek</value>
  </data>
  <data name="AccommodationBehaviourAutoOrderingConfig_Detail" xml:space="preserve">
    <value>Vytváření objednávek</value>
  </data>
  <data name="GlobalBehaviorEKasaConfig_Brief" xml:space="preserve">
    <value>eKasa</value>
  </data>
  <data name="GlobalBehaviorEKasaConfig_Detail" xml:space="preserve">
    <value>eKasa</value>
  </data>
  <data name="GraphicAccommodationCapacityAppearanceColorsConfig_Brief" xml:space="preserve">
    <value />
  </data>
  <data name="GraphicAccommodationCapacityAppearanceColorsConfig_Detail" xml:space="preserve">
    <value />
  </data>
  <data name="LegendsAccommodationCapacityAppearanceColorsConfig_Brief" xml:space="preserve">
    <value />
  </data>
  <data name="LegendsAccommodationCapacityAppearanceColorsConfig_Detail" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointBehaviourStartScreenPicturesConfig_Brief" xml:space="preserve">
    <value>Obrázky</value>
  </data>
  <data name="PresPointBehaviourStartScreenPicturesConfig_Detail" xml:space="preserve">
    <value>Obrázky na startovací obrazovce</value>
  </data>
  <data name="WebKreditAccommodationsOverviewConfig_Brief" xml:space="preserve">
    <value>Ubytování</value>
  </data>
  <data name="WebKreditAccommodationsOverviewConfig_Detail" xml:space="preserve">
    <value>Nastavení záložky Přehled ubytování</value>
  </data>
  <data name="UbyportConfig_Brief" xml:space="preserve">
    <value>Služba UbyPort</value>
  </data>
  <data name="UbyportConfig_Detail" xml:space="preserve">
    <value>Nastavení webové služby UbyPort</value>
  </data>
  <data name="WebKreditMailingConfig_Brief" xml:space="preserve">
    <value>Odesílání emailů</value>
  </data>
  <data name="WebKreditMailingConfig_Detail" xml:space="preserve">
    <value>Nastavení odesílání emailů</value>
  </data>
  <data name="OfficeClientsBehaviourBankAccountNumberConfig_Brief" xml:space="preserve">
    <value>Číslo účtu</value>
  </data>
  <data name="OfficeClientsBehaviourBankAccountNumberConfig_Detail" xml:space="preserve">
    <value>Nastavení čísla bankovního účtu</value>
  </data>
  <data name="WebKreditAccommodationsHistoryConfig_Brief" xml:space="preserve">
    <value>Přehledy</value>
  </data>
  <data name="WebKreditAccommodationsHistoryConfig_Detail" xml:space="preserve">
    <value>Nastavení záložek ostatních přehledů</value>
  </data>
  <data name="WebKreditRegistrationConfig_Brief" xml:space="preserve">
    <value>Registrace</value>
  </data>
  <data name="WebKreditRegistrationConfig_Detail" xml:space="preserve">
    <value>Nastavení záložky registrace</value>
  </data>
  <data name="PresPointBehaviourMenuMealKindFilterConfig_Brief" xml:space="preserve">
    <value>Filtrování jídel</value>
  </data>
  <data name="PresPointBehaviourMenuMealKindFilterConfig_Detail" xml:space="preserve">
    <value>Konfigurace skupiny jídel</value>
  </data>
  <data name="PresPointBehaviourOrderConfig_Brief" xml:space="preserve">
    <value>Objednávka</value>
  </data>
  <data name="PresPointBehaviourOrderConfig_Detail" xml:space="preserve">
    <value>Nastavení obejednávek</value>
  </data>
  <data name="CashDeskBehaviourSaleSlipSplitConfig_Brief" xml:space="preserve">
    <value>Rozdělení pokladních dokladů</value>
  </data>
  <data name="CashDeskBehaviourSaleSlipSplitConfig_Detail" xml:space="preserve">
    <value>Nastavení rozdělení pokladních dokladů</value>
  </data>
  <data name="OfficeClientsBehaviourInternetAccountReportConfig_Brief" xml:space="preserve">
    <value>Zobrazení sestav internetového účtu</value>
  </data>
  <data name="OfficeClientsBehaviourInternetAccountReportConfig_Detail" xml:space="preserve">
    <value>Povoluje zobrazení sestav internetového účtu</value>
  </data>
  <data name="GlobalRulesGdprClientInfoProtectionConfig_Brief" xml:space="preserve">
    <value />
  </data>
  <data name="GlobalRulesGdprClientInfoProtectionConfig_Detail" xml:space="preserve">
    <value />
  </data>
  <data name="CashDeskBehaviourAccommodationSystemConfig_Brief" xml:space="preserve">
    <value />
  </data>
  <data name="CashDeskBehaviourAccommodationSystemConfig_Detail" xml:space="preserve">
    <value />
  </data>
  <data name="MobileStockBehaviorStocksConfig_Brief" xml:space="preserve">
    <value>Sklady</value>
  </data>
  <data name="MobileStockBehaviorStocksConfig_Detail" xml:space="preserve">
    <value>Nastavení dostupných skladů v mobilní inventuře. Zatím jen jeden parametr, ID skladu.</value>
  </data>
  <data name="GlobalHwCardExternalSystemReaderConfig_Brief" xml:space="preserve">
    <value>Čtečka karet (externí systém)</value>
  </data>
  <data name="GlobalHwCardExternalSystemReaderConfig_Detail" xml:space="preserve">
    <value>Nastavení čtečky karet, která přejímá čísla karet z externího systému.</value>
  </data>
  <data name="InfoProClientConfig_Brief" xml:space="preserve">
    <value>Client</value>
  </data>
  <data name="InfoProClientConfig_Detail" xml:space="preserve">
    <value>Client</value>
  </data>
  <data name="InfoProServerConfig_Brief" xml:space="preserve">
    <value>Server</value>
  </data>
  <data name="InfoProServerConfig_Detail" xml:space="preserve">
    <value>Server</value>
  </data>
  <data name="GlobalBehaviourApplicationLanguageDetailConfig_Brief" xml:space="preserve">
    <value>Jazyk aplikace detail</value>
  </data>
  <data name="GlobalBehaviourApplicationLanguageDetailConfig_Detail" xml:space="preserve">
    <value>Detailní konfigurace jazyka aplikace</value>
  </data>
  <data name="GlobalRulesExchangeRateConfig_Brief" xml:space="preserve">
    <value>Kurz cizí měny</value>
  </data>
  <data name="GlobalRulesExchangeRateConfig_Detail" xml:space="preserve">
    <value>Způsob stanovení kurzu cizí měny</value>
  </data>
  <data name="PresPointBehaviourPaymentTermDepositConfig_Brief" xml:space="preserve">
    <value>Vklad záloh přes platební terminál</value>
  </data>
  <data name="PresPointBehaviourPaymentTermDepositConfig_Detail" xml:space="preserve">
    <value>Vklad záloh přes platební terminál</value>
  </data>
  <data name="GlobalRulesVatRateConfig_Brief" xml:space="preserve">
    <value>Sazba DPH</value>
  </data>
  <data name="GlobalRulesVatRateConfig_Detail" xml:space="preserve">
    <value>Pravidla pro nastavení sazeb DPH</value>
  </data>
  <data name="WebKreditGeneralConfig_Brief" xml:space="preserve">
    <value>Obecné</value>
  </data>
  <data name="WebKreditGeneralConfig_Detail" xml:space="preserve">
    <value>Obecné nastavení</value>
  </data>
  <data name="MobileOrderingBehaviorAccountGdprConfig_Brief" xml:space="preserve">
    <value>Gdpr</value>
  </data>
  <data name="MobileOrderingBehaviorAccountGdprConfig_Detail" xml:space="preserve">
    <value>Nastavení záložky Gdpr</value>
  </data>
  <data name="CashDeskBehaviourVatRateConfig_Brief" xml:space="preserve">
    <value>Sazby DPH</value>
  </data>
  <data name="CashDeskBehaviourVatRateConfig_Detail" xml:space="preserve">
    <value>Nastavení markování v alternativní sazbě DPH</value>
  </data>
  <data name="SchedulerPluginsKreditSyncConfig_Brief" xml:space="preserve">
    <value>Volálí služeb DB Kredit</value>
  </data>
  <data name="SchedulerPluginsKreditSyncConfig_Detail" xml:space="preserve">
    <value>Volání služeb DB Kredit - Sazby DPH apod.</value>
  </data>
  <data name="SchedulerPluginsBatchReportConfig_Brief" xml:space="preserve">
    <value>Hromadné tisky</value>
  </data>
  <data name="SchedulerPluginsBatchReportConfig_Detail" xml:space="preserve">
    <value>Automatické spouštění hromadných tisků. V této chvíli probíha v Kanceláři. Je vhodné vyčlenit jedno ID zařízení Kanceláře, která se o to bude starat.</value>
  </data>
  <data name="AccommodationSchedulerConfig_Brief" xml:space="preserve">
    <value>Hotelové ubytování</value>
  </data>
  <data name="AccommodationSchedulerConfig_Detail" xml:space="preserve">
    <value>Nastavení hotelového ubytování</value>
  </data>
  <data name="OfficeBehaviourFilterSettingsConfig_Brief" xml:space="preserve">
    <value>Hodnota filtru</value>
  </data>
  <data name="OfficeBehaviourFilterSettingsConfig_Detail" xml:space="preserve">
    <value>Umožňuje nastavit výchozí hodnotu filtru ve formulářích na "Nic" - není vybrána žádná  hodnota ze seznamu. Definuje se pro každý formulář zvlášť.</value>
  </data>
  <data name="CashDeskBehaviourRestaurantConfig_Brief" xml:space="preserve">
    <value>Nastavení restaurační kasy</value>
  </data>
  <data name="CashDeskBehaviourRestaurantConfig_Detail" xml:space="preserve">
    <value>Nastavení restaurační kasy pro využití grafického editoru restaurace</value>
  </data>
  <data name="OfficePriceMakingBehaviourSubsidyCategoryConfig_Brief" xml:space="preserve">
    <value>Povolení kategorie dotace</value>
  </data>
  <data name="OfficePriceMakingBehaviourSubsidyCategoryConfig_Detail" xml:space="preserve">
    <value>Povolení kategorie dotace v editoru Sazba DPH pro jídla povolí editaci jednotlivých kategorií dotace.</value>
  </data>
  <data name="OfficeWorkplacesBehaviourPriceListConfig_Brief" xml:space="preserve">
    <value>Ceník jídel</value>
  </data>
  <data name="OfficeWorkplacesBehaviourPriceListConfig_Detail" xml:space="preserve">
    <value>Konfigurace ceníku jídel (validace apod.)</value>
  </data>
  <data name="OfficeMealDistributionBehaviourIssueSlipConfig_Brief" xml:space="preserve">
    <value>Odpis jídel</value>
  </data>
  <data name="OfficeMealDistributionBehaviourIssueSlipConfig_Detail" xml:space="preserve">
    <value>Konfigurace odpisu jídel</value>
  </data>
  <data name="CashDeskBehaviourLoginConfig_Brief" xml:space="preserve">
    <value>Konfigurace náhradního přihlášení</value>
  </data>
  <data name="CashDeskBehaviourLoginConfig_Detail" xml:space="preserve">
    <value>Tato třída je určena pro podrobnější nastavení náhradního přihlášení v Kase</value>
  </data>
  <data name="MobileOrderingBehaviourPriceSetupConfig_Brief" xml:space="preserve">
    <value>Nastavení cen</value>
  </data>
  <data name="MobileOrderingBehaviourPriceSetupConfig_Detail" xml:space="preserve">
    <value>Konfigurace parametrů pro zobrazení a nastavení cen</value>
  </data>
  <data name="WaiterCashDeskBehaviourCashDeskPairingConfig_Brief" xml:space="preserve">
    <value>Párování s Kasou</value>
  </data>
  <data name="WaiterCashDeskBehaviourCashDeskPairingConfig_Detail" xml:space="preserve">
    <value>Nastavení párování Číšnické Kasy s běžnou Kasou</value>
  </data>
  <data name="CashDeskDataStorageConfig_Brief" xml:space="preserve">
    <value>Datové uložiště</value>
  </data>
  <data name="CashDeskDataStorageConfig_Detail" xml:space="preserve">
    <value>Nastavení datového uložiště</value>
  </data>
  <data name="GlobalAuthenticationClientConfig_Brief" xml:space="preserve">
    <value>Klienti</value>
  </data>
  <data name="GlobalAuthenticationClientConfig_Detail" xml:space="preserve">
    <value>Konfigurace autentizace klientů oproti aplikačnímu serveru</value>
  </data>
  <data name="GlobalAuthenticationServerConfig_Brief" xml:space="preserve">
    <value>Server</value>
  </data>
  <data name="GlobalAuthenticationServerConfig_Detail" xml:space="preserve">
    <value>Konfigurace typů autentizace</value>
  </data>
  <data name="SchedulerPluginsAppServerConfig_Brief" xml:space="preserve">
    <value>Volání služeb AppServeru</value>
  </data>
  <data name="SchedulerPluginsAppServerConfig_Detail" xml:space="preserve">
    <value>Volání služeb AppServeru</value>
  </data>
  <data name="CashDeskBehaviourNotesConfig_Brief" xml:space="preserve">
    <value>Poznámky</value>
  </data>
  <data name="CashDeskBehaviourNotesConfig_Detail" xml:space="preserve">
    <value>Nastavení poznámek</value>
  </data>
  <data name="SchedulerPrefixesAxxosConfig_Brief" xml:space="preserve">
    <value>AXXOS</value>
  </data>
  <data name="SchedulerPrefixesAxxosConfig_Detail" xml:space="preserve">
    <value>AXXOS</value>
  </data>
  <data name="SchedulerPrefixesGerecConfig_Brief" xml:space="preserve">
    <value>GEREC</value>
  </data>
  <data name="SchedulerPrefixesGerecConfig_Detail" xml:space="preserve">
    <value>GEREC</value>
  </data>
  <data name="GlobalBehaviourKitchenOrderDisplayConfig_Brief" xml:space="preserve">
    <value>Displej v kuchyni</value>
  </data>
  <data name="GlobalBehaviourKitchenOrderDisplayConfig_Detail" xml:space="preserve">
    <value>Nastavení použití displejů v kuchyni</value>
  </data>
  <data name="GlobalSyncUsersConfig_Brief" xml:space="preserve">
    <value>Synchronizace uživatelů</value>
  </data>
  <data name="GlobalSyncUsersConfig_Detail" xml:space="preserve">
    <value>Synchronizace uživatelů</value>
  </data>
  <data name="GlobalSyncClientsConfig_Brief" xml:space="preserve">
    <value>Synchronizace strávníků</value>
  </data>
  <data name="GlobalSyncClientsConfig_Detail" xml:space="preserve">
    <value>Synchronizace strávníků</value>
  </data>
  <data name="CashDeskBehaviourBalanceSaleSlipConfig_Brief" xml:space="preserve">
    <value>Vyrovnávání položek paragonu</value>
  </data>
  <data name="CashDeskBehaviourBalanceSaleSlipConfig_Detail" xml:space="preserve">
    <value>Vyrovnávání položek paragonu</value>
  </data>
  <data name="WaiterCashDeskBehaviourSynchronizationConfig_Brief" xml:space="preserve">
    <value>Synchronizace s Kasou</value>
  </data>
  <data name="WaiterCashDeskBehaviourSynchronizationConfig_Detail" xml:space="preserve">
    <value>Synchronizace s Kasou</value>
  </data>
  <data name="ServiceMonitorBehaviourLatencyMonitorConfig_Brief" xml:space="preserve">
    <value>Monitorování odezvy sítě</value>
  </data>
  <data name="ServiceMonitorBehaviourLatencyMonitorConfig_Detail" xml:space="preserve">
    <value>Monitorování odezvy sítě</value>
  </data>
  <data name="MobileOrderingBehaviourWebKreditConfig_Brief" xml:space="preserve">
    <value>WebKredit</value>
  </data>
  <data name="MobileOrderingBehaviourWebKreditConfig_Detail" xml:space="preserve">
    <value>Nastavení spojení MobilKreditu s WebKreditem</value>
  </data>
  <data name="WaiterCashDeskGeneralConfig_Brief" xml:space="preserve">
    <value>Obecné</value>
  </data>
  <data name="WaiterCashDeskGeneralConfig_Detail" xml:space="preserve">
    <value>Obecné nastavení</value>
  </data>
  <data name="GlobalServiceSysLogConfig_Brief" xml:space="preserve">
    <value>SysLog</value>
  </data>
  <data name="GlobalServiceSysLogConfig_Detail" xml:space="preserve">
    <value>Nastavení SysLog serveru, na který mohou být Schedulerem odesílany logy.</value>
  </data>
  <data name="GlobalSyncAccessRightsConfig_Brief" xml:space="preserve">
    <value>Přistupová práva</value>
  </data>
  <data name="GlobalSyncAccessRightsConfig_Detail" xml:space="preserve">
    <value>Přistupová práva k synchronizovaným uživatelům</value>
  </data>
  <data name="CashDeskBehaviourSalesVoucherConfig_Brief" xml:space="preserve">
    <value>Zpracování slevových voucherů</value>
  </data>
  <data name="CashDeskBehaviourSalesVoucherConfig_Detail" xml:space="preserve">
    <value>Možnost nastavit formát slevových voucheru tak, aby byly při makrování automaticky aplikovány slevy</value>
  </data>
  <data name="OfficeCentralManagementBehaviourRdpConfig_Brief" xml:space="preserve">
    <value>Nastavení spouštění RDP</value>
  </data>
  <data name="OfficeCentralManagementBehaviourRdpConfig_Detail" xml:space="preserve">
    <value>Nastavení spouštění RDP souborů pro jednotlivé organizační jednotky</value>
  </data>
  <data name="GlobalBehaviourAccessRightsAdministrationConfig_Brief" xml:space="preserve">
    <value>Administrace přístupových práv</value>
  </data>
  <data name="GlobalBehaviourAccessRightsAdministrationConfig_Detail" xml:space="preserve">
    <value>Konfigurace přiřazování přístupových práv (aplikačních rolí) jednotlivým uživatelům.</value>
  </data>
  <data name="CashDeskBehaviourPaymentVoucherConfig_Brief" xml:space="preserve">
    <value>Voucher jako platidlo.</value>
  </data>
  <data name="CashDeskBehaviourPaymentVoucherConfig_Detail" xml:space="preserve">
    <value>Konfigurace voucheru jako platidla.</value>
  </data>
  <data name="WebApiGeneralConfig_Brief" xml:space="preserve">
    <value>WebApi</value>
  </data>
  <data name="WebApiGeneralConfig_Detail" xml:space="preserve">
    <value>WebApi</value>
  </data>
  <data name="HotelGeneralConfig_Brief" xml:space="preserve">
    <value>Hotel</value>
  </data>
  <data name="HotelGeneralConfig_Detail" xml:space="preserve">
    <value>Hotel</value>
  </data>
  <data name="OfficeManagementBehaviourLicenseConfig_Brief" xml:space="preserve">
    <value>Licence</value>
  </data>
  <data name="OfficeManagementBehaviourLicenseConfig_Detail" xml:space="preserve">
    <value>Nastavení licencí k jednotlivým pivotům</value>
  </data>
  <data name="MastersOrderingGeneralConfig_Brief" xml:space="preserve">
    <value>Mistrovské objednávání</value>
  </data>
  <data name="MastersOrderingGeneralConfig_Detail" xml:space="preserve">
    <value>Mistrovské objednávání</value>
  </data>
  <data name="CashDeskBehaviorICouponConfig_Brief" xml:space="preserve">
    <value>ICoupon</value>
  </data>
  <data name="CashDeskBehaviorICouponConfig_Detail" xml:space="preserve">
    <value>Nastavení ICoupon pro jednotlivé zařízení.</value>
  </data>
  <data name="GlobalServicesICouponConfig_Brief" xml:space="preserve">
    <value>ICoupon</value>
  </data>
  <data name="GlobalServicesICouponConfig_Detail" xml:space="preserve">
    <value>Globální nastavení pro ICoupon tedy nastavení přihlašování/uživatelských údajů,... </value>
  </data>
  <data name="CashDeskBehaviourGoodsSearchConfig_Brief" xml:space="preserve">
    <value>Nastavení vyhledávání zboží</value>
  </data>
  <data name="CashDeskBehaviourGoodsSearchConfig_Detail" xml:space="preserve">
    <value>Nastavení vyhledávání zboží</value>
  </data>
  <data name="GlobalHwLabelPrinterConfig_Brief" xml:space="preserve">
    <value>Tiskárna štítků</value>
  </data>
  <data name="GlobalHwLabelPrinterConfig_Detail" xml:space="preserve">
    <value>Nastavení tiskárny štítků</value>
  </data>
  <data name="MobileStockBehaviourConfig_Brief" xml:space="preserve">
    <value>Nastavení aplikace</value>
  </data>
  <data name="MobileStockBehaviourConfig_Detail" xml:space="preserve">
    <value>Nastavení aplikace</value>
  </data>
  <data name="GlobalServicesDimensoConnectionConfig_Brief" xml:space="preserve">
    <value>Dimenso</value>
  </data>
  <data name="GlobalServicesDimensoConnectionConfig_Detail" xml:space="preserve">
    <value>Konfigurace připojení k Dimensu</value>
  </data>
</root>