//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS
{
    
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class EchoFaultDetail
    {
        
        private int primaryReturnCodeField;
        
        private int secondaryReturnCodeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public int primaryReturnCode
        {
            get
            {
                return this.primaryReturnCodeField;
            }
            set
            {
                this.primaryReturnCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public int secondaryReturnCode
        {
            get
            {
                return this.secondaryReturnCodeField;
            }
            set
            {
                this.secondaryReturnCodeField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(CardOnFilePaymentFaultDetail))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(PaymentFaultDetail))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class FaultDetail
    {
        
        private string messageIdField;
        
        private int primaryReturnCodeField;
        
        private int secondaryReturnCodeField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public int primaryReturnCode
        {
            get
            {
                return this.primaryReturnCodeField;
            }
            set
            {
                this.primaryReturnCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public int secondaryReturnCode
        {
            get
            {
                return this.secondaryReturnCodeField;
            }
            set
            {
                this.secondaryReturnCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=3)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class CardOnFilePaymentFaultDetail : FaultDetail
    {
        
        private string authenticationLinkField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string authenticationLink
        {
            get
            {
                return this.authenticationLinkField;
            }
            set
            {
                this.authenticationLinkField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class PaymentFaultDetail : FaultDetail
    {
        
        private string authCodeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string authCode
        {
            get
            {
                return this.authCodeField;
            }
            set
            {
                this.authCodeField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.ServiceModel.ServiceContractAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1", ConfigurationName="Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentPort")]
    public interface PaymentPort
    {
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.EchoFaultDetail), Action="", Name="echoServiceException")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        void echo();
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        System.Threading.Tasks.Task echoAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.FaultDetail), Action="", Name="serviceException")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="paymentStatusResponse")]
        Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentStatusResponse getPaymentStatus(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentStatusRequest paymentStatusRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="paymentStatusResponse")]
        System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentStatusResponse> getPaymentStatusAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentStatusRequest paymentStatusRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.FaultDetail), Action="", Name="serviceException")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="masterPaymentStatusResponse")]
        Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.MasterPaymentStatusResponse getMasterPaymentStatus(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentStatusRequest masterPaymentStatusRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="masterPaymentStatusResponse")]
        System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.MasterPaymentStatusResponse> getMasterPaymentStatusAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentStatusRequest masterPaymentStatusRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.FaultDetail), Action="", Name="serviceException")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="masterPaymentStatusResponse")]
        Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.MasterPaymentStatusResponse processMasterPaymentRevoke(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentStatusRequest masterPaymentStatusRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="masterPaymentStatusResponse")]
        System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.MasterPaymentStatusResponse> processMasterPaymentRevokeAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentStatusRequest masterPaymentStatusRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.FaultDetail), Action="", Name="serviceException")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="revokePaymentLinkResponse")]
        Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.RevokePaymentLinkResponse revokePaymentLink(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentStatusRequest revokePaymentLinkRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="revokePaymentLinkResponse")]
        System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.RevokePaymentLinkResponse> revokePaymentLinkAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentStatusRequest revokePaymentLinkRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.FaultDetail), Action="", Name="serviceException")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="paymentDetailResponse")]
        Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentDetailResponse getPaymentDetail(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentStatusRequest paymentDetailRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="paymentDetailResponse")]
        System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentDetailResponse> getPaymentDetailAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentStatusRequest paymentDetailRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.FaultDetail), Action="", Name="serviceException")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="authorizationReverseResponse")]
        Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.AuthorizationReverseResponse processAuthorizationReverse(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.AuthorizationReverseRequest authorizationReverseRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="authorizationReverseResponse")]
        System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.AuthorizationReverseResponse> processAuthorizationReverseAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.AuthorizationReverseRequest authorizationReverseRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.FaultDetail), Action="", Name="serviceException")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="batchCloseResponse")]
        Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.BatchCloseResponse processBatchClose(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.BatchCloseRequest batchClose);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="batchCloseResponse")]
        System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.BatchCloseResponse> processBatchCloseAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.BatchCloseRequest batchClose);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.FaultDetail), Action="", Name="serviceException")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="refundRequestResponse")]
        Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.RefundResponse processRefund(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.RefundRequest refundRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="refundRequestResponse")]
        System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.RefundResponse> processRefundAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.RefundRequest refundRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.FaultDetail), Action="", Name="serviceException")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="refundReverseResponse")]
        Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.RefundReverseResponse processRefundReverse(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.RefundReverseRequest refundReverseRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="refundReverseResponse")]
        System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.RefundReverseResponse> processRefundReverseAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.RefundReverseRequest refundReverseRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.FaultDetail), Action="", Name="serviceException")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="paymentDeleteResponse")]
        Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentDeleteResponse processPaymentDelete(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentDeleteRequest paymentDeleteRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="paymentDeleteResponse")]
        System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentDeleteResponse> processPaymentDeleteAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentDeleteRequest paymentDeleteRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.FaultDetail), Action="", Name="serviceException")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="captureResponse")]
        Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.CaptureResponse processCapture(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.CaptureRequest captureRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="captureResponse")]
        System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.CaptureResponse> processCaptureAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.CaptureRequest captureRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.FaultDetail), Action="", Name="serviceException")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="captureReverseResponse")]
        Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.CaptureReverseResponse processCaptureReverse(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.CaptureReverseRequest captureReverseRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="captureReverseResponse")]
        System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.CaptureReverseResponse> processCaptureReverseAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.CaptureReverseRequest captureReverseRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.FaultDetail), Action="", Name="serviceException")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="paymentCloseResponse")]
        Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentCloseResponse processPaymentClose(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentCloseRequest paymentCloseRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="paymentCloseResponse")]
        System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentCloseResponse> processPaymentCloseAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentCloseRequest paymentCloseRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentFaultDetail), Action="", Name="paymentServiceException")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="recurringPaymentResponse")]
        Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.RecurringPaymentResponse processRecurringPayment(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.RecurringPaymentRequest recurringPaymentRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="recurringPaymentResponse")]
        System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.RecurringPaymentResponse> processRecurringPaymentAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.RecurringPaymentRequest recurringPaymentRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.FaultDetail), Action="", Name="serviceException")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="paymentLinkResponse")]
        Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentLinkResponse createPaymentLink(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentLinkRequest paymentLinkRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="paymentLinkResponse")]
        System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentLinkResponse> createPaymentLinkAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentLinkRequest paymentLinkRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentFaultDetail), Action="", Name="paymentServiceException")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="usageBasedPaymentResponse")]
        Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.UsageBasedPaymentResponse processUsageBasedPayment(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.CardOnFilePaymentRequest usageBasedPaymentRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="usageBasedPaymentResponse")]
        System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.UsageBasedPaymentResponse> processUsageBasedPaymentAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.CardOnFilePaymentRequest usageBasedPaymentRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentFaultDetail), Action="", Name="paymentServiceException")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="usageBasedSubscriptionPaymentResponse")]
        Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.UsageBasedSubscriptionPaymentResponse processUsageBasedSubscriptionPayment(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.UsageBasedSubscriptionPaymentRequest usageBasedSubscriptionPaymentRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="usageBasedSubscriptionPaymentResponse")]
        System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.UsageBasedSubscriptionPaymentResponse> processUsageBasedSubscriptionPaymentAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.UsageBasedSubscriptionPaymentRequest usageBasedSubscriptionPaymentRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentFaultDetail), Action="", Name="paymentServiceException")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="regularSubscriptionPaymentResponse")]
        Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.RegularSubscriptionPaymentResponse processRegularSubscriptionPayment(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.RegularSubscriptionPaymentRequest regularSubscriptionPaymentRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="regularSubscriptionPaymentResponse")]
        System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.RegularSubscriptionPaymentResponse> processRegularSubscriptionPaymentAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.RegularSubscriptionPaymentRequest regularSubscriptionPaymentRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentFaultDetail), Action="", Name="paymentServiceException")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="prepaidPaymentResponse")]
        Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PrepaidPaymentResponse processPrepaidPayment(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PrepaidPaymentRequest prepaidPaymentRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="prepaidPaymentResponse")]
        System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PrepaidPaymentResponse> processPrepaidPaymentAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PrepaidPaymentRequest prepaidPaymentRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.CardOnFilePaymentFaultDetail), Action="", Name="cardOnFilePaymentServiceException")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="cardOnFilePaymentResponse")]
        Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.CardOnFilePaymentResponse processCardOnFilePayment(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.CardOnFilePaymentRequest cardOnFilePaymentRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="cardOnFilePaymentResponse")]
        System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.CardOnFilePaymentResponse> processCardOnFilePaymentAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.CardOnFilePaymentRequest cardOnFilePaymentRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.FaultDetail), Action="", Name="serviceException")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="mpsPreCheckoutResponse")]
        Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.MpsPreCheckoutResponse mpsPreCheckout(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.MpsPreCheckoutRequest mpsPreCheckoutRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="mpsPreCheckoutResponse")]
        System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.MpsPreCheckoutResponse> mpsPreCheckoutAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.MpsPreCheckoutRequest mpsPreCheckoutRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.FaultDetail), Action="", Name="serviceException")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="mpsExpressCheckoutResponse")]
        Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.MpsExpressCheckoutResponse mpsExpressCheckout(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.MpsExpressCheckoutRequest mpsExpressCheckoutRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="mpsExpressCheckoutResponse")]
        System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.MpsExpressCheckoutResponse> mpsExpressCheckoutAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.MpsExpressCheckoutRequest mpsExpressCheckoutRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.FaultDetail), Action="", Name="serviceException")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="paymentStatusResponse")]
        Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentStatusResponse resolvePaymentStatus(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.ResolvePaymentStatusRequest resolvePaymentStatusRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="paymentStatusResponse")]
        System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentStatusResponse> resolvePaymentStatusAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.ResolvePaymentStatusRequest resolvePaymentStatusRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.FaultDetail), Action="", Name="serviceException")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="tokenStatusResponse")]
        Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.TokenStatusResponse getTokenStatus(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.TokenStatusRequest tokenStatusRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="tokenStatusResponse")]
        System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.TokenStatusResponse> getTokenStatusAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.TokenStatusRequest tokenStatusRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.FaultDetail), Action="", Name="serviceException")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="tokenRevokeResponse")]
        Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.TokenRevokeResponse processTokenRevoke(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.TokenRevokeRequest tokenRevokeRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="tokenRevokeResponse")]
        System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.TokenRevokeResponse> processTokenRevokeAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.TokenRevokeRequest tokenRevokeRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentFaultDetail), Action="", Name="paymentServiceException")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="tokenPaymentResponse")]
        Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.TokenPaymentResponse processTokenPayment(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.TokenPaymentRequest tokenPaymentRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="tokenPaymentResponse")]
        System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.TokenPaymentResponse> processTokenPaymentAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.TokenPaymentRequest tokenPaymentRequest);
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class PaymentStatusRequest
    {
        
        private string messageIdField;
        
        private string providerField;
        
        private string merchantNumberField;
        
        private string paymentNumberField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string provider
        {
            get
            {
                return this.providerField;
            }
            set
            {
                this.providerField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string merchantNumber
        {
            get
            {
                return this.merchantNumberField;
            }
            set
            {
                this.merchantNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string paymentNumber
        {
            get
            {
                return this.paymentNumberField;
            }
            set
            {
                this.paymentNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=4)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class PaymentStatusResponse
    {
        
        private string messageIdField;
        
        private int stateField;
        
        private string statusField;
        
        private string subStatusField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public int state
        {
            get
            {
                return this.stateField;
            }
            set
            {
                this.stateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string status
        {
            get
            {
                return this.statusField;
            }
            set
            {
                this.statusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string subStatus
        {
            get
            {
                return this.subStatusField;
            }
            set
            {
                this.subStatusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=4)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class MasterPaymentStatusResponse
    {
        
        private string messageIdField;
        
        private string statusField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string status
        {
            get
            {
                return this.statusField;
            }
            set
            {
                this.statusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=2)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class RevokePaymentLinkResponse
    {
        
        private string messageIdField;
        
        private string statusField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string status
        {
            get
            {
                return this.statusField;
            }
            set
            {
                this.statusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=2)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class PaymentDetailResponse
    {
        
        private string messageIdField;
        
        private int stateField;
        
        private string statusField;
        
        private string subStatusField;
        
        private string paymentMethodField;
        
        private string panMaskedField;
        
        private string brandNameField;
        
        private long paymentAmountField;
        
        private bool paymentAmountFieldSpecified;
        
        private long approveAmountField;
        
        private bool approveAmountFieldSpecified;
        
        private long captureAmountField;
        
        private bool captureAmountFieldSpecified;
        
        private long refundAmountField;
        
        private bool refundAmountFieldSpecified;
        
        private string approveCodeField;
        
        private string paymentTimeField;
        
        private string approveTimeField;
        
        private string lastCaptureTimeField;
        
        private additionalInfoResponse additionalInfoResponseField;
        
        private SimpleValueHolder[] simpleValueHolderField;
        
        private string panTokenField;
        
        private string panPatternField;
        
        private string panExpiryField;
        
        private string acsResultField;
        
        private string dayToCaptureField;
        
        private string traceIdField;
        
        private string authResponseCodeField;
        
        private string authRRNField;
        
        private string paymentAccountReferenceField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public int state
        {
            get
            {
                return this.stateField;
            }
            set
            {
                this.stateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string status
        {
            get
            {
                return this.statusField;
            }
            set
            {
                this.statusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string subStatus
        {
            get
            {
                return this.subStatusField;
            }
            set
            {
                this.subStatusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string paymentMethod
        {
            get
            {
                return this.paymentMethodField;
            }
            set
            {
                this.paymentMethodField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string panMasked
        {
            get
            {
                return this.panMaskedField;
            }
            set
            {
                this.panMaskedField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public string brandName
        {
            get
            {
                return this.brandNameField;
            }
            set
            {
                this.brandNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=7)]
        public long paymentAmount
        {
            get
            {
                return this.paymentAmountField;
            }
            set
            {
                this.paymentAmountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool paymentAmountSpecified
        {
            get
            {
                return this.paymentAmountFieldSpecified;
            }
            set
            {
                this.paymentAmountFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=8)]
        public long approveAmount
        {
            get
            {
                return this.approveAmountField;
            }
            set
            {
                this.approveAmountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool approveAmountSpecified
        {
            get
            {
                return this.approveAmountFieldSpecified;
            }
            set
            {
                this.approveAmountFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=9)]
        public long captureAmount
        {
            get
            {
                return this.captureAmountField;
            }
            set
            {
                this.captureAmountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool captureAmountSpecified
        {
            get
            {
                return this.captureAmountFieldSpecified;
            }
            set
            {
                this.captureAmountFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=10)]
        public long refundAmount
        {
            get
            {
                return this.refundAmountField;
            }
            set
            {
                this.refundAmountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool refundAmountSpecified
        {
            get
            {
                return this.refundAmountFieldSpecified;
            }
            set
            {
                this.refundAmountFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=11)]
        public string approveCode
        {
            get
            {
                return this.approveCodeField;
            }
            set
            {
                this.approveCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=12)]
        public string paymentTime
        {
            get
            {
                return this.paymentTimeField;
            }
            set
            {
                this.paymentTimeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=13)]
        public string approveTime
        {
            get
            {
                return this.approveTimeField;
            }
            set
            {
                this.approveTimeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=14)]
        public string lastCaptureTime
        {
            get
            {
                return this.lastCaptureTimeField;
            }
            set
            {
                this.lastCaptureTimeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=15)]
        public additionalInfoResponse additionalInfoResponse
        {
            get
            {
                return this.additionalInfoResponseField;
            }
            set
            {
                this.additionalInfoResponseField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("simpleValueHolder", Order=16)]
        public SimpleValueHolder[] simpleValueHolder
        {
            get
            {
                return this.simpleValueHolderField;
            }
            set
            {
                this.simpleValueHolderField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=17)]
        public string panToken
        {
            get
            {
                return this.panTokenField;
            }
            set
            {
                this.panTokenField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=18)]
        public string panPattern
        {
            get
            {
                return this.panPatternField;
            }
            set
            {
                this.panPatternField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=19)]
        public string panExpiry
        {
            get
            {
                return this.panExpiryField;
            }
            set
            {
                this.panExpiryField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=20)]
        public string acsResult
        {
            get
            {
                return this.acsResultField;
            }
            set
            {
                this.acsResultField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=21)]
        public string dayToCapture
        {
            get
            {
                return this.dayToCaptureField;
            }
            set
            {
                this.dayToCaptureField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=22)]
        public string traceId
        {
            get
            {
                return this.traceIdField;
            }
            set
            {
                this.traceIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=23)]
        public string authResponseCode
        {
            get
            {
                return this.authResponseCodeField;
            }
            set
            {
                this.authResponseCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=24)]
        public string authRRN
        {
            get
            {
                return this.authRRNField;
            }
            set
            {
                this.authRRNField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=25)]
        public string paymentAccountReference
        {
            get
            {
                return this.paymentAccountReferenceField;
            }
            set
            {
                this.paymentAccountReferenceField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=26)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/gpwebpay/additionalInfo/response/v1")]
    public partial class additionalInfoResponse
    {
        
        private string walletDetailsField;
        
        private additionalInfoResponseContact contactField;
        
        private additionalInfoResponseBillingDetails billingDetailsField;
        
        private additionalInfoResponseShippingDetails shippingDetailsField;
        
        private additionalInfoResponseCardDetail[] cardsDetailsField;
        
        private additionalInfoResponseLoyaltyProgramDetails loyaltyProgramDetailsField;
        
        private additionalInfoResponseEetRegistrationData eetRegistrationDataField;
        
        private string versionField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string walletDetails
        {
            get
            {
                return this.walletDetailsField;
            }
            set
            {
                this.walletDetailsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public additionalInfoResponseContact contact
        {
            get
            {
                return this.contactField;
            }
            set
            {
                this.contactField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public additionalInfoResponseBillingDetails billingDetails
        {
            get
            {
                return this.billingDetailsField;
            }
            set
            {
                this.billingDetailsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public additionalInfoResponseShippingDetails shippingDetails
        {
            get
            {
                return this.shippingDetailsField;
            }
            set
            {
                this.shippingDetailsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=4)]
        [System.Xml.Serialization.XmlArrayItemAttribute("cardDetail", IsNullable=false)]
        public additionalInfoResponseCardDetail[] cardsDetails
        {
            get
            {
                return this.cardsDetailsField;
            }
            set
            {
                this.cardsDetailsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public additionalInfoResponseLoyaltyProgramDetails loyaltyProgramDetails
        {
            get
            {
                return this.loyaltyProgramDetailsField;
            }
            set
            {
                this.loyaltyProgramDetailsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public additionalInfoResponseEetRegistrationData eetRegistrationData
        {
            get
            {
                return this.eetRegistrationDataField;
            }
            set
            {
                this.eetRegistrationDataField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string version
        {
            get
            {
                return this.versionField;
            }
            set
            {
                this.versionField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://gpe.cz/gpwebpay/additionalInfo/response/v1")]
    public partial class additionalInfoResponseContact
    {
        
        private string firstNameField;
        
        private string lastNameField;
        
        private string countryField;
        
        private string phoneField;
        
        private string emailField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string firstName
        {
            get
            {
                return this.firstNameField;
            }
            set
            {
                this.firstNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string lastName
        {
            get
            {
                return this.lastNameField;
            }
            set
            {
                this.lastNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string country
        {
            get
            {
                return this.countryField;
            }
            set
            {
                this.countryField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string phone
        {
            get
            {
                return this.phoneField;
            }
            set
            {
                this.phoneField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string email
        {
            get
            {
                return this.emailField;
            }
            set
            {
                this.emailField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://gpe.cz/gpwebpay/additionalInfo/response/v1")]
    public partial class additionalInfoResponseBillingDetails
    {
        
        private string nameField;
        
        private string address1Field;
        
        private string address2Field;
        
        private string address3Field;
        
        private string cityField;
        
        private string postalCodeField;
        
        private string countryField;
        
        private string countrySubdivisionField;
        
        private string phoneField;
        
        private string emailField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string name
        {
            get
            {
                return this.nameField;
            }
            set
            {
                this.nameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string address1
        {
            get
            {
                return this.address1Field;
            }
            set
            {
                this.address1Field = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string address2
        {
            get
            {
                return this.address2Field;
            }
            set
            {
                this.address2Field = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string address3
        {
            get
            {
                return this.address3Field;
            }
            set
            {
                this.address3Field = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string city
        {
            get
            {
                return this.cityField;
            }
            set
            {
                this.cityField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string postalCode
        {
            get
            {
                return this.postalCodeField;
            }
            set
            {
                this.postalCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public string country
        {
            get
            {
                return this.countryField;
            }
            set
            {
                this.countryField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=7)]
        public string countrySubdivision
        {
            get
            {
                return this.countrySubdivisionField;
            }
            set
            {
                this.countrySubdivisionField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=8)]
        public string phone
        {
            get
            {
                return this.phoneField;
            }
            set
            {
                this.phoneField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=9)]
        public string email
        {
            get
            {
                return this.emailField;
            }
            set
            {
                this.emailField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://gpe.cz/gpwebpay/additionalInfo/response/v1")]
    public partial class additionalInfoResponseShippingDetails
    {
        
        private string nameField;
        
        private string address1Field;
        
        private string address2Field;
        
        private string address3Field;
        
        private string cityField;
        
        private string postalCodeField;
        
        private string countryField;
        
        private string countrySubdivisionField;
        
        private string phoneField;
        
        private string emailField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string name
        {
            get
            {
                return this.nameField;
            }
            set
            {
                this.nameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string address1
        {
            get
            {
                return this.address1Field;
            }
            set
            {
                this.address1Field = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string address2
        {
            get
            {
                return this.address2Field;
            }
            set
            {
                this.address2Field = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string address3
        {
            get
            {
                return this.address3Field;
            }
            set
            {
                this.address3Field = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string city
        {
            get
            {
                return this.cityField;
            }
            set
            {
                this.cityField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string postalCode
        {
            get
            {
                return this.postalCodeField;
            }
            set
            {
                this.postalCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public string country
        {
            get
            {
                return this.countryField;
            }
            set
            {
                this.countryField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=7)]
        public string countrySubdivision
        {
            get
            {
                return this.countrySubdivisionField;
            }
            set
            {
                this.countrySubdivisionField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=8)]
        public string phone
        {
            get
            {
                return this.phoneField;
            }
            set
            {
                this.phoneField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=9)]
        public string email
        {
            get
            {
                return this.emailField;
            }
            set
            {
                this.emailField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://gpe.cz/gpwebpay/additionalInfo/response/v1")]
    public partial class additionalInfoResponseCardDetail
    {
        
        private string brandIdField;
        
        private string brandNameField;
        
        private string cardHolderNameField;
        
        private byte expiryMonthField;
        
        private bool expiryMonthFieldSpecified;
        
        private ushort expiryYearField;
        
        private bool expiryYearFieldSpecified;
        
        private string cardIdField;
        
        private string lastFourField;
        
        private string cardAliasField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string brandId
        {
            get
            {
                return this.brandIdField;
            }
            set
            {
                this.brandIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string brandName
        {
            get
            {
                return this.brandNameField;
            }
            set
            {
                this.brandNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string cardHolderName
        {
            get
            {
                return this.cardHolderNameField;
            }
            set
            {
                this.cardHolderNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public byte expiryMonth
        {
            get
            {
                return this.expiryMonthField;
            }
            set
            {
                this.expiryMonthField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool expiryMonthSpecified
        {
            get
            {
                return this.expiryMonthFieldSpecified;
            }
            set
            {
                this.expiryMonthFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public ushort expiryYear
        {
            get
            {
                return this.expiryYearField;
            }
            set
            {
                this.expiryYearField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool expiryYearSpecified
        {
            get
            {
                return this.expiryYearFieldSpecified;
            }
            set
            {
                this.expiryYearFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string cardId
        {
            get
            {
                return this.cardIdField;
            }
            set
            {
                this.cardIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public string lastFour
        {
            get
            {
                return this.lastFourField;
            }
            set
            {
                this.lastFourField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=7)]
        public string cardAlias
        {
            get
            {
                return this.cardAliasField;
            }
            set
            {
                this.cardAliasField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://gpe.cz/gpwebpay/additionalInfo/response/v1")]
    public partial class additionalInfoResponseLoyaltyProgramDetails
    {
        
        private string programNumberField;
        
        private string programIdField;
        
        private string programNameField;
        
        private byte programExpiryMonthField;
        
        private bool programExpiryMonthFieldSpecified;
        
        private ushort programExpiryYearField;
        
        private bool programExpiryYearFieldSpecified;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string programNumber
        {
            get
            {
                return this.programNumberField;
            }
            set
            {
                this.programNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string programId
        {
            get
            {
                return this.programIdField;
            }
            set
            {
                this.programIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string programName
        {
            get
            {
                return this.programNameField;
            }
            set
            {
                this.programNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public byte programExpiryMonth
        {
            get
            {
                return this.programExpiryMonthField;
            }
            set
            {
                this.programExpiryMonthField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool programExpiryMonthSpecified
        {
            get
            {
                return this.programExpiryMonthFieldSpecified;
            }
            set
            {
                this.programExpiryMonthFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public ushort programExpiryYear
        {
            get
            {
                return this.programExpiryYearField;
            }
            set
            {
                this.programExpiryYearField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool programExpiryYearSpecified
        {
            get
            {
                return this.programExpiryYearFieldSpecified;
            }
            set
            {
                this.programExpiryYearFieldSpecified = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://gpe.cz/gpwebpay/additionalInfo/response/v1")]
    public partial class additionalInfoResponseEetRegistrationData
    {
        
        private string taxIdField;
        
        private int businessPremisesIdField;
        
        private string cashRegisterIdField;
        
        private string receiptNumberField;
        
        private string saleDateTimeField;
        
        private ulong totalSaleAmountField;
        
        private regimeValue regimeField;
        
        private string fiscalCodeField;
        
        private string securityCodeField;
        
        private string signatureCodeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string taxId
        {
            get
            {
                return this.taxIdField;
            }
            set
            {
                this.taxIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public int businessPremisesId
        {
            get
            {
                return this.businessPremisesIdField;
            }
            set
            {
                this.businessPremisesIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string cashRegisterId
        {
            get
            {
                return this.cashRegisterIdField;
            }
            set
            {
                this.cashRegisterIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string receiptNumber
        {
            get
            {
                return this.receiptNumberField;
            }
            set
            {
                this.receiptNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string saleDateTime
        {
            get
            {
                return this.saleDateTimeField;
            }
            set
            {
                this.saleDateTimeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public ulong totalSaleAmount
        {
            get
            {
                return this.totalSaleAmountField;
            }
            set
            {
                this.totalSaleAmountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public regimeValue regime
        {
            get
            {
                return this.regimeField;
            }
            set
            {
                this.regimeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=7)]
        public string fiscalCode
        {
            get
            {
                return this.fiscalCodeField;
            }
            set
            {
                this.fiscalCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=8)]
        public string securityCode
        {
            get
            {
                return this.securityCodeField;
            }
            set
            {
                this.securityCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=9)]
        public string signatureCode
        {
            get
            {
                return this.signatureCodeField;
            }
            set
            {
                this.signatureCodeField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/gpwebpay/additionalInfo/response/v1")]
    public enum regimeValue
    {
        
        /// <remarks/>
        regular,
        
        /// <remarks/>
        simplified,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class SimpleValueHolder
    {
        
        private string nameField;
        
        private string valueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string name
        {
            get
            {
                return this.nameField;
            }
            set
            {
                this.nameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string value
        {
            get
            {
                return this.valueField;
            }
            set
            {
                this.valueField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class AuthorizationReverseRequest
    {
        
        private string messageIdField;
        
        private string providerField;
        
        private string merchantNumberField;
        
        private string paymentNumberField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string provider
        {
            get
            {
                return this.providerField;
            }
            set
            {
                this.providerField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string merchantNumber
        {
            get
            {
                return this.merchantNumberField;
            }
            set
            {
                this.merchantNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string paymentNumber
        {
            get
            {
                return this.paymentNumberField;
            }
            set
            {
                this.paymentNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=4)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class AuthorizationReverseResponse
    {
        
        private string messageIdField;
        
        private int stateField;
        
        private string statusField;
        
        private string subStatusField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public int state
        {
            get
            {
                return this.stateField;
            }
            set
            {
                this.stateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string status
        {
            get
            {
                return this.statusField;
            }
            set
            {
                this.statusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string subStatus
        {
            get
            {
                return this.subStatusField;
            }
            set
            {
                this.subStatusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=4)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class BatchCloseRequest
    {
        
        private string messageIdField;
        
        private string providerField;
        
        private string merchantNumberField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string provider
        {
            get
            {
                return this.providerField;
            }
            set
            {
                this.providerField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string merchantNumber
        {
            get
            {
                return this.merchantNumberField;
            }
            set
            {
                this.merchantNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=3)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class BatchCloseResponse
    {
        
        private string messageIdField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=1)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class RefundRequest
    {
        
        private string messageIdField;
        
        private string providerField;
        
        private string merchantNumberField;
        
        private string paymentNumberField;
        
        private long amountField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string provider
        {
            get
            {
                return this.providerField;
            }
            set
            {
                this.providerField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string merchantNumber
        {
            get
            {
                return this.merchantNumberField;
            }
            set
            {
                this.merchantNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string paymentNumber
        {
            get
            {
                return this.paymentNumberField;
            }
            set
            {
                this.paymentNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public long amount
        {
            get
            {
                return this.amountField;
            }
            set
            {
                this.amountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=5)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class RefundResponse
    {
        
        private string messageIdField;
        
        private int stateField;
        
        private string statusField;
        
        private string subStatusField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public int state
        {
            get
            {
                return this.stateField;
            }
            set
            {
                this.stateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string status
        {
            get
            {
                return this.statusField;
            }
            set
            {
                this.statusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string subStatus
        {
            get
            {
                return this.subStatusField;
            }
            set
            {
                this.subStatusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=4)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class RefundReverseRequest
    {
        
        private string messageIdField;
        
        private string providerField;
        
        private string merchantNumberField;
        
        private string paymentNumberField;
        
        private int refundNumberField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string provider
        {
            get
            {
                return this.providerField;
            }
            set
            {
                this.providerField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string merchantNumber
        {
            get
            {
                return this.merchantNumberField;
            }
            set
            {
                this.merchantNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string paymentNumber
        {
            get
            {
                return this.paymentNumberField;
            }
            set
            {
                this.paymentNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public int refundNumber
        {
            get
            {
                return this.refundNumberField;
            }
            set
            {
                this.refundNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=5)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class RefundReverseResponse
    {
        
        private string messageIdField;
        
        private int stateField;
        
        private string statusField;
        
        private string subStatusField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public int state
        {
            get
            {
                return this.stateField;
            }
            set
            {
                this.stateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string status
        {
            get
            {
                return this.statusField;
            }
            set
            {
                this.statusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string subStatus
        {
            get
            {
                return this.subStatusField;
            }
            set
            {
                this.subStatusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=4)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class PaymentDeleteRequest
    {
        
        private string messageIdField;
        
        private string providerField;
        
        private string merchantNumberField;
        
        private string paymentNumberField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string provider
        {
            get
            {
                return this.providerField;
            }
            set
            {
                this.providerField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string merchantNumber
        {
            get
            {
                return this.merchantNumberField;
            }
            set
            {
                this.merchantNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string paymentNumber
        {
            get
            {
                return this.paymentNumberField;
            }
            set
            {
                this.paymentNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=4)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class PaymentDeleteResponse
    {
        
        private string messageIdField;
        
        private int stateField;
        
        private string statusField;
        
        private string subStatusField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public int state
        {
            get
            {
                return this.stateField;
            }
            set
            {
                this.stateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string status
        {
            get
            {
                return this.statusField;
            }
            set
            {
                this.statusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string subStatus
        {
            get
            {
                return this.subStatusField;
            }
            set
            {
                this.subStatusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=4)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class CaptureRequest
    {
        
        private string messageIdField;
        
        private string providerField;
        
        private string merchantNumberField;
        
        private string paymentNumberField;
        
        private long amountField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string provider
        {
            get
            {
                return this.providerField;
            }
            set
            {
                this.providerField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string merchantNumber
        {
            get
            {
                return this.merchantNumberField;
            }
            set
            {
                this.merchantNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string paymentNumber
        {
            get
            {
                return this.paymentNumberField;
            }
            set
            {
                this.paymentNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public long amount
        {
            get
            {
                return this.amountField;
            }
            set
            {
                this.amountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=5)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class CaptureResponse
    {
        
        private string messageIdField;
        
        private int stateField;
        
        private string statusField;
        
        private string subStatusField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public int state
        {
            get
            {
                return this.stateField;
            }
            set
            {
                this.stateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string status
        {
            get
            {
                return this.statusField;
            }
            set
            {
                this.statusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string subStatus
        {
            get
            {
                return this.subStatusField;
            }
            set
            {
                this.subStatusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=4)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class CaptureReverseRequest
    {
        
        private string messageIdField;
        
        private string providerField;
        
        private string merchantNumberField;
        
        private string paymentNumberField;
        
        private int captureNumberField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string provider
        {
            get
            {
                return this.providerField;
            }
            set
            {
                this.providerField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string merchantNumber
        {
            get
            {
                return this.merchantNumberField;
            }
            set
            {
                this.merchantNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string paymentNumber
        {
            get
            {
                return this.paymentNumberField;
            }
            set
            {
                this.paymentNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public int captureNumber
        {
            get
            {
                return this.captureNumberField;
            }
            set
            {
                this.captureNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=5)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class CaptureReverseResponse
    {
        
        private string messageIdField;
        
        private int stateField;
        
        private string statusField;
        
        private string subStatusField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public int state
        {
            get
            {
                return this.stateField;
            }
            set
            {
                this.stateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string status
        {
            get
            {
                return this.statusField;
            }
            set
            {
                this.statusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string subStatus
        {
            get
            {
                return this.subStatusField;
            }
            set
            {
                this.subStatusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=4)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class PaymentCloseRequest
    {
        
        private string messageIdField;
        
        private string providerField;
        
        private string merchantNumberField;
        
        private string paymentNumberField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string provider
        {
            get
            {
                return this.providerField;
            }
            set
            {
                this.providerField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string merchantNumber
        {
            get
            {
                return this.merchantNumberField;
            }
            set
            {
                this.merchantNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string paymentNumber
        {
            get
            {
                return this.paymentNumberField;
            }
            set
            {
                this.paymentNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=4)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class PaymentCloseResponse
    {
        
        private string messageIdField;
        
        private int stateField;
        
        private string statusField;
        
        private string subStatusField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public int state
        {
            get
            {
                return this.stateField;
            }
            set
            {
                this.stateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string status
        {
            get
            {
                return this.statusField;
            }
            set
            {
                this.statusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string subStatus
        {
            get
            {
                return this.subStatusField;
            }
            set
            {
                this.subStatusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=4)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class RecurringPaymentRequest
    {
        
        private string messageIdField;
        
        private string providerField;
        
        private string merchantNumberField;
        
        private string paymentNumberField;
        
        private string masterPaymentNumberField;
        
        private string orderNumberField;
        
        private string referenceNumberField;
        
        private long amountField;
        
        private bool amountFieldSpecified;
        
        private string currencyCodeField;
        
        private int captureFlagField;
        
        private CardHolderData cardHolderDataField;
        
        private AltTerminalData altTerminalDataField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string provider
        {
            get
            {
                return this.providerField;
            }
            set
            {
                this.providerField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string merchantNumber
        {
            get
            {
                return this.merchantNumberField;
            }
            set
            {
                this.merchantNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string paymentNumber
        {
            get
            {
                return this.paymentNumberField;
            }
            set
            {
                this.paymentNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string masterPaymentNumber
        {
            get
            {
                return this.masterPaymentNumberField;
            }
            set
            {
                this.masterPaymentNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string orderNumber
        {
            get
            {
                return this.orderNumberField;
            }
            set
            {
                this.orderNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public string referenceNumber
        {
            get
            {
                return this.referenceNumberField;
            }
            set
            {
                this.referenceNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=7)]
        public long amount
        {
            get
            {
                return this.amountField;
            }
            set
            {
                this.amountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool amountSpecified
        {
            get
            {
                return this.amountFieldSpecified;
            }
            set
            {
                this.amountFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=8)]
        public string currencyCode
        {
            get
            {
                return this.currencyCodeField;
            }
            set
            {
                this.currencyCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=9)]
        public int captureFlag
        {
            get
            {
                return this.captureFlagField;
            }
            set
            {
                this.captureFlagField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=10)]
        public CardHolderData cardHolderData
        {
            get
            {
                return this.cardHolderDataField;
            }
            set
            {
                this.cardHolderDataField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=11)]
        public AltTerminalData altTerminalData
        {
            get
            {
                return this.altTerminalDataField;
            }
            set
            {
                this.altTerminalDataField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=12)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class CardHolderData
    {
        
        private CardHolderDataCardholderDetails cardholderDetailsField;
        
        private AddressMatchValue addressMatchField;
        
        private bool addressMatchFieldSpecified;
        
        private CardHolderDataBillingDetails billingDetailsField;
        
        private CardHolderDataShippingDetails shippingDetailsField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public CardHolderDataCardholderDetails cardholderDetails
        {
            get
            {
                return this.cardholderDetailsField;
            }
            set
            {
                this.cardholderDetailsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public AddressMatchValue addressMatch
        {
            get
            {
                return this.addressMatchField;
            }
            set
            {
                this.addressMatchField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool addressMatchSpecified
        {
            get
            {
                return this.addressMatchFieldSpecified;
            }
            set
            {
                this.addressMatchFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public CardHolderDataBillingDetails billingDetails
        {
            get
            {
                return this.billingDetailsField;
            }
            set
            {
                this.billingDetailsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public CardHolderDataShippingDetails shippingDetails
        {
            get
            {
                return this.shippingDetailsField;
            }
            set
            {
                this.shippingDetailsField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class CardHolderDataCardholderDetails
    {
        
        private string nameField;
        
        private string loginIdField;
        
        private LoginTypeValue loginTypeField;
        
        private bool loginTypeFieldSpecified;
        
        private string loginTimeField;
        
        private string userAccountIdField;
        
        private string userAccountCreatedDateField;
        
        private UserAccountAgeValue userAccountAgeField;
        
        private bool userAccountAgeFieldSpecified;
        
        private string userAccountLastChangeDateField;
        
        private UserAccountLastChangeAgeValue userAccountLastChangeAgeField;
        
        private bool userAccountLastChangeAgeFieldSpecified;
        
        private string userAccountPasswordChangeDateField;
        
        private UserAccountPasswordChangeAgeValue userAccountPasswordChangeAgeField;
        
        private bool userAccountPasswordChangeAgeFieldSpecified;
        
        private string socialNetworkIdField;
        
        private string emailField;
        
        private string phoneCountryField;
        
        private string phoneField;
        
        private string mobilePhoneCountryField;
        
        private string mobilePhoneField;
        
        private string workPhoneCountryField;
        
        private string workPhoneField;
        
        private string clientIpAddressField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string name
        {
            get
            {
                return this.nameField;
            }
            set
            {
                this.nameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string loginId
        {
            get
            {
                return this.loginIdField;
            }
            set
            {
                this.loginIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public LoginTypeValue loginType
        {
            get
            {
                return this.loginTypeField;
            }
            set
            {
                this.loginTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool loginTypeSpecified
        {
            get
            {
                return this.loginTypeFieldSpecified;
            }
            set
            {
                this.loginTypeFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string loginTime
        {
            get
            {
                return this.loginTimeField;
            }
            set
            {
                this.loginTimeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string userAccountId
        {
            get
            {
                return this.userAccountIdField;
            }
            set
            {
                this.userAccountIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string userAccountCreatedDate
        {
            get
            {
                return this.userAccountCreatedDateField;
            }
            set
            {
                this.userAccountCreatedDateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public UserAccountAgeValue userAccountAge
        {
            get
            {
                return this.userAccountAgeField;
            }
            set
            {
                this.userAccountAgeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool userAccountAgeSpecified
        {
            get
            {
                return this.userAccountAgeFieldSpecified;
            }
            set
            {
                this.userAccountAgeFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=7)]
        public string userAccountLastChangeDate
        {
            get
            {
                return this.userAccountLastChangeDateField;
            }
            set
            {
                this.userAccountLastChangeDateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=8)]
        public UserAccountLastChangeAgeValue userAccountLastChangeAge
        {
            get
            {
                return this.userAccountLastChangeAgeField;
            }
            set
            {
                this.userAccountLastChangeAgeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool userAccountLastChangeAgeSpecified
        {
            get
            {
                return this.userAccountLastChangeAgeFieldSpecified;
            }
            set
            {
                this.userAccountLastChangeAgeFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=9)]
        public string userAccountPasswordChangeDate
        {
            get
            {
                return this.userAccountPasswordChangeDateField;
            }
            set
            {
                this.userAccountPasswordChangeDateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=10)]
        public UserAccountPasswordChangeAgeValue userAccountPasswordChangeAge
        {
            get
            {
                return this.userAccountPasswordChangeAgeField;
            }
            set
            {
                this.userAccountPasswordChangeAgeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool userAccountPasswordChangeAgeSpecified
        {
            get
            {
                return this.userAccountPasswordChangeAgeFieldSpecified;
            }
            set
            {
                this.userAccountPasswordChangeAgeFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=11)]
        public string socialNetworkId
        {
            get
            {
                return this.socialNetworkIdField;
            }
            set
            {
                this.socialNetworkIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=12)]
        public string email
        {
            get
            {
                return this.emailField;
            }
            set
            {
                this.emailField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=13)]
        public string phoneCountry
        {
            get
            {
                return this.phoneCountryField;
            }
            set
            {
                this.phoneCountryField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=14)]
        public string phone
        {
            get
            {
                return this.phoneField;
            }
            set
            {
                this.phoneField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=15)]
        public string mobilePhoneCountry
        {
            get
            {
                return this.mobilePhoneCountryField;
            }
            set
            {
                this.mobilePhoneCountryField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=16)]
        public string mobilePhone
        {
            get
            {
                return this.mobilePhoneField;
            }
            set
            {
                this.mobilePhoneField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=17)]
        public string workPhoneCountry
        {
            get
            {
                return this.workPhoneCountryField;
            }
            set
            {
                this.workPhoneCountryField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=18)]
        public string workPhone
        {
            get
            {
                return this.workPhoneField;
            }
            set
            {
                this.workPhoneField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=19)]
        public string clientIpAddress
        {
            get
            {
                return this.clientIpAddressField;
            }
            set
            {
                this.clientIpAddressField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public enum LoginTypeValue
    {
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("01")]
        Item01,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("02")]
        Item02,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("03")]
        Item03,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("04")]
        Item04,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("05")]
        Item05,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("06")]
        Item06,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public enum UserAccountAgeValue
    {
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("01")]
        Item01,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("02")]
        Item02,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("03")]
        Item03,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("04")]
        Item04,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("05")]
        Item05,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public enum UserAccountLastChangeAgeValue
    {
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("01")]
        Item01,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("02")]
        Item02,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("03")]
        Item03,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("04")]
        Item04,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public enum UserAccountPasswordChangeAgeValue
    {
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("01")]
        Item01,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("02")]
        Item02,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("03")]
        Item03,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("04")]
        Item04,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("05")]
        Item05,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public enum AddressMatchValue
    {
        
        /// <remarks/>
        Y,
        
        /// <remarks/>
        N,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class CardHolderDataBillingDetails
    {
        
        private string nameField;
        
        private string address1Field;
        
        private string address2Field;
        
        private string address3Field;
        
        private string cityField;
        
        private string postalCodeField;
        
        private string countryField;
        
        private string countrySubdivisionField;
        
        private string phoneField;
        
        private string emailField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string name
        {
            get
            {
                return this.nameField;
            }
            set
            {
                this.nameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string address1
        {
            get
            {
                return this.address1Field;
            }
            set
            {
                this.address1Field = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string address2
        {
            get
            {
                return this.address2Field;
            }
            set
            {
                this.address2Field = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string address3
        {
            get
            {
                return this.address3Field;
            }
            set
            {
                this.address3Field = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string city
        {
            get
            {
                return this.cityField;
            }
            set
            {
                this.cityField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string postalCode
        {
            get
            {
                return this.postalCodeField;
            }
            set
            {
                this.postalCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public string country
        {
            get
            {
                return this.countryField;
            }
            set
            {
                this.countryField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=7)]
        public string countrySubdivision
        {
            get
            {
                return this.countrySubdivisionField;
            }
            set
            {
                this.countrySubdivisionField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=8)]
        public string phone
        {
            get
            {
                return this.phoneField;
            }
            set
            {
                this.phoneField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=9)]
        public string email
        {
            get
            {
                return this.emailField;
            }
            set
            {
                this.emailField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class CardHolderDataShippingDetails
    {
        
        private string nameField;
        
        private string address1Field;
        
        private string address2Field;
        
        private string address3Field;
        
        private string cityField;
        
        private string postalCodeField;
        
        private string countryField;
        
        private string countrySubdivisionField;
        
        private string phoneField;
        
        private string emailField;
        
        private string methodField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string name
        {
            get
            {
                return this.nameField;
            }
            set
            {
                this.nameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string address1
        {
            get
            {
                return this.address1Field;
            }
            set
            {
                this.address1Field = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string address2
        {
            get
            {
                return this.address2Field;
            }
            set
            {
                this.address2Field = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string address3
        {
            get
            {
                return this.address3Field;
            }
            set
            {
                this.address3Field = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string city
        {
            get
            {
                return this.cityField;
            }
            set
            {
                this.cityField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string postalCode
        {
            get
            {
                return this.postalCodeField;
            }
            set
            {
                this.postalCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public string country
        {
            get
            {
                return this.countryField;
            }
            set
            {
                this.countryField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=7)]
        public string countrySubdivision
        {
            get
            {
                return this.countrySubdivisionField;
            }
            set
            {
                this.countrySubdivisionField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=8)]
        public string phone
        {
            get
            {
                return this.phoneField;
            }
            set
            {
                this.phoneField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=9)]
        public string email
        {
            get
            {
                return this.emailField;
            }
            set
            {
                this.emailField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=10)]
        public string method
        {
            get
            {
                return this.methodField;
            }
            set
            {
                this.methodField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class AltTerminalData
    {
        
        private string terminalIdField;
        
        private string terminalOwnerField;
        
        private string terminalCityField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string terminalId
        {
            get
            {
                return this.terminalIdField;
            }
            set
            {
                this.terminalIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string terminalOwner
        {
            get
            {
                return this.terminalOwnerField;
            }
            set
            {
                this.terminalOwnerField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string terminalCity
        {
            get
            {
                return this.terminalCityField;
            }
            set
            {
                this.terminalCityField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class RecurringPaymentResponse
    {
        
        private string messageIdField;
        
        private string authCodeField;
        
        private string traceIdField;
        
        private string authResponseCodeField;
        
        private string authRRNField;
        
        private string paymentAccountReferenceField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string authCode
        {
            get
            {
                return this.authCodeField;
            }
            set
            {
                this.authCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string traceId
        {
            get
            {
                return this.traceIdField;
            }
            set
            {
                this.traceIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string authResponseCode
        {
            get
            {
                return this.authResponseCodeField;
            }
            set
            {
                this.authResponseCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string authRRN
        {
            get
            {
                return this.authRRNField;
            }
            set
            {
                this.authRRNField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string paymentAccountReference
        {
            get
            {
                return this.paymentAccountReferenceField;
            }
            set
            {
                this.paymentAccountReferenceField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=6)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class PaymentLinkRequest
    {
        
        private string messageIdField;
        
        private string providerField;
        
        private string merchantNumberField;
        
        private string paymentNumberField;
        
        private long amountField;
        
        private string currencyCodeField;
        
        private int captureFlagField;
        
        private string orderNumberField;
        
        private string referenceNumberField;
        
        private string urlField;
        
        private string descriptionField;
        
        private string merchantDataField;
        
        private string fastPayIdField;
        
        private string defaultPayMethodField;
        
        private string disabledPayMethodsField;
        
        private string emailField;
        
        private string merchantEmailField;
        
        private System.DateTime paymentExpiryField;
        
        private string languageField;
        
        private BooleanType registerRecurringField;
        
        private bool registerRecurringFieldSpecified;
        
        private BooleanType registerTokenField;
        
        private bool registerTokenFieldSpecified;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string provider
        {
            get
            {
                return this.providerField;
            }
            set
            {
                this.providerField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string merchantNumber
        {
            get
            {
                return this.merchantNumberField;
            }
            set
            {
                this.merchantNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string paymentNumber
        {
            get
            {
                return this.paymentNumberField;
            }
            set
            {
                this.paymentNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public long amount
        {
            get
            {
                return this.amountField;
            }
            set
            {
                this.amountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string currencyCode
        {
            get
            {
                return this.currencyCodeField;
            }
            set
            {
                this.currencyCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public int captureFlag
        {
            get
            {
                return this.captureFlagField;
            }
            set
            {
                this.captureFlagField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=7)]
        public string orderNumber
        {
            get
            {
                return this.orderNumberField;
            }
            set
            {
                this.orderNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=8)]
        public string referenceNumber
        {
            get
            {
                return this.referenceNumberField;
            }
            set
            {
                this.referenceNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=9)]
        public string url
        {
            get
            {
                return this.urlField;
            }
            set
            {
                this.urlField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=10)]
        public string description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=11)]
        public string merchantData
        {
            get
            {
                return this.merchantDataField;
            }
            set
            {
                this.merchantDataField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=12)]
        public string fastPayId
        {
            get
            {
                return this.fastPayIdField;
            }
            set
            {
                this.fastPayIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=13)]
        public string defaultPayMethod
        {
            get
            {
                return this.defaultPayMethodField;
            }
            set
            {
                this.defaultPayMethodField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=14)]
        public string disabledPayMethods
        {
            get
            {
                return this.disabledPayMethodsField;
            }
            set
            {
                this.disabledPayMethodsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=15)]
        public string email
        {
            get
            {
                return this.emailField;
            }
            set
            {
                this.emailField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=16)]
        public string merchantEmail
        {
            get
            {
                return this.merchantEmailField;
            }
            set
            {
                this.merchantEmailField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="date", Order=17)]
        public System.DateTime paymentExpiry
        {
            get
            {
                return this.paymentExpiryField;
            }
            set
            {
                this.paymentExpiryField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=18)]
        public string language
        {
            get
            {
                return this.languageField;
            }
            set
            {
                this.languageField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=19)]
        public BooleanType registerRecurring
        {
            get
            {
                return this.registerRecurringField;
            }
            set
            {
                this.registerRecurringField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool registerRecurringSpecified
        {
            get
            {
                return this.registerRecurringFieldSpecified;
            }
            set
            {
                this.registerRecurringFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=20)]
        public BooleanType registerToken
        {
            get
            {
                return this.registerTokenField;
            }
            set
            {
                this.registerTokenField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool registerTokenSpecified
        {
            get
            {
                return this.registerTokenFieldSpecified;
            }
            set
            {
                this.registerTokenFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=21)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public enum BooleanType
    {
        
        /// <remarks/>
        @true,
        
        /// <remarks/>
        @false,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class PaymentLinkResponse
    {
        
        private string messageIdField;
        
        private string paymentNumberField;
        
        private string paymentLinkField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string paymentNumber
        {
            get
            {
                return this.paymentNumberField;
            }
            set
            {
                this.paymentNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string paymentLink
        {
            get
            {
                return this.paymentLinkField;
            }
            set
            {
                this.paymentLinkField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=3)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class CardOnFilePaymentRequest
    {
        
        private string messageIdField;
        
        private string providerField;
        
        private string merchantNumberField;
        
        private string paymentNumberField;
        
        private string orderNumberField;
        
        private string referenceNumberField;
        
        private long amountField;
        
        private string currencyCodeField;
        
        private int captureFlagField;
        
        private SubMerchantData subMerchantDataField;
        
        private string tokenDataField;
        
        private CardHolderData cardHolderDataField;
        
        private PaymentInfo paymentInfoField;
        
        private ShoppingCartInfo shoppingCartInfoField;
        
        private AltTerminalData altTerminalDataField;
        
        private string returnUrlField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string provider
        {
            get
            {
                return this.providerField;
            }
            set
            {
                this.providerField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string merchantNumber
        {
            get
            {
                return this.merchantNumberField;
            }
            set
            {
                this.merchantNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string paymentNumber
        {
            get
            {
                return this.paymentNumberField;
            }
            set
            {
                this.paymentNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string orderNumber
        {
            get
            {
                return this.orderNumberField;
            }
            set
            {
                this.orderNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string referenceNumber
        {
            get
            {
                return this.referenceNumberField;
            }
            set
            {
                this.referenceNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public long amount
        {
            get
            {
                return this.amountField;
            }
            set
            {
                this.amountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=7)]
        public string currencyCode
        {
            get
            {
                return this.currencyCodeField;
            }
            set
            {
                this.currencyCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=8)]
        public int captureFlag
        {
            get
            {
                return this.captureFlagField;
            }
            set
            {
                this.captureFlagField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=9)]
        public SubMerchantData subMerchantData
        {
            get
            {
                return this.subMerchantDataField;
            }
            set
            {
                this.subMerchantDataField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=10)]
        public string tokenData
        {
            get
            {
                return this.tokenDataField;
            }
            set
            {
                this.tokenDataField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=11)]
        public CardHolderData cardHolderData
        {
            get
            {
                return this.cardHolderDataField;
            }
            set
            {
                this.cardHolderDataField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=12)]
        public PaymentInfo paymentInfo
        {
            get
            {
                return this.paymentInfoField;
            }
            set
            {
                this.paymentInfoField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=13)]
        public ShoppingCartInfo shoppingCartInfo
        {
            get
            {
                return this.shoppingCartInfoField;
            }
            set
            {
                this.shoppingCartInfoField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=14)]
        public AltTerminalData altTerminalData
        {
            get
            {
                return this.altTerminalDataField;
            }
            set
            {
                this.altTerminalDataField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="anyURI", Order=15)]
        public string returnUrl
        {
            get
            {
                return this.returnUrlField;
            }
            set
            {
                this.returnUrlField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=16)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class SubMerchantData
    {
        
        private string merchantIdField;
        
        private string merchantTypeField;
        
        private string merchantNameField;
        
        private string merchantStreetField;
        
        private string merchantCityField;
        
        private string merchantPostalCodeField;
        
        private string merchantStateField;
        
        private string merchantCountryField;
        
        private string merchantWebField;
        
        private string merchantServiceNumberField;
        
        private string merchantMcAssignedIdField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string merchantId
        {
            get
            {
                return this.merchantIdField;
            }
            set
            {
                this.merchantIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string merchantType
        {
            get
            {
                return this.merchantTypeField;
            }
            set
            {
                this.merchantTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string merchantName
        {
            get
            {
                return this.merchantNameField;
            }
            set
            {
                this.merchantNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string merchantStreet
        {
            get
            {
                return this.merchantStreetField;
            }
            set
            {
                this.merchantStreetField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string merchantCity
        {
            get
            {
                return this.merchantCityField;
            }
            set
            {
                this.merchantCityField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string merchantPostalCode
        {
            get
            {
                return this.merchantPostalCodeField;
            }
            set
            {
                this.merchantPostalCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public string merchantState
        {
            get
            {
                return this.merchantStateField;
            }
            set
            {
                this.merchantStateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=7)]
        public string merchantCountry
        {
            get
            {
                return this.merchantCountryField;
            }
            set
            {
                this.merchantCountryField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=8)]
        public string merchantWeb
        {
            get
            {
                return this.merchantWebField;
            }
            set
            {
                this.merchantWebField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=9)]
        public string merchantServiceNumber
        {
            get
            {
                return this.merchantServiceNumberField;
            }
            set
            {
                this.merchantServiceNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=10)]
        public string merchantMcAssignedId
        {
            get
            {
                return this.merchantMcAssignedIdField;
            }
            set
            {
                this.merchantMcAssignedIdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class PaymentInfo
    {
        
        private TransactionTypeValue transactionTypeField;
        
        private bool transactionTypeFieldSpecified;
        
        private ShippingIndicatorValue shippingIndicatorField;
        
        private bool shippingIndicatorFieldSpecified;
        
        private PreOrderPurchaseIndValue preOrderPurchaseIndField;
        
        private bool preOrderPurchaseIndFieldSpecified;
        
        private string preOrderDateField;
        
        private ReorderItemsIndValue reorderItemsIndField;
        
        private bool reorderItemsIndFieldSpecified;
        
        private DeliveryTimeframeValue deliveryTimeframeField;
        
        private bool deliveryTimeframeFieldSpecified;
        
        private string deliveryEmailAddressField;
        
        private string giftCardCountField;
        
        private string giftCardAmountField;
        
        private string giftCardCurrencyField;
        
        private string recurringExpiryField;
        
        private string recurringFrequencyField;
        
        private string remmitanceInfo1Field;
        
        private string remmitanceInfo2Field;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public TransactionTypeValue transactionType
        {
            get
            {
                return this.transactionTypeField;
            }
            set
            {
                this.transactionTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool transactionTypeSpecified
        {
            get
            {
                return this.transactionTypeFieldSpecified;
            }
            set
            {
                this.transactionTypeFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public ShippingIndicatorValue shippingIndicator
        {
            get
            {
                return this.shippingIndicatorField;
            }
            set
            {
                this.shippingIndicatorField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool shippingIndicatorSpecified
        {
            get
            {
                return this.shippingIndicatorFieldSpecified;
            }
            set
            {
                this.shippingIndicatorFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public PreOrderPurchaseIndValue preOrderPurchaseInd
        {
            get
            {
                return this.preOrderPurchaseIndField;
            }
            set
            {
                this.preOrderPurchaseIndField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool preOrderPurchaseIndSpecified
        {
            get
            {
                return this.preOrderPurchaseIndFieldSpecified;
            }
            set
            {
                this.preOrderPurchaseIndFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string preOrderDate
        {
            get
            {
                return this.preOrderDateField;
            }
            set
            {
                this.preOrderDateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public ReorderItemsIndValue reorderItemsInd
        {
            get
            {
                return this.reorderItemsIndField;
            }
            set
            {
                this.reorderItemsIndField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool reorderItemsIndSpecified
        {
            get
            {
                return this.reorderItemsIndFieldSpecified;
            }
            set
            {
                this.reorderItemsIndFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public DeliveryTimeframeValue deliveryTimeframe
        {
            get
            {
                return this.deliveryTimeframeField;
            }
            set
            {
                this.deliveryTimeframeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool deliveryTimeframeSpecified
        {
            get
            {
                return this.deliveryTimeframeFieldSpecified;
            }
            set
            {
                this.deliveryTimeframeFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public string deliveryEmailAddress
        {
            get
            {
                return this.deliveryEmailAddressField;
            }
            set
            {
                this.deliveryEmailAddressField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=7)]
        public string giftCardCount
        {
            get
            {
                return this.giftCardCountField;
            }
            set
            {
                this.giftCardCountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=8)]
        public string giftCardAmount
        {
            get
            {
                return this.giftCardAmountField;
            }
            set
            {
                this.giftCardAmountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=9)]
        public string giftCardCurrency
        {
            get
            {
                return this.giftCardCurrencyField;
            }
            set
            {
                this.giftCardCurrencyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=10)]
        public string recurringExpiry
        {
            get
            {
                return this.recurringExpiryField;
            }
            set
            {
                this.recurringExpiryField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=11)]
        public string recurringFrequency
        {
            get
            {
                return this.recurringFrequencyField;
            }
            set
            {
                this.recurringFrequencyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=12)]
        public string remmitanceInfo1
        {
            get
            {
                return this.remmitanceInfo1Field;
            }
            set
            {
                this.remmitanceInfo1Field = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=13)]
        public string remmitanceInfo2
        {
            get
            {
                return this.remmitanceInfo2Field;
            }
            set
            {
                this.remmitanceInfo2Field = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public enum TransactionTypeValue
    {
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("01")]
        Item01,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("03")]
        Item03,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("10")]
        Item10,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("11")]
        Item11,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("28")]
        Item28,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public enum ShippingIndicatorValue
    {
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("01")]
        Item01,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("02")]
        Item02,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("03")]
        Item03,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("04")]
        Item04,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("05")]
        Item05,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("06")]
        Item06,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("07")]
        Item07,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public enum PreOrderPurchaseIndValue
    {
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("01")]
        Item01,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("02")]
        Item02,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public enum ReorderItemsIndValue
    {
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("01")]
        Item01,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("02")]
        Item02,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public enum DeliveryTimeframeValue
    {
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("01")]
        Item01,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("02")]
        Item02,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("03")]
        Item03,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("04")]
        Item04,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class ShoppingCartInfo
    {
        
        private long taxAmountField;
        
        private bool taxAmountFieldSpecified;
        
        private long shippingAmountField;
        
        private bool shippingAmountFieldSpecified;
        
        private long handlingAmountField;
        
        private bool handlingAmountFieldSpecified;
        
        private long cartAmountField;
        
        private bool cartAmountFieldSpecified;
        
        private ShoppingCartItem[] shoppingCartItemsField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public long taxAmount
        {
            get
            {
                return this.taxAmountField;
            }
            set
            {
                this.taxAmountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool taxAmountSpecified
        {
            get
            {
                return this.taxAmountFieldSpecified;
            }
            set
            {
                this.taxAmountFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public long shippingAmount
        {
            get
            {
                return this.shippingAmountField;
            }
            set
            {
                this.shippingAmountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool shippingAmountSpecified
        {
            get
            {
                return this.shippingAmountFieldSpecified;
            }
            set
            {
                this.shippingAmountFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public long handlingAmount
        {
            get
            {
                return this.handlingAmountField;
            }
            set
            {
                this.handlingAmountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool handlingAmountSpecified
        {
            get
            {
                return this.handlingAmountFieldSpecified;
            }
            set
            {
                this.handlingAmountFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public long cartAmount
        {
            get
            {
                return this.cartAmountField;
            }
            set
            {
                this.cartAmountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool cartAmountSpecified
        {
            get
            {
                return this.cartAmountFieldSpecified;
            }
            set
            {
                this.cartAmountFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=4)]
        [System.Xml.Serialization.XmlArrayItemAttribute("shoppingCartItem", IsNullable=false)]
        public ShoppingCartItem[] shoppingCartItems
        {
            get
            {
                return this.shoppingCartItemsField;
            }
            set
            {
                this.shoppingCartItemsField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class ShoppingCartItem
    {
        
        private string itemCodeField;
        
        private string itemDescriptionField;
        
        private long itemQuantityField;
        
        private long itemUnitPriceField;
        
        private string itemClassField;
        
        private string itemTypeField;
        
        private string itemImageUrlField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string itemCode
        {
            get
            {
                return this.itemCodeField;
            }
            set
            {
                this.itemCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string itemDescription
        {
            get
            {
                return this.itemDescriptionField;
            }
            set
            {
                this.itemDescriptionField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public long itemQuantity
        {
            get
            {
                return this.itemQuantityField;
            }
            set
            {
                this.itemQuantityField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public long itemUnitPrice
        {
            get
            {
                return this.itemUnitPriceField;
            }
            set
            {
                this.itemUnitPriceField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string itemClass
        {
            get
            {
                return this.itemClassField;
            }
            set
            {
                this.itemClassField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string itemType
        {
            get
            {
                return this.itemTypeField;
            }
            set
            {
                this.itemTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="anyURI", Order=6)]
        public string itemImageUrl
        {
            get
            {
                return this.itemImageUrlField;
            }
            set
            {
                this.itemImageUrlField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class UsageBasedPaymentResponse
    {
        
        private string messageIdField;
        
        private string authCodeField;
        
        private string tokenDataField;
        
        private string traceIdField;
        
        private string authResponseCodeField;
        
        private string authRRNField;
        
        private string paymentAccountReferenceField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string authCode
        {
            get
            {
                return this.authCodeField;
            }
            set
            {
                this.authCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string tokenData
        {
            get
            {
                return this.tokenDataField;
            }
            set
            {
                this.tokenDataField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string traceId
        {
            get
            {
                return this.traceIdField;
            }
            set
            {
                this.traceIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string authResponseCode
        {
            get
            {
                return this.authResponseCodeField;
            }
            set
            {
                this.authResponseCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string authRRN
        {
            get
            {
                return this.authRRNField;
            }
            set
            {
                this.authRRNField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public string paymentAccountReference
        {
            get
            {
                return this.paymentAccountReferenceField;
            }
            set
            {
                this.paymentAccountReferenceField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=7)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class UsageBasedSubscriptionPaymentRequest
    {
        
        private string messageIdField;
        
        private string providerField;
        
        private string merchantNumberField;
        
        private string paymentNumberField;
        
        private string masterPaymentNumberField;
        
        private string orderNumberField;
        
        private string referenceNumberField;
        
        private long amountField;
        
        private bool amountFieldSpecified;
        
        private string currencyCodeField;
        
        private int captureFlagField;
        
        private SubMerchantData subMerchantDataField;
        
        private CardHolderData cardHolderDataField;
        
        private PaymentInfo paymentInfoField;
        
        private ShoppingCartInfo shoppingCartInfoField;
        
        private AltTerminalData altTerminalDataField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string provider
        {
            get
            {
                return this.providerField;
            }
            set
            {
                this.providerField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string merchantNumber
        {
            get
            {
                return this.merchantNumberField;
            }
            set
            {
                this.merchantNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string paymentNumber
        {
            get
            {
                return this.paymentNumberField;
            }
            set
            {
                this.paymentNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string masterPaymentNumber
        {
            get
            {
                return this.masterPaymentNumberField;
            }
            set
            {
                this.masterPaymentNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string orderNumber
        {
            get
            {
                return this.orderNumberField;
            }
            set
            {
                this.orderNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public string referenceNumber
        {
            get
            {
                return this.referenceNumberField;
            }
            set
            {
                this.referenceNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=7)]
        public long amount
        {
            get
            {
                return this.amountField;
            }
            set
            {
                this.amountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool amountSpecified
        {
            get
            {
                return this.amountFieldSpecified;
            }
            set
            {
                this.amountFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=8)]
        public string currencyCode
        {
            get
            {
                return this.currencyCodeField;
            }
            set
            {
                this.currencyCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=9)]
        public int captureFlag
        {
            get
            {
                return this.captureFlagField;
            }
            set
            {
                this.captureFlagField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=10)]
        public SubMerchantData subMerchantData
        {
            get
            {
                return this.subMerchantDataField;
            }
            set
            {
                this.subMerchantDataField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=11)]
        public CardHolderData cardHolderData
        {
            get
            {
                return this.cardHolderDataField;
            }
            set
            {
                this.cardHolderDataField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=12)]
        public PaymentInfo paymentInfo
        {
            get
            {
                return this.paymentInfoField;
            }
            set
            {
                this.paymentInfoField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=13)]
        public ShoppingCartInfo shoppingCartInfo
        {
            get
            {
                return this.shoppingCartInfoField;
            }
            set
            {
                this.shoppingCartInfoField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=14)]
        public AltTerminalData altTerminalData
        {
            get
            {
                return this.altTerminalDataField;
            }
            set
            {
                this.altTerminalDataField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=15)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class UsageBasedSubscriptionPaymentResponse
    {
        
        private string messageIdField;
        
        private string authCodeField;
        
        private string traceIdField;
        
        private string authResponseCodeField;
        
        private string authRRNField;
        
        private string paymentAccountReferenceField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string authCode
        {
            get
            {
                return this.authCodeField;
            }
            set
            {
                this.authCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string traceId
        {
            get
            {
                return this.traceIdField;
            }
            set
            {
                this.traceIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string authResponseCode
        {
            get
            {
                return this.authResponseCodeField;
            }
            set
            {
                this.authResponseCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string authRRN
        {
            get
            {
                return this.authRRNField;
            }
            set
            {
                this.authRRNField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string paymentAccountReference
        {
            get
            {
                return this.paymentAccountReferenceField;
            }
            set
            {
                this.paymentAccountReferenceField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=6)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class RegularSubscriptionPaymentRequest
    {
        
        private string messageIdField;
        
        private string providerField;
        
        private string merchantNumberField;
        
        private string paymentNumberField;
        
        private string masterPaymentNumberField;
        
        private string orderNumberField;
        
        private string referenceNumberField;
        
        private long subscriptionAmountField;
        
        private bool subscriptionAmountFieldSpecified;
        
        private int captureFlagField;
        
        private SubMerchantData subMerchantDataField;
        
        private CardHolderData cardHolderDataField;
        
        private PaymentInfo paymentInfoField;
        
        private ShoppingCartInfo shoppingCartInfoField;
        
        private AltTerminalData altTerminalDataField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string provider
        {
            get
            {
                return this.providerField;
            }
            set
            {
                this.providerField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string merchantNumber
        {
            get
            {
                return this.merchantNumberField;
            }
            set
            {
                this.merchantNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string paymentNumber
        {
            get
            {
                return this.paymentNumberField;
            }
            set
            {
                this.paymentNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string masterPaymentNumber
        {
            get
            {
                return this.masterPaymentNumberField;
            }
            set
            {
                this.masterPaymentNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string orderNumber
        {
            get
            {
                return this.orderNumberField;
            }
            set
            {
                this.orderNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public string referenceNumber
        {
            get
            {
                return this.referenceNumberField;
            }
            set
            {
                this.referenceNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=7)]
        public long subscriptionAmount
        {
            get
            {
                return this.subscriptionAmountField;
            }
            set
            {
                this.subscriptionAmountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool subscriptionAmountSpecified
        {
            get
            {
                return this.subscriptionAmountFieldSpecified;
            }
            set
            {
                this.subscriptionAmountFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=8)]
        public int captureFlag
        {
            get
            {
                return this.captureFlagField;
            }
            set
            {
                this.captureFlagField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=9)]
        public SubMerchantData subMerchantData
        {
            get
            {
                return this.subMerchantDataField;
            }
            set
            {
                this.subMerchantDataField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=10)]
        public CardHolderData cardHolderData
        {
            get
            {
                return this.cardHolderDataField;
            }
            set
            {
                this.cardHolderDataField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=11)]
        public PaymentInfo paymentInfo
        {
            get
            {
                return this.paymentInfoField;
            }
            set
            {
                this.paymentInfoField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=12)]
        public ShoppingCartInfo shoppingCartInfo
        {
            get
            {
                return this.shoppingCartInfoField;
            }
            set
            {
                this.shoppingCartInfoField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=13)]
        public AltTerminalData altTerminalData
        {
            get
            {
                return this.altTerminalDataField;
            }
            set
            {
                this.altTerminalDataField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=14)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class RegularSubscriptionPaymentResponse
    {
        
        private string messageIdField;
        
        private string authCodeField;
        
        private string traceIdField;
        
        private string authResponseCodeField;
        
        private string authRRNField;
        
        private string paymentAccountReferenceField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string authCode
        {
            get
            {
                return this.authCodeField;
            }
            set
            {
                this.authCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string traceId
        {
            get
            {
                return this.traceIdField;
            }
            set
            {
                this.traceIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string authResponseCode
        {
            get
            {
                return this.authResponseCodeField;
            }
            set
            {
                this.authResponseCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string authRRN
        {
            get
            {
                return this.authRRNField;
            }
            set
            {
                this.authRRNField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string paymentAccountReference
        {
            get
            {
                return this.paymentAccountReferenceField;
            }
            set
            {
                this.paymentAccountReferenceField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=6)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class PrepaidPaymentRequest
    {
        
        private string messageIdField;
        
        private string providerField;
        
        private string merchantNumberField;
        
        private string paymentNumberField;
        
        private string masterPaymentNumberField;
        
        private string orderNumberField;
        
        private string referenceNumberField;
        
        private long subscriptionAmountField;
        
        private bool subscriptionAmountFieldSpecified;
        
        private int captureFlagField;
        
        private SubMerchantData subMerchantDataField;
        
        private CardHolderData cardHolderDataField;
        
        private PaymentInfo paymentInfoField;
        
        private ShoppingCartInfo shoppingCartInfoField;
        
        private AltTerminalData altTerminalDataField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string provider
        {
            get
            {
                return this.providerField;
            }
            set
            {
                this.providerField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string merchantNumber
        {
            get
            {
                return this.merchantNumberField;
            }
            set
            {
                this.merchantNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string paymentNumber
        {
            get
            {
                return this.paymentNumberField;
            }
            set
            {
                this.paymentNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string masterPaymentNumber
        {
            get
            {
                return this.masterPaymentNumberField;
            }
            set
            {
                this.masterPaymentNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string orderNumber
        {
            get
            {
                return this.orderNumberField;
            }
            set
            {
                this.orderNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public string referenceNumber
        {
            get
            {
                return this.referenceNumberField;
            }
            set
            {
                this.referenceNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=7)]
        public long subscriptionAmount
        {
            get
            {
                return this.subscriptionAmountField;
            }
            set
            {
                this.subscriptionAmountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool subscriptionAmountSpecified
        {
            get
            {
                return this.subscriptionAmountFieldSpecified;
            }
            set
            {
                this.subscriptionAmountFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=8)]
        public int captureFlag
        {
            get
            {
                return this.captureFlagField;
            }
            set
            {
                this.captureFlagField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=9)]
        public SubMerchantData subMerchantData
        {
            get
            {
                return this.subMerchantDataField;
            }
            set
            {
                this.subMerchantDataField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=10)]
        public CardHolderData cardHolderData
        {
            get
            {
                return this.cardHolderDataField;
            }
            set
            {
                this.cardHolderDataField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=11)]
        public PaymentInfo paymentInfo
        {
            get
            {
                return this.paymentInfoField;
            }
            set
            {
                this.paymentInfoField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=12)]
        public ShoppingCartInfo shoppingCartInfo
        {
            get
            {
                return this.shoppingCartInfoField;
            }
            set
            {
                this.shoppingCartInfoField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=13)]
        public AltTerminalData altTerminalData
        {
            get
            {
                return this.altTerminalDataField;
            }
            set
            {
                this.altTerminalDataField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=14)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class PrepaidPaymentResponse
    {
        
        private string messageIdField;
        
        private string authCodeField;
        
        private string traceIdField;
        
        private string authResponseCodeField;
        
        private string authRRNField;
        
        private string paymentAccountReferenceField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string authCode
        {
            get
            {
                return this.authCodeField;
            }
            set
            {
                this.authCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string traceId
        {
            get
            {
                return this.traceIdField;
            }
            set
            {
                this.traceIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string authResponseCode
        {
            get
            {
                return this.authResponseCodeField;
            }
            set
            {
                this.authResponseCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string authRRN
        {
            get
            {
                return this.authRRNField;
            }
            set
            {
                this.authRRNField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string paymentAccountReference
        {
            get
            {
                return this.paymentAccountReferenceField;
            }
            set
            {
                this.paymentAccountReferenceField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=6)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class CardOnFilePaymentResponse
    {
        
        private string messageIdField;
        
        private string authCodeField;
        
        private string tokenDataField;
        
        private string traceIdField;
        
        private string authResponseCodeField;
        
        private string authRRNField;
        
        private string paymentAccountReferenceField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string authCode
        {
            get
            {
                return this.authCodeField;
            }
            set
            {
                this.authCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string tokenData
        {
            get
            {
                return this.tokenDataField;
            }
            set
            {
                this.tokenDataField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string traceId
        {
            get
            {
                return this.traceIdField;
            }
            set
            {
                this.traceIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string authResponseCode
        {
            get
            {
                return this.authResponseCodeField;
            }
            set
            {
                this.authResponseCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string authRRN
        {
            get
            {
                return this.authRRNField;
            }
            set
            {
                this.authRRNField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public string paymentAccountReference
        {
            get
            {
                return this.paymentAccountReferenceField;
            }
            set
            {
                this.paymentAccountReferenceField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=7)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class MpsPreCheckoutRequest
    {
        
        private string messageIdField;
        
        private string providerField;
        
        private string merchantNumberField;
        
        private string pairingNumberField;
        
        private BooleanType requestCardDetailsField;
        
        private bool requestCardDetailsFieldSpecified;
        
        private BooleanType requestShippingDetailsField;
        
        private bool requestShippingDetailsFieldSpecified;
        
        private BooleanType requestRewardProgramsField;
        
        private bool requestRewardProgramsFieldSpecified;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string provider
        {
            get
            {
                return this.providerField;
            }
            set
            {
                this.providerField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string merchantNumber
        {
            get
            {
                return this.merchantNumberField;
            }
            set
            {
                this.merchantNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string pairingNumber
        {
            get
            {
                return this.pairingNumberField;
            }
            set
            {
                this.pairingNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public BooleanType requestCardDetails
        {
            get
            {
                return this.requestCardDetailsField;
            }
            set
            {
                this.requestCardDetailsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool requestCardDetailsSpecified
        {
            get
            {
                return this.requestCardDetailsFieldSpecified;
            }
            set
            {
                this.requestCardDetailsFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public BooleanType requestShippingDetails
        {
            get
            {
                return this.requestShippingDetailsField;
            }
            set
            {
                this.requestShippingDetailsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool requestShippingDetailsSpecified
        {
            get
            {
                return this.requestShippingDetailsFieldSpecified;
            }
            set
            {
                this.requestShippingDetailsFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public BooleanType requestRewardPrograms
        {
            get
            {
                return this.requestRewardProgramsField;
            }
            set
            {
                this.requestRewardProgramsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool requestRewardProgramsSpecified
        {
            get
            {
                return this.requestRewardProgramsFieldSpecified;
            }
            set
            {
                this.requestRewardProgramsFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=7)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class MpsPreCheckoutResponse
    {
        
        private string messageIdField;
        
        private MpsPreCheckoutData preCheckoutDataField;
        
        private string walletPartnerLogoUrlField;
        
        private string masterpassLogoUrlField;
        
        private SimpleValueHolder[] simpleValueHolderField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public MpsPreCheckoutData preCheckoutData
        {
            get
            {
                return this.preCheckoutDataField;
            }
            set
            {
                this.preCheckoutDataField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="anyURI", Order=2)]
        public string walletPartnerLogoUrl
        {
            get
            {
                return this.walletPartnerLogoUrlField;
            }
            set
            {
                this.walletPartnerLogoUrlField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="anyURI", Order=3)]
        public string masterpassLogoUrl
        {
            get
            {
                return this.masterpassLogoUrlField;
            }
            set
            {
                this.masterpassLogoUrlField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("simpleValueHolder", Order=4)]
        public SimpleValueHolder[] simpleValueHolder
        {
            get
            {
                return this.simpleValueHolderField;
            }
            set
            {
                this.simpleValueHolderField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=5)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class MpsPreCheckoutData
    {
        
        private MpsPreCheckoutCard[] cardsField;
        
        private MpsContact contactField;
        
        private MpsPreCheckoutShippingAddress[] shippingAddressesField;
        
        private MpsPreCheckoutRewardProgram[] rewardProgramsField;
        
        private string walletNameField;
        
        private SimpleValueHolder[] simpleValueHolderField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("cards", Order=0)]
        public MpsPreCheckoutCard[] cards
        {
            get
            {
                return this.cardsField;
            }
            set
            {
                this.cardsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public MpsContact contact
        {
            get
            {
                return this.contactField;
            }
            set
            {
                this.contactField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("shippingAddresses", Order=2)]
        public MpsPreCheckoutShippingAddress[] shippingAddresses
        {
            get
            {
                return this.shippingAddressesField;
            }
            set
            {
                this.shippingAddressesField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("rewardPrograms", Order=3)]
        public MpsPreCheckoutRewardProgram[] rewardPrograms
        {
            get
            {
                return this.rewardProgramsField;
            }
            set
            {
                this.rewardProgramsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string walletName
        {
            get
            {
                return this.walletNameField;
            }
            set
            {
                this.walletNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("simpleValueHolder", Order=5)]
        public SimpleValueHolder[] simpleValueHolder
        {
            get
            {
                return this.simpleValueHolderField;
            }
            set
            {
                this.simpleValueHolderField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class MpsPreCheckoutCard
    {
        
        private string brandIdField;
        
        private string brandNameField;
        
        private MpsAddress billingAddressField;
        
        private string cardHolderNameField;
        
        private int expiryMonthField;
        
        private bool expiryMonthFieldSpecified;
        
        private int expiryYearField;
        
        private bool expiryYearFieldSpecified;
        
        private string cardIdField;
        
        private string lastFourField;
        
        private string cardAliasField;
        
        private bool selectedAsDefaultField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string brandId
        {
            get
            {
                return this.brandIdField;
            }
            set
            {
                this.brandIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string brandName
        {
            get
            {
                return this.brandNameField;
            }
            set
            {
                this.brandNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public MpsAddress billingAddress
        {
            get
            {
                return this.billingAddressField;
            }
            set
            {
                this.billingAddressField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string cardHolderName
        {
            get
            {
                return this.cardHolderNameField;
            }
            set
            {
                this.cardHolderNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public int expiryMonth
        {
            get
            {
                return this.expiryMonthField;
            }
            set
            {
                this.expiryMonthField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool expiryMonthSpecified
        {
            get
            {
                return this.expiryMonthFieldSpecified;
            }
            set
            {
                this.expiryMonthFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public int expiryYear
        {
            get
            {
                return this.expiryYearField;
            }
            set
            {
                this.expiryYearField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool expiryYearSpecified
        {
            get
            {
                return this.expiryYearFieldSpecified;
            }
            set
            {
                this.expiryYearFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public string cardId
        {
            get
            {
                return this.cardIdField;
            }
            set
            {
                this.cardIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=7)]
        public string lastFour
        {
            get
            {
                return this.lastFourField;
            }
            set
            {
                this.lastFourField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=8)]
        public string cardAlias
        {
            get
            {
                return this.cardAliasField;
            }
            set
            {
                this.cardAliasField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=9)]
        public bool selectedAsDefault
        {
            get
            {
                return this.selectedAsDefaultField;
            }
            set
            {
                this.selectedAsDefaultField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(MpsPreCheckoutShippingAddress))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class MpsAddress
    {
        
        private string cityField;
        
        private string countryField;
        
        private string countrySubdivisionField;
        
        private string address1Field;
        
        private string address2Field;
        
        private string address3Field;
        
        private string postalCodeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string city
        {
            get
            {
                return this.cityField;
            }
            set
            {
                this.cityField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string country
        {
            get
            {
                return this.countryField;
            }
            set
            {
                this.countryField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string countrySubdivision
        {
            get
            {
                return this.countrySubdivisionField;
            }
            set
            {
                this.countrySubdivisionField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string address1
        {
            get
            {
                return this.address1Field;
            }
            set
            {
                this.address1Field = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string address2
        {
            get
            {
                return this.address2Field;
            }
            set
            {
                this.address2Field = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string address3
        {
            get
            {
                return this.address3Field;
            }
            set
            {
                this.address3Field = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public string postalCode
        {
            get
            {
                return this.postalCodeField;
            }
            set
            {
                this.postalCodeField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class MpsPreCheckoutShippingAddress : MpsAddress
    {
        
        private string recipientNameField;
        
        private string recipientPhoneNumberField;
        
        private string addressIdField;
        
        private bool selectedAsDefaultField;
        
        private string shippingAliasField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string recipientName
        {
            get
            {
                return this.recipientNameField;
            }
            set
            {
                this.recipientNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string recipientPhoneNumber
        {
            get
            {
                return this.recipientPhoneNumberField;
            }
            set
            {
                this.recipientPhoneNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string addressId
        {
            get
            {
                return this.addressIdField;
            }
            set
            {
                this.addressIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public bool selectedAsDefault
        {
            get
            {
                return this.selectedAsDefaultField;
            }
            set
            {
                this.selectedAsDefaultField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string shippingAlias
        {
            get
            {
                return this.shippingAliasField;
            }
            set
            {
                this.shippingAliasField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class MpsContact
    {
        
        private string firstNameField;
        
        private string middleNameField;
        
        private string lastNameField;
        
        private string countryField;
        
        private string emailAddressField;
        
        private string phoneNumberField;
        
        private SimpleValueHolder[] simpleValueHolderField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string firstName
        {
            get
            {
                return this.firstNameField;
            }
            set
            {
                this.firstNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string middleName
        {
            get
            {
                return this.middleNameField;
            }
            set
            {
                this.middleNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string lastName
        {
            get
            {
                return this.lastNameField;
            }
            set
            {
                this.lastNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string country
        {
            get
            {
                return this.countryField;
            }
            set
            {
                this.countryField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string emailAddress
        {
            get
            {
                return this.emailAddressField;
            }
            set
            {
                this.emailAddressField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string phoneNumber
        {
            get
            {
                return this.phoneNumberField;
            }
            set
            {
                this.phoneNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("simpleValueHolder", Order=6)]
        public SimpleValueHolder[] simpleValueHolder
        {
            get
            {
                return this.simpleValueHolderField;
            }
            set
            {
                this.simpleValueHolderField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class MpsPreCheckoutRewardProgram
    {
        
        private string rewardNumberField;
        
        private string rewardIdField;
        
        private string rewardNameField;
        
        private int expiryMonthField;
        
        private bool expiryMonthFieldSpecified;
        
        private int expiryYearField;
        
        private bool expiryYearFieldSpecified;
        
        private string rewardProgramIdField;
        
        private string rewardLogoUrlField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string rewardNumber
        {
            get
            {
                return this.rewardNumberField;
            }
            set
            {
                this.rewardNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string rewardId
        {
            get
            {
                return this.rewardIdField;
            }
            set
            {
                this.rewardIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string rewardName
        {
            get
            {
                return this.rewardNameField;
            }
            set
            {
                this.rewardNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public int expiryMonth
        {
            get
            {
                return this.expiryMonthField;
            }
            set
            {
                this.expiryMonthField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool expiryMonthSpecified
        {
            get
            {
                return this.expiryMonthFieldSpecified;
            }
            set
            {
                this.expiryMonthFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public int expiryYear
        {
            get
            {
                return this.expiryYearField;
            }
            set
            {
                this.expiryYearField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool expiryYearSpecified
        {
            get
            {
                return this.expiryYearFieldSpecified;
            }
            set
            {
                this.expiryYearFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string rewardProgramId
        {
            get
            {
                return this.rewardProgramIdField;
            }
            set
            {
                this.rewardProgramIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public string rewardLogoUrl
        {
            get
            {
                return this.rewardLogoUrlField;
            }
            set
            {
                this.rewardLogoUrlField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class MpsExpressCheckoutRequest
    {
        
        private string messageIdField;
        
        private string providerField;
        
        private string merchantNumberField;
        
        private string paymentNumberField;
        
        private string orderNumberField;
        
        private string referenceNumberField;
        
        private long amountField;
        
        private string currencyCodeField;
        
        private int captureFlagField;
        
        private string pairingNumberField;
        
        private string cardIdField;
        
        private string shippingAddressIdField;
        
        private CardHolderData cardHolderDataField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string provider
        {
            get
            {
                return this.providerField;
            }
            set
            {
                this.providerField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string merchantNumber
        {
            get
            {
                return this.merchantNumberField;
            }
            set
            {
                this.merchantNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string paymentNumber
        {
            get
            {
                return this.paymentNumberField;
            }
            set
            {
                this.paymentNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string orderNumber
        {
            get
            {
                return this.orderNumberField;
            }
            set
            {
                this.orderNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string referenceNumber
        {
            get
            {
                return this.referenceNumberField;
            }
            set
            {
                this.referenceNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public long amount
        {
            get
            {
                return this.amountField;
            }
            set
            {
                this.amountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=7)]
        public string currencyCode
        {
            get
            {
                return this.currencyCodeField;
            }
            set
            {
                this.currencyCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=8)]
        public int captureFlag
        {
            get
            {
                return this.captureFlagField;
            }
            set
            {
                this.captureFlagField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=9)]
        public string pairingNumber
        {
            get
            {
                return this.pairingNumberField;
            }
            set
            {
                this.pairingNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=10)]
        public string cardId
        {
            get
            {
                return this.cardIdField;
            }
            set
            {
                this.cardIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=11)]
        public string shippingAddressId
        {
            get
            {
                return this.shippingAddressIdField;
            }
            set
            {
                this.shippingAddressIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=12)]
        public CardHolderData cardHolderData
        {
            get
            {
                return this.cardHolderDataField;
            }
            set
            {
                this.cardHolderDataField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=13)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class MpsExpressCheckoutResponse
    {
        
        private string messageIdField;
        
        private string authCodeField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string authCode
        {
            get
            {
                return this.authCodeField;
            }
            set
            {
                this.authCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=2)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class ResolvePaymentStatusRequest
    {
        
        private string messageIdField;
        
        private string providerField;
        
        private string merchantNumberField;
        
        private string paymentNumberField;
        
        private string paymentStatusField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string provider
        {
            get
            {
                return this.providerField;
            }
            set
            {
                this.providerField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string merchantNumber
        {
            get
            {
                return this.merchantNumberField;
            }
            set
            {
                this.merchantNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string paymentNumber
        {
            get
            {
                return this.paymentNumberField;
            }
            set
            {
                this.paymentNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string paymentStatus
        {
            get
            {
                return this.paymentStatusField;
            }
            set
            {
                this.paymentStatusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=5)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class TokenStatusRequest
    {
        
        private string messageIdField;
        
        private string providerField;
        
        private string merchantNumberField;
        
        private string tokenDataField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string provider
        {
            get
            {
                return this.providerField;
            }
            set
            {
                this.providerField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string merchantNumber
        {
            get
            {
                return this.merchantNumberField;
            }
            set
            {
                this.merchantNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string tokenData
        {
            get
            {
                return this.tokenDataField;
            }
            set
            {
                this.tokenDataField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=4)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class TokenStatusResponse
    {
        
        private string messageIdField;
        
        private string statusField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string status
        {
            get
            {
                return this.statusField;
            }
            set
            {
                this.statusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=2)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class TokenRevokeRequest
    {
        
        private string messageIdField;
        
        private string providerField;
        
        private string merchantNumberField;
        
        private string tokenDataField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string provider
        {
            get
            {
                return this.providerField;
            }
            set
            {
                this.providerField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string merchantNumber
        {
            get
            {
                return this.merchantNumberField;
            }
            set
            {
                this.merchantNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string tokenData
        {
            get
            {
                return this.tokenDataField;
            }
            set
            {
                this.tokenDataField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=4)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class TokenRevokeResponse
    {
        
        private string messageIdField;
        
        private string statusField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string status
        {
            get
            {
                return this.statusField;
            }
            set
            {
                this.statusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=2)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class TokenPaymentRequest
    {
        
        private string messageIdField;
        
        private string providerField;
        
        private string merchantNumberField;
        
        private string paymentNumberField;
        
        private string orderNumberField;
        
        private string referenceNumberField;
        
        private long amountField;
        
        private string currencyCodeField;
        
        private int captureFlagField;
        
        private SubMerchantData subMerchantDataField;
        
        private string tokenDataField;
        
        private CardHolderData cardHolderDataField;
        
        private AltTerminalData altTerminalDataField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string provider
        {
            get
            {
                return this.providerField;
            }
            set
            {
                this.providerField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string merchantNumber
        {
            get
            {
                return this.merchantNumberField;
            }
            set
            {
                this.merchantNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string paymentNumber
        {
            get
            {
                return this.paymentNumberField;
            }
            set
            {
                this.paymentNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string orderNumber
        {
            get
            {
                return this.orderNumberField;
            }
            set
            {
                this.orderNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string referenceNumber
        {
            get
            {
                return this.referenceNumberField;
            }
            set
            {
                this.referenceNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public long amount
        {
            get
            {
                return this.amountField;
            }
            set
            {
                this.amountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=7)]
        public string currencyCode
        {
            get
            {
                return this.currencyCodeField;
            }
            set
            {
                this.currencyCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=8)]
        public int captureFlag
        {
            get
            {
                return this.captureFlagField;
            }
            set
            {
                this.captureFlagField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=9)]
        public SubMerchantData subMerchantData
        {
            get
            {
                return this.subMerchantDataField;
            }
            set
            {
                this.subMerchantDataField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=10)]
        public string tokenData
        {
            get
            {
                return this.tokenDataField;
            }
            set
            {
                this.tokenDataField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=11)]
        public CardHolderData cardHolderData
        {
            get
            {
                return this.cardHolderDataField;
            }
            set
            {
                this.cardHolderDataField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=12)]
        public AltTerminalData altTerminalData
        {
            get
            {
                return this.altTerminalDataField;
            }
            set
            {
                this.altTerminalDataField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=13)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://gpe.cz/pay/pay-ws/proc/v1/type")]
    public partial class TokenPaymentResponse
    {
        
        private string messageIdField;
        
        private string authCodeField;
        
        private string tokenDataField;
        
        private string traceIdField;
        
        private string authResponseCodeField;
        
        private string authRRNField;
        
        private string paymentAccountReferenceField;
        
        private byte[] signatureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string messageId
        {
            get
            {
                return this.messageIdField;
            }
            set
            {
                this.messageIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string authCode
        {
            get
            {
                return this.authCodeField;
            }
            set
            {
                this.authCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string tokenData
        {
            get
            {
                return this.tokenDataField;
            }
            set
            {
                this.tokenDataField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string traceId
        {
            get
            {
                return this.traceIdField;
            }
            set
            {
                this.traceIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string authResponseCode
        {
            get
            {
                return this.authResponseCodeField;
            }
            set
            {
                this.authResponseCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string authRRN
        {
            get
            {
                return this.authRRNField;
            }
            set
            {
                this.authRRNField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public string paymentAccountReference
        {
            get
            {
                return this.paymentAccountReferenceField;
            }
            set
            {
                this.paymentAccountReferenceField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary", Order=7)]
        public byte[] signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    public interface PaymentPortChannel : Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentPort, System.ServiceModel.IClientChannel
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    public partial class PaymentPortClient : System.ServiceModel.ClientBase<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentPort>, Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentPort
    {
        
        /// <summary>
        /// Implement this partial method to configure the service endpoint.
        /// </summary>
        /// <param name="serviceEndpoint">The endpoint to configure</param>
        /// <param name="clientCredentials">The client credentials</param>
        static partial void ConfigureEndpoint(System.ServiceModel.Description.ServiceEndpoint serviceEndpoint, System.ServiceModel.Description.ClientCredentials clientCredentials);
        
        public PaymentPortClient() : 
                base(PaymentPortClient.GetDefaultBinding(), PaymentPortClient.GetDefaultEndpointAddress())
        {
            this.Endpoint.Name = EndpointConfiguration.PaymentPortV1.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public PaymentPortClient(EndpointConfiguration endpointConfiguration) : 
                base(PaymentPortClient.GetBindingForEndpoint(endpointConfiguration), PaymentPortClient.GetEndpointAddress(endpointConfiguration))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public PaymentPortClient(EndpointConfiguration endpointConfiguration, string remoteAddress) : 
                base(PaymentPortClient.GetBindingForEndpoint(endpointConfiguration), new System.ServiceModel.EndpointAddress(remoteAddress))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public PaymentPortClient(EndpointConfiguration endpointConfiguration, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(PaymentPortClient.GetBindingForEndpoint(endpointConfiguration), remoteAddress)
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public PaymentPortClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress)
        {
        }
        
        public void echo()
        {
            base.Channel.echo();
        }
        
        public System.Threading.Tasks.Task echoAsync()
        {
            return base.Channel.echoAsync();
        }
        
        public Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentStatusResponse getPaymentStatus(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentStatusRequest paymentStatusRequest)
        {
            return base.Channel.getPaymentStatus(paymentStatusRequest);
        }
        
        public System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentStatusResponse> getPaymentStatusAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentStatusRequest paymentStatusRequest)
        {
            return base.Channel.getPaymentStatusAsync(paymentStatusRequest);
        }
        
        public Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.MasterPaymentStatusResponse getMasterPaymentStatus(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentStatusRequest masterPaymentStatusRequest)
        {
            return base.Channel.getMasterPaymentStatus(masterPaymentStatusRequest);
        }
        
        public System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.MasterPaymentStatusResponse> getMasterPaymentStatusAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentStatusRequest masterPaymentStatusRequest)
        {
            return base.Channel.getMasterPaymentStatusAsync(masterPaymentStatusRequest);
        }
        
        public Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.MasterPaymentStatusResponse processMasterPaymentRevoke(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentStatusRequest masterPaymentStatusRequest)
        {
            return base.Channel.processMasterPaymentRevoke(masterPaymentStatusRequest);
        }
        
        public System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.MasterPaymentStatusResponse> processMasterPaymentRevokeAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentStatusRequest masterPaymentStatusRequest)
        {
            return base.Channel.processMasterPaymentRevokeAsync(masterPaymentStatusRequest);
        }
        
        public Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.RevokePaymentLinkResponse revokePaymentLink(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentStatusRequest revokePaymentLinkRequest)
        {
            return base.Channel.revokePaymentLink(revokePaymentLinkRequest);
        }
        
        public System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.RevokePaymentLinkResponse> revokePaymentLinkAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentStatusRequest revokePaymentLinkRequest)
        {
            return base.Channel.revokePaymentLinkAsync(revokePaymentLinkRequest);
        }
        
        public Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentDetailResponse getPaymentDetail(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentStatusRequest paymentDetailRequest)
        {
            return base.Channel.getPaymentDetail(paymentDetailRequest);
        }
        
        public System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentDetailResponse> getPaymentDetailAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentStatusRequest paymentDetailRequest)
        {
            return base.Channel.getPaymentDetailAsync(paymentDetailRequest);
        }
        
        public Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.AuthorizationReverseResponse processAuthorizationReverse(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.AuthorizationReverseRequest authorizationReverseRequest)
        {
            return base.Channel.processAuthorizationReverse(authorizationReverseRequest);
        }
        
        public System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.AuthorizationReverseResponse> processAuthorizationReverseAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.AuthorizationReverseRequest authorizationReverseRequest)
        {
            return base.Channel.processAuthorizationReverseAsync(authorizationReverseRequest);
        }
        
        public Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.BatchCloseResponse processBatchClose(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.BatchCloseRequest batchClose)
        {
            return base.Channel.processBatchClose(batchClose);
        }
        
        public System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.BatchCloseResponse> processBatchCloseAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.BatchCloseRequest batchClose)
        {
            return base.Channel.processBatchCloseAsync(batchClose);
        }
        
        public Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.RefundResponse processRefund(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.RefundRequest refundRequest)
        {
            return base.Channel.processRefund(refundRequest);
        }
        
        public System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.RefundResponse> processRefundAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.RefundRequest refundRequest)
        {
            return base.Channel.processRefundAsync(refundRequest);
        }
        
        public Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.RefundReverseResponse processRefundReverse(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.RefundReverseRequest refundReverseRequest)
        {
            return base.Channel.processRefundReverse(refundReverseRequest);
        }
        
        public System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.RefundReverseResponse> processRefundReverseAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.RefundReverseRequest refundReverseRequest)
        {
            return base.Channel.processRefundReverseAsync(refundReverseRequest);
        }
        
        public Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentDeleteResponse processPaymentDelete(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentDeleteRequest paymentDeleteRequest)
        {
            return base.Channel.processPaymentDelete(paymentDeleteRequest);
        }
        
        public System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentDeleteResponse> processPaymentDeleteAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentDeleteRequest paymentDeleteRequest)
        {
            return base.Channel.processPaymentDeleteAsync(paymentDeleteRequest);
        }
        
        public Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.CaptureResponse processCapture(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.CaptureRequest captureRequest)
        {
            return base.Channel.processCapture(captureRequest);
        }
        
        public System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.CaptureResponse> processCaptureAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.CaptureRequest captureRequest)
        {
            return base.Channel.processCaptureAsync(captureRequest);
        }
        
        public Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.CaptureReverseResponse processCaptureReverse(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.CaptureReverseRequest captureReverseRequest)
        {
            return base.Channel.processCaptureReverse(captureReverseRequest);
        }
        
        public System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.CaptureReverseResponse> processCaptureReverseAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.CaptureReverseRequest captureReverseRequest)
        {
            return base.Channel.processCaptureReverseAsync(captureReverseRequest);
        }
        
        public Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentCloseResponse processPaymentClose(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentCloseRequest paymentCloseRequest)
        {
            return base.Channel.processPaymentClose(paymentCloseRequest);
        }
        
        public System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentCloseResponse> processPaymentCloseAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentCloseRequest paymentCloseRequest)
        {
            return base.Channel.processPaymentCloseAsync(paymentCloseRequest);
        }
        
        public Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.RecurringPaymentResponse processRecurringPayment(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.RecurringPaymentRequest recurringPaymentRequest)
        {
            return base.Channel.processRecurringPayment(recurringPaymentRequest);
        }
        
        public System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.RecurringPaymentResponse> processRecurringPaymentAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.RecurringPaymentRequest recurringPaymentRequest)
        {
            return base.Channel.processRecurringPaymentAsync(recurringPaymentRequest);
        }
        
        public Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentLinkResponse createPaymentLink(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentLinkRequest paymentLinkRequest)
        {
            return base.Channel.createPaymentLink(paymentLinkRequest);
        }
        
        public System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentLinkResponse> createPaymentLinkAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentLinkRequest paymentLinkRequest)
        {
            return base.Channel.createPaymentLinkAsync(paymentLinkRequest);
        }
        
        public Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.UsageBasedPaymentResponse processUsageBasedPayment(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.CardOnFilePaymentRequest usageBasedPaymentRequest)
        {
            return base.Channel.processUsageBasedPayment(usageBasedPaymentRequest);
        }
        
        public System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.UsageBasedPaymentResponse> processUsageBasedPaymentAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.CardOnFilePaymentRequest usageBasedPaymentRequest)
        {
            return base.Channel.processUsageBasedPaymentAsync(usageBasedPaymentRequest);
        }
        
        public Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.UsageBasedSubscriptionPaymentResponse processUsageBasedSubscriptionPayment(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.UsageBasedSubscriptionPaymentRequest usageBasedSubscriptionPaymentRequest)
        {
            return base.Channel.processUsageBasedSubscriptionPayment(usageBasedSubscriptionPaymentRequest);
        }
        
        public System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.UsageBasedSubscriptionPaymentResponse> processUsageBasedSubscriptionPaymentAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.UsageBasedSubscriptionPaymentRequest usageBasedSubscriptionPaymentRequest)
        {
            return base.Channel.processUsageBasedSubscriptionPaymentAsync(usageBasedSubscriptionPaymentRequest);
        }
        
        public Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.RegularSubscriptionPaymentResponse processRegularSubscriptionPayment(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.RegularSubscriptionPaymentRequest regularSubscriptionPaymentRequest)
        {
            return base.Channel.processRegularSubscriptionPayment(regularSubscriptionPaymentRequest);
        }
        
        public System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.RegularSubscriptionPaymentResponse> processRegularSubscriptionPaymentAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.RegularSubscriptionPaymentRequest regularSubscriptionPaymentRequest)
        {
            return base.Channel.processRegularSubscriptionPaymentAsync(regularSubscriptionPaymentRequest);
        }
        
        public Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PrepaidPaymentResponse processPrepaidPayment(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PrepaidPaymentRequest prepaidPaymentRequest)
        {
            return base.Channel.processPrepaidPayment(prepaidPaymentRequest);
        }
        
        public System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PrepaidPaymentResponse> processPrepaidPaymentAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PrepaidPaymentRequest prepaidPaymentRequest)
        {
            return base.Channel.processPrepaidPaymentAsync(prepaidPaymentRequest);
        }
        
        public Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.CardOnFilePaymentResponse processCardOnFilePayment(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.CardOnFilePaymentRequest cardOnFilePaymentRequest)
        {
            return base.Channel.processCardOnFilePayment(cardOnFilePaymentRequest);
        }
        
        public System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.CardOnFilePaymentResponse> processCardOnFilePaymentAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.CardOnFilePaymentRequest cardOnFilePaymentRequest)
        {
            return base.Channel.processCardOnFilePaymentAsync(cardOnFilePaymentRequest);
        }
        
        public Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.MpsPreCheckoutResponse mpsPreCheckout(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.MpsPreCheckoutRequest mpsPreCheckoutRequest)
        {
            return base.Channel.mpsPreCheckout(mpsPreCheckoutRequest);
        }
        
        public System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.MpsPreCheckoutResponse> mpsPreCheckoutAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.MpsPreCheckoutRequest mpsPreCheckoutRequest)
        {
            return base.Channel.mpsPreCheckoutAsync(mpsPreCheckoutRequest);
        }
        
        public Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.MpsExpressCheckoutResponse mpsExpressCheckout(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.MpsExpressCheckoutRequest mpsExpressCheckoutRequest)
        {
            return base.Channel.mpsExpressCheckout(mpsExpressCheckoutRequest);
        }
        
        public System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.MpsExpressCheckoutResponse> mpsExpressCheckoutAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.MpsExpressCheckoutRequest mpsExpressCheckoutRequest)
        {
            return base.Channel.mpsExpressCheckoutAsync(mpsExpressCheckoutRequest);
        }
        
        public Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentStatusResponse resolvePaymentStatus(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.ResolvePaymentStatusRequest resolvePaymentStatusRequest)
        {
            return base.Channel.resolvePaymentStatus(resolvePaymentStatusRequest);
        }
        
        public System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.PaymentStatusResponse> resolvePaymentStatusAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.ResolvePaymentStatusRequest resolvePaymentStatusRequest)
        {
            return base.Channel.resolvePaymentStatusAsync(resolvePaymentStatusRequest);
        }
        
        public Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.TokenStatusResponse getTokenStatus(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.TokenStatusRequest tokenStatusRequest)
        {
            return base.Channel.getTokenStatus(tokenStatusRequest);
        }
        
        public System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.TokenStatusResponse> getTokenStatusAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.TokenStatusRequest tokenStatusRequest)
        {
            return base.Channel.getTokenStatusAsync(tokenStatusRequest);
        }
        
        public Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.TokenRevokeResponse processTokenRevoke(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.TokenRevokeRequest tokenRevokeRequest)
        {
            return base.Channel.processTokenRevoke(tokenRevokeRequest);
        }
        
        public System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.TokenRevokeResponse> processTokenRevokeAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.TokenRevokeRequest tokenRevokeRequest)
        {
            return base.Channel.processTokenRevokeAsync(tokenRevokeRequest);
        }
        
        public Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.TokenPaymentResponse processTokenPayment(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.TokenPaymentRequest tokenPaymentRequest)
        {
            return base.Channel.processTokenPayment(tokenPaymentRequest);
        }
        
        public System.Threading.Tasks.Task<Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.TokenPaymentResponse> processTokenPaymentAsync(Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS.TokenPaymentRequest tokenPaymentRequest)
        {
            return base.Channel.processTokenPaymentAsync(tokenPaymentRequest);
        }
        
        public virtual System.Threading.Tasks.Task OpenAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
        }
        
        public virtual System.Threading.Tasks.Task CloseAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginClose(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndClose));
        }
        
        private static System.ServiceModel.Channels.Binding GetBindingForEndpoint(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.PaymentPortV1))
            {
                System.ServiceModel.BasicHttpBinding result = new System.ServiceModel.BasicHttpBinding();
                result.MaxBufferSize = int.MaxValue;
                result.ReaderQuotas = System.Xml.XmlDictionaryReaderQuotas.Max;
                result.MaxReceivedMessageSize = int.MaxValue;
                result.AllowCookies = true;
                return result;
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        private static System.ServiceModel.EndpointAddress GetEndpointAddress(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.PaymentPortV1))
            {
                return new System.ServiceModel.EndpointAddress("file:///PaymentService");
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        private static System.ServiceModel.Channels.Binding GetDefaultBinding()
        {
            return PaymentPortClient.GetBindingForEndpoint(EndpointConfiguration.PaymentPortV1);
        }
        
        private static System.ServiceModel.EndpointAddress GetDefaultEndpointAddress()
        {
            return PaymentPortClient.GetEndpointAddress(EndpointConfiguration.PaymentPortV1);
        }
        
        public enum EndpointConfiguration
        {
            
            PaymentPortV1,
        }
    }
}
