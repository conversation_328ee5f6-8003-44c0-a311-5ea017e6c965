using System;
using System.Collections.Generic;
using System.Text;
using Anete.Config.Core.Internals;
using System.ComponentModel;

namespace Anete.Config.Configs.Core.BatchDataOperations
{
    /// <summary>
    /// Implicitni typ uctu
    /// </summary>
    [ConfigResourceEnum]
    [TypeConverter(typeof(ConfigEnumConverter<DefaultAccountType>))]
    public enum DefaultAccountType
    {
        /// <summary>
        /// Zalohovy ucet
        /// </summary>
        Credit= 0,
        /// <summary>
        /// Volny ucet
        /// </summary>
        Debit = 1,
		/// <summary>
		/// Dle skupiny, viz. https://helpdesk.anete.com/issues/63643
		/// </summary>
		ByClientGroup = 2
    }
}
