using Anete.Config.Core.Attributes;
using Anete.Config.Core.Internals;
using Anete.Devices.Interface.Scale;
using Anete.Utils.ComponentModel.Attributes;
using System;
using System.ComponentModel;

namespace Anete.Config.Configs.Core.Global.Hw
{
	/// <summary>
	/// Nastaveni emulatoru vahy.
	/// Potrebuju docilit podobnych parametru, jako maji bezne vahy. Proto davam primo do konfigurace.
	/// </summary>
	public class EmulatorScaleDeviceSettings : NestedClassBase, IScaleResolutionSettingsProvider, INawiScaleTaraDeviceSettings
	{
		private ScaleResolutionSettings _scaleResolution = new ScaleResolutionSettings();
		[ConfigSRDisplayName("ScaleResolutionSettings", typeof(CasErPlusDialog06ScaleDeviceSettings))]
		[ConfigSRDescription("ScaleResolutionSettings", typeof(CasErPlusDialog06ScaleDeviceSettings))]
		[TypeConverter(typeof(ExpandableObjectConverter))]
		[NestedClass]
		public ScaleResolutionSettings ScaleResolutionSettings
		{
			get { return _scaleResolution; }
			set
			{
				if (_scaleResolution == value)
				{
					return;
				}
				_scaleResolution = value;
				OnPropertyChanged();
			}
		}

		private const bool _useTaraDefValue = false;
		private bool _useTara = _useTaraDefValue;
		/// <summary>
		/// TcpIp adresa nebo nazev hostitele
		/// </summary>
		[DefaultValue(_useTaraDefValue)]
		[ConfigSRDisplayName("UseTara", typeof(CasErPlusDialog06ScaleDeviceSettings))]
		[ConfigSRDescription("UseTara", typeof(CasErPlusDialog06ScaleDeviceSettings))]
		public bool UseTara
		{
			get { return _useTara; }
			set
			{
				if (_useTara == value)
				{
					return;
				}
				_useTara = value;
				OnPropertyChanged();
			}
		}

		IScaleResolutionSettings IScaleResolutionSettingsProvider.ScaleResolutionSettings => ScaleResolutionSettings;
	}
}
