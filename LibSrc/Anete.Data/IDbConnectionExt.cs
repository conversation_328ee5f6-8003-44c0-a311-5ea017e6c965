using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;

namespace Anete.Data
{
	/// <summary>
	/// Extension metody pro ISession
	/// </summary>
	public static class IDbConnectionExt
	{
		
		/// <summary>
		/// Vytvoreni SqlCommand. IDbConnection ve skutecnosti vytvari SqlCommand, jen vystupuje pod IDbCommand. Toto rozhrani
		/// bohuzel neposkytuje sikovne metody pro praci s parametry, proto pretypujeme na SqlCommand
		/// </summary>
		/// <param name="connection">S<PERSON>jeni</param>
		/// <returns></returns>
		public static SqlCommand CreateSqlCommand(this IDbConnection connection)
		{
			return (SqlCommand)connection.CreateCommand();
		}

		public static SqlConnection AsSqlConnection(this IDbConnection connection)
		{
			return (SqlConnection)connection;
		}
	}
}
