using Anete.Config.Core.Attributes;
using Anete.Config.Core.Categories;
using Anete.Config.Core.Internals;
using System;
using System.ComponentModel;

namespace Anete.Config.Configs.Core.Global.Rules
{
    /// <summary>
    /// Konfigurace inkasa
    /// </summary>
    [GlobalRulesConfigDBKey("Collection")]
    [ConfigVersion("*******")]
    [ConfigClassSRDescription(typeof(GlobalRulesCollectionConfig))]
    [DefaultConfigInstance(true)]
    [OfficeCategory(CategoryLevel.Advanced)]
    public class GlobalRulesCollectionConfig : ConfigBase
    {        
        private decimal _collectionMaxValue = 500;
        /// <summary>
        /// Maximalni mozna castka, na kterou se vystavuje inkaso
        /// </summary>
        [DefaultValue(typeof(decimal), "500")]
        [ConfigSRDisplayName("CollectionMaxValue", typeof(GlobalRulesCollectionConfig))]
        [ConfigSRDescription("CollectionMaxValue", typeof(GlobalRulesCollectionConfig))]
        public decimal CollectionMaxValue
        {
            get { return _collectionMaxValue; }
            set
            {
                if (_collectionMaxValue == value)
                {
                    return;
                }
                _collectionMaxValue = value;
                OnPropertyChanged();
            }
        }

        private const bool _collectionLimitsEnabledDefValue = false;
        private bool _collectionLimitsEnabled = _collectionLimitsEnabledDefValue;
        /// <summary>
        /// Povoleni limitu inkasa. Pojmenovano obecne, protoze si umim predstavit i limit pro minimalni castku.
        /// </summary>
        [DefaultValue(_collectionLimitsEnabledDefValue)]
        [ConfigSRDisplayName("CollectionLimitsEnabled", typeof(GlobalRulesCollectionConfig))]
        [ConfigSRDescription("CollectionLimitsEnabled", typeof(GlobalRulesCollectionConfig))]              
        public bool CollectionLimitsEnabled
        {
            get
            {
                return _collectionLimitsEnabled;
            }
            set
            {
                if (_collectionLimitsEnabled == value)
                {
                    return;
                }
                _collectionLimitsEnabled = value;
                OnPropertyChanged();
            }
        }
    }
}
