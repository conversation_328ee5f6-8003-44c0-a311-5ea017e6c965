//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON>ti<PERSON> Developer tool using NHibernate Fluent Mapping template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;
using FluentNHibernate.Mapping;
using FluentNHibernate.MappingModel.Collections;

namespace Anete.Common.Data.Nh.Entities
{
    /// <summary>
    /// There are no comments for UsVylukaPokojuMap in the schema.
    /// </summary>
    public partial class UsVylukaPokojuMap : ClassMap<UsVylukaPokoju>
    {
        /// <summary>
        /// There are no comments for UsVylukaPokojuMap constructor in the schema.
        /// </summary>
        public UsVylukaPokojuMap()
        {
              Schema(@"us");
              Table(@"VylukaPokoju");
              LazyLoad();
              Id(x => x.Id)
                .Column("id")
                .CustomType("Int16")
                .Access.Property().CustomSqlType("smallint")
                .Not.Nullable()
                .Precision(5)                
                .GeneratedBy.Identity();
              Map(x => x.DatumOd)    
                .Column("datumOd")
                .CustomType("DateTime")
                .Access.Property()
                .Generated.Never().CustomSqlType("smalldatetime")
                .Not.Nullable();
              Map(x => x.DatumDo)    
                .Column("datumDo")
                .CustomType("DateTime")
                .Access.Property()
                .Generated.Never().CustomSqlType("smalldatetime")
                .Not.Nullable();
              Map(x => x.Nazev)    
                .Column("nazev")
                .CustomType("String")
                .Access.Property()
                .Generated.Never().CustomSqlType("varchar(50)")
                .Not.Nullable()
                .Length(50);
              Map(x => x.Popis)    
                .Column("popis")
                .CustomType("String")
                .Access.Property()
                .Generated.Never().CustomSqlType("varchar(100)")
                .Length(100);
              HasMany<UsPokojVylukaPokoju>(x => x.UsPokojVylukaPokojus)
                .Access.Property()
                .AsSet()
                .Cascade.None()
                .LazyLoad()
                // .OptimisticLock.Version() /*bug (or missing feature) in Fluent NHibernate*/
                .Inverse()
                .Generic()
                .KeyColumns.Add("idVylukaPokoju", mapping => mapping.Name("idVylukaPokoju")
                                                                     .SqlType("smallint")
                                                                     .Not.Nullable());
              ExtendMapping();
        }

        #region Partial Methods

        partial void ExtendMapping();

        #endregion
    }

}
