using System;
using System.Collections;
using Anete.Utils.AppServices.TypeAdapterLocators;
using Anete.Utils.ComponentModel;
using Anete.Utils.ComponentModel.ComponentPersistence.Adapters;
using Anete.Utils.Reflection.ClassReports.Services;

namespace Anete.Utils.Reflection.ClassReports.Adapters.ValueConverters

{
	/// <summary>
	/// Registrace vsech adapteru, ktere jsou pritomny v tomto namespace
	/// </summary>
	public class AdapterRegistrator : AdapterRegistratorBase<IPropertyReportValueConverterLocator>
	{

		/// <summary>
		/// Vytvori novou instanci
		/// </summary>
		public AdapterRegistrator(IPropertyReportValueConverterLocator locatorService)
			: base(locatorService)
		{

		}

		/// <summary>
		/// Registrace vsech adapteru, ktere jsou pritomny v tomto namespace
		/// </summary>
		public override void Register()
		{
			LocatorService.RegisterAdapter<object, DefaultPropertyReportValueConverter>();
			// Registrace DefaultPropertyReportValueConverter je zde proto, aby se string nevyhodnotil pomoci adapteru pro
			// IEnumerable
			LocatorService.RegisterAdapter<string, DefaultPropertyReportValueConverter>();
			LocatorService.RegisterAdapter<bool, BoolPropertyReportValueConverter>();
			LocatorService.RegisterAdapter<Enum, EnumPropertyReportValueConverter>();
			LocatorService.RegisterAdapter<IToReportString, IToReportStringPropertyReportValueConverter>();
			LocatorService.RegisterAdapter<IEnumerable, IEnumerablePropertyReportValueConverter>();
		}
	}
}
