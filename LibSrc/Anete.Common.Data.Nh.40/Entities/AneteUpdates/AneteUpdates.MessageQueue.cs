//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Anete.Common.Data.Nh.Entities.AneteUpdates
{

    /// <summary>
    /// There are no comments for Anete.Common.Data.Nh.Entities.AneteUpdates.MessageQueue, Anete.Common.Data.Nh.40 in the schema.
    /// </summary>
    public partial class MessageQueue : Anete.Data.Nh.NhEntityBase {
    
        #region Extensibility Method Definitions
        
        /// <summary>
        /// There are no comments for OnCreated in the schema.
        /// </summary>
        partial void OnCreated();
        
        #endregion
        /// <summary>
        /// There are no comments for MessageQueue constructor in the schema.
        /// </summary>
        public MessageQueue()
        {
            OnCreated();
        }

    
        /// <summary>
        /// There are no comments for MessageQueueId in the schema.
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("MessageQueueId", typeof(MessageQueueSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        public virtual System.Guid MessageQueueId
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for IdPocitac in the schema.
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("IdPocitac", typeof(MessageQueueSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual System.Guid IdPocitac
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for IdZarizeni in the schema.
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("IdZarizeni", typeof(MessageQueueSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual int IdZarizeni
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for IdFunkceZarizeni in the schema.
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("IdFunkceZarizeni", typeof(MessageQueueSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual int IdFunkceZarizeni
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Kategorie in the schema.
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Kategorie", typeof(MessageQueueSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual int Kategorie
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Subjekt in the schema.
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Subjekt", typeof(MessageQueueSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual string Subjekt
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for ZpravaUzivatel in the schema.
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("ZpravaUzivatel", typeof(MessageQueueSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual string ZpravaUzivatel
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for ZpravaSystem in the schema.
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("ZpravaSystem", typeof(MessageQueueSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual string ZpravaSystem
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Adresat in the schema.
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Adresat", typeof(MessageQueueSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual string Adresat
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for CasVytvoreni in the schema.
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("CasVytvoreni", typeof(MessageQueueSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual System.DateTime CasVytvoreni
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Precteno in the schema.
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Precteno", typeof(MessageQueueSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual bool Precteno
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for CasPrecteni in the schema.
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("CasPrecteni", typeof(MessageQueueSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual System.Nullable<System.DateTime> CasPrecteni
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Odeslano in the schema.
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Odeslano", typeof(MessageQueueSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual bool Odeslano
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for CasOdeslani in the schema.
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("CasOdeslani", typeof(MessageQueueSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual System.Nullable<System.DateTime> CasOdeslani
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for ObsahujeZastupce in the schema.
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("ObsahujeZastupce", typeof(MessageQueueSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual bool ObsahujeZastupce
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for ZpravaXml in the schema.
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("ZpravaXml", typeof(MessageQueueSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual string ZpravaXml
        {
            get;
            set;
        }
    }

}
