using Anete.Common.Core.Entities;
using Anete.Common.Data.Interface.Entities;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Anete.Common.Data.AccessRights.Parameters.Types
{
	public class RecipeBookParameterRepository : AccessRightParameterEntityRepositoryBase<RecipeBook, RecipeBookKey>
	{
		#region private fields...
		private readonly IRecipeBookRepository _recipeBookRepository;
		#endregion

		#region constructors...		
		public RecipeBookParameterRepository(IRecipeBookRepository recipeBookRepository)
		{
			_recipeBookRepository = recipeBookRepository;
		}
		#endregion

		#region protected overrides...	
		protected override IEnumerable<RecipeBook> GetAllParameterValuesInt()
		{
			return _recipeBookRepository.GetAll();
		}

		protected override RecipeBook GetByIdInt(RecipeBookKey id)
		{
			return _recipeBookRepository.GetById(id.Id);
		}
		#endregion
	}
}
