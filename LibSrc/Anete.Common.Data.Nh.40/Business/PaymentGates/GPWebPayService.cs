using Anete.Common.Core.Interface.AppServices;
using Anete.Common.Core.Interface.Enums;
using Anete.Common.Data.Nh.Business.PaymentGates.GPWebPayWS;
using Anete.Common.Data.Nh.Business.WebKredit.Account;
using Anete.Common.Data.Nh.Entities;
using Anete.Common.Data.Nh.Interface.Services;
using Anete.Config.Configs.Core.Global.Services;
using Anete.Config.Core;
using Anete.Log4Net.Core;
using Anete.Utils;
using Anete.Utils.Extensions;
using NHibernate;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.ServiceModel;
using System.Xml;

namespace Anete.Common.Data.Nh.Business.PaymentGates
{
	public class GPWebPayService : BasePaymentGateService
	{
		private static readonly ILogEx _log = LogManagerEx.GetLogger(typeof(GPWebPayService));
		private const X509KeyStorageFlags _keyFlags = X509KeyStorageFlags.MachineKeySet | X509KeyStorageFlags.PersistKeySet | X509KeyStorageFlags.Exportable;
		private readonly ISystemCurrencyProvider _systemCurrencyProvider;

		private readonly GPWebPayPaymentGateSettings _settings;

		public GPWebPayService(IAccountDataLayer dataLayer, IConfigManager configManager, IAppInstallationIdProvider appInstallationIdProvider, ISystemCurrencyProvider systemCurrencyProvider)
			: base(dataLayer, configManager, appInstallationIdProvider)
		{
			_settings = (GPWebPayPaymentGateSettings)configManager.GetConfig<GlobalServicesPaymentGateConfig>().Settings;
			_systemCurrencyProvider = systemCurrencyProvider;
		}

		protected override NewPaymentModel CreatePaymentInt(ISession session, int clientId, string email, string name, int amount, ApplicationLanguage language, string returnUrl, string ipAddress)
		{
			var currency = _systemCurrencyProvider.Get(session);
		
			var request = new GPWebPayRequestModel
			{
				MerchantNumber = _settings.ClientID,
				OrderNumber = (int)(DateTime.UtcNow.Subtract(new DateTime(2019, 1, 1))).TotalSeconds,
				Amount = amount,
				Url = returnUrl,
				Currency = currency.Kod == "CZK" ? GPWebPayCurrencyCode.Czk : GPWebPayCurrencyCode.Eur,
				DepositFlag = 1,
				PaymentMethod = GPWebPayPaymentMethod.Crd,
				DisabledPaymentMethod = GPWebPayPaymentMethod.Crd,
				Email = email,
				ReferenceNumber = clientId.ToString(),
				Lang = language.ToUICultureInfo().TwoLetterISOLanguageName.ToUpper(),
				AddInfo = GetAddInfo(name, email)
			};
			//request.Url = returnUrl + "/"; tady toto není uplně šťastné řešení na tvrdo přidávat "/" pokud bude chyba opravit již při volání této fce

			var parameters = GetParametersForDigestCalculation(request);
			var message = string.Join("|", parameters.Select(x => x.Value));
			var digest = SignData(message, _settings.CertificatePrivatePath, _settings.CertificatePrivatePasword);
			ValidateDigest(digest, message, _settings.CertificatePrivatePath, _settings.CertificatePrivatePasword);

			parameters.Add(new KeyValuePair<string, string>("DIGEST", digest));
			var args = string.Join("&", parameters.Select(x => $"{Uri.EscapeDataString(x.Key)}={Uri.EscapeDataString(x.Value)}"));
			args = args + "&LANG=" + request.Lang;

			return new NewPaymentModel
			{
				Id = request.OrderNumber,
				Amount = amount,
				Url = $"{_settings.APIUrl}?{args}"
			};
		}

		private XmlElement GetAddInfo(string name, string email)
		{
			var doc = new XmlDocument();

			var nameElement = doc.CreateElement("name");
			nameElement.InnerText = name.RemoveDiacritics();

			var cardholderDetailsElement = doc.CreateElement("cardholderDetails");
			cardholderDetailsElement.AppendChild(nameElement);

			if (email != null)
			{
				var emailElement = doc.CreateElement("email");
				emailElement.InnerText = email;
				cardholderDetailsElement.AppendChild(emailElement);
			}
			
			var cardHolderInfoElement = doc.CreateElement("cardholderInfo");
			cardHolderInfoElement.AppendChild(cardholderDetailsElement);

			var additionalInfoRequestElement = doc.CreateElement("additionalInfoRequest");
			additionalInfoRequestElement.AppendChild(cardHolderInfoElement);
			additionalInfoRequestElement.SetAttribute("version", "5.0");
			additionalInfoRequestElement.SetAttribute("xmlns:xs", "http://www.w3.org/2001/XMLSchema");
			additionalInfoRequestElement.SetAttribute("xmlns", "http://gpe.cz/gpwebpay/additionalInfo/request");

			doc.AppendChild(additionalInfoRequestElement);

			return doc.DocumentElement;
		}

		private string SignData(string message, string certificateFile, string password)
		{
			_log.Debug($"SignData: message: {message}, certificateFile: {certificateFile}");

			var certificate = new X509Certificate2(certificateFile, password, _keyFlags);

			var data = System.Text.Encoding.GetEncoding(1250).GetBytes(message);
			var sha = SHA1.Create();
			var hash = sha.ComputeHash(data);

			using (var rsa = certificate.GetRSAPrivateKey())
			{
				if (rsa == null)
				{
					throw new Exception("Invalid certificate: no private key found");
				}

				var digestHash = rsa.SignHash(hash, HashAlgorithmName.SHA1, RSASignaturePadding.Pkcs1);
				return Convert.ToBase64String(digestHash);
			}
		}

		private void ValidateDigest(string digest, string message, string certificateFile, string password = null)
		{
			_log.Debug($"ValidateDigest: digest: {digest}, message: {message}, certificateFile: {certificateFile}");

			var certificate = new X509Certificate2(certificateFile, password, _keyFlags);

			var digestHash = Convert.FromBase64String(digest);
			var data = System.Text.Encoding.GetEncoding(1250).GetBytes(message);
			var sha = SHA1.Create();
			var hash = sha.ComputeHash(data);

			using (var rsa = certificate.GetRSAPublicKey())
			{
				if (rsa == null)
				{
					throw new Exception("Invalid certificate: no public key found");
				}

				if (!rsa.VerifyHash(hash, digestHash, HashAlgorithmName.SHA1, RSASignaturePadding.Pkcs1))
				{
					throw new Exception($"Invalid digest: {digest}");
				}
			}
		}

		private IList<KeyValuePair<string, string>> GetParametersForDigestCalculation(GPWebPayRequestModel request)
		{
			var parameters = new List<KeyValuePair<string, string>>
			{
				new KeyValuePair<string, string>("MERCHANTNUMBER", request.MerchantNumber),
				new KeyValuePair<string, string>("OPERATION", "CREATE_ORDER"),
				new KeyValuePair<string, string>("ORDERNUMBER", request.OrderNumber.ToString()),
				new KeyValuePair<string, string>("AMOUNT", request.Amount.ToString(CultureInfo.InvariantCulture)),
				new KeyValuePair<string, string>("CURRENCY", ((int)request.Currency).ToString()),
				new KeyValuePair<string, string>("DEPOSITFLAG", request.DepositFlag.ToString())
			};

			if (request.MerOrderNumber != null)
			{
				parameters.Add(new KeyValuePair<string, string>("MERORDERNUM", request.MerOrderNumber));
			}

			parameters.Add(new KeyValuePair<string, string>("URL", request.Url));

			if (request.Description != null)
			{
				parameters.Add(new KeyValuePair<string, string>("DESCRIPTION", request.Description));
			}

			if (request.MD != null)
			{
				parameters.Add(new KeyValuePair<string, string>("MD", request.MD));
			}

			if (request.PaymentMethod != GPWebPayPaymentMethod.NotSet)
			{
				parameters.Add(new KeyValuePair<string, string>("PAYMETHOD", EnumUtils.GetName(request.PaymentMethod)));
			}

			if (request.DisabledPaymentMethod != GPWebPayPaymentMethod.NotSet)
			{
				parameters.Add(new KeyValuePair<string, string>("DISABLEPAYMETHOD", EnumUtils.GetName(request.DisabledPaymentMethod)));
			}

			if (request.PaymentMethods != null && request.PaymentMethods.Length > 0)
			{
				parameters.Add(new KeyValuePair<string, string>("PAYMETHODS", string.Join(",", request.PaymentMethods.Select(x => EnumUtils.GetName(x)))));
			}

			if (request.Email != null)
			{
				parameters.Add(new KeyValuePair<string, string>("EMAIL", request.Email));
			}

			if (request.ReferenceNumber != null)
			{
				parameters.Add(new KeyValuePair<string, string>("REFERENCENUMBER", request.ReferenceNumber));
			}

			if (request.AddInfo != null)
			{
				parameters.Add(new KeyValuePair<string, string>("ADDINFO", request.AddInfo.OuterXml));
			}

			return parameters;
		}

		public override long ProcessRedirect(object model)
		{
			var payment = (GPWebPayResponseModel)model;
			if (payment.PRCode != 50 && (payment.PRCode != 0 || payment.SRCode != 0))
			{
				throw new Exception($"PRCode: {payment.PRCode}, SRCode: {payment.SRCode}");
			}
			return payment.OrderNumber;
		}

		protected override PaymentModel PaymentStatusInt(ISession session, long paymentId, int clientId, string userName, WebPayPendingTransaction pending, DateTime? deposited)
		{
			try
			{
				var response = PaymentStatusResponse(paymentId);
				Guard.ArgumentIsGreatherThanZero(response.paymentAmount, nameof(response.paymentAmount));

				return new PaymentModel
				{
					PaymentId = paymentId,
					ClientId = clientId,
					Amount = response.paymentAmount,
					Created = DateTime.TryParse(response.paymentTime, out var created) ? created : pending?.Tstamp ?? deposited ?? default,
					Deposited = deposited,

					State = response.status,
					SubState = response.subStatus
				};
			}
			catch (FaultException<FaultDetail> ex) when (ex.Detail?.primaryReturnCode == 15 && ex.Detail?.secondaryReturnCode == 1)//15=object not found, 1=ORDERNUMBER
			{
				return new PaymentModel
				{
					PaymentId = paymentId,
					ClientId = clientId,
					Created = pending?.Tstamp ?? deposited ?? default,
					Deposited = deposited,
					State = "UNPAID"
				};
			}
		}

		private PaymentDetailResponse PaymentStatusResponse(long paymentId)
		{
			var request = new PaymentStatusRequest
			{
				messageId = Guid.NewGuid().ToString().Replace("-", string.Empty),
				provider = _settings.ProviderID,
				merchantNumber = _settings.ClientID,
				paymentNumber = paymentId.ToString()
			};
			var parameters = new List<KeyValuePair<string, string>>
			{
				new KeyValuePair<string, string>("messageId", request.messageId),
				new KeyValuePair<string, string>("provider", request.provider),
				new KeyValuePair<string, string>("merchantNumber", request.merchantNumber),
				new KeyValuePair<string, string>("paymentNumber", request.paymentNumber),
			};
			var message = string.Join("|", parameters.Select(x => x.Value));
			var digest = SignData(message, _settings.CertificatePrivatePath, _settings.CertificatePrivatePasword);
			ValidateDigest(digest, message, _settings.CertificatePrivatePath, _settings.CertificatePrivatePasword);
			request.signature = Convert.FromBase64String(digest);

			var binding = new BasicHttpBinding(BasicHttpSecurityMode.Transport);
			var address = new EndpointAddress(_settings.ServicesUrl);
			var client = new PaymentPortClient(binding, address);
			return client.getPaymentDetail(request);
		}

		protected override DepositResult GetDepositResult(ISession session, PaymentModel payment, string userName)
		{
			if (payment.Deposited != null)
			{
				return DepositResult.AlreadyDeposited;
			}
			if (payment.State == "UNPAID")
			{
				return DepositResult.Canceled;
			}
			if (payment.State == "CAPTURED")
			{
				return DepositResult.Sucessful;
			}
			return DepositResult.PaymentNotFinished;
		}
	}
}