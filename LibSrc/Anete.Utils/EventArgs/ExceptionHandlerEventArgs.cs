using System;

namespace Anete.Utils.EventArgs
{
	
	/// <summary>
	/// argumenty pro predavani udalosti ErrorHandlerum
	/// </summary>
	/// <remarks></remarks>
	public class ExceptionHandlerEventArgs : System.EventArgs
	{

		#region constructors
		public ExceptionHandlerEventArgs(Exception exceptionObject)
		{
			ExceptionObject = exceptionObject;
		}
		#endregion

		#region public properties...
		/// <summary>
		/// pripojena vyjimka
		/// </summary>
		public Exception ExceptionObject { get; set; }

		/// <summary>
		/// EventHandler musi nastavit na true, pokud je vyjimka vyrizena
		/// </summary>
		public bool IsHandled { get; set; }
		#endregion

	}
	
}

