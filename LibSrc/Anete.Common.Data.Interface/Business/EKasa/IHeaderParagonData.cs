using System;
using System.Linq;

namespace Anete.Common.Data.Interface.Business.EKasa
{
	public interface IHeaderParagonData
	{
		/// <summary>
		/// Identifikator kasy
		/// </summary>
		string AppInstallationDesc { get;  }
		/// <summary>
		/// Id zarizeni
		/// </summary>
		short AppInstallationId { get; }
		/// <summary>
		/// Datum uskutecneni zdanitelneho plneni
		/// </summary>
		DateTime DatumUzp { get; }
		/// <summary>
		/// DIC - pouziva se bud pro fiskal nebo Eet
		/// </summary>
		string Dic { get;  }
		/// <summary>
		/// DKP pro fiskál
		/// </summary>
		string Dkp { get;  }
		/// <summary>
		/// Id paragonu
		/// </summary>
		int IdParagon { get;  }

		/// <summary>
		/// Id ucetniho obdobi
		/// </summary>
		short IdUop { get;  }
		/// <summary>
		/// Cislo uctenky zformatovane z IdUop, IdParagon a AppInstallationId
		/// </summary>
		string ParagonNumber { get; }

		string OriginalParagonNumberIfItsStorno { get; }

		/// <summary>
		/// Osluha
		/// </summary>
		string Pokladni { get;  }

		/// <summary>
		/// Stůl/židle ve tvaru A/2
		/// </summary>
		string TableChair { get; }
		/// <summary>
		/// Cas prodeje
		/// </summary>
		DateTime SaleDateTime { get;  }
		/// <summary>
		/// Platce Dph
		/// </summary>
		bool VatPayer { get;  }
	}
}
