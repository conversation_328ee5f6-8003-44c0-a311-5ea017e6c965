using Anete.Utils;
using Anete.Utils.ComponentModel;
using Anete.Utils.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Anete.Common.Core.Business.InstallationParameters.Parameters.System.DeviceControllerTypes
{
	public class DeviceControllerTypesParameterValue : InstallationParameterValueBase
	{
		public DeviceControllerTypesParameterValue(DeviceControllerTypeParameterItem[] deviceControllerTypes)
		{
			Guard.ArgumentNotNull(deviceControllerTypes, nameof(deviceControllerTypes));

			DeviceControllerTypes = deviceControllerTypes;
		}

		public DeviceControllerTypeParameterItem[] DeviceControllerTypes { get; }

		public override int GetHashCode()
		{
			return HashCodeUtils.GetHashCode(DeviceControllerTypes.Select(dct => dct.GetHashCode()));
		}

		public override bool Equals(object obj)
		{
			DeviceControllerTypesParameterValue other = obj as DeviceControllerTypesParameterValue;
			if (other == null)
			{
				return false;
			}

			bool sameCollection = other.DeviceControllerTypes.HasSameValues(DeviceControllerTypes, new EqualsEqualityComparer<DeviceControllerTypeParameterItem>());
			return sameCollection;
		}

		protected override string GetTextValue()
		{
			return DeviceControllerTypes.Select(dct => dct.TextRepresentation).Where(s => !string.IsNullOrWhiteSpace(s)).ToCommaSpaceDelimitedString();
		}
	}
}
