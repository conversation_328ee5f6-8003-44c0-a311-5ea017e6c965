using System.Threading;
using System.Threading.Tasks;

namespace Anete.Utils.ComponentModel.Commands
{
	/// <summary>
	/// Handler pro libovolný požadavek (query nebo command) na API.
	/// </summary>
	/// <typeparam name="TRequest">Specifikuje typ požadavku.</typeparam>
	/// <typeparam name="TResponse">Typ návratové hodnoty (typicky nějaký DTO pro query a primitivní typ (int, guid,…) pro command).</typeparam>
	public interface IRequestHandler<TRequest, TResponse>
	{
		TResponse Handle(TRequest request);
	}

	public interface IRequestHandler<TRequest>
	{
		void Handle(TRequest request);
	}

	public interface IRequestHandlerAsync<TRequest, TResponse>
	{
		Task<TResponse> HandleAsync(TRequest request, CancellationToken cancellationToken = default);
	}

	public interface IRequestHandlerAsync<TRequest>
	{
		Task HandleAsync(TRequest request, CancellationToken cancellationToken = default);
	}
}