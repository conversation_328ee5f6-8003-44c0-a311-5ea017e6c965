//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Anete.Common.Data.Nh.Entities {
    using System;
    
    
    /// <summary>
    /// A strongly-typed resource class, for looking up localized strings, formatting them, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilderEx class via the InternalResXFileCodeGeneratorEx custom tool.
    // To add or remove a member, edit your .ResX file then rerun the InternalResXFileCodeGeneratorEx custom tool or rebuild your VS.NET project.
    // Copyright (c) Dmytro <PERSON> 2006-2018 (http://dmytro.kryvko.googlepages.com/)
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("DMKSoftware.CodeGenerators.Tools.StronglyTypedResourceBuilderEx", "2.6.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Naming", "CA1724:TypeNamesShouldNotMatchNamespaces")]
    internal partial class CashDeskPaymentOrderSR {
        
        private static global::System.Resources.ResourceManager _resourceManager;
        
        private static object _internalSyncObject;
        
        private static global::System.Globalization.CultureInfo _resourceCulture;
        
        /// <summary>
        /// Initializes a CashDeskPaymentOrderSR object.
        /// </summary>
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal CashDeskPaymentOrderSR() {
        }
        
        /// <summary>
        /// Thread safe lock object used by this class.
        /// </summary>
        internal static object InternalSyncObject {
            get {
                if (object.ReferenceEquals(_internalSyncObject, null)) {
                    global::System.Threading.Interlocked.CompareExchange(ref _internalSyncObject, new object(), null);
                }
                return _internalSyncObject;
            }
        }
        
        /// <summary>
        /// Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(_resourceManager, null)) {
                    global::System.Threading.Monitor.Enter(InternalSyncObject);
                    try {
                        if (object.ReferenceEquals(_resourceManager, null)) {
                            global::System.Threading.Interlocked.Exchange(ref _resourceManager, new global::System.Resources.ResourceManager("Anete.Common.Data.Nh.Entities.CashDeskPaymentOrderSR", typeof(CashDeskPaymentOrderSR).Assembly));
                        }
                    }
                    finally {
                        global::System.Threading.Monitor.Exit(InternalSyncObject);
                    }
                }
                return _resourceManager;
            }
        }
        
        /// <summary>
        /// Overrides the current thread's CurrentUICulture property for all
        /// resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return _resourceCulture;
            }
            set {
                _resourceCulture = value;
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Zařízení'.
        /// </summary>
        internal static string AppInstallation {
            get {
                return ResourceManager.GetString(ResourceNames.AppInstallation, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Id zařízení'.
        /// </summary>
        internal static string AppInstallationId {
            get {
                return ResourceManager.GetString(ResourceNames.AppInstallationId, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Kód'.
        /// </summary>
        internal static string Code {
            get {
                return ResourceManager.GetString(ResourceNames.Code, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Pořadí'.
        /// </summary>
        internal static string Order {
            get {
                return ResourceManager.GetString(ResourceNames.Order, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Typ úhrady'.
        /// </summary>
        internal static string PaymentType {
            get {
                return ResourceManager.GetString(ResourceNames.PaymentType, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Zobrazit'.
        /// </summary>
        internal static string Visible {
            get {
                return ResourceManager.GetString(ResourceNames.Visible, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Lists all the resource names as constant string fields.
        /// </summary>
        internal class ResourceNames {
            
            /// <summary>
            /// Stores the resource name 'AppInstallation'.
            /// </summary>
            internal const string AppInstallation = "AppInstallation";
            
            /// <summary>
            /// Stores the resource name 'AppInstallationId'.
            /// </summary>
            internal const string AppInstallationId = "AppInstallationId";
            
            /// <summary>
            /// Stores the resource name 'Code'.
            /// </summary>
            internal const string Code = "Code";
            
            /// <summary>
            /// Stores the resource name 'Order'.
            /// </summary>
            internal const string Order = "Order";
            
            /// <summary>
            /// Stores the resource name 'PaymentType'.
            /// </summary>
            internal const string PaymentType = "PaymentType";
            
            /// <summary>
            /// Stores the resource name 'Visible'.
            /// </summary>
            internal const string Visible = "Visible";
        }
    }
}
