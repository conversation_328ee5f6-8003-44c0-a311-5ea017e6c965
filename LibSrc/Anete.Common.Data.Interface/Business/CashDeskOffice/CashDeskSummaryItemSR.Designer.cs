//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Anete.Common.Data.Interface.Business.CashDeskOffice {
    using System;
    
    
    /// <summary>
    /// A strongly-typed resource class, for looking up localized strings, formatting them, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilderEx class via the ResXFileCodeGeneratorEx custom tool.
    // To add or remove a member, edit your .ResX file then rerun the ResXFileCodeGeneratorEx custom tool or rebuild your VS.NET project.
    // Copyright (c) Dmytro Kryvko 2006-2018 (http://dmytro.kryvko.googlepages.com/)
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("DMKSoftware.CodeGenerators.Tools.StronglyTypedResourceBuilderEx", "2.6.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Naming", "CA1724:TypeNamesShouldNotMatchNamespaces")]
    public partial class CashDeskSummaryItemSR {
        
        private static global::System.Resources.ResourceManager _resourceManager;
        
        private static object _internalSyncObject;
        
        private static global::System.Globalization.CultureInfo _resourceCulture;
        
        /// <summary>
        /// Initializes a CashDeskSummaryItemSR object.
        /// </summary>
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        public CashDeskSummaryItemSR() {
        }
        
        /// <summary>
        /// Thread safe lock object used by this class.
        /// </summary>
        public static object InternalSyncObject {
            get {
                if (object.ReferenceEquals(_internalSyncObject, null)) {
                    global::System.Threading.Interlocked.CompareExchange(ref _internalSyncObject, new object(), null);
                }
                return _internalSyncObject;
            }
        }
        
        /// <summary>
        /// Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(_resourceManager, null)) {
                    global::System.Threading.Monitor.Enter(InternalSyncObject);
                    try {
                        if (object.ReferenceEquals(_resourceManager, null)) {
                            global::System.Threading.Interlocked.Exchange(ref _resourceManager, new global::System.Resources.ResourceManager("Anete.Common.Data.Interface.Business.CashDeskOffice.CashDeskSummaryItemSR", typeof(CashDeskSummaryItemSR).Assembly));
                        }
                    }
                    finally {
                        global::System.Threading.Monitor.Exit(InternalSyncObject);
                    }
                }
                return _resourceManager;
            }
        }
        
        /// <summary>
        /// Overrides the current thread's CurrentUICulture property for all
        /// resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return _resourceCulture;
            }
            set {
                _resourceCulture = value;
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Datum'.
        /// </summary>
        public static string Datum {
            get {
                return ResourceManager.GetString(ResourceNames.Datum, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Kauce na kartu'.
        /// </summary>
        public static string Dpp51 {
            get {
                return ResourceManager.GetString(ResourceNames.Dpp51, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Vrácení kauce'.
        /// </summary>
        public static string Dpp52 {
            get {
                return ResourceManager.GetString(ResourceNames.Dpp52, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Záloha vklad'.
        /// </summary>
        public static string Dpp53 {
            get {
                return ResourceManager.GetString(ResourceNames.Dpp53, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Záloha výběr'.
        /// </summary>
        public static string Dpp54 {
            get {
                return ResourceManager.GetString(ResourceNames.Dpp54, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Odvod tržby'.
        /// </summary>
        public static string Dpp55 {
            get {
                return ResourceManager.GetString(ResourceNames.Dpp55, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Vklad/výběr'.
        /// </summary>
        public static string Dpp56 {
            get {
                return ResourceManager.GetString(ResourceNames.Dpp56, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Prodej'.
        /// </summary>
        public static string Dpp57 {
            get {
                return ResourceManager.GetString(ResourceNames.Dpp57, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Prodej storno'.
        /// </summary>
        public static string Dpp58 {
            get {
                return ResourceManager.GetString(ResourceNames.Dpp58, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Obrat'.
        /// </summary>
        public static string Obrat {
            get {
                return ResourceManager.GetString(ResourceNames.Obrat, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Pokladní deník'.
        /// </summary>
        public static string PokladniDenikNoData {
            get {
                return ResourceManager.GetString(ResourceNames.PokladniDenikNoData, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Pokladní deník - {0:d}'.
        /// </summary>
        public static string PokladniDenikTitle {
            get {
                return ResourceManager.GetString(ResourceNames.PokladniDenikTitle, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Stav pokladny'.
        /// </summary>
        public static string Prenos {
            get {
                return ResourceManager.GetString(ResourceNames.Prenos, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Převod'.
        /// </summary>
        public static string Prevod {
            get {
                return ResourceManager.GetString(ResourceNames.Prevod, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Součet'.
        /// </summary>
        public static string Soucet {
            get {
                return ResourceManager.GetString(ResourceNames.Soucet, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Pokladní deník - {0:d}'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string PokladniDenikTitleFormat(object arg0) {
            return string.Format(_resourceCulture, PokladniDenikTitle, arg0);
        }
        
        /// <summary>
        /// Lists all the resource names as constant string fields.
        /// </summary>
        public class ResourceNames {
            
            /// <summary>
            /// Stores the resource name 'Datum'.
            /// </summary>
            public const string Datum = "Datum";
            
            /// <summary>
            /// Stores the resource name 'Dpp51'.
            /// </summary>
            public const string Dpp51 = "Dpp51";
            
            /// <summary>
            /// Stores the resource name 'Dpp52'.
            /// </summary>
            public const string Dpp52 = "Dpp52";
            
            /// <summary>
            /// Stores the resource name 'Dpp53'.
            /// </summary>
            public const string Dpp53 = "Dpp53";
            
            /// <summary>
            /// Stores the resource name 'Dpp54'.
            /// </summary>
            public const string Dpp54 = "Dpp54";
            
            /// <summary>
            /// Stores the resource name 'Dpp55'.
            /// </summary>
            public const string Dpp55 = "Dpp55";
            
            /// <summary>
            /// Stores the resource name 'Dpp56'.
            /// </summary>
            public const string Dpp56 = "Dpp56";
            
            /// <summary>
            /// Stores the resource name 'Dpp57'.
            /// </summary>
            public const string Dpp57 = "Dpp57";
            
            /// <summary>
            /// Stores the resource name 'Dpp58'.
            /// </summary>
            public const string Dpp58 = "Dpp58";
            
            /// <summary>
            /// Stores the resource name 'Obrat'.
            /// </summary>
            public const string Obrat = "Obrat";
            
            /// <summary>
            /// Stores the resource name 'PokladniDenikNoData'.
            /// </summary>
            public const string PokladniDenikNoData = "PokladniDenikNoData";
            
            /// <summary>
            /// Stores the resource name 'PokladniDenikTitle'.
            /// </summary>
            public const string PokladniDenikTitle = "PokladniDenikTitle";
            
            /// <summary>
            /// Stores the resource name 'Prenos'.
            /// </summary>
            public const string Prenos = "Prenos";
            
            /// <summary>
            /// Stores the resource name 'Prevod'.
            /// </summary>
            public const string Prevod = "Prevod";
            
            /// <summary>
            /// Stores the resource name 'Soucet'.
            /// </summary>
            public const string Soucet = "Soucet";
        }
    }
}
