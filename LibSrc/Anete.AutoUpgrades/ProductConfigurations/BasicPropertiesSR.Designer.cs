//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Anete.AutoUpgrades.ProductConfigurations {
    using System;
    
    
    /// <summary>
    /// A strongly-typed resource class, for looking up localized strings, formatting them, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilderEx class via the InternalResXFileCodeGeneratorEx custom tool.
    // To add or remove a member, edit your .ResX file then rerun the InternalResXFileCodeGeneratorEx custom tool or rebuild your VS.NET project.
    // Copyright (c) Dmy<PERSON> 2006-2018 (http://dmytro.kryvko.googlepages.com/)
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("DMKSoftware.CodeGenerators.Tools.StronglyTypedResourceBuilderEx", "2.6.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Naming", "CA1724:TypeNamesShouldNotMatchNamespaces")]
    internal partial class BasicPropertiesSR {
        
        private static global::System.Resources.ResourceManager _resourceManager;
        
        private static object _internalSyncObject;
        
        private static global::System.Globalization.CultureInfo _resourceCulture;
        
        /// <summary>
        /// Initializes a BasicPropertiesSR object.
        /// </summary>
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal BasicPropertiesSR() {
        }
        
        /// <summary>
        /// Thread safe lock object used by this class.
        /// </summary>
        internal static object InternalSyncObject {
            get {
                if (object.ReferenceEquals(_internalSyncObject, null)) {
                    global::System.Threading.Interlocked.CompareExchange(ref _internalSyncObject, new object(), null);
                }
                return _internalSyncObject;
            }
        }
        
        /// <summary>
        /// Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(_resourceManager, null)) {
                    global::System.Threading.Monitor.Enter(InternalSyncObject);
                    try {
                        if (object.ReferenceEquals(_resourceManager, null)) {
                            global::System.Threading.Interlocked.Exchange(ref _resourceManager, new global::System.Resources.ResourceManager("Anete.AutoUpgrades.ProductConfigurations.BasicPropertiesSR", typeof(BasicPropertiesSR).Assembly));
                        }
                    }
                    finally {
                        global::System.Threading.Monitor.Exit(InternalSyncObject);
                    }
                }
                return _resourceManager;
            }
        }
        
        /// <summary>
        /// Overrides the current thread's CurrentUICulture property for all
        /// resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return _resourceCulture;
            }
            set {
                _resourceCulture = value;
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Id zařízení'.
        /// </summary>
        internal static string AppInstallationId {
            get {
                return ResourceManager.GetString(ResourceNames.AppInstallationId, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení připojení k DB AneteAutoUpdates'.
        /// </summary>
        internal static string SingleAutoUpdatesDbConnectionConfiguration {
            get {
                return ResourceManager.GetString(ResourceNames.SingleAutoUpdatesDbConnectionConfiguration, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení připojení k DB Kredit'.
        /// </summary>
        internal static string SingleKreditConnectionConfiguration {
            get {
                return ResourceManager.GetString(ResourceNames.SingleKreditConnectionConfiguration, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení přípojení k DB Sklady'.
        /// </summary>
        internal static string SingleSkladyDbConnectionConfiguration {
            get {
                return ResourceManager.GetString(ResourceNames.SingleSkladyDbConnectionConfiguration, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení služby'.
        /// </summary>
        internal static string WindowsServiceConfiguration {
            get {
                return ResourceManager.GetString(ResourceNames.WindowsServiceConfiguration, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Lists all the resource names as constant string fields.
        /// </summary>
        internal class ResourceNames {
            
            /// <summary>
            /// Stores the resource name 'AppInstallationId'.
            /// </summary>
            internal const string AppInstallationId = "AppInstallationId";
            
            /// <summary>
            /// Stores the resource name 'SingleAutoUpdatesDbConnectionConfiguration'.
            /// </summary>
            internal const string SingleAutoUpdatesDbConnectionConfiguration = "SingleAutoUpdatesDbConnectionConfiguration";
            
            /// <summary>
            /// Stores the resource name 'SingleKreditConnectionConfiguration'.
            /// </summary>
            internal const string SingleKreditConnectionConfiguration = "SingleKreditConnectionConfiguration";
            
            /// <summary>
            /// Stores the resource name 'SingleSkladyDbConnectionConfiguration'.
            /// </summary>
            internal const string SingleSkladyDbConnectionConfiguration = "SingleSkladyDbConnectionConfiguration";
            
            /// <summary>
            /// Stores the resource name 'WindowsServiceConfiguration'.
            /// </summary>
            internal const string WindowsServiceConfiguration = "WindowsServiceConfiguration";
        }
    }
}
