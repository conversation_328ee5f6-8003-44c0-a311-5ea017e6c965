using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;

namespace Anete.Common.Core.Interface.Reports.BatchPrint
{

	/// <summary>
	/// Nastaveni pro odeslani pres mail
	/// </summary>
	[DataContract(Namespace = ReportsNamespace.DefaultNamespace)]
	public class SendToMailExportDestSettings : ReportDestSettingsBase
	{
		/// <summary>
		/// Initializes a new instance
		/// </summary>
		public SendToMailExportDestSettings()
		{
			Recipients = new List<string>();
			ExportSettings = new SaveToFileExportDestSettings();
		}

		/// <summary>
		/// Seznam adresatu
		/// </summary>
		[DataMember]
		public List<string> Recipients { get; set; }

		/// <summary>
		/// Nastaveni exportu
		/// </summary>
		[DataMember]
		public ISaveToFileExportDestSettings ExportSettings { get; set; }


	}
}
