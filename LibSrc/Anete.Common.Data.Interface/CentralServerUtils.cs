using System.Data.SqlClient;
using System.Data;
using System.Reflection;
using System;
using System.Collections.Generic;
using System.Linq;
using Anete.Utils.AppServices;
using Anete.Utils;
using Unity;
using Anete.Common.Data.Interface.AppServices;
using Anete.Log.Core.Log4NetProxy;

namespace Anete.Common.Data.Interface
{
    /// <summary>
    /// Metody pro praci s centralnim serverem na replikovanych systemech
    /// 
    /// Zrale na prepsani. V dobe vzniku tridy byly na ni zcela jine pozadavky, nez ted. Nove potreba specificky pouzivat/nepouzivat server kontext
    /// pro ruzne databaze. Navic ruzne chovani pro ruzne aplikace -> vhodne resit servisem
    /// </summary>
    public static class CentralServerUtils
    {
        // seznam connection stringu, ktere nebudou nikdy vyuzivat centralni server
        // vytvoreno kvuli potrebe vracet prazdny server context pro specificke connection stringy
        private readonly static List<string> _notUseCentralServerConnectionString = new List<string>();

        private static readonly ILogEx _log = LogManagerEx.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        #region public properties...                
        private static string _kreditServerContext;
        /// <summary>
        /// Vraci ServerContext pro databazi Kredit nebo prazdny retezec v zavislosti na tom, zda se nachazim na centralnim nebo
        /// replikovanem server a take na tom, zda je povoleno pouziti server kontextu.
        /// </summary>
        public static string KreditServerContextOrEmptyString
        {
            get
            {
                if (!IsCentralServer && UseKreditServerContext)
                {
                    if (_kreditServerContext == null)
                    {
                        SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(
							DependencyContainer.Instance.Resolve<IKreditDbConnectionProvider>().ConnectionString);
                        _kreditServerContext = ServerContext.Replace("%catalog%", builder.InitialCatalog);
                    }
                    return _kreditServerContext;
                }
                else
                {
                    return "";
                }
            }
        }

        private static bool _useKreditServerContext = false;
        /// <summary>
        /// Bude se pouzivat KreditServerContext? Jeho vyuziti ma smysl pouze u aplikaci, ktere ukladaji konfiguraci,
        /// ktera se musi ulozit na centralni server. V teto chvili se jedna pouze o konfigurator.
        /// </summary>
        public static bool UseKreditServerContext
        {
            get
            {
                return _useKreditServerContext;
            }
            set
            {
                _useKreditServerContext = value;
            }
        }

        
        private static bool? _isCentralServer = null;
        /// <summary>
        /// Gets a value indicating whether this instance is central server.
        /// </summary>
        /// <value>
        /// 	<c>true</c> if this instance is central server; otherwise, <c>false</c>.
        /// </value>
        public static bool IsCentralServer
        {
            get
            {
                if (_isCentralServer == null)
                {
                    LoadServerContext();
                }
                return (bool)_isCentralServer;
            }
        }

		private static bool? _isSkladyCentralServer = null;
		/// <summary>
		/// Gets a value indicating whether this instance is central server.
		/// </summary>
		/// <value>
		/// 	<c>true</c> if this instance is central server; otherwise, <c>false</c>.
		/// </value>
		public static bool IsSkladyCentralServer
		{
			get
			{
				if (_isSkladyCentralServer == null)
				{
					LoadSkladyServerContext();
				}
				return (bool)_isSkladyCentralServer;
			}
		}

        private static bool? _isReplicatedServer = null;
        /// <summary>
        /// Jedna se o replikovany system? Tzn. zda v databazi existuje vice serveru.
        /// </summary>
        public static bool IsReplicatedServer
        {
            get
            {
               if (_isReplicatedServer == null)
               {
                   LoadServerContext();
               }
               return (bool)_isReplicatedServer;
            }
        }

		private static bool? _isSkladyReplicatedServer = null;
        /// <summary>
        /// Jedna se o replikovany system? Tzn. zda v databazi existuje vice serveru.
        /// </summary>
        public static bool IsSkladyReplicatedServer
        {
            get
            {
				if (_isSkladyReplicatedServer == null)
               {
                   LoadSkladyServerContext();
               }
				return (bool)_isSkladyReplicatedServer;
            }
        }
		#endregion

        #region public methods...                
        /// <summary>
        /// Vraci server context pro dany connection string.
        /// Vyuziva se pro databazi automatickych aktualizaci.
        /// </summary>
        /// <param name="connectionString"></param>
        /// <returns></returns>
        public static string GetServerContextOrEmptyString(string connectionString)
        {
            if (!IsCentralServer)
            {
                SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(connectionString);              

                if (_notUseCentralServerConnectionString.Contains(connectionString) || 
					connectionString == DependencyContainer.Instance.Resolve<IKreditDbConnectionProvider>().ConnectionString && 
					!_useKreditServerContext)
                {
                    return "";
                }
                else
                {
                    return ServerContext.Replace("%catalog%", builder.InitialCatalog);
                }
            }
            else
            {
                return "";
            }
        }


		/// <summary>
		/// Vraci server context pro dany connection string db Sklady
		/// </summary>
		public static string GetSkladyServerContextOrEmptyString(string connectionString)
		{
			if (!IsSkladyCentralServer)
			{
				SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(connectionString);

				return SkladyServerContext.Replace("%catalog%", builder.InitialCatalog);
			}
			else
			{
				return "";
			}
		}

		// ToJNOTE: 10.08.2015 Jiz postrada smysl. Mechanizmus detekce linovaneho serveru byl zmenen.
		/// <summary>
		/// Vraci upraveny connection string. Nutno zavolat predtim, nez se zacne pracovat s databazi automatickych aktualizaci.
		/// </summary>		
		/// <returns></returns>
		/*public static string GetNewUpdatesConnectionString(string kreditUpdatesConnectionString)
        {
            // na replikovanem serveru nemuzu predavat InitialKatalog            
            if (!CentralServerUtils.IsCentralServer)
            {
                _log.Debug("Bezim na replikovanem serveru, mozna budu upravovat connection string");
                // musim skontrolovat existenci databaze. Rozhodne se nemuzu pripojovat na server s definovanym katalogem
                SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(kreditUpdatesConnectionString);
                string catalog = builder.InitialCatalog;                
                builder.Remove("Initial Catalog");
				// kvuli rychlemu prepnuti do offline. Neni treba cekat 30 sec na spojeni
				builder.ConnectTimeout = 5;
                _log.InfoFormat("Kontrola existence katalogu {0}", catalog);

                // novy connection string bez katalogu pouziju pro test existence databaze
                bool catalogExists;
                using (SqlConnection connection = new SqlConnection(builder.ToString()))
                {
					// Kak: 13.8.2014 i kdyz je nastaveny ConnectTimeout na 5 sec, stejne trva conn.Open() nejmene 30 sec. Nevim, co s tim
                    connection.Open();
                    string queryString = "select ISNULL(HAS_DBACCESS(@catalog), 0)";
                    using (SqlCommand command = connection.CreateCommand())
                    {
                        command.CommandText = queryString;
                        command.Parameters.Add("@catalog", SqlDbType.VarChar).Value = catalog;
                        catalogExists = (int)command.ExecuteScalar() == 0 ? false : true;                        
                    }
                }

                if (catalogExists)
                {
                    _log.InfoFormat("Catalog existuje, pouziju plny connection string");
                    // databaze existuje, pouzije cely connection string bez prefixu, musim si to poznamenat
                    _notUseCentralServerConnectionString.Add(kreditUpdatesConnectionString);
                    return kreditUpdatesConnectionString;
                }
                else
                {
                    _log.Info("Catalog neexistuje, pouziju connection string bez jeho definice");
                    // databaze neexistuje, na server musim pristupovat bez Initial Catalog
                    _defaultUpdatesKatalog = catalog;
                    return builder.ToString();
                }                
            }
            else
            {
                return kreditUpdatesConnectionString;
            }
        }*/

		/// <summary>
		/// Vraci nazev centralniho serveru
		/// </summary>
		/// <returns></returns>
		public static string GetCentralServerName()
        {
            using (SqlConnection connection = DependencyContainer.Instance.Resolve<IKreditDbConnectionProvider>().CreateConnection())
            {
                connection.Open();
                using (SqlCommand command = connection.CreateCommand())
                {
                    command.CommandText = "select server_name from dba.CFServery where central=1";
                    return (string)command.ExecuteScalar();
                }
            }
        }
        #endregion

        private static string _serverContext = null;
        /// <summary>
        /// Gets the server context.
        /// </summary>
        /// <value>The server context.</value>
        private static string ServerContext
        {
            get
            {
                if (_serverContext == null)
                {
                    LoadServerContext();
                }
                return _serverContext;
            }
        }

		private static string _centralServerName = null;
		/// <summary>
		/// Nazev centralniho serveru
		/// </summary>
		public static string CentralServerName
		{
			get
			{
				if(_centralServerName == null)
				{
					LoadServerContext();
				}
				return _centralServerName;
			}
		}

		private static int _maxTextReplSize;
		/// <summary>
		/// Maximalni velikost textove zpravy, kterou lze prenest na replice
		/// </summary>
		public static int MaxTextReplSize
		{
			get
			{
				if (_serverContext == null)
				{
					LoadServerContext();
				}

				if (_isReplicatedServer != true)
				{
					throw new ArgumentException("Ma smysl volat pouze pokud se jedna o replikovany server");
				}
				return _maxTextReplSize;
			}
		}

		private static string _skladyServerContext = null;
		/// <summary>
		/// Gets the server context.
		/// </summary>
		/// <value>The server context.</value>
		private static string SkladyServerContext
		{
			get
			{
				if (_skladyServerContext == null)
				{
					LoadSkladyServerContext();
				}
				return _skladyServerContext;
			}
		}
		
		/// <summary>
        /// Nastavi property ServerContext a IsCentralServer.
        /// Nastaveno jako public, aby si aplikace sama mohla zavolat metodu LoadServerContext ve chvili, kdy je to vhodne.
        /// Napriklad volani LoadServerContext v dobe, kdy je zahajena transakce, by skoncilo vyjimkou.
        /// </summary>
		private static void LoadServerContext()
		{
			// pred ulozenim si musim zjistit prefix DB serveru, protoze se muze stat, ze ukladam vuci replice
			IKreditDbConnectionProvider kreditConnectionProvider = DependencyContainer.Instance.Resolve<IKreditDbConnectionProvider>();
			using (SqlConnection connection = kreditConnectionProvider.CreateConnection())
			{
				// retezec %catalog% nasledne nahrazuju za nazev databaze podle toho, zda chci kontext pro Kredit nebo AneteUpdates
				connection.Open();
				string queryString =
					@"select '[' + server_name + '].[%catalog%].', 
                    (case when (server_name=@@SERVERNAME or server_name='[' + @@SERVERNAME + ']') then 1 else 0 end),
					server_name
                    from dba.CFServery where central=1";
				/*@"select '[' + server_name + '].[' + db_name() + '].', 
				(case when (server_name=@@SERVERNAME or server_name='[' + @@SERVERNAME + ']') then 1 else 0 end) 
				from dba.CFServery where central=1";*/
				using (SqlCommand command = connection.CreateCommand())
				{
					command.CommandText = queryString;
					using (SqlDataReader reader = command.ExecuteReader())
					{
						if (reader.Read())
						{
							_serverContext = (string)reader[0];
							_isCentralServer = (int)reader[1] == 1;
							_centralServerName = (string)reader[2];
						}
						else
						{
							throw new InvalidOperationException(CentralServerUtilsSR.NelzeZjistitPrefixDBServeruFormat(queryString));
						}
					}
				}

				if (_isCentralServer == true)
				{
					// i prestoze jsem na centralnim serveru, neznamena to, ze se musi jedna o replikovany system
					queryString = "select count(*) from dba.CFServery";
					using (SqlCommand command = connection.CreateCommand())
					{
						command.CommandText = queryString;
						int count = (int)command.ExecuteScalar();
						_isReplicatedServer = count > 1;
					}
				}
				else
				{
					_isReplicatedServer = true;
				}

				if (_isReplicatedServer.Value)
				{
					// vim, ze se jedna o replikovany system, musim nacist maximalni velikost zpravy										
					using (SqlCommand command = connection.CreateCommand())
					{
						command.CommandText = "EXEC sp_configure 'max text repl size'";
						using (SqlDataReader reader = command.ExecuteReader())
						{
							// vraci se vzdy jeden zaznam
							reader.Read();
							int run_value = (int)reader["run_value"];
							// hodnota -1 znamena bez omezeni
							_maxTextReplSize = run_value == -1 ? int.MaxValue : run_value;
						}											
					}

				}
			}
		}

		/// <summary>
		/// Nastavi property SkladyServerContext a IsSkladyCentralServer.
		/// Nastaveno jako public, aby si aplikace sama mohla zavolat metodu LoadSkladyServerContext ve chvili, kdy je to vhodne.
		/// Napriklad volani LoadSkladyServerContext v dobe, kdy je zahajena transakce, by skoncilo vyjimkou.
		/// Upraveno pro db Sklady
		/// </summary>
		private static void LoadSkladyServerContext()
		{
			// pred ulozenim si musim zjistit prefix DB serveru, protoze se muze stat, ze ukladam vuci replice
			using (SqlConnection connection = DependencyContainer.Instance.Resolve<ISkladyDbConnectionProvider>().CreateConnection())
			{
				// retezec %catalog% nasledne nahrazuju za nazev databaze
                connection.Open();
                string queryString =
                    @"select '[' + SERVERNAME + '].[%catalog%].', 
                    (case when (SERVERNAME=@@SERVERNAME or SERVERNAME='[' + @@SERVERNAME + ']') then 1 else 0 end) 
                    from dba.SCC_SERVERY where REPLIKA_ID=1";
                using (SqlCommand command = connection.CreateCommand())
				{
					command.CommandText = queryString;
					using (SqlDataReader reader = command.ExecuteReader())
					{
						if (reader.Read())
                        {
                            _skladyServerContext = (string)reader[0];
                            _isSkladyCentralServer = (int)reader[1] == 1;
                        }
						else
						{
							throw new InvalidOperationException(CentralServerUtilsSR.NelzeZjistitPrefixDBServeruFormat(queryString));
						}
					}
				}

				if (_isSkladyCentralServer == true)
				{
					// i prestoze jsem na centralnim serveru, neznamena to, ze se musi jedna o replikovany system
					queryString = "select count(*) from dba.SCC_SERVERY";
					using (SqlCommand command = connection.CreateCommand())
					{
						command.CommandText = queryString;
						int count = (int)command.ExecuteScalar();
						_isSkladyReplicatedServer = count > 1;
					}
				}
				else
				{
					_isSkladyReplicatedServer = true;
				}
			}
		}
    }
}
