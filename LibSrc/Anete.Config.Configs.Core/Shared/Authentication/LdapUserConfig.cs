using Anete.Config.Core.Attributes;
using Anete.Config.Core.Internals;
using System.ComponentModel;

namespace Anete.Config.Configs.Core.Shared.Authentication
{
	[TypeConverter(typeof(ExpandableObjectConverter))]
	public class LdapUserConfig : NestedClassBase
	{
		private const string _userNameFormatDefValue = "{UserDN}";
		private string _userNameFormat = _userNameFormatDefValue;
		[DefaultValue(_userNameFormatDefValue)]
		[ConfigSRDisplayName(nameof(UserNameFormat), typeof(LdapUserConfig))]
		[ConfigSRDescription(nameof(UserNameFormat), typeof(LdapUserConfig))]
		public string UserNameFormat
		{
			get { return _userNameFormat; }
			set
			{
				if (_userNameFormat == value)
				{
					return;
				}
				_userNameFormat = value;
				OnPropertyChanged();
			}
		}

		private const string _domainDefValue = null;
		private string _domain = _domainDefValue;
		[DefaultValue(_domainDefValue)]
		[ConfigSRDisplayName(nameof(Domain), typeof(LdapUserConfig))]
		[ConfigSRDescription(nameof(Domain), typeof(LdapUserConfig))]
		public string Domain
		{
			get { return _domain; }
			set
			{
				if (_domain == value)
				{
					return;
				}
				_domain = value;
				OnPropertyChanged();
			}
		}

		private const string _userNameKreditFormatDefValue = "{UserName}";
		private string _userNameKreditFormat = _userNameKreditFormatDefValue;
		[DefaultValue(_userNameKreditFormatDefValue)]
		[ConfigSRDisplayName(nameof(UserNameKreditFormat), typeof(LdapUserConfig))]
		[ConfigSRDescription(nameof(UserNameKreditFormat), typeof(LdapUserConfig))]
		public string UserNameKreditFormat
		{
			get { return _userNameKreditFormat; }
			set
			{
				if (_userNameKreditFormat == value)
				{
					return;
				}
				_userNameKreditFormat = value;
				OnPropertyChanged();
			}
		}
	}
}