using Anete.Common.Core.Business.InstallationParameters.Serializers;

namespace Anete.Common.Core.Business.InstallationParameters.Parameters.Computer.ComputerNote
{
	public class ComputerNoteSerializer : DataContractInstallationParameterValueSerializer<ComputerNoteParameterValueDto, ComputerNoteParameterValue>
	{
		protected override ComputerNoteParameterValueDto CreateDto(ComputerNoteParameterValue installationParameterValue)
		{
			return new ComputerNoteParameterValueDto(installationParameterValue.ComputerNote);
		}

		protected override ComputerNoteParameterValue CreateInstallationParameterValue(ComputerNoteParameterValueDto dto)
		{
			return new ComputerNoteParameterValue(dto.ComputerNote);
		}
	}
}