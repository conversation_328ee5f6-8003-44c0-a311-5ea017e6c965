using Anete.ApiServices.Authentication;
using Anete.Common.Core.Interface.Enums;
using System;

namespace Anete.AppServer.AutoUpdates.Interface.Infrastructure
{
	public class AuthenticateRequest
	{
		public AuthenticateRequest(
			Guid computerId,
			Guid computerPassword,
			UpdateTypeId updateTypeId,
			short? appInstallationId,
			AppAuthenticationType[] authenticationTypes = null,
			bool forceRequireCredentials = false,			
			AuthenticationDataBase authData = null
		)
		{
			ComputerId = computerId;
			ComputerPassword = computerPassword;
			AppInstallationId = appInstallationId;
			UpdateTypeId = updateTypeId;		
			ForceRequireCredentials = forceRequireCredentials;
			AuthenticationTypes = authenticationTypes;
			AuthData = authData;		
		}

		public Guid ComputerId { get; }
		public Guid ComputerPassword { get; }
		
		public UpdateTypeId UpdateTypeId { get; }
		public short? AppInstallationId { get; }


		/// <summary>
		/// Zpusoby autentizace. Muze byt null v pripade servisni aplikace - ta prihlasovaci udaje neposila, overuje se jen id pocitace a jeho heslo.
		/// </summary>
		public AppAuthenticationType[] AuthenticationTypes { get; }

		/// <summary>
		/// Vynutit si zadani prihlasovacich udaju?
		/// Ma smysl u aplikaci, ktere maji vice spustitelnych souboru. Nektere z nich bezi bezobsluzne, nektera naopak vyzaduji prihlaseni.
		/// Ne vzdy se tedy da ridit pouze predanym UpdateTypeId.
		/// 
		/// Prikladem jsou ctecky. Maji 2 bezobluszne aplikace - monitor a sluzbu. Dale jednu obsluznout aplikaci - Konfigurator Ctecek.
		/// </summary>
		public bool ForceRequireCredentials { get; }

		/// <summary>
		/// Pro webové aplikace, kde chci přihlašovací údaje předávat přímo
		/// </summary>
		public AuthenticationDataBase AuthData { get; }
	}
}