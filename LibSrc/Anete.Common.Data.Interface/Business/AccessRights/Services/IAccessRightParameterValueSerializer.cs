using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Anete.Common.Data.Interface.Business.AccessRights.Parameters;
using Anete.Common.Data.Interface.Entities;
using Anete.Utils;

namespace Anete.Common.Data.Interface.Business.AccessRights.Services
{
    /// <summary>
    /// Rozhrani pro serializaci/deserializaci parametru pristupovych prav do DB.
    /// </summary>
    public interface IAccessRightParameterValueSerializer
    {
        /// <summary>
        /// Vraci hodnotu parametru prava deserializovanou z daneho stringu.
        /// </summary>
        /// <typeparam name="TAccessRightParameter">The type of the access right parameter.</typeparam>
        /// <typeparam name="TAccessRightParameterKey">The type of the access right parameter key.</typeparam>
        /// <param name="xmlText">The XML text.</param>
        /// <returns></returns>
        IAccessRightParameterValue<TAccessRightParameter> Deserialize<TAccessRightParameter, TAccessRightParameterKey>(string xmlText) 
            where TAccessRightParameterKey: IAccessRightParameterKey
            where TAccessRightParameter : IAccessRightParameter;

        /// <summary>
        /// Vraci string, ktery reprezentuje hodnotu parametru prava.
        /// </summary>
        /// <typeparam name="TAccessRightParameter">The type of the access right parameter.</typeparam>
        /// <typeparam name="TAccessRightParameterKey">The type of the access right parameter key.</typeparam>
        /// <param name="parameterValue">The parameter value.</param>
        /// <returns></returns>
        string Serialize<TAccessRightParameter, TAccessRightParameterKey>(IAccessRightParameterValue<TAccessRightParameter> parameterValue)
            where TAccessRightParameterKey : IAccessRightParameterKey
            where TAccessRightParameter: IAccessRightParameter;
    }
}