//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate Fluent Mapping template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;
using FluentNHibernate.Mapping;
using FluentNHibernate.MappingModel.Collections;

namespace Anete.Common.Data.Nh.Entities
{
    /// <summary>
    /// There are no comments for MenuRowCanteenPrintOnMap in the schema.
    /// </summary>
    public partial class MenuRowCanteenPrintOnMap : ClassMap<MenuRowCanteenPrintOn>
    {
        /// <summary>
        /// There are no comments for MenuRowCanteenPrintOnMap constructor in the schema.
        /// </summary>
        public MenuRowCanteenPrintOnMap()
        {
              Schema(@"dba");
              Table(@"JidelnicekVydejna");
              LazyLoad();
              CompositeId()
                .KeyProperty(x => x.MenuRowId, set => {
                    set.Type("Int32");
                    set.ColumnName("jidlo_pc");
                    set.Access.Property(); } )
                .KeyProperty(x => x.CanteenId, set => {
                    set.Type("Int16");
                    set.ColumnName("id_vydejna");
                    set.Access.Property(); } );
              Map(x => x.PrintOn)    
                .Column("tisk_objednavky")
                .CustomType("Byte")
                .Access.Property()
                .Generated.Never().CustomSqlType("tinyint")
                .Precision(3);
              References(x => x.Canteen)
                .Class<Canteen>()
                .Access.Property()
                .Cascade.None()
                .LazyLoad()
                .Not.Insert()
                .Not.Update()
                .Columns("id_vydejna");
              References(x => x.MenuRow)
                .Class<MenuRow>()
                .Access.Property()
                .Cascade.None()
                .LazyLoad()
                .PropertyRef(p => p.MenuRowId)
                .Not.Insert()
                .Not.Update()
                .Columns("jidlo_pc");
              ExtendMapping();
        }

        #region Partial Methods

        partial void ExtendMapping();

        #endregion
    }

}
