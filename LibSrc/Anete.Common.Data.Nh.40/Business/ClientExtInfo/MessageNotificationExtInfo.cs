using System.Collections.Generic;
using System.Runtime.Serialization;
using Anete.Utils.ComponentModel.Attributes;
namespace Anete.Common.Data.Nh.Business.ClientExtInfo
{
	/// <summary>
	/// Hlídání limitu zálohovného <PERSON> - rozšiřující informace.
	/// </summary>
	[DataContract(Namespace = Anete.Resources.AneteNamespace.DefaultNamespace)]
	public class MessageNotificationExtInfo : IClientExtInfo
	{
		#region constructors...
		public MessageNotificationExtInfo()
		{

		}

		public MessageNotificationExtInfo(List<int> notifications)
		{
			Notifications = notifications;
		}
		#endregion

		#region public properties...
		[DataMember(Order = 1)]
		//[ResXDisplayNameAttribute("Notifications", typeof(MessageNotificationExtInfoSR))]
		public List<int> Notifications { get; private set; }

		#endregion

		#region public methods...
		public void SetProperties(List<int> notifications)
		{
			Notifications = notifications;
		}
		#endregion
	}
}
