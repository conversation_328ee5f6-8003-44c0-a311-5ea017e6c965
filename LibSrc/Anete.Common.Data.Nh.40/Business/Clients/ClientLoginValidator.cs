using System;
using System.Collections.Generic;
using System.Linq;
using Anete.Common.Data.Nh.Entities;
using Microsoft.Practices.EnterpriseLibrary.Validation;
using Anete.Config.Core;
using Anete.Config.Configs.Core.Global.Rules;
using Anete.Utils;
using Anete.Data.Nh.Services;
using NHibernate;
using System.ComponentModel;
using Anete.Utils.Extensions;

namespace Anete.Common.Data.Nh.Business.Clients
{
	/// <summary>
	/// Validace prihlasovaciho jmena
	/// </summary>
	public class ClientLoginValidator
	{
		public const string LoginNameTag = "LoginName";
		private readonly ISessionProvider _sessionProvider;

		/// <summary>
		/// Initializes a new instance of the ClientLoginValidator class.
		/// </summary>
		public ClientLoginValidator(ISessionProvider sessionProvider)
		{
			_sessionProvider = sessionProvider;
		}

		public void Validate(bool isNewClient, string loginName, int clientId, ValidationResults results)
		{
			IList<Client> alreadyExists;

			if (string.IsNullOrEmpty(loginName))
			{
				// nemusim nic validovat, prihlasovaci jmeno neni  vubec nastaveno
				return;
			}

			ISession session = _sessionProvider.Session;
			if (isNewClient)
			{
				alreadyExists = session.QueryOver<Client>().Where(client => client.LoginName == loginName).List();
			}
			else
			{
				alreadyExists = session.QueryOver<Client>().Where(client =>
					client.ClientId != clientId &&
					client.LoginName == loginName)
					.List();
			}
			if (alreadyExists.Any())
			{
				results.AddResult(new ValidationResult(ClientLoginValidatorSR.LoginNameAlreadyExistsInTheDatabaseFormat(alreadyExists.Select(c => c.ClientId).ToCommaSpaceDelimitedString()), this, "ClientLoginValidator", LoginNameTag, null)); ;
			}

			//https://helpdesk.anete.com/issues/55416
			IList<RADSubLogin> alreadyExistsSublogins;
			if (isNewClient)
			{
				alreadyExistsSublogins = session.QueryOver<RADSubLogin>().Where(subLogin => subLogin.AccountName == loginName).List();
			}
			else
			{
				alreadyExistsSublogins = session.QueryOver<RADSubLogin>().Where(subLogin =>
					subLogin.ClientId != clientId &&
					subLogin.AccountName == loginName)
					.List();
			}
			if (alreadyExistsSublogins.Any())
			{
				results.AddResult(new ValidationResult(ClientLoginValidatorSR.SubLoginNameAlreadyExistsInTheDatabaseFormat(alreadyExistsSublogins.Select(subLogin => subLogin.ClientId).ToCommaSpaceDelimitedString()), this, "ClientLoginValidator", LoginNameTag, null)); ;
			}
		}
	}
}
