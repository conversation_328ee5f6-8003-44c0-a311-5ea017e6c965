using System;
using System.Collections.Generic;
using System.Text;
using Anete.Utils.Extensions;
using System.Xml;
using System.Globalization;
using System.IO;
using System.Xml.Linq;

namespace Anete.Common.Data.Interface.Business.Paragon.Uhrada
{

	/// <summary>
	/// Popis detailu uhrady pri platbe bankovni kartou
	/// </summary>
	public class BankKartaUhradaDetail : IUhradaDetail
	{

		#region constructors...
		/// <summary>
		/// Inicializace z Xml stringu
		/// </summary>
		/// <param name="xmlString">Cizi mena v Xml tak, jak je ulozeno v PokladnaP</param>
		public BankKartaUhradaDetail(string xmlString)
		{
			LoadFromXmlString(xmlString);
		}

		/// <summary>
		/// Initializes a new instance
		/// </summary>
		/// <param name="castka">Kolik bylo uhrazeno v cizi mene</param>
		/// <param name="systemNumber">The system number.</param>
		public BankKartaUhradaDetail(decimal castka, int systemNumber)
		{
			SystemNumer = systemNumber;
			Castka = castka;
		}
		#endregion

		#region public properties...
		/// <summary>
		/// SystemNumber platby
		/// </summary>
		public int SystemNumer { get; private set; }

		/// <summary>
		/// Kolik bylo uhrazeno v cizi mene
		/// </summary>
		public decimal Castka { get; private set; }
		#endregion

		#region public overrides...
		public void MakeStorno()
		{
			// #rm 4046: Pokud se stornuje platba bankovni kartou, bylo by v polozce stejne SystemNumber. Proto vynulujeme.
			// Storno platba se nedela pres terminal.
			SystemNumer = 0;
		}

		/// <summary>
		/// Prevod na XmlString tak, jak se vyskytuje v PokladnaP.Poznamka
		/// </summary>
		/// <returns></returns>
		public XElement ToXElement()
		{
			return new XElement("BK",
				new XAttribute("SN", SystemNumer)
			);
		}
		#endregion

		#region private methods...
		/// <summary>
		/// Nacteni x Xml stringu, tak jak se vyskytuje v databazi
		/// </summary>
		/// <param name="xmlString"></param>
		private void LoadFromXmlString(string xmlString)
		{
			// kvuli kompatibilite se starymi daty
			if (xmlString.IsNullOrEmpty())
			{
				SystemNumer = 0;
			}
			else
			{
				XElement element = XElement.Parse(xmlString);
				SystemNumer = (int)element.Attribute("SN");
			}
		}
		#endregion


	}
}
