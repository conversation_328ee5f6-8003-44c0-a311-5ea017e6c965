using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using Anete.Common.Core.Interface.AppServices;
using Anete.Common.Data.AccessRights.Services.AccessRights;
using Anete.Common.Data.Interface.Business.AccessRights.Entities;
using Anete.Common.Data.Interface.Business.AccessRights.Repositories;
using Anete.Common.Data.Interface.Business.AccessRights.Services;
using Anete.Common.Data.Nh.Entities;
using Anete.Common.Data.Nh.Interface.Services.AccessRights;
using NHibernate;
using Unity;

namespace Anete.Common.Data.Nh.Services.AccessRights
{
	/// <summary>
	/// Spravuje historii uzivatelskych prav (importu, verze...).
	/// </summary>
	public class AccessRightHistoryService : IAccessRightHistoryService
	{
		private readonly IServerTimeService _serverTimeService;

		/// <summary>
		/// Initializes a new instance of the AccessRightHistoryService class.
		/// </summary>
		public AccessRightHistoryService(IServerTimeService serverTimeService)
		{
			_serverTimeService = serverTimeService;				
		}

		private AccHistorie GetLastHistoryItem(ISession session, HistoryAction historyAction)
		{	
			return session.Query<AccHistorie>().Where(h=>h.Akce == (byte)historyAction).OrderByDescending(h => h.Datum).FirstOrDefault();			
		}

		public Version GetLastImportedVersion(ISession session)
		{
			AccHistorie history = GetLastHistoryItem(session, HistoryAction.Import);
			if (history == null)
			{
				return null;
			}

			return new Version(history.Verze);
		}

		public void SetLastImportedVersion(ISession session, Version version)
		{
			SetVersionFor(session, version, HistoryAction.Import);
		}
		
		public void SetLastExpotedVersion(ISession session, Version version)
		{
			SetVersionFor(session, version, HistoryAction.Export);
		}

		public Version GetLastExportedVersion(ISession session)
		{
			AccHistorie history = GetLastHistoryItem(session, HistoryAction.Export);
			if (history == null)
			{
				return null;
			}

			return new Version(history.Verze);
		}
		private void SetVersionFor(ISession session, Version version, HistoryAction action)
		{
			AccHistorie history = new AccHistorie()
			{
				Akce = (byte)action,
				Datum = _serverTimeService.Now,
				Verze = version.ToString()
			};

			session.Save(history);
		}
	}
}
