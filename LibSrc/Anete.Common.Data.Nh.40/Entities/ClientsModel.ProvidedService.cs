//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Anete.Common.Data.Nh.Entities
{

    /// <summary>
    /// Tabulka: dba.CFSluzby
    /// </summary>
    public partial class ProvidedService : Anete.Data.Nh.NhEntityBase, INotifyPropertyChanging, INotifyPropertyChanged {

        private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(System.String.Empty);

        private byte _ProvidedServiceId;

        private string _Name;

        private ISet<GoodsGroup> _GoodsGroups;
    
        #region Extensibility Method Definitions
        
        partial void OnCreated();

        public override bool Equals(object obj)
        {
          ProvidedService toCompare = obj as ProvidedService;
          if (toCompare == null)
          {
            return false;
          }

          if (!Object.Equals(this.ProvidedServiceId, toCompare.ProvidedServiceId))
            return false;
         // pokud mam default primary key, znamena to, ze jedna o nove vytvorenou entitu, ktera jeste nema key prirazen. 
        // V takove pripade musim porovnat podle instance
        bool isDefault = true;
        if (this.ProvidedServiceId != default(byte))
        {
          isDefault = false;
        }
     

          if (isDefault)
          {
            return object.ReferenceEquals(toCompare, this);
          }     
          return true;
        }

	private int? _hashCode = null;
        public override int GetHashCode()
        {
	if (_hashCode == null)
          {
          _hashCode = 13;
          _hashCode = (_hashCode * 7) + ProvidedServiceId.GetHashCode();
}
          return _hashCode.Value;
        }
        partial void OnProvidedServiceIdChanging(byte value);
        
        partial void OnProvidedServiceIdChanged();
        partial void OnNameChanging(string value);
        
        partial void OnNameChanged();
        
        #endregion
        public ProvidedService()
        {
            this._GoodsGroups = new HashSet<GoodsGroup>();
            OnCreated();
        }

    
        /// <summary>
        /// Sloupec: id_sluzby
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("ProvidedServiceId", typeof(ProvidedServiceSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(ProvidedService), Tag="ProvidedServiceId")]
        public virtual byte ProvidedServiceId
        {
            get
            {
                return this._ProvidedServiceId;
            }
            set
            {
                if (this._ProvidedServiceId != value)
                {
                    this.OnProvidedServiceIdChanging(value);
                    this.SendPropertyChanging();
                    this._ProvidedServiceId = value;
                    this.SendPropertyChanged("ProvidedServiceId");
                    this.OnProvidedServiceIdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: popis
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Name", typeof(ProvidedServiceSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(ProvidedService), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 50,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="Name")]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(ProvidedService), Tag="Name")]
        public virtual string Name
        {
            get
            {
                return this._Name;
            }
            set
            {
                if (this._Name != value)
                {
                    this.OnNameChanging(value);
                    this.SendPropertyChanging();
                    this._Name = value;
                    this.SendPropertyChanged("Name");
                    this.OnNameChanged();
                }
            }
        }

        [Anete.Data.Attributes.EntitySRDisplayName("GoodsGroups", typeof(ProvidedServiceSR))]
        [Anete.Data.Attributes.DbCollectionProperty]
        public virtual ISet<GoodsGroup> GoodsGroups
        {
            get
            {
                return this._GoodsGroups;
            }
            set
            {
                this._GoodsGroups = value;
            }
        }
   
        public virtual event PropertyChangingEventHandler PropertyChanging;

	// Mam v bazove tride, nema smysl generovat
	// public virtual event PropertyChangedEventHandler PropertyChanged;

        protected virtual void SendPropertyChanging()
        {
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, emptyChangingEventArgs);
        }

        protected virtual void SendPropertyChanging(System.String propertyName) 
        {   	
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, new PropertyChangingEventArgs(propertyName));
        }

        protected virtual void SendPropertyChanged(System.String propertyName)
        {    
		// mam v bazove tride, proto musim volat takto jednoduse
		OnPropertyChanged(propertyName);
/*		        var handler = this.PropertyChanged;
            if (handler != null)
                handler(this, new PropertyChangedEventArgs(propertyName));*/
        }
    }

}
