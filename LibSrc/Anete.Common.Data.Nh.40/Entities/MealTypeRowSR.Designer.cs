//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Anete.Common.Data.Nh.Entities {
    using System;
    
    
    /// <summary>
    /// A strongly-typed resource class, for looking up localized strings, formatting them, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilderEx class via the ResXFileCodeGeneratorEx custom tool.
    // To add or remove a member, edit your .ResX file then rerun the ResXFileCodeGeneratorEx custom tool or rebuild your VS.NET project.
    // Copyright (c) Dmytro <PERSON> 2006-2016 (http://dmytro.kryvko.googlepages.com/)
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("DMKSoftware.CodeGenerators.Tools.StronglyTypedResourceBuilderEx", "2.7.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
#if !SILVERLIGHT && !PocketPC && !Smartphone && !WindowsCE
    [global::System.Reflection.ObfuscationAttribute(Exclude=true, ApplyToMembers=true)]
#endif
    [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Naming", "CA1724:TypeNamesShouldNotMatchNamespaces")]
    public partial class MealTypeRowSR {
        
        private static global::System.Resources.ResourceManager _resourceManager;
        
        private static object _internalSyncObject;
        
        private static global::System.Globalization.CultureInfo _resourceCulture;
        
        /// <summary>
        /// Initializes a MealTypeRowSR object.
        /// </summary>
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        public MealTypeRowSR() {
        }
        
        /// <summary>
        /// Thread safe lock object used by this class.
        /// </summary>
        public static object InternalSyncObject {
            get {
                if (object.ReferenceEquals(_internalSyncObject, null)) {
                    global::System.Threading.Interlocked.CompareExchange(ref _internalSyncObject, new object(), null);
                }
                return _internalSyncObject;
            }
        }
        
        /// <summary>
        /// Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(_resourceManager, null)) {
                    global::System.Threading.Monitor.Enter(InternalSyncObject);
                    try {
                        if (object.ReferenceEquals(_resourceManager, null)) {
                            global::System.Threading.Interlocked.Exchange(ref _resourceManager, new global::System.Resources.ResourceManager("Anete.Common.Data.Nh.Entities.MealTypeRowSR", typeof(MealTypeRowSR).Assembly));
                        }
                    }
                    finally {
                        global::System.Threading.Monitor.Exit(InternalSyncObject);
                    }
                }
                return _resourceManager;
            }
        }
        
        /// <summary>
        /// Overrides the current thread's CurrentUICulture property for all
        /// resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return _resourceCulture;
            }
            set {
                _resourceCulture = value;
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Aktivní'.
        /// </summary>
        public static string Active {
            get {
                return ResourceManager.GetString(ResourceNames.Active, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Souvisí s jídly'.
        /// </summary>
        public static string ChildMealTypeRows {
            get {
                return ResourceManager.GetString(ResourceNames.ChildMealTypeRows, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Kód'.
        /// </summary>
        public static string Code {
            get {
                return ResourceManager.GetString(ResourceNames.Code, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Lokalizovaný název'.
        /// </summary>
        public static string LocalizedName {
            get {
                return ResourceManager.GetString(ResourceNames.LocalizedName, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Typ jídla'.
        /// </summary>
        public static string MealType {
            get {
                return ResourceManager.GetString(ResourceNames.MealType, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Id typu jídla'.
        /// </summary>
        public static string MealTypeId {
            get {
                return ResourceManager.GetString(ResourceNames.MealTypeId, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Id'.
        /// </summary>
        public static string MealTypeRowId {
            get {
                return ResourceManager.GetString(ResourceNames.MealTypeRowId, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Název'.
        /// </summary>
        public static string Name {
            get {
                return ResourceManager.GetString(ResourceNames.Name, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Poznámka'.
        /// </summary>
        public static string Note {
            get {
                return ResourceManager.GetString(ResourceNames.Note, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Implicitní natavení souvisejícího jídla'.
        /// </summary>
        public static string ParentMealTypeRow {
            get {
                return ResourceManager.GetString(ResourceNames.ParentMealTypeRow, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Lists all the resource names as constant string fields.
        /// </summary>
        public class ResourceNames {
            
            /// <summary>
            /// Stores the resource name 'Active'.
            /// </summary>
            public const string Active = "Active";
            
            /// <summary>
            /// Stores the resource name 'ChildMealTypeRows'.
            /// </summary>
            public const string ChildMealTypeRows = "ChildMealTypeRows";
            
            /// <summary>
            /// Stores the resource name 'Code'.
            /// </summary>
            public const string Code = "Code";
            
            /// <summary>
            /// Stores the resource name 'LocalizedName'.
            /// </summary>
            public const string LocalizedName = "LocalizedName";
            
            /// <summary>
            /// Stores the resource name 'MealType'.
            /// </summary>
            public const string MealType = "MealType";
            
            /// <summary>
            /// Stores the resource name 'MealTypeId'.
            /// </summary>
            public const string MealTypeId = "MealTypeId";
            
            /// <summary>
            /// Stores the resource name 'MealTypeRowId'.
            /// </summary>
            public const string MealTypeRowId = "MealTypeRowId";
            
            /// <summary>
            /// Stores the resource name 'Name'.
            /// </summary>
            public const string Name = "Name";
            
            /// <summary>
            /// Stores the resource name 'Note'.
            /// </summary>
            public const string Note = "Note";
            
            /// <summary>
            /// Stores the resource name 'ParentMealTypeRow'.
            /// </summary>
            public const string ParentMealTypeRow = "ParentMealTypeRow";
        }
    }
}
