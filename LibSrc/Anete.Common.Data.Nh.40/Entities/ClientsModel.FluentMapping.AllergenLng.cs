//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate Fluent Mapping template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;
using FluentNHibernate.Mapping;
using FluentNHibernate.MappingModel.Collections;

namespace Anete.Common.Data.Nh.Entities
{
    /// <summary>
    /// There are no comments for AllergenLngMap in the schema.
    /// </summary>
    public partial class AllergenLngMap : ClassMap<AllergenLng>
    {
        /// <summary>
        /// There are no comments for AllergenLngMap constructor in the schema.
        /// </summary>
        public AllergenLngMap()
        {
              Schema(@"dba");
              Table(@"SCC_ALERGENY_Popis");
              LazyLoad();
              CompositeId()
                .KeyProperty(x => x.AllergenId, set => {
                    set.Type("Guid");
                    set.ColumnName("ALERGEN_ID");
                    set.Access.Property(); } )
                .KeyProperty(x => x.LanguageId, set => {
                    set.Type("Anete.Common.Core.Interface.Enums.ApplicationLanguage, Anete.Common.Core.Interface.40");
                    set.ColumnName("langid");
                    set.Access.Property(); } );
              Map(x => x.Name)    
                .Column("POPIS")
                .CustomType("String")
                .Access.Property()
                .Generated.Never().CustomSqlType("nvarchar(200)")
                .Not.Nullable()
                .Length(200);
              Map(x => x.ShortName)    
                .Column("POPIS_ZKRACENY")
                .CustomType("String")
                .Access.Property()
                .Generated.Never().CustomSqlType("nvarchar(50)")
                .Not.Nullable()
                .Length(50);
              References(x => x.Allergen)
                .Class<Allergen>()
                .Access.Property()
                .Cascade.None()
                .LazyLoad()
                .Not.Insert()
                .Not.Update()
                .Columns("ALERGEN_ID");
              ExtendMapping();
        }

        #region Partial Methods

        partial void ExtendMapping();

        #endregion
    }

}
