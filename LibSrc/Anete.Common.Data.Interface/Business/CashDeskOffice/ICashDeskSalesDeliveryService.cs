using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Xml.Linq;

namespace Anete.Common.Data.Interface.Business.CashDeskOffice
{
	/// <summary>
	/// Sluzba pro Kasu - odvody trzby
	/// </summary>
	public interface ICashDeskSalesDeliveryService
	{

		/// <summary>
		/// Nacte polozky platidel dle druhu pro dane datum
		/// </summary>
		/// <param name="date"></param>
		/// <param name="movementSign"></param>
		/// <param name="appInstallationId"></param>
		/// <returns></returns>
		IEnumerable<CurrencyByTypeItem> LoadCurrencyByTypeItemsForDate(DateTime date, int movementSign, short appInstallationId);

		
		/// <summary>
		/// Vraci minimalni datum odvodu trzby, ktere lze zadat. Null, pokud neni nijak omezeno
		/// </summary>
		/// <returns></returns>
		DateTime? GetMinDeliveryDate();

		
		/// <summary>
		/// Vraci datum posledni uzaverky
		/// </summary>
		/// <returns></returns>
		DateTime? GetLastAccountBalanceDate();

		/// <summary>
		/// Vraci datum predposledni uzaverky. Pokud predposledni uzaverka neexistuje, vraci null
		/// </summary>
		/// <returns></returns>
		DateTime? GetPrevAccountBalanceDate();

		/// <summary>
		/// Existuje seznam currencyByTypeItems, ktery obsahuje stav pokladni zasuvky. Ale nejsou v nem vsechna platidla, protoze 
		/// obsahuje pouze platidla s nenulovym stavem supliku. Zde dojde k dotazeni dalsich typu platidel.
		/// </summary>
		/// <param name="currencyByTypeItems">Polozky podle typu platidla</param>
		/// <param name="currencyItems">Platidla</param>
		/// <param name="movementSign">Znamenko pohybu</param>
		IEnumerable<CurrencyByTypeItem> LoadAddCurrencyTypeItems(IEnumerable<CurrencyByTypeItem> currencyByTypeItems, IEnumerable<CashDeskCurrencyItem> currencyItems, int movementSign);

		/// <summary>
		/// Z currencyItems a cashDeskDrawerItems vytvori vystupni kolekci
		/// </summary>
		/// <param name="currencyItems">Platidla</param>
		/// <param name="cashDeskDrawerItems">Stav pokladni zasuvky</param>
		/// <param name="movementSign">Znamenko pohybu</param>
		/// <returns></returns>
		IEnumerable<CurrencyByTypeItem> LoadCurrencyByTypeItems(IEnumerable<CashDeskCurrencyItem> currencyItems, IEnumerable<CashDeskDrawerItem> cashDeskDrawerItems, int movementSign);

		/// <summary>
		/// Vklad/vyber na pokladne pouze v hotovosti
		/// </summary>
		/// <param name="appInstallationId">Id zarizeni</param>
		/// <param name="cashierId">Id prihlasene pokladni</param>
		/// <param name="deliveryDate">Datum a cas odvodu</param>
		/// <param name="note">Poznamka k odvodu</param>
		/// <param name="amount">Castka</param>
		/// <param name="fiscalMode">Je pritomen fiskalni modul?</param>
		int DepositWithdrawal(short appInstallationId, short cashierId, decimal amount, string note, DateTime deliveryDate, bool fiscalMode, SqlConnection conn = null,
			SqlTransaction trans = null);

		/// <summary>
		/// Vklad/vyber ze supliku podle platidel
		/// </summary>
		/// <param name="appInstallationId">The app installation id.</param>
		/// <param name="cashierId">Id prihlasene pokladni</param>
		/// <param name="total">Soucet vklad do �upl�ku soucet kladn�, v�b�r ze �upl�ku soucet z�porn�</param>
		/// <param name="exchangeRateDate">Datum, ze kdy je kurzovy listek</param>
		/// <param name="items">The items.</param>
		/// <returns>
		/// Id v PokladnaP
		/// </returns>
		/// <exception cref="System.ArgumentOutOfRangeException">total;Mus� b�t z�porn� ��slo.</exception>
		/// <param name="note"></param>
		/// <param name="conn"></param>
		/// <param name="fiscalMode">Je v systemu pritomen fiskal?</param>
		/// <param name="trans"></param>
		int DepositWidthdrawalEx(short appInstallationId, short cashierId, decimal total, DateTime deliveryDate, DateTime exchangeRateDate,
			IEnumerable<CashDeskCurrencyItem> items, string note, bool fiscalMode, SqlConnection conn = null, SqlTransaction trans = null);

		/// <summary>
		/// Nacte druhy platidel
		/// </summary>
		/// <returns></returns>
		IEnumerable<CurrencyTypeItem> LoadCurrencyType();

		/// <summary>
		/// Zjisteni zustatku na pokladne v jednotlivych menach
		/// </summary>
		/// <param name="appInstallationId">The app installation id.</param>
		/// <param name="accountingDate">The accounting date.</param>
		/// <returns></returns>
		IEnumerable<CashDeskDrawerItem> LoadCashDeskBalanceByCurrencyType(short appInstallationId, DateTime accountingDate);

		/// <summary>
		/// Zjisteni zustatku na pokladne
		/// </summary>
		/// <param name="appInstalationId">Id zarizeni</param>
		/// <param name="accountingDate">ucetni datum</param>
		decimal LoadCashDeskBalance(short appInstallationId, DateTime accountingDate);

		/// <summary>
		/// Zjisteni celkoveho zustatku na pokladne, prepocteno na lokalni menu
		/// </summary>
		/// <param name="appInstalationId">Id zarizeni</param>
		/// <param name="accountingDate">Ucetni datum</param>
		decimal LoadTotalCashDeskBalance(short appInstallationId, DateTime accountingDate);

		/// <summary>
		/// Vraci navrhovane datum a cas pro odvod trzby. Nejprve hleda DUZP z posledniho paragonu, pak zkontroluje, zda neni
		/// mensi nez datum posledni uzaverky a pripadne nastavi na datum posledni uzaverky
		/// </summary>
		/// <param name="appInstallationId"></param>
		/// <returns></returns>
		DateTime GetDeliveryDate(short appInstallationId);

		/// <summary>
		/// Vraci DUZP z posledniho paragonu
		/// </summary>
		/// <param name="appInstallationId"></param>
		/// <returns></returns>
		DateTime? GetLastSaleSlipDuzp(short appInstallationId);

		/// <summary>
		/// Vraci posledni pohyb z PokladnaP ze zadanych druhu pohybu
		/// </summary>
		/// <param name="appInstallationId"></param>
		/// <returns></returns>
		DateTime? GetLastPokladnaPMovement(short appInstallationId, IEnumerable<Anete.Common.Data.Interface.Enums.IdDpp> movementTypes);

		/// <summary>
		/// Nacteni platidel
		/// </summary>
		/// <param name="appInstallationId">Id zarizeni</param>
		/// <param name="date">Datum, pro ktery se ma nacist</param>
		/// <returns></returns>
		IEnumerable<CashDeskCurrencyItem> LoadCurrency(short appInstallationId, DateTime date);

		/// <summary>
		/// Odvod trzby
		/// </summary>
		/// <param name="appInstallationId">The app installation id.</param>
		/// <param name="cashierId">Id prihlasene pokladni</param>
		/// <param name="total">Soucet</param>
		/// <param name="accountingDate">The accounting date.</param>
		/// <param name="exchangeRateDate">Datum, ze kdy je kurzovy listek</param>
		/// <param name="items">The items.</param>
		/// <param name="note">The note.</param>
		/// <param name="fiscalMode">if set to <c>true</c> [fiscal mode].</param>
		/// <param name="conn">The connection.</param>
		/// <param name="trans">The trans.</param>
		/// <returns>
		/// Id v PokladnaP
		/// </returns>
		int SalesDelivery(short appInstallationId, short cashierId, decimal total, DateTime accountingDate, DateTime exchangeRateDate,
			IEnumerable<CashDeskCurrencyItem> items, string note, bool fiscalMode, SqlConnection conn = null, SqlTransaction trans = null);

	}
}

