using System;
using System.Collections.Generic;
using System.Linq;
using System.Resources;

namespace Anete.Common.Data.Interface.Entities
{
	/// <summary>
	/// Atribut pro lokalaci property skladu db sklady.
	/// </summary>
	public class StockSkladyDisplayNameAttribute : Anete.Data.Attributes.EntitySRDisplayNameAttribute
	{
		/// <summary>
		/// Initializes a new instance of the StockSkladySRDisplayNameAttribute class.
		/// </summary>
		public StockSkladyDisplayNameAttribute(string item)
			: base(item, StockSkladySR.ResourceManager)
		{

		}
	}
}
