using Anete.Config.Core;
using System;
using System.Linq;

namespace Anete.Config.Configs.Core.Global.Behaviour
{
	public static class EKasaAvailableUtils
	{
		public static bool IsEKasaAvailable(IConfigManager configManager)
		{
			// musi existovat alespon jedno z<PERSON>i, ktere ma e-kasu povolenu
			return configManager.GetAllConfigs<GlobalBehaviorEKasaConfig>().Any(c => c.EKasaMode == EKasaMode.Portos);
		}
	}
}
