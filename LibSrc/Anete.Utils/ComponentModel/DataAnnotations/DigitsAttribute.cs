using System.ComponentModel.DataAnnotations;

namespace Anete.Utils.ComponentModel.DataAnnotations
{
	/// <summary>
	/// Validační atribut pro zaj<PERSON>štění, že jsou vyplněny jenom číselné znaky.
	/// </summary>
	public class DigitsAttribute : RegularExpressionAttribute
	{
		/// <summary>
		/// Inicializace nové instance.
		/// </summary>
		/// <param name="useDefaultMessage">If set to <c>true</c> use default message.</param>
		public DigitsAttribute(bool useDefaultMessage = false)
			: base(@"^\d*$")
		{
			if (useDefaultMessage)
			{
				ErrorMessage = DigitsAttributeSR.DefaultErrorMessage;
			}
		}
	}
}
