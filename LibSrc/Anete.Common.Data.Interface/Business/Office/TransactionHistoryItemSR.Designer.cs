//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Anete.Common.Data.Interface.Business.Office {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "15.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class TransactionHistoryItemSR {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal TransactionHistoryItemSR() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Anete.Common.Data.Interface.Business.Office.TransactionHistoryItemSR", typeof(TransactionHistoryItemSR).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Účet.
        /// </summary>
        internal static string Bill {
            get {
                return ResourceManager.GetString("Bill", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Datum.
        /// </summary>
        internal static string Date {
            get {
                return ResourceManager.GetString("Date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Záloha.
        /// </summary>
        internal static string Deposit {
            get {
                return ResourceManager.GetString("Deposit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kam.
        /// </summary>
        internal static string Destination {
            get {
                return ResourceManager.GetString("Destination", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vydáno.
        /// </summary>
        internal static string Distribution {
            get {
                return ResourceManager.GetString("Distribution", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kde.
        /// </summary>
        internal static string Place {
            get {
                return ResourceManager.GetString("Place", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Symbol.
        /// </summary>
        internal static string Symbol {
            get {
                return ResourceManager.GetString("Symbol", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Čas.
        /// </summary>
        internal static string Time {
            get {
                return ResourceManager.GetString("Time", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Druh pohybu.
        /// </summary>
        internal static string TransferType {
            get {
                return ResourceManager.GetString("TransferType", resourceCulture);
            }
        }
    }
}
