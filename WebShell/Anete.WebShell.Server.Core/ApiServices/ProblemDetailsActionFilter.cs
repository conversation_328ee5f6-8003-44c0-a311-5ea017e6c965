using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc.Infrastructure;
using Microsoft.Extensions.DependencyInjection;

namespace Anete.WebShell.Server.Core.ApiServices
{
	internal class ProblemDetailsActionFilter : I<PERSON><PERSON><PERSON><PERSON><PERSON>, IOrderedFilter
	{
		public int Order => -2000;
		public bool IsReusable => true;

		public void OnActionExecuted(ActionExecutedContext context) { }
		public void OnActionExecuting(ActionExecutingContext context)
		{
			if (context.Result == null && !context.ModelState.IsValid)
			{
				var factory = context.HttpContext.RequestServices.GetRequiredService<ProblemDetailsFactory>();
				var problem = factory.CreateValidationProblemDetails(context.HttpContext, context.ModelState, instance: context.HttpContext.Request.Path);
				context.Result = new ProblemDetailsResult(problem);
			}
		}
	}
}