using System;
using System.Collections.Generic;

namespace Anete.Utils.Patterns.AppActions
{
    /// <summary>
    /// AggregatedAppActionInfo pro konkrektni typ akce a konkrektni typ invokeru.
    /// </summary>
    /// <typeparam name="TAppAction">Typ AppAction</typeparam>
    /// <typeparam name="TAppActionInvokerSupport">Typ AppActionInvokeru</typeparam>
    public class AggregatedAppActionInfo<TAppAction, TAppActionInvokerSupport> : AggregatedAppActionInfoBase where TAppAction : IAppAction, new() where TAppActionInvokerSupport: IAppActionInvokerSupport
    {
        #region constructors...
        /// <summary>
        /// Initializes a new instance of the AggregatedAppActionInfo class.
        /// </summary>
        /// <param name="id">The id.</param>
        /// <param name="requestedParams">The requested params.</param>
        /// <param name="launchRules">The launch rules.</param>
		/// <param name="optionalParams">Volitelne parametry</param>
        public AggregatedAppActionInfo(string id, IEnumerable<ParameterKey> requestedParams, IEnumerable<ParameterKey> optionalParams, IEnumerable<IAppActionLauncherFactory> launchRules)
            : base(id, requestedParams, optionalParams, launchRules)
        {
            
        }        
        #endregion

        #region protected overrides...
        /// <summary>
        /// Metoda pro zjisteni typu invokeru
        /// </summary>
        /// <returns></returns>
        protected override Type GetAppActionInvokerSupportType()
        {
            return typeof(TAppActionInvokerSupport);
        }

        /// <summary>
        /// Metoda pro vytvoreni instance akce
        /// </summary>
        /// <returns></returns>
        protected override IAppAction CreateAppActionInstanceInt(object state)
        {
            return new TAppAction();
        }

        /// <summary>
        /// Metoda pro zjisteni typu akce
        /// </summary>
        /// <returns></returns>
        protected override Type GetAppActionType()
        {
            return typeof(TAppAction);
        }        
        #endregion
    }
}
