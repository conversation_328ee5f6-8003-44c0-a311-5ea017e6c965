using Anete.Common.Core.Business.InstallationParameters.Parameters.System.SystemLicences;
using Anete.Common.Core.Interface.Business.InstallationParameters.Parameters;
using Anete.Common.Data.Nh.Entities;
using Anete.Common.Data.Nh.Interface.Services;
using NHibernate;
using System.Linq;

namespace Anete.Common.Data.Nh.Business.InstallationParameters.Parameters.System.SystemLicences
{
	public class SystemLicencesInstallationParameterFactory : ISystemLicencesInstallationParameterFactory
	{
		private readonly IKreditSimpleSessionFactory _kreditSessionProvider;
		public SystemLicencesInstallationParameterFactory(IKreditSimpleSessionFactory kreditSessionProvider)
		{
			_kreditSessionProvider = kreditSessionProvider;
		}

		public ISystemInstallationParameter CreateFromSystem()
		{
			using ISession session = _kreditSessionProvider.CreateSession();
			using ITransaction tx = session.BeginTransaction();

			var licences = session.Query<Licence>()
				.Select(x => new SystemLicenceParameterItem(x.LicenceId, x.Value, x.AppInstallationId, x.ValidTo))
				.ToArray();

			return new SystemLicencesParameter(new SystemLicencesParameterValue(licences));
		}
	}
}