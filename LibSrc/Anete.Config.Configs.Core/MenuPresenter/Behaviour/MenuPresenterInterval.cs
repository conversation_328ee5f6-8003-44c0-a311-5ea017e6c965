using System;
using System.Collections.Generic;
using Anete.Utils;
using System.ComponentModel;
using Anete.Utils.ComponentModel.TypeConverters;

namespace Anete.Config.Configs.Core.MenuPresenter.Behaviour
{
    /// <summary>
    /// Interval pro ucely Denniho menu. Jednotkou je pocet ms od zacatku daneho dne.
    /// Pouzit primo DateTime je nevhodne, protoze PropertyGrid pro nej neobsahuje vhodny editor (vzdy se nastavuje cas) a take by se zbytecne vzdy serializoval i den,
    /// ktery v tomto pripade nema zadny vyznam.
    /// </summary>
    public class MenuPresenterInterval : Interval<int>
    {
        private readonly MsTimeoutConverter _converter = new MsTimeoutConverter();
        #region constructors...
        /// <summary>
        /// Initializes a new instance of the MenuPresenterInterval class.
        /// </summary>
        public MenuPresenterInterval()
            : base(0, 0, true, true)
        {

        }        
        #endregion

        #region public overrides...                
        /// <summary>
        /// Gets or sets the start.
        /// </summary>
        /// <value>The start.</value>
        [TypeConverter(typeof(MsTimeoutConverter))]
        public override int Start {get;set;}

        /// <summary>
        /// Gets or sets the end.
        /// </summary>
        /// <value>The end.</value>
        [TypeConverter(typeof(MsTimeoutConverter))]
        public override int End {get;set;}
        #endregion

        #region protected overrides...
        /// <summary>
        /// Gets the start text.
        /// </summary>
        /// <returns></returns>
        protected override string GetStartText()
        {            
            return (string)_converter.ConvertTo(Start, typeof(string));
        }

        /// <summary>
        /// Gets the end text.
        /// </summary>
        /// <returns></returns>
        protected override string GetEndText()
        {
            return (string)_converter.ConvertTo(End, typeof(string));
        }
        #endregion

    }
}
