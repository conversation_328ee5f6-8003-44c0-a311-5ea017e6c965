//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate Fluent Mapping template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;
using FluentNHibernate.Mapping;
using FluentNHibernate.MappingModel.Collections;

namespace Anete.Common.Data.Nh.Entities
{
    /// <summary>
    /// There are no comments for MenuTemplateMap in the schema.
    /// </summary>
    public partial class MenuTemplateMap : ClassMap<MenuTemplate>
    {
        /// <summary>
        /// There are no comments for MenuTemplateMap constructor in the schema.
        /// </summary>
        public MenuTemplateMap()
        {
              Schema(@"dba");
              Table(@"JidelnicekSablony");
              DynamicInsert();
              DynamicUpdate();
              LazyLoad();
              Id(x => x.MenuTemplateId)
                .Column("IdSablona")
                .CustomType("Int32")
                .Access.Property().CustomSqlType("int")
                .Not.Nullable()
                .Precision(10)              
                .GeneratedBy.Custom("Anete.Common.Data.Nh.IdentityOrSelectMaxOnReplicationIdentityGenerator, Anete.Common.Data.Nh.40");
              Map(x => x.Name)    
                .Column("Nazev")
                .CustomType("String")
                .Access.Property()
                .Generated.Never().CustomSqlType("varchar(50)")
                .Length(50);
              Map(x => x.IsDefault)    
                .Column("Defaultni")
                .CustomType("Boolean")
                .Access.Property()
                .Generated.Never().CustomSqlType("bit");
              Map(x => x.Dow)    
                .Column("Dow")
                .CustomType("Byte")
                .Access.Property()
                .Generated.Never();
              Map(x => x.DaysCount)    
                .Column("Dnu")
                .CustomType("Byte")
                .Access.Property()
                .Generated.Never();
              References(x => x.Workplace)
                .Class<Workplace>()
                .Access.Property()
                .Cascade.None()
                .LazyLoad()
                .Columns("IdJidelna");
              HasMany<MenuTemplateRow>(x => x.Rows)
                .Access.Property()
                .AsSet()
                .Cascade.Delete()
                .LazyLoad()
                // .OptimisticLock.Version() /*bug (or missing feature) in Fluent NHibernate*/
                .Inverse()
                .Generic()
                .KeyColumns.Add("IdSablona", mapping => mapping.Name("IdSablona")
                                                                     .SqlType("int")
                                                                     .Not.Nullable());
              ExtendMapping();
        }

        #region Partial Methods

        partial void ExtendMapping();

        #endregion
    }

}
