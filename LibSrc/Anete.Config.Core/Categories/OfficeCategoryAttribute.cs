using Anete.Common.Core.Interface.Enums;
using System.Collections.Generic;
using Anete.Config.Core.Attributes;
using Anete.Utils;
using Anete.Utils.Collections.Tree;
using System;

namespace Anete.Config.Core.Categories
{
	/// <summary>
	/// Konfiguracni atribut pro Kancelar 8 urcu<PERSON><PERSON>, do jake kategorie dana konfigurace patri.
	/// </summary>
	[AttributeUsage(AttributeTargets.Property | AttributeTargets.Class, AllowMultiple = false)]
	public class OfficeCategoryAttribute : ApplicationLevelCategoryAttribute
	{
		/// <summary>
		/// Initializes a new instance of the Office8CategoryAttribute class.
		/// </summary>Office8CategoryAttribute
		public OfficeCategoryAttribute(params CategoryLevel[] categoryLevels)
			: base(ApplicationType.Office8, categoryLevels)
		{

		}		
	}
}
