using System;
using System.Collections.Generic;
using Unity;

namespace Anete.Infrastructure.Core
{
    /// <summary>
    /// Argumenty eventu v AppLifeCycle.
    /// </summary>
    public class AppLifeCycleEventArgs : EventArgs
    {
        /// <summary>
        /// Initializes a new instance of the AppLifeCycleEventArgs class.
        /// </summary>
        public AppLifeCycleEventArgs(IUnityContainer container)
        {
            this.container = container;
        }

        /// <summary>
        /// UnityContainer, ktery lze vyuzit pro registraci sluzeb
        /// </summary>
        public IUnityContainer container { get; private set; }
    }
}
