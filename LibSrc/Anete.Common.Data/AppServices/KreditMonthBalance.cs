using System;
using Anete.Common.Data.Interface.AppServices;
using System.Data.SqlClient;
using System.Collections.Generic;

namespace Anete.Common.Data.AppServicess
{
	/// <summary>
	/// Implementance IKreditMonthBalance
	/// </summary>
	public class KreditMonthBalance : IKreditMonthBalance
	{
		#region private fields...
		private readonly IDbConnectionProvider _dbConnectionProvider;
        private DateTime? _firstAvailableDate = null;
		#endregion

		#region constructors...
		/// <summary>
		/// Initializes a new instance of the KreditMonthBalance class.
		/// </summary>
		public KreditMonthBalance(IDbConnectionProvider dbConnectionProvider)
		{
			_dbConnectionProvider = dbConnectionProvider;
		}		
		#endregion

		#region IKreditMonthBalance Members
		/// <summary>
		/// Je den dostupny? Tzn. nebyl pro nej jeste vytvorena uzaverka.
		/// </summary>
		/// <param name="date"></param>
		/// <returns></returns>
		public bool IsDateAvailable(DateTime date)
		{
            if (_firstAvailableDate == null)
            {                
                using (SqlConnection conn = _dbConnectionProvider.CreateKreditConnection())
                {
                    conn.Open();
                    using (SqlCommand command = conn.CreateCommand())
                    {
                        command.CommandText = "select top 1 dateadd(d,1,DatumDo) from dba.UctyHU order by DatumDo desc";
                        _firstAvailableDate = (DateTime)command.ExecuteScalar();                        
                    }
                }
            }

            return _firstAvailableDate.Value.Date <= date.Date;
		}

        /// <summary>
        /// Prenacte data. Metoda IsDateAvailable se cachuje. Zavolanim Reload se cache vyprazdni.
        /// </summary>
        public void Reload()
        {
            _firstAvailableDate = null;
        }
        #endregion
    }
}