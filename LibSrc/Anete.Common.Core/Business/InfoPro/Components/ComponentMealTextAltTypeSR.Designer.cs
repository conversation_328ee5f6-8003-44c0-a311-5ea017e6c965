//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Anete.Common.Core.Business.InfoPro.Components {
    using System;
    
    
    /// <summary>
    /// A strongly-typed resource class, for looking up localized strings, formatting them, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilderEx class via the ResXFileCodeGeneratorEx custom tool.
    // To add or remove a member, edit your .ResX file then rerun the ResXFileCodeGeneratorEx custom tool or rebuild your VS.NET project.
    // Copyright (c) Dmytro Kryvko 2006-2020 (http://dmytro.kryvko.googlepages.com/)
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("DMKSoftware.CodeGenerators.Tools.StronglyTypedResourceBuilderEx", "2.9.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Naming", "CA1724:TypeNamesShouldNotMatchNamespaces")]
    public partial class ComponentMealTextAltTypeSR {
        
        private static global::System.Resources.ResourceManager _resourceManager;
        
        private static object _internalSyncObject;
        
        private static global::System.Globalization.CultureInfo _resourceCulture;
        
        /// <summary>
        /// Initializes a ComponentMealTextAltTypeSR object.
        /// </summary>
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        public ComponentMealTextAltTypeSR() {
        }
        
        /// <summary>
        /// Thread safe lock object used by this class.
        /// </summary>
        public static object InternalSyncObject {
            get {
                if (object.ReferenceEquals(_internalSyncObject, null)) {
                    global::System.Threading.Interlocked.CompareExchange(ref _internalSyncObject, new object(), null);
                }
                return _internalSyncObject;
            }
        }
        
        /// <summary>
        /// Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(_resourceManager, null)) {
                    global::System.Threading.Monitor.Enter(InternalSyncObject);
                    try {
                        if (object.ReferenceEquals(_resourceManager, null)) {
                            global::System.Threading.Interlocked.Exchange(ref _resourceManager, new global::System.Resources.ResourceManager("Anete.Common.Core.Business.InfoPro.Components.ComponentMealTextAltTypeSR", typeof(ComponentMealTextAltTypeSR).Assembly));
                        }
                    }
                    finally {
                        global::System.Threading.Monitor.Exit(InternalSyncObject);
                    }
                }
                return _resourceManager;
            }
        }
        
        /// <summary>
        /// Overrides the current thread's CurrentUICulture property for all
        /// resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return _resourceCulture;
            }
            set {
                _resourceCulture = value;
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Vedle sebe'.
        /// </summary>
        public static string ComponentMealTextAltType_Horizontal {
            get {
                return ResourceManager.GetString(ResourceNames.ComponentMealTextAltType_Horizontal, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Pouze jeden jazyk'.
        /// </summary>
        public static string ComponentMealTextAltType_None {
            get {
                return ResourceManager.GetString(ResourceNames.ComponentMealTextAltType_None, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Pod sebou'.
        /// </summary>
        public static string ComponentMealTextAltType_Vertical {
            get {
                return ResourceManager.GetString(ResourceNames.ComponentMealTextAltType_Vertical, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Lists all the resource names as constant string fields.
        /// </summary>
        public class ResourceNames {
            
            /// <summary>
            /// Stores the resource name 'ComponentMealTextAltType_Horizontal'.
            /// </summary>
            public const string ComponentMealTextAltType_Horizontal = "ComponentMealTextAltType_Horizontal";
            
            /// <summary>
            /// Stores the resource name 'ComponentMealTextAltType_None'.
            /// </summary>
            public const string ComponentMealTextAltType_None = "ComponentMealTextAltType_None";
            
            /// <summary>
            /// Stores the resource name 'ComponentMealTextAltType_Vertical'.
            /// </summary>
            public const string ComponentMealTextAltType_Vertical = "ComponentMealTextAltType_Vertical";
        }
    }
}
