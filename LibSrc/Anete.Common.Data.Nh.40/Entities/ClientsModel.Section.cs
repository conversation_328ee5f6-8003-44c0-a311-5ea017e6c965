//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Anete.Common.Data.Nh.Entities
{

    /// <summary>
    /// Tabulka: dba.CFUseky
    /// </summary>
    public partial class Section : Anete.Data.Nh.NhEntityBase, INotifyPropertyChanging, INotifyPropertyChanged {

        private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(System.String.Empty);

        private short _CanteenId;

        private char _SectionCode;

        private string _Name;

        private int _Width;

        private int _Height;

        private Canteen _Canteen;

        private Blob _Blob;

        private ISet<SectionObjects> _SectionObjects;
    
        #region Extensibility Method Definitions
        
        partial void OnCreated();

        public override bool Equals(object obj)
        {
          Section toCompare = obj as Section;
          if (toCompare == null)
          {
            return false;
          }

          if (!Object.Equals(this.CanteenId, toCompare.CanteenId))
            return false;
          if (!Object.Equals(this.SectionCode, toCompare.SectionCode))
            return false;
         // pokud mam default primary key, znamena to, ze jedna o nove vytvorenou entitu, ktera jeste nema key prirazen. 
        // V takove pripade musim porovnat podle instance
        bool isDefault = true;
        if (this.CanteenId != default(short))
        {
          isDefault = false;
        }
     
        if (this.SectionCode != default(char))
        {
          isDefault = false;
        }
     

          if (isDefault)
          {
            return object.ReferenceEquals(toCompare, this);
          }     
          return true;
        }

	private int? _hashCode = null;
        public override int GetHashCode()
        {
	if (_hashCode == null)
          {
          _hashCode = 13;
          _hashCode = (_hashCode * 7) + CanteenId.GetHashCode();
          _hashCode = (_hashCode * 7) + SectionCode.GetHashCode();
}
          return _hashCode.Value;
        }
        partial void OnCanteenIdChanging(short value);
        
        partial void OnCanteenIdChanged();
        partial void OnSectionCodeChanging(char value);
        
        partial void OnSectionCodeChanged();
        partial void OnNameChanging(string value);
        
        partial void OnNameChanged();
        partial void OnWidthChanging(int value);
        
        partial void OnWidthChanged();
        partial void OnHeightChanging(int value);
        
        partial void OnHeightChanged();
        partial void OnCanteenChanging(Canteen value);

        partial void OnCanteenChanged();
        partial void OnBlobChanging(Blob value);

        partial void OnBlobChanged();
        
        #endregion
        public Section()
        {
            this._SectionObjects = new HashSet<SectionObjects>();
            OnCreated();
        }

    
        /// <summary>
        /// Sloupec: id_vydejna
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("CanteenId", typeof(SectionSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(Section), Tag="CanteenId")]
        public virtual short CanteenId
        {
            get
            {
                return this._CanteenId;
            }
            set
            {
                if (this._CanteenId != value)
                {
                    this.OnCanteenIdChanging(value);
                    this.SendPropertyChanging();
                    this._CanteenId = value;
                    this.SendPropertyChanged("CanteenId");
                    this.OnCanteenIdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: usek
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("SectionCode", typeof(SectionSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(Section), Tag="SectionCode")]
        public virtual char SectionCode
        {
            get
            {
                return this._SectionCode;
            }
            set
            {
                if (this._SectionCode != value)
                {
                    this.OnSectionCodeChanging(value);
                    this.SendPropertyChanging();
                    this._SectionCode = value;
                    this.SendPropertyChanged("SectionCode");
                    this.OnSectionCodeChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: popis
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Name", typeof(SectionSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(Section), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 50,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="Name")]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(Section), Tag="Name")]
        public virtual string Name
        {
            get
            {
                return this._Name;
            }
            set
            {
                if (this._Name != value)
                {
                    this.OnNameChanging(value);
                    this.SendPropertyChanging();
                    this._Name = value;
                    this.SendPropertyChanged("Name");
                    this.OnNameChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: sirka
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Width", typeof(SectionSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(Section), Tag="Width")]
        public virtual int Width
        {
            get
            {
                return this._Width;
            }
            set
            {
                if (this._Width != value)
                {
                    this.OnWidthChanging(value);
                    this.SendPropertyChanging();
                    this._Width = value;
                    this.SendPropertyChanged("Width");
                    this.OnWidthChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: vyska
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Height", typeof(SectionSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(Section), Tag="Height")]
        public virtual int Height
        {
            get
            {
                return this._Height;
            }
            set
            {
                if (this._Height != value)
                {
                    this.OnHeightChanging(value);
                    this.SendPropertyChanging();
                    this._Height = value;
                    this.SendPropertyChanged("Height");
                    this.OnHeightChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: id_vydejna
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Canteen", typeof(SectionSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual Canteen Canteen
        {
            get
            {
                return this._Canteen;
            }
            set
            {
                if (this._Canteen != value)
                {
                    this.OnCanteenChanging(value);
                    this.SendPropertyChanging();
                    this._Canteen = value;
                    this.SendPropertyChanged("Canteen");
                    this.OnCanteenChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: id_blob
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Blob", typeof(SectionSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual Blob Blob
        {
            get
            {
                return this._Blob;
            }
            set
            {
                if (this._Blob != value)
                {
                    this.OnBlobChanging(value);
                    this.SendPropertyChanging();
                    this._Blob = value;
                    this.SendPropertyChanged("Blob");
                    this.OnBlobChanged();
                }
            }
        }

        [Anete.Data.Attributes.EntitySRDisplayName("SectionObjects", typeof(SectionSR))]
        [Anete.Data.Attributes.DbCollectionProperty]
        public virtual ISet<SectionObjects> SectionObjects
        {
            get
            {
                return this._SectionObjects;
            }
            set
            {
                this._SectionObjects = value;
            }
        }
   
        public virtual event PropertyChangingEventHandler PropertyChanging;

	// Mam v bazove tride, nema smysl generovat
	// public virtual event PropertyChangedEventHandler PropertyChanged;

        protected virtual void SendPropertyChanging()
        {
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, emptyChangingEventArgs);
        }

        protected virtual void SendPropertyChanging(System.String propertyName) 
        {   	
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, new PropertyChangingEventArgs(propertyName));
        }

        protected virtual void SendPropertyChanged(System.String propertyName)
        {    
		// mam v bazove tride, proto musim volat takto jednoduse
		OnPropertyChanged(propertyName);
/*		        var handler = this.PropertyChanged;
            if (handler != null)
                handler(this, new PropertyChangedEventArgs(propertyName));*/
        }
    }

}
