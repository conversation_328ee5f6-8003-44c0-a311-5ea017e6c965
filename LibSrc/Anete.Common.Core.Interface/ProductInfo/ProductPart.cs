using System;
using System.Diagnostics;
using System.IO;
using Anete.Utils.Configuration;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Drawing;
using Anete.Utils.Extensions;
using Anete.Utils.IO;
using Anete.Utils.Utils;

namespace Anete.Common.Core.Interface.ProductInfo
{

	/// <summary>
	/// Nastaveni jedne casti produktu. Cast produktu = jedna aplikace. 
	/// U jedne aplikace se ocekava, ze ma svou vlastni konfiguraci (.config, .log4net.config).
	/// Vyzaduje se, aby konfiguracni soubor obsahoval ApplicationSettings.
	/// </summary>
	public class ProductPart
	{

		#region constants...
		/// <summary>
		/// Extension pro soubor .exe.config
		/// </summary>
		public const string ConfigExtension = ".config";

		/// <summary>
		/// Extension pro .log4net.config
		/// </summary>
		public const string Log4NetConfigExtension = ".log4net.config";
		#endregion

		#region constructors...
		/// <summary>
		/// Initializes a new instance of the AppFileNames class.
		/// </summary>
		/// <param name="parentProduct">Produkt, pod ktery aplikace patri</param>
		/// <param name="isMaster">Ozna</param>
		public ProductPart(ProductBase parentProduct, bool isMaster, string productPartNameSuffix, bool forceLoginDialog = false)
			: this(parentProduct, isMaster, false, false, 0, productPartNameSuffix, forceLoginDialog)
		{
		}

		/// <summary>
		/// Initializes a new instance of the AppFileNames class.
		/// </summary>
		/// <param name="parentProduct">Produkt, pod ktery aplikace patri</param>
		/// <param name="isMaster">Ozna</param>
		/// <param name="isKiosk">Jedna se o bezobsluznou aplikaci?</param>
		/// <param name="platfromSpecific">Je aplikace zavisla na platforme? Tzn. existuje 32-bitova a 64-bitova verze?</param>
		/// <param name="order">Poradi v ramci produktu, muze se cislovat od 1, musi byt unikatni</param>
		public ProductPart(ProductBase parentProduct, bool isMaster, bool isKiosk, bool platfromSpecific, byte order, string productPartNameSuffix, bool forceLoginDialog = false)
		{
			ProductPartNameSuffix = productPartNameSuffix;
			ParentProduct = parentProduct;
			IsMaster = isMaster;
			ConfigFileInfo = new ConfigFileInformation(this);
			ApplicationInfo = new ApplicationInformation(this, isKiosk, platfromSpecific, forceLoginDialog);
			PathInfo = new PathInformation(this);
			Order = order;
		}
		#endregion

		#region nested classes...
		#region NestedInfoBase
		/// <summary>
		/// Zaklad pro vnorene tridy
		/// </summary>
		public class NestedInfoBase
		{

			#region constructors...
			/// <summary>
			/// Vytvori novou instanci
			/// </summary>
			internal NestedInfoBase(ProductPart productPart)
			{
				ProductPart = productPart;
			}
			#endregion

			/// <summary>
			/// Vlastnik vnorene tridy
			/// </summary>
			public ProductPart ProductPart
			{
				get;
				internal set;
			}

		}
		#endregion

		#region ConnectionStringInformation
		/// <summary>
		/// Informace o jednom connection stringu
		/// </summary>
		[DebuggerDisplay("Name={Name}, Type={ConnectionStringType}, ProductPart={ProductPart}")]
		public class ConnectionStringInformation : NestedInfoBase
		{

			#region constructors...
			/// <summary>
			/// Vytvori novou instanci
			/// </summary>
			public ConnectionStringInformation(ProductPart owner)
				: base(owner)
			{

			}
			#endregion

			#region properties...
			/// <summary>
			/// Typ Connection stringu
			/// </summary>
			public ConfigConnectionStringType ConnectionStringType { get; set; }

			/// <summary>
			/// Adapter pro nacitani a ukladani Kredit connection stringu z .exe.config
			/// </summary>
			public IConnectionStringConfigAdapter ConfigAdapter { get; set; }

			/// <summary>
			/// Nazev Connection stringu. Podstatna property, podle ni se hleda klic v settings
			/// </summary>
			public string Name { get; set; }

			/// <summary>
			/// Vraci nazev ConnectionStringu. Je  slozeno z Name + "ConnectionString"
			/// </summary>
			/// <returns></returns>
			public string ConnectionStringName
			{
				get
				{
					return Name + "ConnectionString";
				}
			}

			/// <summary>
			/// Vraci nazev ConnectionStringu v .exe.config
			/// Napr: Kasa8.Properties.Settings.KreditConnectionString
			/// </summary>
			/// <returns></returns>
			public string ConnectionStringConfigName
			{
				get
				{
					return String.Format("{0}.{1}", ProductPart.ConfigFileInfo.ApplicationSettingsSectionName, ConnectionStringName);
				}
			}
			#endregion

		}

		#endregion

		#region ConfigFileInformation
		/// <summary>
		/// Nastaveni souvisejici s konfiguracnim souborem
		/// </summary>
		public class ConfigFileInformation : NestedInfoBase
		{

			#region constructors...
			/// <summary>
			/// Vytvori novou instanci
			/// </summary>
			/// <param name="productPart"></param>
			internal ConfigFileInformation(ProductPart productPart)
				: base(productPart)
			{
				// default nastaveni
				ContainedComponents = ConfigFileComponents.ApplicationSettings;
				// ID zarizeni se jiz v Setting nenastavuje, zmenim proto toto implicitni nastaveni
				AppInstallationIdType = ConfigAppInstalationIdType.None;

				// KreditConnectionString
				KreditConnectionStringInfo = new ConnectionStringInformation(productPart);
				KreditConnectionStringInfo.Name = ConfigConnectionStringNames.Kredit;
				KreditConnectionStringInfo.ConfigAdapter = new DirectConnectionStringConfigAdapter(KreditConnectionStringInfo);
				KreditConnectionStringInfo.ConnectionStringType = ConfigConnectionStringType.Separate |
					ConfigConnectionStringType.Protected;

				// AutoUpdatesConnectionString
				// Od 2012.1 jiz neni potreba. Aplikace se dotazuji na aktualizace pomoci WCF.
				AneteUpdatesConnectionStringInfo = null;
				/*AneteUpdatesConnectionStringInfo = new ConnectionStringInformation(productPart);
				AneteUpdatesConnectionStringInfo.Name = ConfigConnectionStringNames.AneteUpdates;
				AneteUpdatesConnectionStringInfo.ConfigAdapter = new DirectConnectionStringConfigAdapter(AneteUpdatesConnectionStringInfo);
				AneteUpdatesConnectionStringInfo.ConnectionStringType = ConfigConnectionStringType.Separate |
					ConfigConnectionStringType.Protected;*/

				// SkladyConnectionString a KreditCConnectionString vetsina programu nepodporuje, proto je implicitne na null
				SkladyConnectionStringInfo = null;
				KreditCConnectionStringInfo = null;
				Sklady8ConnectionStringInfo = null;
			}
			#endregion

			#region properties...
			/// <summary>
			/// Urcuje, zda je AppInstallationId sdilene nebo nesdilene
			/// </summary>
			public ConfigAppInstalationIdType AppInstallationIdType { get; set; }

			/// <summary>
			/// Komponenty, ktere konfiguracni soubor obsahuje
			/// </summary>
			public ConfigFileComponents ContainedComponents { get; set; }

			/// <summary>
			/// Nazev cryptovaciho provideru v konfiguracnich souborech.
			/// </summary>
			public string CryptProviderName
			{
				get
				{
					return "DPAPIProtection";
				}
			}

			private string _applicationSettingsSectionName;
			/// <summary>
			/// Nazev sekce application config v konfiguracnim souboru. Pokud neni zadano, vypocita se z nazvu .exe souboru.
			/// Musi byt mozne nastavit, protoze nektere programy maji nastaveni, ktere se neda zjistit ze jmena .exe
			/// </summary>
			public virtual string ApplicationSettingsSectionName
			{
				get
				{
					return _applicationSettingsSectionName ??
						Path.GetFileNameWithoutExtension(ProductPart.PathInfo.ExeFileName) + ".Properties.Settings";
				}
				set
				{
					_applicationSettingsSectionName = value;
				}
			}

			/// <summary>
			/// Informace o KreditConnectionString.
			/// Pokud aplikace nema obsahovat KreditConnectionString, pak nastavte na null
			/// </summary>
			public ConnectionStringInformation KreditConnectionStringInfo
			{
				get
				{
					return _connectionStrings[ConfigConnectionStringNames.Kredit];
				}
				set
				{
					_connectionStrings[ConfigConnectionStringNames.Kredit] = value;
				}
			}

			/// <summary>
			/// Informace o AutoUpdatesConnectionString
			/// Pokud aplikace nema obsahovat AutoUpdatesConnectionString, pak nastavte na null
			/// </summary>
			public ConnectionStringInformation AneteUpdatesConnectionStringInfo
			{
				get
				{
					return _connectionStrings[ConfigConnectionStringNames.AneteUpdates];
				}
				set
				{
					_connectionStrings[ConfigConnectionStringNames.AneteUpdates] = value;
				}
			}

			/// <summary>
			/// Informace o SkladyConnectionString.
			/// Pokud aplikace nema obsahovat SkladyConnectionString, pak nastavte na null
			/// </summary>
			public ConnectionStringInformation SkladyConnectionStringInfo
			{
				get
				{
					return _connectionStrings[ConfigConnectionStringNames.Sklady];
				}
				set
				{
					_connectionStrings[ConfigConnectionStringNames.Sklady] = value;
				}
			}

			public ConnectionStringInformation Sklady8ConnectionStringInfo
			{
				get
				{
					return _connectionStrings[ConfigConnectionStringNames.Sklady8];
				}
				set
				{
					_connectionStrings[ConfigConnectionStringNames.Sklady8] = value;
				}
			}

			/// <summary>
			/// Informace o KreditCConnectionString.
			/// Pokud aplikace nema obsahovat KreditCConnectionString, pak nastavte na null
			/// </summary>
			public ConnectionStringInformation KreditCConnectionStringInfo
			{
				get
				{
					return _connectionStrings[ConfigConnectionStringNames.KreditC];
				}
				set
				{
					_connectionStrings[ConfigConnectionStringNames.KreditC] = value;
				}
			}

			private Dictionary<string, ConnectionStringInformation> _connectionStrings = new Dictionary<string, ConnectionStringInformation>();
			/// <summary>
			/// Connection stringy, ktere aplikace pouziva
			/// </summary>
			public Dictionary<string, ConnectionStringInformation> ConnectionStrings
			{
				get
				{
					return _connectionStrings;
				}
			}
			#endregion

			#region public methods...
			/// <summary>
			/// Vraci true, pokud konfigurace obsahuje sdilene connection stringy
			/// </summary>
			public bool ContainsProtectedConnectionString()
			{
				return ConnectionStrings.Values.Any(info => info != null &&
					info.ConnectionStringType.Contains(ConfigConnectionStringType.Protected));
			}

			// Jiz neni potreba. Pripojeni k databazi automatickych aktualizaci je v Konfiguratoru.
			/*
			/// <summary>
			/// Nastavi pripojeni k DB AneteUpdates.
			/// Driv bylo nastaveno u kazde aplikace, nove vola pouze AutoUpdater.
			/// </summary>
			public void SetAneteUpdatesConfigFileInfo()
			{
				AneteUpdatesConnectionStringInfo = new ConnectionStringInformation(ProductPart)
				{
					Name = ConfigConnectionStringNames.AneteUpdates,
					ConnectionStringType = ConfigConnectionStringType.Shared | ConfigConnectionStringType.Protected
				};
				AneteUpdatesConnectionStringInfo.ConfigAdapter = new DirectConnectionStringConfigAdapter(AneteUpdatesConnectionStringInfo);
			}*/
			#endregion
		}
		#endregion

		#region ApplicationInformation
		/// <summary>
		/// Udaje o aplikaci
		/// </summary>
		public class ApplicationInformation : NestedInfoBase
		{

			#region constructors...
			/// <summary>
			/// Vytvori novou instanci
			/// </summary>
			/// <param name="owner">The owner.</param>
			/// <param name="isKiosk">Jedna se o bezobsluznou aplikaci (pm, denni menu)?</param>					
			/// <param name="platfromSpecific">Je aplikace zavisla na platforme? Tzn. existuje 32-bitova a 64-bitova verze?</param>
			internal ApplicationInformation(ProductPart owner, bool isKiosk, bool platfromSpecific, bool forceLoginDialog)
				: base(owner)
			{
				ForceLoginDialog = forceLoginDialog;
				PlatformSpecific = platfromSpecific;
				IsKiosk = isKiosk;
			}
			#endregion

			#region properties...
			/// <summary>
			/// Nazev aplikace, pouzit jako klic pri praci s ProductPart. Dle nej se napr. urcuje slozka pro logovani.
			/// Zde se obvykle dava nazev aplikace bez pripony .exe, napr. Kasa8
			/// </summary>
			public string Name { get; set; }

			private string _title;
			/// <summary>
			/// Titulek aplikace pro zobrazovani. Pokud neni vyplneno, vraci AppName
			/// </summary>
			public string Title
			{
				get { return string.IsNullOrEmpty(_title) ? Name : _title; }
				set
				{
					_title = value;
				}
			}

			/// <summary>
			/// Zformatovany titulek s verzi a copyrightem
			/// </summary>
			public string TitleWithVersionAndCopyright
			{
				get
				{
					return ProductPartSR.ApplicationTitleWithVersionAndCopyrightFormat(Title, ProductAssemblyInfo.Version,
						ProductAssemblyInfo.Copyright);
				}
			}

			/// <summary>
			/// Zformatovany titulek s verzi a copyrightem
			/// </summary>
			public string TitleWithVersion
			{
				get { return ProductPartSR.ApplicationTitleWithVersionFormat(Title, ProductAssemblyInfo.Version); }
			}

			/// <summary>
			/// Ikona pridruzena k aplikaci.
			/// Pouziva se pro zobrazovani ikon v instalacnim a odinstalacnim dialogu
			/// </summary>
			public Icon Icon { get; set; }

			/// <summary>
			/// Ikona pridruzena k aplikaci prevedena na bitmapu 24x24
			/// </summary>
			public Image Glyph
			{
				get
				{
					if (Icon == null)
					{
						return null;
					}

					return Icon.ToBitmap().GetThumbnailImage(24, 24, null, IntPtr.Zero);
				}
			}

			/// <summary>
			/// Ikona pridruzena k aplikaci prevedena na bitmapu 32x32
			/// </summary>
			public Image LargeGlyph
			{
				get
				{
					if (Icon == null)
					{
						return null;
					}

					return Icon.ToBitmap();
				}
			}

			/// <summary>
			/// Image, ktery se pouziva pri startu programu jako splash screen
			/// </summary>
			public Image StartupSplashImage { get; set; }

			/// <summary>
			/// Jedna se o bezobsluznou aplikaci (pm, denni menu)?
			/// </summary>
			public bool IsKiosk { get; set; }

			private Assembly _productAssembly;
			/// <summary>
			/// Vraci Assembly, ve ktere je definovana verze a nazev produktu. Vetsinou to byva spoustejici exe. 
			/// U aplikaci odvozenych z Shell vsak nepotrebuji znat verzi Shell.exe, ale jedne z knihoven,
			/// ktere definuji produkt. 
			/// </summary>
			public Assembly ProductAssembly
			{
				get
				{
					// nebylo-li zadano explicitne, bereme exe soubor
					if (_productAssembly == null)
					{
						string assemblyName = Path.GetFileNameWithoutExtension(ProductPart.PathInfo.ExeFilePath);
						_productAssembly = AppDomain.CurrentDomain.GetAssemblies()
							.SingleOrDefault(asm => asm.GetName().Name == assemblyName);

						if (_productAssembly == null)
						{
							throw new InvalidProgramException(string.Format("Cannot find ProductAssembly for '{0}'", assemblyName));
						}
					}

					return _productAssembly;
				}
				set
				{
					_productAssembly = value;
				}
			}

			private AsmInfo _productAssemblyInfo;
			/// <summary>
			/// Informace o Assembly, ktera spousti dany produkt
			/// </summary>
			public virtual AsmInfo ProductAssemblyInfo
			{
				get
				{
					if (_productAssemblyInfo == null)
					{
						_productAssemblyInfo = new AsmInfo(ProductAssembly);
					}

					return _productAssemblyInfo;
				}
			}

			/// <summary>
			/// Je aplikace zavisla na platforme? Tzn. existuje 32-bitova a 64-bitova verze?
			/// </summary>
			public bool PlatformSpecific { get; private set; }

			/// <summary>
			/// Bude pro tuto cast aplikace vyzadovan login dialig i prestoze dany typ aplikace jej nepotrebuje?
			/// 
			/// Napriklad ctecky obsahuji monitor a sluzbu - ty dialog nepotrebuji. Nicmene konfigurator ctecek jej vyzaduje.
			/// </summary>
			public bool ForceLoginDialog { get; }
			#endregion

		}
		#endregion

		#region PathInformation
		/// <summary>
		/// informace o souborech a cestach
		/// </summary>
		public class PathInformation : NestedInfoBase
		{

			#region constructors...
			/// <summary>
			/// Vytvori novou instanci
			/// </summary>
			internal PathInformation(ProductPart owner)
				: base(owner)
			{

			}
			#endregion

			#region properties...
			/// <summary>
			/// Bazovy adresar aplikace od nehoz se odvozuje cesta ke vsem souborum a adresarum.
			/// </summary>
			public virtual string BaseDir { get; set; }

			/// <summary>
			/// Bin adresar aplikace
			/// </summary>
			public string BinDir
			{
				get
				{
					return Path.Combine(BaseDir, ProductPart.ParentProduct.RelativeBinPath);
				}
			}

			/// <summary>
			/// Temp adresar aplikace
			/// </summary>
			public string TempDir
			{
				get
				{
					return Path.Combine(BaseDir, ProductPart.ParentProduct.RelativeTempPath);
				}
			}

			private string _exeFilePath;
			/// <summary>
			/// Cesta k exe souboru
			/// </summary>
			public string ExeFilePath
			{
				get
				{
					return Path.Combine(BinDir, _exeFilePath);
				}
			}

			/// <summary>
			/// Pouze nazev spustitelneho souboru. Bez cesty.
			/// </summary>
			public string ExeFileName
			{
				get
				{
					return Path.GetFileName(_exeFilePath);
				}
				set
				{
					// toto vypada divne, ale je to tak zamysleno. Do teto promenne se priradi nazev .exe pri konfiguraci
					// PathInformation
					_exeFilePath = value;
				}
			}

			/// <summary>
			/// Cesta ke konfiguracnimu souboru
			/// </summary>
			public string ConfigFilePath
			{
				get
				{
					return PathUtils.AddExtension(ExeFilePath, ConfigExtension);
				}
			}

			/// <summary>
			/// Nazev konfiguracniho souboru
			/// </summary>
			public string ConfigFileName
			{
				get
				{
					return PathUtils.AddExtension(ExeFileName, ConfigExtension);
				}
			}

			/// <summary>
			/// Cela cesta k Log4Net konfiguracnimu souboru
			/// </summary>
			public string Log4NetConfigFilePath
			{
				get
				{
					return PathUtils.AddExtension(ExeFilePath, Log4NetConfigExtension);
				}
			}

			/// <summary>
			/// Nazev log4net konfiguracniho souboru
			/// </summary>
			public string Log4NetConfigFileName
			{
				get
				{
					return PathUtils.AddExtension(ExeFileName, Log4NetConfigExtension);
				}
			}

			/// <summary>
			/// Cesta k logovacimu adresari. Na tuto cestu se nastavuje Log4Net konfigurace.
			/// </summary>
			/// <remarks>
			/// Pokud produkt obsahuje pouze jednu aplikaci, ukladaji se logy do slozky s nazvem teto aplikace.
			/// V pripade vetsiho mnozstvi aplikaci je pro kazdou z nich vytvorena zvlastni slozka pro logovani.
			/// </remarks>
			public virtual string LogDirectoryPath
			{
				get
				{
					if (ProductPart.ParentProduct.ProductParts.Count > 1)
					{
						// vice aplikaci v Product
						return Path.Combine("{commonappdata}", String.Format(@"Anete\Logs\{0}\{1}", ProductPart.ParentProduct.ProductName,
							ProductPart.ApplicationInfo.Name));
					}
					else
					{
						// pouze jedna aplikace
						return Path.Combine("{commonappdata}", String.Format(@"Anete\Logs\{0}", ProductPart.ParentProduct.ProductName));
					}
				}
			}
			#endregion

			#region public methods...
			/// <summary>
			/// Vraci cestu k souboru v Bin adresari aplikace
			/// </summary>
			/// <param name="fileName"></param>
			/// <returns></returns>
			public string GetBinFilePath(string fileName)
			{
				return Path.Combine(BinDir, fileName);
			}

			/// <summary>
			/// Vraci cestu k souboru v Temp adresari aplikace
			/// </summary>
			/// <param name="fileName"></param>
			/// <returns></returns>
			public string GetTempFilePath(string fileName)
			{
				return Path.Combine(TempDir, fileName);

			}
			#endregion

		}
		#endregion

		#endregion

		#region public properties...
		/// <summary>
		/// Produkt, pod ktery tato aplikace patri
		/// </summary>
		public ProductBase ParentProduct { get; private set; }

		/// <summary>
		/// Komponenty, ktere aplikace obsahuje
		/// </summary>
		public ProductPartComponents ContainedComponents { get; set; }

		/// <summary>
		/// Nastaveni konfiguracniho souboru
		/// </summary>
		public ConfigFileInformation ConfigFileInfo { get; private set; }

		/// <summary>
		/// Nastaveni aplikace
		/// </summary>
		public ApplicationInformation ApplicationInfo { get; private set; }

		/// <summary>
		/// Informace o cestach
		/// </summary>
		public PathInformation PathInfo { get; private set; }

		/// <summary>
		/// Pokud Product obsahuje vice ProductPart, pak tato promenna oznacuje hlavni cast aplikace. 
		/// Hlavni cast aplikace potrebuji znat pri instalaci, odinstalaci atd.
		/// Pokud obsahuje Product nekolik ProductPart se sdilenou konfiguraci, jeden z nich musi byt oznaceny jako Master
		/// </summary>
		public bool IsMaster { get; private set; }

		/// <summary>
		/// Poradi v ProductPart v ramci celeho Product. Pouziva se pro zapis do tabulky SWPocitaceDetail.
		/// Musi byt nemenne pro dany ProductPart.
		/// </summary>
		public byte Order { get; private set; }

		/// <summary>
		/// Suffix k nazvu produktu. Napriklad pro monitor aktualizaci je suffix Monitor. Hlavni produkt je AutoUpdater. Vysledny nazev by tedy mel byt AutoUpdaterMonitor.
		/// Pokud je null, neni zadny suffix pouzivan a predpoklada se, ze aplikace ma jen jeden exe soubor.
		/// </summary>
		public string ProductPartNameSuffix { get; private set; }
		#endregion

		#region public methods...
		/// <summary>
		/// Inicializace ConfigFileManager - tento objekt by se mel pouzivat misto ConfigUpdater
		/// </summary>
		/// <returns></returns>
		public virtual ConfigFileManager CreateConfigFileManager()
		{
			return new ConfigFileManager(ConfigFileInfo.ApplicationSettingsSectionName, PathInfo.ConfigFilePath);
		}

		/// <summary>
		/// Initializes the specified base dir.
		/// </summary>
		/// <param name="baseDir">The base dir.</param>
		public void Initialize(string baseDir)
		{
			PathInfo.BaseDir = baseDir;
		}
		#endregion

		#region public overrides...
		/// <summary>
		/// Returns a <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.
		/// </summary>
		/// <returns>
		/// A <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.
		/// </returns>
		public override string ToString()
		{
			return ApplicationInfo.Name;
		}
		#endregion
	}
}
