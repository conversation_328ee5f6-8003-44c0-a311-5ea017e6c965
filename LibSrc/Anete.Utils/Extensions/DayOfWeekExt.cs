using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;

namespace Anete.Utils.Extensions
{
	
	/// <summary>
	/// Extension metody pro DayOfWeek
	/// </summary>
	public static class DayOfWeekExt
	{

		/// <summary>
		/// Vraci dny v tydnu setridene tak, aby prvni byl den v tydnu, ktery odpovida aktualni kulture
		/// </summary>
		/// <returns></returns>
		public static IEnumerable<DayOfWeek> GetValuesSorted()
		{
			return Thread.CurrentThread.CurrentCulture.DateTimeFormat.GetWeekDaysSorted();
		}

		public static string GetLocalizedName(this DayOfWeek dayOfWeek)
		{
			return DateTimeUtils.GetNameOfDay(dayOfWeek);
		}

		/// <summary>
		/// Vraci poradove cislo dne
		/// </summary>
		/// <param name="dayOfWeek"></param>
		/// <returns></returns>
		public static int GetOrder(this DayOfWeek dayOfWeek)
		{
			return GetValuesSorted().ElementIndex(dayOfWeek);
		}
	}
}
