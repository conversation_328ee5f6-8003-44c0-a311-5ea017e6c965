using Anete.Common.Core.Business.InstallationParameters.Groups;
using Anete.Common.Core.Interface.Business.InstallationParameters.Groups;
using Anete.Common.Core.Interface.Business.InstallationParameters.Parameters;
using Anete.Common.Core.Interface.Business.InstallationParameters.ReportGroups;
using System.Collections.Generic;
using Unity;

namespace Anete.Common.Core.Business.InstallationParameters.Parameters.System.SystemActiveWebClients
{
	public class SystemActiveWebClientsDesc : ContainerBasedInstallationParameterDescBase<SystemActiveWebClientsFactory, SystemActiveWebClientsValueBasedFactory, SystemActiveWebClientsSerializer, NotReportedInstalationParameterReportGroup>
	{
		public SystemActiveWebClientsDesc(IUnityContainer container, InstallationParameterGroupFactory groupFactory)
			: base(container, InstallationParameterTargets.System, Interface.Enums.InstallationParameterId.SystemActiveWebClients, groupFactory)
		{ }

		protected override IEnumerable<InstallationParameterGroupId> GetGroupsInt()
		{
			yield return InstallationParameterGroupId.SystemInfo;
		}

		protected override string GetDescriptionInt()
		{
			return "Celkový počet povolených strávníků v systému pro web.";
		}
	}
}