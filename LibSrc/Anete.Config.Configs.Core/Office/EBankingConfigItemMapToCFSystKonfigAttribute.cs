using System;
using System.Collections.Generic;
using System.Text;
using Anete.Config.Core.CFSystKonfig;
using Anete.Utils;
using System.Linq;
using Anete.Utils.Extensions;
using Anete.Utils.Collections;

namespace Anete.Config.Configs.Core.Office
{
    /// <summary>
    /// Mapovani polozky do CFSystKonfig.
    /// Toto je specialni mapovani. Mapuje pole polozek EBankingConfigItem na vice polozek CFSystKonfig. Mapovani probiha tak, 
    /// ze polozka pole s indexem 0 se mapuje na "HomeBanking", polozka 1 na "HomeBanking1" atd.
    /// Pritom polozka s indexem 0 existuje v CFSystKonfig stale, ostatni polozky se mazou nebo pridavaji v zavislosti na tom, 
    /// zda existuje polozka pole s danym indexem.
    /// Pokud je pole prazdne, ulozi se pouze polozka "HomeBanking" a v nastaveni ma prazdny retezec
    /// </summary>
    public class EBankingConfigItemMapToCFSystKonfigAttribute : TypedMapToCFSystKonfigBaseAttribute
    {

        #region private static fields...
        /// <summary>
        /// Mapovani mezi Enumem a hodnotou CFSystKonfig
        /// </summary>
        private readonly static Dictionary<EBankingType, string> _ebankingTypeToCFSystKonfigMap =
            new Dictionary<EBankingType, string>()
            {
                {EBankingType.BestKb, "BESTKB"},
                {EBankingType.BestKbFormatIkm, "BESTKB_IKM"},
                {EBankingType.CeskaSporitelna, "CSAS"},
                {EBankingType.CsobBusinessBanking24, "CSOBBB24"},
                {EBankingType.CsobMultiCash940, "CSOBMT940"},
                {EBankingType.KbFormatKm, "KB_KM"},
                {EBankingType.InfSystemStatPoklSr, "ISŠPSR"},
                {EBankingType.CnbOsu, "OSU_CNB"},
                {EBankingType.CeskaPostaOsu, "OSU_CP"},
				{EBankingType.SlovenskaSporitelnaAbo, "SSP_ABO"},
				{EBankingType.FioFormatGpc, "FIO_GPC"},
				// UnicreditBank je podporovana az od Kan8, z toho duvodu nema toto mapovani smysl.
				// Nicmene neni mozne se ho nejak jednoduse zbavit.
				{EBankingType.UnicreditMultiCashStruct, "MULTI_UNI"},
				{EBankingType.VolksbankAbo, "VOLKSBANK_ABO"},
				{EBankingType.Iso20022, "ISO20022"},
				{EBankingType.Vub, "VUB_ABO"},
                {EBankingType.FioAbo, "FIO_ABO"},
				{EBankingType.CsGeorge, "CS_GEORGE"},
            };
        #endregion

        #region constructors...
        /// <summary>
        /// Initializes a new instance 
        /// </summary>
        /// <param name="key">The key.</param>
        /// <param name="option">The option.</param>
        /// <param name="nazev">The nazev.</param>
        /// <param name="opravneni">The opravneni.</param>
        /// <param name="typ">The typ.</param>
        /// <param name="poznamka">The poznamka.</param>
        /// <param name="poradi">The poradi.</param>
        /// <param name="funkceZarizeni">The funkce zarizeni.</param>
        public EBankingConfigItemMapToCFSystKonfigAttribute(string key, MapToCFSystKonfigOption option, string nazev,
            byte opravneni, char typ, string poznamka, int poradi, string funkceZarizeni)
            : base(key, option, nazev, opravneni, typ, poznamka, poradi, funkceZarizeni, typeof(EBankingConfigItem[]))
        {
            RequestCFSystKonfigValue = false;
        }
        #endregion

        #region protected overrides...
        /// <summary>
        /// Bude se ukladat hodnota do CFSystKonfig?
        /// Vytvoreno jako virtualni, aby nebylo nutne upravovat stavajici konfiguracni tridy.
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        protected override bool GetSaveValue(object obj)
        {
            EBankingConfigItem[] items = (EBankingConfigItem[])obj;
            // ukladat budu, pokud dana hodnota existuje nebo pokud jde o polozku 0. V Order je ulozen index polozky
            return Order == 0 || items.Length - 1 >= Order;
        }

        /// <summary>
        /// Vraci textovou hodnotu, ktera se ma ulozit do CFSystKonfig.
        /// Volano z GetCFSystKonfigValue. Je zajisteno, ze v teto metode bude mit parametr <c>obj</c> pozadovany type.
        /// </summary>
        /// <param name="obj">Konfiguracni objekt, z nehoz se ziska hodnota pro ulozeni do CFSystKonfig</param>
        /// <returns>
        /// Stringova hodnota, ktera se ma ulozit do CFSystKonfig
        /// </returns>
        protected override string GetTypedCFSystKonfigValue(object obj)
        {
            EBankingConfigItem[] items = (EBankingConfigItem[])obj;
            
            // pokud se jedna o polozku s indexem 0 a neni definovan zadny banking, ulozime prazdny retezec
            // Polozku s indexem 0 asi kancelar potrebuje 
            if (Order == 0 && items.Length == 0) 
            {
                return string.Empty;
            }

            EBankingConfigItem item = items[Order];
            KeyValueTokens tokens = new KeyValueTokens();
            tokens.Add("Typ", _ebankingTypeToCFSystKonfigMap[item.EBankingType]);

            if (item.Settings != null)
            {
                item.Settings.GetCFSystKonfigTokens(tokens);
            }

            return tokens.ToString();
        }

        /// <summary>
        /// Nastavi objekt <c>obj</c> tak, aby odpovidal hodnote v CFSystKonfig.
        /// Volano z SetValueFromCFSystKonfig. Je zajisteno, ze v teto metode bude mit parametr <c>obj</c> pozadovany type.
        /// </summary>
        /// <param name="obj">Konfiguracni objekt, jehoz hodnota se nastavuje podle CFSystKonfig</param>
        /// <param name="cfSystKonfigValue">Hodnota ulozena v CFSystKonfig</param>
        protected override void SetTypedValueFromCFSystKonfig(ref object obj, string cfSystKonfigValue)
        {
            // pokud hodnota v CFSystKonfig neexistuje, neprovedu zadne nastaveni
            if (!CFSystKonfigValueExists || string.IsNullOrEmpty(cfSystKonfigValue))
            {
                return;
            }

            EBankingConfigItem[] items = (EBankingConfigItem[])obj;
            KeyValueTokens tokens = new KeyValueTokens(";");
            // Kak: 7.7.2009 Toto nesmyslnou upravu vyzaduje ViS - neni schopen zarovnat hodnoty pri exportu
            tokens.TrimValuesWhileParsing = false;
            tokens.Parse(cfSystKonfigValue);

            if (Order > items.Length - 1)
            {
                // index muze byt vetsi maximalne o jedna
                if (Order > items.Length)
                {
                    throw new ArgumentException(TypedMapToCFSystKonfigBaseAttributeSR.InvalidMapOrderFormat(Order, Order - 1));
                }

                // musim rozsirit pole
                Array.Resize(ref items, items.Length + 1);
            }

            // zalozeni nove polozky
            EBankingConfigItem newItem = new EBankingConfigItem();
            string eBankingType = tokens.Get<string>("Typ");
            newItem.EBankingType = _ebankingTypeToCFSystKonfigMap.GetKey(value => value.MatchTrimTo(eBankingType));

            if (newItem.Settings != null)
            {
                newItem.Settings.ParseCFSystKonfigTokens(tokens);
            }

            items[Order] = newItem;

            // pri Array.Resize se vytvari nove pole, musim na nej spravne nastavit referenci
            obj = items;
        }
        #endregion

    }
}
