using System;
using System.Collections.Generic;
using System.Linq;
using Anete.Utils.Extensions;

namespace Anete.Common.Data.Interface.AppServices.Campaign
{

	/// <summary>
	/// Otazka v ramci dane kampane
	/// </summary>
	public class CampaignQuestion
	{
		#region constructors...
		public CampaignQuestion(int campaignId, int questionId, string name, bool renderAsRating,
			CampaignQuestionDateInfo dateInfo,
			CampaignQuestionCanteenInfo canteenInfo,
			CampaignQuestionMealInfo mealInfo,
			params CampaingQuestionAnswer[] answers)
		{
			MealInfo = mealInfo;
			CanteenInfo = canteenInfo;
			DateInfo = dateInfo;
			QuestionId = questionId;
			CampaignId = campaignId;
			Answers = answers;
			Name = name;
			RenderAsRating = renderAsRating;
		}
		#endregion

		#region public properties...				
		public string Name { get; private set; }

		/// <summary>
		/// Odpovedi, serazene ve spravnem poradi
		/// </summary>
		public CampaingQuestionAnswer[] Answers { get; private set; }

		/// <summary>
		/// Zobrazit otazku pomoci hvezdicek?
		/// </summary>
		public bool RenderAsRating { get; private set; }		

		/// <summary>
		/// Id kampane
		/// </summary>
		public int CampaignId { get; private set; }

		/// <summary>
		/// Id otazky
		/// </summary>
		public int QuestionId { get; private set; }

		/// <summary>
		/// Pokud se otazka vztahuje k danemu datu, je vyplneno
		/// </summary>
		public CampaignQuestionDateInfo DateInfo { get; private set; }

		/// <summary>
		/// Pokud se otazka vztahuje k vydejne, je vyplneno
		/// </summary>
		public CampaignQuestionCanteenInfo CanteenInfo { get; private set; }

		/// <summary>
		/// Pokud je otazka svazana s jidlem, je vyplneno.
		/// </summary>
		public CampaignQuestionMealInfo MealInfo { get; private set; }

		/// <summary>
		/// Pokud otazka byla zodpovezena bude vyplnene podle Id zvolenej odpovede
		/// </summary>
		public int? AnswerId { get; set; } = null;

		/// <summary>
		/// Pokud otazka byla zodpovezena bude vyplnene ANK_Data.Id
		/// </summary>
		public int? Answer { get; set; } = null;
		#endregion

		#region public overrides...
		public override string ToString()
		{
			return string.Format("'{0}' (odpovedi:{1}) (Datum={2}, Vydejna={3}, Jidlo={4})", Name, Answers.Select(a => a.Name).ToCommaSpaceDelimitedString(),
				DateInfo != null ? DateInfo.Date : (DateTime?)null, CanteenInfo != null ? CanteenInfo.CanteenId : (short?)null,
				MealInfo != null ? MealInfo.MenuRowId : (int?)null);
		}
		#endregion
	}
}