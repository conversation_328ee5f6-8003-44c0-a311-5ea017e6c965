using System;
using Microsoft.Practices.EnterpriseLibrary.Validation.Validators;
using Microsoft.Practices.EnterpriseLibrary.Validation;

namespace Anete.Common.Core.Interface.Validators
{
    /// <summary>
    /// Describes a <see cref="NotNullValidator"/>.
    /// </summary>
    [AttributeUsage(AttributeTargets.Property
        | AttributeTargets.Field
        | AttributeTargets.Method
        | AttributeTargets.Class
        | AttributeTargets.Parameter,
        AllowMultiple = true,
        Inherited = false)]
    public sealed class LocalizableNotNullValidatorAttribute : LocalizableValueValidatorAttribute
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="LocalizableNotNullValidatorAttribute"/> class.
        /// </summary>
        /// <param name="validatedClassType">Trida, nad kterou validace probiha. Zjistuji se z ni nazvy property.</param>
        public LocalizableNotNullValidatorAttribute(Type validatedClassType)
            : base(validatedClassType)
        {

        }

        /// <summary>
        /// Creates the <see cref="NotNullValidator"/> described by the attribute object.
        /// </summary>
        /// <param name="targetType">The type of object that will be validated by the validator.</param>
        /// <returns>The created <see cref="NotNullValidator"/>.</returns>
        protected override Validator DoCreateValidator(Type targetType)
        {
            return new LocalizableNotNullValidator(ValidatedClassType, Negated) { PredefinedCaption = PredefinedCaption};
        }
    }

}