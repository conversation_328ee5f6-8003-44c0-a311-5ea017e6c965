//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate Fluent Mapping template.
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;
using FluentNHibernate.Mapping;
using FluentNHibernate.MappingModel.Collections;

namespace Anete.Common.Data.Nh.Entities
{
  public partial class ZarizeniKVypujcce
  {
               /// <summary>
		       /// Nastavi novou hodnotu navigacni property se zachovanim konzistentnich udaju ve vazebnich kolekcich
		       /// </summary>
           public virtual void SetUsBudova(UsBudova value)
           {
              UsBudova oldValue = UsBudova;
              UsBudova = value;

              // uprava vazby
              if (value != null)
              {
                value.ZarizeniKVypujcces.Add(this);
              }
              if (oldValue != null)
              {
                oldValue.ZarizeniKVypujcces.Remove(this);
              }
           }
                      /// <summary>
		       /// Nastavi novou hodnotu navigacni property se zachovanim konzistentnich udaju ve vazebnich kolekcich
		       /// </summary>
           public virtual void SetCenikovePolozky(CenikovePolozky value)
           {
              CenikovePolozky oldValue = CenikovePolozky;
              CenikovePolozky = value;

              // uprava vazby
              if (value != null)
              {
                value.ZarizeniKVypujcces.Add(this);
              }
              if (oldValue != null)
              {
                oldValue.ZarizeniKVypujcces.Remove(this);
              }
           }
                      /// <summary>
		       /// Nastavi novou hodnotu navigacni property se zachovanim konzistentnich udaju ve vazebnich kolekcich
		       /// </summary>
           public virtual void SetCenikovePolozky_idPolozkyKauce(CenikovePolozky value)
           {
              CenikovePolozky oldValue = CenikovePolozky_idPolozkyKauce;
              CenikovePolozky_idPolozkyKauce = value;

              // uprava vazby
              if (value != null)
              {
                value.ZarizeniKVypujcces_idPolozkyKauce.Add(this);
              }
              if (oldValue != null)
              {
                oldValue.ZarizeniKVypujcces_idPolozkyKauce.Remove(this);
              }
           }
             }
}
