<?xml version="1.0" encoding="utf-8"?>
<EntityDeveloperDiagram>
  <Diagram Version="1.20.0.98">
    <DiagramModel>
      <Model xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xsi:type="ContextVwModel">
        <CustomProperties>
          <OID>0</OID>
          <BackgroundColor>Window</BackgroundColor>
        </CustomProperties>
        <Children>
          <Model xsi:type="EnumTypeVwModel">
            <CustomProperties>
              <OID>1</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>2</OID>
                  <Parent>1</Parent>
                </CustomProperties>
                <Children />
                <Size>
                  <Width>203 px</Width>
                  <Height>0 px</Height>
                </Size>
              </Model>
            </Children>
            <Location>
              <X>304 px</X>
              <Y>-392 px</Y>
            </Location>
            <Size>
              <Width>208 px</Width>
              <Height>48 px</Height>
            </Size>
            <MinSize>
              <Width>100 px</Width>
              <Height>48 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>48 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>2aa5e230-f714-4dbf-80eb-d70381586f15</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateEnumType</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="EnumTypeVwModel">
            <CustomProperties>
              <OID>3</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>4</OID>
                  <Parent>3</Parent>
                </CustomProperties>
                <Children />
                <Size>
                  <Width>203 px</Width>
                  <Height>0 px</Height>
                </Size>
              </Model>
            </Children>
            <Location>
              <X>296 px</X>
              <Y>-488 px</Y>
            </Location>
            <Size>
              <Width>208 px</Width>
              <Height>48 px</Height>
            </Size>
            <MinSize>
              <Width>100 px</Width>
              <Height>48 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>48 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>5623bf64-9200-488a-8ad1-543a6b4e79f8</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateEnumType</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>5</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>6</OID>
                  <Parent>5</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>7</OID>
                      <Parent>6</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>250 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>e9bb3537-ebcb-44b5-88ab-4df1c8337277</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>8</OID>
                      <Parent>6</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>250 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>9eeb5d07-f2bb-4d83-8140-3f5374fcf04b</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>9</OID>
                      <Parent>6</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>250 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>2629d47e-4716-437f-8edb-27cdc44169c7</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>10</OID>
                      <Parent>6</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>54.5 px</Y>
                    </Location>
                    <Size>
                      <Width>250 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>1083ecff-fd1d-48ec-8c5f-9606e65912ea</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>251 px</Width>
                  <Height>73 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>73 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>11</OID>
                  <Parent>5</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>12</OID>
                      <Parent>11</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>250 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>28f3baa7-4352-4b74-8a66-5642d3e18be3</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>13</OID>
                      <Parent>11</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>18 px</Y>
                    </Location>
                    <Size>
                      <Width>250 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>51332983-8efb-448b-b816-51c1e755d52a</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>14</OID>
                      <Parent>11</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>36 px</Y>
                    </Location>
                    <Size>
                      <Width>250 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>b8484d0f-1ea7-4c57-b6ef-64a2af06edb5</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>72 px</Y>
                </Location>
                <Size>
                  <Width>251 px</Width>
                  <Height>73 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>73 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>-24 px</X>
              <Y>-488 px</Y>
            </Location>
            <Size>
              <Width>256 px</Width>
              <Height>194 px</Height>
            </Size>
            <MinSize>
              <Width>100 px</Width>
              <Height>194 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>194 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>0da6066d-2ea4-4cba-9d08-20cf06067a84</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>15</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>16</OID>
                  <Parent>15</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>17</OID>
                      <Parent>16</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>f872a401-ec4d-4857-b784-c6e1546dc6a6</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>18</OID>
                      <Parent>16</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>88ccc239-6a96-4db3-aeb0-91ef3376290b</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>19</OID>
                      <Parent>16</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>ffe3ef29-f3ed-404a-b790-9f61d5058672</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>20</OID>
                      <Parent>16</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>54.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>3be457c0-8ad9-4833-bb78-c21a46e653d1</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>21</OID>
                      <Parent>16</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>72.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>0c0ea8e1-31d9-4a8d-b70e-b1b8c7dbd995</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>22</OID>
                      <Parent>16</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>90.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>e80965cf-eb28-45a5-b9fe-0bbf17215bcc</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>23</OID>
                      <Parent>16</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>108.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>b2b59bb4-b309-4e14-b0e9-b286e2bd6105</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>145 px</Width>
                  <Height>127 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>127 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>24</OID>
                  <Parent>15</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>25</OID>
                      <Parent>24</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>30877f23-64a5-4eb6-a418-b4dcc1b98db1</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>26</OID>
                      <Parent>24</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>18 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>5ade1055-382b-46d5-93ec-f679c81e70d8</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>27</OID>
                      <Parent>24</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>36 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>a82a8778-9f26-4f5d-8de6-423258ebf74e</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>28</OID>
                      <Parent>24</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>54 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>4d6fd632-e76b-4403-b4a9-ce4d2430cc47</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>29</OID>
                      <Parent>24</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>cfa8e7a5-4b61-4fd9-96a5-a48a733f646f</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>30</OID>
                      <Parent>24</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>ec38b8fd-c79a-4174-91b7-6841358a38be</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>126 px</Y>
                </Location>
                <Size>
                  <Width>145 px</Width>
                  <Height>91 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>91 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>-768 px</X>
              <Y>-664 px</Y>
            </Location>
            <Size>
              <Width>150 px</Width>
              <Height>266 px</Height>
            </Size>
            <MinSize>
              <Width>100 px</Width>
              <Height>266 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>266 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>2445fa3a-6c5f-4e5f-a9d5-152894dfd928</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="EnumTypeVwModel">
            <CustomProperties>
              <OID>31</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>32</OID>
                  <Parent>31</Parent>
                </CustomProperties>
                <Children />
                <Size>
                  <Width>267 px</Width>
                  <Height>0 px</Height>
                </Size>
              </Model>
            </Children>
            <Location>
              <X>288 px</X>
              <Y>-600 px</Y>
            </Location>
            <Size>
              <Width>272 px</Width>
              <Height>48 px</Height>
            </Size>
            <MinSize>
              <Width>100 px</Width>
              <Height>48 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>48 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>28c4dd3c-9844-46a0-b074-05b2731e1f01</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateEnumType</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>33</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>34</OID>
                  <Parent>33</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>35</OID>
                      <Parent>34</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>234 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>f77f56d6-8793-40b9-a836-21fd5bbe1542</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>36</OID>
                      <Parent>34</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>234 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>bfb60106-828e-4ed3-8777-205677fdc877</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>235 px</Width>
                  <Height>37 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>37 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>37</OID>
                  <Parent>33</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>38</OID>
                      <Parent>37</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>234 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>858625c5-f837-4438-82dd-257d0cc67330</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>36 px</Y>
                </Location>
                <Size>
                  <Width>235 px</Width>
                  <Height>37 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>37 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>-24 px</X>
              <Y>-200 px</Y>
            </Location>
            <Size>
              <Width>240 px</Width>
              <Height>122 px</Height>
            </Size>
            <MinSize>
              <Width>100 px</Width>
              <Height>122 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>122 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>082ca73a-21d0-4dba-b9e2-662fd6205082</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>39</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>40</OID>
                  <Parent>39</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>41</OID>
                      <Parent>40</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>266 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>31023619-d1d1-4f99-927e-966fb069aad0</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>42</OID>
                      <Parent>40</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>266 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>12f3ad96-df57-42b1-937e-b5da2909da45</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>43</OID>
                      <Parent>40</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>266 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>a63a6c02-de39-48e7-98af-92d27b667eb2</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>44</OID>
                      <Parent>40</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>54.5 px</Y>
                    </Location>
                    <Size>
                      <Width>266 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>d56a78a6-4a31-4101-9ee4-dd138634e849</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>45</OID>
                      <Parent>40</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>72.5 px</Y>
                    </Location>
                    <Size>
                      <Width>266 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>7e61050a-5153-4009-bd75-6563cb2841d8</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>46</OID>
                      <Parent>40</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>90.5 px</Y>
                    </Location>
                    <Size>
                      <Width>266 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>4a1a6c7c-18c0-4ec8-8ed6-7a97ee902d3f</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>267 px</Width>
                  <Height>109 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>109 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>47</OID>
                  <Parent>39</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>48</OID>
                      <Parent>47</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>266 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>2162ea71-d3a9-4cb6-a5cd-530d420364e3</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>49</OID>
                      <Parent>47</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>18 px</Y>
                    </Location>
                    <Size>
                      <Width>266 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>f5591ba9-cb71-4d17-9af4-35e0826877b6</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>50</OID>
                      <Parent>47</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>36 px</Y>
                    </Location>
                    <Size>
                      <Width>266 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>cf273e1c-f6da-47c5-a9c9-ad0082c6f417</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>108 px</Y>
                </Location>
                <Size>
                  <Width>267 px</Width>
                  <Height>73 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>73 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>-400 px</X>
              <Y>-416 px</Y>
            </Location>
            <Size>
              <Width>272 px</Width>
              <Height>230 px</Height>
            </Size>
            <MinSize>
              <Width>100 px</Width>
              <Height>230 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>230 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>f1fe9d47-83ee-45a6-8b93-b8afb8754755</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>51</OID>
              <OutModel>39</OutModel>
              <InModel>5</InModel>
              <OutPort>52</OutPort>
              <InPort>53</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>144.029126213592 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Right</Align>
                <CustomProperties>
                  <OID>52</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>Undefined</X>
                  <Y>136.5 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Left</Align>
                <CustomProperties>
                  <OID>53</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <BendPoints>
              <PointD>
                <X>-108 px</X>
                <Y>-271.970873786408 px</Y>
              </PointD>
              <PointD>
                <X>-108 px</X>
                <Y>-351.5 px</Y>
              </PointD>
            </BendPoints>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>ca25344e-3945-49c2-abd2-baf8ceacdcfa</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>54</OID>
              <OutModel>33</OutModel>
              <InModel>39</InModel>
              <OutPort>55</OutPort>
              <InPort>56</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>61 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Left</Align>
                <CustomProperties>
                  <OID>55</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>136 px</X>
                  <Y>0 px</Y>
                </Location>
                <Anchors>Left Right</Anchors>
                <Align>Bottom</Align>
                <CustomProperties>
                  <OID>56</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <BendPoints>
              <PointD>
                <X>-264 px</X>
                <Y>-139 px</Y>
              </PointD>
            </BendPoints>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>746fce3f-875d-468b-87dc-5adb693f18e9</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>57</OID>
              <OutModel>39</OutModel>
              <InModel>15</InModel>
              <OutPort>58</OutPort>
              <InPort>59</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>9 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Left</Align>
                <CustomProperties>
                  <OID>58</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>257 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Right</Align>
                <CustomProperties>
                  <OID>59</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>2c6ec422-71cb-4fd9-a3f2-b152a94761b7</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="ComplexTypeVwModel">
            <CustomProperties>
              <OID>60</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>61</OID>
                  <Parent>60</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>62</OID>
                      <Parent>61</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>248 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>cbb4d2e8-19e2-4961-8e93-2cdc9e423c95</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>63</OID>
                      <Parent>61</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>248 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>e833a6db-82bb-4c90-af45-0976e81d3258</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>64</OID>
                      <Parent>61</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>248 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>7d7a5b0b-7f8b-4c47-8ad7-9b672ba3947c</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>65</OID>
                      <Parent>61</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>54.5 px</Y>
                    </Location>
                    <Size>
                      <Width>248 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>ec425aba-7271-45b6-a641-1accb739a6f6</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>249 px</Width>
                  <Height>73 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>73 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>66</OID>
                  <Parent>60</Parent>
                </CustomProperties>
                <Children />
                <Location>
                  <X>0 px</X>
                  <Y>72 px</Y>
                </Location>
                <Size>
                  <Width>249 px</Width>
                  <Height>19 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>19 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>-448 px</X>
              <Y>-640 px</Y>
            </Location>
            <Size>
              <Width>254 px</Width>
              <Height>140 px</Height>
            </Size>
            <MinSize>
              <Width>100 px</Width>
              <Height>140 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>140 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>fc5370ac-f084-4682-89f3-bd7ae183519b</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateComplexType</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="EnumTypeVwModel">
            <CustomProperties>
              <OID>67</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>68</OID>
                  <Parent>67</Parent>
                </CustomProperties>
                <Children />
                <Size>
                  <Width>295 px</Width>
                  <Height>0 px</Height>
                </Size>
              </Model>
            </Children>
            <Location>
              <X>208 px</X>
              <Y>-696 px</Y>
            </Location>
            <Size>
              <Width>300 px</Width>
              <Height>48 px</Height>
            </Size>
            <MinSize>
              <Width>100 px</Width>
              <Height>48 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>48 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>880ee1de-a2df-4b0c-bbec-debaa654cc1a</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateEnumType</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="EnumTypeVwModel">
            <CustomProperties>
              <OID>69</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>70</OID>
                  <Parent>69</Parent>
                </CustomProperties>
                <Children />
                <Size>
                  <Width>145 px</Width>
                  <Height>0 px</Height>
                </Size>
              </Model>
            </Children>
            <Location>
              <X>392 px</X>
              <Y>-280 px</Y>
            </Location>
            <Size>
              <Width>150 px</Width>
              <Height>48 px</Height>
            </Size>
            <MinSize>
              <Width>100 px</Width>
              <Height>48 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>48 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>4cb392bf-81a8-4acf-9982-f0f14556fcda</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateEnumType</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
        </Children>
        <GridSize>8 px</GridSize>
        <ViewPort>
          <ScaleMode>Free</ScaleMode>
          <Scale>1</Scale>
          <Location>
            <X>-892 px</X>
            <Y>-869 px</Y>
          </Location>
        </ViewPort>
        <Oid xsi:type="SchemaModelOID">
          <Path>f754714d-fb80-45dd-ac38-27fa47a21e75</Path>
          <TypeName>EntityDeveloper.NHibernate.HibernateContextModel</TypeName>
        </Oid>
      </Model>
    </DiagramModel>
  </Diagram>
  <DiagramOptions Version="v2.0">
    <Options xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xsi:type="PageOptions">
      <TopLeftMargins>
        <Width>39.3700787401575 in/100</Width>
        <Height>39.3700787401575 in/100</Height>
      </TopLeftMargins>
      <BottomRightMargins>
        <Width>39.3700787401575 in/100</Width>
        <Height>39.3700787401575 in/100</Height>
      </BottomRightMargins>
      <PaperSize>
        <Width>827 in/100</Width>
        <Height>1169 in/100</Height>
      </PaperSize>
    </Options>
    <Options xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xsi:type="PrintOptions" />
    <Options xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xsi:type="ViewOptions">
      <ShadowOffset>
        <X>4 px</X>
        <Y>4 px</Y>
      </ShadowOffset>
      <CustomProperties />
    </Options>
    <EdDiagramOptions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
      <CustomProperties />
    </EdDiagramOptions>
  </DiagramOptions>
</EntityDeveloperDiagram>