using Anete.Config.Core.Internals;
using Anete.Resources;
using Anete.Utils.ComponentModel.Attributes;
using System;
using System.ComponentModel;
using System.Runtime.Serialization;

namespace Anete.Config.Configs.Core.CashDesk
{
	/// <summary>
	/// Kde budou ulozeny rozmarkovane doklady?
	/// </summary>
	[ConfigResourceEnum]
	[TypeConverter(typeof(ConfigEnumConverter<DataStorageType>))]
	[DataContract(Namespace = AneteNamespace.DefaultNamespace)]
	public enum DataStorageType
	{
		/// <summary>
		/// Ulozeno v lokalnich souborech
		/// </summary>	
		[EnumBasedFactory(typeof(MdbDataStorageSettings))]
		[EnumMember]
		Mdb,
		/// <summary>
		/// Databaze Kredit - nemuze fungovat v offline rezimu
		/// </summary>		
		[EnumBasedFactory(typeof(KreditDataStorageSettings))]
		[EnumMember]
		Kredit,
	}
}
