//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Anete.Common.Data.Nh.Entities {
    using System;
    
    
    /// <summary>
    /// A strongly-typed resource class, for looking up localized strings, formatting them, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilderEx class via the ResXFileCodeGeneratorEx custom tool.
    // To add or remove a member, edit your .ResX file then rerun the ResXFileCodeGeneratorEx custom tool or rebuild your VS.NET project.
    // Copyright (c) Dmytro <PERSON> 2006-2018 (http://dmytro.kryvko.googlepages.com/)
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("DMKSoftware.CodeGenerators.Tools.StronglyTypedResourceBuilderEx", "2.6.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Naming", "CA1724:TypeNamesShouldNotMatchNamespaces")]
    public partial class CashierSR {
        
        private static global::System.Resources.ResourceManager _resourceManager;
        
        private static object _internalSyncObject;
        
        private static global::System.Globalization.CultureInfo _resourceCulture;
        
        /// <summary>
        /// Initializes a CashierSR object.
        /// </summary>
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        public CashierSR() {
        }
        
        /// <summary>
        /// Thread safe lock object used by this class.
        /// </summary>
        public static object InternalSyncObject {
            get {
                if (object.ReferenceEquals(_internalSyncObject, null)) {
                    global::System.Threading.Interlocked.CompareExchange(ref _internalSyncObject, new object(), null);
                }
                return _internalSyncObject;
            }
        }
        
        /// <summary>
        /// Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(_resourceManager, null)) {
                    global::System.Threading.Monitor.Enter(InternalSyncObject);
                    try {
                        if (object.ReferenceEquals(_resourceManager, null)) {
                            global::System.Threading.Interlocked.Exchange(ref _resourceManager, new global::System.Resources.ResourceManager("Anete.Common.Data.Nh.Entities.CashierSR", typeof(CashierSR).Assembly));
                        }
                    }
                    finally {
                        global::System.Threading.Monitor.Exit(InternalSyncObject);
                    }
                }
                return _resourceManager;
            }
        }
        
        /// <summary>
        /// Overrides the current thread's CurrentUICulture property for all
        /// resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return _resourceCulture;
            }
            set {
                _resourceCulture = value;
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Zařízení'.
        /// </summary>
        public static string AppInstallation {
            get {
                return ResourceManager.GetString(ResourceNames.AppInstallation, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Pokladní'.
        /// </summary>
        public static string Cashier_Desc {
            get {
                return ResourceManager.GetString(ResourceNames.Cashier_Desc, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Kód pokladní'.
        /// </summary>
        public static string CashierCode {
            get {
                return ResourceManager.GetString(ResourceNames.CashierCode, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Id'.
        /// </summary>
        public static string CashierId {
            get {
                return ResourceManager.GetString(ResourceNames.CashierId, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Kód'.
        /// </summary>
        public static string Code {
            get {
                return ResourceManager.GetString(ResourceNames.Code, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Smazáno'.
        /// </summary>
        public static string Deleted {
            get {
                return ResourceManager.GetString(ResourceNames.Deleted, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to '#IdOpravneni#'.
        /// </summary>
        public static string IdOpravneni {
            get {
                return ResourceManager.GetString(ResourceNames.IdOpravneni, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Jazyk'.
        /// </summary>
        public static string Language {
            get {
                return ResourceManager.GetString(ResourceNames.Language, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Přihlašovací jméno'.
        /// </summary>
        public static string LoginName {
            get {
                return ResourceManager.GetString(ResourceNames.LoginName, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Jméno'.
        /// </summary>
        public static string Name {
            get {
                return ResourceManager.GetString(ResourceNames.Name, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Heslo'.
        /// </summary>
        public static string Password {
            get {
                return ResourceManager.GetString(ResourceNames.Password, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Lists all the resource names as constant string fields.
        /// </summary>
        public class ResourceNames {
            
            /// <summary>
            /// Stores the resource name 'AppInstallation'.
            /// </summary>
            public const string AppInstallation = "AppInstallation";
            
            /// <summary>
            /// Stores the resource name 'Cashier_Desc'.
            /// </summary>
            public const string Cashier_Desc = "Cashier_Desc";
            
            /// <summary>
            /// Stores the resource name 'CashierCode'.
            /// </summary>
            public const string CashierCode = "CashierCode";
            
            /// <summary>
            /// Stores the resource name 'CashierId'.
            /// </summary>
            public const string CashierId = "CashierId";
            
            /// <summary>
            /// Stores the resource name 'Code'.
            /// </summary>
            public const string Code = "Code";
            
            /// <summary>
            /// Stores the resource name 'Deleted'.
            /// </summary>
            public const string Deleted = "Deleted";
            
            /// <summary>
            /// Stores the resource name 'IdOpravneni'.
            /// </summary>
            public const string IdOpravneni = "IdOpravneni";
            
            /// <summary>
            /// Stores the resource name 'Language'.
            /// </summary>
            public const string Language = "Language";
            
            /// <summary>
            /// Stores the resource name 'LoginName'.
            /// </summary>
            public const string LoginName = "LoginName";
            
            /// <summary>
            /// Stores the resource name 'Name'.
            /// </summary>
            public const string Name = "Name";
            
            /// <summary>
            /// Stores the resource name 'Password'.
            /// </summary>
            public const string Password = "Password";
        }
    }
}
