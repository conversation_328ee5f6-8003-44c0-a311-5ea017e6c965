using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Anete.Utils.Collections;
using System.ComponentModel;
using NHibernate;

namespace Anete.Data.Nh
{
    /// <summary>
    /// Kazdou operaci dela v transakci a vola commit.
    /// </summary>
    /// <typeparam name="TEntity"></typeparam>
    public class BindingListToSessionWithCommitAdapter<TEntity> : BindingListToSessionAdapterBase<TEntity>
        where TEntity : class, new()
    {
        /// <summary>
        /// Initializes a new instance of the BindingListToSessionAdapter class.
        /// </summary>
        /// <param name="allowAddItem">
        /// Parametr bylo nutne vyvtorit, protoze grid typicky vytvory novou polozku uz ve svem specialnim radku. 
        /// Ta ale jeste neni validni, tudiz ji nelze pridat do db.</param>
        /// <param name="allowRemoveItem">Zabraneni ostraneni polozky. Ma smysl, kdyz to presenter resi ve vlastni rezii.</param>
        /// <param name="bindingList">Data k synchronizaci</param>
        /// <param name="session">Session</param>
        public BindingListToSessionWithCommitAdapter(BindingList<TEntity> bindingList, ISession session, bool allowAddItem, bool allowRemoveItem)
            : base(bindingList, session, allowAddItem, allowRemoveItem)
        {
        }



        protected override void AddItemToStorageInt(ISession session, TEntity item)
        {
            using (ITransaction tx = session.BeginTransaction())
            {
                session.Save(item);
                tx.Commit();
            }
        }

        protected override void RemoveItemFromStorageInt(ISession session, TEntity item)
        {
            using (ITransaction tx = session.BeginTransaction())
            {
                session.Delete(item);
                tx.Commit();
            }
        }  
    }
}
