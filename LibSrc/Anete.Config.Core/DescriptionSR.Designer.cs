//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Anete.Config.Core {
    using System;
    
    
    /// <summary>
    /// A strongly-typed resource class, for looking up localized strings, formatting them, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilderEx class via the ResXFileCodeGeneratorEx custom tool.
    // To add or remove a member, edit your .ResX file then rerun the ResXFileCodeGeneratorEx custom tool or rebuild your VS.NET project.
    // Copyright (c) Dmytro <PERSON>v<PERSON> 2006-2024 (http://dmytro.kryvko.googlepages.com/)
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("DMKSoftware.CodeGenerators.Tools.StronglyTypedResourceBuilderEx", "*******")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Naming", "CA1724:TypeNamesShouldNotMatchNamespaces")]
    public partial class DescriptionSR {
        
        private static global::System.Resources.ResourceManager _resourceManager;
        
        private static object _internalSyncObject;
        
        private static global::System.Globalization.CultureInfo _resourceCulture;
        
        /// <summary>
        /// Initializes a DescriptionSR object.
        /// </summary>
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        public DescriptionSR() {
        }
        
        /// <summary>
        /// Thread safe lock object used by this class.
        /// </summary>
        public static object InternalSyncObject {
            get {
                if (object.ReferenceEquals(_internalSyncObject, null)) {
                    global::System.Threading.Interlocked.CompareExchange(ref _internalSyncObject, new object(), null);
                }
                return _internalSyncObject;
            }
        }
        
        /// <summary>
        /// Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(_resourceManager, null)) {
                    global::System.Threading.Monitor.Enter(InternalSyncObject);
                    try {
                        if (object.ReferenceEquals(_resourceManager, null)) {
                            global::System.Threading.Interlocked.Exchange(ref _resourceManager, new global::System.Resources.ResourceManager("Anete.Config.Core.DescriptionSR", typeof(DescriptionSR).Assembly));
                        }
                    }
                    finally {
                        global::System.Threading.Monitor.Exit(InternalSyncObject);
                    }
                }
                return _resourceManager;
            }
        }
        
        /// <summary>
        /// Overrides the current thread's CurrentUICulture property for all
        /// resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return _resourceCulture;
            }
            set {
                _resourceCulture = value;
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení vzhledu ubytování'.
        /// </summary>
        public static string AccommodationAppearanceLabelColorsConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.AccommodationAppearanceLabelColorsConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení vzhledu ubytování'.
        /// </summary>
        public static string AccommodationAppearanceLabelColorsConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.AccommodationAppearanceLabelColorsConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Automatické vytváření objednávek'.
        /// </summary>
        public static string AccommodationBehaviourAutoOrderingConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.AccommodationBehaviourAutoOrderingConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Vytváření objednávek'.
        /// </summary>
        public static string AccommodationBehaviourAutoOrderingConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.AccommodationBehaviourAutoOrderingConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Ubytovací systém'.
        /// </summary>
        public static string AccommodationBehaviourSystemConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.AccommodationBehaviourSystemConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Ubytovací systém'.
        /// </summary>
        public static string AccommodationBehaviourSystemConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.AccommodationBehaviourSystemConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Hotelové ubytování'.
        /// </summary>
        public static string AccommodationSchedulerConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.AccommodationSchedulerConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení hotelového ubytování'.
        /// </summary>
        public static string AccommodationSchedulerConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.AccommodationSchedulerConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Přihlašování'.
        /// </summary>
        public static string AutoUpdaterBehaviourImpersonationConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.AutoUpdaterBehaviourImpersonationConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace přihlašování'.
        /// </summary>
        public static string AutoUpdaterBehaviourImpersonationConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.AutoUpdaterBehaviourImpersonationConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Monitor aktualizací'.
        /// </summary>
        public static string AutoUpdaterBehaviourMonitorConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.AutoUpdaterBehaviourMonitorConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace monitoru aktualizací'.
        /// </summary>
        public static string AutoUpdaterBehaviourMonitorConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.AutoUpdaterBehaviourMonitorConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Synchronizace času'.
        /// </summary>
        public static string AutoUpdaterDateTimeSynchronizationConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.AutoUpdaterDateTimeSynchronizationConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace synchronizace času'.
        /// </summary>
        public static string AutoUpdaterDateTimeSynchronizationConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.AutoUpdaterDateTimeSynchronizationConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Parametry změnového řízení'.
        /// </summary>
        public static string BatchDataOperationsParametersConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.BatchDataOperationsParametersConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace parametrů změnového řízení'.
        /// </summary>
        public static string BatchDataOperationsParametersConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.BatchDataOperationsParametersConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Půlnoc pro jídelníček'.
        /// </summary>
        public static string CashDeskAccoutingMidnightConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskAccoutingMidnightConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace parametrů souvisejících s přenačtením jídelníčku.'.
        /// </summary>
        public static string CashDeskAccoutingMidnightConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskAccoutingMidnightConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'EET'.
        /// </summary>
        public static string CashDeskBehaviorEetConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskBehaviorEetConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace EET'.
        /// </summary>
        public static string CashDeskBehaviorEetConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskBehaviorEetConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'ICoupon'.
        /// </summary>
        public static string CashDeskBehaviorICouponConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskBehaviorICouponConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení ICoupon pro jednotlivé zařízení.'.
        /// </summary>
        public static string CashDeskBehaviorICouponConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskBehaviorICouponConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to ''.
        /// </summary>
        public static string CashDeskBehaviourAccommodationSystemConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskBehaviourAccommodationSystemConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to ''.
        /// </summary>
        public static string CashDeskBehaviourAccommodationSystemConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskBehaviourAccommodationSystemConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Vyrovnávání položek paragonu'.
        /// </summary>
        public static string CashDeskBehaviourBalanceSaleSlipConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskBehaviourBalanceSaleSlipConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Vyrovnávání položek paragonu'.
        /// </summary>
        public static string CashDeskBehaviourBalanceSaleSlipConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskBehaviourBalanceSaleSlipConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení vyhledávání zboží'.
        /// </summary>
        public static string CashDeskBehaviourGoodsSearchConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskBehaviourGoodsSearchConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení vyhledávání zboží'.
        /// </summary>
        public static string CashDeskBehaviourGoodsSearchConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskBehaviourGoodsSearchConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Zamykání'.
        /// </summary>
        public static string CashDeskBehaviourLockConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskBehaviourLockConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace zamykání kasy při nečinnosti a uživatelského zamykání'.
        /// </summary>
        public static string CashDeskBehaviourLockConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskBehaviourLockConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace náhradního přihlášení'.
        /// </summary>
        public static string CashDeskBehaviourLoginConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskBehaviourLoginConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Tato třída je určena pro podrobnější nastavení náhradního přihlášení v Kase'.
        /// </summary>
        public static string CashDeskBehaviourLoginConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskBehaviourLoginConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Různé'.
        /// </summary>
        public static string CashDeskBehaviourMiscellaneousConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskBehaviourMiscellaneousConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Různá nastavení'.
        /// </summary>
        public static string CashDeskBehaviourMiscellaneousConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskBehaviourMiscellaneousConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Poznámky'.
        /// </summary>
        public static string CashDeskBehaviourNotesConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskBehaviourNotesConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení poznámek'.
        /// </summary>
        public static string CashDeskBehaviourNotesConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskBehaviourNotesConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Voucher jako platidlo.'.
        /// </summary>
        public static string CashDeskBehaviourPaymentVoucherConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskBehaviourPaymentVoucherConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace voucheru jako platidla.'.
        /// </summary>
        public static string CashDeskBehaviourPaymentVoucherConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskBehaviourPaymentVoucherConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení restaurační kasy'.
        /// </summary>
        public static string CashDeskBehaviourRestaurantConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskBehaviourRestaurantConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení restaurační kasy pro využití grafického editoru restaurace'.
        /// </summary>
        public static string CashDeskBehaviourRestaurantConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskBehaviourRestaurantConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Rozdělení pokladních dokladů'.
        /// </summary>
        public static string CashDeskBehaviourSaleSlipSplitConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskBehaviourSaleSlipSplitConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení rozdělení pokladních dokladů'.
        /// </summary>
        public static string CashDeskBehaviourSaleSlipSplitConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskBehaviourSaleSlipSplitConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Zpracování slevových voucherů'.
        /// </summary>
        public static string CashDeskBehaviourSalesVoucherConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskBehaviourSalesVoucherConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Možnost nastavit formát slevových voucheru tak, aby byly při makrování automaticky aplikovány slevy'.
        /// </summary>
        public static string CashDeskBehaviourSalesVoucherConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskBehaviourSalesVoucherConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení displeje obsluhy'.
        /// </summary>
        public static string CashDeskBehaviourServiceDisplayConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskBehaviourServiceDisplayConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení displeje obsluhy (kasy)'.
        /// </summary>
        public static string CashDeskBehaviourServiceDisplayConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskBehaviourServiceDisplayConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Tara'.
        /// </summary>
        public static string CashDeskBehaviourTaraConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskBehaviourTaraConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace tárování'.
        /// </summary>
        public static string CashDeskBehaviourTaraConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskBehaviourTaraConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Touch'.
        /// </summary>
        public static string CashDeskBehaviourTouchConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskBehaviourTouchConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace parametrů souvisejících s Touch klávesnicí'.
        /// </summary>
        public static string CashDeskBehaviourTouchConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskBehaviourTouchConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Uživatelské rozhraní'.
        /// </summary>
        public static string CashDeskBehaviourUserInterfaceConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskBehaviourUserInterfaceConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace typu uživatelského rozhraní'.
        /// </summary>
        public static string CashDeskBehaviourUserInterfaceConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskBehaviourUserInterfaceConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Sazby DPH'.
        /// </summary>
        public static string CashDeskBehaviourVatRateConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskBehaviourVatRateConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení markování v alternativní sazbě DPH'.
        /// </summary>
        public static string CashDeskBehaviourVatRateConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskBehaviourVatRateConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Datové uložiště'.
        /// </summary>
        public static string CashDeskDataStorageConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskDataStorageConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení datového uložiště'.
        /// </summary>
        public static string CashDeskDataStorageConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskDataStorageConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Fiskální modul'.
        /// </summary>
        public static string CashDeskFiscalModuleConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskFiscalModuleConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace fiskálního modulu'.
        /// </summary>
        public static string CashDeskFiscalModuleConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskFiscalModuleConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Zákaznický displej'.
        /// </summary>
        public static string CashDeskHwClientDisplayConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskHwClientDisplayConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace zákaznického displeje'.
        /// </summary>
        public static string CashDeskHwClientDisplayConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskHwClientDisplayConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Elektronická peněženka'.
        /// </summary>
        public static string CashDeskHwEWalletConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskHwEWalletConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace elektronické peněženky'.
        /// </summary>
        public static string CashDeskHwEWalletConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskHwEWalletConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Manažer'.
        /// </summary>
        public static string CashDeskManagerConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskManagerConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace modulů manažeru kasy'.
        /// </summary>
        public static string CashDeskManagerConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskManagerConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Offline'.
        /// </summary>
        public static string CashDeskOfflineConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskOfflineConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace režimu offline'.
        /// </summary>
        public static string CashDeskOfflineConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskOfflineConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Rozšířené informace o sortimentu'.
        /// </summary>
        public static string CashDeskPluginsGoodsExtendedInfoConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskPluginsGoodsExtendedInfoConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace pluginu zobrazujícího rozšířené informace o sortimentu'.
        /// </summary>
        public static string CashDeskPluginsGoodsExtendedInfoConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskPluginsGoodsExtendedInfoConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení pro Tul'.
        /// </summary>
        public static string CashDeskPluginsTulPluginConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskPluginsTulPluginConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace pluginu pro Tul'.
        /// </summary>
        public static string CashDeskPluginsTulPluginConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskPluginsTulPluginConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Prodej'.
        /// </summary>
        public static string CashDeskSaleConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskSaleConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace prodeje'.
        /// </summary>
        public static string CashDeskSaleConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskSaleConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Modul sklady'.
        /// </summary>
        public static string CashDeskStockModuleConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskStockModuleConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace modulu sklady'.
        /// </summary>
        public static string CashDeskStockModuleConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.CashDeskStockModuleConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Obsluhovaná zařízení'.
        /// </summary>
        public static string DevicesControllerBehaviourAppInstallationsConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.DevicesControllerBehaviourAppInstallationsConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace seznamu obsluhovaných zařízení'.
        /// </summary>
        public static string DevicesControllerBehaviourAppInstallationsConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.DevicesControllerBehaviourAppInstallationsConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Offline'.
        /// </summary>
        public static string DevicesControllerOfflineConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.DevicesControllerOfflineConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace režimu offline'.
        /// </summary>
        public static string DevicesControllerOfflineConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.DevicesControllerOfflineConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nástroje'.
        /// </summary>
        public static string DevicesControllerServicesToolsConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.DevicesControllerServicesToolsConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení nástroju pro správu terminálu Anete'.
        /// </summary>
        public static string DevicesControllerServicesToolsConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.DevicesControllerServicesToolsConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Klienti'.
        /// </summary>
        public static string GlobalAuthenticationClientConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalAuthenticationClientConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace autentizace klientů oproti aplikačnímu serveru'.
        /// </summary>
        public static string GlobalAuthenticationClientConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalAuthenticationClientConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Server'.
        /// </summary>
        public static string GlobalAuthenticationServerConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalAuthenticationServerConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace typů autentizace'.
        /// </summary>
        public static string GlobalAuthenticationServerConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalAuthenticationServerConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Kampaně'.
        /// </summary>
        public static string GlobalBehaviorCampaignsConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviorCampaignsConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení pro anketní kampaně'.
        /// </summary>
        public static string GlobalBehaviorCampaignsConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviorCampaignsConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Eet'.
        /// </summary>
        public static string GlobalBehaviorEetConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviorEetConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení pro EET'.
        /// </summary>
        public static string GlobalBehaviorEetConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviorEetConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'eKasa'.
        /// </summary>
        public static string GlobalBehaviorEKasaConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviorEKasaConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'eKasa'.
        /// </summary>
        public static string GlobalBehaviorEKasaConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviorEKasaConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Administrace přístupových práv'.
        /// </summary>
        public static string GlobalBehaviourAccessRightsAdministrationConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviourAccessRightsAdministrationConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace přiřazování přístupových práv (aplikačních rolí) jednotlivým uživatelům.'.
        /// </summary>
        public static string GlobalBehaviourAccessRightsAdministrationConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviourAccessRightsAdministrationConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Stát'.
        /// </summary>
        public static string GlobalBehaviourApplicationCountryConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviourApplicationCountryConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace státu'.
        /// </summary>
        public static string GlobalBehaviourApplicationCountryConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviourApplicationCountryConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Jazyk aplikace'.
        /// </summary>
        public static string GlobalBehaviourApplicationLanguageConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviourApplicationLanguageConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace jazyka aplikace'.
        /// </summary>
        public static string GlobalBehaviourApplicationLanguageConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviourApplicationLanguageConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Jazyk aplikace detail'.
        /// </summary>
        public static string GlobalBehaviourApplicationLanguageDetailConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviourApplicationLanguageDetailConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Detailní konfigurace jazyka aplikace'.
        /// </summary>
        public static string GlobalBehaviourApplicationLanguageDetailConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviourApplicationLanguageDetailConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Automatické aktualizace'.
        /// </summary>
        public static string GlobalBehaviourAutoUpdatesConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviourAutoUpdatesConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace automatických aktualizací'.
        /// </summary>
        public static string GlobalBehaviourAutoUpdatesConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviourAutoUpdatesConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Ubytování klienta'.
        /// </summary>
        public static string GlobalBehaviourClientAccommodationConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviourClientAccommodationConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace ubytování klienta'.
        /// </summary>
        public static string GlobalBehaviourClientAccommodationConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviourClientAccommodationConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Zařazení klienta'.
        /// </summary>
        public static string GlobalBehaviourClientAssignmentConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviourClientAssignmentConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace zařazení klienta'.
        /// </summary>
        public static string GlobalBehaviourClientAssignmentConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviourClientAssignmentConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Strávník'.
        /// </summary>
        public static string GlobalBehaviourClientConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviourClientConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace údajů o strávníkovi'.
        /// </summary>
        public static string GlobalBehaviourClientConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviourClientConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Vynucení změny hesla klientem'.
        /// </summary>
        public static string GlobalBehaviourClientForceChangePasswordConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviourClientForceChangePasswordConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to ''.
        /// </summary>
        public static string GlobalBehaviourClientForceChangePasswordConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviourClientForceChangePasswordConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Požadavek odsouhlasení podmínek užívání klientem'.
        /// </summary>
        public static string GlobalBehaviourClientRequireAgreementWithTermsConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviourClientRequireAgreementWithTermsConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to ''.
        /// </summary>
        public static string GlobalBehaviourClientRequireAgreementWithTermsConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviourClientRequireAgreementWithTermsConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Ladění aplikace'.
        /// </summary>
        public static string GlobalBehaviourDebugConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviourDebugConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení pro ladění aplikace'.
        /// </summary>
        public static string GlobalBehaviourDebugConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviourDebugConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Slevový systém'.
        /// </summary>
        public static string GlobalBehaviourDiscountSystemConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviourDiscountSystemConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace slevového systému'.
        /// </summary>
        public static string GlobalBehaviourDiscountSystemConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviourDiscountSystemConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Synchronizace jídelníčku s FBS'.
        /// </summary>
        public static string GlobalBehaviourFbsMenuConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviourFbsMenuConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace synchronizace jídelníčku s FBS'.
        /// </summary>
        public static string GlobalBehaviourFbsMenuConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviourFbsMenuConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Displej v kuchyni'.
        /// </summary>
        public static string GlobalBehaviourKitchenOrderDisplayConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviourKitchenOrderDisplayConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení použití displejů v kuchyni'.
        /// </summary>
        public static string GlobalBehaviourKitchenOrderDisplayConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviourKitchenOrderDisplayConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Objednávka do kuchyně'.
        /// </summary>
        public static string GlobalBehaviourKitchenOrderPrintConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviourKitchenOrderPrintConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace pro tisk objednávky do kuchyně'.
        /// </summary>
        public static string GlobalBehaviourKitchenOrderPrintConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviourKitchenOrderPrintConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Tisk stravenek'.
        /// </summary>
        public static string GlobalBehaviourMealTicketConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviourMealTicketConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace tisku stravenek'.
        /// </summary>
        public static string GlobalBehaviourMealTicketConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviourMealTicketConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Zprávy pro klienty'.
        /// </summary>
        public static string GlobalBehaviourMessageForClientsConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviourMessageForClientsConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace zpráv pro klienty'.
        /// </summary>
        public static string GlobalBehaviourMessageForClientsConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviourMessageForClientsConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Zobrazování cen'.
        /// </summary>
        public static string GlobalBehaviourPricesConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviourPricesConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace zobrazování cen'.
        /// </summary>
        public static string GlobalBehaviourPricesConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviourPricesConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Paragon'.
        /// </summary>
        public static string GlobalBehaviourSalesSlipPrintConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviourSalesSlipPrintConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace údajů tištěných na paragonu'.
        /// </summary>
        public static string GlobalBehaviourSalesSlipPrintConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviourSalesSlipPrintConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Zasílání zpráv na support'.
        /// </summary>
        public static string GlobalBehaviourTechSupportMessagingConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviourTechSupportMessagingConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace zasílání zpráv na support'.
        /// </summary>
        public static string GlobalBehaviourTechSupportMessagingConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalBehaviourTechSupportMessagingConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Čtečka karet (externí systém)'.
        /// </summary>
        public static string GlobalHwCardExternalSystemReaderConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalHwCardExternalSystemReaderConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení čtečky karet, která přejímá čísla karet z externího systému.'.
        /// </summary>
        public static string GlobalHwCardExternalSystemReaderConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalHwCardExternalSystemReaderConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Klávesnicová čtečka karet'.
        /// </summary>
        public static string GlobalHwCardKbdReaderConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalHwCardKbdReaderConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace klávesnicové čtečky karet'.
        /// </summary>
        public static string GlobalHwCardKbdReaderConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalHwCardKbdReaderConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Sériová čtečka karet'.
        /// </summary>
        public static string GlobalHwCardSerialReaderConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalHwCardSerialReaderConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace čtečky karet připojené k sériovému portu'.
        /// </summary>
        public static string GlobalHwCardSerialReaderConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalHwCardSerialReaderConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Zákaznický displej'.
        /// </summary>
        public static string GlobalHwClientDisplayConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalHwClientDisplayConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace zákaznického displeje'.
        /// </summary>
        public static string GlobalHwClientDisplayConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalHwClientDisplayConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Čtečka čárových kódů'.
        /// </summary>
        public static string GlobalHwEanKbdReaderConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalHwEanKbdReaderConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace čtečky čárových kódů'.
        /// </summary>
        public static string GlobalHwEanKbdReaderConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalHwEanKbdReaderConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Sériová čtečka čárových kódů'.
        /// </summary>
        public static string GlobalHwEanSerialReaderConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalHwEanSerialReaderConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace sériové čtečky čárových kódů'.
        /// </summary>
        public static string GlobalHwEanSerialReaderConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalHwEanSerialReaderConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Elektronická peněženka'.
        /// </summary>
        public static string GlobalHwEWalletConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalHwEWalletConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace elektronické peněženky'.
        /// </summary>
        public static string GlobalHwEWalletConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalHwEWalletConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Tiskárna v kuchyni'.
        /// </summary>
        public static string GlobalHwKitchenPrinterConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalHwKitchenPrinterConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace tiskárny pro tisk objednávek jídel v kuchyni. Tiskne na ni Kasa.'.
        /// </summary>
        public static string GlobalHwKitchenPrinterConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalHwKitchenPrinterConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Tiskárna štítků'.
        /// </summary>
        public static string GlobalHwLabelPrinterConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalHwLabelPrinterConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení tiskárny štítků'.
        /// </summary>
        public static string GlobalHwLabelPrinterConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalHwLabelPrinterConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Váha Nawi'.
        /// </summary>
        public static string GlobalHwNawiScaleConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalHwNawiScaleConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace certifikované váhy'.
        /// </summary>
        public static string GlobalHwNawiScaleConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalHwNawiScaleConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Platební terminál'.
        /// </summary>
        public static string GlobalHwPaymentTermConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalHwPaymentTermConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení platebního terminálu'.
        /// </summary>
        public static string GlobalHwPaymentTermConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalHwPaymentTermConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Bankovní tiskárna'.
        /// </summary>
        public static string GlobalHwPosPrinterConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalHwPosPrinterConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení bankovní tískárny'.
        /// </summary>
        public static string GlobalHwPosPrinterConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalHwPosPrinterConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Váha'.
        /// </summary>
        public static string GlobalHwScaleConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalHwScaleConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace váhy'.
        /// </summary>
        public static string GlobalHwScaleConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalHwScaleConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'VGA zákaznický displej'.
        /// </summary>
        public static string GlobalHwVgaClientDisplayConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalHwVgaClientDisplayConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení VGA zákaznického displeje'.
        /// </summary>
        public static string GlobalHwVgaClientDisplayConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalHwVgaClientDisplayConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Účetní půlnoc'.
        /// </summary>
        public static string GlobalRulesAccountingMidnightConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalRulesAccountingMidnightConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace parametrů souvisejících s účetní půlnocí'.
        /// </summary>
        public static string GlobalRulesAccountingMidnightConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalRulesAccountingMidnightConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Uzávěrka'.
        /// </summary>
        public static string GlobalRulesBalanceConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalRulesBalanceConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace uzávěrky'.
        /// </summary>
        public static string GlobalRulesBalanceConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalRulesBalanceConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Výdejna'.
        /// </summary>
        public static string GlobalRulesCanteenConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalRulesCanteenConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace výdejny'.
        /// </summary>
        public static string GlobalRulesCanteenConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalRulesCanteenConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Správa identifikačních karet'.
        /// </summary>
        public static string GlobalRulesCardManagementConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalRulesCardManagementConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace identifikačních karet používaných v systému'.
        /// </summary>
        public static string GlobalRulesCardManagementConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalRulesCardManagementConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Souhrn pokladny'.
        /// </summary>
        public static string GlobalRulesCashDeskConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalRulesCashDeskConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace souhrnu pokladny'.
        /// </summary>
        public static string GlobalRulesCashDeskConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalRulesCashDeskConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Hotovostní operace pro klienty'.
        /// </summary>
        public static string GlobalRulesCashOperationsConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalRulesCashOperationsConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace hotovostních operací pro klienty'.
        /// </summary>
        public static string GlobalRulesCashOperationsConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalRulesCashOperationsConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Správa klientů'.
        /// </summary>
        public static string GlobalRulesClientManagementConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalRulesClientManagementConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace správy klientů'.
        /// </summary>
        public static string GlobalRulesClientManagementConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalRulesClientManagementConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Uživatelské jméno'.
        /// </summary>
        public static string GlobalRulesClientUserNameConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalRulesClientUserNameConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace povolených znaků uživatelského jména pro WebKredit. Nastavuje se v evidenčním listu klienta.'.
        /// </summary>
        public static string GlobalRulesClientUserNameConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalRulesClientUserNameConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Inkaso'.
        /// </summary>
        public static string GlobalRulesCollectionConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalRulesCollectionConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace inkasa'.
        /// </summary>
        public static string GlobalRulesCollectionConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalRulesCollectionConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Kurz cizí měny'.
        /// </summary>
        public static string GlobalRulesExchangeRateConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalRulesExchangeRateConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Způsob stanovení kurzu cizí měny'.
        /// </summary>
        public static string GlobalRulesExchangeRateConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalRulesExchangeRateConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to ''.
        /// </summary>
        public static string GlobalRulesGdprClientInfoProtectionConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalRulesGdprClientInfoProtectionConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to ''.
        /// </summary>
        public static string GlobalRulesGdprClientInfoProtectionConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalRulesGdprClientInfoProtectionConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Zpracování osobních údajů'.
        /// </summary>
        public static string GlobalRulesGdprPersonalDataAgreementConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalRulesGdprPersonalDataAgreementConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace pro souhlasy se zpracováním osobních údajů'.
        /// </summary>
        public static string GlobalRulesGdprPersonalDataAgreementConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalRulesGdprPersonalDataAgreementConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Jídla a jídelníček'.
        /// </summary>
        public static string GlobalRulesMenuConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalRulesMenuConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace jídelníčku, jídel, dotací'.
        /// </summary>
        public static string GlobalRulesMenuConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalRulesMenuConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nutriční hodnoty'.
        /// </summary>
        public static string GlobalRulesNutritionalValuesConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalRulesNutritionalValuesConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení nutričních hodnot. Používá se v PM, kde se tímto nastavením řídí zobrazení nutričních hodnot v sestavě pro přihlášeného klienta. V Kan8 se dle tohoto nastavení inicializuje sestava s nutričními hodnotami pro daného klienta. Uplatní se pouze pokud existuje licence.'.
        /// </summary>
        public static string GlobalRulesNutritionalValuesConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalRulesNutritionalValuesConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Objednávání'.
        /// </summary>
        public static string GlobalRulesOrderingConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalRulesOrderingConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace objednávání jídel'.
        /// </summary>
        public static string GlobalRulesOrderingConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalRulesOrderingConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Složitost hesla'.
        /// </summary>
        public static string GlobalRulesPasswordConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalRulesPasswordConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace složitosti hesla (jeho délka, povolené znaky, atd.)'.
        /// </summary>
        public static string GlobalRulesPasswordConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalRulesPasswordConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Pravidla zaokrouhlování'.
        /// </summary>
        public static string GlobalRulesRoundingConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalRulesRoundingConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace pravidel zaokrouhlování pro paragon, položky paragonu a měnu'.
        /// </summary>
        public static string GlobalRulesRoundingConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalRulesRoundingConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace sestavy - Přehled útrat a příspěvků'.
        /// </summary>
        public static string GlobalRulesSpendingAndSubsidyConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalRulesSpendingAndSubsidyConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace zpřístupnění sestavy a nastavení ceníkových složek.'.
        /// </summary>
        public static string GlobalRulesSpendingAndSubsidyConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalRulesSpendingAndSubsidyConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Sazba DPH'.
        /// </summary>
        public static string GlobalRulesVatRateConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalRulesVatRateConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Pravidla pro nastavení sazeb DPH'.
        /// </summary>
        public static string GlobalRulesVatRateConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalRulesVatRateConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Aplikační server'.
        /// </summary>
        public static string GlobalServicesAppServerConnectionConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalServicesAppServerConnectionConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace připojení k aplikačnímu serveru'.
        /// </summary>
        public static string GlobalServicesAppServerConnectionConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalServicesAppServerConnectionConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Přihlášení k databázi'.
        /// </summary>
        public static string GlobalServicesDbConnectionConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalServicesDbConnectionConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Přihlašovací údaje k databázím'.
        /// </summary>
        public static string GlobalServicesDbConnectionConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalServicesDbConnectionConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Údržba databáze'.
        /// </summary>
        public static string GlobalServicesDbMaintenanceConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalServicesDbMaintenanceConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace údržby databáze'.
        /// </summary>
        public static string GlobalServicesDbMaintenanceConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalServicesDbMaintenanceConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Přihlašovací údaje'.
        /// </summary>
        public static string GlobalServicesFbsCredentialsConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalServicesFbsCredentialsConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Přihlašovací údaje pro WCF služby. '.
        /// </summary>
        public static string GlobalServicesFbsCredentialsConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalServicesFbsCredentialsConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Služby'.
        /// </summary>
        public static string GlobalServicesFbsServicesConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalServicesFbsServicesConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace WCF služeb'.
        /// </summary>
        public static string GlobalServicesFbsServicesConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalServicesFbsServicesConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Přihlašovací údaje'.
        /// </summary>
        public static string GlobalServicesHelpDeskCredentialsConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalServicesHelpDeskCredentialsConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Přihlašovací údaje pro WCF služby.'.
        /// </summary>
        public static string GlobalServicesHelpDeskCredentialsConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalServicesHelpDeskCredentialsConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Služby'.
        /// </summary>
        public static string GlobalServicesHelpDeskServicesConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalServicesHelpDeskServicesConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace WCF služeb'.
        /// </summary>
        public static string GlobalServicesHelpDeskServicesConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalServicesHelpDeskServicesConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'ICoupon'.
        /// </summary>
        public static string GlobalServicesICouponConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalServicesICouponConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Globální nastavení pro ICoupon tedy nastavení přihlašování/uživatelských údajů,... '.
        /// </summary>
        public static string GlobalServicesICouponConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalServicesICouponConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'IPS'.
        /// </summary>
        public static string GlobalServicesIpsConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalServicesIpsConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace interního platebního systému'.
        /// </summary>
        public static string GlobalServicesIpsConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalServicesIpsConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Logování'.
        /// </summary>
        public static string GlobalServicesLoggingConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalServicesLoggingConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace logování'.
        /// </summary>
        public static string GlobalServicesLoggingConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalServicesLoggingConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Platební brána'.
        /// </summary>
        public static string GlobalServicesPaymentGateConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalServicesPaymentGateConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení platební brány'.
        /// </summary>
        public static string GlobalServicesPaymentGateConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalServicesPaymentGateConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to ''.
        /// </summary>
        public static string GlobalServicesReportServiceConnectionConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalServicesReportServiceConnectionConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to ''.
        /// </summary>
        public static string GlobalServicesReportServiceConnectionConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalServicesReportServiceConnectionConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'SMTP'.
        /// </summary>
        public static string GlobalServicesSmtpConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalServicesSmtpConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace SMTP serveru'.
        /// </summary>
        public static string GlobalServicesSmtpConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalServicesSmtpConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení odchozí HTTP proxy'.
        /// </summary>
        public static string GlobalServicesWebProxyConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalServicesWebProxyConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace pro nastavení odchozí HTTP proxy'.
        /// </summary>
        public static string GlobalServicesWebProxyConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalServicesWebProxyConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'SysLog'.
        /// </summary>
        public static string GlobalServiceSysLogConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalServiceSysLogConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení SysLog serveru, na který mohou být Schedulerem odesílany logy.'.
        /// </summary>
        public static string GlobalServiceSysLogConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalServiceSysLogConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Support ANETE'.
        /// </summary>
        public static string GlobalSupportContactsAneteConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalSupportContactsAneteConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Kontakty na support ANETE'.
        /// </summary>
        public static string GlobalSupportContactsAneteConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalSupportContactsAneteConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Support u zákazníka'.
        /// </summary>
        public static string GlobalSupportContactsLocalConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalSupportContactsLocalConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Kontakty na support zákazníka'.
        /// </summary>
        public static string GlobalSupportContactsLocalConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalSupportContactsLocalConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Vzdálená správa'.
        /// </summary>
        public static string GlobalSupportRemoteAccessConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalSupportRemoteAccessConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace vzdálené správy. Zjednodušuje spouštění TeamVieweru.'.
        /// </summary>
        public static string GlobalSupportRemoteAccessConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalSupportRemoteAccessConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Přistupová práva'.
        /// </summary>
        public static string GlobalSyncAccessRightsConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalSyncAccessRightsConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Přistupová práva k synchronizovaným uživatelům'.
        /// </summary>
        public static string GlobalSyncAccessRightsConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalSyncAccessRightsConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Synchronizace strávníků'.
        /// </summary>
        public static string GlobalSyncClientsConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalSyncClientsConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Synchronizace strávníků'.
        /// </summary>
        public static string GlobalSyncClientsConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalSyncClientsConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Synchronizace uživatelů'.
        /// </summary>
        public static string GlobalSyncUsersConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalSyncUsersConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Synchronizace uživatelů'.
        /// </summary>
        public static string GlobalSyncUsersConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalSyncUsersConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Vlastník licence'.
        /// </summary>
        public static string GlobalSystemOwnerConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalSystemOwnerConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace parametrů vlastníka licence'.
        /// </summary>
        public static string GlobalSystemOwnerConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GlobalSystemOwnerConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to ''.
        /// </summary>
        public static string GraphicAccommodationCapacityAppearanceColorsConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GraphicAccommodationCapacityAppearanceColorsConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to ''.
        /// </summary>
        public static string GraphicAccommodationCapacityAppearanceColorsConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GraphicAccommodationCapacityAppearanceColorsConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Paragon'.
        /// </summary>
        public static string GuestOrderingBehaviourParagonConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.GuestOrderingBehaviourParagonConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace vytváření paragonu'.
        /// </summary>
        public static string GuestOrderingBehaviourParagonConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.GuestOrderingBehaviourParagonConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Hotel'.
        /// </summary>
        public static string HotelGeneralConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.HotelGeneralConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Hotel'.
        /// </summary>
        public static string HotelGeneralConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.HotelGeneralConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Client'.
        /// </summary>
        public static string InfoProClientConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.InfoProClientConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Client'.
        /// </summary>
        public static string InfoProClientConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.InfoProClientConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Server'.
        /// </summary>
        public static string InfoProServerConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.InfoProServerConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Server'.
        /// </summary>
        public static string InfoProServerConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.InfoProServerConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to ''.
        /// </summary>
        public static string LegendsAccommodationCapacityAppearanceColorsConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.LegendsAccommodationCapacityAppearanceColorsConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to ''.
        /// </summary>
        public static string LegendsAccommodationCapacityAppearanceColorsConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.LegendsAccommodationCapacityAppearanceColorsConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Mistrovské objednávání'.
        /// </summary>
        public static string MastersOrderingGeneralConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.MastersOrderingGeneralConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Mistrovské objednávání'.
        /// </summary>
        public static string MastersOrderingGeneralConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.MastersOrderingGeneralConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Vzhled pozadí'.
        /// </summary>
        public static string MenuPresenterAppearanceBackgroundConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.MenuPresenterAppearanceBackgroundConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Vzhled pozadí'.
        /// </summary>
        public static string MenuPresenterAppearanceBackgroundConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.MenuPresenterAppearanceBackgroundConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Vzhled grupy na první úrovni'.
        /// </summary>
        public static string MenuPresenterAppearanceFirstLevelHeaderConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.MenuPresenterAppearanceFirstLevelHeaderConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Vzhled grupy na první úrovni'.
        /// </summary>
        public static string MenuPresenterAppearanceFirstLevelHeaderConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.MenuPresenterAppearanceFirstLevelHeaderConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Vzhled a velikost loga'.
        /// </summary>
        public static string MenuPresenterAppearanceLogoConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.MenuPresenterAppearanceLogoConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Vzhled a velikost loga'.
        /// </summary>
        public static string MenuPresenterAppearanceLogoConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.MenuPresenterAppearanceLogoConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Vzhled jídelníčku'.
        /// </summary>
        public static string MenuPresenterAppearanceMenuConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.MenuPresenterAppearanceMenuConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Vzhled jídelníčku'.
        /// </summary>
        public static string MenuPresenterAppearanceMenuConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.MenuPresenterAppearanceMenuConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Vzhled hlavičky se sloupci v jídelníčku'.
        /// </summary>
        public static string MenuPresenterAppearanceMenuHeaderConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.MenuPresenterAppearanceMenuHeaderConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Vzhled hlavičky se sloupci v jídelníčku'.
        /// </summary>
        public static string MenuPresenterAppearanceMenuHeaderConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.MenuPresenterAppearanceMenuHeaderConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Vzhled grupy na druhé úrovni'.
        /// </summary>
        public static string MenuPresenterAppearanceSecondLevelHeaderConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.MenuPresenterAppearanceSecondLevelHeaderConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Vzhled grupy na druhé úrovni. Uplatní se pouze pokud je využito tydenní menu'.
        /// </summary>
        public static string MenuPresenterAppearanceSecondLevelHeaderConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.MenuPresenterAppearanceSecondLevelHeaderConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Vzhled textů na hlavní obrazovce'.
        /// </summary>
        public static string MenuPresenterAppearanceTextConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.MenuPresenterAppearanceTextConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Vzhled textů na hlavní obrazovce'.
        /// </summary>
        public static string MenuPresenterAppearanceTextConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.MenuPresenterAppearanceTextConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Sloupce'.
        /// </summary>
        public static string MenuPresenterBehaviourColumnsConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.MenuPresenterBehaviourColumnsConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení jednotlivých sloupců jídelníčku. Umožňuje definovat jejich pořadí, to zda mají být zobrazeny a také konfiguraci specifickou pro každý typ sloupce.'.
        /// </summary>
        public static string MenuPresenterBehaviourColumnsConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.MenuPresenterBehaviourColumnsConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení počtů'.
        /// </summary>
        public static string MenuPresenterBehaviourMealCountsConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.MenuPresenterBehaviourMealCountsConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace nastavení počtů'.
        /// </summary>
        public static string MenuPresenterBehaviourMealCountsConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.MenuPresenterBehaviourMealCountsConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení jídelníčku'.
        /// </summary>
        public static string MenuPresenterBehaviourMenuConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.MenuPresenterBehaviourMenuConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení filtrování a dálších položek jídelníčku. Dovoluje zvolit jaké alternativy a druhy jídel mají byt zobrazeny.'.
        /// </summary>
        public static string MenuPresenterBehaviourMenuConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.MenuPresenterBehaviourMenuConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Filtr jídelníčku'.
        /// </summary>
        public static string MenuPresenterBehaviourMenuFilterConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.MenuPresenterBehaviourMenuFilterConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Filt jídelníčku'.
        /// </summary>
        public static string MenuPresenterBehaviourMenuFilterConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.MenuPresenterBehaviourMenuFilterConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Poznámka'.
        /// </summary>
        public static string MenuPresenterBehaviourNoteConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.MenuPresenterBehaviourNoteConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace nastavení zobrazení poznámky. Definuje se, jaké informace budou v poznámce zobrazeny.'.
        /// </summary>
        public static string MenuPresenterBehaviourNoteConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.MenuPresenterBehaviourNoteConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Rolování'.
        /// </summary>
        public static string MenuPresenterBehaviourRollConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.MenuPresenterBehaviourRollConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Rolování jídelníčku'.
        /// </summary>
        public static string MenuPresenterBehaviourRollConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.MenuPresenterBehaviourRollConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Uživatelské rozhraní'.
        /// </summary>
        public static string MenuPresenterBehaviourUserInterfaceConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.MenuPresenterBehaviourUserInterfaceConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace uživatelského rozhraní'.
        /// </summary>
        public static string MenuPresenterBehaviourUserInterfaceConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.MenuPresenterBehaviourUserInterfaceConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'IP kamera'.
        /// </summary>
        public static string MenuPresenterHwIpCamConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.MenuPresenterHwIpCamConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace IP kamery'.
        /// </summary>
        public static string MenuPresenterHwIpCamConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.MenuPresenterHwIpCamConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Autorizace'.
        /// </summary>
        public static string MobileOrderingAuthenticationConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.MobileOrderingAuthenticationConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace autorizace mobilního objednávání'.
        /// </summary>
        public static string MobileOrderingAuthenticationConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.MobileOrderingAuthenticationConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Gdpr'.
        /// </summary>
        public static string MobileOrderingBehaviorAccountGdprConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.MobileOrderingBehaviorAccountGdprConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení záložky Gdpr'.
        /// </summary>
        public static string MobileOrderingBehaviorAccountGdprConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.MobileOrderingBehaviorAccountGdprConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Menu'.
        /// </summary>
        public static string MobileOrderingBehaviorMenuConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.MobileOrderingBehaviorMenuConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Zobrazení menu mobilního objednávání'.
        /// </summary>
        public static string MobileOrderingBehaviorMenuConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.MobileOrderingBehaviorMenuConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení cen'.
        /// </summary>
        public static string MobileOrderingBehaviourPriceSetupConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.MobileOrderingBehaviourPriceSetupConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace parametrů pro zobrazení a nastavení cen'.
        /// </summary>
        public static string MobileOrderingBehaviourPriceSetupConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.MobileOrderingBehaviourPriceSetupConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'WebKredit'.
        /// </summary>
        public static string MobileOrderingBehaviourWebKreditConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.MobileOrderingBehaviourWebKreditConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení spojení MobilKreditu s WebKreditem'.
        /// </summary>
        public static string MobileOrderingBehaviourWebKreditConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.MobileOrderingBehaviourWebKreditConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Sklady'.
        /// </summary>
        public static string MobileStockBehaviorStocksConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.MobileStockBehaviorStocksConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení dostupných skladů v mobilní inventuře. Zatím jen jeden parametr, ID skladu.'.
        /// </summary>
        public static string MobileStockBehaviorStocksConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.MobileStockBehaviorStocksConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení aplikace'.
        /// </summary>
        public static string MobileStockBehaviourConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.MobileStockBehaviourConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení aplikace'.
        /// </summary>
        public static string MobileStockBehaviourConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.MobileStockBehaviourConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'EET'.
        /// </summary>
        public static string OfficeBehaviorEetConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeBehaviorEetConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení pro EET'.
        /// </summary>
        public static string OfficeBehaviorEetConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeBehaviorEetConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Vícenásobné spuštění aplikace'.
        /// </summary>
        public static string OfficeBehaviourAppSingleInstanceConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeBehaviourAppSingleInstanceConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace (ne)možnosti vícenásobného spuštění dané aplikace.'.
        /// </summary>
        public static string OfficeBehaviourAppSingleInstanceConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeBehaviourAppSingleInstanceConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Uzávěrka'.
        /// </summary>
        public static string OfficeBehaviourBalanceConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeBehaviourBalanceConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace uzávěrky'.
        /// </summary>
        public static string OfficeBehaviourBalanceConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeBehaviourBalanceConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Náhradní objednávání'.
        /// </summary>
        public static string OfficeBehaviourBatchOrdering_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeBehaviourBatchOrdering_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace náhradního objednávání'.
        /// </summary>
        public static string OfficeBehaviourBatchOrdering_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeBehaviourBatchOrdering_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Kalkulovaný ceník'.
        /// </summary>
        public static string OfficeBehaviourCalcPriceList_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeBehaviourCalcPriceList_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace kalkulovaného ceníku'.
        /// </summary>
        public static string OfficeBehaviourCalcPriceList_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeBehaviourCalcPriceList_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Správa karet'.
        /// </summary>
        public static string OfficeBehaviourCardManagement_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeBehaviourCardManagement_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace správy karet'.
        /// </summary>
        public static string OfficeBehaviourCardManagement_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeBehaviourCardManagement_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Hodnota filtru'.
        /// </summary>
        public static string OfficeBehaviourFilterSettingsConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeBehaviourFilterSettingsConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Umožňuje nastavit výchozí hodnotu filtru ve formulářích na &quot;Nic&quot; - není vybrána žádná  hodnota ze seznamu. Definuje se pro každý formulář zvlášť.'.
        /// </summary>
        public static string OfficeBehaviourFilterSettingsConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeBehaviourFilterSettingsConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Stravenky'.
        /// </summary>
        public static string OfficeBehaviourMealTicketsConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeBehaviourMealTicketsConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace stravenek'.
        /// </summary>
        public static string OfficeBehaviourMealTicketsConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeBehaviourMealTicketsConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Cenotvorba'.
        /// </summary>
        public static string OfficeBehaviourPricingConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeBehaviourPricingConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace cenotvorby'.
        /// </summary>
        public static string OfficeBehaviourPricingConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeBehaviourPricingConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Tisk paragonu'.
        /// </summary>
        public static string OfficeBehaviourSalesSlipPrintConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeBehaviourSalesSlipPrintConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace tisku paragonu'.
        /// </summary>
        public static string OfficeBehaviourSalesSlipPrintConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeBehaviourSalesSlipPrintConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení spouštění RDP'.
        /// </summary>
        public static string OfficeCentralManagementBehaviourRdpConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeCentralManagementBehaviourRdpConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení spouštění RDP souborů pro jednotlivé organizační jednotky'.
        /// </summary>
        public static string OfficeCentralManagementBehaviourRdpConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeCentralManagementBehaviourRdpConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Číslo účtu'.
        /// </summary>
        public static string OfficeClientsBehaviourBankAccountNumberConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeClientsBehaviourBankAccountNumberConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení čísla bankovního účtu'.
        /// </summary>
        public static string OfficeClientsBehaviourBankAccountNumberConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeClientsBehaviourBankAccountNumberConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Výdej karty'.
        /// </summary>
        public static string OfficeClientsBehaviourCardIssueConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeClientsBehaviourCardIssueConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace výdeje karty'.
        /// </summary>
        public static string OfficeClientsBehaviourCardIssueConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeClientsBehaviourCardIssueConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Sklad karet'.
        /// </summary>
        public static string OfficeClientsBehaviourCardStockConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeClientsBehaviourCardStockConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení skladu karet'.
        /// </summary>
        public static string OfficeClientsBehaviourCardStockConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeClientsBehaviourCardStockConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'E-Banking - import'.
        /// </summary>
        public static string OfficeClientsBehaviourEBankingImportConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeClientsBehaviourEBankingImportConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení importu plateb v E-Bankingu'.
        /// </summary>
        public static string OfficeClientsBehaviourEBankingImportConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeClientsBehaviourEBankingImportConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Zobrazení sestav internetového účtu'.
        /// </summary>
        public static string OfficeClientsBehaviourInternetAccountReportConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeClientsBehaviourInternetAccountReportConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Povoluje zobrazení sestav internetového účtu'.
        /// </summary>
        public static string OfficeClientsBehaviourInternetAccountReportConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeClientsBehaviourInternetAccountReportConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'E-Banking'.
        /// </summary>
        public static string OfficeEBankingConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeEBankingConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace E-Bankingu'.
        /// </summary>
        public static string OfficeEBankingConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeEBankingConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Bankovní tiskárna'.
        /// </summary>
        public static string OfficeHwPrinterConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeHwPrinterConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace bankovní tiskárny'.
        /// </summary>
        public static string OfficeHwPrinterConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeHwPrinterConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Licence'.
        /// </summary>
        public static string OfficeManagementBehaviourLicenseConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeManagementBehaviourLicenseConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení licencí k jednotlivým pivotům'.
        /// </summary>
        public static string OfficeManagementBehaviourLicenseConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeManagementBehaviourLicenseConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Poptávky distribuce jídel'.
        /// </summary>
        public static string OfficeMealDistributionBehaviourInquiriesConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeMealDistributionBehaviourInquiriesConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace poptávek distribuce jídel'.
        /// </summary>
        public static string OfficeMealDistributionBehaviourInquiriesConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeMealDistributionBehaviourInquiriesConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Přehled poptávek'.
        /// </summary>
        public static string OfficeMealDistributionBehaviourInquirySummaryConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeMealDistributionBehaviourInquirySummaryConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace přehledu poptávek'.
        /// </summary>
        public static string OfficeMealDistributionBehaviourInquirySummaryConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeMealDistributionBehaviourInquirySummaryConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Odpis jídel'.
        /// </summary>
        public static string OfficeMealDistributionBehaviourIssueSlipConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeMealDistributionBehaviourIssueSlipConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace odpisu jídel'.
        /// </summary>
        public static string OfficeMealDistributionBehaviourIssueSlipConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeMealDistributionBehaviourIssueSlipConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Dodací listy ALMED'.
        /// </summary>
        public static string OfficeMealDistributionReportsDeliveryAlmedConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeMealDistributionReportsDeliveryAlmedConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace dodacího listu ALMED'.
        /// </summary>
        public static string OfficeMealDistributionReportsDeliveryAlmedConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeMealDistributionReportsDeliveryAlmedConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Přehled prodaného zboží a jídel'.
        /// </summary>
        public static string OfficePriceMakingBehaviourSaleSummaryConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.OfficePriceMakingBehaviourSaleSummaryConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace cenových složek pro přehled (pivot) prodaného zboží a jídel'.
        /// </summary>
        public static string OfficePriceMakingBehaviourSaleSummaryConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.OfficePriceMakingBehaviourSaleSummaryConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Povolení kategorie dotace'.
        /// </summary>
        public static string OfficePriceMakingBehaviourSubsidyCategoryConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.OfficePriceMakingBehaviourSubsidyCategoryConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Povolení kategorie dotace v editoru Sazba DPH pro jídla povolí editaci jednotlivých kategorií dotace.'.
        /// </summary>
        public static string OfficePriceMakingBehaviourSubsidyCategoryConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.OfficePriceMakingBehaviourSubsidyCategoryConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Import do NISu'.
        /// </summary>
        public static string OfficeWorkplacesBehaviourImportNisConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeWorkplacesBehaviourImportNisConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení importu do NISu'.
        /// </summary>
        public static string OfficeWorkplacesBehaviourImportNisConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeWorkplacesBehaviourImportNisConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Typy jídla'.
        /// </summary>
        public static string OfficeWorkplacesBehaviourMealTypeConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeWorkplacesBehaviourMealTypeConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace typů jídel'.
        /// </summary>
        public static string OfficeWorkplacesBehaviourMealTypeConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeWorkplacesBehaviourMealTypeConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Editace jídelníčku'.
        /// </summary>
        public static string OfficeWorkplacesBehaviourMenuConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeWorkplacesBehaviourMenuConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace editoru jídelníčku'.
        /// </summary>
        public static string OfficeWorkplacesBehaviourMenuConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeWorkplacesBehaviourMenuConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Přehled změn dle objednávek'.
        /// </summary>
        public static string OfficeWorkplacesBehaviourOrderChangesConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeWorkplacesBehaviourOrderChangesConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení nabízených druhu jídel ve filtru u Přehledu změn dle objednávek. '.
        /// </summary>
        public static string OfficeWorkplacesBehaviourOrderChangesConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeWorkplacesBehaviourOrderChangesConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Ceník jídel'.
        /// </summary>
        public static string OfficeWorkplacesBehaviourPriceListConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeWorkplacesBehaviourPriceListConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace ceníku jídel (validace apod.)'.
        /// </summary>
        public static string OfficeWorkplacesBehaviourPriceListConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeWorkplacesBehaviourPriceListConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'CAH - Export celkového přehledu po jednotlivcích '.
        /// </summary>
        public static string OfficeWorkplacesReportsImpersonationCahConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeWorkplacesReportsImpersonationCahConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'CAH - Export celkového přehledu po jednotlivcích do formátu csv. Nastavení impersonace, názvu souboru a místa uložení souboru.'.
        /// </summary>
        public static string OfficeWorkplacesReportsImpersonationCahConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeWorkplacesReportsImpersonationCahConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Denní jídelníček'.
        /// </summary>
        public static string OfficeWorkplacesReportsTemplatesDayMenuConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeWorkplacesReportsTemplatesDayMenuConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace vzhledu denního jídelníčku'.
        /// </summary>
        public static string OfficeWorkplacesReportsTemplatesDayMenuConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeWorkplacesReportsTemplatesDayMenuConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Tydenní jídelníček'.
        /// </summary>
        public static string OfficeWorkplacesReportsTemplatesWeekMenuConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeWorkplacesReportsTemplatesWeekMenuConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace vzhledu tydenního jídelníčku'.
        /// </summary>
        public static string OfficeWorkplacesReportsTemplatesWeekMenuConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeWorkplacesReportsTemplatesWeekMenuConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Týdenní menu'.
        /// </summary>
        public static string OfficeWorkplacesReportsWeekMenuConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeWorkplacesReportsWeekMenuConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace sestavy týdenní menu'.
        /// </summary>
        public static string OfficeWorkplacesReportsWeekMenuConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.OfficeWorkplacesReportsWeekMenuConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Obecné'.
        /// </summary>
        public static string PatientOrderingBehaviourGeneralConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.PatientOrderingBehaviourGeneralConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Obecné'.
        /// </summary>
        public static string PatientOrderingBehaviourGeneralConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.PatientOrderingBehaviourGeneralConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Barvy'.
        /// </summary>
        public static string PresPointAppearanceColorsConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.PresPointAppearanceColorsConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace barev'.
        /// </summary>
        public static string PresPointAppearanceColorsConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.PresPointAppearanceColorsConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Písmo'.
        /// </summary>
        public static string PresPointAppearanceFontConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.PresPointAppearanceFontConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace písma'.
        /// </summary>
        public static string PresPointAppearanceFontConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.PresPointAppearanceFontConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Docházka'.
        /// </summary>
        public static string PresPointBahaviourAttendanceConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.PresPointBahaviourAttendanceConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace docházky'.
        /// </summary>
        public static string PresPointBahaviourAttendanceConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.PresPointBahaviourAttendanceConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Implicitní výdejna'.
        /// </summary>
        public static string PresPointBehaviourCanteenConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.PresPointBehaviourCanteenConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení způsobu určení implicitní výdejny po přihlášení strávníka'.
        /// </summary>
        public static string PresPointBehaviourCanteenConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.PresPointBehaviourCanteenConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Informace o klientovi'.
        /// </summary>
        public static string PresPointBehaviourClientInfoConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.PresPointBehaviourClientInfoConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace zobrazovaných informací o klientovi'.
        /// </summary>
        public static string PresPointBehaviourClientInfoConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.PresPointBehaviourClientInfoConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Opona'.
        /// </summary>
        public static string PresPointBehaviourCurtainConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.PresPointBehaviourCurtainConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace opony, která zabraňuje problikávání při zobrazení formuláře po přihlášení uživatele.'.
        /// </summary>
        public static string PresPointBehaviourCurtainConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.PresPointBehaviourCurtainConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Přihlášení/Odhlášení'.
        /// </summary>
        public static string PresPointBehaviourLoginConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.PresPointBehaviourLoginConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace přihlášení a odhlášení klienta.'.
        /// </summary>
        public static string PresPointBehaviourLoginConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.PresPointBehaviourLoginConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Stravenky'.
        /// </summary>
        public static string PresPointBehaviourMealTicketConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.PresPointBehaviourMealTicketConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace stravenek (omezení jejich tisku)'.
        /// </summary>
        public static string PresPointBehaviourMealTicketConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.PresPointBehaviourMealTicketConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Jídelníček'.
        /// </summary>
        public static string PresPointBehaviourMenuConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.PresPointBehaviourMenuConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace jídelníčku'.
        /// </summary>
        public static string PresPointBehaviourMenuConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.PresPointBehaviourMenuConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Filtrování jídel'.
        /// </summary>
        public static string PresPointBehaviourMenuMealKindFilterConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.PresPointBehaviourMenuMealKindFilterConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace skupiny jídel'.
        /// </summary>
        public static string PresPointBehaviourMenuMealKindFilterConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.PresPointBehaviourMenuMealKindFilterConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Poznámka'.
        /// </summary>
        public static string PresPointBehaviourNoteConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.PresPointBehaviourNoteConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace zobrazení poznámky'.
        /// </summary>
        public static string PresPointBehaviourNoteConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.PresPointBehaviourNoteConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Objednávka'.
        /// </summary>
        public static string PresPointBehaviourOrderConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.PresPointBehaviourOrderConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení obejednávek'.
        /// </summary>
        public static string PresPointBehaviourOrderConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.PresPointBehaviourOrderConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Vklad záloh přes platební terminál'.
        /// </summary>
        public static string PresPointBehaviourPaymentTermDepositConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.PresPointBehaviourPaymentTermDepositConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Vklad záloh přes platební terminál'.
        /// </summary>
        public static string PresPointBehaviourPaymentTermDepositConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.PresPointBehaviourPaymentTermDepositConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Šetřič obrazovky'.
        /// </summary>
        public static string PresPointBehaviourScreenSaverConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.PresPointBehaviourScreenSaverConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace šetříče obrazovky'.
        /// </summary>
        public static string PresPointBehaviourScreenSaverConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.PresPointBehaviourScreenSaverConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Jídelníček na přihlašovací obrazovce'.
        /// </summary>
        public static string PresPointBehaviourStartMenuConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.PresPointBehaviourStartMenuConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace jídelníčku na přihlašovací obrazovce'.
        /// </summary>
        public static string PresPointBehaviourStartMenuConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.PresPointBehaviourStartMenuConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Obrázky'.
        /// </summary>
        public static string PresPointBehaviourStartScreenPicturesConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.PresPointBehaviourStartScreenPicturesConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Obrázky na startovací obrazovce'.
        /// </summary>
        public static string PresPointBehaviourStartScreenPicturesConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.PresPointBehaviourStartScreenPicturesConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Uživatelské rozhraní'.
        /// </summary>
        public static string PresPointBehaviourUserInterfaceConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.PresPointBehaviourUserInterfaceConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace uživatelského rozhrani'.
        /// </summary>
        public static string PresPointBehaviourUserInterfaceConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.PresPointBehaviourUserInterfaceConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Timeouty PM'.
        /// </summary>
        public static string PresPointTimeoutConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.PresPointTimeoutConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Všechny timeouty používané v PM.'.
        /// </summary>
        public static string PresPointTimeoutConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.PresPointTimeoutConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Volání služeb AppServeru'.
        /// </summary>
        public static string SchedulerPluginsAppServerConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.SchedulerPluginsAppServerConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Volání služeb AppServeru'.
        /// </summary>
        public static string SchedulerPluginsAppServerConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.SchedulerPluginsAppServerConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Generování aktualizačních dávek'.
        /// </summary>
        public static string SchedulerPluginsAutoUpdaterConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.SchedulerPluginsAutoUpdaterConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení generování aktualizačních dávek'.
        /// </summary>
        public static string SchedulerPluginsAutoUpdaterConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.SchedulerPluginsAutoUpdaterConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Hromadné tisky'.
        /// </summary>
        public static string SchedulerPluginsBatchReportConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.SchedulerPluginsBatchReportConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Automatické spouštění hromadných tisků. V této chvíli probíha v Kanceláři. Je vhodné vyčlenit jedno ID zařízení Kanceláře, která se o to bude starat.'.
        /// </summary>
        public static string SchedulerPluginsBatchReportConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.SchedulerPluginsBatchReportConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Synchronizace menu s Fbs'.
        /// </summary>
        public static string SchedulerPluginsFbsMenuSyncConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.SchedulerPluginsFbsMenuSyncConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení synchronizace menu s Fbs'.
        /// </summary>
        public static string SchedulerPluginsFbsMenuSyncConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.SchedulerPluginsFbsMenuSyncConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Synchronizace s HelpDesk'.
        /// </summary>
        public static string SchedulerPluginsHelpDeskSyncConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.SchedulerPluginsHelpDeskSyncConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení synchronizace s HelpDesk'.
        /// </summary>
        public static string SchedulerPluginsHelpDeskSyncConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.SchedulerPluginsHelpDeskSyncConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Volálí služeb DB Kredit'.
        /// </summary>
        public static string SchedulerPluginsKreditSyncConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.SchedulerPluginsKreditSyncConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Volání služeb DB Kredit - Sazby DPH apod.'.
        /// </summary>
        public static string SchedulerPluginsKreditSyncConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.SchedulerPluginsKreditSyncConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Odesílání skladových pohybů'.
        /// </summary>
        public static string SchedulerPluginsStockMovementSenderConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.SchedulerPluginsStockMovementSenderConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení parametrů pro odesílání skladových pohybů na Fbs. 
        ///Konfigurace WCF služeb je v Global.Services.Fbs.Services a Global.Services.Fbs.Credentials.
        ///Pozor: Scheduler si přenačítá změny v konfiguraci každých 5 min. Při změně v konfiguraci nebo plánu není třeba scheduler restartovat.'.
        /// </summary>
        public static string SchedulerPluginsStockMovementSenderConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.SchedulerPluginsStockMovementSenderConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'AXXOS'.
        /// </summary>
        public static string SchedulerPrefixesAxxosConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.SchedulerPrefixesAxxosConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'AXXOS'.
        /// </summary>
        public static string SchedulerPrefixesAxxosConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.SchedulerPrefixesAxxosConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'GEREC'.
        /// </summary>
        public static string SchedulerPrefixesGerecConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.SchedulerPrefixesGerecConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'GEREC'.
        /// </summary>
        public static string SchedulerPrefixesGerecConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.SchedulerPrefixesGerecConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Doplňková tlačítka'.
        /// </summary>
        public static string ServePointAppearanceAdditionalButtonsConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.ServePointAppearanceAdditionalButtonsConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení vzhledu doplňkových tlačítek'.
        /// </summary>
        public static string ServePointAppearanceAdditionalButtonsConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.ServePointAppearanceAdditionalButtonsConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Vzhled aplikace'.
        /// </summary>
        public static string ServePointAppearanceApplicationConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.ServePointAppearanceApplicationConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení vzhledu celé aplikace.'.
        /// </summary>
        public static string ServePointAppearanceApplicationConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.ServePointAppearanceApplicationConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Základní tlačítka'.
        /// </summary>
        public static string ServePointAppearanceBasicButtonsConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.ServePointAppearanceBasicButtonsConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení vzhledu základních tlačítek'.
        /// </summary>
        public static string ServePointAppearanceBasicButtonsConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.ServePointAppearanceBasicButtonsConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Výstražná tlačítka'.
        /// </summary>
        public static string ServePointAppearanceExclamationButtonsConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.ServePointAppearanceExclamationButtonsConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení vzhledu výstražných tlačítek'.
        /// </summary>
        public static string ServePointAppearanceExclamationButtonsConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.ServePointAppearanceExclamationButtonsConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Tabulkové zobrazení'.
        /// </summary>
        public static string ServePointAppearanceGridViewConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.ServePointAppearanceGridViewConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení vzhledu tabulek.'.
        /// </summary>
        public static string ServePointAppearanceGridViewConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.ServePointAppearanceGridViewConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Jednoduché zobrazení'.
        /// </summary>
        public static string ServePointAppearanceSimpleViewConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.ServePointAppearanceSimpleViewConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení vzhledu jednoduchého zobrazení'.
        /// </summary>
        public static string ServePointAppearanceSimpleViewConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.ServePointAppearanceSimpleViewConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace prodeje jídel a sortimentu'.
        /// </summary>
        public static string ServePointBehaviourSellingSettings_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.ServePointBehaviourSellingSettings_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Obecné nastavení možností prodeje jídel a sortimentu'.
        /// </summary>
        public static string ServePointBehaviourSellingSettings_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.ServePointBehaviourSellingSettings_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigurace výdeje jídel'.
        /// </summary>
        public static string ServePointBehaviourServeringSettingsConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.ServePointBehaviourServeringSettingsConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Obecné nastavení možností výdeje jídel'.
        /// </summary>
        public static string ServePointBehaviourServeringSettingsConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.ServePointBehaviourServeringSettingsConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Uživatelské rozhraní'.
        /// </summary>
        public static string ServePointBehaviourUserInterfaceConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.ServePointBehaviourUserInterfaceConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení chování uživatelského rozhraní'.
        /// </summary>
        public static string ServePointBehaviourUserInterfaceConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.ServePointBehaviourUserInterfaceConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Zobrazení údajů o klientovi'.
        /// </summary>
        public static string ServePointDisplayClientPersonalDataConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.ServePointDisplayClientPersonalDataConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení zobrazovaných informací o klientovi'.
        /// </summary>
        public static string ServePointDisplayClientPersonalDataConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.ServePointDisplayClientPersonalDataConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Timeouty'.
        /// </summary>
        public static string ServePointTimeoutsConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.ServePointTimeoutsConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Všechny timeouty používané ve výdejním místě'.
        /// </summary>
        public static string ServePointTimeoutsConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.ServePointTimeoutsConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Monitorování odezvy sítě'.
        /// </summary>
        public static string ServiceMonitorBehaviourLatencyMonitorConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.ServiceMonitorBehaviourLatencyMonitorConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Monitorování odezvy sítě'.
        /// </summary>
        public static string ServiceMonitorBehaviourLatencyMonitorConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.ServiceMonitorBehaviourLatencyMonitorConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Zdrávé jídlo'.
        /// </summary>
        public static string SloHealthyMenuConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.SloHealthyMenuConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Konfigrace zdravého jídla. Definuje, které jídlo je prezentováno jako zdravé a seznam druhů jídel, z kterých se určí složky zdravého jídla.'.
        /// </summary>
        public static string SloHealthyMenuConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.SloHealthyMenuConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Služba UbyPort'.
        /// </summary>
        public static string UbyportConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.UbyportConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení webové služby UbyPort'.
        /// </summary>
        public static string UbyportConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.UbyportConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Párování s Kasou'.
        /// </summary>
        public static string WaiterCashDeskBehaviourCashDeskPairingConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.WaiterCashDeskBehaviourCashDeskPairingConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení párování Číšnické Kasy s běžnou Kasou'.
        /// </summary>
        public static string WaiterCashDeskBehaviourCashDeskPairingConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.WaiterCashDeskBehaviourCashDeskPairingConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Synchronizace s Kasou'.
        /// </summary>
        public static string WaiterCashDeskBehaviourSynchronizationConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.WaiterCashDeskBehaviourSynchronizationConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Synchronizace s Kasou'.
        /// </summary>
        public static string WaiterCashDeskBehaviourSynchronizationConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.WaiterCashDeskBehaviourSynchronizationConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Obecné'.
        /// </summary>
        public static string WaiterCashDeskGeneralConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.WaiterCashDeskGeneralConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Obecné nastavení'.
        /// </summary>
        public static string WaiterCashDeskGeneralConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.WaiterCashDeskGeneralConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'WebApi'.
        /// </summary>
        public static string WebApiGeneralConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.WebApiGeneralConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'WebApi'.
        /// </summary>
        public static string WebApiGeneralConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.WebApiGeneralConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Přehledy'.
        /// </summary>
        public static string WebKreditAccommodationsHistoryConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.WebKreditAccommodationsHistoryConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení záložek ostatních přehledů'.
        /// </summary>
        public static string WebKreditAccommodationsHistoryConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.WebKreditAccommodationsHistoryConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Ubytování'.
        /// </summary>
        public static string WebKreditAccommodationsOverviewConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.WebKreditAccommodationsOverviewConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení záložky Přehled ubytování'.
        /// </summary>
        public static string WebKreditAccommodationsOverviewConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.WebKreditAccommodationsOverviewConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'EBanking'.
        /// </summary>
        public static string WebKreditAccountEBankingConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.WebKreditAccountEBankingConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení záložky EBanking'.
        /// </summary>
        public static string WebKreditAccountEBankingConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.WebKreditAccountEBankingConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Gdpr'.
        /// </summary>
        public static string WebKreditAccountGdprConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.WebKreditAccountGdprConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení záložky Gdpr'.
        /// </summary>
        public static string WebKreditAccountGdprConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.WebKreditAccountGdprConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení účtu'.
        /// </summary>
        public static string WebKreditAccountSettingsConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.WebKreditAccountSettingsConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení záložky Nastavení účtu'.
        /// </summary>
        public static string WebKreditAccountSettingsConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.WebKreditAccountSettingsConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Autentifikace'.
        /// </summary>
        public static string WebKreditAuthenticationConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.WebKreditAuthenticationConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení autentifikace'.
        /// </summary>
        public static string WebKreditAuthenticationConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.WebKreditAuthenticationConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Bannery'.
        /// </summary>
        public static string WebKreditBannersConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.WebKreditBannersConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení cest k bannerům a reklamám'.
        /// </summary>
        public static string WebKreditBannersConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.WebKreditBannersConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Zpětná vazba'.
        /// </summary>
        public static string WebKreditFeedbackConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.WebKreditFeedbackConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení modulu Zpětná vazba'.
        /// </summary>
        public static string WebKreditFeedbackConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.WebKreditFeedbackConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Obecné'.
        /// </summary>
        public static string WebKreditGeneralConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.WebKreditGeneralConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Obecné nastavení'.
        /// </summary>
        public static string WebKreditGeneralConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.WebKreditGeneralConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Hlavička'.
        /// </summary>
        public static string WebKreditHeaderConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.WebKreditHeaderConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení hlavičky hlavní stránky'.
        /// </summary>
        public static string WebKreditHeaderConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.WebKreditHeaderConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Historie účtu'.
        /// </summary>
        public static string WebKreditHistoryConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.WebKreditHistoryConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení modulu Historie účtu'.
        /// </summary>
        public static string WebKreditHistoryConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.WebKreditHistoryConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Informace'.
        /// </summary>
        public static string WebKreditInformationsConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.WebKreditInformationsConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení modulu Informace'.
        /// </summary>
        public static string WebKreditInformationsConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.WebKreditInformationsConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Lokalizace'.
        /// </summary>
        public static string WebKreditLocalizationConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.WebKreditLocalizationConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení lokalizace'.
        /// </summary>
        public static string WebKreditLocalizationConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.WebKreditLocalizationConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Odesílání emailů'.
        /// </summary>
        public static string WebKreditMailingConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.WebKreditMailingConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení odesílání emailů'.
        /// </summary>
        public static string WebKreditMailingConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.WebKreditMailingConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Objednávání'.
        /// </summary>
        public static string WebKreditOrderingConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.WebKreditOrderingConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení modulu Objednávání'.
        /// </summary>
        public static string WebKreditOrderingConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.WebKreditOrderingConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Burza'.
        /// </summary>
        public static string WebKreditOrderingExchangesConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.WebKreditOrderingExchangesConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení záložky Burza'.
        /// </summary>
        public static string WebKreditOrderingExchangesConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.WebKreditOrderingExchangesConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Jídelníček - před přihlášením'.
        /// </summary>
        public static string WebKreditOrderingMenuAnonymousConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.WebKreditOrderingMenuAnonymousConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení záložky Jídelníček - před přihlášením'.
        /// </summary>
        public static string WebKreditOrderingMenuAnonymousConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.WebKreditOrderingMenuAnonymousConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Jídelníček - po přihlášení'.
        /// </summary>
        public static string WebKreditOrderingMenuAuthenticatedConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.WebKreditOrderingMenuAuthenticatedConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení záložky Jídelníček - po přihlášení'.
        /// </summary>
        public static string WebKreditOrderingMenuAuthenticatedConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.WebKreditOrderingMenuAuthenticatedConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Objednávky'.
        /// </summary>
        public static string WebKreditOrderingOrdersConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.WebKreditOrderingOrdersConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení záložky Objednávky'.
        /// </summary>
        public static string WebKreditOrderingOrdersConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.WebKreditOrderingOrdersConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Registrace'.
        /// </summary>
        public static string WebKreditRegistrationConfig_Brief {
            get {
                return ResourceManager.GetString(ResourceNames.WebKreditRegistrationConfig_Brief, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Nastavení záložky registrace'.
        /// </summary>
        public static string WebKreditRegistrationConfig_Detail {
            get {
                return ResourceManager.GetString(ResourceNames.WebKreditRegistrationConfig_Detail, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Lists all the resource names as constant string fields.
        /// </summary>
        public class ResourceNames {
            
            /// <summary>
            /// Stores the resource name 'AccommodationAppearanceLabelColorsConfig_Brief'.
            /// </summary>
            public const string AccommodationAppearanceLabelColorsConfig_Brief = "AccommodationAppearanceLabelColorsConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'AccommodationAppearanceLabelColorsConfig_Detail'.
            /// </summary>
            public const string AccommodationAppearanceLabelColorsConfig_Detail = "AccommodationAppearanceLabelColorsConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'AccommodationBehaviourAutoOrderingConfig_Brief'.
            /// </summary>
            public const string AccommodationBehaviourAutoOrderingConfig_Brief = "AccommodationBehaviourAutoOrderingConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'AccommodationBehaviourAutoOrderingConfig_Detail'.
            /// </summary>
            public const string AccommodationBehaviourAutoOrderingConfig_Detail = "AccommodationBehaviourAutoOrderingConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'AccommodationBehaviourSystemConfig_Brief'.
            /// </summary>
            public const string AccommodationBehaviourSystemConfig_Brief = "AccommodationBehaviourSystemConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'AccommodationBehaviourSystemConfig_Detail'.
            /// </summary>
            public const string AccommodationBehaviourSystemConfig_Detail = "AccommodationBehaviourSystemConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'AccommodationSchedulerConfig_Brief'.
            /// </summary>
            public const string AccommodationSchedulerConfig_Brief = "AccommodationSchedulerConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'AccommodationSchedulerConfig_Detail'.
            /// </summary>
            public const string AccommodationSchedulerConfig_Detail = "AccommodationSchedulerConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'AutoUpdaterBehaviourImpersonationConfig_Brief'.
            /// </summary>
            public const string AutoUpdaterBehaviourImpersonationConfig_Brief = "AutoUpdaterBehaviourImpersonationConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'AutoUpdaterBehaviourImpersonationConfig_Detail'.
            /// </summary>
            public const string AutoUpdaterBehaviourImpersonationConfig_Detail = "AutoUpdaterBehaviourImpersonationConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'AutoUpdaterBehaviourMonitorConfig_Brief'.
            /// </summary>
            public const string AutoUpdaterBehaviourMonitorConfig_Brief = "AutoUpdaterBehaviourMonitorConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'AutoUpdaterBehaviourMonitorConfig_Detail'.
            /// </summary>
            public const string AutoUpdaterBehaviourMonitorConfig_Detail = "AutoUpdaterBehaviourMonitorConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'AutoUpdaterDateTimeSynchronizationConfig_Brief'.
            /// </summary>
            public const string AutoUpdaterDateTimeSynchronizationConfig_Brief = "AutoUpdaterDateTimeSynchronizationConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'AutoUpdaterDateTimeSynchronizationConfig_Detail'.
            /// </summary>
            public const string AutoUpdaterDateTimeSynchronizationConfig_Detail = "AutoUpdaterDateTimeSynchronizationConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'BatchDataOperationsParametersConfig_Brief'.
            /// </summary>
            public const string BatchDataOperationsParametersConfig_Brief = "BatchDataOperationsParametersConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'BatchDataOperationsParametersConfig_Detail'.
            /// </summary>
            public const string BatchDataOperationsParametersConfig_Detail = "BatchDataOperationsParametersConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'CashDeskAccoutingMidnightConfig_Brief'.
            /// </summary>
            public const string CashDeskAccoutingMidnightConfig_Brief = "CashDeskAccoutingMidnightConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'CashDeskAccoutingMidnightConfig_Detail'.
            /// </summary>
            public const string CashDeskAccoutingMidnightConfig_Detail = "CashDeskAccoutingMidnightConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'CashDeskBehaviorEetConfig_Brief'.
            /// </summary>
            public const string CashDeskBehaviorEetConfig_Brief = "CashDeskBehaviorEetConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'CashDeskBehaviorEetConfig_Detail'.
            /// </summary>
            public const string CashDeskBehaviorEetConfig_Detail = "CashDeskBehaviorEetConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'CashDeskBehaviorICouponConfig_Brief'.
            /// </summary>
            public const string CashDeskBehaviorICouponConfig_Brief = "CashDeskBehaviorICouponConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'CashDeskBehaviorICouponConfig_Detail'.
            /// </summary>
            public const string CashDeskBehaviorICouponConfig_Detail = "CashDeskBehaviorICouponConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'CashDeskBehaviourAccommodationSystemConfig_Brief'.
            /// </summary>
            public const string CashDeskBehaviourAccommodationSystemConfig_Brief = "CashDeskBehaviourAccommodationSystemConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'CashDeskBehaviourAccommodationSystemConfig_Detail'.
            /// </summary>
            public const string CashDeskBehaviourAccommodationSystemConfig_Detail = "CashDeskBehaviourAccommodationSystemConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'CashDeskBehaviourBalanceSaleSlipConfig_Brief'.
            /// </summary>
            public const string CashDeskBehaviourBalanceSaleSlipConfig_Brief = "CashDeskBehaviourBalanceSaleSlipConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'CashDeskBehaviourBalanceSaleSlipConfig_Detail'.
            /// </summary>
            public const string CashDeskBehaviourBalanceSaleSlipConfig_Detail = "CashDeskBehaviourBalanceSaleSlipConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'CashDeskBehaviourGoodsSearchConfig_Brief'.
            /// </summary>
            public const string CashDeskBehaviourGoodsSearchConfig_Brief = "CashDeskBehaviourGoodsSearchConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'CashDeskBehaviourGoodsSearchConfig_Detail'.
            /// </summary>
            public const string CashDeskBehaviourGoodsSearchConfig_Detail = "CashDeskBehaviourGoodsSearchConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'CashDeskBehaviourLockConfig_Brief'.
            /// </summary>
            public const string CashDeskBehaviourLockConfig_Brief = "CashDeskBehaviourLockConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'CashDeskBehaviourLockConfig_Detail'.
            /// </summary>
            public const string CashDeskBehaviourLockConfig_Detail = "CashDeskBehaviourLockConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'CashDeskBehaviourLoginConfig_Brief'.
            /// </summary>
            public const string CashDeskBehaviourLoginConfig_Brief = "CashDeskBehaviourLoginConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'CashDeskBehaviourLoginConfig_Detail'.
            /// </summary>
            public const string CashDeskBehaviourLoginConfig_Detail = "CashDeskBehaviourLoginConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'CashDeskBehaviourMiscellaneousConfig_Brief'.
            /// </summary>
            public const string CashDeskBehaviourMiscellaneousConfig_Brief = "CashDeskBehaviourMiscellaneousConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'CashDeskBehaviourMiscellaneousConfig_Detail'.
            /// </summary>
            public const string CashDeskBehaviourMiscellaneousConfig_Detail = "CashDeskBehaviourMiscellaneousConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'CashDeskBehaviourNotesConfig_Brief'.
            /// </summary>
            public const string CashDeskBehaviourNotesConfig_Brief = "CashDeskBehaviourNotesConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'CashDeskBehaviourNotesConfig_Detail'.
            /// </summary>
            public const string CashDeskBehaviourNotesConfig_Detail = "CashDeskBehaviourNotesConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'CashDeskBehaviourPaymentVoucherConfig_Brief'.
            /// </summary>
            public const string CashDeskBehaviourPaymentVoucherConfig_Brief = "CashDeskBehaviourPaymentVoucherConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'CashDeskBehaviourPaymentVoucherConfig_Detail'.
            /// </summary>
            public const string CashDeskBehaviourPaymentVoucherConfig_Detail = "CashDeskBehaviourPaymentVoucherConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'CashDeskBehaviourRestaurantConfig_Brief'.
            /// </summary>
            public const string CashDeskBehaviourRestaurantConfig_Brief = "CashDeskBehaviourRestaurantConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'CashDeskBehaviourRestaurantConfig_Detail'.
            /// </summary>
            public const string CashDeskBehaviourRestaurantConfig_Detail = "CashDeskBehaviourRestaurantConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'CashDeskBehaviourSaleSlipSplitConfig_Brief'.
            /// </summary>
            public const string CashDeskBehaviourSaleSlipSplitConfig_Brief = "CashDeskBehaviourSaleSlipSplitConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'CashDeskBehaviourSaleSlipSplitConfig_Detail'.
            /// </summary>
            public const string CashDeskBehaviourSaleSlipSplitConfig_Detail = "CashDeskBehaviourSaleSlipSplitConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'CashDeskBehaviourSalesVoucherConfig_Brief'.
            /// </summary>
            public const string CashDeskBehaviourSalesVoucherConfig_Brief = "CashDeskBehaviourSalesVoucherConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'CashDeskBehaviourSalesVoucherConfig_Detail'.
            /// </summary>
            public const string CashDeskBehaviourSalesVoucherConfig_Detail = "CashDeskBehaviourSalesVoucherConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'CashDeskBehaviourServiceDisplayConfig_Brief'.
            /// </summary>
            public const string CashDeskBehaviourServiceDisplayConfig_Brief = "CashDeskBehaviourServiceDisplayConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'CashDeskBehaviourServiceDisplayConfig_Detail'.
            /// </summary>
            public const string CashDeskBehaviourServiceDisplayConfig_Detail = "CashDeskBehaviourServiceDisplayConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'CashDeskBehaviourTaraConfig_Brief'.
            /// </summary>
            public const string CashDeskBehaviourTaraConfig_Brief = "CashDeskBehaviourTaraConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'CashDeskBehaviourTaraConfig_Detail'.
            /// </summary>
            public const string CashDeskBehaviourTaraConfig_Detail = "CashDeskBehaviourTaraConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'CashDeskBehaviourTouchConfig_Brief'.
            /// </summary>
            public const string CashDeskBehaviourTouchConfig_Brief = "CashDeskBehaviourTouchConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'CashDeskBehaviourTouchConfig_Detail'.
            /// </summary>
            public const string CashDeskBehaviourTouchConfig_Detail = "CashDeskBehaviourTouchConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'CashDeskBehaviourUserInterfaceConfig_Brief'.
            /// </summary>
            public const string CashDeskBehaviourUserInterfaceConfig_Brief = "CashDeskBehaviourUserInterfaceConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'CashDeskBehaviourUserInterfaceConfig_Detail'.
            /// </summary>
            public const string CashDeskBehaviourUserInterfaceConfig_Detail = "CashDeskBehaviourUserInterfaceConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'CashDeskBehaviourVatRateConfig_Brief'.
            /// </summary>
            public const string CashDeskBehaviourVatRateConfig_Brief = "CashDeskBehaviourVatRateConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'CashDeskBehaviourVatRateConfig_Detail'.
            /// </summary>
            public const string CashDeskBehaviourVatRateConfig_Detail = "CashDeskBehaviourVatRateConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'CashDeskDataStorageConfig_Brief'.
            /// </summary>
            public const string CashDeskDataStorageConfig_Brief = "CashDeskDataStorageConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'CashDeskDataStorageConfig_Detail'.
            /// </summary>
            public const string CashDeskDataStorageConfig_Detail = "CashDeskDataStorageConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'CashDeskFiscalModuleConfig_Brief'.
            /// </summary>
            public const string CashDeskFiscalModuleConfig_Brief = "CashDeskFiscalModuleConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'CashDeskFiscalModuleConfig_Detail'.
            /// </summary>
            public const string CashDeskFiscalModuleConfig_Detail = "CashDeskFiscalModuleConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'CashDeskHwClientDisplayConfig_Brief'.
            /// </summary>
            public const string CashDeskHwClientDisplayConfig_Brief = "CashDeskHwClientDisplayConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'CashDeskHwClientDisplayConfig_Detail'.
            /// </summary>
            public const string CashDeskHwClientDisplayConfig_Detail = "CashDeskHwClientDisplayConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'CashDeskHwEWalletConfig_Brief'.
            /// </summary>
            public const string CashDeskHwEWalletConfig_Brief = "CashDeskHwEWalletConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'CashDeskHwEWalletConfig_Detail'.
            /// </summary>
            public const string CashDeskHwEWalletConfig_Detail = "CashDeskHwEWalletConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'CashDeskManagerConfig_Brief'.
            /// </summary>
            public const string CashDeskManagerConfig_Brief = "CashDeskManagerConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'CashDeskManagerConfig_Detail'.
            /// </summary>
            public const string CashDeskManagerConfig_Detail = "CashDeskManagerConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'CashDeskOfflineConfig_Brief'.
            /// </summary>
            public const string CashDeskOfflineConfig_Brief = "CashDeskOfflineConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'CashDeskOfflineConfig_Detail'.
            /// </summary>
            public const string CashDeskOfflineConfig_Detail = "CashDeskOfflineConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'CashDeskPluginsGoodsExtendedInfoConfig_Brief'.
            /// </summary>
            public const string CashDeskPluginsGoodsExtendedInfoConfig_Brief = "CashDeskPluginsGoodsExtendedInfoConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'CashDeskPluginsGoodsExtendedInfoConfig_Detail'.
            /// </summary>
            public const string CashDeskPluginsGoodsExtendedInfoConfig_Detail = "CashDeskPluginsGoodsExtendedInfoConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'CashDeskPluginsTulPluginConfig_Brief'.
            /// </summary>
            public const string CashDeskPluginsTulPluginConfig_Brief = "CashDeskPluginsTulPluginConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'CashDeskPluginsTulPluginConfig_Detail'.
            /// </summary>
            public const string CashDeskPluginsTulPluginConfig_Detail = "CashDeskPluginsTulPluginConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'CashDeskSaleConfig_Brief'.
            /// </summary>
            public const string CashDeskSaleConfig_Brief = "CashDeskSaleConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'CashDeskSaleConfig_Detail'.
            /// </summary>
            public const string CashDeskSaleConfig_Detail = "CashDeskSaleConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'CashDeskStockModuleConfig_Brief'.
            /// </summary>
            public const string CashDeskStockModuleConfig_Brief = "CashDeskStockModuleConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'CashDeskStockModuleConfig_Detail'.
            /// </summary>
            public const string CashDeskStockModuleConfig_Detail = "CashDeskStockModuleConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'DevicesControllerBehaviourAppInstallationsConfig_Brief'.
            /// </summary>
            public const string DevicesControllerBehaviourAppInstallationsConfig_Brief = "DevicesControllerBehaviourAppInstallationsConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'DevicesControllerBehaviourAppInstallationsConfig_Detail'.
            /// </summary>
            public const string DevicesControllerBehaviourAppInstallationsConfig_Detail = "DevicesControllerBehaviourAppInstallationsConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'DevicesControllerOfflineConfig_Brief'.
            /// </summary>
            public const string DevicesControllerOfflineConfig_Brief = "DevicesControllerOfflineConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'DevicesControllerOfflineConfig_Detail'.
            /// </summary>
            public const string DevicesControllerOfflineConfig_Detail = "DevicesControllerOfflineConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'DevicesControllerServicesToolsConfig_Brief'.
            /// </summary>
            public const string DevicesControllerServicesToolsConfig_Brief = "DevicesControllerServicesToolsConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'DevicesControllerServicesToolsConfig_Detail'.
            /// </summary>
            public const string DevicesControllerServicesToolsConfig_Detail = "DevicesControllerServicesToolsConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalAuthenticationClientConfig_Brief'.
            /// </summary>
            public const string GlobalAuthenticationClientConfig_Brief = "GlobalAuthenticationClientConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalAuthenticationClientConfig_Detail'.
            /// </summary>
            public const string GlobalAuthenticationClientConfig_Detail = "GlobalAuthenticationClientConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalAuthenticationServerConfig_Brief'.
            /// </summary>
            public const string GlobalAuthenticationServerConfig_Brief = "GlobalAuthenticationServerConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalAuthenticationServerConfig_Detail'.
            /// </summary>
            public const string GlobalAuthenticationServerConfig_Detail = "GlobalAuthenticationServerConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviorCampaignsConfig_Brief'.
            /// </summary>
            public const string GlobalBehaviorCampaignsConfig_Brief = "GlobalBehaviorCampaignsConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviorCampaignsConfig_Detail'.
            /// </summary>
            public const string GlobalBehaviorCampaignsConfig_Detail = "GlobalBehaviorCampaignsConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviorEetConfig_Brief'.
            /// </summary>
            public const string GlobalBehaviorEetConfig_Brief = "GlobalBehaviorEetConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviorEetConfig_Detail'.
            /// </summary>
            public const string GlobalBehaviorEetConfig_Detail = "GlobalBehaviorEetConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviorEKasaConfig_Brief'.
            /// </summary>
            public const string GlobalBehaviorEKasaConfig_Brief = "GlobalBehaviorEKasaConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviorEKasaConfig_Detail'.
            /// </summary>
            public const string GlobalBehaviorEKasaConfig_Detail = "GlobalBehaviorEKasaConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviourAccessRightsAdministrationConfig_Brief'.
            /// </summary>
            public const string GlobalBehaviourAccessRightsAdministrationConfig_Brief = "GlobalBehaviourAccessRightsAdministrationConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviourAccessRightsAdministrationConfig_Detail'.
            /// </summary>
            public const string GlobalBehaviourAccessRightsAdministrationConfig_Detail = "GlobalBehaviourAccessRightsAdministrationConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviourApplicationCountryConfig_Brief'.
            /// </summary>
            public const string GlobalBehaviourApplicationCountryConfig_Brief = "GlobalBehaviourApplicationCountryConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviourApplicationCountryConfig_Detail'.
            /// </summary>
            public const string GlobalBehaviourApplicationCountryConfig_Detail = "GlobalBehaviourApplicationCountryConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviourApplicationLanguageConfig_Brief'.
            /// </summary>
            public const string GlobalBehaviourApplicationLanguageConfig_Brief = "GlobalBehaviourApplicationLanguageConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviourApplicationLanguageConfig_Detail'.
            /// </summary>
            public const string GlobalBehaviourApplicationLanguageConfig_Detail = "GlobalBehaviourApplicationLanguageConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviourApplicationLanguageDetailConfig_Brief'.
            /// </summary>
            public const string GlobalBehaviourApplicationLanguageDetailConfig_Brief = "GlobalBehaviourApplicationLanguageDetailConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviourApplicationLanguageDetailConfig_Detail'.
            /// </summary>
            public const string GlobalBehaviourApplicationLanguageDetailConfig_Detail = "GlobalBehaviourApplicationLanguageDetailConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviourAutoUpdatesConfig_Brief'.
            /// </summary>
            public const string GlobalBehaviourAutoUpdatesConfig_Brief = "GlobalBehaviourAutoUpdatesConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviourAutoUpdatesConfig_Detail'.
            /// </summary>
            public const string GlobalBehaviourAutoUpdatesConfig_Detail = "GlobalBehaviourAutoUpdatesConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviourClientAccommodationConfig_Brief'.
            /// </summary>
            public const string GlobalBehaviourClientAccommodationConfig_Brief = "GlobalBehaviourClientAccommodationConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviourClientAccommodationConfig_Detail'.
            /// </summary>
            public const string GlobalBehaviourClientAccommodationConfig_Detail = "GlobalBehaviourClientAccommodationConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviourClientAssignmentConfig_Brief'.
            /// </summary>
            public const string GlobalBehaviourClientAssignmentConfig_Brief = "GlobalBehaviourClientAssignmentConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviourClientAssignmentConfig_Detail'.
            /// </summary>
            public const string GlobalBehaviourClientAssignmentConfig_Detail = "GlobalBehaviourClientAssignmentConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviourClientConfig_Brief'.
            /// </summary>
            public const string GlobalBehaviourClientConfig_Brief = "GlobalBehaviourClientConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviourClientConfig_Detail'.
            /// </summary>
            public const string GlobalBehaviourClientConfig_Detail = "GlobalBehaviourClientConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviourClientForceChangePasswordConfig_Brief'.
            /// </summary>
            public const string GlobalBehaviourClientForceChangePasswordConfig_Brief = "GlobalBehaviourClientForceChangePasswordConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviourClientForceChangePasswordConfig_Detail'.
            /// </summary>
            public const string GlobalBehaviourClientForceChangePasswordConfig_Detail = "GlobalBehaviourClientForceChangePasswordConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviourClientRequireAgreementWithTermsConfig_Brief'.
            /// </summary>
            public const string GlobalBehaviourClientRequireAgreementWithTermsConfig_Brief = "GlobalBehaviourClientRequireAgreementWithTermsConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviourClientRequireAgreementWithTermsConfig_Detail'.
            /// </summary>
            public const string GlobalBehaviourClientRequireAgreementWithTermsConfig_Detail = "GlobalBehaviourClientRequireAgreementWithTermsConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviourDebugConfig_Brief'.
            /// </summary>
            public const string GlobalBehaviourDebugConfig_Brief = "GlobalBehaviourDebugConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviourDebugConfig_Detail'.
            /// </summary>
            public const string GlobalBehaviourDebugConfig_Detail = "GlobalBehaviourDebugConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviourDiscountSystemConfig_Brief'.
            /// </summary>
            public const string GlobalBehaviourDiscountSystemConfig_Brief = "GlobalBehaviourDiscountSystemConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviourDiscountSystemConfig_Detail'.
            /// </summary>
            public const string GlobalBehaviourDiscountSystemConfig_Detail = "GlobalBehaviourDiscountSystemConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviourFbsMenuConfig_Brief'.
            /// </summary>
            public const string GlobalBehaviourFbsMenuConfig_Brief = "GlobalBehaviourFbsMenuConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviourFbsMenuConfig_Detail'.
            /// </summary>
            public const string GlobalBehaviourFbsMenuConfig_Detail = "GlobalBehaviourFbsMenuConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviourKitchenOrderDisplayConfig_Brief'.
            /// </summary>
            public const string GlobalBehaviourKitchenOrderDisplayConfig_Brief = "GlobalBehaviourKitchenOrderDisplayConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviourKitchenOrderDisplayConfig_Detail'.
            /// </summary>
            public const string GlobalBehaviourKitchenOrderDisplayConfig_Detail = "GlobalBehaviourKitchenOrderDisplayConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviourKitchenOrderPrintConfig_Brief'.
            /// </summary>
            public const string GlobalBehaviourKitchenOrderPrintConfig_Brief = "GlobalBehaviourKitchenOrderPrintConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviourKitchenOrderPrintConfig_Detail'.
            /// </summary>
            public const string GlobalBehaviourKitchenOrderPrintConfig_Detail = "GlobalBehaviourKitchenOrderPrintConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviourMealTicketConfig_Brief'.
            /// </summary>
            public const string GlobalBehaviourMealTicketConfig_Brief = "GlobalBehaviourMealTicketConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviourMealTicketConfig_Detail'.
            /// </summary>
            public const string GlobalBehaviourMealTicketConfig_Detail = "GlobalBehaviourMealTicketConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviourMessageForClientsConfig_Brief'.
            /// </summary>
            public const string GlobalBehaviourMessageForClientsConfig_Brief = "GlobalBehaviourMessageForClientsConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviourMessageForClientsConfig_Detail'.
            /// </summary>
            public const string GlobalBehaviourMessageForClientsConfig_Detail = "GlobalBehaviourMessageForClientsConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviourPricesConfig_Brief'.
            /// </summary>
            public const string GlobalBehaviourPricesConfig_Brief = "GlobalBehaviourPricesConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviourPricesConfig_Detail'.
            /// </summary>
            public const string GlobalBehaviourPricesConfig_Detail = "GlobalBehaviourPricesConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviourSalesSlipPrintConfig_Brief'.
            /// </summary>
            public const string GlobalBehaviourSalesSlipPrintConfig_Brief = "GlobalBehaviourSalesSlipPrintConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviourSalesSlipPrintConfig_Detail'.
            /// </summary>
            public const string GlobalBehaviourSalesSlipPrintConfig_Detail = "GlobalBehaviourSalesSlipPrintConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviourTechSupportMessagingConfig_Brief'.
            /// </summary>
            public const string GlobalBehaviourTechSupportMessagingConfig_Brief = "GlobalBehaviourTechSupportMessagingConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalBehaviourTechSupportMessagingConfig_Detail'.
            /// </summary>
            public const string GlobalBehaviourTechSupportMessagingConfig_Detail = "GlobalBehaviourTechSupportMessagingConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalHwCardExternalSystemReaderConfig_Brief'.
            /// </summary>
            public const string GlobalHwCardExternalSystemReaderConfig_Brief = "GlobalHwCardExternalSystemReaderConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalHwCardExternalSystemReaderConfig_Detail'.
            /// </summary>
            public const string GlobalHwCardExternalSystemReaderConfig_Detail = "GlobalHwCardExternalSystemReaderConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalHwCardKbdReaderConfig_Brief'.
            /// </summary>
            public const string GlobalHwCardKbdReaderConfig_Brief = "GlobalHwCardKbdReaderConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalHwCardKbdReaderConfig_Detail'.
            /// </summary>
            public const string GlobalHwCardKbdReaderConfig_Detail = "GlobalHwCardKbdReaderConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalHwCardSerialReaderConfig_Brief'.
            /// </summary>
            public const string GlobalHwCardSerialReaderConfig_Brief = "GlobalHwCardSerialReaderConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalHwCardSerialReaderConfig_Detail'.
            /// </summary>
            public const string GlobalHwCardSerialReaderConfig_Detail = "GlobalHwCardSerialReaderConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalHwClientDisplayConfig_Brief'.
            /// </summary>
            public const string GlobalHwClientDisplayConfig_Brief = "GlobalHwClientDisplayConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalHwClientDisplayConfig_Detail'.
            /// </summary>
            public const string GlobalHwClientDisplayConfig_Detail = "GlobalHwClientDisplayConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalHwEanKbdReaderConfig_Brief'.
            /// </summary>
            public const string GlobalHwEanKbdReaderConfig_Brief = "GlobalHwEanKbdReaderConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalHwEanKbdReaderConfig_Detail'.
            /// </summary>
            public const string GlobalHwEanKbdReaderConfig_Detail = "GlobalHwEanKbdReaderConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalHwEanSerialReaderConfig_Brief'.
            /// </summary>
            public const string GlobalHwEanSerialReaderConfig_Brief = "GlobalHwEanSerialReaderConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalHwEanSerialReaderConfig_Detail'.
            /// </summary>
            public const string GlobalHwEanSerialReaderConfig_Detail = "GlobalHwEanSerialReaderConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalHwEWalletConfig_Brief'.
            /// </summary>
            public const string GlobalHwEWalletConfig_Brief = "GlobalHwEWalletConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalHwEWalletConfig_Detail'.
            /// </summary>
            public const string GlobalHwEWalletConfig_Detail = "GlobalHwEWalletConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalHwKitchenPrinterConfig_Brief'.
            /// </summary>
            public const string GlobalHwKitchenPrinterConfig_Brief = "GlobalHwKitchenPrinterConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalHwKitchenPrinterConfig_Detail'.
            /// </summary>
            public const string GlobalHwKitchenPrinterConfig_Detail = "GlobalHwKitchenPrinterConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalHwLabelPrinterConfig_Brief'.
            /// </summary>
            public const string GlobalHwLabelPrinterConfig_Brief = "GlobalHwLabelPrinterConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalHwLabelPrinterConfig_Detail'.
            /// </summary>
            public const string GlobalHwLabelPrinterConfig_Detail = "GlobalHwLabelPrinterConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalHwNawiScaleConfig_Brief'.
            /// </summary>
            public const string GlobalHwNawiScaleConfig_Brief = "GlobalHwNawiScaleConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalHwNawiScaleConfig_Detail'.
            /// </summary>
            public const string GlobalHwNawiScaleConfig_Detail = "GlobalHwNawiScaleConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalHwPaymentTermConfig_Brief'.
            /// </summary>
            public const string GlobalHwPaymentTermConfig_Brief = "GlobalHwPaymentTermConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalHwPaymentTermConfig_Detail'.
            /// </summary>
            public const string GlobalHwPaymentTermConfig_Detail = "GlobalHwPaymentTermConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalHwPosPrinterConfig_Brief'.
            /// </summary>
            public const string GlobalHwPosPrinterConfig_Brief = "GlobalHwPosPrinterConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalHwPosPrinterConfig_Detail'.
            /// </summary>
            public const string GlobalHwPosPrinterConfig_Detail = "GlobalHwPosPrinterConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalHwScaleConfig_Brief'.
            /// </summary>
            public const string GlobalHwScaleConfig_Brief = "GlobalHwScaleConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalHwScaleConfig_Detail'.
            /// </summary>
            public const string GlobalHwScaleConfig_Detail = "GlobalHwScaleConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalHwVgaClientDisplayConfig_Brief'.
            /// </summary>
            public const string GlobalHwVgaClientDisplayConfig_Brief = "GlobalHwVgaClientDisplayConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalHwVgaClientDisplayConfig_Detail'.
            /// </summary>
            public const string GlobalHwVgaClientDisplayConfig_Detail = "GlobalHwVgaClientDisplayConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalRulesAccountingMidnightConfig_Brief'.
            /// </summary>
            public const string GlobalRulesAccountingMidnightConfig_Brief = "GlobalRulesAccountingMidnightConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalRulesAccountingMidnightConfig_Detail'.
            /// </summary>
            public const string GlobalRulesAccountingMidnightConfig_Detail = "GlobalRulesAccountingMidnightConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalRulesBalanceConfig_Brief'.
            /// </summary>
            public const string GlobalRulesBalanceConfig_Brief = "GlobalRulesBalanceConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalRulesBalanceConfig_Detail'.
            /// </summary>
            public const string GlobalRulesBalanceConfig_Detail = "GlobalRulesBalanceConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalRulesCanteenConfig_Brief'.
            /// </summary>
            public const string GlobalRulesCanteenConfig_Brief = "GlobalRulesCanteenConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalRulesCanteenConfig_Detail'.
            /// </summary>
            public const string GlobalRulesCanteenConfig_Detail = "GlobalRulesCanteenConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalRulesCardManagementConfig_Brief'.
            /// </summary>
            public const string GlobalRulesCardManagementConfig_Brief = "GlobalRulesCardManagementConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalRulesCardManagementConfig_Detail'.
            /// </summary>
            public const string GlobalRulesCardManagementConfig_Detail = "GlobalRulesCardManagementConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalRulesCashDeskConfig_Brief'.
            /// </summary>
            public const string GlobalRulesCashDeskConfig_Brief = "GlobalRulesCashDeskConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalRulesCashDeskConfig_Detail'.
            /// </summary>
            public const string GlobalRulesCashDeskConfig_Detail = "GlobalRulesCashDeskConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalRulesCashOperationsConfig_Brief'.
            /// </summary>
            public const string GlobalRulesCashOperationsConfig_Brief = "GlobalRulesCashOperationsConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalRulesCashOperationsConfig_Detail'.
            /// </summary>
            public const string GlobalRulesCashOperationsConfig_Detail = "GlobalRulesCashOperationsConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalRulesClientManagementConfig_Brief'.
            /// </summary>
            public const string GlobalRulesClientManagementConfig_Brief = "GlobalRulesClientManagementConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalRulesClientManagementConfig_Detail'.
            /// </summary>
            public const string GlobalRulesClientManagementConfig_Detail = "GlobalRulesClientManagementConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalRulesClientUserNameConfig_Brief'.
            /// </summary>
            public const string GlobalRulesClientUserNameConfig_Brief = "GlobalRulesClientUserNameConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalRulesClientUserNameConfig_Detail'.
            /// </summary>
            public const string GlobalRulesClientUserNameConfig_Detail = "GlobalRulesClientUserNameConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalRulesCollectionConfig_Brief'.
            /// </summary>
            public const string GlobalRulesCollectionConfig_Brief = "GlobalRulesCollectionConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalRulesCollectionConfig_Detail'.
            /// </summary>
            public const string GlobalRulesCollectionConfig_Detail = "GlobalRulesCollectionConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalRulesExchangeRateConfig_Brief'.
            /// </summary>
            public const string GlobalRulesExchangeRateConfig_Brief = "GlobalRulesExchangeRateConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalRulesExchangeRateConfig_Detail'.
            /// </summary>
            public const string GlobalRulesExchangeRateConfig_Detail = "GlobalRulesExchangeRateConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalRulesGdprClientInfoProtectionConfig_Brief'.
            /// </summary>
            public const string GlobalRulesGdprClientInfoProtectionConfig_Brief = "GlobalRulesGdprClientInfoProtectionConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalRulesGdprClientInfoProtectionConfig_Detail'.
            /// </summary>
            public const string GlobalRulesGdprClientInfoProtectionConfig_Detail = "GlobalRulesGdprClientInfoProtectionConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalRulesGdprPersonalDataAgreementConfig_Brief'.
            /// </summary>
            public const string GlobalRulesGdprPersonalDataAgreementConfig_Brief = "GlobalRulesGdprPersonalDataAgreementConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalRulesGdprPersonalDataAgreementConfig_Detail'.
            /// </summary>
            public const string GlobalRulesGdprPersonalDataAgreementConfig_Detail = "GlobalRulesGdprPersonalDataAgreementConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalRulesMenuConfig_Brief'.
            /// </summary>
            public const string GlobalRulesMenuConfig_Brief = "GlobalRulesMenuConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalRulesMenuConfig_Detail'.
            /// </summary>
            public const string GlobalRulesMenuConfig_Detail = "GlobalRulesMenuConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalRulesNutritionalValuesConfig_Brief'.
            /// </summary>
            public const string GlobalRulesNutritionalValuesConfig_Brief = "GlobalRulesNutritionalValuesConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalRulesNutritionalValuesConfig_Detail'.
            /// </summary>
            public const string GlobalRulesNutritionalValuesConfig_Detail = "GlobalRulesNutritionalValuesConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalRulesOrderingConfig_Brief'.
            /// </summary>
            public const string GlobalRulesOrderingConfig_Brief = "GlobalRulesOrderingConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalRulesOrderingConfig_Detail'.
            /// </summary>
            public const string GlobalRulesOrderingConfig_Detail = "GlobalRulesOrderingConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalRulesPasswordConfig_Brief'.
            /// </summary>
            public const string GlobalRulesPasswordConfig_Brief = "GlobalRulesPasswordConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalRulesPasswordConfig_Detail'.
            /// </summary>
            public const string GlobalRulesPasswordConfig_Detail = "GlobalRulesPasswordConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalRulesRoundingConfig_Brief'.
            /// </summary>
            public const string GlobalRulesRoundingConfig_Brief = "GlobalRulesRoundingConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalRulesRoundingConfig_Detail'.
            /// </summary>
            public const string GlobalRulesRoundingConfig_Detail = "GlobalRulesRoundingConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalRulesSpendingAndSubsidyConfig_Brief'.
            /// </summary>
            public const string GlobalRulesSpendingAndSubsidyConfig_Brief = "GlobalRulesSpendingAndSubsidyConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalRulesSpendingAndSubsidyConfig_Detail'.
            /// </summary>
            public const string GlobalRulesSpendingAndSubsidyConfig_Detail = "GlobalRulesSpendingAndSubsidyConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalRulesVatRateConfig_Brief'.
            /// </summary>
            public const string GlobalRulesVatRateConfig_Brief = "GlobalRulesVatRateConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalRulesVatRateConfig_Detail'.
            /// </summary>
            public const string GlobalRulesVatRateConfig_Detail = "GlobalRulesVatRateConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalServicesAppServerConnectionConfig_Brief'.
            /// </summary>
            public const string GlobalServicesAppServerConnectionConfig_Brief = "GlobalServicesAppServerConnectionConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalServicesAppServerConnectionConfig_Detail'.
            /// </summary>
            public const string GlobalServicesAppServerConnectionConfig_Detail = "GlobalServicesAppServerConnectionConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalServicesDbConnectionConfig_Brief'.
            /// </summary>
            public const string GlobalServicesDbConnectionConfig_Brief = "GlobalServicesDbConnectionConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalServicesDbConnectionConfig_Detail'.
            /// </summary>
            public const string GlobalServicesDbConnectionConfig_Detail = "GlobalServicesDbConnectionConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalServicesDbMaintenanceConfig_Brief'.
            /// </summary>
            public const string GlobalServicesDbMaintenanceConfig_Brief = "GlobalServicesDbMaintenanceConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalServicesDbMaintenanceConfig_Detail'.
            /// </summary>
            public const string GlobalServicesDbMaintenanceConfig_Detail = "GlobalServicesDbMaintenanceConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalServicesFbsCredentialsConfig_Brief'.
            /// </summary>
            public const string GlobalServicesFbsCredentialsConfig_Brief = "GlobalServicesFbsCredentialsConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalServicesFbsCredentialsConfig_Detail'.
            /// </summary>
            public const string GlobalServicesFbsCredentialsConfig_Detail = "GlobalServicesFbsCredentialsConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalServicesFbsServicesConfig_Brief'.
            /// </summary>
            public const string GlobalServicesFbsServicesConfig_Brief = "GlobalServicesFbsServicesConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalServicesFbsServicesConfig_Detail'.
            /// </summary>
            public const string GlobalServicesFbsServicesConfig_Detail = "GlobalServicesFbsServicesConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalServicesHelpDeskCredentialsConfig_Brief'.
            /// </summary>
            public const string GlobalServicesHelpDeskCredentialsConfig_Brief = "GlobalServicesHelpDeskCredentialsConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalServicesHelpDeskCredentialsConfig_Detail'.
            /// </summary>
            public const string GlobalServicesHelpDeskCredentialsConfig_Detail = "GlobalServicesHelpDeskCredentialsConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalServicesHelpDeskServicesConfig_Brief'.
            /// </summary>
            public const string GlobalServicesHelpDeskServicesConfig_Brief = "GlobalServicesHelpDeskServicesConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalServicesHelpDeskServicesConfig_Detail'.
            /// </summary>
            public const string GlobalServicesHelpDeskServicesConfig_Detail = "GlobalServicesHelpDeskServicesConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalServicesICouponConfig_Brief'.
            /// </summary>
            public const string GlobalServicesICouponConfig_Brief = "GlobalServicesICouponConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalServicesICouponConfig_Detail'.
            /// </summary>
            public const string GlobalServicesICouponConfig_Detail = "GlobalServicesICouponConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalServicesIpsConfig_Brief'.
            /// </summary>
            public const string GlobalServicesIpsConfig_Brief = "GlobalServicesIpsConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalServicesIpsConfig_Detail'.
            /// </summary>
            public const string GlobalServicesIpsConfig_Detail = "GlobalServicesIpsConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalServicesLoggingConfig_Brief'.
            /// </summary>
            public const string GlobalServicesLoggingConfig_Brief = "GlobalServicesLoggingConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalServicesLoggingConfig_Detail'.
            /// </summary>
            public const string GlobalServicesLoggingConfig_Detail = "GlobalServicesLoggingConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalServicesPaymentGateConfig_Brief'.
            /// </summary>
            public const string GlobalServicesPaymentGateConfig_Brief = "GlobalServicesPaymentGateConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalServicesPaymentGateConfig_Detail'.
            /// </summary>
            public const string GlobalServicesPaymentGateConfig_Detail = "GlobalServicesPaymentGateConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalServicesReportServiceConnectionConfig_Brief'.
            /// </summary>
            public const string GlobalServicesReportServiceConnectionConfig_Brief = "GlobalServicesReportServiceConnectionConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalServicesReportServiceConnectionConfig_Detail'.
            /// </summary>
            public const string GlobalServicesReportServiceConnectionConfig_Detail = "GlobalServicesReportServiceConnectionConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalServicesSmtpConfig_Brief'.
            /// </summary>
            public const string GlobalServicesSmtpConfig_Brief = "GlobalServicesSmtpConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalServicesSmtpConfig_Detail'.
            /// </summary>
            public const string GlobalServicesSmtpConfig_Detail = "GlobalServicesSmtpConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalServicesWebProxyConfig_Brief'.
            /// </summary>
            public const string GlobalServicesWebProxyConfig_Brief = "GlobalServicesWebProxyConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalServicesWebProxyConfig_Detail'.
            /// </summary>
            public const string GlobalServicesWebProxyConfig_Detail = "GlobalServicesWebProxyConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalServiceSysLogConfig_Brief'.
            /// </summary>
            public const string GlobalServiceSysLogConfig_Brief = "GlobalServiceSysLogConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalServiceSysLogConfig_Detail'.
            /// </summary>
            public const string GlobalServiceSysLogConfig_Detail = "GlobalServiceSysLogConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalSupportContactsAneteConfig_Brief'.
            /// </summary>
            public const string GlobalSupportContactsAneteConfig_Brief = "GlobalSupportContactsAneteConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalSupportContactsAneteConfig_Detail'.
            /// </summary>
            public const string GlobalSupportContactsAneteConfig_Detail = "GlobalSupportContactsAneteConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalSupportContactsLocalConfig_Brief'.
            /// </summary>
            public const string GlobalSupportContactsLocalConfig_Brief = "GlobalSupportContactsLocalConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalSupportContactsLocalConfig_Detail'.
            /// </summary>
            public const string GlobalSupportContactsLocalConfig_Detail = "GlobalSupportContactsLocalConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalSupportRemoteAccessConfig_Brief'.
            /// </summary>
            public const string GlobalSupportRemoteAccessConfig_Brief = "GlobalSupportRemoteAccessConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalSupportRemoteAccessConfig_Detail'.
            /// </summary>
            public const string GlobalSupportRemoteAccessConfig_Detail = "GlobalSupportRemoteAccessConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalSyncAccessRightsConfig_Brief'.
            /// </summary>
            public const string GlobalSyncAccessRightsConfig_Brief = "GlobalSyncAccessRightsConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalSyncAccessRightsConfig_Detail'.
            /// </summary>
            public const string GlobalSyncAccessRightsConfig_Detail = "GlobalSyncAccessRightsConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalSyncClientsConfig_Brief'.
            /// </summary>
            public const string GlobalSyncClientsConfig_Brief = "GlobalSyncClientsConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalSyncClientsConfig_Detail'.
            /// </summary>
            public const string GlobalSyncClientsConfig_Detail = "GlobalSyncClientsConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalSyncUsersConfig_Brief'.
            /// </summary>
            public const string GlobalSyncUsersConfig_Brief = "GlobalSyncUsersConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalSyncUsersConfig_Detail'.
            /// </summary>
            public const string GlobalSyncUsersConfig_Detail = "GlobalSyncUsersConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GlobalSystemOwnerConfig_Brief'.
            /// </summary>
            public const string GlobalSystemOwnerConfig_Brief = "GlobalSystemOwnerConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GlobalSystemOwnerConfig_Detail'.
            /// </summary>
            public const string GlobalSystemOwnerConfig_Detail = "GlobalSystemOwnerConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GraphicAccommodationCapacityAppearanceColorsConfig_Brief'.
            /// </summary>
            public const string GraphicAccommodationCapacityAppearanceColorsConfig_Brief = "GraphicAccommodationCapacityAppearanceColorsConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GraphicAccommodationCapacityAppearanceColorsConfig_Detail'.
            /// </summary>
            public const string GraphicAccommodationCapacityAppearanceColorsConfig_Detail = "GraphicAccommodationCapacityAppearanceColorsConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'GuestOrderingBehaviourParagonConfig_Brief'.
            /// </summary>
            public const string GuestOrderingBehaviourParagonConfig_Brief = "GuestOrderingBehaviourParagonConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'GuestOrderingBehaviourParagonConfig_Detail'.
            /// </summary>
            public const string GuestOrderingBehaviourParagonConfig_Detail = "GuestOrderingBehaviourParagonConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'HotelGeneralConfig_Brief'.
            /// </summary>
            public const string HotelGeneralConfig_Brief = "HotelGeneralConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'HotelGeneralConfig_Detail'.
            /// </summary>
            public const string HotelGeneralConfig_Detail = "HotelGeneralConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'InfoProClientConfig_Brief'.
            /// </summary>
            public const string InfoProClientConfig_Brief = "InfoProClientConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'InfoProClientConfig_Detail'.
            /// </summary>
            public const string InfoProClientConfig_Detail = "InfoProClientConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'InfoProServerConfig_Brief'.
            /// </summary>
            public const string InfoProServerConfig_Brief = "InfoProServerConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'InfoProServerConfig_Detail'.
            /// </summary>
            public const string InfoProServerConfig_Detail = "InfoProServerConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'LegendsAccommodationCapacityAppearanceColorsConfig_Brief'.
            /// </summary>
            public const string LegendsAccommodationCapacityAppearanceColorsConfig_Brief = "LegendsAccommodationCapacityAppearanceColorsConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'LegendsAccommodationCapacityAppearanceColorsConfig_Detail'.
            /// </summary>
            public const string LegendsAccommodationCapacityAppearanceColorsConfig_Detail = "LegendsAccommodationCapacityAppearanceColorsConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'MastersOrderingGeneralConfig_Brief'.
            /// </summary>
            public const string MastersOrderingGeneralConfig_Brief = "MastersOrderingGeneralConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'MastersOrderingGeneralConfig_Detail'.
            /// </summary>
            public const string MastersOrderingGeneralConfig_Detail = "MastersOrderingGeneralConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'MenuPresenterAppearanceBackgroundConfig_Brief'.
            /// </summary>
            public const string MenuPresenterAppearanceBackgroundConfig_Brief = "MenuPresenterAppearanceBackgroundConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'MenuPresenterAppearanceBackgroundConfig_Detail'.
            /// </summary>
            public const string MenuPresenterAppearanceBackgroundConfig_Detail = "MenuPresenterAppearanceBackgroundConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'MenuPresenterAppearanceFirstLevelHeaderConfig_Brief'.
            /// </summary>
            public const string MenuPresenterAppearanceFirstLevelHeaderConfig_Brief = "MenuPresenterAppearanceFirstLevelHeaderConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'MenuPresenterAppearanceFirstLevelHeaderConfig_Detail'.
            /// </summary>
            public const string MenuPresenterAppearanceFirstLevelHeaderConfig_Detail = "MenuPresenterAppearanceFirstLevelHeaderConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'MenuPresenterAppearanceLogoConfig_Brief'.
            /// </summary>
            public const string MenuPresenterAppearanceLogoConfig_Brief = "MenuPresenterAppearanceLogoConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'MenuPresenterAppearanceLogoConfig_Detail'.
            /// </summary>
            public const string MenuPresenterAppearanceLogoConfig_Detail = "MenuPresenterAppearanceLogoConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'MenuPresenterAppearanceMenuConfig_Brief'.
            /// </summary>
            public const string MenuPresenterAppearanceMenuConfig_Brief = "MenuPresenterAppearanceMenuConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'MenuPresenterAppearanceMenuConfig_Detail'.
            /// </summary>
            public const string MenuPresenterAppearanceMenuConfig_Detail = "MenuPresenterAppearanceMenuConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'MenuPresenterAppearanceMenuHeaderConfig_Brief'.
            /// </summary>
            public const string MenuPresenterAppearanceMenuHeaderConfig_Brief = "MenuPresenterAppearanceMenuHeaderConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'MenuPresenterAppearanceMenuHeaderConfig_Detail'.
            /// </summary>
            public const string MenuPresenterAppearanceMenuHeaderConfig_Detail = "MenuPresenterAppearanceMenuHeaderConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'MenuPresenterAppearanceSecondLevelHeaderConfig_Brief'.
            /// </summary>
            public const string MenuPresenterAppearanceSecondLevelHeaderConfig_Brief = "MenuPresenterAppearanceSecondLevelHeaderConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'MenuPresenterAppearanceSecondLevelHeaderConfig_Detail'.
            /// </summary>
            public const string MenuPresenterAppearanceSecondLevelHeaderConfig_Detail = "MenuPresenterAppearanceSecondLevelHeaderConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'MenuPresenterAppearanceTextConfig_Brief'.
            /// </summary>
            public const string MenuPresenterAppearanceTextConfig_Brief = "MenuPresenterAppearanceTextConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'MenuPresenterAppearanceTextConfig_Detail'.
            /// </summary>
            public const string MenuPresenterAppearanceTextConfig_Detail = "MenuPresenterAppearanceTextConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'MenuPresenterBehaviourColumnsConfig_Brief'.
            /// </summary>
            public const string MenuPresenterBehaviourColumnsConfig_Brief = "MenuPresenterBehaviourColumnsConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'MenuPresenterBehaviourColumnsConfig_Detail'.
            /// </summary>
            public const string MenuPresenterBehaviourColumnsConfig_Detail = "MenuPresenterBehaviourColumnsConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'MenuPresenterBehaviourMealCountsConfig_Brief'.
            /// </summary>
            public const string MenuPresenterBehaviourMealCountsConfig_Brief = "MenuPresenterBehaviourMealCountsConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'MenuPresenterBehaviourMealCountsConfig_Detail'.
            /// </summary>
            public const string MenuPresenterBehaviourMealCountsConfig_Detail = "MenuPresenterBehaviourMealCountsConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'MenuPresenterBehaviourMenuConfig_Brief'.
            /// </summary>
            public const string MenuPresenterBehaviourMenuConfig_Brief = "MenuPresenterBehaviourMenuConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'MenuPresenterBehaviourMenuConfig_Detail'.
            /// </summary>
            public const string MenuPresenterBehaviourMenuConfig_Detail = "MenuPresenterBehaviourMenuConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'MenuPresenterBehaviourMenuFilterConfig_Brief'.
            /// </summary>
            public const string MenuPresenterBehaviourMenuFilterConfig_Brief = "MenuPresenterBehaviourMenuFilterConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'MenuPresenterBehaviourMenuFilterConfig_Detail'.
            /// </summary>
            public const string MenuPresenterBehaviourMenuFilterConfig_Detail = "MenuPresenterBehaviourMenuFilterConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'MenuPresenterBehaviourNoteConfig_Brief'.
            /// </summary>
            public const string MenuPresenterBehaviourNoteConfig_Brief = "MenuPresenterBehaviourNoteConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'MenuPresenterBehaviourNoteConfig_Detail'.
            /// </summary>
            public const string MenuPresenterBehaviourNoteConfig_Detail = "MenuPresenterBehaviourNoteConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'MenuPresenterBehaviourRollConfig_Brief'.
            /// </summary>
            public const string MenuPresenterBehaviourRollConfig_Brief = "MenuPresenterBehaviourRollConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'MenuPresenterBehaviourRollConfig_Detail'.
            /// </summary>
            public const string MenuPresenterBehaviourRollConfig_Detail = "MenuPresenterBehaviourRollConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'MenuPresenterBehaviourUserInterfaceConfig_Brief'.
            /// </summary>
            public const string MenuPresenterBehaviourUserInterfaceConfig_Brief = "MenuPresenterBehaviourUserInterfaceConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'MenuPresenterBehaviourUserInterfaceConfig_Detail'.
            /// </summary>
            public const string MenuPresenterBehaviourUserInterfaceConfig_Detail = "MenuPresenterBehaviourUserInterfaceConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'MenuPresenterHwIpCamConfig_Brief'.
            /// </summary>
            public const string MenuPresenterHwIpCamConfig_Brief = "MenuPresenterHwIpCamConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'MenuPresenterHwIpCamConfig_Detail'.
            /// </summary>
            public const string MenuPresenterHwIpCamConfig_Detail = "MenuPresenterHwIpCamConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'MobileOrderingAuthenticationConfig_Brief'.
            /// </summary>
            public const string MobileOrderingAuthenticationConfig_Brief = "MobileOrderingAuthenticationConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'MobileOrderingAuthenticationConfig_Detail'.
            /// </summary>
            public const string MobileOrderingAuthenticationConfig_Detail = "MobileOrderingAuthenticationConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'MobileOrderingBehaviorAccountGdprConfig_Brief'.
            /// </summary>
            public const string MobileOrderingBehaviorAccountGdprConfig_Brief = "MobileOrderingBehaviorAccountGdprConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'MobileOrderingBehaviorAccountGdprConfig_Detail'.
            /// </summary>
            public const string MobileOrderingBehaviorAccountGdprConfig_Detail = "MobileOrderingBehaviorAccountGdprConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'MobileOrderingBehaviorMenuConfig_Brief'.
            /// </summary>
            public const string MobileOrderingBehaviorMenuConfig_Brief = "MobileOrderingBehaviorMenuConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'MobileOrderingBehaviorMenuConfig_Detail'.
            /// </summary>
            public const string MobileOrderingBehaviorMenuConfig_Detail = "MobileOrderingBehaviorMenuConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'MobileOrderingBehaviourPriceSetupConfig_Brief'.
            /// </summary>
            public const string MobileOrderingBehaviourPriceSetupConfig_Brief = "MobileOrderingBehaviourPriceSetupConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'MobileOrderingBehaviourPriceSetupConfig_Detail'.
            /// </summary>
            public const string MobileOrderingBehaviourPriceSetupConfig_Detail = "MobileOrderingBehaviourPriceSetupConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'MobileOrderingBehaviourWebKreditConfig_Brief'.
            /// </summary>
            public const string MobileOrderingBehaviourWebKreditConfig_Brief = "MobileOrderingBehaviourWebKreditConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'MobileOrderingBehaviourWebKreditConfig_Detail'.
            /// </summary>
            public const string MobileOrderingBehaviourWebKreditConfig_Detail = "MobileOrderingBehaviourWebKreditConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'MobileStockBehaviorStocksConfig_Brief'.
            /// </summary>
            public const string MobileStockBehaviorStocksConfig_Brief = "MobileStockBehaviorStocksConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'MobileStockBehaviorStocksConfig_Detail'.
            /// </summary>
            public const string MobileStockBehaviorStocksConfig_Detail = "MobileStockBehaviorStocksConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'MobileStockBehaviourConfig_Brief'.
            /// </summary>
            public const string MobileStockBehaviourConfig_Brief = "MobileStockBehaviourConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'MobileStockBehaviourConfig_Detail'.
            /// </summary>
            public const string MobileStockBehaviourConfig_Detail = "MobileStockBehaviourConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'OfficeBehaviorEetConfig_Brief'.
            /// </summary>
            public const string OfficeBehaviorEetConfig_Brief = "OfficeBehaviorEetConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'OfficeBehaviorEetConfig_Detail'.
            /// </summary>
            public const string OfficeBehaviorEetConfig_Detail = "OfficeBehaviorEetConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'OfficeBehaviourAppSingleInstanceConfig_Brief'.
            /// </summary>
            public const string OfficeBehaviourAppSingleInstanceConfig_Brief = "OfficeBehaviourAppSingleInstanceConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'OfficeBehaviourAppSingleInstanceConfig_Detail'.
            /// </summary>
            public const string OfficeBehaviourAppSingleInstanceConfig_Detail = "OfficeBehaviourAppSingleInstanceConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'OfficeBehaviourBalanceConfig_Brief'.
            /// </summary>
            public const string OfficeBehaviourBalanceConfig_Brief = "OfficeBehaviourBalanceConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'OfficeBehaviourBalanceConfig_Detail'.
            /// </summary>
            public const string OfficeBehaviourBalanceConfig_Detail = "OfficeBehaviourBalanceConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'OfficeBehaviourBatchOrdering_Brief'.
            /// </summary>
            public const string OfficeBehaviourBatchOrdering_Brief = "OfficeBehaviourBatchOrdering_Brief";
            
            /// <summary>
            /// Stores the resource name 'OfficeBehaviourBatchOrdering_Detail'.
            /// </summary>
            public const string OfficeBehaviourBatchOrdering_Detail = "OfficeBehaviourBatchOrdering_Detail";
            
            /// <summary>
            /// Stores the resource name 'OfficeBehaviourCalcPriceList_Brief'.
            /// </summary>
            public const string OfficeBehaviourCalcPriceList_Brief = "OfficeBehaviourCalcPriceList_Brief";
            
            /// <summary>
            /// Stores the resource name 'OfficeBehaviourCalcPriceList_Detail'.
            /// </summary>
            public const string OfficeBehaviourCalcPriceList_Detail = "OfficeBehaviourCalcPriceList_Detail";
            
            /// <summary>
            /// Stores the resource name 'OfficeBehaviourCardManagement_Brief'.
            /// </summary>
            public const string OfficeBehaviourCardManagement_Brief = "OfficeBehaviourCardManagement_Brief";
            
            /// <summary>
            /// Stores the resource name 'OfficeBehaviourCardManagement_Detail'.
            /// </summary>
            public const string OfficeBehaviourCardManagement_Detail = "OfficeBehaviourCardManagement_Detail";
            
            /// <summary>
            /// Stores the resource name 'OfficeBehaviourFilterSettingsConfig_Brief'.
            /// </summary>
            public const string OfficeBehaviourFilterSettingsConfig_Brief = "OfficeBehaviourFilterSettingsConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'OfficeBehaviourFilterSettingsConfig_Detail'.
            /// </summary>
            public const string OfficeBehaviourFilterSettingsConfig_Detail = "OfficeBehaviourFilterSettingsConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'OfficeBehaviourMealTicketsConfig_Brief'.
            /// </summary>
            public const string OfficeBehaviourMealTicketsConfig_Brief = "OfficeBehaviourMealTicketsConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'OfficeBehaviourMealTicketsConfig_Detail'.
            /// </summary>
            public const string OfficeBehaviourMealTicketsConfig_Detail = "OfficeBehaviourMealTicketsConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'OfficeBehaviourPricingConfig_Brief'.
            /// </summary>
            public const string OfficeBehaviourPricingConfig_Brief = "OfficeBehaviourPricingConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'OfficeBehaviourPricingConfig_Detail'.
            /// </summary>
            public const string OfficeBehaviourPricingConfig_Detail = "OfficeBehaviourPricingConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'OfficeBehaviourSalesSlipPrintConfig_Brief'.
            /// </summary>
            public const string OfficeBehaviourSalesSlipPrintConfig_Brief = "OfficeBehaviourSalesSlipPrintConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'OfficeBehaviourSalesSlipPrintConfig_Detail'.
            /// </summary>
            public const string OfficeBehaviourSalesSlipPrintConfig_Detail = "OfficeBehaviourSalesSlipPrintConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'OfficeCentralManagementBehaviourRdpConfig_Brief'.
            /// </summary>
            public const string OfficeCentralManagementBehaviourRdpConfig_Brief = "OfficeCentralManagementBehaviourRdpConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'OfficeCentralManagementBehaviourRdpConfig_Detail'.
            /// </summary>
            public const string OfficeCentralManagementBehaviourRdpConfig_Detail = "OfficeCentralManagementBehaviourRdpConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'OfficeClientsBehaviourBankAccountNumberConfig_Brief'.
            /// </summary>
            public const string OfficeClientsBehaviourBankAccountNumberConfig_Brief = "OfficeClientsBehaviourBankAccountNumberConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'OfficeClientsBehaviourBankAccountNumberConfig_Detail'.
            /// </summary>
            public const string OfficeClientsBehaviourBankAccountNumberConfig_Detail = "OfficeClientsBehaviourBankAccountNumberConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'OfficeClientsBehaviourCardIssueConfig_Brief'.
            /// </summary>
            public const string OfficeClientsBehaviourCardIssueConfig_Brief = "OfficeClientsBehaviourCardIssueConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'OfficeClientsBehaviourCardIssueConfig_Detail'.
            /// </summary>
            public const string OfficeClientsBehaviourCardIssueConfig_Detail = "OfficeClientsBehaviourCardIssueConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'OfficeClientsBehaviourCardStockConfig_Brief'.
            /// </summary>
            public const string OfficeClientsBehaviourCardStockConfig_Brief = "OfficeClientsBehaviourCardStockConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'OfficeClientsBehaviourCardStockConfig_Detail'.
            /// </summary>
            public const string OfficeClientsBehaviourCardStockConfig_Detail = "OfficeClientsBehaviourCardStockConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'OfficeClientsBehaviourEBankingImportConfig_Brief'.
            /// </summary>
            public const string OfficeClientsBehaviourEBankingImportConfig_Brief = "OfficeClientsBehaviourEBankingImportConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'OfficeClientsBehaviourEBankingImportConfig_Detail'.
            /// </summary>
            public const string OfficeClientsBehaviourEBankingImportConfig_Detail = "OfficeClientsBehaviourEBankingImportConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'OfficeClientsBehaviourInternetAccountReportConfig_Brief'.
            /// </summary>
            public const string OfficeClientsBehaviourInternetAccountReportConfig_Brief = "OfficeClientsBehaviourInternetAccountReportConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'OfficeClientsBehaviourInternetAccountReportConfig_Detail'.
            /// </summary>
            public const string OfficeClientsBehaviourInternetAccountReportConfig_Detail = "OfficeClientsBehaviourInternetAccountReportConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'OfficeEBankingConfig_Brief'.
            /// </summary>
            public const string OfficeEBankingConfig_Brief = "OfficeEBankingConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'OfficeEBankingConfig_Detail'.
            /// </summary>
            public const string OfficeEBankingConfig_Detail = "OfficeEBankingConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'OfficeHwPrinterConfig_Brief'.
            /// </summary>
            public const string OfficeHwPrinterConfig_Brief = "OfficeHwPrinterConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'OfficeHwPrinterConfig_Detail'.
            /// </summary>
            public const string OfficeHwPrinterConfig_Detail = "OfficeHwPrinterConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'OfficeManagementBehaviourLicenseConfig_Brief'.
            /// </summary>
            public const string OfficeManagementBehaviourLicenseConfig_Brief = "OfficeManagementBehaviourLicenseConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'OfficeManagementBehaviourLicenseConfig_Detail'.
            /// </summary>
            public const string OfficeManagementBehaviourLicenseConfig_Detail = "OfficeManagementBehaviourLicenseConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'OfficeMealDistributionBehaviourInquiriesConfig_Brief'.
            /// </summary>
            public const string OfficeMealDistributionBehaviourInquiriesConfig_Brief = "OfficeMealDistributionBehaviourInquiriesConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'OfficeMealDistributionBehaviourInquiriesConfig_Detail'.
            /// </summary>
            public const string OfficeMealDistributionBehaviourInquiriesConfig_Detail = "OfficeMealDistributionBehaviourInquiriesConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'OfficeMealDistributionBehaviourInquirySummaryConfig_Brief'.
            /// </summary>
            public const string OfficeMealDistributionBehaviourInquirySummaryConfig_Brief = "OfficeMealDistributionBehaviourInquirySummaryConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'OfficeMealDistributionBehaviourInquirySummaryConfig_Detail'.
            /// </summary>
            public const string OfficeMealDistributionBehaviourInquirySummaryConfig_Detail = "OfficeMealDistributionBehaviourInquirySummaryConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'OfficeMealDistributionBehaviourIssueSlipConfig_Brief'.
            /// </summary>
            public const string OfficeMealDistributionBehaviourIssueSlipConfig_Brief = "OfficeMealDistributionBehaviourIssueSlipConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'OfficeMealDistributionBehaviourIssueSlipConfig_Detail'.
            /// </summary>
            public const string OfficeMealDistributionBehaviourIssueSlipConfig_Detail = "OfficeMealDistributionBehaviourIssueSlipConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'OfficeMealDistributionReportsDeliveryAlmedConfig_Brief'.
            /// </summary>
            public const string OfficeMealDistributionReportsDeliveryAlmedConfig_Brief = "OfficeMealDistributionReportsDeliveryAlmedConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'OfficeMealDistributionReportsDeliveryAlmedConfig_Detail'.
            /// </summary>
            public const string OfficeMealDistributionReportsDeliveryAlmedConfig_Detail = "OfficeMealDistributionReportsDeliveryAlmedConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'OfficePriceMakingBehaviourSaleSummaryConfig_Brief'.
            /// </summary>
            public const string OfficePriceMakingBehaviourSaleSummaryConfig_Brief = "OfficePriceMakingBehaviourSaleSummaryConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'OfficePriceMakingBehaviourSaleSummaryConfig_Detail'.
            /// </summary>
            public const string OfficePriceMakingBehaviourSaleSummaryConfig_Detail = "OfficePriceMakingBehaviourSaleSummaryConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'OfficePriceMakingBehaviourSubsidyCategoryConfig_Brief'.
            /// </summary>
            public const string OfficePriceMakingBehaviourSubsidyCategoryConfig_Brief = "OfficePriceMakingBehaviourSubsidyCategoryConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'OfficePriceMakingBehaviourSubsidyCategoryConfig_Detail'.
            /// </summary>
            public const string OfficePriceMakingBehaviourSubsidyCategoryConfig_Detail = "OfficePriceMakingBehaviourSubsidyCategoryConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'OfficeWorkplacesBehaviourImportNisConfig_Brief'.
            /// </summary>
            public const string OfficeWorkplacesBehaviourImportNisConfig_Brief = "OfficeWorkplacesBehaviourImportNisConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'OfficeWorkplacesBehaviourImportNisConfig_Detail'.
            /// </summary>
            public const string OfficeWorkplacesBehaviourImportNisConfig_Detail = "OfficeWorkplacesBehaviourImportNisConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'OfficeWorkplacesBehaviourMealTypeConfig_Brief'.
            /// </summary>
            public const string OfficeWorkplacesBehaviourMealTypeConfig_Brief = "OfficeWorkplacesBehaviourMealTypeConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'OfficeWorkplacesBehaviourMealTypeConfig_Detail'.
            /// </summary>
            public const string OfficeWorkplacesBehaviourMealTypeConfig_Detail = "OfficeWorkplacesBehaviourMealTypeConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'OfficeWorkplacesBehaviourMenuConfig_Brief'.
            /// </summary>
            public const string OfficeWorkplacesBehaviourMenuConfig_Brief = "OfficeWorkplacesBehaviourMenuConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'OfficeWorkplacesBehaviourMenuConfig_Detail'.
            /// </summary>
            public const string OfficeWorkplacesBehaviourMenuConfig_Detail = "OfficeWorkplacesBehaviourMenuConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'OfficeWorkplacesBehaviourOrderChangesConfig_Brief'.
            /// </summary>
            public const string OfficeWorkplacesBehaviourOrderChangesConfig_Brief = "OfficeWorkplacesBehaviourOrderChangesConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'OfficeWorkplacesBehaviourOrderChangesConfig_Detail'.
            /// </summary>
            public const string OfficeWorkplacesBehaviourOrderChangesConfig_Detail = "OfficeWorkplacesBehaviourOrderChangesConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'OfficeWorkplacesBehaviourPriceListConfig_Brief'.
            /// </summary>
            public const string OfficeWorkplacesBehaviourPriceListConfig_Brief = "OfficeWorkplacesBehaviourPriceListConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'OfficeWorkplacesBehaviourPriceListConfig_Detail'.
            /// </summary>
            public const string OfficeWorkplacesBehaviourPriceListConfig_Detail = "OfficeWorkplacesBehaviourPriceListConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'OfficeWorkplacesReportsImpersonationCahConfig_Brief'.
            /// </summary>
            public const string OfficeWorkplacesReportsImpersonationCahConfig_Brief = "OfficeWorkplacesReportsImpersonationCahConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'OfficeWorkplacesReportsImpersonationCahConfig_Detail'.
            /// </summary>
            public const string OfficeWorkplacesReportsImpersonationCahConfig_Detail = "OfficeWorkplacesReportsImpersonationCahConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'OfficeWorkplacesReportsTemplatesDayMenuConfig_Brief'.
            /// </summary>
            public const string OfficeWorkplacesReportsTemplatesDayMenuConfig_Brief = "OfficeWorkplacesReportsTemplatesDayMenuConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'OfficeWorkplacesReportsTemplatesDayMenuConfig_Detail'.
            /// </summary>
            public const string OfficeWorkplacesReportsTemplatesDayMenuConfig_Detail = "OfficeWorkplacesReportsTemplatesDayMenuConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'OfficeWorkplacesReportsTemplatesWeekMenuConfig_Brief'.
            /// </summary>
            public const string OfficeWorkplacesReportsTemplatesWeekMenuConfig_Brief = "OfficeWorkplacesReportsTemplatesWeekMenuConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'OfficeWorkplacesReportsTemplatesWeekMenuConfig_Detail'.
            /// </summary>
            public const string OfficeWorkplacesReportsTemplatesWeekMenuConfig_Detail = "OfficeWorkplacesReportsTemplatesWeekMenuConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'OfficeWorkplacesReportsWeekMenuConfig_Brief'.
            /// </summary>
            public const string OfficeWorkplacesReportsWeekMenuConfig_Brief = "OfficeWorkplacesReportsWeekMenuConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'OfficeWorkplacesReportsWeekMenuConfig_Detail'.
            /// </summary>
            public const string OfficeWorkplacesReportsWeekMenuConfig_Detail = "OfficeWorkplacesReportsWeekMenuConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'PatientOrderingBehaviourGeneralConfig_Brief'.
            /// </summary>
            public const string PatientOrderingBehaviourGeneralConfig_Brief = "PatientOrderingBehaviourGeneralConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'PatientOrderingBehaviourGeneralConfig_Detail'.
            /// </summary>
            public const string PatientOrderingBehaviourGeneralConfig_Detail = "PatientOrderingBehaviourGeneralConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'PresPointAppearanceColorsConfig_Brief'.
            /// </summary>
            public const string PresPointAppearanceColorsConfig_Brief = "PresPointAppearanceColorsConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'PresPointAppearanceColorsConfig_Detail'.
            /// </summary>
            public const string PresPointAppearanceColorsConfig_Detail = "PresPointAppearanceColorsConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'PresPointAppearanceFontConfig_Brief'.
            /// </summary>
            public const string PresPointAppearanceFontConfig_Brief = "PresPointAppearanceFontConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'PresPointAppearanceFontConfig_Detail'.
            /// </summary>
            public const string PresPointAppearanceFontConfig_Detail = "PresPointAppearanceFontConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'PresPointBahaviourAttendanceConfig_Brief'.
            /// </summary>
            public const string PresPointBahaviourAttendanceConfig_Brief = "PresPointBahaviourAttendanceConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'PresPointBahaviourAttendanceConfig_Detail'.
            /// </summary>
            public const string PresPointBahaviourAttendanceConfig_Detail = "PresPointBahaviourAttendanceConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'PresPointBehaviourCanteenConfig_Brief'.
            /// </summary>
            public const string PresPointBehaviourCanteenConfig_Brief = "PresPointBehaviourCanteenConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'PresPointBehaviourCanteenConfig_Detail'.
            /// </summary>
            public const string PresPointBehaviourCanteenConfig_Detail = "PresPointBehaviourCanteenConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'PresPointBehaviourClientInfoConfig_Brief'.
            /// </summary>
            public const string PresPointBehaviourClientInfoConfig_Brief = "PresPointBehaviourClientInfoConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'PresPointBehaviourClientInfoConfig_Detail'.
            /// </summary>
            public const string PresPointBehaviourClientInfoConfig_Detail = "PresPointBehaviourClientInfoConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'PresPointBehaviourCurtainConfig_Brief'.
            /// </summary>
            public const string PresPointBehaviourCurtainConfig_Brief = "PresPointBehaviourCurtainConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'PresPointBehaviourCurtainConfig_Detail'.
            /// </summary>
            public const string PresPointBehaviourCurtainConfig_Detail = "PresPointBehaviourCurtainConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'PresPointBehaviourLoginConfig_Brief'.
            /// </summary>
            public const string PresPointBehaviourLoginConfig_Brief = "PresPointBehaviourLoginConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'PresPointBehaviourLoginConfig_Detail'.
            /// </summary>
            public const string PresPointBehaviourLoginConfig_Detail = "PresPointBehaviourLoginConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'PresPointBehaviourMealTicketConfig_Brief'.
            /// </summary>
            public const string PresPointBehaviourMealTicketConfig_Brief = "PresPointBehaviourMealTicketConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'PresPointBehaviourMealTicketConfig_Detail'.
            /// </summary>
            public const string PresPointBehaviourMealTicketConfig_Detail = "PresPointBehaviourMealTicketConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'PresPointBehaviourMenuConfig_Brief'.
            /// </summary>
            public const string PresPointBehaviourMenuConfig_Brief = "PresPointBehaviourMenuConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'PresPointBehaviourMenuConfig_Detail'.
            /// </summary>
            public const string PresPointBehaviourMenuConfig_Detail = "PresPointBehaviourMenuConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'PresPointBehaviourMenuMealKindFilterConfig_Brief'.
            /// </summary>
            public const string PresPointBehaviourMenuMealKindFilterConfig_Brief = "PresPointBehaviourMenuMealKindFilterConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'PresPointBehaviourMenuMealKindFilterConfig_Detail'.
            /// </summary>
            public const string PresPointBehaviourMenuMealKindFilterConfig_Detail = "PresPointBehaviourMenuMealKindFilterConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'PresPointBehaviourNoteConfig_Brief'.
            /// </summary>
            public const string PresPointBehaviourNoteConfig_Brief = "PresPointBehaviourNoteConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'PresPointBehaviourNoteConfig_Detail'.
            /// </summary>
            public const string PresPointBehaviourNoteConfig_Detail = "PresPointBehaviourNoteConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'PresPointBehaviourOrderConfig_Brief'.
            /// </summary>
            public const string PresPointBehaviourOrderConfig_Brief = "PresPointBehaviourOrderConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'PresPointBehaviourOrderConfig_Detail'.
            /// </summary>
            public const string PresPointBehaviourOrderConfig_Detail = "PresPointBehaviourOrderConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'PresPointBehaviourPaymentTermDepositConfig_Brief'.
            /// </summary>
            public const string PresPointBehaviourPaymentTermDepositConfig_Brief = "PresPointBehaviourPaymentTermDepositConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'PresPointBehaviourPaymentTermDepositConfig_Detail'.
            /// </summary>
            public const string PresPointBehaviourPaymentTermDepositConfig_Detail = "PresPointBehaviourPaymentTermDepositConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'PresPointBehaviourScreenSaverConfig_Brief'.
            /// </summary>
            public const string PresPointBehaviourScreenSaverConfig_Brief = "PresPointBehaviourScreenSaverConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'PresPointBehaviourScreenSaverConfig_Detail'.
            /// </summary>
            public const string PresPointBehaviourScreenSaverConfig_Detail = "PresPointBehaviourScreenSaverConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'PresPointBehaviourStartMenuConfig_Brief'.
            /// </summary>
            public const string PresPointBehaviourStartMenuConfig_Brief = "PresPointBehaviourStartMenuConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'PresPointBehaviourStartMenuConfig_Detail'.
            /// </summary>
            public const string PresPointBehaviourStartMenuConfig_Detail = "PresPointBehaviourStartMenuConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'PresPointBehaviourStartScreenPicturesConfig_Brief'.
            /// </summary>
            public const string PresPointBehaviourStartScreenPicturesConfig_Brief = "PresPointBehaviourStartScreenPicturesConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'PresPointBehaviourStartScreenPicturesConfig_Detail'.
            /// </summary>
            public const string PresPointBehaviourStartScreenPicturesConfig_Detail = "PresPointBehaviourStartScreenPicturesConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'PresPointBehaviourUserInterfaceConfig_Brief'.
            /// </summary>
            public const string PresPointBehaviourUserInterfaceConfig_Brief = "PresPointBehaviourUserInterfaceConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'PresPointBehaviourUserInterfaceConfig_Detail'.
            /// </summary>
            public const string PresPointBehaviourUserInterfaceConfig_Detail = "PresPointBehaviourUserInterfaceConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'PresPointTimeoutConfig_Brief'.
            /// </summary>
            public const string PresPointTimeoutConfig_Brief = "PresPointTimeoutConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'PresPointTimeoutConfig_Detail'.
            /// </summary>
            public const string PresPointTimeoutConfig_Detail = "PresPointTimeoutConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'SchedulerPluginsAppServerConfig_Brief'.
            /// </summary>
            public const string SchedulerPluginsAppServerConfig_Brief = "SchedulerPluginsAppServerConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'SchedulerPluginsAppServerConfig_Detail'.
            /// </summary>
            public const string SchedulerPluginsAppServerConfig_Detail = "SchedulerPluginsAppServerConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'SchedulerPluginsAutoUpdaterConfig_Brief'.
            /// </summary>
            public const string SchedulerPluginsAutoUpdaterConfig_Brief = "SchedulerPluginsAutoUpdaterConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'SchedulerPluginsAutoUpdaterConfig_Detail'.
            /// </summary>
            public const string SchedulerPluginsAutoUpdaterConfig_Detail = "SchedulerPluginsAutoUpdaterConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'SchedulerPluginsBatchReportConfig_Brief'.
            /// </summary>
            public const string SchedulerPluginsBatchReportConfig_Brief = "SchedulerPluginsBatchReportConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'SchedulerPluginsBatchReportConfig_Detail'.
            /// </summary>
            public const string SchedulerPluginsBatchReportConfig_Detail = "SchedulerPluginsBatchReportConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'SchedulerPluginsFbsMenuSyncConfig_Brief'.
            /// </summary>
            public const string SchedulerPluginsFbsMenuSyncConfig_Brief = "SchedulerPluginsFbsMenuSyncConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'SchedulerPluginsFbsMenuSyncConfig_Detail'.
            /// </summary>
            public const string SchedulerPluginsFbsMenuSyncConfig_Detail = "SchedulerPluginsFbsMenuSyncConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'SchedulerPluginsHelpDeskSyncConfig_Brief'.
            /// </summary>
            public const string SchedulerPluginsHelpDeskSyncConfig_Brief = "SchedulerPluginsHelpDeskSyncConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'SchedulerPluginsHelpDeskSyncConfig_Detail'.
            /// </summary>
            public const string SchedulerPluginsHelpDeskSyncConfig_Detail = "SchedulerPluginsHelpDeskSyncConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'SchedulerPluginsKreditSyncConfig_Brief'.
            /// </summary>
            public const string SchedulerPluginsKreditSyncConfig_Brief = "SchedulerPluginsKreditSyncConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'SchedulerPluginsKreditSyncConfig_Detail'.
            /// </summary>
            public const string SchedulerPluginsKreditSyncConfig_Detail = "SchedulerPluginsKreditSyncConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'SchedulerPluginsStockMovementSenderConfig_Brief'.
            /// </summary>
            public const string SchedulerPluginsStockMovementSenderConfig_Brief = "SchedulerPluginsStockMovementSenderConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'SchedulerPluginsStockMovementSenderConfig_Detail'.
            /// </summary>
            public const string SchedulerPluginsStockMovementSenderConfig_Detail = "SchedulerPluginsStockMovementSenderConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'SchedulerPrefixesAxxosConfig_Brief'.
            /// </summary>
            public const string SchedulerPrefixesAxxosConfig_Brief = "SchedulerPrefixesAxxosConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'SchedulerPrefixesAxxosConfig_Detail'.
            /// </summary>
            public const string SchedulerPrefixesAxxosConfig_Detail = "SchedulerPrefixesAxxosConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'SchedulerPrefixesGerecConfig_Brief'.
            /// </summary>
            public const string SchedulerPrefixesGerecConfig_Brief = "SchedulerPrefixesGerecConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'SchedulerPrefixesGerecConfig_Detail'.
            /// </summary>
            public const string SchedulerPrefixesGerecConfig_Detail = "SchedulerPrefixesGerecConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'ServePointAppearanceAdditionalButtonsConfig_Brief'.
            /// </summary>
            public const string ServePointAppearanceAdditionalButtonsConfig_Brief = "ServePointAppearanceAdditionalButtonsConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'ServePointAppearanceAdditionalButtonsConfig_Detail'.
            /// </summary>
            public const string ServePointAppearanceAdditionalButtonsConfig_Detail = "ServePointAppearanceAdditionalButtonsConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'ServePointAppearanceApplicationConfig_Brief'.
            /// </summary>
            public const string ServePointAppearanceApplicationConfig_Brief = "ServePointAppearanceApplicationConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'ServePointAppearanceApplicationConfig_Detail'.
            /// </summary>
            public const string ServePointAppearanceApplicationConfig_Detail = "ServePointAppearanceApplicationConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'ServePointAppearanceBasicButtonsConfig_Brief'.
            /// </summary>
            public const string ServePointAppearanceBasicButtonsConfig_Brief = "ServePointAppearanceBasicButtonsConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'ServePointAppearanceBasicButtonsConfig_Detail'.
            /// </summary>
            public const string ServePointAppearanceBasicButtonsConfig_Detail = "ServePointAppearanceBasicButtonsConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'ServePointAppearanceExclamationButtonsConfig_Brief'.
            /// </summary>
            public const string ServePointAppearanceExclamationButtonsConfig_Brief = "ServePointAppearanceExclamationButtonsConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'ServePointAppearanceExclamationButtonsConfig_Detail'.
            /// </summary>
            public const string ServePointAppearanceExclamationButtonsConfig_Detail = "ServePointAppearanceExclamationButtonsConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'ServePointAppearanceGridViewConfig_Brief'.
            /// </summary>
            public const string ServePointAppearanceGridViewConfig_Brief = "ServePointAppearanceGridViewConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'ServePointAppearanceGridViewConfig_Detail'.
            /// </summary>
            public const string ServePointAppearanceGridViewConfig_Detail = "ServePointAppearanceGridViewConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'ServePointAppearanceSimpleViewConfig_Brief'.
            /// </summary>
            public const string ServePointAppearanceSimpleViewConfig_Brief = "ServePointAppearanceSimpleViewConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'ServePointAppearanceSimpleViewConfig_Detail'.
            /// </summary>
            public const string ServePointAppearanceSimpleViewConfig_Detail = "ServePointAppearanceSimpleViewConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'ServePointBehaviourSellingSettings_Brief'.
            /// </summary>
            public const string ServePointBehaviourSellingSettings_Brief = "ServePointBehaviourSellingSettings_Brief";
            
            /// <summary>
            /// Stores the resource name 'ServePointBehaviourSellingSettings_Detail'.
            /// </summary>
            public const string ServePointBehaviourSellingSettings_Detail = "ServePointBehaviourSellingSettings_Detail";
            
            /// <summary>
            /// Stores the resource name 'ServePointBehaviourServeringSettingsConfig_Brief'.
            /// </summary>
            public const string ServePointBehaviourServeringSettingsConfig_Brief = "ServePointBehaviourServeringSettingsConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'ServePointBehaviourServeringSettingsConfig_Detail'.
            /// </summary>
            public const string ServePointBehaviourServeringSettingsConfig_Detail = "ServePointBehaviourServeringSettingsConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'ServePointBehaviourUserInterfaceConfig_Brief'.
            /// </summary>
            public const string ServePointBehaviourUserInterfaceConfig_Brief = "ServePointBehaviourUserInterfaceConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'ServePointBehaviourUserInterfaceConfig_Detail'.
            /// </summary>
            public const string ServePointBehaviourUserInterfaceConfig_Detail = "ServePointBehaviourUserInterfaceConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'ServePointDisplayClientPersonalDataConfig_Brief'.
            /// </summary>
            public const string ServePointDisplayClientPersonalDataConfig_Brief = "ServePointDisplayClientPersonalDataConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'ServePointDisplayClientPersonalDataConfig_Detail'.
            /// </summary>
            public const string ServePointDisplayClientPersonalDataConfig_Detail = "ServePointDisplayClientPersonalDataConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'ServePointTimeoutsConfig_Brief'.
            /// </summary>
            public const string ServePointTimeoutsConfig_Brief = "ServePointTimeoutsConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'ServePointTimeoutsConfig_Detail'.
            /// </summary>
            public const string ServePointTimeoutsConfig_Detail = "ServePointTimeoutsConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'ServiceMonitorBehaviourLatencyMonitorConfig_Brief'.
            /// </summary>
            public const string ServiceMonitorBehaviourLatencyMonitorConfig_Brief = "ServiceMonitorBehaviourLatencyMonitorConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'ServiceMonitorBehaviourLatencyMonitorConfig_Detail'.
            /// </summary>
            public const string ServiceMonitorBehaviourLatencyMonitorConfig_Detail = "ServiceMonitorBehaviourLatencyMonitorConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'SloHealthyMenuConfig_Brief'.
            /// </summary>
            public const string SloHealthyMenuConfig_Brief = "SloHealthyMenuConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'SloHealthyMenuConfig_Detail'.
            /// </summary>
            public const string SloHealthyMenuConfig_Detail = "SloHealthyMenuConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'UbyportConfig_Brief'.
            /// </summary>
            public const string UbyportConfig_Brief = "UbyportConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'UbyportConfig_Detail'.
            /// </summary>
            public const string UbyportConfig_Detail = "UbyportConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'WaiterCashDeskBehaviourCashDeskPairingConfig_Brief'.
            /// </summary>
            public const string WaiterCashDeskBehaviourCashDeskPairingConfig_Brief = "WaiterCashDeskBehaviourCashDeskPairingConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'WaiterCashDeskBehaviourCashDeskPairingConfig_Detail'.
            /// </summary>
            public const string WaiterCashDeskBehaviourCashDeskPairingConfig_Detail = "WaiterCashDeskBehaviourCashDeskPairingConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'WaiterCashDeskBehaviourSynchronizationConfig_Brief'.
            /// </summary>
            public const string WaiterCashDeskBehaviourSynchronizationConfig_Brief = "WaiterCashDeskBehaviourSynchronizationConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'WaiterCashDeskBehaviourSynchronizationConfig_Detail'.
            /// </summary>
            public const string WaiterCashDeskBehaviourSynchronizationConfig_Detail = "WaiterCashDeskBehaviourSynchronizationConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'WaiterCashDeskGeneralConfig_Brief'.
            /// </summary>
            public const string WaiterCashDeskGeneralConfig_Brief = "WaiterCashDeskGeneralConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'WaiterCashDeskGeneralConfig_Detail'.
            /// </summary>
            public const string WaiterCashDeskGeneralConfig_Detail = "WaiterCashDeskGeneralConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'WebApiGeneralConfig_Brief'.
            /// </summary>
            public const string WebApiGeneralConfig_Brief = "WebApiGeneralConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'WebApiGeneralConfig_Detail'.
            /// </summary>
            public const string WebApiGeneralConfig_Detail = "WebApiGeneralConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'WebKreditAccommodationsHistoryConfig_Brief'.
            /// </summary>
            public const string WebKreditAccommodationsHistoryConfig_Brief = "WebKreditAccommodationsHistoryConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'WebKreditAccommodationsHistoryConfig_Detail'.
            /// </summary>
            public const string WebKreditAccommodationsHistoryConfig_Detail = "WebKreditAccommodationsHistoryConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'WebKreditAccommodationsOverviewConfig_Brief'.
            /// </summary>
            public const string WebKreditAccommodationsOverviewConfig_Brief = "WebKreditAccommodationsOverviewConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'WebKreditAccommodationsOverviewConfig_Detail'.
            /// </summary>
            public const string WebKreditAccommodationsOverviewConfig_Detail = "WebKreditAccommodationsOverviewConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'WebKreditAccountEBankingConfig_Brief'.
            /// </summary>
            public const string WebKreditAccountEBankingConfig_Brief = "WebKreditAccountEBankingConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'WebKreditAccountEBankingConfig_Detail'.
            /// </summary>
            public const string WebKreditAccountEBankingConfig_Detail = "WebKreditAccountEBankingConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'WebKreditAccountGdprConfig_Brief'.
            /// </summary>
            public const string WebKreditAccountGdprConfig_Brief = "WebKreditAccountGdprConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'WebKreditAccountGdprConfig_Detail'.
            /// </summary>
            public const string WebKreditAccountGdprConfig_Detail = "WebKreditAccountGdprConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'WebKreditAccountSettingsConfig_Brief'.
            /// </summary>
            public const string WebKreditAccountSettingsConfig_Brief = "WebKreditAccountSettingsConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'WebKreditAccountSettingsConfig_Detail'.
            /// </summary>
            public const string WebKreditAccountSettingsConfig_Detail = "WebKreditAccountSettingsConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'WebKreditAuthenticationConfig_Brief'.
            /// </summary>
            public const string WebKreditAuthenticationConfig_Brief = "WebKreditAuthenticationConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'WebKreditAuthenticationConfig_Detail'.
            /// </summary>
            public const string WebKreditAuthenticationConfig_Detail = "WebKreditAuthenticationConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'WebKreditBannersConfig_Brief'.
            /// </summary>
            public const string WebKreditBannersConfig_Brief = "WebKreditBannersConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'WebKreditBannersConfig_Detail'.
            /// </summary>
            public const string WebKreditBannersConfig_Detail = "WebKreditBannersConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'WebKreditFeedbackConfig_Brief'.
            /// </summary>
            public const string WebKreditFeedbackConfig_Brief = "WebKreditFeedbackConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'WebKreditFeedbackConfig_Detail'.
            /// </summary>
            public const string WebKreditFeedbackConfig_Detail = "WebKreditFeedbackConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'WebKreditGeneralConfig_Brief'.
            /// </summary>
            public const string WebKreditGeneralConfig_Brief = "WebKreditGeneralConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'WebKreditGeneralConfig_Detail'.
            /// </summary>
            public const string WebKreditGeneralConfig_Detail = "WebKreditGeneralConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'WebKreditHeaderConfig_Brief'.
            /// </summary>
            public const string WebKreditHeaderConfig_Brief = "WebKreditHeaderConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'WebKreditHeaderConfig_Detail'.
            /// </summary>
            public const string WebKreditHeaderConfig_Detail = "WebKreditHeaderConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'WebKreditHistoryConfig_Brief'.
            /// </summary>
            public const string WebKreditHistoryConfig_Brief = "WebKreditHistoryConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'WebKreditHistoryConfig_Detail'.
            /// </summary>
            public const string WebKreditHistoryConfig_Detail = "WebKreditHistoryConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'WebKreditInformationsConfig_Brief'.
            /// </summary>
            public const string WebKreditInformationsConfig_Brief = "WebKreditInformationsConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'WebKreditInformationsConfig_Detail'.
            /// </summary>
            public const string WebKreditInformationsConfig_Detail = "WebKreditInformationsConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'WebKreditLocalizationConfig_Brief'.
            /// </summary>
            public const string WebKreditLocalizationConfig_Brief = "WebKreditLocalizationConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'WebKreditLocalizationConfig_Detail'.
            /// </summary>
            public const string WebKreditLocalizationConfig_Detail = "WebKreditLocalizationConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'WebKreditMailingConfig_Brief'.
            /// </summary>
            public const string WebKreditMailingConfig_Brief = "WebKreditMailingConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'WebKreditMailingConfig_Detail'.
            /// </summary>
            public const string WebKreditMailingConfig_Detail = "WebKreditMailingConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'WebKreditOrderingConfig_Brief'.
            /// </summary>
            public const string WebKreditOrderingConfig_Brief = "WebKreditOrderingConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'WebKreditOrderingConfig_Detail'.
            /// </summary>
            public const string WebKreditOrderingConfig_Detail = "WebKreditOrderingConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'WebKreditOrderingExchangesConfig_Brief'.
            /// </summary>
            public const string WebKreditOrderingExchangesConfig_Brief = "WebKreditOrderingExchangesConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'WebKreditOrderingExchangesConfig_Detail'.
            /// </summary>
            public const string WebKreditOrderingExchangesConfig_Detail = "WebKreditOrderingExchangesConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'WebKreditOrderingMenuAnonymousConfig_Brief'.
            /// </summary>
            public const string WebKreditOrderingMenuAnonymousConfig_Brief = "WebKreditOrderingMenuAnonymousConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'WebKreditOrderingMenuAnonymousConfig_Detail'.
            /// </summary>
            public const string WebKreditOrderingMenuAnonymousConfig_Detail = "WebKreditOrderingMenuAnonymousConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'WebKreditOrderingMenuAuthenticatedConfig_Brief'.
            /// </summary>
            public const string WebKreditOrderingMenuAuthenticatedConfig_Brief = "WebKreditOrderingMenuAuthenticatedConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'WebKreditOrderingMenuAuthenticatedConfig_Detail'.
            /// </summary>
            public const string WebKreditOrderingMenuAuthenticatedConfig_Detail = "WebKreditOrderingMenuAuthenticatedConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'WebKreditOrderingOrdersConfig_Brief'.
            /// </summary>
            public const string WebKreditOrderingOrdersConfig_Brief = "WebKreditOrderingOrdersConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'WebKreditOrderingOrdersConfig_Detail'.
            /// </summary>
            public const string WebKreditOrderingOrdersConfig_Detail = "WebKreditOrderingOrdersConfig_Detail";
            
            /// <summary>
            /// Stores the resource name 'WebKreditRegistrationConfig_Brief'.
            /// </summary>
            public const string WebKreditRegistrationConfig_Brief = "WebKreditRegistrationConfig_Brief";
            
            /// <summary>
            /// Stores the resource name 'WebKreditRegistrationConfig_Detail'.
            /// </summary>
            public const string WebKreditRegistrationConfig_Detail = "WebKreditRegistrationConfig_Detail";
        }
    }
}
