using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Anete.Config.Core.Categories;
using Anete.Config.Core.Internals;
using Anete.Config.Core.Attributes;
using Anete.Utils.Collections.Tree;
using System.ComponentModel;
using System.Drawing;

namespace Anete.Config.Configs.Core.Office.MealDistribution.Behaviour
{
    /// <summary>
    /// Konfigurace tvorby poptavek v distribuci jidel
    /// </summary>
    [OfficeMealDistributionBehaviourDBKey("Inquiries")]
    [ConfigVersion("*******")]
    [ConfigClassSRDescription(typeof(OfficeMealDistributionBehaviourInquiriesConfig))]
    [DefaultConfigInstance(true)]
    [Office8ModuleCategory(Office8Module.MealDistribution)]
    public class OfficeMealDistributionBehaviourInquiriesConfig : ConfigBase
    {
        private const bool _allowChangeOldInquiriesDefValue = false;
        private bool _allowChangeOldInquiries = _allowChangeOldInquiriesDefValue;
        /// <summary>
        /// Povolit zmenu poptavek urcenych prodatum mensi nez dnes. Zmenu je i tak mozne provest pouze pokud neni uzavreno ucetni obdobi.
        /// </summary>
        [DefaultValue(_allowChangeOldInquiriesDefValue)]
        [ConfigSRDisplayName("AllowChangeOldInquiries", typeof(OfficeMealDistributionBehaviourInquiriesConfig))]
        [ConfigSRDescription("AllowChangeOldInquiries", typeof(OfficeMealDistributionBehaviourInquiriesConfig))]
        public bool AllowChangeOldInquiries
        {
            get { return _allowChangeOldInquiries; }
            set
            {
                if (_allowChangeOldInquiries == value)
                {
                    return;
                }
                _allowChangeOldInquiries = value;
                OnPropertyChanged("AllowChangeOldInquiries");
            }
        }
		
		private decimal _inquiryMealEndTimeOffset = 0;
		/// <summary>
		/// Offset v hodinach, pomoci ktereho lze posunout stop cas u vytvareni poptavek.
		/// </summary>
		[DefaultValue(typeof(decimal), "0")]
		[ConfigSRDisplayName("InquiryMealEndTimeOffset", typeof(OfficeMealDistributionBehaviourInquiriesConfig))]
		[ConfigSRDescription("InquiryMealEndTimeOffset", typeof(OfficeMealDistributionBehaviourInquiriesConfig))]		
		public decimal InquiryMealEndTimeOffset
		{
			get { return _inquiryMealEndTimeOffset; }
			set
			{
				if (_inquiryMealEndTimeOffset == value)
				{
					return;
				}
				_inquiryMealEndTimeOffset = value;
				OnPropertyChanged("InquiryMealEndTimeOffset");
			}
		}

		private const bool _allowDataExtensionDefValue = false;
		private bool _allowDataExtension = _allowDataExtensionDefValue;
		/// <summary>
		/// Povolit rozsireni udaju
		/// </summary>
		[DefaultValue(_allowDataExtensionDefValue)]
		[ConfigSRDisplayName("AllowDataExtension", typeof(OfficeMealDistributionBehaviourInquiriesConfig))]
		[ConfigSRDescription("AllowDataExtension", typeof(OfficeMealDistributionBehaviourInquiriesConfig))]
		public bool AllowDataExtension
		{
			get { return _allowDataExtension; }
			set
			{
				if (_allowDataExtension == value)
				{
					return;
				}
				_allowDataExtension = value;
				OnPropertyChanged("AllowDataExtension");
			}
		}

		private MealKindWorkplaceConfigItem[] _mealKindLunch = new MealKindWorkplaceConfigItem[0];
		/// <summary>
		/// Identifikace obedovych druhu jidel
		/// </summary>
		[ConfigSRDisplayName(nameof(MealKindLunch), typeof(OfficeMealDistributionBehaviourInquiriesConfig))]
		[ConfigSRDescription(nameof(MealKindLunch), typeof(OfficeMealDistributionBehaviourInquiriesConfig))]
		public MealKindWorkplaceConfigItem[] MealKindLunch
		{
			get
			{
				return _mealKindLunch;
			}
			set
			{
				if (_mealKindLunch == value)
				{
					return;
				}
				_mealKindLunch = value;
				OnPropertyChanged(nameof(MealKindLunch));
			}
		}
	}
}