using System;
using System.Collections.Generic;
using System.Reflection;
using Anete.Utils.ComponentModel.Attributes;
using Anete.Utils.Extensions;
using Anete.Utils.Reflection.ClassReports;

namespace Anete.Data.EntityReports
{
    /// <summary>
    /// Report pro reportovani objektu EF, Pouziva se zejmena pro logovani celych objektu.
    /// Do reportu prevadi pouze property oznacene atributy PrimitivePropertyAttribute a ComplexPropertyAttribute. 
    /// Nevypisuje tudiz navigacni property a dalsi pomocne property
    /// Pozor: Je znacne pomale, nepouzivat na vykonove kritickych mistech. 
    /// Vychazi asi 10x pomalejsi nez normalni String.Format - 1000 pruchodu objektu o 10 property 350 mSec
    /// </summary>
    public class EntityReport : IClassReport
    {
        #region private fields...
        private object _classInstance;
        #endregion

        #region constructors...
        /// <summary>
        /// Initializes a new instance of the ClassReport class.
        /// </summary>
        /// <param name="classInstance">The class instance.</param>
        public EntityReport(object classInstance)
        {
            _classInstance = classInstance;
        }
        #endregion

        #region IClassReport Members
        /// <summary>
        /// Typ tridy, z ktere byl report vygenerovan
        /// </summary>
        /// <value></value>
        public Type Type
        {
            get { throw new NotImplementedException(); }
        }

        private IEnumerable<IItemReport> _items = null;
        /// <summary>
        /// Seznam polozek, ktere se maji zobrazit
        /// </summary>
        /// <value></value>
        public IEnumerable<IItemReport> Items
        {
            get
            {
                if (_items == null)
                {
                    _items = EnumerateItems(_classInstance);
                }
                return _items;
            }
        }

		public string Name { get; set; }
		

		public string TextValue { get; set; }
		#endregion

		#region public static methods...
		/// <summary>
		/// Staticky wrapper
		/// </summary>
		/// <param name="obj">The obj.</param>
		public static string GetReportString(object obj)
        {
            return new EntityReport(obj).Items.ToReportString();
        }
        #endregion

        #region protected methods...
        /// <summary>
        /// Enumeruje objekt obj
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        protected virtual IEnumerable<IItemReport> EnumerateItems(object obj)
        {
            if (obj == null)
            {
                yield break;
            }

            EntityReportPropertyEnumerator propertyEnumerator = new EntityReportPropertyEnumerator(obj);
            foreach (PropertyInfo propertyInfo in propertyEnumerator.GetProperties())
            {
                if (propertyInfo.GetAttribute<NestedClassAttribute>(true) != null)
                {
                    yield return new NestedClassEntityReport(propertyInfo.Name, propertyInfo.GetValue(obj, null));
                }
                else
                {
                    yield return new PropertyReport(propertyInfo.Name, obj);
                }
            }          
        }
        #endregion

    }
}
