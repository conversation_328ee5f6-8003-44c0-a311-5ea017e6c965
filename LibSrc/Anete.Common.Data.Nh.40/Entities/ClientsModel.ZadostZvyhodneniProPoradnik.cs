//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Anete.Common.Data.Nh.Entities
{

    /// <summary>
    /// Tabulka: us.Zadost_ZvyhodneniProPoradnik
    /// </summary>
    public partial class ZadostZvyhodneniProPoradnik : Anete.Data.Nh.NhEntityBase, INotifyPropertyChanging, INotifyPropertyChanged {

        private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(System.String.Empty);

        private int _IdZadost;

        private short _IdZvyhodneniProPoradnik;

        private Zadost _Zadost;

        private ZvyhodneniProPoradnik _ZvyhodneniProPoradnik;
    
        #region Extensibility Method Definitions
        
        partial void OnCreated();

        public override bool Equals(object obj)
        {
          ZadostZvyhodneniProPoradnik toCompare = obj as ZadostZvyhodneniProPoradnik;
          if (toCompare == null)
          {
            return false;
          }

          if (!Object.Equals(this.IdZadost, toCompare.IdZadost))
            return false;
          if (!Object.Equals(this.IdZvyhodneniProPoradnik, toCompare.IdZvyhodneniProPoradnik))
            return false;
         // pokud mam default primary key, znamena to, ze jedna o nove vytvorenou entitu, ktera jeste nema key prirazen. 
        // V takove pripade musim porovnat podle instance
        bool isDefault = true;
        if (this.IdZadost != default(int))
        {
          isDefault = false;
        }
     
        if (this.IdZvyhodneniProPoradnik != default(short))
        {
          isDefault = false;
        }
     

          if (isDefault)
          {
            return object.ReferenceEquals(toCompare, this);
          }     
          return true;
        }

	private int? _hashCode = null;
        public override int GetHashCode()
        {
	if (_hashCode == null)
          {
          _hashCode = 13;
          _hashCode = (_hashCode * 7) + IdZadost.GetHashCode();
          _hashCode = (_hashCode * 7) + IdZvyhodneniProPoradnik.GetHashCode();
}
          return _hashCode.Value;
        }
        partial void OnIdZadostChanging(int value);
        
        partial void OnIdZadostChanged();
        partial void OnIdZvyhodneniProPoradnikChanging(short value);
        
        partial void OnIdZvyhodneniProPoradnikChanged();
        partial void OnZadostChanging(Zadost value);

        partial void OnZadostChanged();
        partial void OnZvyhodneniProPoradnikChanging(ZvyhodneniProPoradnik value);

        partial void OnZvyhodneniProPoradnikChanged();
        
        #endregion
        public ZadostZvyhodneniProPoradnik()
        {
            OnCreated();
        }

    
        /// <summary>
        /// Sloupec: idZadost
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("IdZadost", typeof(ZadostZvyhodneniProPoradnikSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(ZadostZvyhodneniProPoradnik), Tag="IdZadost")]
        public virtual int IdZadost
        {
            get
            {
                return this._IdZadost;
            }
            set
            {
                if (this._IdZadost != value)
                {
                    this.OnIdZadostChanging(value);
                    this.SendPropertyChanging();
                    this._IdZadost = value;
                    this.SendPropertyChanged("IdZadost");
                    this.OnIdZadostChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: idZvyhodneniProPoradnik
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("IdZvyhodneniProPoradnik", typeof(ZadostZvyhodneniProPoradnikSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(ZadostZvyhodneniProPoradnik), Tag="IdZvyhodneniProPoradnik")]
        public virtual short IdZvyhodneniProPoradnik
        {
            get
            {
                return this._IdZvyhodneniProPoradnik;
            }
            set
            {
                if (this._IdZvyhodneniProPoradnik != value)
                {
                    this.OnIdZvyhodneniProPoradnikChanging(value);
                    this.SendPropertyChanging();
                    this._IdZvyhodneniProPoradnik = value;
                    this.SendPropertyChanged("IdZvyhodneniProPoradnik");
                    this.OnIdZvyhodneniProPoradnikChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: idZadost
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Zadost", typeof(ZadostZvyhodneniProPoradnikSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual Zadost Zadost
        {
            get
            {
                return this._Zadost;
            }
            set
            {
                if (this._Zadost != value)
                {
                    this.OnZadostChanging(value);
                    this.SendPropertyChanging();
                    this._Zadost = value;
                    this.SendPropertyChanged("Zadost");
                    this.OnZadostChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: idZvyhodneniProPoradnik
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("ZvyhodneniProPoradnik", typeof(ZadostZvyhodneniProPoradnikSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual ZvyhodneniProPoradnik ZvyhodneniProPoradnik
        {
            get
            {
                return this._ZvyhodneniProPoradnik;
            }
            set
            {
                if (this._ZvyhodneniProPoradnik != value)
                {
                    this.OnZvyhodneniProPoradnikChanging(value);
                    this.SendPropertyChanging();
                    this._ZvyhodneniProPoradnik = value;
                    this.SendPropertyChanged("ZvyhodneniProPoradnik");
                    this.OnZvyhodneniProPoradnikChanged();
                }
            }
        }
   
        public virtual event PropertyChangingEventHandler PropertyChanging;

	// Mam v bazove tride, nema smysl generovat
	// public virtual event PropertyChangedEventHandler PropertyChanged;

        protected virtual void SendPropertyChanging()
        {
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, emptyChangingEventArgs);
        }

        protected virtual void SendPropertyChanging(System.String propertyName) 
        {   	
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, new PropertyChangingEventArgs(propertyName));
        }

        protected virtual void SendPropertyChanged(System.String propertyName)
        {    
		// mam v bazove tride, proto musim volat takto jednoduse
		OnPropertyChanged(propertyName);
/*		        var handler = this.PropertyChanged;
            if (handler != null)
                handler(this, new PropertyChangedEventArgs(propertyName));*/
        }
    }

}
