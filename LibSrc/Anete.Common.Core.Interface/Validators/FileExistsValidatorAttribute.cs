using System;
using Microsoft.Practices.EnterpriseLibrary.Validation.Validators;

namespace Anete.Common.Core.Interface.Validators
{
    /// <summary>
    /// Validace existence/neexistence souboru
    /// </summary>
    public class FileExistsValidatorAttribute : ValueValidatorAttribute
    {
        /// <summary>
        /// Initializes a new instance of the FileExistsValidatorAttribute class.
        /// </summary>
        public FileExistsValidatorAttribute()
        {
        }

        /// <summary>
        /// Creates the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Validation.Validator"/> described by the attribute object providing validator specific
        /// information.
        /// </summary>
        /// <param name="targetType">The type of object that will be validated by the validator.</param>
        /// <returns>
        /// The created <see cref="T:Microsoft.Practices.EnterpriseLibrary.Validation.Validator"/>.
        /// </returns>
        /// <remarks>This operation must be overridden by subclasses.</remarks>
        protected override Microsoft.Practices.EnterpriseLibrary.Validation.Validator DoCreateValidator(Type targetType)
        {
            return new FileExistsValidator();
        }
    }
}
