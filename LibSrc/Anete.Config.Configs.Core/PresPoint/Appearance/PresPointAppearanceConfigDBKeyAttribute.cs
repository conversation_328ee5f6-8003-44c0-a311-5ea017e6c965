using System;
using System.Collections.Generic;
using System.Text;
using Anete.Config.Core.Internals;
using Anete.Utils;
using Anete.Utils.Collections.Tree;

namespace Anete.Config.Configs.Core.PresPoint.Appearance
{    
    /// <summary>
    /// Attribut konfiguracni tridy pro vzhled PM
    /// </summary>
    public class PresPointAppearanceConfigDBKeyAttribute : PresPointConfigDBKeyAttribute
    {
          /// <summary>
        /// Prefix databazoveho klice pro nastaveni vzhledu PM
        /// </summary>
        public const string PresPointAppearanceDBKeyPrefix = "Appearance";

        /// <summary>
        /// Initializes a new instance of the PresPointAppearanceConfigDBKeyAttribute class.
        /// </summary>
        /// <param name="behaviorKey">Klic bez prefixu PM\Appearance</param>
        public PresPointAppearanceConfigDBKeyAttribute(string behaviorKey)
            : base(KeyUtils.Combine(PresPointAppearanceDBKeyPrefix, behaviorKey))
        {
        }
    }
}
