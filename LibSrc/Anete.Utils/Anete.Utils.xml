<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Anete.Utils</name>
    </assembly>
    <members>
        <member name="T:Anete.Utils.StrUtils">
            <summary>
            utlility pro praci se stringy
            </summary>
        </member>
        <member name="M:Anete.Utils.StrUtils.SubstrCopy(System.String,System.Int32,System.Int32)">
            <summary>
            okopiruje Count znaku od StartIndex. Funguje jako Copy z Delphi. Na rozdil od Substr nevadi, kdyz je
            StartIndex nebo Count mimo rozsah retezce
            </summary>
            <param name="s">Vstupni retezec</param>
            <param name="startIndex">Index pocatecniho znaku, prvni znak ma index 0</param>
            <param name="count">Pocet znaku k okopirovani</param>
            <returns>Vysledna cast retezce</returns>
        </member>
        <member name="M:Anete.Utils.StrUtils.RestOf(System.String,System.Int32)">
            <summary>
            okopiruje cely zbytek retezce
            </summary>
            <param name="s">Vstupni retezec</param>
            <param name="startIndex">Pocatecni index</param>
            <returns>Vysledna cast retezce</returns>
        </member>
        <member name="M:Anete.Utils.StrUtils.Left(System.String,System.Int32)">
            <summary>
            Okopiruje cast retezce zleva
            </summary>
            <param name="s">Vstupni retezec</param>
            <param name="lenght">Pocet znaku k okopirovani</param>
            <returns></returns>
        </member>
        <member name="M:Anete.Utils.StrUtils.Right(System.String,System.Int32)">
            <summary>
            Okopiruje case retezce zprava
            </summary>
            <param name="s">Vstupni retezec</param>
            <param name="count">Pocet znaku k okopirovani</param>
            <returns></returns>
        </member>
        <member name="M:Anete.Utils.StrUtils.IsEmpty(System.String)">
            <summary>
            Vraci true, pokud je retezec null, "" nebo obsahuje pouze mezery
            </summary>
            <param name="s">Vstupni retezec</param>
            <returns></returns>
        </member>
        <member name="M:Anete.Utils.StrUtils.IsNotEmpty(System.String)">
            <summary>
            Vraci true, pokud je retezec null, "" nebo obsahuje pouze mezery
            </summary>
            <param name="s">Vstupni retezec</param>
            <returns></returns>
        </member>
        <member name="M:Anete.Utils.StrUtils.EqualsCI(System.String,System.String)">
            <summary>
            Porovnani retezcu bez ohledu na velikost pisma
            </summary>
            <param name="a">prvni retezec</param>
            <param name="b">druhy retezec</param>
            <returns>true, pokud jsou retezce totozne</returns>
        </member>
        <member name="M:Anete.Utils.StrUtils.IsSame(System.String,System.String)">
            <summary>
            Porovnani retezcu bez ohledu na velikost pisma a mezery na konci nebo pocatku
            </summary>
            <param name="a">prvni retezec</param>
            <param name="b">druhy retezec</param>
            <returns>true, pokud jsou oba retezce totozne</returns>
        </member>
        <member name="M:Anete.Utils.StrUtils.After(System.String,System.String)">
            <summary>
            extrahuje z retezce cast nasledujici po subStr
            </summary>
            <param name="subStr">rozhodujici cast</param>
            <param name="str">cely retezec</param>
            <returns>cast nasledujici po subStr</returns>
        </member>
        <member name="M:Anete.Utils.StrUtils.Before(System.String,System.String)">
            <summary>
            extrahuje z retezce cast predchazejici pred subStr
            </summary>
            <param name="subStr">rozhodujici cast</param>
            <param name="str">cely retezec</param>
            <returns>cast predchazejici pred subStr</returns>
        </member>
        <member name="M:Anete.Utils.StrUtils.Between(System.String,System.String,System.String)">
            <summary>
            Extrahuje z retezce cast mezi startStr a endStr. Pokud neni startSubStr nebo endSubStr
            nalezen, vraci prazdny retezec
            </summary>
            <param name="startSubstr">Pocatecni podretezec</param>
            <param name="endSubstr">Konecny podretezec</param>
            <param name="str">cely retezec</param>
            <returns></returns>
            <remarks></remarks>
        </member>
        <member name="M:Anete.Utils.StrUtils.RemovePrefix(System.String,System.String)">
            <summary>
            pokud string zacina prefixem prefix, odstrani ho. Porovnani je case-insensitive
            </summary>
            <param name="prefix"></param>
            <param name="str"></param>
            <remarks></remarks>
        </member>
        <member name="M:Anete.Utils.StrUtils.RemoveSuffix(System.String,System.String)">
            <summary>
            pokud string konci suffixem suffix, odstrani ho. Porovnani je case-insensitive
            </summary>
            <param name="suffix"></param>
            <param name="str"></param>
            <remarks></remarks>
        </member>
        <member name="M:Anete.Utils.StrUtils.ExtractWord(System.Int32,System.String,System.Char)">
            <summary>
            Je dana mnozina oddelovacu slov-WordDelims,vraci N-te slovo v S
            </summary>
            <param name="n">index slova, zacina od 1</param>
            <param name="str">vstupni retezec</param>
            <param name="wordDelims">znak oddelujici slova</param>
            <returns>m-te slovo v retezci</returns>
        </member>
        <member name="M:Anete.Utils.StrUtils.WordCount(System.String,System.Char)">
            <summary>
            Vraci pocet slov oddelenych oddelovaci slov-WordDelims
            </summary>
            <param name="str">vstupni string</param>
            <param name="wordDelims">oddelovac slov</param>
            <returns>pocet slov v retezci</returns>
        </member>
        <member name="M:Anete.Utils.StrUtils.IsContainedInList(System.String,System.String)">
            <summary>
            Vraci true, pokud je toFind obsazen v seznamu list
            </summary>
            <param name="value">string ktery je vyhledavany v seznamu</param>
            <param name="valueList">seznam moznych hodnot oddeleny carkami</param>
            <returns>true, pokud je toFind obsazen v list</returns>
        </member>
        <member name="M:Anete.Utils.StrUtils.IsContainedInList(System.String,System.String,System.Char)">
            <summary>
            Vraci true, pokud je toFind obsazen v seznamu list
            </summary>
            <param name="value">string ktery je vyhledavany v seznamu</param>
            <param name="valueList">seznam moznych hodnot oddeleny separator</param>
            <param name="delimiter">oddelovac seznamu list</param>
            <returns>true, pokud je toFind obsazen v list</returns>
        </member>
        <member name="M:Anete.Utils.StrUtils.Split(System.String,System.String,System.String,System.Boolean)">
            <summary>
            Funguje obdobne jako String.Split ale umoznuje pouziti oddelovacu textu
            </summary>
            <param name="expression">vstupni text</param>
            <param name="delimiter">oddelovac stringu</param>
            <param name="qualifier">textovy kvalifikator</param>
            <param name="ignoreCase">ignorovat velikost pisma</param>
            <returns>pole oddelenych stringu</returns>
            <remarks>Mirne upravene z http://www.codeproject.com/useritems/TextQualifyingSplit.asp</remarks>
        </member>
        <member name="M:Anete.Utils.StrUtils.MyPadL(System.String,System.Int32)">
            <summary>
            jako PadLeft, ale vetsi delku orizne
            </summary>
            <param name="s"></param>
            <param name="l"></param>
            <returns></returns>
            <remarks></remarks>
        </member>
        <member name="M:Anete.Utils.StrUtils.MyPadR(System.String,System.Int32)">
            <summary>
            jako PadRight, ale vetsi delku orizne
            </summary>
            <param name="s"></param>
            <param name="l"></param>
            <returns></returns>
            <remarks></remarks>
        </member>
        <member name="M:Anete.Utils.StrUtils.MyPadC(System.String,System.Int32)">
            <summary>
            zarovnani doprostred
            </summary>
            <param name="s"></param>
            <param name="l"></param>
            <returns></returns>
            <remarks></remarks>
        </member>
        <member name="M:Anete.Utils.StrUtils.MyPadC(System.String,System.Int32,System.Char)">
            <summary>
            zarovnani doprostred
            </summary>
            <param name="s"></param>
            <param name="l"></param>
            <param name="PadChar"></param>
            <returns></returns>
            <remarks></remarks>
        </member>
        <member name="T:Anete.Utils.DisposableSingleton`1">
            <summary>
            implementace singletonu s rozhranim Disposable
            </summary>
        </member>
        <member name="T:Anete.Utils.Disposable">
            <summary>
            trida, ktera implementuje rozhrani IDisposable. Od ni je dobre odvozovat vsechny
            tridy, ktere potrebuji implementovat IDisposable
            </summary>
            <remarks>Oproti std. Dispose pattern upraveno podle http://www.vbinfozine.com/a_disposable.shtml
            Tato implementace mi prijde bezpecnejsi
            Vypis zasobniku v pripade nezavolani Dispose je z http://blogs.msdn.com/andrewdownum/archive/2005/01/04/346448.aspx
            </remarks>
        </member>
        <member name="M:Anete.Utils.Disposable.#ctor">
            <summary>
            Initializes a new instance of the Disposable class.
            </summary>
        </member>
        <member name="M:Anete.Utils.Disposable.Finalize">
            <summary>
            destruktor, zavola automaticky Dispose
            </summary>
        </member>
        <member name="M:Anete.Utils.Disposable.DisposeUnmanagedResources">
            <summary>
            Tato metoda musi uvolnit vsechny nespravovane prostredky.
            </summary>
            <remarks>Pokud ji zajima, zda je volana z Dispose, je mozno testovat flag IsDisposing</remarks>
        </member>
        <member name="M:Anete.Utils.Disposable.DisposeManagedResources">
            <summary>
            Tato metoda muze uvolnit vsechny nespravovane prostredky
            </summary>
            <remarks>Je nutno prepisovat pouze pokud napr. objekt alokuje velke objemy pameti</remarks>
        </member>
        <member name="M:Anete.Utils.Disposable.CheckDisposed">
            <summary>
            Zkontroluje, zda u objektu nebyla volana metoda Dispose
            </summary>
            <remarks>Pokud byla volana, vyvola vyjimku</remarks>
        </member>
        <member name="M:Anete.Utils.Disposable.Dispose">
            <summary>
            implementace Dispose od IDisposable
            </summary>
        </member>
        <member name="P:Anete.Utils.Disposable.IsDisposed">
            <summary>
            rika, zda u objektu uz bylo zavolano Dispose
            </summary>
            <value></value>
            <returns></returns>
            <remarks></remarks>
        </member>
        <member name="P:Anete.Utils.Disposable.IsDisposing">
            <summary>
            rika, zda se prave vola metoda Dispose, tedy volana uzivatelem
            </summary>
            <remarks></remarks>
        </member>
        <member name="P:Anete.Utils.DisposableSingleton`1.Instance">
            <summary>
            Instance singletonu
            </summary>
        </member>
        <member name="T:Anete.Utils.ExceptionHandlerEventArgs">
            <summary>
            argumenty pro predavani udalosti ErrorHandlerum
            </summary>
            <remarks></remarks>
        </member>
        <member name="M:Anete.Utils.ExceptionHandlerEventArgs.#ctor(System.Exception)">
            <summary>
            konstruktor
            </summary>
            <param name="aExceptionObject"></param>
        </member>
        <member name="P:Anete.Utils.ExceptionHandlerEventArgs.ExceptionObject">
            <summary>
            pripojena vyjimka
            </summary>
            <value></value>
            <returns></returns>
            <remarks></remarks>
        </member>
        <member name="P:Anete.Utils.ExceptionHandlerEventArgs.IsHandled">
            <summary>
            EventHandler musi nastavit na true, pokud je vyjimka vyrizena
            </summary>
            <value></value>
            <returns></returns>
            <remarks></remarks>
        </member>
        <member name="T:Anete.Kasa8.Common.BaseResDescriptionAttribute">
            <summary>
            Atribut pro nacitani lokalizovaneho popisu z resource souboru
            </summary>
            <remarks>Z toto tridy je nutno odvodit tridy pro praci s konkretnimi resource managery</remarks>
        </member>
        <member name="M:Anete.Kasa8.Common.BaseResDescriptionAttribute.#ctor(System.Resources.ResourceManager,System.String)">
            <summary>
            
            </summary>
            <param name="resManager"></param>
            <param name="description"></param>
        </member>
        <member name="P:Anete.Kasa8.Common.BaseResDescriptionAttribute.Description">
            <summary>
            
            </summary>
        </member>
        <member name="T:Anete.Utils.Exceptions.DbKeyNotFoundUserException">
             <summary>
            
             </summary>
        </member>
        <member name="M:Anete.Utils.Exceptions.DbKeyNotFoundUserException.#ctor">
            <summary>
            Constructs a new DbKeyNotFoundUserException.
            </summary>
        </member>
        <member name="M:Anete.Utils.Exceptions.DbKeyNotFoundUserException.#ctor(System.String,System.String)">
            <summary>
            Constructs a new DbKeyNotFoundUserException.
            </summary>
            <param name="message">The exception message</param>
            <param name="key">The value for the Key property.</param>
        </member>
        <member name="M:Anete.Utils.Exceptions.DbKeyNotFoundUserException.#ctor(System.String,System.String,System.Exception)">
            <summary>
            Constructs a new DbKeyNotFoundUserException.
            </summary>
            <param name="message">The exception message.</param>
            <param name="key">The value for the Key property.</param>
            <param name="innerException">The inner exception.</param>
        </member>
        <member name="M:Anete.Utils.Exceptions.DbKeyNotFoundUserException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Serialization constructor.
            </summary>
        </member>
        <member name="M:Anete.Utils.Exceptions.DbKeyNotFoundUserException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Overridden method from the ISerializable interface, to include the additional fields in serialization.
            </summary>
        </member>
        <member name="P:Anete.Utils.Exceptions.DbKeyNotFoundUserException.Key">
             <summary>
            
             </summary>
        </member>
        <member name="T:Anete.Utils.Exceptions.ConfigException">
            <summary>
            Obecna chyba konfigurace
            </summary>
        </member>
        <member name="M:Anete.Utils.Exceptions.ConfigException.#ctor">
            <summary>
            Constructs a new ConfigException.
            </summary>
        </member>
        <member name="M:Anete.Utils.Exceptions.ConfigException.#ctor(System.String)">
            <summary>
            Constructs a new ConfigException.
            </summary>
            <param name="message">The exception message</param>
        </member>
        <member name="M:Anete.Utils.Exceptions.ConfigException.#ctor(System.String,System.Exception)">
            <summary>
            Constructs a new ConfigException.
            </summary>
            <param name="message">The exception message</param>
            <param name="innerException">The inner exception</param>
        </member>
        <member name="M:Anete.Utils.Exceptions.ConfigException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Serialization constructor.
            </summary>
        </member>
        <member name="T:Anete.Utils.Singleton`1">
             <summary>
             Genericka implementace singletonu
             </summary>
             <remarks>
             Singleton se pouziva pro promenne, ktere maji existovat pouze jednou pro cely program
             Typicky priklad:
             odvozeni vlastni tridy, abych pro uzivatele skryl generika:
            
             Public Class ExeSettings
                 Inherits Singleton(Of ExeSettings)
            
             Public Property Name...
             End Class
            
             A pak v aplikaci:
             myName = ExecSettings.Instance.Name
            
             Zajisti automaticke vytvoreni singletonu, pokud dosud neexistoval. Nebo vraci ukazatel na instanci
             tridy, pokud uz predtim byl volan
             </remarks>
        </member>
        <member name="P:Anete.Utils.Singleton`1.Instance">
            <summary>
            Instance singletonu
            </summary>
        </member>
        <member name="T:Anete.Utils.LibSys">
            <summary>
            
            </summary>
        </member>
        <member name="M:Anete.Utils.LibSys.GetTypes(System.Collections.IEnumerable)">
            <summary>
            Takes an IEnumerable and returns a System.Type[]
            </summary>
            <param name="Items">
            The items whose Types you want
            </param>
            <returns>
            An array containing the System.Types of the Items
            </returns>
        </member>
        <member name="T:Anete.Utils.OleDbUils">
            <summary>
            Utility pro praci s OleDb
            </summary>
            <remarks></remarks>
        </member>
        <member name="M:Anete.Utils.OleDbUils.WaitForAccessFileToBeReleased(System.String,System.Int32)">
            <summary>
            Ceka, az OleDb driver opravdu zavre soubor
            </summary>
            <param name="fileName">Jmeno souboru OleDb</param>
            <param name="timeout">Cas v sekundach, po ktery se max. ceka</param>
            <returns>True, pokud se podarilo zavrit soubor, jinak False</returns>
            <remarks></remarks>
        </member>
        <member name="T:Anete.Utils.NullUtils">
            <summary>
            Podpora pro nullable typy
            </summary>
            <remarks>http://www.panopticoncentral.net/archive/2004/06/04/1180.aspx
            Dim i As Nullable(Of Integer) = 100
            i = New Nullable(Of Integer)(i.Value + 20)
            </remarks>
        </member>
        <member name="M:Anete.Utils.NullUtils.Coalesce``1(System.Nullable{``0},``0)">
            <summary>
            Vraci hodnotu x, pokud je obsazena, jinak hodnotu y
            </summary>
            <typeparam name="T"></typeparam>
            <param name="x"></param>
            <param name="y"></param>
            <returns></returns>
            <remarks>
            Dim a As Nullable(Of Integer) = Nothing
            Debug.WriteLine(NullUtils.Coalesce(a, -1))
            </remarks>
        </member>
        <member name="M:Anete.Utils.NullUtils.Coalesce``1(System.Nullable{``0},System.Nullable{``0},``0)">
            <summary>
            vraci hodnotu x, pokud ma hodnotu, y, pokud ma hodnotu, jinak z
            </summary>
            <typeparam name="T"></typeparam>
            <param name="x"></param>
            <param name="y"></param>
            <param name="z"></param>
            <returns></returns>
            <remarks>
            Dim a As Nullable(Of Integer) = Nothing
            Dim b As Nullable(Of Integer) = Nothing
            Debug.WriteLine(NullUtils.Coalesce(a, b, -1))
            </remarks>
        </member>
        <member name="T:Anete.Utils.ConfigFileMerge">
            <summary>
            Manages config file reading and writing, and optional merging of two config files.
            Bylo nutne pocestit exception hlasky ...
            </summary>
            
        </member>
        <member name="M:Anete.Utils.ConfigFileMerge.#ctor(System.String,System.String,System.Boolean)">
            <summary>
            Do noveho konfiguracniho souboru MasterConfigPath pripoji vsechny stavajici klice z mergefromConfigPath. 
            Tim vznikne soubor, ktery ma vsechny atributy noveho souboru a pritom se zachovaji vsechny klice ze 
            stavajiciho souboru.
            </summary>
            <param name="masterConfigPath">Novy soubor, do ktereho chci doplnit stavajici klice</param>
            <param name="mergeFromConfigPath">Soubor, ze ktereho chci doplnit klice</param>
            <param name="makeMergeFromConfigPathTheSavePath">True pokud chci ulozit zmeny do mergeFromConfigPath</param>
            <exception cref="T:System.Exception">if mergeFromConfigPath is specified but does not exist, and makeMergeFromConfigPathTheSavePath is false</exception>
        </member>
        <member name="M:Anete.Utils.ConfigFileMerge.ReplaceXPathValues(System.String,System.String,System.Text.RegularExpressions.Regex)">
            <summary>
            Search and replace on one or more specified values as specified by a single xpath expressino
            </summary>
            <param name="xPath"></param>
            <param name="replaceWith"></param>
            <param name="regexPattern">Optionally specify a regex pattern to search within the found values. 
            If a single () group is found within this expression, only this portion is replaced.</param>
            <returns></returns>
            <exception cref="T:System.ApplicationException">When no nodes match xpath-expression and no regexPattern is specified (is null), 
            and can't auto-create the node (is not an appSettings expression).</exception>
        </member>
        <member name="M:Anete.Utils.ConfigFileMerge.GetXmlNodePath(System.Xml.XmlNode)">
            <summary>
            Vraci plnou cestu k uzlu 
            </summary>
            <param name="node"></param>
            <returns></returns>
        </member>
        <member name="M:Anete.Utils.ConfigFileMerge.UpdateExistingElementsAndAttribs(System.Xml.XmlDocument,System.Xml.XmlDocument)">
            <summary>
            Merge element and attribute values from one xml doc to another.
            </summary>
            <param name="fromXdoc"></param>
            <param name="toXdoc"></param>
            <remarks>
            Multiple same-named peer elements, are merged in the ordinal order they appear.
            </remarks>
        </member>
        <member name="T:Anete.Utils.Set`1">
            <summary>
            
            </summary>
            <remarks>Pochazi z http://www.codeproject.com/csharp/Types_Set.asp 
            </remarks>
            <typeparam name="T"></typeparam>   
        </member>
        <member name="M:Anete.Utils.Set`1.#ctor(System.Object[])">
                    <summary>
                        Constructs and populates a Set.
                    </summary>
                    <param name="Items">
                        (Optional) Items to add to the new Set.
                    </param>
        </member>
        <member name="M:Anete.Utils.Set`1.op_Implicit(`0)~Anete.Utils.Set{`0}">
                    <summary>
                        Converts an item to a Set
                    </summary>
        </member>
        <member name="M:Anete.Utils.Set`1.op_Implicit(`0[])~Anete.Utils.Set{`0}">
                    <summary>
                        Converts an array of items to a Set
                    </summary>
        </member>
        <member name="M:Anete.Utils.Set`1.op_Implicit(System.Array)~Anete.Utils.Set{`0}">
                    <summary>
                        Converts an array of items to a Set
                    </summary>
        </member>
        <member name="M:Anete.Utils.Set`1.op_Implicit(System.Collections.CollectionBase)~Anete.Utils.Set{`0}">
                    <summary>
                        Converts a Collection of items to a Set
                    </summary>
        </member>
        <member name="M:Anete.Utils.Set`1.op_Implicit(System.Collections.ArrayList)~Anete.Utils.Set{`0}">
                    <summary>
                        Converts an ArrayList of items to a Set
                    </summary>
        </member>
        <member name="M:Anete.Utils.Set`1.op_Addition(Anete.Utils.Set{`0},Anete.Utils.Set{`0})">
                    <summary>
                        Union of the two Sets; Set of items that are elements of at least one of the Sets.
                    </summary>
        </member>
        <member name="M:Anete.Utils.Set`1.op_Subtraction(Anete.Utils.Set{`0},Anete.Utils.Set{`0})">
                    <summary>
                        Relative complement; items that are elements of the first Set, but not Elements of the second Set.
                    </summary>
        </member>
        <member name="M:Anete.Utils.Set`1.op_BitwiseOr(Anete.Utils.Set{`0},Anete.Utils.Set{`0})">
                    <summary>
                        Union of the two Sets; items that are elements of at least one of the Sets.
                    </summary>
        </member>
        <member name="M:Anete.Utils.Set`1.op_BitwiseAnd(Anete.Utils.Set{`0},Anete.Utils.Set{`0})">
                    <summary>
                        Intersection of the two Sets; items that are elements of both of the Sets.
                    </summary>
        </member>
        <member name="M:Anete.Utils.Set`1.op_ExclusiveOr(Anete.Utils.Set{`0},Anete.Utils.Set{`0})">
                    <summary>
                        Exclusive Or of the two Sets; items that are elements of only one of the Sets.
                    </summary>
        </member>
        <member name="M:Anete.Utils.Set`1.op_Equality(Anete.Utils.Set{`0},Anete.Utils.Set{`0})">
                    <summary>
                        Test equality of Sets; True if both Sets have the same elements
                    </summary>
        </member>
        <member name="M:Anete.Utils.Set`1.op_Inequality(Anete.Utils.Set{`0},Anete.Utils.Set{`0})">
                    <summary>
                        Test inequality of Sets; True if the Sets do not have the same elements
                    </summary>
        </member>
        <member name="M:Anete.Utils.Set`1.op_LessThan(Anete.Utils.Set{`0},Anete.Utils.Set{`0})">
                    <summary>
                        Subset; true if the first Set is a subset of (but is not equal to) the second
                    </summary>
        </member>
        <member name="M:Anete.Utils.Set`1.op_GreaterThan(Anete.Utils.Set{`0},Anete.Utils.Set{`0})">
                    <summary>
                        Superset; true if the first Set is a superset of (but is not equal to) the second 
                    </summary>
        </member>
        <member name="M:Anete.Utils.Set`1.op_LessThanOrEqual(Anete.Utils.Set{`0},Anete.Utils.Set{`0})">
                    <summary>
                        Subset; true if the first Set is a subset of the second 
                    </summary>
        </member>
        <member name="M:Anete.Utils.Set`1.op_GreaterThanOrEqual(Anete.Utils.Set{`0},Anete.Utils.Set{`0})">
                    <summary>
                        Superset; true if the first Set is a superset of the second 
                    </summary>
        </member>
        <member name="M:Anete.Utils.Set`1.GetEnumerator">
                    <summary>
                        Enumerator for the elements of the Set
                    </summary>
        </member>
        <member name="M:Anete.Utils.Set`1.Equals(System.Object)">
                    <summary>
                        Yada yada yada
                    </summary>
        </member>
        <member name="M:Anete.Utils.Set`1.GetHashCode">
                    <summary>
                        Yada yada yada
                    </summary>
        </member>
        <member name="M:Anete.Utils.Set`1.ToString">
                    <summary>
                        Returns the elements of the Set in set format; { element1 , element2 ... }
                    </summary>
                    <remarks>
                        ToString() is called on each element in turn.
                        No attempt is made to protect against elements whose ToString() values contain commas or braces.
                    </remarks>
        </member>
        <member name="M:Anete.Utils.Set`1.ToString(Anete.Utils.SortMode,System.Object[])">
                    <summary>
                        Returns the elements of the Set in set format; { element1 , element2 ... }
                    </summary>
                    <param name="SortMode">
                        Whether or not to sort the elements.
                    </param>
                    <param name="FormatInfo">
                        (Optional) Formatting information to pass to ToString()
                    </param>
                    <remarks>
                        ToString() is called on each element in turn.
                        No attempt is made to protect against elements whose ToString() values contain commas or braces.
                    </remarks>
        </member>
        <member name="M:Anete.Utils.Set`1.Add(System.Object[])">
                    <summary>
                        Attempts to add Items to the Set
                    </summary>
        </member>
        <member name="M:Anete.Utils.Set`1.Remove(System.Object[])">
                    <summary>
                        Attempts to remove Items from the Set
                    </summary>
        </member>
        <member name="M:Anete.Utils.Set`1.Contains(System.Object[])">
                    <summary>
                        Returns true if the Set contains the Item(s)
                    </summary>
        </member>
        <member name="M:Anete.Utils.Set`1.Clear">
                    <summary>
                        Removes all elements from the Set
                    </summary>
        </member>
        <member name="P:Anete.Utils.Set`1.Cardinality">
                    <return>
                        The number of elements in the Set.
                    </return>
        </member>
        <member name="P:Anete.Utils.Set`1.EqualityComparer">
                    <return>
                        The System.Collections.Generic.IEqualityComparer to use
                    </return>
        </member>
        <member name="T:Anete.Utils.SysUtils">
            <summary>
            Utility systemoveho charakteru
            </summary>
            <remarks></remarks>
        </member>
        <member name="M:Anete.Utils.SysUtils.IsDesignMode">
            <summary>
            Vraci true, pokud je system v DesignMode.
            </summary>
            <returns></returns>
            <remarks>Obchazi se tim to, ze u Component detekce pomoci Site.DesignMode
            nefunguje.</remarks>
        </member>
        <member name="M:Anete.Utils.SysUtils.GetFullMethodName">
            <summary>
            Vraci jmeno plne metody na zadanem ofsetu zasobniku.
            </summary>
            <returns></returns>
            <remarks></remarks>
        </member>
        <member name="M:Anete.Utils.SysUtils.GetFullMethodName(System.Int32)">
            <summary>
            Vraci jmeno plne metody na zadanem ofsetu zasobniku.
            </summary>
            <param name="skipFrames">Ofset v zasobniku. Vetsinou se nastavuje na 1 -
            ignorovat stack metody GetFullMethodName</param>
            <returns></returns>
            <remarks></remarks>
        </member>
        <member name="M:Anete.Utils.SysUtils.GetCurrentMethodName">
            <summary>
            Vraci jmeno plne jmeno prave volane metody
            </summary>
            <returns></returns>
            <remarks></remarks>
        </member>
        <member name="M:Anete.Utils.SysUtils.DisposeAndNull``1(``0@)">
            <summary>
            Zavola Dispose a nastavi referenci na obj na Nothing
            </summary>
            <typeparam name="T"></typeparam>
            <param name="obj"></param>
            <remarks>Volani teto funkce nahrazuje casto opakovanou konstrukci:
            If obj IsNot Nothing Then
               obj.Dispose()
               obj = Nothing
            End If</remarks>
        </member>
        <member name="M:Anete.Utils.SysUtils.CheckArrayLength``1(``0[],System.Int32,System.String)">
            <summary>
            Zkontroluje delku pole a vyvola vyjimku, pokud neodpovida
            </summary>
            <typeparam name="T"></typeparam>
            <param name="arr">Pole</param>
            <param name="length">Zadana delka</param>
            <param name="arrName">Nazev pole, predava se do vyjimky ArchumentOutOfRange</param>
            <remarks></remarks>
        </member>
        <member name="M:Anete.Utils.SysUtils.CheckArrayMinLength``1(``0[],System.Int32,System.String)">
            <summary>
            Zkontroluje delku pole a vyvola vyjimku, pokud neodpovida
            </summary>
            <typeparam name="T"></typeparam>
            <param name="arr">Pole</param>
            <param name="minLength">Minimalni zadana delka</param>
            <param name="arrName">Nazev pole, predava se do vyjimky ArchumentOutOfRange</param>
            <remarks></remarks>
        </member>
        <member name="T:Anete.Utils.FileUtils">
            <summary>
            Utility pro praci se soubory
            </summary>
        </member>
        <member name="M:Anete.Utils.FileUtils.SetReadWriteAttributes(System.String)">
            <summary>
            Vymaze atribut read-only u daneho souboru
            </summary>
            <param name="fileName">Nazev souboru</param>
            <remarks></remarks>
        </member>
        <member name="M:Anete.Utils.FileUtils.DeleteFile(System.String)">
            <summary>
            Smaze soubor i tehdy, kdyz je Read-only
            </summary>
            <param name="fileName"></param>
            <remarks></remarks>
        </member>
        <member name="T:Anete.Utils.ASCIIEncodingNoCZ">
            <summary>
            Třída pro převod znaků s diakritikou na znaky bez diakritiky.
            </summary>
            <remarks>
            Nazpět to samozřejmě nemůže fungovat, takže nazpět dostaneme klasický ASCII.
            </remarks>
        </member>
        <member name="M:Anete.Utils.ASCIIEncodingNoCZ.GetString(System.String)">
            <summary>
            Převod řetězce s českou diakritikou na řetězec bez diakritiky.
            </summary>
            <param name="text">Vstupní řetězec</param>
            <returns>Výstupní řetězec.</returns>
        </member>
        <member name="M:Anete.Utils.ASCIIEncodingNoCZ.GetBytes(System.String,System.Int32,System.Int32,System.Byte[],System.Int32)">
            <summary>
            Encodes the specified range of a String into the specified range of a byte array.
            </summary>
            <param name="s">A String to encode.</param>
            <param name="charIndex">The index of the first character in chars to encode.</param>
            <param name="charCount">The number of characters to encode. </param>
            <param name="bytes">The byte array where the encoding is stored.</param>
            <param name="byteIndex">The index of the first element in bytes where the encoding is stored.</param>
            <returns>The number of encoded bytes in array bytes.</returns>
        </member>
        <member name="M:Anete.Utils.ASCIIEncodingNoCZ.GetBytes(System.Char[],System.Int32,System.Int32,System.Byte[],System.Int32)">
            <summary>
            When overridden in a derived class, encodes a range of characters from a character array into a byte array.
            </summary>
            <param name="chars">The character array to encode.</param>
            <param name="charIndex">The index of the first character in chars to encode.</param>
            <param name="charCount">The number of characters to encode. </param>
            <param name="bytes">The byte array where the encoding is stored.</param>
            <param name="byteIndex">The index of the first element in bytes where the encoding is stored.</param>
            <returns>The number of bytes stored in array bytes.</returns>
        </member>
        <member name="M:Anete.Utils.ASCIIEncodingNoCZ.GetEncoder">
            <summary>
            Return Encoder.
            </summary>
            <returns>Encode ASCIINoCZ.</returns>
        </member>
        <member name="P:Anete.Utils.ASCIIEncodingNoCZ.ASCIINoCZ">
            <summary>
            ASCII NoCzech Encoder.
            </summary>
        </member>
        <member name="P:Anete.Utils.ASCIIEncodingNoCZ.EncodingName">
            <summary>
            Název kódování v 'lidském' tvaru.
            </summary>
        </member>
        <member name="P:Anete.Utils.ASCIIEncodingNoCZ.BodyName">
            <summary>
            Název kódovací stránky pro použití v tagu záhlaví mailovacího agenta.
            </summary>
        </member>
        <member name="P:Anete.Utils.ASCIIEncodingNoCZ.CodePage">
            <summary>
            Číslo kódovací stránky.
            </summary>
            <remarks>
            Identifikuje kódovací stránku. Z nedostatku fantazie volím 0.
            </remarks>
        </member>
        <member name="T:Anete.Utils.ASCIIEncoderNoCZ">
            <summary>
            Encoder pro odstranění diakritiky z českých textů.
            </summary>
        </member>
        <member name="M:Anete.Utils.ASCIIEncoderNoCZ.#ctor">
            <summary>
            Bezparametrový konstruktor.
            </summary>
        </member>
        <member name="M:Anete.Utils.ASCIIEncoderNoCZ.GetByteCount(System.Char[],System.Int32,System.Int32,System.Boolean)">
            <summary>
            Calculates the number of bytes required to encode a specified character array.
            </summary>
            <param name="chars">The character array to encode.</param>
            <param name="index">The starting index of the character array to encode.</param>
            <param name="count">The number of characters to encode. </param>
            <param name="flush">true if this instance can flush its internal state after the calculation; otherwise, false.</param>
            <returns>The number of bytes the next call to GetBytes would produce from encoding the specified range of characters and honoring flush.</returns>
        </member>
        <member name="M:Anete.Utils.ASCIIEncoderNoCZ.GetBytes(System.Char[],System.Int32,System.Int32,System.Byte[],System.Int32,System.Boolean)">
            <summary>
            When overridden in a derived class, encodes a specified range of characters in a character array and stores them 
            in a specified range of a byte array.
            </summary>
            <param name="chars">The character array to encode.</param>
            <param name="charIndex">The index of the first character in chars to encode.</param>
            <param name="charCount">The number of characters to encode. </param>
            <param name="bytes">The byte array where the encoding is stored.</param>
            <param name="byteIndex">The index of the first element in bytes where the encoding is stored.</param>
            <param name="flush">true if this encoder can flush its state at the end of the conversion; 
            otherwise, false. To ensure correct termination of a sequence of blocks of encoded bytes, the last call to GetBytes can specify a value of true for flush. </param>
            <returns>The number of bytes encoded into bytes.</returns>
        </member>
        <member name="T:Anete.Utils.Win32Imports.Kernel32">
            <summary>
            Importy z Kernel32
            </summary>
        </member>
        <member name="T:Anete.Utils.InnoMutexes">
            <summary>
            Trida pro vytvoreni instalacnich mutexu. Pomoci nich detekuje Inno Setup, ze aplikace bezi
            a nemuze tudiz spustit novou instalaci.
            </summary>
            <remarks>
            Vytvari se 2 mutexy: jeden pro aktualni session (bez prefixu), druhy s
            prefixem Global\ slouzi pro detekci bezici aplikace pres vice uzivatelskych session,
            pokud je zapnute Fast user switching</remarks>
        </member>
        <member name="M:Anete.Utils.InnoMutexes.#ctor(System.String)">
            <summary>
            Initializes a new instance of the InnoMutexes class.
            </summary>
            <param name="mutexName">Nazev mutexu</param>
        </member>
        <member name="M:Anete.Utils.InnoMutexes.#ctor">
            <summary>
            Inicializace noveho mutexu s nazvem podle
            </summary>
        </member>
        <member name="M:Anete.Utils.InnoMutexes.DisposeUnmanagedResources">
            <summary>
            Uvolneni nemanagovanych zdroju
            </summary>
        </member>
        <member name="T:Anete.Utils.Exceptions.DbConfigException">
            <summary>
            Chyba databazove konfigurace
            </summary>
        </member>
        <member name="M:Anete.Utils.Exceptions.DbConfigException.#ctor">
            <summary>
            Constructs a new DbConfigException.
            </summary>
        </member>
        <member name="M:Anete.Utils.Exceptions.DbConfigException.#ctor(System.String,System.String)">
            <summary>
            Constructs a new DbConfigException.
            </summary>
            <param name="message">The exception message</param>
            <param name="configKey">The value for the ConfigKey property.</param>
        </member>
        <member name="M:Anete.Utils.Exceptions.DbConfigException.#ctor(System.String,System.String,System.Exception)">
            <summary>
            Constructs a new DbConfigException.
            </summary>
            <param name="message">The exception message.</param>
            <param name="configKey">The value for the ConfigKey property.</param>
            <param name="innerException">The inner exception.</param>
        </member>
        <member name="M:Anete.Utils.Exceptions.DbConfigException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Serialization constructor.
            </summary>
        </member>
        <member name="M:Anete.Utils.Exceptions.DbConfigException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Overridden method from the ISerializable interface, to include the additional fields in serialization.
            </summary>
        </member>
        <member name="P:Anete.Utils.Exceptions.DbConfigException.Message">
            <summary>
            Overridden property from System.Exception, to include the additional fields in the message.
            </summary>
        </member>
        <member name="P:Anete.Utils.Exceptions.DbConfigException.ConfigKey">
             <summary>
            
             </summary>
        </member>
        <member name="T:Anete.Utils.ConfigUpdater">
            <summary>
            Aktualizace nastaveni v app.Config, odpovidajici Settings.
            </summary>
            <remarks>Pouziva se pri instalaci programu pro pocatecni modifikaci app.config
            Pozor pokud se pouzije z prostredi a ne z EXE tak se zmeny provedou pouze
            v spoboru Kasa8.vshost.exe.config, ktery je ale po ukonceni prepsan
            kasa8.config takze o zmeny se ihned prijde.</remarks>
        </member>
        <member name="M:Anete.Utils.ConfigUpdater.#ctor(System.String)">
            <summary>
            Initializes a new instance of the configUpdater class
            </summary>
            <param name="aSectionName">Jmeno sekce, obycejne My.Settings</param>
            <remarks>Otevre urcenou sekci z apllicattionSettings pomoci ConfigurationManageru
            a do elements nacte jednotlive settings elementy.  </remarks>
        </member>
        <member name="M:Anete.Utils.ConfigUpdater.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the configUpdater class
            </summary>
            <param name="aSectionName">Jmeno sekce, obycejne My.MySettings</param>
            <param name="aGroupName">Jmeno skupiny, pro aplikacni nastaveni "applicationSettings",
            pro user nastaveni "userSettings"</param>
            <remarks >Otevre urcenou sekci pomoci ConfigurationManageru a do elements nacte
            jednotlive settings elementy.  </remarks>
        </member>
        <member name="M:Anete.Utils.ConfigUpdater.#ctor(System.String,System.String,System.String)">
            <summary>
            Initializes a new instance of the configUpdater class
            </summary>
            <param name="aSectionName">Jmeno sekce, obycejne My.Settings</param>
            <param name="aGroupName">Jmeno skupiny, pro aplikacni nastaveni "applicationSettings",
            pro user nastaveni "userSettings"</param>
            <param name="filePath">Jmeno .config souboru</param>
            <remarks >Otevre urcenou sekci pomoci ConfigurationManageru a do elements nacte
            jednotlive settings elementy.  </remarks>
        </member>
        <member name="M:Anete.Utils.ConfigUpdater.GetSettingsElement(System.String)">
            <summary>
            Vraci element s danym nazvem. Pri nenalezeni vyvola vyjimku
            </summary>
            <param name="name">Nazev elementu</param>
            <returns>Nalezeny element</returns>
            <remarks></remarks>
        </member>
        <member name="M:Anete.Utils.ConfigUpdater.FindSettingsElement(System.String)">
            <summary>
            Vraci element s danym nazvem nebo Nothing
            </summary>
            <param name="name">Nazev elementu</param>
            <returns>Nalezeny element nebo Nothing pokud element neexistuje</returns>
            <remarks></remarks>
        </member>
        <member name="M:Anete.Utils.ConfigUpdater.GetSettingsValue(System.String)">
            <summary>
            Vraci hodnotu pro dany element
            </summary>
            <param name="name">Nazev elementu</param>
            <returns>Hodnotu elementu</returns>
            <remarks></remarks>
        </member>
        <member name="M:Anete.Utils.ConfigUpdater.GetSettingsValueBool(System.String)">
            <summary>
            Vraci hodnotu pro dany element
            </summary>
            <param name="name">Nazev elementu</param>
            <returns>Hodnota elementu</returns>
            <remarks></remarks>
        </member>
        <member name="M:Anete.Utils.ConfigUpdater.GetSettingsValueInt(System.String)">
            <summary>
            Vraci hodnotu pro dany element
            </summary>
            <param name="name">Nazev elementu</param>
            <returns>Hodnota elementu</returns>
            <remarks></remarks>
        </member>
        <member name="M:Anete.Utils.ConfigUpdater.GetSettingsValueDec(System.String)">
            <summary>
            Vraci hodnotu pro dany element
            </summary>
            <param name="name">Nazev elementu</param>
            <returns>Hodnota elementu</returns>
            <remarks></remarks>
        </member>
        <member name="M:Anete.Utils.ConfigUpdater.SetSettingsValue(System.String,System.String)">
            <summary>
            Nastavi hodnotu pro dany element
            </summary>
            <param name="name"></param>
            <param name="value"></param>
            <remarks>Postupne prochazi vsechny elemnty a pro danou sekci
            a pokud jej najde zmeni hodnotu InnerText na sectionValue</remarks>
        </member>
        <member name="M:Anete.Utils.ConfigUpdater.SetSettingsValue(System.String,System.Int32)">
            <summary>
            Nastavi hodnotu pro dany element
            </summary>
            <param name="name"></param>
            <param name="value"></param>
            <remarks>Postupne prochazi vsechny elemnty a pro danou sekci
            a pokud jej najde zmeni hodnotu InnerText na sectionValue</remarks>
        </member>
        <member name="M:Anete.Utils.ConfigUpdater.SetSettingsValue(System.String,System.Decimal)">
            <summary>
            Nastavi hodnotu pro dany element
            </summary>
            <param name="name"></param>
            <param name="value"></param>
            <remarks>Postupne prochazi vsechny elemnty a pro danou sekci
            a pokud jej najde zmeni hodnotu InnerText na sectionValue</remarks>
        </member>
        <member name="M:Anete.Utils.ConfigUpdater.SetSettingsValue(System.String,System.Boolean)">
            <summary>
            Nastavi hodnotu pro dany element
            </summary>
            <param name="name"></param>
            <param name="value"></param>
            <remarks>Postupne prochazi vsechny elemnty a pro danou sekci
            a pokud jej najde zmeni hodnotu InnerText na sectionValue</remarks>
        </member>
        <member name="M:Anete.Utils.ConfigUpdater.SetConnStrValue(System.String,System.String)">
            <summary>
            Nastavi hodnotu pro connStr
            </summary>
            <param name="connStrName"></param>
            <param name="value"></param>
            <remarks>Obdoba SetSectionValue pro connectionStrings</remarks>
        </member>
        <member name="M:Anete.Utils.ConfigUpdater.SaveConfig">
            <summary>
            Ulozeni zmen v configu do souboru
             </summary>
             <remarks>Nastavi ForceSave pro danou section a provede Full save.</remarks>
        </member>
        <member name="P:Anete.Utils.ConfigUpdater.SectionName">
            <summary>
            Jmeno sekce, obycejne My.Settings
            </summary>
            <value></value>
            <returns></returns>
            <remarks></remarks>
        </member>
        <member name="T:Anete.Utils.CommandLine.ArgumentType">
            <summary>
            Used to control parsing of command line arguments.
            </summary>
        </member>
        <member name="F:Anete.Utils.CommandLine.ArgumentType.Required">
            <summary>
            Indicates that this field is required. An error will be displayed
            if it is not present when parsing arguments.
            </summary>
        </member>
        <member name="F:Anete.Utils.CommandLine.ArgumentType.Unique">
            <summary>
            Only valid in conjunction with Multiple.
            Duplicate values will result in an error.
            </summary>
        </member>
        <member name="F:Anete.Utils.CommandLine.ArgumentType.Multiple">
            <summary>
            Inidicates that the argument may be specified more than once.
            Only valid if the argument is a collection
            </summary>
        </member>
        <member name="F:Anete.Utils.CommandLine.ArgumentType.AtMostOnce">
            <summary>
            The default type for non-collection arguments.
            The argument is not required, but an error will be reported if it is specified more than once.
            </summary>
        </member>
        <member name="F:Anete.Utils.CommandLine.ArgumentType.LastOccurenceWins">
            <summary>
            For non-collection arguments, when the argument is specified more than
            once no error is reported and the value of the argument is the last
            value which occurs in the argument list.
            </summary>
        </member>
        <member name="F:Anete.Utils.CommandLine.ArgumentType.MultipleUnique">
            <summary>
            The default type for collection arguments.
            The argument is permitted to occur multiple times, but duplicate 
            values will cause an error to be reported.
            </summary>
        </member>
        <member name="T:Anete.Utils.CommandLine.ArgumentAttribute">
            <summary>
            Allows control of command line parsing.
            Attach this attribute to instance fields of types used
            as the destination of command line argument parsing.
            </summary>
        </member>
        <member name="M:Anete.Utils.CommandLine.ArgumentAttribute.#ctor(Anete.Utils.CommandLine.ArgumentType)">
            <summary>
            Allows control of command line parsing.
            </summary>
            <param name="type"> Specifies the error checking to be done on the argument. </param>
        </member>
        <member name="P:Anete.Utils.CommandLine.ArgumentAttribute.Type">
            <summary>
            The error checking to be done on the argument.
            </summary>
        </member>
        <member name="P:Anete.Utils.CommandLine.ArgumentAttribute.DefaultShortName">
            <summary>
            Returns true if the argument did not have an explicit short name specified.
            </summary>
        </member>
        <member name="P:Anete.Utils.CommandLine.ArgumentAttribute.ShortName">
            <summary>
            The short name of the argument.
            Set to null means use the default short name if it does not
            conflict with any other parameter name.
            Set to String.Empty for no short name.
            This property should not be set for DefaultArgumentAttributes.
            </summary>
        </member>
        <member name="P:Anete.Utils.CommandLine.ArgumentAttribute.DefaultLongName">
            <summary>
            Returns true if the argument did not have an explicit long name specified.
            </summary>
        </member>
        <member name="P:Anete.Utils.CommandLine.ArgumentAttribute.LongName">
            <summary>
            The long name of the argument.
            Set to null means use the default long name.
            The long name for every argument must be unique.
            It is an error to specify a long name of String.Empty.
            </summary>
        </member>
        <member name="P:Anete.Utils.CommandLine.ArgumentAttribute.DefaultValue">
            <summary>
            The default value of the argument.
            </summary>
        </member>
        <member name="P:Anete.Utils.CommandLine.ArgumentAttribute.HasDefaultValue">
            <summary>
            Returns true if the argument has a default value.
            </summary>
        </member>
        <member name="P:Anete.Utils.CommandLine.ArgumentAttribute.HasHelpText">
            <summary>
            Returns true if the argument has help text specified.
            </summary>
        </member>
        <member name="P:Anete.Utils.CommandLine.ArgumentAttribute.HelpText">
            <summary>
            The help text for the argument.
            </summary>
        </member>
        <member name="T:Anete.Utils.CommandLine.DefaultArgumentAttribute">
            <summary>
            Indicates that this argument is the default argument.
            '/' or '-' prefix only the argument value is specified.
            The ShortName property should not be set for DefaultArgumentAttribute
            instances. The LongName property is used for usage text only and
            does not affect the usage of the argument.
            </summary>
        </member>
        <member name="M:Anete.Utils.CommandLine.DefaultArgumentAttribute.#ctor(Anete.Utils.CommandLine.ArgumentType)">
            <summary>
            Indicates that this argument is the default argument.
            </summary>
            <param name="type"> Specifies the error checking to be done on the argument. </param>
        </member>
        <member name="T:Anete.Utils.CommandLine.ErrorReporter">
            <summary>
            A delegate used in error reporting.
            </summary>
        </member>
        <member name="T:Anete.Utils.CommandLine.Parser">
             <summary>
             Parser for command line arguments.
            
             The parser specification is infered from the instance fields of the object
             specified as the destination of the parse.
             Valid argument types are: int, uint, string, bool, enums
             Also argument types of Array of the above types are also valid.
             
             Error checking options can be controlled by adding a ArgumentAttribute
             to the instance fields of the destination object.
            
             At most one field may be marked with the DefaultArgumentAttribute
             indicating that arguments without a '-' or '/' prefix will be parsed as that argument.
            
             If not specified then the parser will infer default options for parsing each
             instance field. The default long name of the argument is the field name. The
             default short name is the first character of the long name. Long names and explicitly
             specified short names must be unique. Default short names will be used provided that
             the default short name does not conflict with a long name or an explicitly
             specified short name.
            
             Arguments which are array types are collection arguments. Collection
             arguments can be specified multiple times.
             </summary>
        </member>
        <member name="F:Anete.Utils.CommandLine.Parser.NewLine">
            <summary>
            The System Defined new line string.
            </summary>
        </member>
        <member name="M:Anete.Utils.CommandLine.Parser.#ctor">
            <summary>
            Don't ever call this.
            </summary>
        </member>
        <member name="M:Anete.Utils.CommandLine.Parser.ParseArgumentsWithUsage(System.String[],System.Object)">
            <summary>
            Parses Command Line Arguments. Displays usage message to Console.Out
            if /?, /help or invalid arguments are encounterd.
            Errors are output on Console.Error.
            Use ArgumentAttributes to control parsing behaviour.
            </summary>
            <param name="arguments"> The actual arguments. </param>
            <param name="destination"> The resulting parsed arguments. </param>
            <returns> true if no errors were detected. </returns>
        </member>
        <member name="M:Anete.Utils.CommandLine.Parser.ParseArguments(System.String[],System.Object)">
            <summary>
            Parses Command Line Arguments. 
            Errors are output on Console.Error.
            Use ArgumentAttributes to control parsing behaviour.
            </summary>
            <param name="arguments"> The actual arguments. </param>
            <param name="destination"> The resulting parsed arguments. </param>
            <returns> true if no errors were detected. </returns>
        </member>
        <member name="M:Anete.Utils.CommandLine.Parser.ParseArguments(System.String[],System.Object,Anete.Utils.CommandLine.ErrorReporter)">
            <summary>
            Parses Command Line Arguments. 
            Use ArgumentAttributes to control parsing behaviour.
            </summary>
            <param name="arguments"> The actual arguments. </param>
            <param name="destination"> The resulting parsed arguments. </param>
            <param name="reporter"> The destination for parse errors. </param>
            <returns> true if no errors were detected. </returns>
        </member>
        <member name="M:Anete.Utils.CommandLine.Parser.ParseHelp(System.String[])">
            <summary>
            Checks if a set of arguments asks for help.
            </summary>
            <param name="args"> Args to check for help. </param>
            <returns> Returns true if args contains /? or /help. </returns>
        </member>
        <member name="M:Anete.Utils.CommandLine.Parser.ArgumentsUsage(System.Type)">
            <summary>
            Returns a Usage string for command line argument parsing.
            Use ArgumentAttributes to control parsing behaviour.
            Formats the output to the width of the current console window.
            </summary>
            <param name="argumentType"> The type of the arguments to display usage for. </param>
            <returns> Printable string containing a user friendly description of command line arguments. </returns>
        </member>
        <member name="M:Anete.Utils.CommandLine.Parser.ArgumentsUsage(System.Type,System.Int32)">
            <summary>
            Returns a Usage string for command line argument parsing.
            Use ArgumentAttributes to control parsing behaviour.
            </summary>
            <param name="argumentType"> The type of the arguments to display usage for. </param>
            <param name="columns"> The number of columns to format the output to. </param>
            <returns> Printable string containing a user friendly description of command line arguments. </returns>
        </member>
        <member name="M:Anete.Utils.CommandLine.Parser.GetConsoleWindowWidth">
            <summary>
            Returns the number of columns in the current console window
            </summary>
            <returns>Returns the number of columns in the current console window</returns>
        </member>
        <member name="M:Anete.Utils.CommandLine.Parser.IndexOf(System.Text.StringBuilder,System.Char,System.Int32)">
            <summary>
            Searches a StringBuilder for a character
            </summary>
            <param name="text"> The text to search. </param>
            <param name="value"> The character value to search for. </param>
            <param name="startIndex"> The index to stat searching at. </param>
            <returns> The index of the first occurence of value or -1 if it is not found. </returns>
        </member>
        <member name="M:Anete.Utils.CommandLine.Parser.LastIndexOf(System.Text.StringBuilder,System.Char,System.Int32)">
            <summary>
            Searches a StringBuilder for a character in reverse
            </summary>
            <param name="text"> The text to search. </param>
            <param name="value"> The character to search for. </param>
            <param name="startIndex"> The index to start the search at. </param>
            <returns>The index of the last occurence of value in text or -1 if it is not found. </returns>
        </member>
        <member name="M:Anete.Utils.CommandLine.Parser.#ctor(System.Type,Anete.Utils.CommandLine.ErrorReporter)">
            <summary>
            Creates a new command line argument parser.
            </summary>
            <param name="argumentSpecification"> The type of object to  parse. </param>
            <param name="reporter"> The destination for parse errors. </param>
        </member>
        <member name="M:Anete.Utils.CommandLine.Parser.ParseArgumentList(System.String[],System.Object)">
            <summary>
            Parses an argument list into an object
            </summary>
            <param name="args"></param>
            <param name="destination"></param>
            <returns> true if an error occurred </returns>
        </member>
        <member name="M:Anete.Utils.CommandLine.Parser.Parse(System.String[],System.Object)">
            <summary>
            Parses an argument list.
            </summary>
            <param name="args"> The arguments to parse. </param>
            <param name="destination"> The destination of the parsed arguments. </param>
            <returns> true if no parse errors were encountered. </returns>
        </member>
        <member name="M:Anete.Utils.CommandLine.Parser.GetUsageString(System.Int32)">
            <summary>
            A user firendly usage string describing the command line argument syntax.
            </summary>
        </member>
        <member name="P:Anete.Utils.CommandLine.Parser.HasDefaultArgument">
            <summary>
            Does this parser have a default argument.
            </summary>
            <value> Does this parser have a default argument. </value>
        </member>
        <member name="T:Anete.Utils.EnumHelper">
            <summary>
            Trida pro praci s Enumy
            </summary>
            <remarks>Pochazi z http://blogs.msdn.com/abhinaba/archive/2005/10/20/483000.aspx
            uxCoolValueComboBox = EnumHelper.GetList(TypeOf(CoolValue))
            </remarks>
        </member>
        <member name="M:Anete.Utils.EnumHelper.GetDescription(System.Enum)">
            <summary>
            Vraci popis k Enumu zadany pomoci atributu Description
            </summary>
            <param name="en"></param>
            <returns></returns>
        </member>
        <member name="M:Anete.Utils.EnumHelper.GetList(System.Type)">
            <summary>
            Vraci kolekci popisu k enumu
            </summary>
            <param name="enumType"></param>
            <returns></returns>
        </member>
        <member name="T:Anete.Utils.Exceptions.ResourceNotFoundException">
            <summary>
            vyjimka pri nenalezeni resource
            </summary>
        </member>
        <member name="M:Anete.Utils.Exceptions.ResourceNotFoundException.#ctor">
            <summary>
            Constructs a new ResourceNotFoundException.
            </summary>
        </member>
        <member name="M:Anete.Utils.Exceptions.ResourceNotFoundException.#ctor(System.String)">
            <summary>
            Constructs a new ResourceNotFoundException.
            </summary>
            <param name="message">The exception message</param>
        </member>
        <member name="M:Anete.Utils.Exceptions.ResourceNotFoundException.#ctor(System.String,System.Exception)">
            <summary>
            Constructs a new ResourceNotFoundException.
            </summary>
            <param name="message">The exception message</param>
            <param name="innerException">The inner exception</param>
        </member>
        <member name="M:Anete.Utils.Exceptions.ResourceNotFoundException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Serialization constructor.
            </summary>
        </member>
        <member name="T:Anete.Utils.Log4Net.Log4NetConfigFileUpdater">
            <summary>
            Aktualizace nastaveni v app.log4net.config, pracuje primo s konfiguracnim souborem
            </summary>
            <remarks>
            </remarks>
        </member>
        <member name="T:Anete.Utils.Log4Net.Log4NetXmlDocConfigUpdater">
            <summary>
            Aktualizace nastaveni v app.log4net.config, Xml konfigurace se predava jako dokument
            nacteny do pameti.
            </summary>
            <remarks>
            </remarks>
        </member>
        <member name="F:Anete.Utils.Log4Net.Log4NetXmlDocConfigUpdater.mConfigXmlDoc">
            <summary>
            Konfiguracni soubor log4Net nacteny do pameti jako Xml dokument
            </summary>
            <remarks></remarks>
        </member>
        <member name="M:Anete.Utils.Log4Net.Log4NetXmlDocConfigUpdater.#ctor(System.Xml.XmlDocument)">
            <summary>
            Initializes a new instance of the Log4NetXmlDocConfigUpdater class.
            </summary>
            <param name="configXmlDoc">log4net.config nacteny do XmlDocument</param>
        </member>
        <member name="M:Anete.Utils.Log4Net.Log4NetXmlDocConfigUpdater.SetConnectionString(System.String,System.String)">
            <summary>
            Aktualizace nastaveni connection stringu u konkretniho appenderu
            </summary>
            <param name="appenderName">Nazev appenderu</param>
            <param name="connectionString">Nove nastavovany connection string</param>
            <remarks>Pri nenalezeni appenderu vyvola vyjimku ArgumentException</remarks>
        </member>
        <member name="M:Anete.Utils.Log4Net.Log4NetXmlDocConfigUpdater.UpdateAppenderFileDirs(System.String)">
            <summary>
            Projde konfiguraci a u vsech fileAppenderu nastavi cestu na dirName
            </summary>
            <param name="dirName">Nazev noveho adresare</param>
            <remarks>Pro nazev souboru pouzije ten, ktery je uvedeny v .config, jen u neho zmeni cestu</remarks>
        </member>
        <member name="P:Anete.Utils.Log4Net.Log4NetXmlDocConfigUpdater.ConfigXmlDoc">
            <summary>
            Konfiguracni soubor log4Net nacteny do pameti jako Xml dokument
            </summary>
            <value></value>
            <returns></returns>
            <remarks></remarks>
        </member>
        <member name="M:Anete.Utils.Log4Net.Log4NetConfigFileUpdater.#ctor(System.String)">
            <summary>
            Initializes a new instance of the Log4NetConfigUpdater class.
            </summary>
            <param name="configFileName">Plny nazev souboru</param>
        </member>
        <member name="M:Anete.Utils.Log4Net.Log4NetConfigFileUpdater.Save">
            <summary>
            Ulozeni zmen do XML souboru
             </summary>
             <remarks></remarks>
        </member>
        <member name="T:Anete.Utils.XmlUtils">
            <summary>
            
            </summary>
        </member>
        <member name="M:Anete.Utils.XmlUtils.GetXmlNodePath(System.Xml.XmlNode)">
            <summary>
            K danemu uzlu vraci celou cestu
            </summary>
            <param name="node">Xml uzel</param>
            <returns>Cela cesta k uzlu</returns>
            <remarks></remarks>
        </member>
        <member name="T:Anete.Utils.StringArrHelper">
            <summary>
            Pomocne operace s poli
            </summary>
        </member>
        <member name="M:Anete.Utils.StringArrHelper.Append(System.String[],System.String[])">
            <summary>
            Pripoji k poli array1 druhe pole a vraci vysledek
            </summary>
            <param name="array1">Pole</param>
            <param name="arrayToAppend">Pole k pripojeni</param>
            <returns>Spojena pole</returns>
        </member>
        <member name="T:Anete.Utils.MathUtils">
            <summary>
            matematicke utility
            </summary>
            <remarks></remarks>
        </member>
        <member name="M:Anete.Utils.MathUtils.IsFraction(System.Double)">
            <summary>
            vraci true, pokud je number desetinne cislo
            </summary>
            <param name="number"></param>
            <returns></returns>
            <remarks></remarks>
        </member>
        <member name="M:Anete.Utils.MathUtils.IsFraction(System.Decimal)">
            <summary>
            vraci true, pokud je number desetinne cislo
            </summary>
            <param name="number"></param>
            <returns></returns>
            <remarks></remarks>
        </member>
        <member name="M:Anete.Utils.MathUtils.IsZero(System.Decimal)">
            <summary>
            Vraci true, pokud je number po zaokrouhleni na dany pocet mist nulove
            </summary>
            <param name="number"></param>
            <remarks></remarks>
        </member>
        <member name="M:Anete.Utils.MathUtils.IsZero(System.Decimal,System.Int32)">
            <summary>
            Vraci true, pokud je number po zaokrouhleni na dany pocet mist nulove
            </summary>
            <param name="number"></param>
            <param name="decimals"></param>
            <remarks></remarks>
        </member>
        <member name="T:Anete.Utils.Exceptions.FormValidateException">
            <summary>
            Exception vyvolavana pri chybe validace formulare
            </summary>
        </member>
        <member name="M:Anete.Utils.Exceptions.FormValidateException.#ctor">
            <summary>
            Constructs a new FormValidateException.
            </summary>
        </member>
        <member name="M:Anete.Utils.Exceptions.FormValidateException.#ctor(System.String)">
            <summary>
            Constructs a new FormValidateException.
            </summary>
            <param name="message">The exception message</param>
        </member>
        <member name="M:Anete.Utils.Exceptions.FormValidateException.#ctor(System.String,System.Exception)">
            <summary>
            Constructs a new FormValidateException.
            </summary>
            <param name="message">The exception message</param>
            <param name="innerException">The inner exception</param>
        </member>
        <member name="M:Anete.Utils.Exceptions.FormValidateException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Serialization constructor.
            </summary>
        </member>
        <member name="T:DbUtils">
            <summary>
            utility pro databazove operace
            </summary>
            <remarks></remarks>
        </member>
        <member name="M:DbUtils.CheckExpectedRowCount(System.Data.DataRow[],System.Int32,System.String)">
            <summary>
            zkontroluje, zda rows obsahuji ocekavany pocet radku. Pokud ne, vyvola vyjimku
            </summary>
            <param name="rows"></param>
            <param name="expectedCount">Očekávaný počet</param>
            <param name="selectStr">databazovy dotaz</param>
            <remarks></remarks>
        </member>
        <member name="M:DbUtils.CheckExpectedRowCount(System.Data.DataTable,System.Int32)">
            <summary>
            zkontroluje, zda rows obsahuji ocekavany pocet radku. Pokud ne, vyvola vyjimku
            </summary>
            <param name="table">Tabulka</param>
            <param name="expectedCount">Očekávaný počet</param>
            <remarks></remarks>
        </member>
        <member name="M:DbUtils.CheckMaxRowCount(System.Data.DataTable,System.Int32)">
            <summary>
            zkontroluje, zda rows obsahuji maximalne ocekavany pocet radku. Pokud ne, vyvola vyjimku
            </summary>
            <param name="table">Tabulka</param>
            <param name="maxCount">Maximalni počet řádků</param>
            <remarks></remarks>
        </member>
        <member name="M:DbUtils.DbToDec(System.Object)">
            <summary>
            Pokud value neni DbNull, vraci jeho hodnotu, jinak 0
            </summary>
            <param name="value"></param>
            <returns></returns>
            <remarks>Pouziva se zejmena v pripade, kdy databazove hodnoty vraci Null a ja je chci jako
            cislo</remarks>
        </member>
        <member name="M:DbUtils.DbToDbl(System.Object)">
            <summary>
            Pokud value neni DbNull, vraci jeho hodnotu, jinak 0
            </summary>
            <param name="value"></param>
            <returns></returns>
            <remarks>Pouziva se zejmena v pripade, kdy databazove hodnoty vraci Null a ja je chci jako
            cislo</remarks>
        </member>
        <member name="M:DbUtils.DbToInt(System.Object)">
            <summary>
            Pokud value neni DbNull, vraci jeho hodnotu, jinak 0
            </summary>
            <param name="value"></param>
            <returns></returns>
            <remarks>Pouziva se zejmena v pripade, kdy databazove hodnoty vraci Null a ja je chci jako
            cislo</remarks>
        </member>
        <member name="M:DbUtils.DbToByte(System.Object)">
            <summary>
            Pokud value neni DbNull, vraci jeho hodnotu, jinak 0
            </summary>
            <param name="value"></param>
            <returns></returns>
            <remarks>Pouziva se zejmena v pripade, kdy databazove hodnoty vraci Null a ja je chci jako
            cislo</remarks>
        </member>
        <member name="M:DbUtils.DbToBool(System.Object)">
            <summary>
            Pokud value neni DbNull, vraci jeho hodnotu, jinak 0
            </summary>
            <param name="value"></param>
            <returns></returns>
            <remarks>Pouziva se zejmena v pripade, kdy databazove hodnoty vraci Null a ja je chci jako
            hodnotu boolean</remarks>
        </member>
        <member name="T:Anete.Utils.UserFileAccess">
            <summary>
            Trida pro testovani pristupovych prav k adresarum
            </summary>
            <remarks>
            Pochazi z http://www.codeproject.com/useritems/UserFileAccessRights.asp
            </remarks>
        </member>
        <member name="M:Anete.Utils.UserFileAccess.#ctor(System.String)">
            
            Convenience constructor assumes the current user
            
            
        </member>
        <member name="M:Anete.Utils.UserFileAccess.#ctor(System.String,System.Security.Principal.WindowsIdentity)">
            
            Supply the path to the file or directory and a user or group. Access checks are done
            during instanciation to ensure we always have a valid object
            
            
            
        </member>
        <member name="M:Anete.Utils.UserFileAccess.ToString">
            <summary>
            Prevede na string
            </summary>
            <returns></returns>
        </member>
        <member name="T:Anete.Utils.Exceptions.DbKeyNotFoundException">
            <summary>
            Exception vyvolana pri nenalezeni databazoveho klice
            </summary>
        </member>
        <member name="T:Anete.Utils.Exceptions.DbException">
            <summary>
            Exception vyvolana pri obecne chyne databaze
            </summary>
        </member>
        <member name="M:Anete.Utils.Exceptions.DbException.#ctor">
            <summary>
            Constructs a new DbKeyNotFoundException.
            </summary>
        </member>
        <member name="M:Anete.Utils.Exceptions.DbException.#ctor(System.String,System.String)">
            <summary>
            Konstruktor
            </summary>
            <param name="message">Zprava vyjimky</param>
            <param name="dataTableName">Jmeno datove tabulky</param>
        </member>
        <member name="M:Anete.Utils.Exceptions.DbException.#ctor(System.String,System.String,System.Exception)">
            <summary>
            Constructs a new DbKeyNotFoundException.
            </summary>
            <param name="message">The exception message.</param>
            <param name="dataTableName">Jmeno datove tabulky</param>
            <param name="innerException">The inner exception.</param>
        </member>
        <member name="M:Anete.Utils.Exceptions.DbException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Serialization constructor.
            </summary>
        </member>
        <member name="M:Anete.Utils.Exceptions.DbException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Overridden method from the ISerializable interface, to include the additional fields in serialization.
            </summary>
        </member>
        <member name="P:Anete.Utils.Exceptions.DbException.Message">
            <summary>
            Overridden property from System.Exception, to include the additional fields in the message.
            </summary>
        </member>
        <member name="P:Anete.Utils.Exceptions.DbException.DataTableName">
            <summary>
            Nazev tabulky
            </summary>
            <value></value>
            <returns></returns>
            <remarks></remarks>
        </member>
        <member name="M:Anete.Utils.Exceptions.DbKeyNotFoundException.#ctor">
            <summary>
            Constructs a new DbKeyNotFoundException.
            </summary>
        </member>
        <member name="M:Anete.Utils.Exceptions.DbKeyNotFoundException.#ctor(System.String,System.Object,System.String)">
            <summary>
            Constructs a new DbKeyNotFoundException.
            </summary>
            <param name="message">The exception message</param>
            <param name="key">The value for the Key property.</param>
            <param name="dataTableName">Nazev tabulky</param>
        </member>
        <member name="M:Anete.Utils.Exceptions.DbKeyNotFoundException.#ctor(System.String,System.Object,System.String,System.Exception)">
            <summary>
            Constructs a new DbKeyNotFoundException.
            </summary>
            <param name="message">The exception message.</param>
            <param name="key">The value for the Key property.</param>
            <param name="innerException">The inner exception.</param>
            <param name="dataTableName">Nazev tabulky</param>
        </member>
        <member name="M:Anete.Utils.Exceptions.DbKeyNotFoundException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Serialization constructor.
            </summary>
        </member>
        <member name="M:Anete.Utils.Exceptions.DbKeyNotFoundException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Overridden method from the ISerializable interface, to include the additional fields in serialization.
            </summary>
        </member>
        <member name="P:Anete.Utils.Exceptions.DbKeyNotFoundException.Message">
            <summary>
            Overridden property from System.Exception, to include the additional fields in the message.
            </summary>
        </member>
        <member name="P:Anete.Utils.Exceptions.DbKeyNotFoundException.Key">
            <summary>
            Hodnota nenalezeneho databazoveho klice
            </summary>
        </member>
        <member name="T:Anete.Utils.Exceptions.MustOverrideException">
            <summary>
            Exception vyvolavana v pripadech, kdy nelze vytvorit abstraktni tridu (napr. WinForms) a
            ne nutno donutit uzivatele, aby udelal Override
            </summary>
        </member>
        <member name="M:Anete.Utils.Exceptions.MustOverrideException.#ctor">
            <summary>
            Constructs a new MustOverrideException.
            </summary>
        </member>
        <member name="M:Anete.Utils.Exceptions.MustOverrideException.#ctor(System.String)">
            <summary>
            Constructs a new MustOverrideException.
            </summary>
            <param name="message">The exception message</param>
        </member>
        <member name="M:Anete.Utils.Exceptions.MustOverrideException.#ctor(System.String,System.Exception)">
            <summary>
            Constructs a new MustOverrideException.
            </summary>
            <param name="message">The exception message</param>
            <param name="innerException">The inner exception</param>
        </member>
        <member name="M:Anete.Utils.Exceptions.MustOverrideException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Serialization constructor.
            </summary>
        </member>
        <member name="T:Anete.Utils.ExcUtils">
            <summary>
            Utility pro praci s exceptions
            </summary>
            <remarks></remarks>
        </member>
        <member name="F:Anete.Utils.ExcUtils.mStackOverflowType">
            <summary>
            Ulozene typy specifickych exceptions
            </summary>
            <remarks>Zde ukladam typy, abych pak zvysil vykon pri opakovanych dotazech. Okoukano z
            ADONet.ADP</remarks>
        </member>
        <member name="M:Anete.Utils.ExcUtils.#cctor">
            <summary>
            Initializes a new instance of the ExcUtils class.
            </summary>
        </member>
        <member name="M:Anete.Utils.ExcUtils.ArgumentOutOfRange(System.String,System.Object,System.String)">
            <summary>
            Vyvola vyjimku ArgumentOutOfRangeException
            </summary>
            <param name="paramName">Nazev parametru</param>
            <param name="actualValue">Aktualni hodnota</param>
            <param name="msg">Hlaseni ke zobrazeni</param>
            <remarks></remarks>
        </member>
        <member name="M:Anete.Utils.ExcUtils.ArgumentOutOfRange(System.String,System.Object)">
            <summary>
            Vyvola vyjimku ArgumentOutOfRangeException
            </summary>
            <param name="paramName">Nazev parametru</param>
            <param name="actualValue">Aktualni hodnota</param>
            <remarks></remarks>
        </member>
        <member name="M:Anete.Utils.ExcUtils.ArgumentOutOfRange(System.String)">
            <summary>
            Vraci vyjimku ArgumentOutOfRangeException
            </summary>
            <param name="paramName">Nazev parametru</param>
            <remarks></remarks>
        </member>
        <member name="M:Anete.Utils.ExcUtils.ArgumentNull(System.String)">
            <summary>
            Vraci vyjimku ArgumentNullException
            </summary>
            <param name="paramName">Nazev parametru</param>
            <remarks></remarks>
        </member>
        <member name="M:Anete.Utils.ExcUtils.CommandLine(System.String)">
            <summary>
            Vraci vyjimku CommandLineException
            </summary>
            <param name="message">Hlaseni</param>
            <remarks>Hlaseni je zformatovano do stringu "Chyba pri zpracovani parametru"...</remarks>
        </member>
        <member name="M:Anete.Utils.ExcUtils.DbKeyNotFound(System.Object,System.String)">
            <summary>
            Pri nenalezeni klice v databazi
            </summary>
            <param name="key"></param>
            <param name="tableName"></param>
            <returns></returns>
            <remarks></remarks>
        </member>
        <member name="M:Anete.Utils.ExcUtils.NotImplemented">
            <summary>
            Funkce mela byt implementovana, ale neni.
            </summary>
            <remarks></remarks>
        </member>
        <member name="M:Anete.Utils.ExcUtils.MustOverride">
            <summary>
            Funkce musi byt predefinovana u nasledniku
            </summary>
            <remarks></remarks>
        </member>
        <member name="M:Anete.Utils.ExcUtils.XmlAttributeNotFound(System.String,System.Xml.XmlNode)">
            <summary>
            Xml atribut nebyl nalezen
            </summary>
            <param name="attrName">Nazev atributu</param>
            <param name="node">Uzel, ve kterem atribut chybi</param>
            <remarks></remarks>
        </member>
        <member name="M:Anete.Utils.ExcUtils.IsCatchableExceptionType(System.Exception)">
            <summary>
            Vraci true, pokud vyjimka neni jedna ze systemovych vyjimek
            </summary>
            <param name="e"></param>
            <returns></returns>
            <remarks>Z disasembly objektu System.Data.Common.ADP.
            Hodi se napr. tehdy, kdyz chci zachytavat opravdu vsechny vyjimky ale ne systemove.</remarks>
        </member>
        <member name="M:Anete.Utils.ExcUtils.BuildExceptionMsg(System.Exception)">
            <summary>
            Vyextrahuje zpravu vcetne zprav vsech InnerExceptions
            </summary>
            <param name="exceptionObject"></param>
            <returns></returns>
            <remarks></remarks>
        </member>
        <member name="M:Anete.Utils.ExcUtils.BuildExceptionMsg(System.Exception,System.String)">
            <summary>
            Vyextrahuje zpravu vcetne zprav vsech InnerExceptions
            </summary>
            <param name="exceptionObject"></param>
            <param name="delimiter"></param>
            <returns></returns>
            <remarks></remarks>
        </member>
        <member name="T:Anete.Utils.SysInfo">
            <summary>
            
            </summary>
        </member>
        <member name="M:Anete.Utils.SysInfo.CurrentWindowsIdentity">
            <summary>
            exception-safe WindowsIdentity.GetCurrent retrieval returns "domain\username" 
            per MS, this sometimes randomly fails with "Access Denied" particularly on NT4
            </summary>
            <returns></returns>
        </member>
        <member name="M:Anete.Utils.SysInfo.CurrentEnvironmentIdentity">
            <summary>
            exception-safe "domain\username" retrieval from Environment
            </summary>
            <returns></returns>
        </member>
        <member name="M:Anete.Utils.SysInfo.UserIdentity">
            <summary>
            retrieve identity with fallback on error to safer method
            </summary>
            <returns></returns>
        </member>
        <member name="T:Anete.Utils.Exceptions.CommandLineException">
            <summary>
            Exception pri zpracovani parametru prikazove radky
            </summary>
        </member>
        <member name="M:Anete.Utils.Exceptions.CommandLineException.#ctor">
            <summary>
            Constructs a new CommandLineException.
            </summary>
        </member>
        <member name="M:Anete.Utils.Exceptions.CommandLineException.#ctor(System.String)">
            <summary>
            Constructs a new CommandLineException.
            </summary>
            <param name="message">The exception message</param>
        </member>
        <member name="M:Anete.Utils.Exceptions.CommandLineException.#ctor(System.String,System.Exception)">
            <summary>
            Constructs a new CommandLineException.
            </summary>
            <param name="message">The exception message</param>
            <param name="innerException">The inner exception</param>
        </member>
        <member name="M:Anete.Utils.Exceptions.CommandLineException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Serialization constructor.
            </summary>
        </member>
        <member name="T:Anete.Utils.User32">
            <summary>
            Importy User32
            </summary>
        </member>
        <member name="T:Anete.Utils.SortMode">
                <summary>
                    What type of sorting to perform
                </summary>
        </member>
        <member name="F:Anete.Utils.SortMode.None">
                        <summary>
                            No sorting
                        </summary>
        </member>
        <member name="F:Anete.Utils.SortMode.Native">
                        <summary>
                            Perform the sort before performing the ToString()s
                        </summary>
        </member>
        <member name="F:Anete.Utils.SortMode.String">
                        <summary>
                            Perform the sort after performing the ToString()s
                        </summary>
        </member>
    </members>
</doc>
