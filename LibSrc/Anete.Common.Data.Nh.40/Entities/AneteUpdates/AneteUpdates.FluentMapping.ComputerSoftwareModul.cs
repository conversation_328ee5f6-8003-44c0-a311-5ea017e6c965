//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate Fluent Mapping template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;
using FluentNHibernate.Mapping;
using FluentNHibernate.MappingModel.Collections;

namespace Anete.Common.Data.Nh.Entities.AneteUpdates
{
    /// <summary>
    /// There are no comments for ComputerSoftwareModulMap in the schema.
    /// </summary>
    public partial class ComputerSoftwareModulMap : ClassMap<ComputerSoftwareModul>
    {
        /// <summary>
        /// There are no comments for ComputerSoftwareModulMap constructor in the schema.
        /// </summary>
        public ComputerSoftwareModulMap()
        {
              Schema(@"dbo");
              Table(@"SWPocitaceDetail2");
              LazyLoad();
              CompositeId()
                .KeyProperty(x => x.ComputerId, set => {
                    set.Type("Guid");
                    set.ColumnName("IdPocitac");
                    set.Access.Property(); } )
                .KeyProperty(x => x.UpdateTypeId, set => {
                    set.Type("Anete.Common.Core.Interface.Enums.UpdateTypeId, Anete.Common.Core.Interface.40");
                    set.ColumnName("IdTypAktualizace");
                    set.Access.Property(); } )
                .KeyProperty(x => x.SoftwarePart, set => {
                    set.Type("Byte");
                    set.ColumnName("Poradi");
                    set.Access.Property(); } )
                .KeyProperty(x => x.ComputerSoftwareTypeId, set => {
                    set.Type("Anete.Common.Core.Interface.Enums.ComputerSoftwareTypeId, Anete.Common.Core.Interface.40");
                    set.ColumnName("Typ");
                    set.Access.Property(); } );
              Map(x => x.CreatedTime)    
                .Column("Zalozeno")
                .CustomType("DateTime")
                .Access.Property()
                .Generated.Never().CustomSqlType("datetime")
                .Not.Nullable();
              Map(x => x.UpdatedTime)    
                .Column("Aktualizovano")
                .CustomType("DateTime")
                .Access.Property()
                .Generated.Never().CustomSqlType("datetime")
                .Not.Nullable();
              Map(x => x.Name)    
                .Column("Nazev")
                .CustomType("String")
                .Access.Property()
                .Generated.Never()
                .Length(100);
              References(x => x.ComputerSoftware)
                .Class<ComputerSoftware>()
                .Access.Property()
                .Cascade.None()
                .LazyLoad()
                .Not.Insert()
                .Not.Update()
                .Columns("IdPocitac", "IdTypAktualizace", "Typ");
              References(x => x.UpdateType)
                .Class<UpdateType>()
                .Access.Property()
                .Cascade.None()
                .LazyLoad()
                .Not.Insert()
                .Not.Update()
                .Columns("IdTypAktualizace");
              ExtendMapping();
        }

        #region Partial Methods

        partial void ExtendMapping();

        #endregion
    }

}
