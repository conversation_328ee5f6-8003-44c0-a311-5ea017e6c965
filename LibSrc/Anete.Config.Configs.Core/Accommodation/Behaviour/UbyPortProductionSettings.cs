using Anete.Config.Core.Attributes;

namespace Anete.Config.Configs.Core.Accommodation.Behaviour
{
	public class UbyPortProductionSettings : UbyPortSettingsBase
	{
		private const string _addressDefaultValue = @"https://ubyport.pcr.cz/ws_uby/ws_uby.svc?singleWsdl";
		private const string _domainDefaultValue = @"EXRESORTMV";

		private string _address = _addressDefaultValue;
		/// <summary>
		/// Adresa webove sluzby 
		/// </summary>
		[ConfigSRDisplayName("Address", typeof(UbyPortProductionSettings))]
		public override string Address
		{
			get { return _address; }
			set
			{
				if (_address == value)
				{
					return;
				}

				_address = value;
				OnPropertyChanged();
			}
		}

		private string _userName;
		/// <summary>
		/// Uzivatelske jmeno
		/// </summary>
		[ConfigSRDisplayName("UserName", typeof(UbyPortProductionSettings))]
		public override string UserName
		{
			get { return _userName; }
			set
			{
				if (_userName == value)
				{
					return;
				}

				_userName = value;
				OnPropertyChanged();
			}
		}

		private string _domain = _domainDefaultValue;
		/// <summary>
		/// Domena
		/// </summary>
		[ConfigSRDisplayName("Domain", typeof(UbyPortProductionSettings))]
		public override string Domain
		{
			get { return _domain; }
			set
			{
				if (_domain == value)
				{
					return;
				}

				_domain = value;
				OnPropertyChanged();
			}
		}

		private string _password;
		/// <summary>
		/// Heslo
		/// </summary>
		[ConfigSRDisplayName("Password", typeof(UbyPortProductionSettings))]
		public override string Password
		{
			get { return _password; }
			set
			{
				if (_password == value)
				{
					return;
				}

				_password = value;
				OnPropertyChanged();
			}
		}
	}
}
