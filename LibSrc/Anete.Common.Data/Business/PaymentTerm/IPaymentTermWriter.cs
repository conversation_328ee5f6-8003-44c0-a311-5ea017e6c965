using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Anete.Common.Data.Interface.Business.Paragon;
using Anete.Common.Data.Interface.Business.Paragon.Uhrada;
using Anete.Common.Data.Interface.Entities;
using Anete.Devices.Interface.PaymentTerm.RequestHandlers;
using Anete.Utils.ComponentModel;

namespace Anete.Common.Data.Business.PaymentTerm
{
	/// <summary>
	/// Zapisovac dat platebniho terminalu
	/// </summary>
	public interface IPaymentTermWriter
	{
		/// <summary>
		/// Pridruzeny platebni terminal
		/// </summary>
		Anete.Devices.Interface.PaymentTerm.IPaymentTerm PaymentTerm { get; }
		/// <summary>
		/// Platba bankovnim terminalem
		/// </summary>
		/// <param name="detailPlatby">Data k uhrade</param>
		/// <param name="salesSlipPrimaryKey">Primarni klic paragonu</param>
		/// <param name="salesSlipAttributes">Dodatocne atributy paragonu</param>
		/// <param name="userMessageAdapter">Message adapter</param>
		/// <param name="parentWindow">Pouzivane jako parent pri zobrazeni doplnujiciho dialogu</param>
		/// <returns>Modifikovana data uhrady. Pri platbe muze dojit ke zmene uhrady bank. kartou na nekolik uhrad, pokud se platba opakuje</returns>
		Task<PaymentTermWriterResult> Write(UhradaDetailPlatby detailPlatby, SalesSlipPrimaryKey salesSlipPrimaryKey, SalesSlipAttributes salesSlipAttributes, IPaymentCallBack paymentTermCallBack, CancellationToken cancellationToken);

	}

}
