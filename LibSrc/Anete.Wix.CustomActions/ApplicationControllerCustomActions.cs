using System;
using System.Collections.Generic;
using System.Text;
using Microsoft.Deployment.WindowsInstaller;
using System.Diagnostics;
using System.IO;
using System.Runtime.InteropServices;
using System.Security.Principal;
using Anete.Utils;
using Anete.Utils.Win32;

namespace Anete.Wix.CustomActions
{
    /// <summary>
    /// Custom akce pro aplikace
    /// </summary>
    public class ApplicationControllerCustomActions
    {       
        /// <summary>
        /// Spusti aplikaci pod zalogovanym uzivatelem.
        /// Nutne v pripade, ze se instaluje ze sluzby - uzivatel system.        
        /// Akce neni spoustena jako deffered, nemuze tudiz obsahovat zadne vlastni property.
        /// Odkazuje se pouze na globalni property.
        /// </summary>
        /// <param name="session"></param>
        /// <returns></returns
        [CustomAction]
        public static ActionResult StartAppAsLoggedUser(Session session)
        {
            return CustomActionUtils.RunCustomAction((ses) =>
            {                
                ses.Log("StartAppAsLoggedUser");

                string exeFileName = ses["ExeFileNameWithPath"];
                string cmdLineArgs = ses["MAINAPPARGS"];                

                // pokud mam argumenty prikazoveho radku prazdne, zmenim je na null, aby se mi null nasledne predalo v api funkci
                if (cmdLineArgs == "") cmdLineArgs = null;

                LoggedUserProcessStarter.StartProcess((log)=>ses.Log(log), exeFileName, cmdLineArgs);

                return ActionResult.Success;
            }, session);
        }

        /// <summary>
        /// Vzhledem k tomu, ze tato akce vyuziva globalni property, ktere se obtizne predefinovavaji, vytvoril jsem druhou akci,
        /// ktera misto puvodnich ExeFileNameWithPath a MAINAPPARGS vyuziva ExeFileNameWithPath2 a ExeFileNameArgs2
        /// </summary>
        /// <param name="session"></param>
        /// <returns></returns>
        [CustomAction]
        public static ActionResult StartAppAsLoggedUser2(Session session)
        {
            return CustomActionUtils.RunCustomAction((ses) =>
            {
                ses.Log("StartAppAsLoggedUser2");

                string exeFileName = ses["ExeFileNameWithPath2"];
                string cmdLineArgs = ses["ExeFileNameArgs2"];

                // pokud mam argumenty prikazoveho radku prazdne, zmenim je na null, aby se mi null nasledne predalo v api funkci
                if (cmdLineArgs == "") cmdLineArgs = null;

				LoggedUserProcessStarter.StartProcess((log) => ses.Log(log), exeFileName, cmdLineArgs);

                return ActionResult.Success;
            }, session);
        }

        /// <summary>
        /// Pokusi se uzavrit hlavni okno aplikace.
        /// Pokud se nepodari, ukoncuje proces.
        /// </summary>
        /// <param name="session"></param>
        /// <returns></returns>
        [CustomAction]
        public static ActionResult KillSoftly(Session session)
        {
            return CustomActionUtils.RunCustomAction((ses) =>
            {
                session.Log("KillSoftly");
                // seznam spustitelnych souboru
                string exeFileNames = session.CustomActionData["ExeFileNames"];
                session.Log("ExeFileNames: {0}", exeFileNames);

                foreach (string exeFileName in exeFileNames.Split('|'))
                {
                    session.Log(string.Format("Process {0}", exeFileName));
                    foreach (Process appProcess in Process.GetProcessesByName(Path.GetFileNameWithoutExtension(exeFileName)))
                    {						
                        session.Log(string.Format("Ukoncuju process {0}", exeFileName));
						// moznost kontrolovat alespon cestu
						//if (appProcess.MainModule.FileName.ToLowerInvariant().Contains(installedAppConfiguration.InstallDir.ToLowerInvariant()))
						int waitTimeOut = 20000;
						if (appProcess.MainWindowHandle != IntPtr.Zero)
						{
							session.Log($"Zaviram hlavni okno s Handle={appProcess.MainWindowHandle}...");
							appProcess.CloseMainWindow();						
							session.Log("Cekam {0} ms na ukonceni aplikace", waitTimeOut);
						} else
						{
							session.Log($"Nepodarilo se ziskat handle hlavniho okna. Aplikaci sestrelim.");
						}

						// pokud neznam handle okna nebo pokud ho znam a nepodarilo se okno uzavrit
                        if (appProcess.MainWindowHandle == IntPtr.Zero || (appProcess.MainWindowHandle != IntPtr.Zero && !appProcess.WaitForExit(waitTimeOut)))
                        {
                            // neposloucha, budu drsny :-)
                            session.Log("Aplikace se neuzavrela, sestreluji ji");
                            appProcess.Kill();
							session.Log("Cekam {0} ms na sestreleni aplikace", waitTimeOut);
							appProcess.WaitForExit(waitTimeOut);
							if (appProcess.HasExited)
							{
								session.Log("Aplikace byla sestrelena");
							}
							else
							{
								ses.Log("Aplikaci se nepodarilo sestrelit, instalace bude vyzadovat restart");
							}                            
                        }
                        else
                        {
                            session.Log("Aplikace byla ukoncena");
                        }

                    }
                }
                return ActionResult.Success;
            }, session);
        }        
    }
}
