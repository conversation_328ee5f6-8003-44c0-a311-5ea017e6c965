using System;
using System.Collections.Generic;
using System.Text;

namespace Anete.AutoUpgrades.Admin.Operations.MSI
{
    /// <summary>
    /// Trida pro ulozeni logu z MSI
    /// </summary>
    public class MSIUpgradeLog : UpgradeLogBase
    {
        /// <summary>
        /// Bezparametric<PERSON> konstruktor, nutny kvuli deserializaci.
        /// </summary>
        public MSIUpgradeLog()
        {            
        }

        /// <summary>
        /// Log vytvoreny Windows installerem.
        /// </summary>
        public string MSILog { get; set; }

		public string LogFileName { get; private set; }

        public string ErrorText { get; set; }

		/// <summary>
		/// Inicializacni metoda
		/// </summary>		
		public void Initialize(string msiLog, string logFileName)
		{
			MSILog = msiLog;
			LogFileName = logFileName;
		}

        /// <summary>
        /// Text, ktery Upgrader zalogovava pomoci Log4Net
        /// </summary>
        /// <returns></returns>
        public override string GetLog4NetText()
        {
            /*
#if DEBUG
            return "Misto MSI logu pouze tento text, abych nemusel cakat, nez se mi log vypise do konzole";
#else
             */
            if (ErrorText != null)
            {
                return ErrorText + Environment.NewLine + Environment.NewLine + MSILog;
            } else
            {
                return MSILog;
            }
//#endif
        }

		protected override string FindLogFileInt() => LogFileName;
	}
}
