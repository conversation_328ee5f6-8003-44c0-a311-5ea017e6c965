using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml.Linq;

namespace Anete.Common.Data.Interface.Business.Paragon
{

	/// <summary>
	/// Data paroveho paragonu pri deleni paragonu
	/// </summary>
	public class SplitPairSaleSlipData : PairSaleSlipDataBase
	{

		/// <summary>
		/// Inicializace z XElement
		/// </summary>
		public SplitPairSaleSlipData(XElement element)
			: base(element, SalesSlipAttributes.XmlTagNames.PairSalesSlip)
		{

		}

		/// <summary>
		/// Konstruktor pro normalni inicializaci
		/// </summary>
		public SplitPairSaleSlipData(int salesSlipId, short accountBalanceId, int clientId)
			: base(salesSlipId, accountBalanceId, clientId, SalesSlipAttributes.XmlTagNames.PairSalesSlip)
		{

		}
	}
}
