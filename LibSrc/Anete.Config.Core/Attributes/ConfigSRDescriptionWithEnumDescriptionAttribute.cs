using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Anete.Config.Core.Internals;
using System.Resources;
namespace Anete.Config.Core.Attributes
{

	/// <summary>
	/// Atribut pro popis enumu provazany s PropertySR a EnumsSR
	/// </summary>
	public class ConfigSRDescriptionWithEnumDescriptionAttribute : ConfigSRDescriptionWithEnumDescriptionAttributeBase
	{

		public ConfigSRDescriptionWithEnumDescriptionAttribute(string propertyName, Type enumType, Type configClassType)
			: base(propertyName, enumType, configClassType)
		{
			
		}

		/// <summary>
		/// Novy nazev zdroje je tvoren typem enumu vcetne namespce bez Anete.Config.Core.Configs a jeho hodnotou.
		/// </summary>
		/// <param name="enumValue"></param>
		/// <returns></returns>
		protected override string GetEnumResourceItemName(Enum enumValue)
		{
			return ConfigEnumUtils.GetResourceItemNameForEnum(enumValue, EnumType);
		}
	}
}
