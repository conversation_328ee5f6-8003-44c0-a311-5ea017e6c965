//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON>ti<PERSON> Developer tool using NHibernate Fluent Mapping template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;
using FluentNHibernate.Mapping;
using FluentNHibernate.MappingModel.Collections;

namespace Anete.Common.Data.Nh.Entities
{
    /// <summary>
    /// There are no comments for OrderPriceMap in the schema.
    /// </summary>
    public partial class OrderPriceMap : ClassMap<OrderPrice>
    {
        /// <summary>
        /// There are no comments for OrderPriceMap constructor in the schema.
        /// </summary>
        public OrderPriceMap()
        {
              Schema(@"dba");
              Table(@"objednavky_ceny");
              LazyLoad();
              CompositeId()
                .KeyProperty(x => x.OrderId, set => {
                    set.Type("Int32");
                    set.ColumnName("pc");
                    set.Access.Property(); } )
                .KeyProperty(x => x.PriceElementNameId, set => {
                    set.Type("Int16");
                    set.ColumnName("polozka_id");
                    set.Access.Property(); } );
              Map(x => x.Price)    
                .Column("hodnota")
                .CustomType("Decimal")
                .Access.Property()
                .Generated.Never().CustomSqlType("numeric(9,2)")
                .Precision(9)
                .Scale(2);
              References(x => x.Order)
                .Class<Order>()
                .Access.Property()
                .Cascade.None()
                .LazyLoad()
                .Not.Insert()
                .Not.Update()
                .Columns("pc");
              References(x => x.PriceElementName)
                .Class<PriceElementName>()
                .Access.Property()
                .Cascade.None()
                .LazyLoad()
                .Not.Insert()
                .Not.Update()
                .Columns("polozka_id");
              ExtendMapping();
        }

        #region Partial Methods

        partial void ExtendMapping();

        #endregion
    }

}
