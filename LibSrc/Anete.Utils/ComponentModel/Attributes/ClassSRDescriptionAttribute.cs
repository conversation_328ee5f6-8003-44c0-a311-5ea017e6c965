using System;
using System.Resources;

namespace Anete.Utils.ComponentModel.Attributes
{
    /// <summary>
    /// Atribut pro lokalizovatelny popis tridy.    
    /// Property je ulozena v resource souboru pod klicem ClassName_Description. 
    /// Pokud je typ genericky, je klic ve tvarku ClassName_1_Description, kde 1 udava pocet generickych argumentu.
    /// Obvykle byva vytvorit potomka, ktery se odkazuje na konkretni resource manager
    /// </summary>
    [AttributeUsage(AttributeTargets.Class, AllowMultiple = false, Inherited = true)]
    public class ClassSRDescriptionAttribute : SRDescriptionAttribute
    {
        private const string _descriptionResourceSuffix = "_Description";

        #region constructors...
        /// <summary>
        /// Initializes a new instance of the LocalizableDescriptionConfigAttribute class.
        /// </summary>
        public ClassSRDescriptionAttribute(Type classType, ResourceManager resourceManager)
            // znak '' vznika u generickych typu. Ve zdrojich nelze pouzit, nahradim jej proto za _
            : base(classType.Name.Replace('`', '_') + _descriptionResourceSuffix, resourceManager)
        {
        }
        #endregion
    }

}
