using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Anete.Devices.Interface.DeviceControllers.Automats
{

	public class VyvolavaciQtMetadata : AutomatMetadataBase
	{

		public VyvolavaciQtMetadata() : base(AutomatGeneralNames.InfoObjednavek, "VyvolávacíQt", "Vyvolavací čtečka do jedálně. Zobrezuje jídla na vyzvednutí", "Qt automaty", new int[] { 223 }, "VyvolávacíQt")
		{
		}
	}
}
