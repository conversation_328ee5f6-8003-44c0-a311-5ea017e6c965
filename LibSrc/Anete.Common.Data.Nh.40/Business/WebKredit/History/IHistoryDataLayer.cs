using Anete.Common.Core.Interface.Enums;
using NHibernate;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Anete.Common.Data.Nh.Business.WebKredit.History
{
	public interface IHistoryDataLayer
	{
		/// <summary>
		/// dba.RAD_HistoriePohybuEx
		/// </summary>
		Task<List<HistoryItemModel>> GetAccountHistoryAsync(ISession session, int idLk, DateTime startDate, DateTime endDate, ApplicationLanguage menuLanguage, ApplicationLanguage applicationLanguage);

		/// <summary>
		/// Uctenky
		/// </summary>
		Task<List<ReceiptModel>> GetReceiptsAsync(ISession session, int clientId, DateTime startDate, DateTime endDate);

		/// <summary>
		/// Útraty a příspěvky
		/// </summary>
		Task<List<SpendingAndSubsidyItemModel>> GetSpendingAndSubsidiesAsync(ISession session, int idLk, DateTime startDate, DateTime endDate, ApplicationLanguage language);

		/// <summary>
		/// dba.RAD_PrehledDotaci
		/// </summary>
		Task<SubsidiesModel> GetSubsidiesAsync(ISession session, int idLk, DateTime startDate, DateTime endDate);
	}
}