namespace Anete.Common.Data.Nh.Business.WebKredit.Ordering
{
	public enum MenuItemState
	{
		/// <summary>
		/// V poradku
		/// </summary>
		Success = 0,
		/// <summary>
		/// Nemáte povoleno objednat toto jídlo
		/// </summary>
		OrderingMealNotAllowed = 1,
		/// <summary>
		/// Toto jídlo se nevaří
		/// </summary>
		NotCookedMeal = 2,
		/// <summary>
		/// Vyčerpán počet vařených porcí
		/// </summary>
		CookingLimitExceeded = 3,
		/// <summary>
		/// Objednávání nebylo doposud zahájeno
		/// </summary>
		OrderingNotYetStarted = 4,
		/// <summary>
		/// Objednávání ukončeno
		/// </summary>
		OrderingFinished = 5,
		/// <summary>
		/// Vyčerpán počet vydávaných porcí
		/// </summary>
		CanteenLimitExceeded = 6,
		/// <summary>
		/// Nelze objednat do této výdejny
		/// </summary>
		OrderingToThisCanteenNotAllowed = 7,
		/// <summary>
		/// Nelze objednat (společné jídlo)
		/// </summary>
		NotPossibleToOrderSharedMeal = 9,
		/// <summary>
		/// Výdejna tento den není v provozu
		/// </summary>
		CanteenNotWorking = 20,
		/// <summary>
		/// Není potvrzený jídleníček
		/// </summary>
		NotApprovedMenu = 21,
		/// <summary>
		/// Nelze objednat (nespecifikováno). Znamena vetsinou, ze jde o minutkove jidlo
		/// </summary>
		CannotOrderNotSpecified = 99,
		/// <summary>
		/// Stav nelze objednat - jidlo neni na objednavku
		/// </summary>
		CannotOrderMealNotOrderable = 111
	}
}