using Anete.Common.Core.AppServices.Language;
using System;
using System.Runtime.Serialization;
using System.Collections.Generic;
using Anete.Utils;
using Anete.Resources;
using Anete.Common.Core.Interface.Enums;

namespace Anete.Common.Core.Entities
{
    /// <summary>
    /// Druh jidla
    /// </summary>
    [DataContract(Namespace = AneteNamespace.DefaultNamespace)]
	public class MealKind
	{
		#region constructors...					
        public MealKind(short mealKindId, short workplaceId, params MealKindLng[] mealKindLng)
        {
            Id = mealKindId;
            WorkplaceId = workplaceId;
            InitLanguage(mealKindLng);
        }

        public MealKind(short mealKindId, short altCount, short workplaceId, params MealKindLng[] mealKindLng)
		{            
            Id = mealKindId;			
			AltCount = altCount;
			WorkplaceId = workplaceId;
            InitLanguage(mealKindLng);
        }
		#endregion

        public void InitLanguage(MealKindLng[] mealKindLng)
        {
            MealKindLng = mealKindLng;
        }

		/// <summary>
		/// Id druhu jidla. Jednoznacne druh jidla identifikuje a pouziva se i pri porovnavani.
		/// </summary>
		[DataMember(Order = 0)]
		public short Id { get; private set; }        

		/// <summary>
		/// Popis druhu jidla
		/// </summary>		
		public string Name
        {
            get
            {
                ApplicationLanguage language = ApplicationLanguageExt.GetFromThreadUiCulture();
                return LanguagePropertyResolver.GetPropertyValue(MealKindLng, p => p.Name, language, true);
            }                
        }        

		/// <summary>
		/// Zkratka nazvu jidla
		/// </summary>	
		public string ShortName
        {
            get
            {
                ApplicationLanguage language = ApplicationLanguageExt.GetFromThreadUiCulture();
                return LanguagePropertyResolver.GetPropertyValue(MealKindLng, p => p.ShortName, language, true);
            }
        }        

		private bool _isAltCountSet = false;
		private int _altCount;
		/// <summary>
		/// Pocet alternativ pro dany druh jidla. Odpovida sloupci pocet tabulky dba.JidlaDruhy.
		/// </summary>
        [DataMember(Order = 1)]
		public int AltCount
		{
			get
			{
				if (!_isAltCountSet)
				{
					throw ExcUtils.PropertyNotInitialized();
				}
				return _altCount;
			}
			private set
			{
				_isAltCountSet = true;
				_altCount = value;
			}
		}



		/// <summary>
		/// Id vyvarovny
		/// </summary>
		[DataMember(Order = 2)]
		public short WorkplaceId { get; private set; }

        [DataMember(Order = 3)]
        public MealKindLng[] MealKindLng { get; private set; }

        #region public overrides...				
        public override int GetHashCode()
		{
			return BitwiseUtils.IntFrom2Short(WorkplaceId, Id);			
		}
		
		public override bool Equals(object obj)
		{
			if (!(obj is MealKind))
			{
				return false;
			}

			MealKind mealKind = (MealKind)obj;
			return mealKind.Id == Id && mealKind.WorkplaceId == WorkplaceId;         
		}

	   
		public override string ToString()
		{
			return string.Format("{0} {1}", Id, Name);
		}
		#endregion
	}
}
