using System;
using System.Collections.Generic;
using System.Reflection;
using System.Linq;
using System.Diagnostics;

namespace Anete.Utils.Patterns.AppActions
{
    /// <summary>
    /// Atributem se oznaci property, jez ma byt soucasti kolekce parametru.
    /// </summary>
    [AttributeUsage(AttributeTargets.Property, AllowMultiple=false)]
    public class ToParamCollectionAttribute : Attribute
    {
        /// <summary>
        /// Type parametru. Neni-li nastaven, vezme se primo typ property.
        /// </summary>
        public Type ParamType { get; set; }

        /// <summary>
        /// Id parametru. Nemusi byt nastaveno, pokud ma property unikatni typ (rozhrani).
        /// </summary>
        public string ParamId { get; set; }

		private bool _requied = true;
		/// <summary>
		/// Je parametr vyzadovan? Implicitne je vyzadovano.
		/// </summary>
		public bool Required
		{
			get
			{
				return _requied;
			}
			set
			{
				_requied = value;
			}
		}

        /// <summary>
        /// Vraci TypedParam, ktery bude synchronizovan s property pi.
        /// </summary>
        /// <param name="pi"></param>
        /// <param name="piOwner"></param>
        /// <returns></returns>
        public TypedParam GetTypedParam(PropertyInfo pi, object piOwner)
        {
            if (!pi.CanRead || !pi.CanWrite)
            {
                // TOJTODO: Lokalizovat
                throw new ArgumentException(
                    string.Format("Property '{0}' obsahující parametr musí být nastavena pro čtení i zápis", pi.Name), "pi");
            }            

            if (string.IsNullOrEmpty(ParamId))
            {                
                CheckParamTypeIsUnique(pi, piOwner);
            }            
            
            TypedParamFromProperty typedParamFromProperty = new TypedParamFromProperty(pi, piOwner, new ParameterKey(ParamType ?? pi.PropertyType, ParamId), Required);
            return typedParamFromProperty;
        }

        /// <summary>
        /// Kontrola, zda je ParamType v ramci tridy unikatni. Nemuzu mit ve tride oznacene dve property stejneho typu bez nastaveneho id.
        /// Vypocetne narocne, proto se deje jen v DEBUG.
        /// </summary>
        /// <param name="pi"></param>
        /// <param name="piOwner"></param>
        [Conditional("DEBUG")]
        private void CheckParamTypeIsUnique(PropertyInfo pi, object piOwner)
        {
            int sameParamTypeCount = piOwner.GetType().GetProperties().Count(p => p.PropertyType == ParamType);
            if (sameParamTypeCount > 1)
            {
                throw new ArgumentException(
                    string.Format("Property '{0}' nelze použit jako parametr bez určení id, protože třída '{1}' obsahuje více property stejnehé typu '{2}'", pi.Name, piOwner, ParamType));
            }
        }
    }
}
