using System;
using System.ComponentModel;
using Anete.Config.Core.Attributes;
using Anete.Utils;

namespace Anete.Config.Configs.Core.CashDesk
{
    /// <summary>
    /// Trida pro konfiguraci skladu Orion
    /// </summary>
    public class StockModuleSettingsOrion : StockModuleSettingsBase
    {
        private const string _pluFileNameDefValue = "Plu.txt";
        private string _pluFileName = _pluFileNameDefValue;
        /// <summary>
        /// Nazev importniho souboru.
        /// </summary>
        [DefaultValue(_pluFileNameDefValue)]
        [ConfigSRDisplayName("PluFileName", typeof(StockModuleSettingsOrion))]
        [ConfigSRDescription("PluFileName", typeof(StockModuleSettingsOrion))]
        public string PluFileName
        {
            get
            {
                return _pluFileName;
            }
            set
            {
                if (_pluFileName == value)
                {
                    return;
                }
                _pluFileName = value;
                OnPropertyChanged("PluFileName");
            }
        }

        private const string _exportFileNameDefValue = "Prodej_{DateTime.Now:yyMMdd_HHmmss}.txt";
        private string _exportFileName = _exportFileNameDefValue;
        /// <summary>
        /// Jmeno souboru, ve kterem se exportuji prodeje
        /// Pozor: nemuze pouzivat [EvaluatePropertyParameters(true)], jinak by se jednou dosadilo datum a uz nikdy nezmenilo
        /// </summary>
        [DefaultValue(_exportFileNameDefValue)]
        [ConfigSRDisplayName("ExportFileName", typeof(StockModuleSettingsOrion))]
        [ConfigSRDescription("ExportFileName", typeof(StockModuleSettingsOrion))]
        public string ExportFileName
        {
            get
            {
                return _exportFileName;
            }
            set
            {
                if (_exportFileName == value)
                {
                    return;
                }
                _exportFileName = value;
                OnPropertyChanged("ExportFileName");
            }
        }

        /// <summary>
        /// Vraci ExportFileName formatovany pomoci ParameterProvider
        /// </summary>
        [Browsable(false)]
        public string ExportFileNameFormat
        {
            get
            {
                return StrUtils.FormatSymbolicNamed(ExportFileName);
            }
        }

        private const string _exportDirNameDefValue = @"O:\ORION2\IMP\KASA{AppInstallationInfo.AppInstallationId:d2}";
        private string _exportDirName = _exportDirNameDefValue;
        /// <summary>
        /// Adresar kam se exportuji soubory s prodeji
        /// Proc se jmenuje Export a ma cestu IMP? Jde o exportni adresar z hlediska Kasy.
        /// </summary>
        [DefaultValue(_exportDirNameDefValue)]
        [ConfigSRDisplayName("ExportDirName", typeof(StockModuleSettingsOrion))]
        [ConfigSRDescription("ExportDirName", typeof(StockModuleSettingsOrion))]
        [EvaluatePropertyParameters(true)]
        public string ExportDirName
        {
            get
            {
                return _exportDirName;
            }
            set
            {
                if (_exportDirName == value)
                {
                    return;
                }
                _exportDirName = value;
                OnPropertyChanged("ExportDirName");
            }
        }

        private const string _inputDirNameDefValue = @"O:\ORION2\EXPORT\KASA{AppInstallationInfo.AppInstallationId:d2}";
        private string _inputDirName = _inputDirNameDefValue;
        /// <summary>
        /// Nazev importniho adresare. Za parametr se dosazuje IdZarizeni
        /// Proc se jmenuje Input a ma cestu Export? Jde o importni adresar z hlediska Kasy.
        /// </summary>
        [DefaultValue(_inputDirNameDefValue)]
        [ConfigSRDisplayName("InputDirName", typeof(StockModuleSettingsOrion))]
        [ConfigSRDescription("InputDirName", typeof(StockModuleSettingsOrion))]
        [EvaluatePropertyParameters(true)]
        public string InputDirName
        {
            get
            {
                return _inputDirName;
            }
            set
            {
                if (_inputDirName == value)
                {
                    return;
                }
                _inputDirName = value;
                OnPropertyChanged("InputDirName");
            }
        }

        private const string _archiveDirNameDefValue = @"O:\ORION2\ARCHIV\KASA{AppInstallationInfo.AppInstallationId:d2}";
        private string _archiveDirName = _archiveDirNameDefValue;
        /// <summary>
        /// Nazev adresare, kam se presune importovany soubor Plu.txt. Za parametr se dosazuje IdZarizeni        
        /// </summary>
        [DefaultValue(_archiveDirNameDefValue)]
        [ConfigSRDisplayName("ArchiveDirName", typeof(StockModuleSettingsOrion))]
        [ConfigSRDescription("ArchiveDirName", typeof(StockModuleSettingsOrion))]
        [EvaluatePropertyParameters(true)]
        public string ArchiveDirName
        {
            get
            {
                return _archiveDirName;
            }
            set
            {
                if (_archiveDirName == value)
                {
                    return;
                }
                _archiveDirName = value;
                OnPropertyChanged("ArchiveDirName");
            }
        }

    }
}
