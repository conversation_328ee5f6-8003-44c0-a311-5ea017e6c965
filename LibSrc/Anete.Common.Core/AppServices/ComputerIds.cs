using System;
using System.Linq;

namespace Anete.Common.Core.AppServices
{
    public static class ComputerIds
    {
        /// <summary>
        /// Id pocitace, ktere se vyuziva pro ulozeni systemovych parametru
        /// </summary>
        public static Guid System
        {
            get
            {
                return Guid.Empty;
            }
        }

		public static Guid AneteAdmin { get; } = new Guid("ECD38DBA-9530-4112-B3C3-21B7FB4D0569");

		/// <summary>
		/// Id pocitace Konfiguratoru. Pouzije se pouze v pripade, kdy na PC neni ID konfigurator definovano
		/// </summary>
		public static Guid Konfigurator { get; } = new Guid("C19D2497-D2DB-4196-8ABC-33868573B6B7");
	}
}