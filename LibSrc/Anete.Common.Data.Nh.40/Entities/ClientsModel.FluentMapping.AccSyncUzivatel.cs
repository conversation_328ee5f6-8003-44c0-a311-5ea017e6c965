//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate Fluent Mapping template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;
using FluentNHibernate.Mapping;
using FluentNHibernate.MappingModel.Collections;

namespace Anete.Common.Data.Nh.Entities
{
    /// <summary>
    /// There are no comments for AccSyncUzivatelMap in the schema.
    /// </summary>
    public partial class AccSyncUzivatelMap : ClassMap<AccSyncUzivatel>
    {
        /// <summary>
        /// There are no comments for AccSyncUzivatelMap constructor in the schema.
        /// </summary>
        public AccSyncUzivatelMap()
        {
              Schema(@"dbo");
              Table(@"ACC_Sync_Uzivatel");
              LazyLoad();
              CompositeId()
                .KeyProperty(x => x.IdAutentizace, set => {
                    set.Type("Anete.Common.Core.Interface.Enums.AppAuthenticationType, Anete.Common.Core.Interface.40");
                    set.ColumnName("IdAutentizace");
                    set.Access.Property(); } )
                .KeyProperty(x => x.Login, set => {
                    set.Type("String");
                    set.ColumnName("Login");
                    set.Length(30);
                    set.Access.Property(); } );
              Map(x => x.Prijmeni)    
                .Column("Prijmeni")
                .CustomType("String")
                .Access.Property()
                .Generated.Never().CustomSqlType("varchar(30)")
                .Not.Nullable()
                .Length(30);
              Map(x => x.Jmeno)    
                .Column("Jmeno")
                .CustomType("String")
                .Access.Property()
                .Generated.Never().CustomSqlType("varchar(30)")
                .Length(30);
              Map(x => x.TitulPredJmenem)    
                .Column("TitulPredJmenem")
                .CustomType("String")
                .Access.Property()
                .Generated.Never().CustomSqlType("varchar(20)")
                .Length(20);
              Map(x => x.TitulZaJmenem)    
                .Column("TitulZaJmenem")
                .CustomType("String")
                .Access.Property()
                .Generated.Never().CustomSqlType("varchar(20)")
                .Length(20);
              Map(x => x.Poznamka)    
                .Column("Poznamka")
                .CustomType("String")
                .Access.Property()
                .Generated.Never().CustomSqlType("varchar(200)")
                .Length(200);
              Map(x => x.Email)    
                .Column("email")
                .CustomType("String")
                .Access.Property()
                .Generated.Never().CustomSqlType("varchar(254)")
                .Length(254);
              Map(x => x.Role)    
                .Column("Role")
                .CustomType("StringClob")
                .Access.Property()
                .Generated.Never().CustomSqlType("xml");
              Map(x => x.Nastaveni)    
                .Column("Nastaveni")
                .CustomType("StringClob")
                .Access.Property()
                .Generated.Never().CustomSqlType("xml");
              References(x => x.AccAutentizace)
                .Class<AccAutentizace>()
                .Access.Property()
                .Cascade.None()
                .LazyLoad()
                .Not.Insert()
                .Not.Update()
                .Columns("IdAutentizace");
              ExtendMapping();
        }

        #region Partial Methods

        partial void ExtendMapping();

        #endregion
    }

}
