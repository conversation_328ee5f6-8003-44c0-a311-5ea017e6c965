//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Anete.Common.Data.Nh.Entities {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class MagionAccountCoding1SR {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal MagionAccountCoding1SR() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Anete.Common.Data.Nh.Entities.MagionAccountCoding1SR", typeof(MagionAccountCoding1SR).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Skupina strávníků.
        /// </summary>
        public static string ClientGroup {
            get {
                return ResourceManager.GetString("ClientGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Zakázka.
        /// </summary>
        public static string Contract {
            get {
                return ResourceManager.GetString("Contract", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Finanční zdroj.
        /// </summary>
        public static string FinancialSource {
            get {
                return ResourceManager.GetString("FinancialSource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dotováno.
        /// </summary>
        public static string HasSubsidy {
            get {
                return ResourceManager.GetString("HasSubsidy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Id.
        /// </summary>
        public static string MagionAccountCoding1Id {
            get {
                return ResourceManager.GetString("MagionAccountCoding1Id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Typ.
        /// </summary>
        public static string Type {
            get {
                return ResourceManager.GetString("Type", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Platí od.
        /// </summary>
        public static string ValidFrom {
            get {
                return ResourceManager.GetString("ValidFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Platí do.
        /// </summary>
        public static string ValidTo {
            get {
                return ResourceManager.GetString("ValidTo", resourceCulture);
            }
        }
    }
}
