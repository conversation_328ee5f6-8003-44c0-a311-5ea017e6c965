using Microsoft.Extensions.Logging;
using System;

namespace Anete.Utils.ComponentModel.Commands
{
	public abstract class RequestHandlerDecoratorBase
	{
		/// <summary>
		/// Vytvorena zvlastni metoda pro logovani request - snaze se dohleda, ze se vola podruhe v pripade prvotnim nezalogovani a nasledne vyjimce
		/// </summary>
		/// <typeparam name="TRequest"></typeparam>
		/// <param name="log"></param>
		/// <param name="request"></param>
		protected void LogRequest<TRequest>(ILogger log, TRequest request)
		{
			RequestHandlerDecoratorHelper<TRequest>.LogRequest(log, request);
		}	

		protected void TryLogResponse<TRequest, TResponse>(ILogger log, TRequest request, TResponse response, bool canLogRequest)
		{
			if (LoggerEnabledUtils.CanLog(request))
			{
				// pokud nemam zalogovany request, pridam i jej - nechci mit v logu pouze odpoved bez pozadavku
				if(!canLogRequest)
				{
					LogRequest(log, request);
				}
				LogResponse(log, response);
			}
		}

		protected bool TryLogRequest<TRequest>(ILogger log, TRequest request)
		{
			bool canLogRequest = LoggerEnabledUtils.CanLog(request);
			if (canLogRequest)
			{
				LogRequest(log, request);
			}

			return canLogRequest;
		}

		protected void LogRequestException<TRequest>(ILogger log, TRequest request, bool canLogRequest, Exception ex)
		{
			// pokud nemam doposud zalogovany request, musim zavolat jeho logovani, nechci zobrazit pouze chybu a nemit v logu vstupni parametry requestu
			if (!canLogRequest)
			{
				LogRequest(log, request);
			}
			RequestHandlerDecoratorHelper<TRequest>.LogError(log, ex);
		}

		private void LogResponse<TResponse>(ILogger log, TResponse response)
		{
			RequestHandlerDecoratorHelper<TResponse>.LogResponse(log, response);
		}
	}
}