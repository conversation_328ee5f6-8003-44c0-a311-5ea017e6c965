using Anete.Common.Core.Business.InstallationParameters.Groups;
using Anete.Common.Core.Interface.Business.InstallationParameters.Parameters;
using Unity;
using System;
using System.Linq;
using System.Collections.Generic;
using Anete.Common.Core.Interface.Business.InstallationParameters.Groups;
using Anete.Common.Core.Interface.Business.InstallationParameters.ReportGroups;

namespace Anete.Common.Core.Business.InstallationParameters.Parameters.Computer.ComputerPlatform
{
    /// <summary>
    /// Mena nastavena v systemu pocitace
    /// </summary>
    public class ComputerPlatformDesc : ContainerBasedInstallationParameterDescBase<ComputerPlatformFactory, ComputerPlatformValueBasedFactory,
		ComputerPlatformSerializer, ComputerInstalationParameterReportGroup>
    {
        public ComputerPlatformDesc(IUnityContainer container, InstallationParameterGroupFactory groupFactory) 
            : base(container, InstallationParameterTargets.Computer, Interface.Enums.InstallationParameterId.ComputerPlatform, groupFactory)
        {
            
        }

        protected override IEnumerable<InstallationParameterGroupId> GetGroupsInt()
        {
            yield return InstallationParameterGroupId.ComputerInfo;
        }

        protected override string GetDescriptionInt()
        {
            return "Platforma počítače";
        }
    }
}
