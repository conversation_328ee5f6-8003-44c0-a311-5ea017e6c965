using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Reflection;
using Anete.Utils.Collections;
using System.Collections;
using System.ComponentModel;
using Anete.Utils.Extensions;
using Anete.Utils.EventArgs;

namespace Anete.Utils.ComponentModel
{

    /// <summary>
    /// Synchronizuje dve instance stejneho typu. Synchronizaci se mysli sjednoceni hodnot property.
    /// </summary>
    /// <typeparam name="TInstance">Typ objektu, ktery se synchronizuje</typeparam>
    /// <typeparam name="TComparer">Porovnavac, ktery hleda rozdilne property</typeparam>
    public class InstancePropertiesSynchronizer<TInstance, TComparer> where TComparer : InstancePropertiesComparerBase<TInstance>, new()
    {
		/// <summary>
		/// Delegat volany pri odstraneni polozky z kolekce
		/// </summary>
		public Action<object, IList> AfterItemRemoved { get; set; }

		protected virtual TComparer CreateComparer()
		{
			TComparer comparer = new TComparer();

			OnComparerCreated(new SimpleDataEventArgs<TComparer>(comparer));

			return comparer;
		}

		// ToJTODO: 13.10.2010 Napsat testy
        /// <summary>
        /// Sjednoti hodnoty property obou instanci.
        /// Pokud narazi na read-only propery, jejiz hodnota je rozdilna, vyvola vyjimku.
        /// </summary>
        /// <param name="source">Zdrojova instance, ze ktere se ctou hodnoty property</param>
        /// <param name="dest">Cilova instane, do ktere se property zapisuji</param>
        /// <param name="synchronizeProperties">Bude se provadet synchronizace property?</param>
        /// <param name="synchronizeCollections">Bude se provadet synchronizace kolekci?</param>
        /// <param name="logger">Loguje zmeny v jednotlivych entitach</param>
        public void Synchronize(TInstance source, TInstance dest, bool synchronizeProperties, bool synchronizeCollections, IEntitySynchronizationLogger logger)
        {
			TComparer comparer = CreateComparer();
            IInstanceComparerResults instanceCompareResults = comparer.Compare(source, dest);

            if (synchronizeProperties)
            {
                foreach (string propertyName in instanceCompareResults.DifferentProperties)
                {
                    PropertyInfo propertyInfo = typeof(TInstance).GetProperty(propertyName);
                    if (!propertyInfo.CanWrite)
                    {
                        // nelze zapisovat, nemuzu tudiz provest synchronizaci
                        throw new ArgumentException(string.Format("Property {0} je pouze pro cteni, nemuzu ji synchronizovat", propertyName));
                    }

                    object oldValue = propertyInfo.GetValue(dest, null);
                    object sourceValue = propertyInfo.GetValue(source, null);
                    DisplayNameAttribute displayNameAttribute = propertyInfo.GetAttribute<DisplayNameAttribute>();
                    string displayName = null;
                    if (displayNameAttribute != null)
                    {
                        displayName = displayNameAttribute.DisplayName;
                    }

                    propertyInfo.SetValue(dest, sourceValue, null);
                    logger.AddLog(dest, new ChangePropertyValueEntityAction(propertyName, displayName, sourceValue, oldValue));
					OnPropertyValueChanged(propertyName, oldValue, sourceValue);
                }
            }

            if (synchronizeCollections)
            {             
                foreach (ICollectionComparerResult collectionCompareResult in instanceCompareResults.Collections)
                {
                    if (collectionCompareResult.IsIdentical)
                    {
                        continue;
                    }

                    PropertyInfo propertyInfo = typeof(TInstance).GetProperty(collectionCompareResult.CollectionName);
                    // ToJTODO: 14.10.2010 Predpokladam, ze se bude jednat o ICollection. Melo by se vyresit systemoveji.
                    //IList sourceCollection = (IList)propertyInfo.GetValue(source, null);
                    IListSource destCollectionSource = (IListSource)propertyInfo.GetValue(dest, null);
                    IList destCollection = destCollectionSource.GetList();

                    foreach (object itemToAdd in collectionCompareResult.ItemsToAdd)
                    {
                        destCollection.Add(itemToAdd);
                        logger.AddLog(dest, new ModifyCollectionEntityAction(collectionCompareResult.CollectionName, itemToAdd, ModifyCollectionType.AddItem));
                    }

                    foreach (object itemToDelete in collectionCompareResult.ItemsToDelete)
                    {
						logger.AddLog(dest, new ModifyCollectionEntityAction(collectionCompareResult.CollectionName, itemToDelete, ModifyCollectionType.RemoveItem));
                        destCollection.Remove(itemToDelete);
						if (AfterItemRemoved != null)
						{						
							AfterItemRemoved(itemToDelete, destCollection);
						}
                    }
                }
            }
        }

		#region events...
		/// <summary>
		/// Vyvolano pri zmene hodnoty property behem synchronizace
		/// </summary>
		public event EventHandler<PropertyValueChangedEventArgs> PropertyValueChanged;

		/// <summary>
		/// Vyvolano po vytvoreni compareru. Urceno pro jeho inicializaci.
		/// </summary>
		public event EventHandler<SimpleDataEventArgs<TComparer>> ComparerCreated;
		#endregion

		#region protected virtual methods...
		/// <summary>
		/// Called when [property value changed].
		/// </summary>
		protected virtual void OnPropertyValueChanged(string propertyName, object beforeChangedValue, object afterChangedValue)
		{
			EventHandler<PropertyValueChangedEventArgs> handler = PropertyValueChanged;
			if (handler != null)
			{
				handler(this, new PropertyValueChangedEventArgs(propertyName, beforeChangedValue, afterChangedValue));
			}
		}
		
		/// <summary>
		/// Triggers the ComparerCreated event.
		/// </summary>
		protected virtual void OnComparerCreated(SimpleDataEventArgs<TComparer> ea)
		{
			EventHandler<SimpleDataEventArgs<TComparer>> handler = ComparerCreated;
			if (handler != null)
			{
				handler(this, ea);
			}
		}		
		#endregion
    }
}
