<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AutoUpdaterBehaviourMonitorConfig_UpgradeWhen_DisplayName" xml:space="preserve">
    <value>Aktualizovat pokud</value>
  </data>
  <data name="ConfigBase_DBKeyName_Description" xml:space="preserve">
    <value>Klíč jednoznačně určující daný typ konfigurační třidy</value>
  </data>
  <data name="ConfigBase_DBKeyName_DisplayName" xml:space="preserve">
    <value>Klíč</value>
  </data>
  <data name="ConfigBase_DbVersion_Description" xml:space="preserve">
    <value>Verze, která je uložena v databázi.</value>
  </data>
  <data name="ConfigBase_DbVersion_DisplayName" xml:space="preserve">
    <value>Verze v databázi</value>
  </data>
  <data name="ConfigBase_Version_Description" xml:space="preserve">
    <value>Aktuální verze konfigurační třídy.</value>
  </data>
  <data name="ConfigBase_Version_DisplayName" xml:space="preserve">
    <value>Verze</value>
  </data>
  <data name="MenuPresenterCanteenColumn_CanteenList_DisplayName" xml:space="preserve">
    <value>Výdejny</value>
  </data>
  <data name="MenuPresenterColumnConfigBase_Position_DisplayName" xml:space="preserve">
    <value>Pozice</value>
  </data>
  <data name="MenuPresenterColumnConfigBase_Visible_DisplayName" xml:space="preserve">
    <value>Zobrazit</value>
  </data>
  <data name="MenuPresenterBehaviourColumnsConfig_Canteen_Description" xml:space="preserve">
    <value>Sloupec s číslem výdejny. Zároveň umožnuje definovat, pro jaké výdejny bude jídelní lístek zobrazen.</value>
  </data>
  <data name="MenuPresenterBehaviourColumnsConfig_Canteen_DisplayName" xml:space="preserve">
    <value>Výdejna</value>
  </data>
  <data name="MenuPresenterBehaviourColumnsConfig_NotprovidedFood_DisplayName" xml:space="preserve">
    <value>Množství nevydaných</value>
  </data>
  <data name="MenuPresenterBehaviourColumnsConfig_Price_DisplayName" xml:space="preserve">
    <value>Cena</value>
  </data>
  <data name="MenuPresenterBehaviourColumnsConfig_RestOfFood_DisplayName" xml:space="preserve">
    <value>Zůstatek porcí</value>
  </data>
  <data name="MenuPresenterBehaviourColumnsConfig_Unsold_DisplayName" xml:space="preserve">
    <value>Množství neprodaných</value>
  </data>
  <data name="MenuPresenterBehaviourMenuConfig_Canteen_Description" xml:space="preserve">
    <value>Výdejna, která bude zobrazena v horní části jídelníčku.</value>
  </data>
  <data name="MenuPresenterBehaviourMenuConfig_Canteen_DisplayName" xml:space="preserve">
    <value>Výdejna</value>
  </data>
  <data name="MenuPresenterBehaviourMenuConfig_Day_Description" xml:space="preserve">
    <value>Pro jaký den bude jídelníček zobrazen</value>
  </data>
  <data name="MenuPresenterBehaviourMenuConfig_Day_DisplayName" xml:space="preserve">
    <value>Jídelníček na</value>
  </data>
  <data name="MenuPresenterBehaviourMenuFilterConfig_Filter_Description" xml:space="preserve">
    <value>Druh a k němu seznam alternativ, ktere se budou zobrazovat.
Pokud není v seznamu žádná alternativa, znamená to, že se budou zobrazovat všechny alternativy svého druhu. Příklad: 1;2=1,2,3,4 - od druhu jídla 1 všechny alternativy, od druhu jídla 2 alternativy 1 až 4.</value>
  </data>
  <data name="MenuPresenterBehaviourMenuFilterConfig_Filter_DisplayName" xml:space="preserve">
    <value>Filtr</value>
  </data>
  <data name="MenuPresenterBehaviourMenuConfig_ShowCanteenText_Description" xml:space="preserve">
    <value>Bude v jídelníčku zobraz text s názvem výdejny?</value>
  </data>
  <data name="MenuPresenterBehaviourMenuConfig_ShowCanteenText_DisplayName" xml:space="preserve">
    <value>Zobrazovat výdejnu</value>
  </data>
  <data name="MenuPresenterBehaviourMenuConfig_Title_Description" xml:space="preserve">
    <value>Nadpis jídelníčku</value>
  </data>
  <data name="MenuPresenterBehaviourMenuConfig_Title_DisplayName" xml:space="preserve">
    <value>Nadpis</value>
  </data>
  <data name="MenuPresenterBehaviourRollConfig_RollBy_Description" xml:space="preserve">
    <value>O kolik pixelů se bude rolovat po uplynutí "Interval rolování"</value>
  </data>
  <data name="MenuPresenterBehaviourRollConfig_RollBy_DisplayName" xml:space="preserve">
    <value>Rolovat o</value>
  </data>
  <data name="MenuPresenterBehaviourRollConfig_RollInterval_Description" xml:space="preserve">
    <value>Interval, po kterem dojde k rolovaní o "Rolovat o" pixelů</value>
  </data>
  <data name="MenuPresenterBehaviourRollConfig_RollInterval_DisplayName" xml:space="preserve">
    <value>Interval rolování</value>
  </data>
  <data name="PresPointTimeoutConfig_LogoutDialogEx_Description" xml:space="preserve">
    <value>Doba zobrazení [s] podrobného odhlašovacího dialogu s seznamem neuskutečněných operací.</value>
  </data>
  <data name="PresPointTimeoutConfig_LogoutDialogEx_DisplayName" xml:space="preserve">
    <value>Doba zobrazení [s] podrobného odhlašovacího dialogu</value>
  </data>
  <data name="PresPointTimeoutConfig_LogoutDialog_Description" xml:space="preserve">
    <value>Doba zobrazení [s] odhlašovacího dialogu</value>
  </data>
  <data name="PresPointTimeoutConfig_LogoutDialog_DisplayName" xml:space="preserve">
    <value>Doba zobrazení [s] odhlašovacího dialogu</value>
  </data>
  <data name="PresPointTimeoutConfig_SessionTime_Description" xml:space="preserve">
    <value>Maximální čas [s], po který je klient přihlášen. Po jeho uplynutí dojde automaticky k jeho odhlášení.</value>
  </data>
  <data name="PresPointTimeoutConfig_SessionTime_DisplayName" xml:space="preserve">
    <value>Maximální čas [s], po který je klient přihlášen</value>
  </data>
  <data name="PresPointTimeoutConfig_SleepTime_Description" xml:space="preserve">
    <value>Doba [s], po které je klient odhlášen v případě jeho nečinnosti.</value>
  </data>
  <data name="PresPointTimeoutConfig_SleepTime_DisplayName" xml:space="preserve">
    <value>Doba [s], po které je klient odhlášen v případě jeho nečinnosti</value>
  </data>
  <data name="MenuPresenterBehaviourColumnsConfig_Price_Description" xml:space="preserve">
    <value>Cena</value>
  </data>
  <data name="MenuPresenterBehaviourColumnsConfig_NotprovidedFood_Description" xml:space="preserve">
    <value>Množství nevydaných</value>
  </data>
  <data name="MenuPresenterBehaviourColumnsConfig_Unsold_Description" xml:space="preserve">
    <value>Množství neprodaných</value>
  </data>
  <data name="MenuPresenterBehaviourColumnsConfig_RestOfFood_Description" xml:space="preserve">
    <value>Zůstatek porcí</value>
  </data>
  <data name="AutoUpdaterBehaviourImpersonationConfig_User_Description" xml:space="preserve">
    <value>Uživatelský účet, pod kterým se spouští instalace aktualizačního balíčku.
Použije se pouze v případě, že je zapnuto přihlašování jako zadaný uživatel.</value>
  </data>
  <data name="AutoUpdaterBehaviourImpersonationConfig_User_DisplayName" xml:space="preserve">
    <value>Uživatelský účet</value>
  </data>
  <data name="AutoUpdaterBehaviourImpersonationConfig_Domain_DisplayName" xml:space="preserve">
    <value>Doména</value>
  </data>
  <data name="AutoUpdaterBehaviourImpersonationConfig_Domain_Description" xml:space="preserve">
    <value>Doména, pod kterou se spouští instalace aktualizačního balíčku.
Použije se pouze v případě, že je zapnuto přihlašování jako zadaný uživatel.</value>
  </data>
  <data name="AutoUpdaterBehaviourImpersonationConfig_Password_DisplayName" xml:space="preserve">
    <value>Heslo</value>
  </data>
  <data name="AutoUpdaterBehaviourImpersonationConfig_Password_Description" xml:space="preserve">
    <value>Heslo uživatele, pod kterým se spouští instalace aktualizačního balíčku.
Použije se pouze v případě, že je zapnuto přihlašování jako zadaný uživatel.</value>
  </data>
  <data name="CashDeskBehaviourMiscellaneousConfig_ErrorDisplayTimeout_DisplayName" xml:space="preserve">
    <value>Timeout zobrazení chyb [s]</value>
  </data>
  <data name="CashDeskBehaviourMiscellaneousConfig_ErrorDisplayTimeout_Description" xml:space="preserve">
    <value>Čas, po který jsou zobrazena chybová hlášení kasy v oblasti pod paragonem.</value>
  </data>
  <data name="StartKeyConfigBase_StartKey_Description" xml:space="preserve">
    <value>Nastavuje startovací znak čtečky, který čtečka posílá jako svůj počáteční znak.</value>
  </data>
  <data name="StartKeyConfigBase_StartKey_DisplayName" xml:space="preserve">
    <value>Startovací znak</value>
  </data>
  <data name="MenuPresenterAppearanceTextConfig_TitleSize_Description" xml:space="preserve">
    <value>Velikost nadpisu</value>
  </data>
  <data name="MenuPresenterAppearanceTextConfig_TitleSize_DisplayName" xml:space="preserve">
    <value>Velikost nadpisu</value>
  </data>
  <data name="MenuPresenterAppearanceTextConfig_TitleWeight_Description" xml:space="preserve">
    <value>Šířka písma nadpisu</value>
  </data>
  <data name="MenuPresenterAppearanceTextConfig_TitleWeight_DisplayName" xml:space="preserve">
    <value>Šířka písma nadpisu</value>
  </data>
  <data name="MenuPresenterAppearanceTextConfig_RightTitleSize_Description" xml:space="preserve">
    <value>Velikost textu na pravé straně v titulku jídelníčku. Text: Na Den: xxx, Výdejna: xxx</value>
  </data>
  <data name="MenuPresenterAppearanceTextConfig_RightTitleSize_DisplayName" xml:space="preserve">
    <value>Velikost textu na pravé straně v titulku jídelníčku</value>
  </data>
  <data name="MenuPresenterAppearanceTextConfig_MessageSize_Description" xml:space="preserve">
    <value>Velikost oblasti se zprávou pro strávníky</value>
  </data>
  <data name="MenuPresenterAppearanceTextConfig_MessageSize_DisplayName" xml:space="preserve">
    <value>Velikost oblasti se zprávou pro strávníky</value>
  </data>
  <data name="CashDeskSaleConfig_EanPricePrefix_Description" xml:space="preserve">
    <value>Startovací prefixy čárových kódů pro prodej váhou. Složený čárový kód obsahuje informaci o kódu zboží a hmotnosti, dopočítává se cena
Standardní nastavení je 27.</value>
  </data>
  <data name="CashDeskSaleConfig_EanPricePrefix_DisplayName" xml:space="preserve">
    <value>Prefixy při prodeji EAN cenou</value>
  </data>
  <data name="CashDeskSaleConfig_EanWeightPrefix_Description" xml:space="preserve">
    <value>Startovací prefixy čárových kódů pro prodej cenou. Složený čárový kód obsahuje informaci o kódu zboží a ceně, dopočítává se hmotnost.
Standardní nastavení je 29.</value>
  </data>
  <data name="CashDeskSaleConfig_EanWeightPrefix_DisplayName" xml:space="preserve">
    <value>Prefixy při prodeji EAN váhou</value>
  </data>
  <data name="CashDeskSaleConfig_EnableClientRelogin_Description" xml:space="preserve">
    <value>Pokud je povoleno, je možno přehlásit strávníka v paragonu, který nemá položky i pokud již byl předtím přihlášen jiný klient.
Pokud je zakázáno, nelze strávníka přehlásit a pokladní musí explicitně založit nový paragon a tím strávníka odhlásit. Zakazuje se u zákazníků, kde je příliš rychlý přísun klientů a mohlo by se stát, že se bez vědomí pokladní přihlásí další klient.</value>
  </data>
  <data name="CashDeskSaleConfig_EnableClientRelogin_DisplayName" xml:space="preserve">
    <value>Přehlášení klienta v prázdném paragonu</value>
  </data>
  <data name="CashDeskBehaviourUserInterfaceConfig_InterfaceType_Description" xml:space="preserve">
    <value>Určuje, jaké rozhraní Kasy bude zobrazeno.</value>
  </data>
  <data name="CashDeskBehaviourUserInterfaceConfig_InterfaceType_DisplayName" xml:space="preserve">
    <value>Typ rozhraní</value>
  </data>
  <data name="MenuPresenterAppearanceBackgroundConfig_TextColor_DisplayName" xml:space="preserve">
    <value>Barva textu</value>
  </data>
  <data name="MenuPresenterAppearanceBackgroundConfig_TextColor_Description" xml:space="preserve">
    <value>Barva textu</value>
  </data>
  <data name="MenuPresenterAppearanceBackgroundConfig_BackgroundColor_Description" xml:space="preserve">
    <value>Barva pozadí</value>
  </data>
  <data name="MenuPresenterAppearanceBackgroundConfig_BackgroundColor_DisplayName" xml:space="preserve">
    <value>Barva pozadí</value>
  </data>
  <data name="MenuPresenterAppearanceBackgroundConfig_OuterBorderColor_DisplayName" xml:space="preserve">
    <value>Barva orámování</value>
  </data>
  <data name="MenuPresenterAppearanceBackgroundConfig_OuterBorderColor_Description" xml:space="preserve">
    <value>Barva orámování</value>
  </data>
  <data name="CashDeskOfflineConfig_Enabled_Description" xml:space="preserve">
    <value>Pokud je zapnuto, je Kasa schopna fungovat v režimu offline.</value>
  </data>
  <data name="CashDeskOfflineConfig_Enabled_DisplayName" xml:space="preserve">
    <value>Povolit režim offline</value>
  </data>
  <data name="CashDeskOfflineConfig_ReplicationInterval_DisplayName" xml:space="preserve">
    <value>Interval replikace [min]</value>
  </data>
  <data name="CashDeskOfflineConfig_ReplicationInterval_Description" xml:space="preserve">
    <value>Perioda, ve které se opakuje replikace tabulek ze serveru do místní offline databáze.</value>
  </data>
  <data name="CashDeskOfflineConfig_ReplicationRange_DisplayName" xml:space="preserve">
    <value>Rozsah replikace [dny]</value>
  </data>
  <data name="CashDeskOfflineConfig_ReplicationRange_Description" xml:space="preserve">
    <value>Určuje rozsah, pro který se budou replikovat data Kasy do offline. 
Tj. na kolik dnů dopředu se budou data replikovat. Týká se zejména jídelníčku.</value>
  </data>
  <data name="MenuPresenterAppearanceMenuConfig_BackgroundColor_Description" xml:space="preserve">
    <value>Barva pozadí</value>
  </data>
  <data name="MenuPresenterAppearanceMenuConfig_BackgroundColor_DisplayName" xml:space="preserve">
    <value>Barva pozadí</value>
  </data>
  <data name="MenuPresenterAppearanceMenuConfig_TextColor_DisplayName" xml:space="preserve">
    <value>Barva písma</value>
  </data>
  <data name="MenuPresenterAppearanceMenuConfig_TextColor_Description" xml:space="preserve">
    <value>Barva písma</value>
  </data>
  <data name="MenuPresenterAppearanceMenuConfig_AlternateRowColor_DisplayName" xml:space="preserve">
    <value>Barva sudých řádků</value>
  </data>
  <data name="MenuPresenterAppearanceMenuConfig_AlternateRowColor_Description" xml:space="preserve">
    <value>Barva sudých řádků</value>
  </data>
  <data name="MenuPresenterAppearanceMenuConfig_FontSize_Description" xml:space="preserve">
    <value>Velikost písma</value>
  </data>
  <data name="MenuPresenterAppearanceMenuConfig_FontSize_DisplayName" xml:space="preserve">
    <value>Velikost písma</value>
  </data>
  <data name="MenuPresenterAppearanceMenuConfig_DualCenaFontSize_Description" xml:space="preserve">
    <value>Velikost písma u duální ceny</value>
  </data>
  <data name="MenuPresenterAppearanceMenuConfig_DualCenaFontSize_DisplayName" xml:space="preserve">
    <value>Velikost písma u duální ceny</value>
  </data>
  <data name="MenuPresenterAppearanceMenuConfig_RowFontWeight_Description" xml:space="preserve">
    <value>Šířka písma</value>
  </data>
  <data name="MenuPresenterAppearanceMenuConfig_RowFontWeight_DisplayName" xml:space="preserve">
    <value>Šířka písma</value>
  </data>
  <data name="MenuPresenterAppearanceLogoConfig_FileName_Description" xml:space="preserve">
    <value>Relativní cesta k souboru s logem. Odzkoušen pouze formát png, který se zde hodí nejvíce díky možnosti použít transparentní pozadí.</value>
  </data>
  <data name="MenuPresenterAppearanceLogoConfig_FileName_DisplayName" xml:space="preserve">
    <value>Název souboru s logem</value>
  </data>
  <data name="MenuPresenterAppearanceLogoConfig_MaxHeight_Description" xml:space="preserve">
    <value>Maximální výška</value>
  </data>
  <data name="MenuPresenterAppearanceLogoConfig_MaxHeight_DisplayName" xml:space="preserve">
    <value>Maximální výška</value>
  </data>
  <data name="MenuPresenterAppearanceLogoConfig_MaxWidth_Description" xml:space="preserve">
    <value>Maximální šířka</value>
  </data>
  <data name="MenuPresenterAppearanceLogoConfig_MaxWidth_DisplayName" xml:space="preserve">
    <value>Maximální šířka</value>
  </data>
  <data name="CashDeskBehaviourTouchConfig_MealColumnCount_Description" xml:space="preserve">
    <value>Určuje, kolik sloupců jídel se zobrazí na obrazovce. Více sloupců znamená možnost zobrazení více tlačítek ale zároveň menší tlačítka. </value>
  </data>
  <data name="CashDeskBehaviourTouchConfig_MealColumnCount_DisplayName" xml:space="preserve">
    <value>Počet sloupců jídel</value>
  </data>
  <data name="CashDeskBehaviourTouchConfig_DisplayGoodPrices_Description" xml:space="preserve">
    <value>Pokud je zapnuto, na tlačítkách se zobrazují ceny sortimentu. Ceny jídel se nezobrazují nikdy.</value>
  </data>
  <data name="CashDeskBehaviourTouchConfig_DisplayGoodPrices_DisplayName" xml:space="preserve">
    <value>Zobrazovat ceny sortimentu</value>
  </data>
  <data name="CashDeskBehaviourTaraConfig_ButtonEnabled_DisplayName" xml:space="preserve">
    <value>Povolit tlačítko Tara</value>
  </data>
  <data name="CashDeskBehaviourTaraConfig_ButtonEnabled_Description" xml:space="preserve">
    <value>Určuje, zda bude povoleno nebo zakázáno tlačítko Tara.</value>
  </data>
  <data name="CashDeskBehaviourTaraConfig_TaraActive_Description" xml:space="preserve">
    <value>Pokud je povoleno, je tárování po startu Kasy zapnuto, v opačném případě vypnuto.
Tárování se používá při prodeji vážených jídel a sortimentu.</value>
  </data>
  <data name="CashDeskBehaviourTaraConfig_TaraActive_DisplayName" xml:space="preserve">
    <value>Zapnout tárování po startu</value>
  </data>
  <data name="GlobalBehaviourMessageForClientsConfig_MessageForClients_Description" xml:space="preserve">
    <value>Doplňková zpráva pro klienty, zobrazená na úvodní obrazovce PM.</value>
  </data>
  <data name="GlobalBehaviourMessageForClientsConfig_MessageForClients_DisplayName" xml:space="preserve">
    <value>Doplňková zpráva pro klienty</value>
  </data>
  <data name="CashDeskBehaviourLockConfig_UserInterfaceLockEnabled_Description" xml:space="preserve">
    <value>Povolit nebo zakázat tlačítko Zamkni kasu</value>
  </data>
  <data name="CashDeskBehaviourLockConfig_UserInterfaceLockEnabled_DisplayName" xml:space="preserve">
    <value>Povolit tlačítko Zamkni kasu</value>
  </data>
  <data name="CashDeskBehaviourLockConfig_InactivityLockEnabled_DisplayName" xml:space="preserve">
    <value>Automatické uzamčení při nečinnosti</value>
  </data>
  <data name="CashDeskBehaviourLockConfig_InactivityLockEnabled_Description" xml:space="preserve">
    <value>Povolit uzamykání při nečinnosti kasy  - spustí se chránič obrazovky s nutností nového přihlášení obsluhy.</value>
  </data>
  <data name="CashDeskBehaviourLockConfig_InactivityTimeout_DisplayName" xml:space="preserve">
    <value>Timeout uzamčení při nečinnosti [s]</value>
  </data>
  <data name="CashDeskBehaviourLockConfig_InactivityTimeout_Description" xml:space="preserve">
    <value>Po jaké době nečinnosti se kasa automaticky uzamkne  - spustí se chránič obrazovky s nutností nového přihlášení obsluhy.</value>
  </data>
  <data name="GlobalHwCardSerialReaderConfig_ReaderType_Description" xml:space="preserve">
    <value>Nastavuje konkrétní typ čtečky.</value>
  </data>
  <data name="GlobalHwCardSerialReaderConfig_ReaderType_DisplayName" xml:space="preserve">
    <value>Typ čtečky</value>
  </data>
  <data name="GlobalHwCardSerialReaderConfig_Settings_Description" xml:space="preserve">
    <value>Nastavení různých parametrů čtečky, které přímo závisí na zvoleném typu čtečky.</value>
  </data>
  <data name="GlobalHwCardSerialReaderConfig_Settings_DisplayName" xml:space="preserve">
    <value>Nastavení čtečky</value>
  </data>
  <data name="GlobalHwClientDisplayConfig_ClientDisplayType_Description" xml:space="preserve">
    <value>Nastavení typu zákaznického displeje.</value>
  </data>
  <data name="GlobalHwClientDisplayConfig_ClientDisplayType_DisplayName" xml:space="preserve">
    <value>Typ zákaznického displeje</value>
  </data>
  <data name="GlobalHwClientDisplayConfig_Settings_DisplayName" xml:space="preserve">
    <value>Nastavení</value>
  </data>
  <data name="GlobalHwClientDisplayConfig_Settings_Description" xml:space="preserve">
    <value>Konfigurace parametrů zákaznického displeje</value>
  </data>
  <data name="MenuPresenterAppearanceMenuHeaderConfig_BackgroundColor_Description" xml:space="preserve">
    <value>Barva pozadí</value>
  </data>
  <data name="MenuPresenterAppearanceMenuHeaderConfig_BackgroundColor_DisplayName" xml:space="preserve">
    <value>Barva pozadí</value>
  </data>
  <data name="MenuPresenterAppearanceMenuHeaderConfig_FontSize_DisplayName" xml:space="preserve">
    <value>Velikost písma</value>
  </data>
  <data name="MenuPresenterAppearanceMenuHeaderConfig_FontSize_Description" xml:space="preserve">
    <value>Velikost písma</value>
  </data>
  <data name="MenuPresenterAppearanceMenuHeaderConfig_FontWeight_Description" xml:space="preserve">
    <value>Šířka písma</value>
  </data>
  <data name="MenuPresenterAppearanceMenuHeaderConfig_FontWeight_DisplayName" xml:space="preserve">
    <value>Šířka písma</value>
  </data>
  <data name="CashDeskAccoutingMidnightConfig_AutoReloadMenu_Description" xml:space="preserve">
    <value>Definuje, co se stane při překročení účetní půlnoci. 
Pokud je zapnuto, jídelníček se automaticky přenačte bez zásahu uživatele (uživatel je pouze informován). Přenačtení proběhne až po uložení všech paragonů s rozmarkovanými jídly.
Pokud je vypnuto, zobrazí se pouze hlášení o nutnosti přenačíst jídelníček. </value>
  </data>
  <data name="CashDeskAccoutingMidnightConfig_AutoReloadMenu_DisplayName" xml:space="preserve">
    <value>Automaticky přenačíst jídelníček</value>
  </data>
  <data name="PresPointAppearanceFontConfig_ButtonGroupHeaderFont_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceFontConfig_ButtonGroupHeaderFont_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceFontConfig_GridTextFontSize_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceFontConfig_GridTextFontSize_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceFontConfig_OrdersRowHeight_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceFontConfig_OrdersRowHeight_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceFontConfig_MenuRowHeight_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceFontConfig_MenuRowHeight_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceFontConfig_BurzaRowHeight_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceFontConfig_BurzaRowHeight_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceFontConfig_EnterTextFont_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceFontConfig_EnterTextFont_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_FormBackgroundColor1_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_FormBackgroundColor1_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_FormBackgroundColor2_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_FormBackgroundColor2_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_ErrorFormBackgroundColor1_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_ErrorFormBackgroundColor1_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_ErrorFormBackgroundColor2_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_ErrorFormBackgroundColor2_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_BorderTransparency_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_BorderTransparency_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_StartFromBorderColor_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_RightTitleColor_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_RightTitleColor_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_LeftTitleColor_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_LeftTitleColor_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_SekundarniTextColor_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_SekundarniTextColor_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_DefaultTextColor_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_DefaultTextColor_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_ButtonTextColor_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_ButtonTextColor_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_DisabledTextRowColor_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_DisabledTextRowColor_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_DisabledButtonTextColor_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_DisabledButtonTextColor_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_ButtonBorderColorNoTrans_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_ButtonBorderColorNoTrans_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_RightButtonColor1_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_RightButtonColor1_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_RightButtonColor2_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_RightButtonColor2_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_LeftButtonColor1_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_LeftButtonColor1_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_LeftButtonColor2_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_LeftButtonColor2_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_NormalButtonColor1_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_NormalButtonColor1_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_NormalButtonColor2_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_NormalButtonColor2_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_SelectedButtonColor1_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_SelectedButtonColor1_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_SelectedButtonColor2_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_SelectedButtonColor2_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_AlternateNormalButtonColor1_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_AlternateNormalButtonColor1_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_ScrollButtonColor1_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_ScrollButtonColor1_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_ScrollButtonColor2_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_ScrollButtonColor2_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_DatumErrorColor_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_DatumErrorColor_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_DatumColor_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_DatumColor_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_DisabledButtonColor_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_DisabledButtonColor_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_EnterColor_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_EnterColor_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_GridBackColor_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_GridBackColor_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_RightGridBackground_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_RightGridBackground_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_RightGridRowColor1_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_RightGridRowColor1_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_RightGridRowColor2_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_RightGridRowColor2_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_LeftGridRowColor1_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_LeftGridRowColor1_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_LeftGridRowColor2_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_LeftGridRowColor2_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_RightGridBorderColorNoTransp_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_RightGridBorderColorNoTransp_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_LeftGridBorderColorNoTransp_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_LeftGridBorderColorNoTransp_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_MainGridSelectedRowColor_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_MainGridSelectedRowColor_Description" xml:space="preserve">
    <value />
  </data>
  <data name="AutoUpdaterBehaviourMonitorConfig_RescanInterval_DisplayName" xml:space="preserve">
    <value>Interval přenačtení aktualizací</value>
  </data>
  <data name="AutoUpdaterBehaviourMonitorConfig_RescanInterval_Description" xml:space="preserve">
    <value>Interval, po kterém se otestuje, zda neexistuje v databázi nová aktualizace.</value>
  </data>
  <data name="AutoUpdaterBehaviourMonitorConfig_TrayIcon_DisplayName" xml:space="preserve">
    <value>Tray ikona</value>
  </data>
  <data name="CashDeskAccoutingMidnightConfig_MidnightOffset_DisplayName" xml:space="preserve">
    <value>Posun účetní půlnoci [hod]</value>
  </data>
  <data name="CashDeskAccoutingMidnightConfig_MidnightOffset_Description" xml:space="preserve">
    <value>Posun účetní půlnoci pro testování neuzavřených pohybů.
V hodinách prodeje přes půlnoc. 
Pokud chcete posunout účetní půlnoc na 3 hodiny ráno, zadejte 3. 
Pokud chcete posunout účetní půlnoc na 2:30 hodiny ráno, zadejte 2.5.</value>
  </data>
  <data name="CashDeskHwClientDisplayConfig_LongDisplayTimeout_Description" xml:space="preserve">
    <value>Tato hodnota se použije pro zobrazení celkové ceny, nemá-li se vracet částka v hotovosti.</value>
  </data>
  <data name="CashDeskHwClientDisplayConfig_LongDisplayTimeout_DisplayName" xml:space="preserve">
    <value>Timeout dlouhého zobrazení [s]</value>
  </data>
  <data name="CashDeskHwClientDisplayConfig_ShortDisplayTimeout_Description" xml:space="preserve">
    <value>Tato hodnota se použije pro zobrazení celkové ceny, má-li se vracet částka v hotovosti.</value>
  </data>
  <data name="CashDeskHwClientDisplayConfig_ShortDisplayTimeout_DisplayName" xml:space="preserve">
    <value>Timeout krátkého zobrazení [s]</value>
  </data>
  <data name="CashDeskSaleConfig_LoadMenuAtStart_DisplayName" xml:space="preserve">
    <value>Načíst jídelníček při startu</value>
  </data>
  <data name="CashDeskSaleConfig_LoadMenuAtStart_Description" xml:space="preserve">
    <value>Pokud je povoleno, po startu Kasy je automaticky načten jídelníček. </value>
  </data>
  <data name="CashDeskSaleConfig_ReadMealOrdersAtStart_Description" xml:space="preserve">
    <value>Po přihlášení klienta jsou do paragonu načteny dosud nevydané objednávky z jiných zařízení (například čteček), při prodeji se provede pouze jejich výdej.</value>
  </data>
  <data name="CashDeskSaleConfig_RestrictCanteenAccess_Description" xml:space="preserve">
    <value>Pokud je povoleno, jsou hlídána pravidla přístupu do výdejny. 
Pokud je zakázáno, pak nejsou pravidla přístupu hlídána.</value>
  </data>
  <data name="CashDeskSaleConfig_RestrictCanteenAccess_DisplayName" xml:space="preserve">
    <value>Respektovat pravidla přístupu do jídelny</value>
  </data>
  <data name="MenuPresenterColumnConfigBase_Position_Description" xml:space="preserve">
    <value>Pozice sloupce na obrazovce. Sloupce jsou seřazeny dle pozice vzestupně zleva doprava.</value>
  </data>
  <data name="MenuPresenterColumnConfigBase_Visible_Description" xml:space="preserve">
    <value>Určuje, zda bude zobrazen sloupec v jídelníčku.</value>
  </data>
  <data name="GlobalBehaviourPricesConfig_DualPrices_Description" xml:space="preserve">
    <value>Nastavení duálních cen</value>
  </data>
  <data name="GlobalBehaviourPricesConfig_DualPrices_DisplayName" xml:space="preserve">
    <value>Duální ceny</value>
  </data>
  <data name="GlobalBehaviourClientConfig_ClientDisplayedIdentType_Description" xml:space="preserve">
    <value>Způsob zobrazení identifikace strávníka.</value>
  </data>
  <data name="GlobalBehaviourClientConfig_ClientDisplayedIdentType_DisplayName" xml:space="preserve">
    <value>Zobrazení identifikace strávníka</value>
  </data>
  <data name="MenuPresenterCanteenColumn_CanteenList_Description" xml:space="preserve">
    <value>Výdejny</value>
  </data>
  <data name="GlobalRulesCashOperationsConfig_CashDeposit_DisplayName" xml:space="preserve">
    <value>Povolit vklad hotovosti</value>
  </data>
  <data name="GlobalRulesCashOperationsConfig_CashDeposit_Description" xml:space="preserve">
    <value>Povolit nebo zakázat vklad hotovosti na účet. V Kase povoluje nebo zakazuje zobrazení tlačítka Vklad.</value>
  </data>
  <data name="GlobalRulesCashOperationsConfig_CashWithdrawal_DisplayName" xml:space="preserve">
    <value>Povolit výběr hotovosti</value>
  </data>
  <data name="GlobalRulesCashOperationsConfig_CashWithdrawal_Description" xml:space="preserve">
    <value>Povolit nebo zakázat výběr hotovosti z účtu. V Kase povoluje nebo zakazuje zobrazení tlačítka Výběr.</value>
  </data>
  <data name="GlobalRulesRoundingConfig_SalesSlipRoundingRule_DisplayName" xml:space="preserve">
    <value>Celková cena paragonu</value>
  </data>
  <data name="GlobalRulesRoundingConfig_SalesSlipRoundingRule_Description" xml:space="preserve">
    <value>Pravidla pro zaokrouhlování celkové ceny paragonu</value>
  </data>
  <data name="GlobalRulesRoundingConfig_SalesSlipItemRoundingRule_DisplayName" xml:space="preserve">
    <value>Položky paragonu</value>
  </data>
  <data name="GlobalRulesRoundingConfig_SalesSlipItemRoundingRule_Description" xml:space="preserve">
    <value>Pravidla pro zaokrouhlování jednotlivých položek paragonu</value>
  </data>
  <data name="GlobalRulesRoundingConfig_CurrencyRoundingRule_DisplayName" xml:space="preserve">
    <value>Měna</value>
  </data>
  <data name="GlobalRulesRoundingConfig_CurrencyRoundingRule_Description" xml:space="preserve">
    <value>Pravidla pro zaokrouhlování měny</value>
  </data>
  <data name="RoundingRuleBase_Enabled_DisplayName" xml:space="preserve">
    <value>Zapnout zaokrouhlování</value>
  </data>
  <data name="RoundingRuleBase_Enabled_Description" xml:space="preserve">
    <value>Povolit nebo zakázat zaokrouhlování. Pokud je zakázáno, ostatní pravidla nemají platnost.</value>
  </data>
  <data name="RoundingRuleBase_Logic_DisplayName" xml:space="preserve">
    <value>Logika zaokrouhlování</value>
  </data>
  <data name="RoundingRuleBase_Decimals_Description" xml:space="preserve">
    <value>Počet desetinných míst, na které se zaokrouhluje. Pokud chcete zaokrouhlit čísla před desetinnou čárkou, zadejte zápornou hodnotu.
Zaokrouhlit na desetiny = 1
Zaokrouhlit na celé čísla = 0
Zaokrouhlit na desítky = -1</value>
  </data>
  <data name="RoundingRuleBase_Decimals_DisplayName" xml:space="preserve">
    <value>Počet desetinných míst</value>
  </data>
  <data name="CashDeskHwClientDisplayConfig_DisplayText_Description" xml:space="preserve">
    <value>Text, který se zobrazí na zákaznickém displeji, pokud je způsob zobrazení nastaven na "Text".</value>
  </data>
  <data name="CashDeskHwClientDisplayConfig_DisplayText_DisplayName" xml:space="preserve">
    <value>Text k zobrazení</value>
  </data>
  <data name="GlobalBehaviourSalesSlipPrintConfig_Footer_Description" xml:space="preserve">
    <value>Text, který se tiskne v patičce každého paragonu.
Editujte v editoru po rozbalení Combo boxu.

Každý řádek může mít tvar: Text|Funkce. Pokud není uvedena Funkce, bude text zarovnán doprostřed.

Přehled funkcí:
Znak|REPL - vytiskne řadu znaků na celou šířku papíru
Text|PADL - zarovná text mezerami zleva, text bude tudíž umístěn vpravo.
Text|PADR - zarovná text mezerami zprava, text bude tudíž umístěn vlevo.</value>
  </data>
  <data name="GlobalBehaviourSalesSlipPrintConfig_Footer_DisplayName" xml:space="preserve">
    <value>Patička</value>
  </data>
  <data name="GlobalBehaviourSalesSlipPrintConfig_Header_Description" xml:space="preserve">
    <value>Text, který se tiskne v hlavičce každého paragonu.
Editujte v editoru po rozbalení Combo boxu.

Každý řádek může mít tvar: Text|Funkce. Pokud není uvedena Funkce, bude text zarovnán doprostřed.

Přehled funkcí:
Znak|REPL - vytiskne řadu znaků na celou šířku papíru
Text|PADL - zarovná text mezerami zleva, text bude tudíž umístěn vpravo.
Text|PADR - zarovná text mezerami zprava, text bude tudíž umístěn vlevo.</value>
  </data>
  <data name="GlobalBehaviourSalesSlipPrintConfig_Header_DisplayName" xml:space="preserve">
    <value>Hlavička</value>
  </data>
  <data name="CashDeskSaleConfig_AllowCashSale_DisplayName" xml:space="preserve">
    <value>Povolit prodej za hotové</value>
  </data>
  <data name="CashDeskSaleConfig_AllowCashSale_Description" xml:space="preserve">
    <value>Pokud je povoleno, je povolen prodej za hotové. 
Pokud je zakázáno, je povolen prodej pouze na karty.</value>
  </data>
  <data name="CashDeskSaleConfig_CategoryWithoutSubsidy_Description" xml:space="preserve">
    <value>Určuje, za jakou cenu se prodávají teplá jídla po vyčerpání porcí v primární i sekundární kategorii dotace.</value>
  </data>
  <data name="CashDeskSaleConfig_CategoryWithoutSubsidy_DisplayName" xml:space="preserve">
    <value>Kategorie bez dotace</value>
  </data>
  <data name="PresPointBehaviourUserInterfaceConfig_InterfaceType_DisplayName" xml:space="preserve">
    <value>Uživatelské rozhraní</value>
  </data>
  <data name="PresPointBehaviourUserInterfaceConfig_InterfaceType_Description" xml:space="preserve">
    <value>Typ uživatelského rozhraní.</value>
  </data>
  <data name="PresPointBehaviourClientInfoConfig_ShowSubsidies_DisplayName" xml:space="preserve">
    <value>Zobrazovat jídla s dotací</value>
  </data>
  <data name="PresPointBehaviourClientInfoConfig_ShowSubsidies_Description" xml:space="preserve">
    <value>Určuje, zda se budou zobrazovat jídla s dotací v hlavičce klienta.</value>
  </data>
  <data name="PresPointBehaviourClientInfoConfig_ShowDaySum_DisplayName" xml:space="preserve">
    <value>Zobrazovat denní součet</value>
  </data>
  <data name="PresPointBehaviourClientInfoConfig_ShowDaySum_Description" xml:space="preserve">
    <value>Denní součet je zobrazován pro aktuálne vybrané datum. Jedná se o celkovou částku, za kterou jso pro dané datum objednána jídla.</value>
  </data>
  <data name="AutoUpdaterBehaviourMonitorConfig_TrayIcon_Description" xml:space="preserve">
    <value>Určuje, zda se bude zobrazovat tray ikona.</value>
  </data>
  <data name="PresPointBehaviourMenuConfig_MenuFor_Description" xml:space="preserve">
    <value>Určuje, pro jaký den bude zobrazen jídelníček po přihlášení klienta.</value>
  </data>
  <data name="PresPointBehaviourMenuConfig_MenuFor_DisplayName" xml:space="preserve">
    <value>Jídelníček na</value>
  </data>
  <data name="GlobalSystemOwnerConfig_OwnerId_DisplayName" xml:space="preserve">
    <value>Zkratka vlastníka licence (prefix zákazníka)</value>
  </data>
  <data name="GlobalSystemOwnerConfig_OwnerId_Description" xml:space="preserve">
    <value>Jedinečný řetězec, který identifikuje majitele licence. Dříve se nazývalo prefix zákazníka. </value>
  </data>
  <data name="SerialDeviceSettings_PortSettings_Description" xml:space="preserve">
    <value>Nastavení parametrů portu sériové čtečky.</value>
  </data>
  <data name="SerialDeviceSettings_PortSettings_DisplayName" xml:space="preserve">
    <value>Port</value>
  </data>
  <data name="SerialDeviceSettings_ReadTimeOut_DisplayName" xml:space="preserve">
    <value>Timeout čtení [mSec]</value>
  </data>
  <data name="SerialDeviceSettings_ReadTimeOut_Description" xml:space="preserve">
    <value>Nastavení timeoutu v milisekundách pro čtení z portu.</value>
  </data>
  <data name="SerialDeviceSettings_WriteTimeOut_Description" xml:space="preserve">
    <value>Nastavení timeoutu v milisekundách pro zápis na port.</value>
  </data>
  <data name="SerialDeviceSettings_WriteTimeOut_DisplayName" xml:space="preserve">
    <value>Timeout zápisu [mSec]</value>
  </data>
  <data name="CashDeskSaleConfig_ReadMealOrdersAtStart_DisplayName" xml:space="preserve">
    <value>Načíst nevydané objednávky po přihlášení</value>
  </data>
  <data name="CashDeskStockModuleConfig_ModuleType_DisplayName" xml:space="preserve">
    <value>Typ modulu</value>
  </data>
  <data name="CashDeskStockModuleConfig_ModuleType_Description" xml:space="preserve">
    <value>Volba typu modulu sklady</value>
  </data>
  <data name="CashDeskHwClientDisplayConfig_DisplayType_DisplayName" xml:space="preserve">
    <value>Typ zobrazení</value>
  </data>
  <data name="StockModuleSettingsBase_Settings_DisplayName" xml:space="preserve">
    <value>Nastavení</value>
  </data>
  <data name="StockModuleSettingsBase_Settings_Description" xml:space="preserve">
    <value>Nastavení modulu skladů Orion</value>
  </data>
  <data name="StockModuleSettingsOrion_PluFileName_Description" xml:space="preserve">
    <value>Název souboru s importem zboží.</value>
  </data>
  <data name="StockModuleSettingsOrion_PluFileName_DisplayName" xml:space="preserve">
    <value>Název importního souboru zboží</value>
  </data>
  <data name="StockModuleSettingsOrion_ExportFileName_Description" xml:space="preserve">
    <value>Název souboru s exportem prodejů.</value>
  </data>
  <data name="StockModuleSettingsOrion_ExportFileName_DisplayName" xml:space="preserve">
    <value>Název exportního souboru prodejů</value>
  </data>
  <data name="StockModuleSettingsOrion_InputDirName_Description" xml:space="preserve">
    <value>Odsud se importuje zboží.</value>
  </data>
  <data name="StockModuleSettingsOrion_InputDirName_DisplayName" xml:space="preserve">
    <value>Adresář pro import zboží</value>
  </data>
  <data name="StockModuleSettingsOrion_ArchiveDirName_Description" xml:space="preserve">
    <value>Zde se archivují importované soubory zboží.</value>
  </data>
  <data name="StockModuleSettingsOrion_ArchiveDirName_DisplayName" xml:space="preserve">
    <value>Adresář pro archiv importu zboží</value>
  </data>
  <data name="PosPrinterConfigBase_SpoolerName_DisplayName" xml:space="preserve">
    <value>Název tiskárny</value>
  </data>
  <data name="PosPrinterConfigBase_SpoolerName_Description" xml:space="preserve">
    <value>Port nebo název tiskárny, nakonfiguravané ve Windows. Musí odpovídat názvu tiskárny Windows. 
Pozor: Pro síťové tiskárny musí být uvedeno včetně názvu serveru, tedy \\server\tiskárna.</value>
  </data>
  <data name="PosPrinterConfigBase_PrinterType_DisplayName" xml:space="preserve">
    <value>Typ tiskárny</value>
  </data>
  <data name="PosPrinterConfigBase_PrinterType_Description" xml:space="preserve">
    <value>Typ tiskárny z Anete.DevicesConfig.xml. Určuje sadu příkazů, posílaných do tiskárny.</value>
  </data>
  <data name="VatRoundingRule_Enabled_Description" xml:space="preserve">
    <value>Povolit nebo zakázat zaokrouhlování. Pokud je zakázáno, ostatní pravidla nemají platnost.</value>
  </data>
  <data name="VatRoundingRule_Enabled_DisplayName" xml:space="preserve">
    <value>Zapnout zaokrouhlování</value>
  </data>
  <data name="VatRoundingRule_Logic_DisplayName" xml:space="preserve">
    <value>Logika zaokrouhlování</value>
  </data>
  <data name="VatRoundingRule_Decimals_Description" xml:space="preserve">
    <value>Počet desetinných míst, na které se zaokrouhluje. Pokud chcete zaokrouhlit čísla před desetinnou čárkou, zadejte zápornou hodnotu.
Zaokrouhlit na desetiny = 1
Zaokrouhlit na celé čísla = 0
Zaokrouhlit na desítky = -1</value>
  </data>
  <data name="VatRoundingRule_Decimals_DisplayName" xml:space="preserve">
    <value>Počet desetinných míst</value>
  </data>
  <data name="GlobalRulesRoundingConfig_VatRoundingRule_Description" xml:space="preserve">
    <value>Pravidla pro zaokrouhlování DPH</value>
  </data>
  <data name="GlobalRulesRoundingConfig_VatRoundingRule_DisplayName" xml:space="preserve">
    <value>DPH</value>
  </data>
  <data name="RoundingRuleBase_Logic_Description" xml:space="preserve">
    <value>Logika zaokrouhlování</value>
  </data>
  <data name="VatRoundingRule_Logic_Description" xml:space="preserve">
    <value>Logika zaokrouhlování</value>
  </data>
  <data name="CashDeskHwClientDisplayConfig_DisplayType_Description" xml:space="preserve">
    <value>Typ zobrazení na zákazníckém displeji</value>
  </data>
  <data name="GlobalBehaviourApplicationLanguageConfig_LanguageCount_DisplayName" xml:space="preserve">
    <value>Počet jazyků</value>
  </data>
  <data name="GlobalBehaviourApplicationLanguageConfig_LanguageCount_Description" xml:space="preserve">
    <value>Ovlivňuje počet jazyků, které jsou v aplikaci nabízeny. Prozatím využívá pouze PM. V PM může být přepsáno z GlobalBehaviourApplicationLanguageDetail</value>
  </data>
  <data name="PresPointAppearanceColorsConfig_StartFromBorderColor_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_AlternateNormalButtonColor2_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_AlternateNormalButtonColor2_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="AutoUpdaterBehaviourMonitorConfig_UpgradeWhen_Description" xml:space="preserve">
    <value>Určuje, za jakých podmínek proběhne aktualizace aplikací.</value>
  </data>
  <data name="CashDeskSaleConfig_CheckGoodsQuantity_DisplayName" xml:space="preserve">
    <value>Kontrolovat stav skladu (nepovolit prodej do mínusu)</value>
  </data>
  <data name="CashDeskSaleConfig_CheckGoodsQuantity_Description" xml:space="preserve">
    <value>Pokud je povoleno, není možné prodávat do mínusu.
Pokud není povoleno, není stav skladu hlídán a je možno prodávat do záporných množství.</value>
  </data>
  <data name="GlobalRulesCardManagementConfig_GenerateExtCardCode_DisplayName" xml:space="preserve">
    <value>Vytvořit kód karty</value>
  </data>
  <data name="GlobalRulesCardManagementConfig_GenerateExtCardCode_Description" xml:space="preserve">
    <value>Při zapnutí tohoto parametru je při výdeji karet požadováno pouze načtení vnitřního čísla karty. Uživatelské číslo (kód) karty je vygenerován systémem. Pokud je tento parametr zapnutý, nesmí být zapnutý parametr Ztotožni s osobním číslem.</value>
  </data>
  <data name="GlobalRulesCardManagementConfig_IgnoreCardSet_DisplayName" xml:space="preserve">
    <value>Uživatelské kódování karet</value>
  </data>
  <data name="GlobalRulesCardManagementConfig_IgnoreCardSet_Description" xml:space="preserve">
    <value>Je-li parametr vypnut, jsou systémem akceptovány pouze karty, uvedené v tabulce KartyFK. Jinou kartu nelze do systému zavést, s výjimkou karty s kódem 1. Při zapnutém parametru se nově zadávané karty do KartyFK dopisují a jsou systémem přijaty.</value>
  </data>
  <data name="GlobalRulesCardManagementConfig_InstallationIntCode_DisplayName" xml:space="preserve">
    <value>Instalační číslo</value>
  </data>
  <data name="GlobalRulesCardManagementConfig_InstallationIntCode_Description" xml:space="preserve">
    <value>Vnitřní číslo instalace ve formátu dat SECCOM. Rozlišuje karty se stejným číslem emise i pořadovým číslem. Uplatňuje se prakticky pouze u magnetických karet. U bezkontaktních karet je většinou jednoznačnost čísla zajištěna výrobcem v celém rozsahu vyráběných karet.</value>
  </data>
  <data name="GlobalRulesCardManagementConfig_InstallationIntCodeLength_DisplayName" xml:space="preserve">
    <value>Délka čísla instalace</value>
  </data>
  <data name="GlobalRulesCardManagementConfig_InstallationIntCodeLength_Description" xml:space="preserve">
    <value>Určuje počet znaků vnitřního čísla instalace.</value>
  </data>
  <data name="GlobalRulesCardManagementConfig_IntCodeLength_DisplayName" xml:space="preserve">
    <value>Délka čísla karty</value>
  </data>
  <data name="GlobalRulesCardManagementConfig_IntCodeLength_Description" xml:space="preserve">
    <value>Určuje počet znaků čísla karty (včetně čísla emise u magnetických karet).</value>
  </data>
  <data name="GlobalRulesCardManagementConfig_MCRPovolHex_DisplayName" xml:space="preserve">
    <value>Uživatelské kódování karet - hexadecimální čísla karet</value>
  </data>
  <data name="GlobalRulesCardManagementConfig_MCRPovolHex_Description" xml:space="preserve">
    <value>Povoluje použít ve vnitřním čísle karty hexadecimální čísla. Parametr je použit pouze z důvodu zpětné kompatibility, časem bude zrušen.</value>
  </data>
  <data name="GlobalRulesCardManagementConfig_MCRPoziceCisla_DisplayName" xml:space="preserve">
    <value>Pozice čísla karty v řetězci ze snímače</value>
  </data>
  <data name="GlobalRulesCardManagementConfig_MCRPoziceCisla_Description" xml:space="preserve">
    <value>Určuje pozici v řetězci ze snímače, na které se nachází první znak čísla karty (včetně čísla emise). Parametr je použit pouze z důvodu zpětné kompatibility, časem bude zrušen.</value>
  </data>
  <data name="GlobalRulesCardManagementConfig_MCRPoziceCislaInst_Description" xml:space="preserve">
    <value>Určuje pozici v řetězci ze snímače, na které se nachází první znak vnitřního čísla instalace (prakticky pouze pro magnetické karty). Parametr je použit pouze z důvodu zpětné kompatibility, časem bude zrušen.</value>
  </data>
  <data name="GlobalRulesCardManagementConfig_MCRPoziceCislaInst_DisplayName" xml:space="preserve">
    <value>Pozice čísla instalace</value>
  </data>
  <data name="GlobalRulesCardManagementConfig_MultipleCardsPerClient_Description" xml:space="preserve">
    <value>Povoluje strávníkům používat současně více aktivních identifikačních karet. Standardně vždy nastaveno na vypnuto.</value>
  </data>
  <data name="GlobalRulesCardManagementConfig_CardDistributionType_Description" xml:space="preserve">
    <value>Určuje způsob vedení karet vydaných za nulovou cenu.</value>
  </data>
  <data name="GlobalRulesCardManagementConfig_CardDistributionType_DisplayName" xml:space="preserve">
    <value>Karty za nulovou cenu registrovány odděleně</value>
  </data>
  <data name="GlobalRulesCardManagementConfig_UsePersonalCodeAsExtCardCode_Description" xml:space="preserve">
    <value>Při zapnutí tohoto parametru je při výdeji karet požadováno pouze načtení vnitřního čísla karty. Uživatelské číslo (kód) karty je vygenerován systémem podle osobního čísla strávníka. Pokud je tento parametr zapnutý, musí být zapnuta kontrola duplicit osobního čísla v celé databázi a nesmí být zapnutý parametr Vytvořit kód karty.</value>
  </data>
  <data name="GlobalRulesCardManagementConfig_UsePersonalCodeAsExtCardCode_DisplayName" xml:space="preserve">
    <value>Uživatelské kódování karet - ztotožnit s os. číslem</value>
  </data>
  <data name="GlobalRulesMenuConfig_DisplayedMealKinds_DisplayName" xml:space="preserve">
    <value>Zobrazované druhy jídel</value>
  </data>
  <data name="GlobalRulesMenuConfig_DisplayedMealKinds_Description" xml:space="preserve">
    <value>Seznam druhů jídel, které se zobrazují v Kase. Zadejte jako seznam čísel oddělený čárkami.</value>
  </data>
  <data name="GlobalRulesOrderingConfig_AcceptHolidays_Description" xml:space="preserve">
    <value>Nastavuje aplikaci svátků pro objednací pravidla a objednací schema</value>
  </data>
  <data name="GlobalRulesOrderingConfig_AcceptHolidays_DisplayName" xml:space="preserve">
    <value>Povolit nastavení svátků</value>
  </data>
  <data name="GlobalRulesOrderingConfig_AllowExchangeMessSize_DisplayName" xml:space="preserve">
    <value>Burza bez stravního limitu</value>
  </data>
  <data name="GlobalRulesOrderingConfig_AllowExchangeMessSize_Description" xml:space="preserve">
    <value>Povoluje ignorování velikostí porcí v burze.</value>
  </data>
  <data name="GlobalRulesOrderingConfig_EnableMealTicketExchange_DisplayName" xml:space="preserve">
    <value>Povolit burzu stravenek</value>
  </data>
  <data name="GlobalRulesOrderingConfig_EnableMealTicketExchange_Description" xml:space="preserve">
    <value>Zapíná burzu stravenek. Při vypnutém parametru není možné do burzy vložit žádné jídlo.</value>
  </data>
  <data name="GlobalRulesOrderingConfig_UseMessSizes_DisplayName" xml:space="preserve">
    <value>Povolit stravní limity (4 místný display)</value>
  </data>
  <data name="GlobalRulesOrderingConfig_UseMessSizes_Description" xml:space="preserve">
    <value>Zapíná mechanismus pro rozlišování velikosti porcí a podporu čtyřmístných výdejních displejů.</value>
  </data>
  <data name="GlobalRulesOrderingConfig_Vouchers_Description" xml:space="preserve">
    <value>Umožňuje omezit měsíční počet dotovaných porcí (zapíná tzv. bonový systém).</value>
  </data>
  <data name="GlobalRulesOrderingConfig_Vouchers_DisplayName" xml:space="preserve">
    <value>Omezení měsíčního počtu dotovaných porcí</value>
  </data>
  <data name="GlobalRulesOrderingConfig_VoucherSource_DisplayName" xml:space="preserve">
    <value>Plnění bonů</value>
  </data>
  <data name="GlobalRulesOrderingConfig_VoucherSource_Description" xml:space="preserve">
    <value>Určuje způsob plnění bonů.</value>
  </data>
  <data name="CashDeskFiscalModuleConfig_ModuleType_DisplayName" xml:space="preserve">
    <value>Typ fiskálního modulu</value>
  </data>
  <data name="CashDeskFiscalModuleConfig_ModuleType_Description" xml:space="preserve">
    <value>Typ fiskálního modulu</value>
  </data>
  <data name="CashDeskFiscalModuleConfig_TaxIdentificationCode_Description" xml:space="preserve">
    <value>Daňové identifikační číslo pokladny pro fiskální modul (DIC).
Pokud je vyplněn, tiskne se na paragonu.</value>
  </data>
  <data name="CashDeskFiscalModuleConfig_TaxIdentificationCode_DisplayName" xml:space="preserve">
    <value>Daňové identifikační číslo pokladny</value>
  </data>
  <data name="CashDeskFiscalModuleConfig_PosIdentificationCode_Description" xml:space="preserve">
    <value>Daňový kód pokladny pro fiskální modul (DKP). Musí být jedinečný pro každou kasu.
Pokud je vyplněn, tiskne se na paragonu.</value>
  </data>
  <data name="CashDeskFiscalModuleConfig_PosIdentificationCode_DisplayName" xml:space="preserve">
    <value>Daňový kód pokladny</value>
  </data>
  <data name="FiscalModuleSettingsBase_Settings_DisplayName" xml:space="preserve">
    <value>Nastavení fiskálního modulu</value>
  </data>
  <data name="FiscalModuleSettingsBase_Settings_Description" xml:space="preserve">
    <value>Nastavení fiskálního modulu</value>
  </data>
  <data name="GlobalRulesCardManagementConfig_MultipleCardsPerClient_DisplayName" xml:space="preserve">
    <value>Povolit více aktivních karet</value>
  </data>
  <data name="GlobalRulesMenuConfig_EnableCanteenLimits_Description" xml:space="preserve">
    <value>Povoluje hlídání limitu vydávaných porcí na výdejnu.</value>
  </data>
  <data name="GlobalRulesMenuConfig_EnableCanteenLimits_DisplayName" xml:space="preserve">
    <value>Povolit limity porcí na výdejnu</value>
  </data>
  <data name="GlobalRulesMenuConfig_EnableWeightedMeal_DisplayName" xml:space="preserve">
    <value>Povolit vážená jídla</value>
  </data>
  <data name="GlobalRulesMenuConfig_EnableWeightedMeal_Description" xml:space="preserve">
    <value>Povoluje možnost použití a prodeje/výdeje vážených jídel.</value>
  </data>
  <data name="GlobalRulesMenuConfig_UseAlternativeLanguage_DisplayName" xml:space="preserve">
    <value>Povolit alternativní jazyk jídelníčku</value>
  </data>
  <data name="GlobalRulesMenuConfig_UseAlternativeLanguage_Description" xml:space="preserve">
    <value>Povoluje použití alternativního jazyka jídelníčku.</value>
  </data>
  <data name="GlobalBehaviourSalesSlipPrintConfig_PrintAccountBallance_Description" xml:space="preserve">
    <value>Povoluje kase tisk počátečního a koncového stavu účtu při prodeji kartou.</value>
  </data>
  <data name="GlobalBehaviourSalesSlipPrintConfig_PrintAccountBallance_DisplayName" xml:space="preserve">
    <value>Tisknout stav účtu</value>
  </data>
  <data name="GlobalBehaviourSalesSlipPrintConfig_PrintVoucherCount_Description" xml:space="preserve">
    <value>Povoluje kase tisk zbývajícího stavu bonů při prodeji kartou.</value>
  </data>
  <data name="GlobalBehaviourSalesSlipPrintConfig_PrintVoucherCount_DisplayName" xml:space="preserve">
    <value>Tisknout stav bonů</value>
  </data>
  <data name="GlobalRulesClientManagementConfig_BirthCodeDuplication_DisplayName" xml:space="preserve">
    <value>Kontrola duplicity rodného čísla</value>
  </data>
  <data name="GlobalRulesClientManagementConfig_BirthCodeDuplication_Description" xml:space="preserve">
    <value>Udává, v jakém rozsahu se bude kontrolovat výskyt stejného rodného čísla.</value>
  </data>
  <data name="GlobalRulesClientManagementConfig_PersonalCodeDuplication_DisplayName" xml:space="preserve">
    <value>Kontrola duplicity osobního čísla</value>
  </data>
  <data name="GlobalRulesClientManagementConfig_PersonalCodeDuplication_Description" xml:space="preserve">
    <value>Udává, v jakém rozsahu se bude kontrolovat výskyt stejného osobního čísla.</value>
  </data>
  <data name="GlobalRulesClientManagementConfig_CustomerFileExpirationEnabled_DisplayName" xml:space="preserve">
    <value>Povolit expirační dobu evidenčních listů</value>
  </data>
  <data name="GlobalRulesClientManagementConfig_CustomerFileExpirationEnabled_Description" xml:space="preserve">
    <value>Povoluje expirovat vyřazené evidenční listy strávníků po stanovené době.</value>
  </data>
  <data name="GlobalRulesClientManagementConfig_CustomerFileExpiration_Description" xml:space="preserve">
    <value>Udává počet měsíců, po jaké dojde k expiraci vyřazených evidenčních listů strávníků.</value>
  </data>
  <data name="GlobalRulesClientManagementConfig_CustomerFileExpiration_DisplayName" xml:space="preserve">
    <value>Expirační doba evidenčních listů [měsíc]</value>
  </data>
  <data name="OfficeHwPrinterConfig_CashBox_DisplayName" xml:space="preserve">
    <value>Peněžní zásuvka</value>
  </data>
  <data name="OfficeHwPrinterConfig_CashBox_Description" xml:space="preserve">
    <value>Specifikace hardwarové peněžní zásuvky.</value>
  </data>
  <data name="OfficeHwPrinterConfig_CutCommand_DisplayName" xml:space="preserve">
    <value>Řezání papíru</value>
  </data>
  <data name="OfficeHwPrinterConfig_CutCommand_Description" xml:space="preserve">
    <value>Povel pro bankovní tiskárnu, který spustí nůžky pro odstřihnutí vytištěného paragonu. Je možné též využít pro odřádkování (posun papíru před ustřižením, nebo pro vysunutí potištěné části paragonu z tiskárny, pokud nemá střih papíru).</value>
  </data>
  <data name="CashDeskSaleConfig_EnableSaleOfUnspecifiedGoods_DisplayName" xml:space="preserve">
    <value>Povolit prodej nesledovaného sortimentu</value>
  </data>
  <data name="CashDeskSaleConfig_EnableSaleOfUnspecifiedGoods_Description" xml:space="preserve">
    <value>Pokud je povoleno, je povolen prodej nesledovaného sortimentu.</value>
  </data>
  <data name="ComPortSettings_PortName_Description" xml:space="preserve">
    <value>Název portu, musí odpovídat skutečnému portu ve Windows.</value>
  </data>
  <data name="ComPortSettings_PortName_DisplayName" xml:space="preserve">
    <value>Port</value>
  </data>
  <data name="ComPortSettings_BaudRate_DisplayName" xml:space="preserve">
    <value>Rychlost přenosu</value>
  </data>
  <data name="ComPortSettings_BaudRate_Description" xml:space="preserve">
    <value>Rychlost přenosu dat. Musí být nastaveno dle daného snímače.</value>
  </data>
  <data name="ComPortSettings_Parity_Description" xml:space="preserve">
    <value>Parita</value>
  </data>
  <data name="ComPortSettings_Parity_DisplayName" xml:space="preserve">
    <value>Parita</value>
  </data>
  <data name="ComPortSettings_DataBits_DisplayName" xml:space="preserve">
    <value>Počet bitů</value>
  </data>
  <data name="ComPortSettings_DataBits_Description" xml:space="preserve">
    <value>Počet bitů</value>
  </data>
  <data name="OfficeBehaviourMealTicketsConfig_HideOrderColumns_DisplayName" xml:space="preserve">
    <value>Restaurační provoz</value>
  </data>
  <data name="OfficeBehaviourMealTicketsConfig_HideOrderColumns_Description" xml:space="preserve">
    <value>Mění chování systému pro výhradně restaurační provoz.</value>
  </data>
  <data name="OfficeBehaviourMealTicketsConfig_ReadMealTicketsCount_Description" xml:space="preserve">
    <value>Určuje, zda se počty stravenek zjišťují přímo ze vzdálených serverů nebo z lokálních replik dat.</value>
  </data>
  <data name="OfficeBehaviourMealTicketsConfig_ReadMealTicketsCount_DisplayName" xml:space="preserve">
    <value>Počítání stravenek</value>
  </data>
  <data name="OfficeBehaviourBalanceConfig_EnableIndividualAccountBalance_DisplayName" xml:space="preserve">
    <value>Povolit individuálního uzavření účtu</value>
  </data>
  <data name="OfficeBehaviourBalanceConfig_EnableIndividualAccountBalance_Description" xml:space="preserve">
    <value>Povoluje nebo zakazuje individuální uzavření účtu.</value>
  </data>
  <data name="OfficeBehaviourBalanceConfig_ReportRecapitulation_Description" xml:space="preserve">
    <value>Rozlišení rekapitulace nákladů v rámci druhu</value>
  </data>
  <data name="OfficeBehaviourBalanceConfig_ReportRecapitulation_DisplayName" xml:space="preserve">
    <value>Rozlišení rekapitulace nákladů v rámci druhu</value>
  </data>
  <data name="OfficeBehaviourBatchOrdering_EnableBatchOperations_DisplayName" xml:space="preserve">
    <value>Povolit hromadné operace</value>
  </data>
  <data name="OfficeBehaviourBatchOrdering_EnableBatchOperations_Description" xml:space="preserve">
    <value>Povoluje hromadné operace v náhradním objednávání.</value>
  </data>
  <data name="OfficeBehaviourBatchOrdering_ConformToRules_Description" xml:space="preserve">
    <value>Pokud je povoleno, respektuje náhradní objednávání standardní objednací pravidla systému. 
Pokud je zakázáno, provede náhradní objednávání i objednávky, které objednací pravidla porušují.</value>
  </data>
  <data name="OfficeBehaviourBatchOrdering_ConformToRules_DisplayName" xml:space="preserve">
    <value>Respektovat objednací pravidla</value>
  </data>
  <data name="OfficeBehaviourCardManagement_GetIntCodeFromCardSet_DisplayName" xml:space="preserve">
    <value>Kancelář bez snímače karet</value>
  </data>
  <data name="OfficeBehaviourCardManagement_GetIntCodeFromCardSet_Description" xml:space="preserve">
    <value>Pokud je povoleno, nepoužívá se snímač karet, ale čísla se přidělují ze zásobníku.
Pokud je zakázáno, využívá se snímač karet.
Pokud má být povoleno, ostatní parametry musí být nastaveny takto: 
Global.Rules.CardManagement.IgnoreCardSet = false
Global.Rules.CardManagement.GenerateExtCardCode = false
Global.Rules.CardManagement.UsePersonalCodeAsExtCardCode = false</value>
  </data>
  <data name="OfficeBehaviourCardManagement_RepeatExtCardCodeInput_DisplayName" xml:space="preserve">
    <value>Opakovaně zadat uživatelský kód karty</value>
  </data>
  <data name="OfficeBehaviourCardManagement_RepeatExtCardCodeInput_Description" xml:space="preserve">
    <value>Pokud je povoleno, musí uživatel zadat kód karty 2x, aby se zamezilo chybnému vstupu.
Pokud je zakázáno, kód karty se zadává pouze jednou.</value>
  </data>
  <data name="OfficeBehaviourCalcPriceList_EnableCalcPriceList_Description" xml:space="preserve">
    <value>Povoluje kalkulovaný ceník.</value>
  </data>
  <data name="OfficeBehaviourCalcPriceList_EnableCalcPriceList_DisplayName" xml:space="preserve">
    <value>Povolit kalkulovaný ceník</value>
  </data>
  <data name="OfficeBehaviourCalcPriceList_EnableDayMaxOfPriceParts_Description" xml:space="preserve">
    <value>Povoluje systém dotací dle denního obratu strávníka.</value>
  </data>
  <data name="OfficeBehaviourCalcPriceList_EnableDayMaxOfPriceParts_DisplayName" xml:space="preserve">
    <value>Povolit denní maxima cenových složek</value>
  </data>
  <data name="GlobalServicesSmtpConfig_SmtpHost_Description" xml:space="preserve">
    <value>Název nebo IP adresa SMTP serveru.</value>
  </data>
  <data name="GlobalServicesSmtpConfig_SmtpHost_DisplayName" xml:space="preserve">
    <value>SMTP server</value>
  </data>
  <data name="GlobalServicesSmtpConfig_EnableLogin_Description" xml:space="preserve">
    <value>Povolit přihlašování k SMTP serveru pomocí zadaného jména a hesla.</value>
  </data>
  <data name="GlobalServicesSmtpConfig_EnableLogin_DisplayName" xml:space="preserve">
    <value>Povolit SMTP autentizaci</value>
  </data>
  <data name="GlobalServicesSmtpConfig_User_Description" xml:space="preserve">
    <value>Uživatelské jméno pro přihlášení k SMTP serveru.</value>
  </data>
  <data name="GlobalServicesSmtpConfig_User_DisplayName" xml:space="preserve">
    <value>Uživatelské jméno</value>
  </data>
  <data name="GlobalServicesSmtpConfig_Password_Description" xml:space="preserve">
    <value>Heslo pro přihlášení k SMTP serveru.</value>
  </data>
  <data name="GlobalServicesSmtpConfig_Password_DisplayName" xml:space="preserve">
    <value>Heslo</value>
  </data>
  <data name="GlobalServicesSmtpConfig_SenderDomain_Description" xml:space="preserve">
    <value>Tato doména se použije při vytváření adresy odesílatele. Část adresy před doménou se generuje automaticky pomocí Id zařízení. 
Zde byste měli uvést doménu organizace, ve které je nainstalován systém.</value>
  </data>
  <data name="GlobalServicesSmtpConfig_SenderDomain_DisplayName" xml:space="preserve">
    <value>Doména odesílatele</value>
  </data>
  <data name="GlobalServicesSmtpConfig_SendTimeout_DisplayName" xml:space="preserve">
    <value>Timeout odeslání [s]</value>
  </data>
  <data name="GlobalServicesSmtpConfig_SendTimeout_Description" xml:space="preserve">
    <value>Timeout odeslání mailu.</value>
  </data>
  <data name="GlobalServicesLoggingConfig_LogToLogActivity_DisplayName" xml:space="preserve">
    <value>Logovat do LogAktivita</value>
  </data>
  <data name="GlobalServicesLoggingConfig_LogToLogActivity_Description" xml:space="preserve">
    <value>Povoluje nebo zakazuje protokolování kritických činností obsluhy do tabulky LogAktivita. Logují se např. operace s financemi, hromadné objednávky, změny zařazení, blokace karet...
Protokolované operace jsou vyjmenovány v tabulce LogAkce .</value>
  </data>
  <data name="PresPointBehaviourUserInterfaceConfig_FullScreen_DisplayName" xml:space="preserve">
    <value>Celá obrazovka</value>
  </data>
  <data name="PresPointBehaviourUserInterfaceConfig_FullScreen_Description" xml:space="preserve">
    <value>Určuje, zda bude hlavní okno zobrazeno přes celou obrazovku včetně TaskBar lišty Windows.</value>
  </data>
  <data name="PresPointBehaviourUserInterfaceConfig_KeepAppFocus_Description" xml:space="preserve">
    <value>Pokud je povoleno hlídání focusu, nelze aplikaci zobrazit na pozadí.</value>
  </data>
  <data name="PresPointBehaviourUserInterfaceConfig_KeepAppFocus_DisplayName" xml:space="preserve">
    <value>Hlídat focus</value>
  </data>
  <data name="GlobalHwScaleConfig_ScaleType_Description" xml:space="preserve">
    <value>Nastavuje konkrétní typ váhy</value>
  </data>
  <data name="GlobalHwScaleConfig_ScaleType_DisplayName" xml:space="preserve">
    <value>Typ váhy</value>
  </data>
  <data name="GlobalHwScaleConfig_Settings_Description" xml:space="preserve">
    <value>Konfigurace parametrů váhy</value>
  </data>
  <data name="GlobalHwScaleConfig_Settings_DisplayName" xml:space="preserve">
    <value>Nastavení váhy</value>
  </data>
  <data name="GlobalRulesCanteenConfig_CanteenSelectionLogic_Description" xml:space="preserve">
    <value>Určuje, v jakém pořadí je vybírána implicitní výdejna pro daného strávníka. 
Výdejna je vybírána v pořadí, ve kterém jsou parametry vyjmenovány. Pokud není daný parametr definován, pokračuje výběr dále na další parametr v pořadí.</value>
  </data>
  <data name="GlobalRulesCanteenConfig_CanteenSelectionLogic_DisplayName" xml:space="preserve">
    <value>Pravidla pro výběr implicitní výdejny</value>
  </data>
  <data name="MenuPresenterBehaviourUserInterfaceConfig_FullScreen_DisplayName" xml:space="preserve">
    <value>Celá obrazovka</value>
  </data>
  <data name="MenuPresenterBehaviourUserInterfaceConfig_FullScreen_Description" xml:space="preserve">
    <value>Určuje, zda bude hlavní okno zobrazeno přes celou obrazovku včetně TaskBar lišty Windows.</value>
  </data>
  <data name="MenuPresenterBehaviourUserInterfaceConfig_ScreenIndex_Description" xml:space="preserve">
    <value>Číslo displeje, na kterém bude menu zobrazeno</value>
  </data>
  <data name="MenuPresenterBehaviourUserInterfaceConfig_ScreenIndex_DisplayName" xml:space="preserve">
    <value>Číslo displeje</value>
  </data>
  <data name="CsobEBankingSettings_BankCode_Description" xml:space="preserve">
    <value>Kód banky ve tvaru 0000</value>
  </data>
  <data name="CsobEBankingSettings_BankCode_DisplayName" xml:space="preserve">
    <value>Kód banky (tvar 0000)</value>
  </data>
  <data name="CsobEBankingSettings_AccountNumber_DisplayName" xml:space="preserve">
    <value>Číslo účtu (tvar 000000-***********)</value>
  </data>
  <data name="CsobEBankingSettings_AccountNumber_Description" xml:space="preserve">
    <value>Číslo účtu ve tvaru 000000-***********</value>
  </data>
  <data name="CsobEBankingSettings_MandatorName_DisplayName" xml:space="preserve">
    <value>Jméno příkazce</value>
  </data>
  <data name="CsobEBankingSettings_MandatorName_Description" xml:space="preserve">
    <value>Název toho, jehož jménem se příkaz odesílá</value>
  </data>
  <data name="CeskaSporitelnaEBankingSettings_CompanyName_Description" xml:space="preserve">
    <value>Název organizace</value>
  </data>
  <data name="CeskaSporitelnaEBankingSettings_CompanyName_DisplayName" xml:space="preserve">
    <value>Název organizace</value>
  </data>
  <data name="CeskaSporitelnaEBankingSettings_CompanyNumber_Description" xml:space="preserve">
    <value>Číslo organizace</value>
  </data>
  <data name="CeskaSporitelnaEBankingSettings_CompanyNumber_DisplayName" xml:space="preserve">
    <value>Číslo organizace</value>
  </data>
  <data name="CeskaSporitelnaEBankingSettings_AccountNumber_DisplayName" xml:space="preserve">
    <value>Číslo účtu</value>
  </data>
  <data name="CeskaSporitelnaEBankingSettings_AccountNumber_Description" xml:space="preserve">
    <value>Číslo účtu</value>
  </data>
  <data name="CeskaSporitelnaEBankingSettings_CreditAccountCode_DisplayName" xml:space="preserve">
    <value>Kód "Dal"</value>
  </data>
  <data name="CeskaSporitelnaEBankingSettings_CreditAccountCode_Description" xml:space="preserve">
    <value>Kód "Dal"</value>
  </data>
  <data name="OfficeEBankingConfig_Items_DisplayName" xml:space="preserve">
    <value>Položky</value>
  </data>
  <data name="OfficeEBankingConfig_Items_Description" xml:space="preserve">
    <value>Konfigurace jednotlivých typů E-Bankingu</value>
  </data>
  <data name="EBankingConfigItem_EBankingType_DisplayName" xml:space="preserve">
    <value>Typ E-Bankingu</value>
  </data>
  <data name="EBankingConfigItem_EBankingType_Description" xml:space="preserve">
    <value>Volba typu E-Bankingu</value>
  </data>
  <data name="EBankingConfigItem_Settings_DisplayName" xml:space="preserve">
    <value>Nastavení</value>
  </data>
  <data name="EBankingConfigItem_Settings_Description" xml:space="preserve">
    <value>Nastavení pro daný typ E-Bankingu</value>
  </data>
  <data name="BestKbEBankingSettings_BankCode_Description" xml:space="preserve">
    <value>Kód banky</value>
  </data>
  <data name="BestKbEBankingSettings_BankCode_DisplayName" xml:space="preserve">
    <value>Kód banky</value>
  </data>
  <data name="BestKbEBankingSettings_AccountNumber_DisplayName" xml:space="preserve">
    <value>Číslo účtu</value>
  </data>
  <data name="BestKbEBankingSettings_AccountNumber_Description" xml:space="preserve">
    <value>Číslo účtu</value>
  </data>
  <data name="BestKbEBankingSettings_BranchCode_Description" xml:space="preserve">
    <value>Kód pobočky</value>
  </data>
  <data name="BestKbEBankingSettings_BranchCode_DisplayName" xml:space="preserve">
    <value>Kód pobočky</value>
  </data>
  <data name="BestKbEBankingSettings_StationCode_Description" xml:space="preserve">
    <value>Číslo stanice</value>
  </data>
  <data name="BestKbEBankingSettings_StationCode_DisplayName" xml:space="preserve">
    <value>Číslo stanice</value>
  </data>
  <data name="BestKbEBankingSettings_Description_Description" xml:space="preserve">
    <value>Popis</value>
  </data>
  <data name="BestKbEBankingSettings_Description_DisplayName" xml:space="preserve">
    <value>Popis</value>
  </data>
  <data name="BestKbEBankingSettings_ClientNumber_DisplayName" xml:space="preserve">
    <value>Číslo klienta</value>
  </data>
  <data name="BestKbEBankingSettings_ClientNumber_Description" xml:space="preserve">
    <value>Číslo klienta</value>
  </data>
  <data name="CeskaSporitelnaEBankingSettings_DebitAccountCode_Description" xml:space="preserve">
    <value>Kód "Má dáti"</value>
  </data>
  <data name="CeskaSporitelnaEBankingSettings_DebitAccountCode_DisplayName" xml:space="preserve">
    <value>Kód "Má dáti"</value>
  </data>
  <data name="GlobalBehaviourApplicationLanguageConfig_ApplicationLanguage_DisplayName" xml:space="preserve">
    <value>Jazyk aplikace</value>
  </data>
  <data name="GlobalBehaviourApplicationLanguageConfig_ApplicationLanguage_Description" xml:space="preserve">
    <value>Jazyk, ve kterém pracuje aplikace (Povolené hodnoty jsou Cz, nebo Sk).</value>
  </data>
  <data name="GlobalSystemOwnerConfig_WwwAddress_DisplayName" xml:space="preserve">
    <value>WWW adresa vlastníka licence</value>
  </data>
  <data name="GlobalSystemOwnerConfig_WwwAddress_Description" xml:space="preserve">
    <value>Zobrazí se při generování přístupového jména a hesla pro internetové objednávání. Slouží jako pomůcka pro začínající strávníky, aby věděli, na jaké www adrese mohou objednávat stravu.</value>
  </data>
  <data name="GlobalRulesBalanceConfig_AttendanceRecalc_DisplayName" xml:space="preserve">
    <value>Přepočet dle docházky</value>
  </data>
  <data name="GlobalRulesBalanceConfig_AttendanceRecalc_Description" xml:space="preserve">
    <value>Parametr povoluje při uzávěrce přepočet dotací podle souboru z docházky a stanovuje jeho typ. Pokud je přepočet povolen, má v Kreditu vyšší prioritu, než standardní nastavení systému (denní i měsíční limit dotovaných porcí).</value>
  </data>
  <data name="AutoUpdaterBehaviourImpersonationConfig_Impersonate_Description" xml:space="preserve">
    <value>Pokud je povoleno, aktualizátor spustí instalaci pod uživatelským jménem a heslem, které je zadáno v dalších parametrech. 
Pokud je zakázáno, aktualizace poběží pod aktuálním uživatelským účtem. 
Je nutno povolit, pokud aktuální uživatel nemá práva na zápis do instalačního adresáře.</value>
  </data>
  <data name="AutoUpdaterBehaviourImpersonationConfig_Impersonate_DisplayName" xml:space="preserve">
    <value>Provést přihlášení jako zadaný uživatel</value>
  </data>
  <data name="KeyValueToken_Key_DisplayName" xml:space="preserve">
    <value>Klíč (název hodnoty)</value>
  </data>
  <data name="KeyValueToken_Key_Description" xml:space="preserve">
    <value>Klíč (název hodnoty)</value>
  </data>
  <data name="KeyValueToken_Value_Description" xml:space="preserve">
    <value>Hodnota</value>
  </data>
  <data name="KeyValueToken_Value_DisplayName" xml:space="preserve">
    <value>Hodnota</value>
  </data>
  <data name="KeyValueStringToken_Key_DisplayName" xml:space="preserve">
    <value>Klíč (název hodnoty)</value>
  </data>
  <data name="KeyValueStringToken_Key_Description" xml:space="preserve">
    <value>Klíč (název hodnoty)</value>
  </data>
  <data name="KeyValueStringToken_Value_Description" xml:space="preserve">
    <value>Hodnota</value>
  </data>
  <data name="KeyValueStringToken_Value_DisplayName" xml:space="preserve">
    <value>Hodnota</value>
  </data>
  <data name="GlobalRulesMenuConfig_MultiKindMealExpedition_DisplayName" xml:space="preserve">
    <value>Vícedruhový výdej</value>
  </data>
  <data name="GlobalRulesMenuConfig_MultiKindMealExpedition_Description" xml:space="preserve">
    <value>Výdej více druhů jídel současně na čtyřmístný display.  
Nadefinujte skupiny druhů jídla. Tyto skupiny se pak při vícedruhovém výdeji přepínají přiložením systémové karty.</value>
  </data>
  <data name="MealKindGroup_MealKinds_DisplayName" xml:space="preserve">
    <value>Seznam druhů (oddělený čárkami)</value>
  </data>
  <data name="MealKindGroup_MealKinds_Description" xml:space="preserve">
    <value>Seznam druhů jídel oddělený čárkami v pořadí, v jakém se mají zobrazovat na displeji.</value>
  </data>
  <data name="OfficeBehaviourSalesSlipPrintConfig_PrintMode_DisplayName" xml:space="preserve">
    <value>Tisk paragonu</value>
  </data>
  <data name="OfficeBehaviourSalesSlipPrintConfig_PrintMode_Description" xml:space="preserve">
    <value>Konfiguruje mód tisku paragonu</value>
  </data>
  <data name="GlobalRulesCashOperationsConfig_CashDepositMaxValue_Description" xml:space="preserve">
    <value>Pokud je zadaná hodnota větší než 0, pak je maximální vklad hotovosti omezen zadanou částkou.
0 = dle legislativy (aktuálně 5000Kč/EUR).</value>
  </data>
  <data name="GlobalRulesCashOperationsConfig_CashDepositMaxValue_DisplayName" xml:space="preserve">
    <value>Maximální hodnota vkladu hotovosti</value>
  </data>
  <data name="BatchDataOperationsParametersConfig_ClientIdent_Description" xml:space="preserve">
    <value>Parametr určuje, které číslo v databázi strávníků je považováno za jednoznačný identifikátor strávníka.</value>
  </data>
  <data name="BatchDataOperationsParametersConfig_ClientIdent_DisplayName" xml:space="preserve">
    <value>Identifikace strávníka</value>
  </data>
  <data name="BatchDataOperationsParametersConfig_ImportToColumns_Description" xml:space="preserve">
    <value>Pokud je povoleno, data se plní přímo do příslušných sloupců a nic se neupravuje.
Pokud je zakázáno, všechna data pro ZRD se plní do sloupce 'veta' ve formě řetězce s oddělovači.</value>
  </data>
  <data name="BatchDataOperationsParametersConfig_ImportToColumns_DisplayName" xml:space="preserve">
    <value>Importovat do sloupců</value>
  </data>
  <data name="BatchDataOperationsParametersConfig_DataSource_DisplayName" xml:space="preserve">
    <value>Zdrojová databáze</value>
  </data>
  <data name="BatchDataOperationsParametersConfig_DataSource_Description" xml:space="preserve">
    <value>Název zdrojové databáze, ze které jsou data pro ZRD načítána (např: 'UCTO.dba.Zamestnanci'). Pokud se tabulka plní jiným způsebem (DTS), nezapisuje se nic.</value>
  </data>
  <data name="BatchDataOperationsParametersConfig_CreateOrgStruct_DisplayName" xml:space="preserve">
    <value>Vytvořit organizační strukturu</value>
  </data>
  <data name="BatchDataOperationsParametersConfig_CreateOrgStruct_Description" xml:space="preserve">
    <value>Parametr určuje, jak se ZRD zachová, pokud přichází údaje o organizaci, která neexistuje. 
Pokud je povoleno, vytvoří se nová struktura. 
Pokud je zakázáno, pouze se zaloguje chyba.</value>
  </data>
  <data name="BatchDataOperationsParametersConfig_DeleteOrders_DisplayName" xml:space="preserve">
    <value>Mazat objednávky při smazání karty</value>
  </data>
  <data name="BatchDataOperationsParametersConfig_DeleteOrders_Description" xml:space="preserve">
    <value>Parametr určuje, zda má ZRD při odebrání karty strávníkovi smazat případné objednávky.</value>
  </data>
  <data name="BatchDataOperationsParametersConfig_DefaultAccountType_Description" xml:space="preserve">
    <value>Typ účtu, který se použije, když nemá strávník implicitně uvedený typ účtu.</value>
  </data>
  <data name="BatchDataOperationsParametersConfig_DefaultAccountType_DisplayName" xml:space="preserve">
    <value>Implicitní typ účtu</value>
  </data>
  <data name="BatchDataOperationsParametersConfig_CardCodeRecalcType_Description" xml:space="preserve">
    <value>Určuje, jak změnové řízení databáze zachází s přicházejícím číslem karty.</value>
  </data>
  <data name="BatchDataOperationsParametersConfig_CardCodeRecalcType_DisplayName" xml:space="preserve">
    <value>Přepočet kódu karty</value>
  </data>
  <data name="BatchDataOperationsParametersConfig_UseZrdZarazeni_Description" xml:space="preserve">
    <value>Pokud je povoleno, pak se načítají některá data z tabulky ZRDZarazeni.
(idSkupina,TypUctu,mzu,mdp,hdu).</value>
  </data>
  <data name="BatchDataOperationsParametersConfig_UseZrdZarazeni_DisplayName" xml:space="preserve">
    <value>Načítat z tabulky ZRDZarazeni</value>
  </data>
  <data name="BatchDataOperationsParametersConfig_ForceCardChange_Description" xml:space="preserve">
    <value>Je-li povoleno, pak při výdeji karty ji původnímu držiteli odebere a vydá ji tomuto, jen to zaloguje. 
Je-li zakázáno vypíše hlášení : karta nevydána - používá ji jiný strávník.</value>
  </data>
  <data name="BatchDataOperationsParametersConfig_ForceCardChange_DisplayName" xml:space="preserve">
    <value>Bezpodmínečně změnit kartu</value>
  </data>
  <data name="BatchDataOperationsParametersConfig_SuspendCardOnReturn_Description" xml:space="preserve">
    <value>Pokud je povoleno, pak při požadavku na vrácení karty se původní karta se nevrátí, ale je jen pozastavena.</value>
  </data>
  <data name="BatchDataOperationsParametersConfig_SuspendCardOnReturn_DisplayName" xml:space="preserve">
    <value>Pozastavit kartu při požadavku na vrácení</value>
  </data>
  <data name="BatchDataOperationsParametersConfig_DeleteFromKartyFK_Description" xml:space="preserve">
    <value>Je-li povoleno, pak je karta smazána i z tabulky KartyFK. 
Pozor: Nepoužívá se, ponechávat vypnuto.</value>
  </data>
  <data name="BatchDataOperationsParametersConfig_DeleteFromKartyFK_DisplayName" xml:space="preserve">
    <value>Smazat z KartyFK</value>
  </data>
  <data name="PresPointBehaviourMealTicketConfig_EnablePrintRestriction_Description" xml:space="preserve">
    <value>Pokud je povoleno omezení tisku stravenek, kontroluje se počet dnů dopředu a čas.</value>
  </data>
  <data name="PresPointBehaviourMealTicketConfig_EnablePrintRestriction_DisplayName" xml:space="preserve">
    <value>Omezovat tisk stravenek</value>
  </data>
  <data name="PresPointBehaviourMealTicketConfig_MaxDaysAhead_Description" xml:space="preserve">
    <value>Počet dnů dopředu, pro které je možné provést tisk stravenky. Pokud je počet dnů dopředu nastaven na 0, kontroluje se i čas.</value>
  </data>
  <data name="PresPointBehaviourMealTicketConfig_MaxDaysAhead_DisplayName" xml:space="preserve">
    <value>Počet dnů dopředu pro tisk stravenek</value>
  </data>
  <data name="PresPointBehaviourMealTicketConfig_StartTime_Description" xml:space="preserve">
    <value>Pokud je počet dnů dopředu nastaven na 0, hlídá se i čas.</value>
  </data>
  <data name="PresPointBehaviourMealTicketConfig_StartTime_DisplayName" xml:space="preserve">
    <value>Tisknout od</value>
  </data>
  <data name="CashDeskBehaviourUserInterfaceConfig_FullScreen_DisplayName" xml:space="preserve">
    <value>Celá obrazovka</value>
  </data>
  <data name="CashDeskBehaviourUserInterfaceConfig_FullScreen_Description" xml:space="preserve">
    <value>Určuje, zda bude hlavní okno zobrazeno přes celou obrazovku včetně TaskBar lišty Windows.</value>
  </data>
  <data name="CashDeskBehaviourUserInterfaceConfig_KeepAppFocus_DisplayName" xml:space="preserve">
    <value>Hlídat focus</value>
  </data>
  <data name="CashDeskBehaviourUserInterfaceConfig_KeepAppFocus_Description" xml:space="preserve">
    <value>Automatické udržování focusu v aplikaci. Pokud je povoleno, nelze zobrazit žádný jiný program než Kasu.</value>
  </data>
  <data name="ServePointBehaviourServeringSettingsConfig_ApprovedServering_DisplayName" xml:space="preserve">
    <value>Odsouhlasení výdeje obsluhou</value>
  </data>
  <data name="ServePointBehaviourServeringSettingsConfig_ApprovedServering_Description" xml:space="preserve">
    <value>Odsouhlasení výdeje obsluhou tzn. nelze přiložit další kartu, dokud obsluha nestiskne tlačítko Další.</value>
  </data>
  <data name="ServePointBehaviourServeringSettingsConfig_StartServering_DisplayName" xml:space="preserve">
    <value>Zahájení výdeje</value>
  </data>
  <data name="ServePointBehaviourServeringSettingsConfig_StartServering_Description" xml:space="preserve">
    <value>Zahájení výdeje.</value>
  </data>
  <data name="ServePointBehaviourServeringSettingsConfig_ServeringMealKindType_DisplayName" xml:space="preserve">
    <value>Nastavení výdavaných druhů jídel</value>
  </data>
  <data name="ServePointBehaviourServeringSettingsConfig_ServeringMealKindType_Description" xml:space="preserve">
    <value>Nastavení výdavaných druhů jídel.</value>
  </data>
  <data name="ServePointBehaviourServeringSettingsConfig_ServeringRecordType_Description" xml:space="preserve">
    <value>Rozdíl v typu výdeje je způsob záznamu do tabulky objednávky.</value>
  </data>
  <data name="ServePointBehaviourServeringSettingsConfig_ServeringRecordType_DisplayName" xml:space="preserve">
    <value>Typ výdeje</value>
  </data>
  <data name="ServePointAppearanceSimpleViewConfig_BackgroundTopColor_DisplayName" xml:space="preserve">
    <value>Barva horního okraje pozadí</value>
  </data>
  <data name="ServePointAppearanceSimpleViewConfig_BackgroundTopColor_Description" xml:space="preserve">
    <value>Barva horního okraje pozadí u jednoduchých zobrazení.</value>
  </data>
  <data name="ServePointAppearanceSimpleViewConfig_BackgroundBottomColor_Description" xml:space="preserve">
    <value>Barva spodního okraje pozadí u jednoduchých zobrazení.</value>
  </data>
  <data name="ServePointAppearanceSimpleViewConfig_BackgroundBottomColor_DisplayName" xml:space="preserve">
    <value>Barva spodního okraje pozadí</value>
  </data>
  <data name="ServePointDisplayClientPersonalDataConfig_Title_Description" xml:space="preserve">
    <value>Zobrazení titulu strávníka.</value>
  </data>
  <data name="ServePointDisplayClientPersonalDataConfig_Title_DisplayName" xml:space="preserve">
    <value>Titul</value>
  </data>
  <data name="ServePointDisplayClientPersonalDataConfig_Name_DisplayName" xml:space="preserve">
    <value>Jméno a příjmení</value>
  </data>
  <data name="ServePointDisplayClientPersonalDataConfig_Name_Description" xml:space="preserve">
    <value>Zobrazení jména a příjmení strávníka.</value>
  </data>
  <data name="ServePointDisplayClientPersonalDataConfig_BirthCode_Description" xml:space="preserve">
    <value>Zobrazení rodného čísla přihlášeného strávníka.</value>
  </data>
  <data name="ServePointDisplayClientPersonalDataConfig_BirthCode_DisplayName" xml:space="preserve">
    <value>Rodné číslo</value>
  </data>
  <data name="ServePointDisplayClientPersonalDataConfig_PersonalCode_Description" xml:space="preserve">
    <value>Zobrazení osobního čísla strávníka.</value>
  </data>
  <data name="ServePointDisplayClientPersonalDataConfig_PersonalCode_DisplayName" xml:space="preserve">
    <value>Osobní číslo</value>
  </data>
  <data name="ServePointDisplayClientPersonalDataConfig_CardCode_Description" xml:space="preserve">
    <value>Zobrazení čísla karty přihlášeného strávníka.</value>
  </data>
  <data name="ServePointDisplayClientPersonalDataConfig_CardCode_DisplayName" xml:space="preserve">
    <value>Číslo karty</value>
  </data>
  <data name="ServePointDisplayClientPersonalDataConfig_ClientGroup_Description" xml:space="preserve">
    <value>Zobrazení skupiny, do které je strávník zařazen.</value>
  </data>
  <data name="ServePointDisplayClientPersonalDataConfig_ClientGroup_DisplayName" xml:space="preserve">
    <value>Skupina klienta</value>
  </data>
  <data name="ServePointDisplayClientPersonalDataConfig_MessSize_Description" xml:space="preserve">
    <value>Zobrazení stravní normy strávníka.</value>
  </data>
  <data name="ServePointDisplayClientPersonalDataConfig_MessSize_DisplayName" xml:space="preserve">
    <value>Stravní norma</value>
  </data>
  <data name="ServePointDisplayClientPersonalDataConfig_Organization_Description" xml:space="preserve">
    <value>Zobrazení organizace strávníka.</value>
  </data>
  <data name="ServePointDisplayClientPersonalDataConfig_Organization_DisplayName" xml:space="preserve">
    <value>Organizace</value>
  </data>
  <data name="ServePointDisplayClientPersonalDataConfig_Resort_Description" xml:space="preserve">
    <value>Zobrazení střediska strávníka.</value>
  </data>
  <data name="ServePointDisplayClientPersonalDataConfig_Resort_DisplayName" xml:space="preserve">
    <value>Středisko</value>
  </data>
  <data name="ServePointDisplayClientPersonalDataConfig_DisponsibleCreditBalance_Description" xml:space="preserve">
    <value>Zobrazení disponibilního zůstatku učtu strávníka.</value>
  </data>
  <data name="ServePointDisplayClientPersonalDataConfig_DisponsibleCreditBalance_DisplayName" xml:space="preserve">
    <value>Disponibilní zůtatek účtu</value>
  </data>
  <data name="ServePointDisplayClientPersonalDataConfig_VoucherCount_DisplayName" xml:space="preserve">
    <value>Stav bonů</value>
  </data>
  <data name="ServePointDisplayClientPersonalDataConfig_VoucherCount_Description" xml:space="preserve">
    <value>Zobrazení stavu bonů strávníka.</value>
  </data>
  <data name="ButtonsSettingsBase_BackgroundTopColor_DisplayName" xml:space="preserve">
    <value>Barva horni části pozadí</value>
  </data>
  <data name="ButtonsSettingsBase_BackgroundTopColor_Description" xml:space="preserve">
    <value>Barva horni části pozadí</value>
  </data>
  <data name="ButtonsSettingsBase_BackgroundBottomColor_DisplayName" xml:space="preserve">
    <value>Barva spodní částí pozadí</value>
  </data>
  <data name="ButtonsSettingsBase_BackgroundBottomColor_Description" xml:space="preserve">
    <value>Barva spodní částí pozadí</value>
  </data>
  <data name="ButtonsSettingsBase_BorderLine_DisplayName" xml:space="preserve">
    <value>Barva orámování</value>
  </data>
  <data name="ButtonsSettingsBase_BorderLine_Description" xml:space="preserve">
    <value>Barva orámování</value>
  </data>
  <data name="ButtonsSettingsBase_FontColor_DisplayName" xml:space="preserve">
    <value>Barva písma</value>
  </data>
  <data name="ButtonsSettingsBase_FontColor_Description" xml:space="preserve">
    <value>Barva písma</value>
  </data>
  <data name="ServePointBehaviourUserInterfaceConfig_FullScreen_Description" xml:space="preserve">
    <value>Určuje, zda bude hlavní okno zobrazeno přes celou obrazovku včetně TaskBar lišty Windows.</value>
  </data>
  <data name="ServePointBehaviourUserInterfaceConfig_FullScreen_DisplayName" xml:space="preserve">
    <value>Celá obrazovka</value>
  </data>
  <data name="ServePointBehaviourUserInterfaceConfig_ClientScreenIndex_DisplayName" xml:space="preserve">
    <value>Index klienstkého displeje</value>
  </data>
  <data name="ServePointBehaviourUserInterfaceConfig_ClientScreenIndex_Description" xml:space="preserve">
    <value>Index displeje, na kterém je zobrazení klientská obrazovka (obrazovka, kterou vidí strávník).</value>
  </data>
  <data name="ServePointBehaviourUserInterfaceConfig_ServiceScreenIndex_DisplayName" xml:space="preserve">
    <value>Index servisního displeje</value>
  </data>
  <data name="ServePointBehaviourUserInterfaceConfig_ServiceScreenIndex_Description" xml:space="preserve">
    <value>Index displeje, na kterém je zobrazena servisní obrazovka (obrazovka, kterou vidí obsluha).</value>
  </data>
  <data name="ServePointAppearanceApplicationConfig_BackgroundTopColor_DisplayName" xml:space="preserve">
    <value>Barva horního okraje pozadí</value>
  </data>
  <data name="ServePointAppearanceApplicationConfig_BackgroundTopColor_Description" xml:space="preserve">
    <value>Barva horního okraje pozadí všech hlavních oken používaných v aplikaci.</value>
  </data>
  <data name="ServePointAppearanceApplicationConfig_BackgroundBottomColor_Description" xml:space="preserve">
    <value>Barva spodního okraje pozadí všech hlavních oken používaných v aplikaci.</value>
  </data>
  <data name="ServePointAppearanceApplicationConfig_BackgroundBottomColor_DisplayName" xml:space="preserve">
    <value>Barva spodního okraje pozadí</value>
  </data>
  <data name="ServePointAppearanceApplicationConfig_StandardFontColor_DisplayName" xml:space="preserve">
    <value>Standardní barva písma</value>
  </data>
  <data name="ServePointAppearanceApplicationConfig_StandardFontColor_Description" xml:space="preserve">
    <value>Standardní barva písma používaná v rámci celé aplikace.</value>
  </data>
  <data name="ServePointAppearanceApplicationConfig_ViolentFontColor_Description" xml:space="preserve">
    <value>Výrazná barva písma používaná v rámci celé aplikace.</value>
  </data>
  <data name="ServePointAppearanceApplicationConfig_ViolentFontColor_DisplayName" xml:space="preserve">
    <value>Výrazná barva písma</value>
  </data>
  <data name="ServePointAppearanceApplicationConfig_AlternateFontColor_DisplayName" xml:space="preserve">
    <value>Doplňková barva písma</value>
  </data>
  <data name="ServePointAppearanceApplicationConfig_AlternateFontColor_Description" xml:space="preserve">
    <value>Doplňková barva písma používaná v rámci celé aplikace.</value>
  </data>
  <data name="ServePointTimeoutsConfig_ErrorMessagesMinimum_Description" xml:space="preserve">
    <value>Minimální doba v sekundách, po kterou budou zobrazena chybová hlášení.</value>
  </data>
  <data name="ServePointTimeoutsConfig_ErrorMessagesMinimum_DisplayName" xml:space="preserve">
    <value>Minimální doba chybových hlášení [s]</value>
  </data>
  <data name="ServePointTimeoutsConfig_ErrorMessagesMaximum_Description" xml:space="preserve">
    <value>Maximální doba v sekundách, po kterou budou zobrazena chybová hlášení.</value>
  </data>
  <data name="ServePointTimeoutsConfig_ErrorMessagesMaximum_DisplayName" xml:space="preserve">
    <value>Maximální doba chybových hlášení [s]</value>
  </data>
  <data name="ServePointTimeoutsConfig_NextCardApposition_Description" xml:space="preserve">
    <value>Ochranná lhůta pro přiložení další karty. Čas v sekundách, která určuje, za jakou dobu lze načíst další kartu po předchozím úspeěšném načtení. Jedná se především o problém rychlého přiložení té samé karty.</value>
  </data>
  <data name="ServePointTimeoutsConfig_NextCardApposition_DisplayName" xml:space="preserve">
    <value>Ochranná lhůta [s]</value>
  </data>
  <data name="ServePointTimeoutsConfig_DisplayData_DisplayName" xml:space="preserve">
    <value>Údaje zobrazené po úspěšném přihlašení [s]</value>
  </data>
  <data name="ServePointTimeoutsConfig_DisplayData_Description" xml:space="preserve">
    <value>Čas v sekundách, který určuje maximální dobu, po kterou budou zobrazeny údaje o strávníkovi a údaje o vydávaných jídlech. Zobrazení se přeruší přiložením další karty, ale s ohledem na ochrannou lhůtu.</value>
  </data>
  <data name="ServePointTimeoutsConfig_CountersCycle_DisplayName" xml:space="preserve">
    <value>Perioda čítačů jídel [s]</value>
  </data>
  <data name="ServePointTimeoutsConfig_CountersCycle_Description" xml:space="preserve">
    <value>Doba v sekundách, jak dlouho zůstanou zobrazeny údaje o výdavaných alternativách při multidruhovém výdeji jídel (nebo při jednodruhovém výdeji s použitím porcí).</value>
  </data>
  <data name="ServePointAppearanceGridViewConfig_StandardFontColor_Description" xml:space="preserve">
    <value>Standardní barva písma používaná v tabulkách.</value>
  </data>
  <data name="ServePointAppearanceGridViewConfig_StandardFontColor_DisplayName" xml:space="preserve">
    <value>Standardní barva písma</value>
  </data>
  <data name="ServePointAppearanceGridViewConfig_HeaderFontColor_Description" xml:space="preserve">
    <value>Barva písma hlaviček sloupců v tabulkách.</value>
  </data>
  <data name="ServePointAppearanceGridViewConfig_HeaderFontColor_DisplayName" xml:space="preserve">
    <value>Barva písma hlaviček sloupců</value>
  </data>
  <data name="ServePointAppearanceGridViewConfig_HeaderBackgroundColor_Description" xml:space="preserve">
    <value>Barva pozadí hlaviček sloupců v tabulkách.</value>
  </data>
  <data name="ServePointAppearanceGridViewConfig_HeaderBackgroundColor_DisplayName" xml:space="preserve">
    <value>Barva pozadí hlaviček sloupců</value>
  </data>
  <data name="ServePointAppearanceGridViewConfig_EvenRowBackgroundColor_Description" xml:space="preserve">
    <value>Barva pozadí sudých řádků v tabulkách.</value>
  </data>
  <data name="ServePointAppearanceGridViewConfig_EvenRowBackgroundColor_DisplayName" xml:space="preserve">
    <value>Barva pozadí sudých řádků</value>
  </data>
  <data name="ServePointAppearanceGridViewConfig_OddRowBackgroundColor_DisplayName" xml:space="preserve">
    <value>Barva pozadí lichých řádků</value>
  </data>
  <data name="ServePointAppearanceGridViewConfig_OddRowBackgroundColor_Description" xml:space="preserve">
    <value>Barva pozadí lichých řádků v tabulkách.</value>
  </data>
  <data name="ServePointAppearanceGridViewConfig_BackgroundTopColor_DisplayName" xml:space="preserve">
    <value>Barva horního okraje pozadí</value>
  </data>
  <data name="ServePointAppearanceGridViewConfig_BackgroundTopColor_Description" xml:space="preserve">
    <value>Barva horního okraje pozadí tabulek používaných v aplikaci.</value>
  </data>
  <data name="ServePointAppearanceGridViewConfig_BackgroundBottomColor_Description" xml:space="preserve">
    <value>Barva spodního okraje pozadí tabulek používaných v aplikaci.</value>
  </data>
  <data name="ServePointAppearanceGridViewConfig_BackgroundBottomColor_DisplayName" xml:space="preserve">
    <value>Barva spodního okraje pozadí</value>
  </data>
  <data name="ServePointAppearanceGridViewConfig_BorderColor_DisplayName" xml:space="preserve">
    <value>Barva ohraničení</value>
  </data>
  <data name="ServePointAppearanceGridViewConfig_BorderColor_Description" xml:space="preserve">
    <value>Barva ohraničení tabulek.</value>
  </data>
  <data name="ServePointAppearanceGridViewConfig_SeparatorLineColor_Description" xml:space="preserve">
    <value>Barva oddělovacích čar v tabulkách.</value>
  </data>
  <data name="ServePointAppearanceGridViewConfig_SeparatorLineColor_DisplayName" xml:space="preserve">
    <value>Barva oddělovacích čar</value>
  </data>
  <data name="GlobalHwCardKbdReaderConfig_CodeConverterType_DisplayName" xml:space="preserve">
    <value>Konverze kódu karty</value>
  </data>
  <data name="GlobalHwCardKbdReaderConfig_CodeConverterType_Description" xml:space="preserve">
    <value>Určuje typ jak bude zkonvertován kód karty přečtený snímačem.</value>
  </data>
  <data name="SupportContact_Name_Description" xml:space="preserve">
    <value>Název kontaktu</value>
  </data>
  <data name="SupportContact_Name_DisplayName" xml:space="preserve">
    <value>Název</value>
  </data>
  <data name="SupportContact_PhoneNumbers_DisplayName" xml:space="preserve">
    <value>Telefonní čísla</value>
  </data>
  <data name="SupportContact_PhoneNumbers_Description" xml:space="preserve">
    <value>Seznam telefonních čísel oddělených čárkou</value>
  </data>
  <data name="SupportContact_Emails_Description" xml:space="preserve">
    <value>Seznam emailu oddělený čárkou</value>
  </data>
  <data name="SupportContact_Emails_DisplayName" xml:space="preserve">
    <value>E-maily</value>
  </data>
  <data name="SupportContact_Note_DisplayName" xml:space="preserve">
    <value>Poznámka</value>
  </data>
  <data name="SupportContact_Note_Description" xml:space="preserve">
    <value>Libovolná textová poznámka</value>
  </data>
  <data name="SupportContact_WWW_DisplayName" xml:space="preserve">
    <value>WWW</value>
  </data>
  <data name="SupportContact_WWW_Description" xml:space="preserve">
    <value>Webová adresa</value>
  </data>
  <data name="SupportContact_StreetAddress_DisplayName" xml:space="preserve">
    <value>Poštovní adresa</value>
  </data>
  <data name="SupportContact_StreetAddress_Description" xml:space="preserve">
    <value>Poštovní adresa</value>
  </data>
  <data name="ContactsBaseConfig_Contacts_Description" xml:space="preserve">
    <value>Seznam kontaktů</value>
  </data>
  <data name="ContactsBaseConfig_Contacts_DisplayName" xml:space="preserve">
    <value>Kontakty</value>
  </data>
  <data name="GlobalHwCardSerialReaderConfig_CodeConverterType_DisplayName" xml:space="preserve">
    <value>Konverze kódu karty</value>
  </data>
  <data name="GlobalHwCardSerialReaderConfig_CodeConverterType_Description" xml:space="preserve">
    <value>Určuje typ jak bude zkonvertován kód karty přečtený snímačem.</value>
  </data>
  <data name="GlobalHwEanSerialReaderConfig_ReaderType_Description" xml:space="preserve">
    <value>Nastavuje konkrétní typ čtečky.</value>
  </data>
  <data name="GlobalHwEanSerialReaderConfig_ReaderType_DisplayName" xml:space="preserve">
    <value>Typ čtečky</value>
  </data>
  <data name="GlobalHwEanSerialReaderConfig_Settings_DisplayName" xml:space="preserve">
    <value>Nastavení čtečky</value>
  </data>
  <data name="GlobalHwEanSerialReaderConfig_Settings_Description" xml:space="preserve">
    <value>Nastavení různých parametrů čtečky, které přímo závisí na zvoleném typu čtečky.</value>
  </data>
  <data name="SupportContact_BugReportEmail_Description" xml:space="preserve">
    <value>E-mail pro zasílaní chybových hlášení</value>
  </data>
  <data name="SupportContact_BugReportEmail_DisplayName" xml:space="preserve">
    <value>E-mail pro zasílaní chybových hlášení</value>
  </data>
  <data name="OfficeBehaviourPricingConfig_StockAccess_Description" xml:space="preserve">
    <value>Konfigurace přístupu ke skladům v cenotvorbě.</value>
  </data>
  <data name="OfficeBehaviourPricingConfig_StockAccess_DisplayName" xml:space="preserve">
    <value>Přístup ke skladům v cenotvorbě</value>
  </data>
  <data name="ComPortSettings_StopBits_Description" xml:space="preserve">
    <value>Počet stop bitů</value>
  </data>
  <data name="ComPortSettings_StopBits_DisplayName" xml:space="preserve">
    <value>Počet stop bitů</value>
  </data>
  <data name="PresPointBehaviourScreenSaverConfig_Enabled_DisplayName" xml:space="preserve">
    <value>Povolit</value>
  </data>
  <data name="PresPointBehaviourScreenSaverConfig_Enabled_Description" xml:space="preserve">
    <value>Povolit šetříč obrazovky</value>
  </data>
  <data name="PresPointBehaviourScreenSaverConfig_InactivityTime_DisplayName" xml:space="preserve">
    <value>Čas do aktivace</value>
  </data>
  <data name="PresPointBehaviourScreenSaverConfig_InactivityTime_Description" xml:space="preserve">
    <value>Čas v s, po kterém dojde k aktivaci spořiče obrazovky, pokud je spořič povolen.</value>
  </data>
  <data name="PresPointBehaviourScreenSaverConfig_Opacity_Description" xml:space="preserve">
    <value>Intenzita spořiče obrazovky (opak průhlednosti). Rozsah 0 - 100</value>
  </data>
  <data name="PresPointBehaviourScreenSaverConfig_Opacity_DisplayName" xml:space="preserve">
    <value>Intenzita</value>
  </data>
  <data name="GlobalBehaviourAutoUpdatesConfig_EnableAutoUpdates_Description" xml:space="preserve">
    <value>Možnost povolit nebo zakázat automatické aktualizace. Pokud jsou zakázany, nebudou aplikace vůbec využívat aktualizační databázi.</value>
  </data>
  <data name="GlobalBehaviourAutoUpdatesConfig_EnableAutoUpdates_DisplayName" xml:space="preserve">
    <value>Povolit automatické aktualizace</value>
  </data>
  <data name="PresPointAppearanceColorsConfig_MessageForClientsColor_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PresPointAppearanceColorsConfig_MessageForClientsColor_Description" xml:space="preserve">
    <value />
  </data>
  <data name="StockModuleSettingsOrion_ExportDirName_DisplayName" xml:space="preserve">
    <value>Adresář pro export prodejů</value>
  </data>
  <data name="StockModuleSettingsOrion_ExportDirName_Description" xml:space="preserve">
    <value>Sem se exportují údaje o prodejích.</value>
  </data>
  <data name="AutoUpdaterBehaviourMonitorConfig_RestartTimeout_Description" xml:space="preserve">
    <value>Interval, po kterém je proveden další pokus o vyhledání aktualizací poté, co předchozí vyhledávání selhalo.</value>
  </data>
  <data name="AutoUpdaterBehaviourMonitorConfig_RestartTimeout_DisplayName" xml:space="preserve">
    <value>Interval restartu vyhledávání aktualizací</value>
  </data>
  <data name="MenuPresenterBehaviourMenuConfig_MenuType_Description" xml:space="preserve">
    <value>Typ menu</value>
  </data>
  <data name="MenuPresenterBehaviourMenuConfig_MenuType_DisplayName" xml:space="preserve">
    <value>Typ menu</value>
  </data>
  <data name="MenuPresenterBehaviourMenuConfig_MenuSettings_DisplayName" xml:space="preserve">
    <value>Nastavení menu</value>
  </data>
  <data name="MenuPresenterBehaviourMenuConfig_MenuSettings_Description" xml:space="preserve">
    <value>Nastavení menu pro daný typ menu</value>
  </data>
  <data name="MenuPresenterBehaviourMenuConfig_ReloadTimeout_Description" xml:space="preserve">
    <value>Interval, po kterém je provedeno přenačtení jídelníčku</value>
  </data>
  <data name="MenuPresenterBehaviourMenuConfig_ReloadTimeout_DisplayName" xml:space="preserve">
    <value>Interval [s] přenačítání jídelníčku</value>
  </data>
  <data name="WeekMenuSettings_NumberOfDaysFromToday_Description" xml:space="preserve">
    <value>Počet dnů od dnešního dne, pro které bude zobrazen jídelníček</value>
  </data>
  <data name="WeekMenuSettings_NumberOfDaysFromToday_DisplayName" xml:space="preserve">
    <value>Počet dnů</value>
  </data>
  <data name="WeekMenuSettings_ShowDayWithoutMenuDefinition_Description" xml:space="preserve">
    <value>Bude se zobrazovat den, pro který není definován jídelníček (platny != -1)? Pokud ano, bude u tohoto dne zobrazen text "Tento den se nevaří". V opačném případě bude den preskočen.</value>
  </data>
  <data name="WeekMenuSettings_ShowDayWithoutMenuDefinition_DisplayName" xml:space="preserve">
    <value>Zobrazit den bez jídelníčku</value>
  </data>
  <data name="GlobalBehaviourSalesSlipPrintConfig_PrintGoodsLimitBalance_Description" xml:space="preserve">
    <value>Povoluje tisk zůstatku limitu pro prodej sortimentu při prodeji kartou.</value>
  </data>
  <data name="GlobalBehaviourSalesSlipPrintConfig_PrintGoodsLimitBalance_DisplayName" xml:space="preserve">
    <value>Tisknout zůstatek limitu sortimentu</value>
  </data>
  <data name="GlobalBehaviourSalesSlipPrintConfig_PrintMealLimitBalance_DisplayName" xml:space="preserve">
    <value>Tisknout zůstatek limitu jídel</value>
  </data>
  <data name="GlobalBehaviourSalesSlipPrintConfig_PrintMealLimitBalance_Description" xml:space="preserve">
    <value>Povoluje tisk zůstatku limitu pro prodej jídel při prodeji kartou.</value>
  </data>
  <data name="GlobalBehaviourSalesSlipPrintConfig_PrintSubsidies_Description" xml:space="preserve">
    <value>Povoluje tisk přehledu poskytnutých dotací od zaměstnavatele a FKSP.
Pozor: Vyžaduje zákaznickou stored proceduru s názvem dbo.PREFIX_DejPrispevkyParagonu.</value>
  </data>
  <data name="GlobalBehaviourSalesSlipPrintConfig_PrintSubsidies_DisplayName" xml:space="preserve">
    <value>Tisknout poskytnuté dotace od zaměstnavatele a FKSP (vyžaduje zákaznickou SP)</value>
  </data>
  <data name="MenuPresenterBehaviourMenuConfig_ShowDetails_DisplayName" xml:space="preserve">
    <value>Zobrazit poznámku</value>
  </data>
  <data name="MenuPresenterBehaviourMenuConfig_ShowDetails_Description" xml:space="preserve">
    <value>Zobrazit poznámku (detail jídla)</value>
  </data>
  <data name="MenuPresenterBehaviourMenuConfig_AltLanguage_Description" xml:space="preserve">
    <value>Pokud je povoleno, jsou názvy jídel zobrazeny v alternativním jazyce</value>
  </data>
  <data name="MenuPresenterBehaviourMenuConfig_AltLanguage_DisplayName" xml:space="preserve">
    <value>Používat alternativní jazyk</value>
  </data>
  <data name="MenuPresenterBehaviourMenuFilterConfig_IgnoreVisibilitySettings_Description" xml:space="preserve">
    <value>Pokud není zaškrknuto, zobrazují se záznamy z tabulky jídelniček, u kterých hodnota popis≠NULL a zároveň zobrazit=-1. Zaškrknutím se parametr zobrazit ignoruje</value>
  </data>
  <data name="MenuPresenterBehaviourMenuFilterConfig_IgnoreVisibilitySettings_DisplayName" xml:space="preserve">
    <value>Ignorovat nastavení atributu "zobrazit"</value>
  </data>
  <data name="MenuPresenterHideRowColumnConfigBase_HideNegativeValues_DisplayName" xml:space="preserve">
    <value>Skrýt negativní hodnotu</value>
  </data>
  <data name="MenuPresenterHideRowColumnConfigBase_HideNegativeValues_Description" xml:space="preserve">
    <value>Budou se zobrazovat řádky, obsahující negativní hodnotu?</value>
  </data>
  <data name="MenuPresenterPriceColumn_SubsidiesCategory_Description" xml:space="preserve">
    <value>Kategorie dotace</value>
  </data>
  <data name="MenuPresenterPriceColumn_SubsidiesCategory_DisplayName" xml:space="preserve">
    <value>Kategorie dotace</value>
  </data>
  <data name="MenuPresenterAppearanceHeaderConfigBase_BackgroundColor_DisplayName" xml:space="preserve">
    <value>Barva pozadí</value>
  </data>
  <data name="MenuPresenterAppearanceHeaderConfigBase_BackgroundColor_Description" xml:space="preserve">
    <value>Barva pozadí</value>
  </data>
  <data name="MenuPresenterAppearanceHeaderConfigBase_TextColor_Description" xml:space="preserve">
    <value>Barva písma</value>
  </data>
  <data name="MenuPresenterAppearanceHeaderConfigBase_TextColor_DisplayName" xml:space="preserve">
    <value>Barva písma</value>
  </data>
  <data name="MenuPresenterAppearanceHeaderConfigBase_FontSize_Description" xml:space="preserve">
    <value>Velikost písma</value>
  </data>
  <data name="MenuPresenterAppearanceHeaderConfigBase_FontSize_DisplayName" xml:space="preserve">
    <value>Velikost písma</value>
  </data>
  <data name="CashDeskSaleConfig_CheckDisponsibleCreditBallanceInOffline_Description" xml:space="preserve">
    <value>Pokud je zapnuto, v offline režimu se kontroluje prodej vůči poslednímu disponibilnímu zůstatku účtu.
Pokud je vypnuto, v offline režimu se nic nekontroluje a je povolen prodej do záporného zůstatku účtu.

I když je kontrola zapnuta, má tyto omezení, které je třeba brát v úvahu:
* Údaje o stavu účtu se replikují pouze jednou za desítky minut (záleží na nastavení Kasy), tudíž mohou být dost zastaralé. Klient si mohl mezitím vložit peníze na účet nebo vybrat a na údajích se to neprojeví.
* V offline režimu se údaje o stavu účtu při prodeji neaktualizují. Klient má pořád stejný stav účtu, jako měl při poslední replikaci.
* V offline režimu nelze spočítat cenu dotovaného jídla, tudíž se prodává za nedotované ceny a při přechodu do online se tyto ceny aktualizují.</value>
  </data>
  <data name="CashDeskSaleConfig_CheckDisponsibleCreditBallanceInOffline_DisplayName" xml:space="preserve">
    <value>Kontrolovat disponibilní zůstatek účtu v offline</value>
  </data>
  <data name="ServePointBehaviourUserInterfaceConfig_HiddenMouseCursor_Description" xml:space="preserve">
    <value>Určuje, jestli v aplikaci bude vidět kurzor myši.</value>
  </data>
  <data name="ServePointBehaviourUserInterfaceConfig_HiddenMouseCursor_DisplayName" xml:space="preserve">
    <value>Schovaný kurzor myši</value>
  </data>
  <data name="CashDeskSaleConfig_MaxSaleSlipTotalCash_Description" xml:space="preserve">
    <value>Udává maximální cenu hotovostního paragonu, kterou lze namarkovat.</value>
  </data>
  <data name="CashDeskSaleConfig_MaxSaleSlipTotalCash_DisplayName" xml:space="preserve">
    <value>Maximální cena hotovostního paragonu</value>
  </data>
  <data name="CashDeskSaleConfig_MaxSaleSlipTotalCredit_Description" xml:space="preserve">
    <value>Udává maximální cenu bezhotovostního paragonu, kterou lze namarkovat.</value>
  </data>
  <data name="CashDeskSaleConfig_MaxSaleSlipTotalCredit_DisplayName" xml:space="preserve">
    <value>Maximální cena bezhotovostního paragonu</value>
  </data>
  <data name="BowaPaegasFm6v1FiscalModuleSettings_SerialDeviceSettings_Description" xml:space="preserve">
    <value>Nastavení parametrů sériové komunikace</value>
  </data>
  <data name="BowaPaegasFm6v1FiscalModuleSettings_SerialDeviceSettings_DisplayName" xml:space="preserve">
    <value>Parametry sériové komunikace</value>
  </data>
  <data name="AutoUpdaterBehaviourMonitorConfig_RescanAfterUpgradeFoundInterval_Description" xml:space="preserve">
    <value>Doba, o kterou se pozdrží další test nových aktualizací, bezprostředně poté, co byly již aktualizace nalezeny. Vytvořeno protože aktualizace se instalují určitou dobu a během této doby je zbytečné testovat existenci nových aktualizací.</value>
  </data>
  <data name="AutoUpdaterBehaviourMonitorConfig_RescanAfterUpgradeFoundInterval_DisplayName" xml:space="preserve">
    <value>Interval přenačtení aktualizací bezprostředně po nalezení existující aktualizace</value>
  </data>
  <data name="AutoUpdaterBehaviourMonitorConfig_UpgradeDelay_Description" xml:space="preserve">
    <value>Čas, který je dán uživateli k tomu, aby ukončil aplikace po nalezení aktualizací. Jakmile AneteAutoUpgraterMonitor nalezne novou aktualizaci, zobrazí dialog s výzvou k ukončení všech aplikací a po uplynutí této doby zahají jejich instalaci.</value>
  </data>
  <data name="AutoUpdaterBehaviourMonitorConfig_UpgradeDelay_DisplayName" xml:space="preserve">
    <value>Zpoždění instalace aktualizací po jejich nalezení</value>
  </data>
  <data name="VatRoundingRule_CalcLogic_Description" xml:space="preserve">
    <value>Logika, podle které se počítá DPH.</value>
  </data>
  <data name="VatRoundingRule_CalcLogic_DisplayName" xml:space="preserve">
    <value>Logika výpočtu DPH</value>
  </data>
  <data name="ServePointBehaviourUserInterfaceConfig_MultiMealKindType_Description" xml:space="preserve">
    <value>Určuje způsob zobrazení při vícedruhovém výdeji jídel.</value>
  </data>
  <data name="ServePointBehaviourUserInterfaceConfig_MultiMealKindType_DisplayName" xml:space="preserve">
    <value>Způsob zobrazení vícedruhového výdeje</value>
  </data>
  <data name="MenuPresenterAppearanceMenuConfig_DetailFontSize_Description" xml:space="preserve">
    <value>Velikost písma detailu</value>
  </data>
  <data name="MenuPresenterAppearanceMenuConfig_DetailFontSize_DisplayName" xml:space="preserve">
    <value>Velikost písma detailu</value>
  </data>
  <data name="GlobalRulesRoundingConfig_DrawbackRoundingRule_Description" xml:space="preserve">
    <value>Pravidla pro zaokrouhlování srážek ze mzdy</value>
  </data>
  <data name="GlobalRulesRoundingConfig_DrawbackRoundingRule_DisplayName" xml:space="preserve">
    <value>Srážky ze mzdy</value>
  </data>
  <data name="KbFormatKmSettings_BankCode_DisplayName" xml:space="preserve">
    <value>Kód banky (tvar 0000)</value>
  </data>
  <data name="KbFormatKmSettings_BankCode_Description" xml:space="preserve">
    <value>Kód banky ve tvaru 0000</value>
  </data>
  <data name="KbFormatKmSettings_AccountNumber_Description" xml:space="preserve">
    <value>Číslo účtu ve tvaru 000000-***********</value>
  </data>
  <data name="KbFormatKmSettings_AccountNumber_DisplayName" xml:space="preserve">
    <value>Číslo účtu (tvar 000000-***********)</value>
  </data>
  <data name="KbFormatKmSettings_MandatorName_DisplayName" xml:space="preserve">
    <value>Jméno příkazce</value>
  </data>
  <data name="KbFormatKmSettings_MandatorName_Description" xml:space="preserve">
    <value>Jméno příkazce</value>
  </data>
  <data name="FromCashdesksSettings_CashDesks_DisplayName" xml:space="preserve">
    <value>Seznam kas</value>
  </data>
  <data name="FromCashdesksSettings_CashDesks_Description" xml:space="preserve">
    <value>Seznam kas, které budou v menu zobrazeny. Zapisují se id zařízení oddělená čárkou.</value>
  </data>
  <data name="MenuPresenterBehaviourMealCountsConfig_MealCountSource_DisplayName" xml:space="preserve">
    <value>Zdroj počtů</value>
  </data>
  <data name="MenuPresenterBehaviourMealCountsConfig_MealCountSource_Description" xml:space="preserve">
    <value>Zdroj počtů</value>
  </data>
  <data name="MenuPresenterBehaviourMealCountsConfig_MealCountSettings_DisplayName" xml:space="preserve">
    <value>Detailní nastavení zvoleného typu počtů</value>
  </data>
  <data name="MenuPresenterBehaviourMealCountsConfig_MealCountSettings_Description" xml:space="preserve">
    <value>Nastavení vlastností konkrétního typu počtů</value>
  </data>
  <data name="CashDeskPluginsGoodsExtendedInfoConfig_Enabled_Description" xml:space="preserve">
    <value>Pokud je povoleno, v Keyboard kase je přístupná položka Další funkce/Další informace, která zobrazuje pro položku sortimentu další informace pocházající z databáze Sklady.</value>
  </data>
  <data name="CashDeskPluginsGoodsExtendedInfoConfig_Enabled_DisplayName" xml:space="preserve">
    <value>Povolit zobrazování rozšířených informací</value>
  </data>
  <data name="ServePointTimeoutsConfig_LogoutClient_DisplayName" xml:space="preserve">
    <value>Automatické odhlášení [s]</value>
  </data>
  <data name="ServePointTimeoutsConfig_LogoutClient_Description" xml:space="preserve">
    <value>Čas v sekundách, který určuje, po jaké době dojde k odhlášení klienta při nečinnosti.</value>
  </data>
  <data name="SortimentKindGroup_SortimentKinds_DisplayName" xml:space="preserve">
    <value>Druh sortimentu</value>
  </data>
  <data name="SortimentKindGroup_SortimentKinds_Description" xml:space="preserve">
    <value>Udává druh sortimentu</value>
  </data>
  <data name="ServePointBehaviourSellingSettings_SellingSortimentKinds_Description" xml:space="preserve">
    <value>Udává prodávané druhy sortimentu.</value>
  </data>
  <data name="ServePointBehaviourSellingSettings_SellingSortimentKinds_DisplayName" xml:space="preserve">
    <value>Prodávané druhy sortimentu</value>
  </data>
  <data name="GlobalBehaviourDiscountSystemConfig_Enabled_Description" xml:space="preserve">
    <value>Povoluje použítí slevového systému v Kase a v dalších aplikacích.</value>
  </data>
  <data name="GlobalBehaviourDiscountSystemConfig_Enabled_DisplayName" xml:space="preserve">
    <value>Povolit slevový systém</value>
  </data>
  <data name="ServePointBehaviourGeneralSettingsConfig_ServingMealMode_DisplayName" xml:space="preserve">
    <value>Výdej jídel</value>
  </data>
  <data name="ServePointBehaviourGeneralSettingsConfig_ServingMealMode_Description" xml:space="preserve">
    <value>Režim výdeje objednaných jídel.</value>
  </data>
  <data name="ServePointBehaviourGeneralSettingsConfig_SellSortimentMode_Description" xml:space="preserve">
    <value>Režim prodeje volného sortimentu.</value>
  </data>
  <data name="ServePointBehaviourGeneralSettingsConfig_SellSortimentMode_DisplayName" xml:space="preserve">
    <value>Prodej sortimentu</value>
  </data>
  <data name="ServePointBehaviourGeneralSettingsConfig_SellMealsMode_Description" xml:space="preserve">
    <value>Režim prodeje volných jídel, která nejsou objednána.</value>
  </data>
  <data name="ServePointBehaviourGeneralSettingsConfig_SellMealsMode_DisplayName" xml:space="preserve">
    <value>Prodej jídel</value>
  </data>
  <data name="GlobalBehaviourDiscountSystemConfig_RefreshInterval_Description" xml:space="preserve">
    <value>Určuje, po jakém čase v minutách bude klient načítat konfiguraci slev z databáze. 
0 = nepovolit automatické načítání.

Nově přidané slevy se tedy projeví až po tomto intervalu. Časté načítání z databáze zatěžuje SQL server. Proto je třeba volit kompromis.</value>
  </data>
  <data name="GlobalBehaviourDiscountSystemConfig_RefreshInterval_DisplayName" xml:space="preserve">
    <value>Interval znovunačtení slev [min]</value>
  </data>
  <data name="ServePointBehaviourSellingSettings_SellingFirstSelectionMode_DisplayName" xml:space="preserve">
    <value>Výchozí obrazovka prodeje</value>
  </data>
  <data name="ServePointBehaviourSellingSettings_SellingFirstSelectionMode_Description" xml:space="preserve">
    <value>Udává, která obrazovka bude zobrazena ve výchozím stavu.</value>
  </data>
  <data name="StockModuleSettingsAnete_EnableImportOfGoods_DisplayName" xml:space="preserve">
    <value>Povolit tlačítko Aktualizuj zboží Kasy</value>
  </data>
  <data name="StockModuleSettingsAnete_EnableImportOfGoods_Description" xml:space="preserve">
    <value>Pokud je povoleno, pak se zobrazuje v Kase tlačítko Aktualizuj zboží Kasy. 
U některých zakázek aktualizace zboží Kasy způsobovala problémy, proto zaveden tento parametr.</value>
  </data>
  <data name="BowaPaegasFm6v2SerialDeviceSettings_SerialDeviceSettings_DisplayName" xml:space="preserve">
    <value>Parametry sériové komunikace</value>
  </data>
  <data name="BowaPaegasFm6v2SerialDeviceSettings_SerialDeviceSettings_Description" xml:space="preserve">
    <value>Nastavení parametrů sériové komunikace</value>
  </data>
  <data name="GlobalHwEWalletConfig_EWalletType_DisplayName" xml:space="preserve">
    <value>Typ peněženky</value>
  </data>
  <data name="GlobalHwEWalletConfig_EWalletType_Description" xml:space="preserve">
    <value>Nastavuje konkrétní typ peněženky</value>
  </data>
  <data name="GlobalHwEWalletConfig_Settings_DisplayName" xml:space="preserve">
    <value>Nastavení peněženky</value>
  </data>
  <data name="GlobalHwEWalletConfig_Settings_Description" xml:space="preserve">
    <value>Nastavení různých parametrů peněženky, které přímo závisí na zvoleném typu peněženky.</value>
  </data>
  <data name="CashDeskHwEWalletConfig_MaxDisponsibleCreditBalance_Description" xml:space="preserve">
    <value>Pokud disponibilní zůstatek na elektronické peněžence překročí tento limit, bude tato skutečnost zalogována.</value>
  </data>
  <data name="CashDeskHwEWalletConfig_MaxDisponsibleCreditBalance_DisplayName" xml:space="preserve">
    <value>Maximální disponibilní zůstatek </value>
  </data>
  <data name="CashDeskBehaviourMiscellaneousConfig_WarningDisplayTimeout_Description" xml:space="preserve">
    <value>Čas, po který jsou zobrazena varovná hlášení kasy v oblasti pod paragonem.</value>
  </data>
  <data name="CashDeskBehaviourMiscellaneousConfig_WarningDisplayTimeout_DisplayName" xml:space="preserve">
    <value>Timeou zobrazení varování [s]</value>
  </data>
  <data name="CashDeskBehaviourMiscellaneousConfig_InfoDisplayTimeout_DisplayName" xml:space="preserve">
    <value>Timeout zobrazení info [s]</value>
  </data>
  <data name="CashDeskBehaviourMiscellaneousConfig_InfoDisplayTimeout_Description" xml:space="preserve">
    <value>Čas, po který jsou zobrazena informační hlášení kasy v oblasti pod paragonem.</value>
  </data>
  <data name="PresPointBehaviourMenuConfig_NotePosition_DisplayName" xml:space="preserve">
    <value>Umístění poznámky</value>
  </data>
  <data name="PresPointBehaviourMenuConfig_NotePosition_Description" xml:space="preserve">
    <value>Určuje, kde bude umístěna poznámka k jednotlivým druhům jídel v menu.</value>
  </data>
  <data name="OfficeBehaviourCardManagement_Payment_Description" xml:space="preserve">
    <value>Výše záloh na kartu pro ostatní</value>
  </data>
  <data name="OfficeBehaviourCardManagement_Payment_DisplayName" xml:space="preserve">
    <value>Výše zaloh pro zaměstnance</value>
  </data>
  <data name="OfficeBehaviourCardManagement_OthersAdvancePayment_DisplayName" xml:space="preserve">
    <value>Výše záloh pro ostatní</value>
  </data>
  <data name="OfficeBehaviourCardManagement_OthersAdvancePayment_Description" xml:space="preserve">
    <value>Výše záloh na kartu pro ostatní</value>
  </data>
  <data name="PresPointBehaviourCurtainConfig_CurtainType_DisplayName" xml:space="preserve">
    <value>Typ opony</value>
  </data>
  <data name="PresPointBehaviourCurtainConfig_CurtainType_Description" xml:space="preserve">
    <value>Opona se zobrazí po přihlášení nebo odhlášení uživate a zabrání tím viditelnému postupnému zobrazení formuláře.</value>
  </data>
  <data name="PresPointBehaviourCurtainConfig_Settings_Description" xml:space="preserve">
    <value>Nastavení pro daný typ opony</value>
  </data>
  <data name="PresPointBehaviourCurtainConfig_Settings_DisplayName" xml:space="preserve">
    <value>Nastavení opony</value>
  </data>
  <data name="CurtainSettingsBase_ShowDuration_DisplayName" xml:space="preserve">
    <value>Doba zobrazení [ms]</value>
  </data>
  <data name="CurtainSettingsBase_ShowDuration_Description" xml:space="preserve">
    <value>Doba zobrazení určuje čas, který má k dispozici formulář, aby zobrazil potřebné prvky a poté byla opona zavřena.</value>
  </data>
  <data name="CurtainSettingsBase_FadeDuration_Description" xml:space="preserve">
    <value>Doba zprůhlednění určuje čas, po který bude probíhat postupné zprůhlednění opony a tím pádem i zobrazení požadovaného formuláře.</value>
  </data>
  <data name="CurtainSettingsBase_FadeDuration_DisplayName" xml:space="preserve">
    <value>Doba zprůhlednění</value>
  </data>
  <data name="GlobalServicesFbsServicesConfig_CodeTablesConfig_DisplayName" xml:space="preserve">
    <value>Služba pro číselníky (staré)</value>
  </data>
  <data name="GlobalServicesFbsServicesConfig_CodeTablesConfig_Description" xml:space="preserve">
    <value>Konfigurace WCF služby pro číselníky (staré)</value>
  </data>
  <data name="GlobalServicesFbsServicesConfig_DemandsConfig_DisplayName" xml:space="preserve">
    <value>Služba pro požadavky</value>
  </data>
  <data name="GlobalServicesFbsServicesConfig_DemandsConfig_Description" xml:space="preserve">
    <value>Konfigurace WCF služby pro požadavky</value>
  </data>
  <data name="WcfServiceClientConfig_EndpointAddress_Description" xml:space="preserve">
    <value>URL adresa služby</value>
  </data>
  <data name="WcfServiceClientConfig_EndpointAddress_DisplayName" xml:space="preserve">
    <value>URL adresa</value>
  </data>
  <data name="GlobalServicesFbsCredentialsConfig_Subdomain_DisplayName" xml:space="preserve">
    <value>Subdoména</value>
  </data>
  <data name="GlobalServicesFbsCredentialsConfig_Subdomain_Description" xml:space="preserve">
    <value>Subdoména</value>
  </data>
  <data name="GlobalServicesFbsServicesConfig_OrdersConfig_Description" xml:space="preserve">
    <value>Konfigurace WCF služby pro objednávky</value>
  </data>
  <data name="GlobalServicesFbsServicesConfig_OrdersConfig_DisplayName" xml:space="preserve">
    <value>Služba pro objednávky</value>
  </data>
  <data name="AppSingleInstanceConfigBase_OnlySingleInstance_Description" xml:space="preserve">
    <value>Povolit pouze jednu instanci aplikace? Vypnutí tohoto nastavení způsobí, že na jednom PC bude možné spustit aplikaci víckrát.</value>
  </data>
  <data name="AppSingleInstanceConfigBase_OnlySingleInstance_DisplayName" xml:space="preserve">
    <value>Pouze jedna instance</value>
  </data>
  <data name="AnetAttendanceSettings_WcfServiceClientConfig_Description" xml:space="preserve">
    <value>Nastavení webové služby</value>
  </data>
  <data name="AnetAttendanceSettings_WcfServiceClientConfig_DisplayName" xml:space="preserve">
    <value>Nastavení webové služby</value>
  </data>
  <data name="PresPointBahaviourAttendanceConfig_AttendanceType_Description" xml:space="preserve">
    <value>Typ docházky</value>
  </data>
  <data name="PresPointBahaviourAttendanceConfig_AttendanceType_DisplayName" xml:space="preserve">
    <value>Typ docházky</value>
  </data>
  <data name="PresPointBahaviourAttendanceConfig_Settings_Description" xml:space="preserve">
    <value>Nastavení pro daný typ docházky.</value>
  </data>
  <data name="PresPointBahaviourAttendanceConfig_Settings_DisplayName" xml:space="preserve">
    <value>Nastavení docházky</value>
  </data>
  <data name="GlobalBehaviourKitchenOrderPrintConfig_PrintMode_Description" xml:space="preserve">
    <value>Určuje, jak se bude tisknout objednávka do kuchyně.</value>
  </data>
  <data name="GlobalBehaviourKitchenOrderPrintConfig_PrintMode_DisplayName" xml:space="preserve">
    <value>Tisk objednávky</value>
  </data>
  <data name="AutoUpdaterDateTimeSynchronizationConfig_Enabled_Description" xml:space="preserve">
    <value>Povolit synchronizaci času? Po změně není nutný restart Autoupdateru.</value>
  </data>
  <data name="AutoUpdaterDateTimeSynchronizationConfig_Enabled_DisplayName" xml:space="preserve">
    <value>Povolit</value>
  </data>
  <data name="AutoUpdaterDateTimeSynchronizationConfig_SynchronizationInterval_Description" xml:space="preserve">
    <value>Interval, po kterém je provedena další synchronizace času.</value>
  </data>
  <data name="AutoUpdaterDateTimeSynchronizationConfig_SynchronizationInterval_DisplayName" xml:space="preserve">
    <value>Interval synchronizace</value>
  </data>
  <data name="GlobalBehaviourKitchenOrderPrintConfig_OfflineCounterPrefix_Description" xml:space="preserve">
    <value>Číslo, které se bude používat pro výpočet hodnoty čítače v offline režimu. Skutečná hodnota čítače bude Prefix * 10000. V online režimu se použije centrální čítač.</value>
  </data>
  <data name="GlobalBehaviourKitchenOrderPrintConfig_OfflineCounterPrefix_DisplayName" xml:space="preserve">
    <value>Prefix čítače v offline režimu</value>
  </data>
  <data name="GlobalBehaviourKitchenOrderPrintConfig_CounterResetTime_DisplayName" xml:space="preserve">
    <value>Resetovat offline čítače v</value>
  </data>
  <data name="GlobalBehaviourKitchenOrderPrintConfig_CounterResetTime_Description" xml:space="preserve">
    <value>Čas, kdy se resetují offline čítače na hodnotu 1</value>
  </data>
  <data name="ServePointTimeoutsConfig_InfoMessageMinimum_Description" xml:space="preserve">
    <value>Minimální doba v sekundách, po kterou budou zobrazena informativní hlášení.</value>
  </data>
  <data name="ServePointTimeoutsConfig_InfoMessageMinimum_DisplayName" xml:space="preserve">
    <value>Minimální doba informativních hlášení [s]</value>
  </data>
  <data name="ServePointBehaviourUserInterfaceConfig_TopMost_Description" xml:space="preserve">
    <value>Nastavuje oběma oknům Výdejního místa příznak vždy nahoře.</value>
  </data>
  <data name="ServePointBehaviourUserInterfaceConfig_TopMost_DisplayName" xml:space="preserve">
    <value>Vždy nahoře</value>
  </data>
  <data name="GlobalBehaviourKitchenOrderPrintConfig_NumberOfCopies_Description" xml:space="preserve">
    <value>Určuje, kolik kopíí objednávek do kuchyně bude vytištěno. Vytvořeno kvůli Výdejnímu místu, které vyžaduje 2 kopie. Kasa prozatím nerespektuje.</value>
  </data>
  <data name="GlobalBehaviourKitchenOrderPrintConfig_NumberOfCopies_DisplayName" xml:space="preserve">
    <value>Počet kopií</value>
  </data>
  <data name="ServePointTimeoutsConfig_ReloadCounters_Description" xml:space="preserve">
    <value>Perioda, s jakou se přenačítají čítače ve stavu nevydává/neprodává.</value>
  </data>
  <data name="ServePointTimeoutsConfig_ReloadCounters_DisplayName" xml:space="preserve">
    <value>Přenačtění čítačů [s]</value>
  </data>
  <data name="ServePointBehaviourServeringSettingsConfig_AllowServingRecordTypeChange_DisplayName" xml:space="preserve">
    <value>Povolit obsluze změnit typ výdeje</value>
  </data>
  <data name="ServePointBehaviourServeringSettingsConfig_AllowServingRecordTypeChange_Description" xml:space="preserve">
    <value>Určuje, zda obsluha bude mít možnost změnit předdefinovaný typ výdeje. Pokud je povoleno, bude při zahájení výdeje zobrazeno tlačítko, jestli se má provádět pouze registrace.</value>
  </data>
  <data name="GlobalRulesCashOperationsConfig_CashWithdrawalMaxValue_Description" xml:space="preserve">
    <value>Pokud je zadaná hodnota větší než 0, pak je maximální výběr hotovosti omezen zadanou částkou.
0 = neomezovat.</value>
  </data>
  <data name="GlobalRulesCashOperationsConfig_CashWithdrawalMaxValue_DisplayName" xml:space="preserve">
    <value>Maximální hodnota výběru hotovosti</value>
  </data>
  <data name="CashDeskOfflineConfig_CalcMealPriceBySubsidyCategory_DisplayName" xml:space="preserve">
    <value>Povolit výpočet cen podle Kategorie dotace</value>
  </data>
  <data name="CashDeskOfflineConfig_CalcMealPriceBySubsidyCategory_Description" xml:space="preserve">
    <value>Pokud je povoleno, pak se ceny jídla v offline nepočítají jako maximální, ale počítá se správná cena podle kategorií dotace.
Omezení: Funguje pouze za předpokladu, že nebude použit kalkulovaný ceník, nebudou denní limity, bonový systém, docházka.</value>
  </data>
  <data name="GlobalServicesFbsServicesConfig_DeliveriesConfig_DisplayName" xml:space="preserve">
    <value>Služba pro dodávky</value>
  </data>
  <data name="GlobalServicesFbsServicesConfig_DeliveriesConfig_Description" xml:space="preserve">
    <value>Konfigurace WCF služby pro požadavky</value>
  </data>
  <data name="CnbOsuEBankingSettings_AccountNumber_DisplayName" xml:space="preserve">
    <value>Číslo účtu</value>
  </data>
  <data name="CnbOsuEBankingSettings_AccountNumber_Description" xml:space="preserve">
    <value>Číslo účtu</value>
  </data>
  <data name="CnbOsuEBankingSettings_ClientCode_Description" xml:space="preserve">
    <value>Kód klienta</value>
  </data>
  <data name="CnbOsuEBankingSettings_ClientCode_DisplayName" xml:space="preserve">
    <value>Kód klienta</value>
  </data>
  <data name="BatchDataOperationsParametersConfig_CancelAllOrders_Description" xml:space="preserve">
    <value>Pokud je povoleno, po ukončení platnosti EL zruší všechny objednávky i mimo pravidla, pošle SMS.
Pokud je zakázáno, nastaví PlatnostDo na poslední nesmazatelnou objednávku.</value>
  </data>
  <data name="BatchDataOperationsParametersConfig_CancelAllOrders_DisplayName" xml:space="preserve">
    <value>Zrušit všechny objednávky</value>
  </data>
  <data name="BatchDataOperationsParametersConfig_OpenClosedAccounts_DisplayName" xml:space="preserve">
    <value>Otevřít uzavřené účty</value>
  </data>
  <data name="BatchDataOperationsParametersConfig_OpenClosedAccounts_Description" xml:space="preserve">
    <value>Pokud je povoleno, otevře uzavřený účet.
Pokud je zakázáno, zahlásí chybu, pokud najde uzavřený účet.</value>
  </data>
  <data name="BatchDataOperationsParametersConfig_AllowDuplicateSAN_DisplayName" xml:space="preserve">
    <value>Povolit přidělení existujícího SAN novému strávníkovi a odebrání původnímu</value>
  </data>
  <data name="BatchDataOperationsParametersConfig_AllowDuplicateSAN_Description" xml:space="preserve">
    <value>Pokud je povoleno, je umožněno přidělit již existující SAN novému strávníkovi.</value>
  </data>
  <data name="MenuPresenterBehaviourMenuFilterConfig_FilterByMealGroup_DisplayName" xml:space="preserve">
    <value>Filtrování dle seskupení jídel</value>
  </data>
  <data name="MenuPresenterBehaviourMenuFilterConfig_FilterByMealGroup_Description" xml:space="preserve">
    <value>Filtrovat dle seskupení jídel? V takovém připadě se vůbec nepoužije zakladní filter v Konfigurátoru. Vytvořeno jako nouzové řešení pro STU, protože nejsou implementována práva pro Konfigurátor.</value>
  </data>
  <data name="CashDeskFiscalModuleConfig_WriteGoodsFromNonCashSalesSlipToFiscal_Description" xml:space="preserve">
    <value>Pokud je zapnuto, bude bezhotovostní paragon rozdělen na dva: Na bezhotovostní část bez sortimentu - ta se zapíše pouze do databáze Kredit a na hotovostní část se sortimentem, která se zapíše do fiskálu i do databáze Kredit. Řeší se tím požadavek STU.
Pokud je vypnuto, do fiskálu se zapisují pouze hotovostní paragony.</value>
  </data>
  <data name="CashDeskFiscalModuleConfig_WriteGoodsFromNonCashSalesSlipToFiscal_DisplayName" xml:space="preserve">
    <value>Zapisovat zboží z bezhotovostních paragonů do fiskálu</value>
  </data>
  <data name="OfficeMealDistributionBehaviourInquiriesConfig_AllowChangeOldInquiries_DisplayName" xml:space="preserve">
    <value>Povolit změnu starých poptávek</value>
  </data>
  <data name="OfficeMealDistributionBehaviourInquiriesConfig_AllowChangeOldInquiries_Description" xml:space="preserve">
    <value>Povolení změny poptávek určených pro datum menši než dnes. Zůstává zachováno, že nelze změnit poptávky v uzavřeném účetním období.</value>
  </data>
  <data name="PresPointBehaviourMenuConfig_AllowAltMenu_DisplayName" xml:space="preserve">
    <value>Povolit přepínání mezi běžným a alternativním jídelníčkem</value>
  </data>
  <data name="PresPointBehaviourMenuConfig_AllowAltMenu_Description" xml:space="preserve">
    <value>Pokud je povoleno, klient má možnost si po přihlášení zvolit, zda chce mít zobrazen standardní nebo alternativní jídelníček. Ihned po přihlášení je mu zobrazen jídelníček, který odpovádá nastavení v evidenčním listu. Pokud je zakázano, zobrazí se jídelníček dle evidenčního listu klienta.</value>
  </data>
  <data name="MenuPresenterBehaviourColumnsConfig_Price2_Description" xml:space="preserve">
    <value>Alternativní cena. Je u ní možné nastavit jinou kategorii dotace. Používá se např pro zobrazení ceny bez DPH.</value>
  </data>
  <data name="MenuPresenterBehaviourColumnsConfig_Price2_DisplayName" xml:space="preserve">
    <value>Alternativní cena</value>
  </data>
  <data name="MenuPresenterPriceColumn_Caption_Description" xml:space="preserve">
    <value>Název sloupce. Pokud není nastaveno, použije se předdefinovaná lokalizovaná hodnota.</value>
  </data>
  <data name="MenuPresenterPriceColumn_Caption_DisplayName" xml:space="preserve">
    <value>Název sloupce</value>
  </data>
  <data name="MenuPresenterBehaviourMenuFilterConfig_TimeFilter_Description" xml:space="preserve">
    <value>Filtrování druhů jídel dle času</value>
  </data>
  <data name="MenuPresenterBehaviourMenuFilterConfig_TimeFilter_DisplayName" xml:space="preserve">
    <value>Filtrovaní dle času</value>
  </data>
  <data name="MenuPresenterMealKindIntervalSetting_MealKindId_DisplayName" xml:space="preserve">
    <value>Id druhu jídla</value>
  </data>
  <data name="MenuPresenterMealKindIntervalSetting_MealKindId_Description" xml:space="preserve">
    <value>Id druhu jídla, pro kterého se definují intervaly, kdy má být daný druh jídla zobrazen.</value>
  </data>
  <data name="MenuPresenterMealKindIntervalSetting_Intervals_Description" xml:space="preserve">
    <value>Seznam intervalů, v kterých má být druh jídla rozbazen.</value>
  </data>
  <data name="MenuPresenterMealKindIntervalSetting_Intervals_DisplayName" xml:space="preserve">
    <value>Intervaly</value>
  </data>
  <data name="CashDeskSaleConfig_SettleUpCreditBalanceOnKartouCash_DisplayName" xml:space="preserve">
    <value>Vyrovnat záporný zůstatek účtu při platbě Kartou-Cash</value>
  </data>
  <data name="CashDeskSaleConfig_SettleUpCreditBalanceOnKartouCash_Description" xml:space="preserve">
    <value>Pokud je povoleno a při platbě Kartou-Cash má klient záporný zůstatek účtu, pak se do celkové úhrady paragonu započítá i vyrovnání záporného zůstatku účtu.
Není možno kombinovat s "Kartou-Cash doplácí pouze záporný zůstatek účtu".</value>
  </data>
  <data name="GlobalServicesDbConnectionConfig_AneteUpdatesConfig_Description" xml:space="preserve">
    <value>Konfigurace připojení k databázi AneteUpdates</value>
  </data>
  <data name="GlobalServicesDbConnectionConfig_AneteUpdatesConfig_DisplayName" xml:space="preserve">
    <value>AneteUpdates</value>
  </data>
  <data name="GlobalServicesDbConnectionConfig_SkladyConfig_Description" xml:space="preserve">
    <value>Konfigurace připojení k databázi Sklady</value>
  </data>
  <data name="GlobalServicesDbConnectionConfig_SkladyConfig_DisplayName" xml:space="preserve">
    <value>Sklady</value>
  </data>
  <data name="DbConnectionConfig_Server_Description" xml:space="preserve">
    <value>Název serveru.</value>
  </data>
  <data name="DbConnectionConfig_Server_DisplayName" xml:space="preserve">
    <value>Server</value>
  </data>
  <data name="DbConnectionConfig_Database_DisplayName" xml:space="preserve">
    <value>Databáze</value>
  </data>
  <data name="DbConnectionConfig_Database_Description" xml:space="preserve">
    <value>Název databáze, ke které se připojuji.</value>
  </data>
  <data name="DbConnectionConfig_AuthenticationType_DisplayName" xml:space="preserve">
    <value>Typ autentizace</value>
  </data>
  <data name="DbConnectionConfig_UserName_DisplayName" xml:space="preserve">
    <value>Přihlašovací jméno</value>
  </data>
  <data name="DbConnectionConfig_UserName_Description" xml:space="preserve">
    <value>Jméno uživatele. Použije se pouze v případě, že je zadán druh autentizace "Uživatelské jméno a heslo".</value>
  </data>
  <data name="DbConnectionConfig_Password_DisplayName" xml:space="preserve">
    <value>Heslo</value>
  </data>
  <data name="DbConnectionConfig_Password_Description" xml:space="preserve">
    <value>Heslo uživatele. Použije se pouze v případě, že je zadán druh autentizace "Uživatelské jméno a heslo".</value>
  </data>
  <data name="DbConnectionConfig_ConnectTimeout_Description" xml:space="preserve">
    <value>Doba, po kterou se čeká na vytvoření připojení k databázi.</value>
  </data>
  <data name="DbConnectionConfig_ConnectTimeout_DisplayName" xml:space="preserve">
    <value>Timeout připojení</value>
  </data>
  <data name="GlobalBehaviourTechSupportMessagingConfig_Enabled_Description" xml:space="preserve">
    <value>Celkové povolení systému zasílání zpráv
Pokud je zakázáno, žádná hlášení nebudou odesílána.</value>
  </data>
  <data name="GlobalBehaviourTechSupportMessagingConfig_Enabled_DisplayName" xml:space="preserve">
    <value>Povolit</value>
  </data>
  <data name="GlobalBehaviourTechSupportMessagingConfig_ApplicationFail_DisplayName" xml:space="preserve">
    <value>Zasílání zpráv o pádech aplikace</value>
  </data>
  <data name="GlobalBehaviourTechSupportMessagingConfig_ApplicationFail_Description" xml:space="preserve">
    <value>Nastavuje odesílání zpráv při pádu aplikace.</value>
  </data>
  <data name="GlobalBehaviourTechSupportMessagingConfig_TransportType_DisplayName" xml:space="preserve">
    <value>Typ transportu</value>
  </data>
  <data name="GlobalBehaviourTechSupportMessagingConfig_TransportType_Description" xml:space="preserve">
    <value>Nastavuje, jakým způsobem se budou zprávy přenášet dále.</value>
  </data>
  <data name="GlobalBehaviourTechSupportMessagingConfig_UserMessages_DisplayName" xml:space="preserve">
    <value>Zasílání uživatelských zpráv</value>
  </data>
  <data name="GlobalBehaviourTechSupportMessagingConfig_UserMessages_Description" xml:space="preserve">
    <value>Konfigurace zasílání uživatelských zpráv</value>
  </data>
  <data name="ApplicationErrorSettingsBase_Enabled_DisplayName" xml:space="preserve">
    <value>Povolit</value>
  </data>
  <data name="ApplicationErrorSettingsBase_Enabled_Description" xml:space="preserve">
    <value>Povolit zasílání chybových zpráv o pádu aplikace.</value>
  </data>
  <data name="UserMessagesSettings_Enabled_DisplayName" xml:space="preserve">
    <value>Povolit</value>
  </data>
  <data name="UserMessagesSettings_Enabled_Description" xml:space="preserve">
    <value>Povolit zasílání uživatelských zpráv</value>
  </data>
  <data name="UserMessagesSettings_MessageBoxItemEnabled_Description" xml:space="preserve">
    <value>Povolit zasílání uživatelských zpráv z každého message boxu</value>
  </data>
  <data name="UserMessagesSettings_MessageBoxItemEnabled_DisplayName" xml:space="preserve">
    <value>Z message boxu</value>
  </data>
  <data name="UserMessagesSettings_MainMenuItemEnabled_Description" xml:space="preserve">
    <value>Povolit zasílání uživatelských zpráv z hlavního menu aplikace</value>
  </data>
  <data name="UserMessagesSettings_MainMenuItemEnabled_DisplayName" xml:space="preserve">
    <value>Z hlavního menu</value>
  </data>
  <data name="GlobalServicesSmtpConfig_SmtpPort_DisplayName" xml:space="preserve">
    <value>SMTP port</value>
  </data>
  <data name="GlobalServicesSmtpConfig_SmtpPort_Description" xml:space="preserve">
    <value>Číslo portu, na kterém jsou odesílány emaily přes SMTP server.</value>
  </data>
  <data name="UserMessagesSettings_ScreenShotAttachmentMode_DisplayName" xml:space="preserve">
    <value>Snímek obrazovky</value>
  </data>
  <data name="UserMessagesSettings_ScreenShotAttachmentMode_Description" xml:space="preserve">
    <value>Jak bude odesílán snímek obrazovky.</value>
  </data>
  <data name="UserMessagesSettings_LogsAttachmentMode_Description" xml:space="preserve">
    <value>Jak budou odesílány zazipované logy aplikace.</value>
  </data>
  <data name="UserMessagesSettings_LogsAttachmentMode_DisplayName" xml:space="preserve">
    <value>Logy</value>
  </data>
  <data name="UserMessagesSettings_AttachmentSettings_DisplayName" xml:space="preserve">
    <value>Odesílané přílohy</value>
  </data>
  <data name="UserMessagesSettings_AttachmentSettings_Description" xml:space="preserve">
    <value>Nastavení toho, které přílohy se odesílají.</value>
  </data>
  <data name="ApplicationErrorSettingsBase_AttachmentSettings_Description" xml:space="preserve">
    <value>Nastavení toho, které přílohy se odesílají.</value>
  </data>
  <data name="ApplicationErrorSettingsBase_AttachmentSettings_DisplayName" xml:space="preserve">
    <value>Odesílané přílohy</value>
  </data>
  <data name="GlobalBehaviourTechSupportMessagingConfig_CommandProcessorSettings_DisplayName" xml:space="preserve">
    <value>Příkazový procesor</value>
  </data>
  <data name="GlobalBehaviourTechSupportMessagingConfig_CommandProcessorSettings_Description" xml:space="preserve">
    <value>Nastavení příkazového procesoru</value>
  </data>
  <data name="CommandProcessorSettings_Enabled_Description" xml:space="preserve">
    <value>Pokud je povoleno, je příkazový procesor aktivní.</value>
  </data>
  <data name="CommandProcessorSettings_Enabled_DisplayName" xml:space="preserve">
    <value>Povolit</value>
  </data>
  <data name="CommandProcessorSettings_PollingInterval_Description" xml:space="preserve">
    <value>Interval, ve kterém se procesor příkazů dotazuje fronty.</value>
  </data>
  <data name="CommandProcessorSettings_PollingInterval_DisplayName" xml:space="preserve">
    <value>Dotazovací interval [s]</value>
  </data>
  <data name="GlobalBehaviourTechSupportMessagingConfig_LocalQueue_DisplayName" xml:space="preserve">
    <value>Lokální fronta</value>
  </data>
  <data name="GlobalBehaviourTechSupportMessagingConfig_LocalQueue_Description" xml:space="preserve">
    <value>Nastavení lokální fronty - v ní se shromažďují odeslané zprávy na místním počítači.</value>
  </data>
  <data name="LocalQueueSettings_SentQueueMaxSize_DisplayName" xml:space="preserve">
    <value>Maximální velikost fronty s odeslanými zprávami [MB]</value>
  </data>
  <data name="LocalQueueSettings_SentQueueMaxSize_Description" xml:space="preserve">
    <value>Maximální velikost fronty s odeslanými zprávami. Po překročení této velikosti se nejstarší zprávy vymažou.</value>
  </data>
  <data name="MobileOrderingBehaviorAuthorizationConfig_AuthorizationType_DisplayName" xml:space="preserve">
    <value>Autorizační modul</value>
  </data>
  <data name="MobileOrderingBehaviorAuthorizationConfig_AuthorizationType_Description" xml:space="preserve">
    <value>Typ autorizačního modulu.</value>
  </data>
  <data name="MobileOrderingBehaviorAuthorizationConfig_Settings_DisplayName" xml:space="preserve">
    <value>Nastavení</value>
  </data>
  <data name="MobileOrderingBehaviorAuthorizationConfig_Settings_Description" xml:space="preserve">
    <value>Nastavení parametrů autorizačního modulu.</value>
  </data>
  <data name="LdapServerConfig_Address_Description" xml:space="preserve">
    <value>Název serveru nebo jeho IP adresa</value>
  </data>
  <data name="LdapServerConfig_Address_DisplayName" xml:space="preserve">
    <value>Adresa</value>
  </data>
  <data name="LdapServerConfig_Port_Description" xml:space="preserve">
    <value>Port, na kterém komunikuje server. 
0 znamená použít implicitní hodnotu, což je 389 bez SSL a 636 s SSL.</value>
  </data>
  <data name="LdapServerConfig_Port_DisplayName" xml:space="preserve">
    <value>Port</value>
  </data>
  <data name="LdapServerConfig_UseSsl_Description" xml:space="preserve">
    <value>Použít SSL při komunikaci se serverem.</value>
  </data>
  <data name="LdapServerConfig_UseSsl_DisplayName" xml:space="preserve">
    <value>Použít SSL</value>
  </data>
  <data name="LdapServerConfig_ProtocolVersion_DisplayName" xml:space="preserve">
    <value>Verze LDAP protokolu</value>
  </data>
  <data name="LdapServerConfig_ProtocolVersion_Description" xml:space="preserve">
    <value>Verze LDAP protokolu. Sděluje administrátor systému.</value>
  </data>
  <data name="LdapServerConfig_AuthType_DisplayName" xml:space="preserve">
    <value>Typ autorizace</value>
  </data>
  <data name="LdapServerConfig_AuthType_Description" xml:space="preserve">
    <value>Typ autorizace serveru. Sděluje administrátor systému.</value>
  </data>
  <data name="LdapAuthorizationSettingsBase_ServerConfig_DisplayName" xml:space="preserve">
    <value>LDAP server</value>
  </data>
  <data name="LdapAuthorizationSettingsBase_ServerConfig_Description" xml:space="preserve">
    <value>Nastavení parametrů LDAP serveru</value>
  </data>
  <data name="LdapServiceCredentialsConfig_UserName_DisplayName" xml:space="preserve">
    <value>Uživatelské jméno</value>
  </data>
  <data name="LdapServiceCredentialsConfig_UserName_Description" xml:space="preserve">
    <value>Přihlašovací jméno uživatele</value>
  </data>
  <data name="LdapServiceCredentialsConfig_Password_DisplayName" xml:space="preserve">
    <value>Heslo</value>
  </data>
  <data name="LdapServiceCredentialsConfig_Password_Description" xml:space="preserve">
    <value>Heslo uživatele</value>
  </data>
  <data name="LdapServiceCredentialsConfig_Domain_Description" xml:space="preserve">
    <value>Doména, do které se přihlašuje uživatel.</value>
  </data>
  <data name="LdapServiceCredentialsConfig_Domain_DisplayName" xml:space="preserve">
    <value>Doména</value>
  </data>
  <data name="LdapAuthorizationSettingsBase_ServiceUserCredentials_Description" xml:space="preserve">
    <value>Pod tímto uživatelem se provede prvotní hledání v LDAP stromu. Sděluje administrátor systému.</value>
  </data>
  <data name="LdapAuthorizationSettingsBase_ServiceUserCredentials_DisplayName" xml:space="preserve">
    <value>Přihlášení servisního uživatele</value>
  </data>
  <data name="LdapAuthorizationSettingsBase_SearchRequestConfig_Description" xml:space="preserve">
    <value>Nastavení parametrů pro vyhledávání v LDAP stromu</value>
  </data>
  <data name="LdapAuthorizationSettingsBase_SearchRequestConfig_DisplayName" xml:space="preserve">
    <value>Vyhledávání ve stromu</value>
  </data>
  <data name="LdapSearchRequestConfig_BaseDN_DisplayName" xml:space="preserve">
    <value>BaseDN</value>
  </data>
  <data name="LdapSearchRequestConfig_BaseDN_Description" xml:space="preserve">
    <value>Atribut BaseDN, který se používá jako kořen stromu pro vyhledávání. Sděluje administrátor systému.</value>
  </data>
  <data name="LdapSearchRequestConfig_SearchRequest_DisplayName" xml:space="preserve">
    <value>Vyhledávací dotaz</value>
  </data>
  <data name="LdapSearchRequestConfig_SearchRequest_Description" xml:space="preserve">
    <value>Formátovací řetězec pro vyhledávání v LDAP stromu. Sděluje administrátor systému.
Formátovací parametry: {UserName} název přihlašovaného uživatele.
Běžný tvar je uid={UserName}.</value>
  </data>
  <data name="MobileOrderingBehaviorAuthorizationConfig_AuthorizationCacheExpiration_DisplayName" xml:space="preserve">
    <value>Doba expirace autorizační cache [s]</value>
  </data>
  <data name="MobileOrderingBehaviorAuthorizationConfig_AuthorizationCacheExpiration_Description" xml:space="preserve">
    <value>Autorizační cache zajišťuje, aby se nemusela opakovaně volat autorizační procedura pro přihlášení uživatele. Je důležité zejména u pomalých autorizací, např. LDAP.
Po uplynutí doby doby expirace se znovu provede volání procedury pro přihlášení.</value>
  </data>
  <data name="GlobalRulesCashOperationsConfig_AllowCashDepositToFreeAccount_Description" xml:space="preserve">
    <value>Povoluje vklad na volný účet klienta.
Pokud je povoleno, je vklad na volný účet umožněn po kontrolním dotazu,
Pokud je zakázáno, vklad na volný účet není možný.</value>
  </data>
  <data name="GlobalRulesCashOperationsConfig_AllowCashDepositToFreeAccount_DisplayName" xml:space="preserve">
    <value>Povolit vklad na volný účet</value>
  </data>
  <data name="OfficeClientsBehaviourEBankingImportConfig_MaximumAmmount_DisplayName" xml:space="preserve">
    <value>Maximální částka</value>
  </data>
  <data name="OfficeClientsBehaviourEBankingImportConfig_MaximumAmmount_Description" xml:space="preserve">
    <value>Přednastavená hodnota maximální částky. Všechny importované platby, které přesáhnou tuto částku, budou označeny jako podezřelé a nebudou označeny pro import.</value>
  </data>
  <data name="CashDeskSaleConfig_EnableItemReturnWithoutSale_Description" xml:space="preserve">
    <value>Pokud je povoleno, pak je možné na paragonu stornovat i položky, které nebyly na paragonu dříve prodány.
Pokud je zakázáno, je možné pouze stornování položek v prostoru paragonu.</value>
  </data>
  <data name="CashDeskSaleConfig_EnableItemReturnWithoutSale_DisplayName" xml:space="preserve">
    <value>Povolit stornování položek bez předchozího prodeje</value>
  </data>
  <data name="VolksbankAboEBankingSettings_AccountNumber_Description" xml:space="preserve">
    <value>Číslo účtu ve tvaru 000000-***********</value>
  </data>
  <data name="VolksbankAboEBankingSettings_AccountNumber_DisplayName" xml:space="preserve">
    <value>Číslo účtu (tvar 000000-***********)</value>
  </data>
  <data name="VolksbankAboEBankingSettings_BankCode_Description" xml:space="preserve">
    <value>Kód banky</value>
  </data>
  <data name="VolksbankAboEBankingSettings_BankCode_DisplayName" xml:space="preserve">
    <value>Kód banky</value>
  </data>
  <data name="VolksbankAboEBankingSettings_Description_Description" xml:space="preserve">
    <value>Popis</value>
  </data>
  <data name="VolksbankAboEBankingSettings_Description_DisplayName" xml:space="preserve">
    <value>Popis</value>
  </data>
  <data name="PresPointBehaviourMenuConfig_ShowOrderedMealCount_Description" xml:space="preserve">
    <value>Zobrazí v menu počet objednaných jídel. Vytvořeno proto, aby strávník měl přehled o počtu objednaných jídel, protože na některých zakázkách se jídla vaří až od určítého počtu. On se díky tomu může včas rozhodnout a objednávku změnit.</value>
  </data>
  <data name="PresPointBehaviourMenuConfig_ShowOrderedMealCount_DisplayName" xml:space="preserve">
    <value>Zobrazit počet objednaných jídel</value>
  </data>
  <data name="PresPointBehaviourMenuConfig_OrdersMidnightOffset_Description" xml:space="preserve">
    <value>Posunutí umožní zobrazit objednávky z předešlého dne. Využívá VSČR k tisku stravenek k jídlům, jejichž výdej probíhá přes půlnoc.</value>
  </data>
  <data name="PresPointBehaviourMenuConfig_OrdersMidnightOffset_DisplayName" xml:space="preserve">
    <value>Posunutí půlnoci [h]</value>
  </data>
  <data name="OfficeClientsBehaviourCardStockConfig_Enable_Description" xml:space="preserve">
    <value>Povolení/zakázaní skladu karet. Pokud je sklad karet zakázán, Kancelář 8 provede po spuštění fiktivní příjem karet.</value>
  </data>
  <data name="OfficeClientsBehaviourCardStockConfig_Enable_DisplayName" xml:space="preserve">
    <value>Povolit</value>
  </data>
  <data name="MobileOrderingBehaviorMenuConfig_ShowMealFromExchangeIcon_DisplayName" xml:space="preserve">
    <value>Zobrazit ikonu pokud je jídlo z burzy</value>
  </data>
  <data name="MobileOrderingBehaviorMenuConfig_ShowMealFromExchangeIcon_Description" xml:space="preserve">
    <value>Pokud je povoleno, zobrazuje se u jídel z burzy ikona.</value>
  </data>
  <data name="MobileOrderingBehaviorMenuConfig_ShowMealPicture_DisplayName" xml:space="preserve">
    <value>Zobrazit obrázek jídla</value>
  </data>
  <data name="MobileOrderingBehaviorMenuConfig_ShowMealPicture_Description" xml:space="preserve">
    <value>Pokud je povoleno a k jídlu existuje obrázek, je možno zobrazit obrázek jídla.</value>
  </data>
  <data name="MobileOrderingBehaviorMenuConfig_ShowUnorderableMeals_Description" xml:space="preserve">
    <value>Globální povolení/zakázání zobrazení jídel, která nejdou objednat. 
Pokud je povoleno, zobrazují se i jídla, která nemohou být objednána např. proto, že už uplynula doba objednání. Pak jsou v platnosti další volby v této sekci, které povolují nebo zakazují zobrazení těchto jídel.
Pokud je zakázáno, pak se zobrazují pouze jídla, která je možné objednat. Další volby v této sekci se ignorují.</value>
  </data>
  <data name="MobileOrderingBehaviorMenuConfig_ShowUnorderableMeals_DisplayName" xml:space="preserve">
    <value>Zobrazit i jídla, která nemohou být objednána</value>
  </data>
  <data name="OfficeClientsBehaviourCardIssueConfig_CreateNewCardBy_Description" xml:space="preserve">
    <value>Umožnuje nastavit implicitní způsob výdeje karty.</value>
  </data>
  <data name="OfficeClientsBehaviourCardIssueConfig_CreateNewCardBy_DisplayName" xml:space="preserve">
    <value>Způsob výdeje</value>
  </data>
  <data name="PresPointBehaviourLoginConfig_AllowLogoutButton_DisplayName" xml:space="preserve">
    <value>Povolit odhlašovací tlačítko</value>
  </data>
  <data name="PresPointBehaviourLoginConfig_AllowLogoutButton_Description" xml:space="preserve">
    <value>Povolení odhlašovacího tlačítka umožní klientům potvrdit změny v PM bez nutnosti přikládat opětovně kartu. Nevyhodou je, že v připadě, že se klient neodhlásí, můžou mu  být někým jiným změněny objednávky.</value>
  </data>
  <data name="GlobalBehaviourSalesSlipPrintConfig_PrintPriceListComponents_DisplayName" xml:space="preserve">
    <value>Tisknout seznam ceníkových složek</value>
  </data>
  <data name="GlobalBehaviourSalesSlipPrintConfig_PrintPriceListComponents_Description" xml:space="preserve">
    <value>Pokud je povoleno, na paragonu se tiskne seznam ceníkových složek, ze kterých se skládá cena jídla. Tedy např. Plná cena, Dotace, Dotovaná cena. Seznam složek, které se tisknou, je v Ceníkové složky k tisku.</value>
  </data>
  <data name="GlobalBehaviourSalesSlipPrintConfig_PriceListComponentIds_DisplayName" xml:space="preserve">
    <value>Ceníkové složky k tisku</value>
  </data>
  <data name="GlobalBehaviourSalesSlipPrintConfig_PriceListComponentIds_Description" xml:space="preserve">
    <value>Seznam Id ceníkových složek, které se mají tisknout. Zadávejte jako čísla oddělená čárkami. Ceníkové složky najdete v tabulce SCC_CENIK_NAZVY.</value>
  </data>
  <data name="OfficeMealDistributionBehaviourInquirySummaryConfig_ShowOrderCount_Description" xml:space="preserve">
    <value>Zobrazí předem objednaná jídla z tabulky objednávky. Pokud je povoleno, zároveň se zobrazí i sloupec počet celkem, definovaný jako součet z počet poptávaný a počet objednaný.</value>
  </data>
  <data name="OfficeMealDistributionBehaviourInquirySummaryConfig_ShowOrderCount_DisplayName" xml:space="preserve">
    <value>Zobrazit předem objednaná jídla</value>
  </data>
  <data name="OfficeBehaviourBalanceConfig_EnableNegativeDrawback_Description" xml:space="preserve">
    <value>Pokud je povoleno, systém vrací při uzávěrce přebytky účtu do mzdy (záporná srážka = příplatek ke mzdě).
Pokud je zakázáno, systém kladné zůstatky na účtu ponechá.</value>
  </data>
  <data name="OfficeBehaviourBalanceConfig_EnableNegativeDrawback_DisplayName" xml:space="preserve">
    <value>Povolit záporné srážky ze mzdy</value>
  </data>
  <data name="OfficeClientsBehaviourCardStockConfig_VisibleFrom_Description" xml:space="preserve">
    <value>Datum, od kdy se mají zobrazovat pohyby karet. Řeší se tím to, aby obsluhy neviděly pohyby, které byly vytvořeny před vynulováním stavu karet. Pokud není nastaveno, je možno zobrazit pohyby od libovolného data.</value>
  </data>
  <data name="OfficeClientsBehaviourCardStockConfig_VisibleFrom_DisplayName" xml:space="preserve">
    <value>Zobrazovat pohyby od</value>
  </data>
  <data name="OfficeMealDistributionBehaviourInquiriesConfig_InquiryMealEndTimeOffset_Description" xml:space="preserve">
    <value>Posunutí [h] stop času poptávání. Ovlivní i čas pro změnu počtu schlazených položek.</value>
  </data>
  <data name="OfficeMealDistributionBehaviourInquiriesConfig_InquiryMealEndTimeOffset_DisplayName" xml:space="preserve">
    <value>Posun [h] stop času poptávání</value>
  </data>
  <data name="MobileOrderingBehaviorMenuConfig_AcceptShowInMenuFlag_Description" xml:space="preserve">
    <value>Pokud je povoleno, zobrazují se pouze jídla se zapnutým příznakem Zobrazit, tedy určená pro objednávání.
Pokud je zakázáno, zobrazují se všechna jídla. Jídla, která nejdou objednat, se zobrazují s hlášením, že nejdou objednat.</value>
  </data>
  <data name="MobileOrderingBehaviorMenuConfig_AcceptShowInMenuFlag_DisplayName" xml:space="preserve">
    <value>Respektovat příznak Zobrazit</value>
  </data>
  <data name="MobileOrderingBehaviorMenuConfig_ShowMealsWithOrderInAdvance_DisplayName" xml:space="preserve">
    <value>Zobrazit jídla označené "Objednat dopředu"</value>
  </data>
  <data name="MobileOrderingBehaviorMenuConfig_ShowMealsWithOrderInAdvance_Description" xml:space="preserve">
    <value>Určuje zobrazení jídel označených v jídelníčku "Objednat dopředu"</value>
  </data>
  <data name="MobileOrderingBehaviorMenuConfig_ShowMealsFromAnotherCanteen_DisplayName" xml:space="preserve">
    <value>Zobrazit jídla nepatřící do dané výdejny</value>
  </data>
  <data name="MobileOrderingBehaviorMenuConfig_ShowMealsFromAnotherCanteen_Description" xml:space="preserve">
    <value>Týká se jídel, které mají omezením výdejny určeno, že v dané výdejně nejsou k dispozici.
Pokud je povoleno, zobrazují se jídla, které nejsou k dispozici.
Pokud je zakázáno, zobrazují se pouze jídla pro danou výdejnu.</value>
  </data>
  <data name="MobileOrderingBehaviorMenuConfig_ShowMealWithOverdrawnLimit_Description" xml:space="preserve">
    <value>Pokud je povoleno, jídla s vyčerpaným limitem porcí se zobrazují, ale nejdou objednat.
Pokud je zakázáno, jídla s vyčerpaným limitem se nezobrazují.</value>
  </data>
  <data name="MobileOrderingBehaviorMenuConfig_ShowMealWithOverdrawnLimit_DisplayName" xml:space="preserve">
    <value>Zobrazit jídla s vyčerpaným limitem porcí</value>
  </data>
  <data name="MobileOrderingBehaviorMenuConfig_ShowCommonMeals_Description" xml:space="preserve">
    <value>Pokud je povoleno, zobrazují se v menu společná jídla.</value>
  </data>
  <data name="MobileOrderingBehaviorMenuConfig_ShowCommonMeals_DisplayName" xml:space="preserve">
    <value>Zobrazit společná jídla</value>
  </data>
  <data name="TriggerConfig_Enabled_Description" xml:space="preserve">
    <value>Pokud je povoleno, bude probíhat spouštění podle časovače.</value>
  </data>
  <data name="TriggerConfig_Enabled_DisplayName" xml:space="preserve">
    <value>Povolit</value>
  </data>
  <data name="TriggerConfig_TriggerType_DisplayName" xml:space="preserve">
    <value>Typ plánovače</value>
  </data>
  <data name="TriggerConfig_TriggerType_Description" xml:space="preserve">
    <value>Typ plánovače</value>
  </data>
  <data name="TriggerConfig_TriggerSettings_DisplayName" xml:space="preserve">
    <value>Nastavení plánovače</value>
  </data>
  <data name="TriggerConfig_TriggerSettings_Description" xml:space="preserve">
    <value>Nastavení plánovače</value>
  </data>
  <data name="SimpleMinuteTriggerSettings_RepeatInterval_DisplayName" xml:space="preserve">
    <value>Opakovat každých [min]</value>
  </data>
  <data name="SimpleMinuteTriggerSettings_RepeatInterval_Description" xml:space="preserve">
    <value>Po kolika minutách opakovat spuštění</value>
  </data>
  <data name="GlobalServicesFbsServicesConfig_StockMovemetsConfig_Description" xml:space="preserve">
    <value>Konfigurace WCF služby pro odesílání skladových pohybů</value>
  </data>
  <data name="GlobalServicesFbsServicesConfig_StockMovemetsConfig_DisplayName" xml:space="preserve">
    <value>Služba pro skladové pohyby</value>
  </data>
  <data name="WcfCredentialsConfig_UserName_DisplayName" xml:space="preserve">
    <value>Přihlašovací jméno</value>
  </data>
  <data name="WcfCredentialsConfig_UserName_Description" xml:space="preserve">
    <value>Přihlašovací jméno</value>
  </data>
  <data name="WcfCredentialsConfig_Password_Description" xml:space="preserve">
    <value>Heslo</value>
  </data>
  <data name="WcfCredentialsConfig_Password_DisplayName" xml:space="preserve">
    <value>Heslo</value>
  </data>
  <data name="SimpleDayTriggerSettings_RepeatInterval_DisplayName" xml:space="preserve">
    <value>Opakovat každých [dní]</value>
  </data>
  <data name="SimpleDayTriggerSettings_RepeatInterval_Description" xml:space="preserve">
    <value>Po kolika dnech se má opakovat</value>
  </data>
  <data name="SimpleDayTriggerSettings_StartAt_Description" xml:space="preserve">
    <value>Ve kterém čase se má spustit</value>
  </data>
  <data name="SimpleDayTriggerSettings_StartAt_DisplayName" xml:space="preserve">
    <value>Spustit v čase</value>
  </data>
  <data name="CronMultipleTriggerSettings_CronExpressions_Description" xml:space="preserve">
    <value>Výrazy pro CRON časovač. Můžete ho sestavit na http://www.cronmaker.com/
Příklady: 
0 0 20 13 1/1 ? * Každý 13. den v měsíci ve 20:00
0 30 20 13 1/1 ? * Každý 13. den v měsíci ve 20:30
0 0 0/1 1/1 * ? * Každou hodinu v každém dni
0 0 20 ? 1/1 MON#1 * Každé první pondělí v každém měsíci ve 20:00
</value>
  </data>
  <data name="CronMultipleTriggerSettings_CronExpressions_DisplayName" xml:space="preserve">
    <value>CRON výrazy</value>
  </data>
  <data name="SimpleHourTriggerSettings_RepeatInterval_DisplayName" xml:space="preserve">
    <value>Opakovat každých [hod]</value>
  </data>
  <data name="SimpleHourTriggerSettings_RepeatInterval_Description" xml:space="preserve">
    <value>Po kolika hodinách se má opakovat</value>
  </data>
  <data name="CronTriggerSettings_CronExpression_DisplayName" xml:space="preserve">
    <value>CRON výraz</value>
  </data>
  <data name="CronTriggerSettings_CronExpression_Description" xml:space="preserve">
    <value>Výraz pro CRON časovač. Můžete ho sestavit na http://www.cronmaker.com/
Příklady: 
0 0 20 13 1/1 ? * Každý 13. den v měsíci ve 20:00
0 30 20 13 1/1 ? * Každý 13. den v měsíci ve 20:30
0 0 0/1 1/1 * ? * Každou hodinu v každém dni
0 0 20 ? 1/1 MON#1 * Každé první pondělí v každém měsíci ve 20:00</value>
  </data>
  <data name="GlobalServicesFbsCredentialsConfig_UserName_Description" xml:space="preserve">
    <value>Uživatelské jméno. Použije se pouze pro aplikace, které nevyžadují zadání jména a hesla od uživatele.</value>
  </data>
  <data name="GlobalServicesFbsCredentialsConfig_UserName_DisplayName" xml:space="preserve">
    <value>Uživatelské jméno</value>
  </data>
  <data name="GlobalServicesFbsCredentialsConfig_Password_DisplayName" xml:space="preserve">
    <value>Heslo</value>
  </data>
  <data name="GlobalServicesFbsCredentialsConfig_Password_Description" xml:space="preserve">
    <value>Heslo.  Použije se pouze pro aplikace, které nevyžadují zadání jména a hesla od uživatele.</value>
  </data>
  <data name="SchedulerPluginsStockMovementSenderConfig_MonthCount_DisplayName" xml:space="preserve">
    <value>Počet minulých měsíců k odeslání</value>
  </data>
  <data name="SchedulerPluginsStockMovementSenderConfig_MonthCount_Description" xml:space="preserve">
    <value>Kolik minulých měsíců se má odeslat. Jestliže se úloha spouští v měsíci 8. a je nastaveno 1, odešle se 7. měsíc.</value>
  </data>
  <data name="FailedJobConfig_RetryOnFailEnabled_DisplayName" xml:space="preserve">
    <value>Opakovat při chybě</value>
  </data>
  <data name="FailedJobConfig_RetryOnFailEnabled_Description" xml:space="preserve">
    <value>Pokud je povoleno, při chybě v úloze se bude úloha opakovat. 
Pokud je zakázáno, při chybě nebude úloha opakována.</value>
  </data>
  <data name="FailedJobConfig_RetryAfter_DisplayName" xml:space="preserve">
    <value>Opakovat po</value>
  </data>
  <data name="FailedJobConfig_RetryAfter_Description" xml:space="preserve">
    <value>Počet minut, po kterých se bude úloha opakovat.</value>
  </data>
  <data name="FailedJobConfig_MaxRetryCount_Description" xml:space="preserve">
    <value>Maximální počet opakování při chybě.</value>
  </data>
  <data name="FailedJobConfig_MaxRetryCount_DisplayName" xml:space="preserve">
    <value>Maximální počet opakování</value>
  </data>
  <data name="GlobalServicesSmtpConfig_EnableSsl_Description" xml:space="preserve">
    <value>Povolit při komunikaci SSL</value>
  </data>
  <data name="GlobalServicesSmtpConfig_EnableSsl_DisplayName" xml:space="preserve">
    <value>Povolit SSL</value>
  </data>
  <data name="MenuPresenterBehaviourColumnsConfig_ShowPicture_Description" xml:space="preserve">
    <value>Povolení/zakázaní zobrazení obrázku. Zobrazení obrázku způsobí rozšíření rádku s jídlem.</value>
  </data>
  <data name="MenuPresenterBehaviourColumnsConfig_ShowPicture_DisplayName" xml:space="preserve">
    <value>Zobrazit obrázek</value>
  </data>
  <data name="DayMenuWithPicturesSetting_RowCount_Description" xml:space="preserve">
    <value>Počet řádků se zobrazeným obrázkem jídla. Všechny řádky jsou stejně vysoké.</value>
  </data>
  <data name="DayMenuWithPicturesSetting_RowCount_DisplayName" xml:space="preserve">
    <value>Počet řádků</value>
  </data>
  <data name="DayMenuWithPicturesSetting_ColumnCount_DisplayName" xml:space="preserve">
    <value>Počet sloupců</value>
  </data>
  <data name="DayMenuWithPicturesSetting_ColumnCount_Description" xml:space="preserve">
    <value>Počet sloupců se zobrazeným obrázkem jídla. Všechny sloupce jsou stejně široké.</value>
  </data>
  <data name="GlobalBehaviourApplicationCountryConfig_ApplicationCountry_Description" xml:space="preserve">
    <value>Stát, s jehož specifiky pracuje aplikace.</value>
  </data>
  <data name="GlobalBehaviourApplicationCountryConfig_ApplicationCountry_DisplayName" xml:space="preserve">
    <value>Stát</value>
  </data>
  <data name="MenuPresenterAppearanceMenuConfig_DataColumnWidth_Description" xml:space="preserve">
    <value>Šířka sloupce s cenou, počtem kusů atd.</value>
  </data>
  <data name="MenuPresenterAppearanceMenuConfig_DataColumnWidth_DisplayName" xml:space="preserve">
    <value>Šířka datového sloupce</value>
  </data>
  <data name="GlobalHwPaymentTermConfig_PaymentTermType_DisplayName" xml:space="preserve">
    <value>Typ terminálu</value>
  </data>
  <data name="GlobalHwPaymentTermConfig_PaymentTermType_Description" xml:space="preserve">
    <value>Typ připojeného terminálu</value>
  </data>
  <data name="GlobalHwPaymentTermConfig_Settings_Description" xml:space="preserve">
    <value>Nastavení konfigurace terminálu</value>
  </data>
  <data name="GlobalHwPaymentTermConfig_Settings_DisplayName" xml:space="preserve">
    <value>Nastavení terminálu</value>
  </data>
  <data name="SoNetPaymentTermSettings_WriteLog_DisplayName" xml:space="preserve">
    <value>Zapisovat do logu</value>
  </data>
  <data name="SoNetPaymentTermSettings_WriteLog_Description" xml:space="preserve">
    <value>Zapisovat do logu komunikaci s terminálem.</value>
  </data>
  <data name="SoNetPaymentTermSettings_LogLevel_DisplayName" xml:space="preserve">
    <value>Úroveň logování</value>
  </data>
  <data name="SoNetPaymentTermSettings_LogLevel_Description" xml:space="preserve">
    <value>Úroveň logování</value>
  </data>
  <data name="SoNetPaymentTermSettings_ConnType_DisplayName" xml:space="preserve">
    <value>Typ připojení</value>
  </data>
  <data name="SoNetPaymentTermSettings_ConnType_Description" xml:space="preserve">
    <value>Typ připojení terminálu</value>
  </data>
  <data name="SoNetPaymentTermSettings_Settings_Description" xml:space="preserve">
    <value>Nastavení parametrů spojení</value>
  </data>
  <data name="SoNetPaymentTermSettings_Settings_DisplayName" xml:space="preserve">
    <value>Nastavení spojení</value>
  </data>
  <data name="SoNetTcpIpSettings_Address_Description" xml:space="preserve">
    <value>IP adresa nebo název, odpovídající IP adrese.
Jak zjistit IP adresu terminálu:  Na terminálu zvolit Menu, Funkce, číslo funkce 66. Zobrazí se IP.</value>
  </data>
  <data name="SoNetTcpIpSettings_Address_DisplayName" xml:space="preserve">
    <value>IP adresa</value>
  </data>
  <data name="SoNetTcpIpSettings_Port_Description" xml:space="preserve">
    <value>Číslo portu, implicitně 1818</value>
  </data>
  <data name="SoNetTcpIpSettings_Port_DisplayName" xml:space="preserve">
    <value>Port</value>
  </data>
  <data name="CashDeskSaleConfig_EnableMultiPayment_Description" xml:space="preserve">
    <value>Pokud je povoleno, zobrazí se panel Úhrada paragonu, kde je možno použít mnoho kombinací platidel.
Pokud je zakázáno, zobrazí se klasický panel Úhrada paragonu.</value>
  </data>
  <data name="CashDeskSaleConfig_EnableMultiPayment_DisplayName" xml:space="preserve">
    <value>Povolit kombinovanou úhradu</value>
  </data>
  <data name="CashDeskManagerConfig_EnableGoodsEdit_Description" xml:space="preserve">
    <value>Pokud je povoleno, lze v manageru editovat a vytvářet zboží, které není z externího skladu.
Pokud je zakázáno, je všechno zboží pouze pro čtení.</value>
  </data>
  <data name="CashDeskManagerConfig_EnableGoodsEdit_DisplayName" xml:space="preserve">
    <value>Povolit editaci zboží</value>
  </data>
  <data name="GlobalRulesAccountingMidnightConfig_MidnightOffset_DisplayName" xml:space="preserve">
    <value>Posun účetní půlnoci [hod]</value>
  </data>
  <data name="GlobalRulesAccountingMidnightConfig_MidnightOffset_Description" xml:space="preserve">
    <value>Posun účetní půlnoci. Zadává se v hodinách prodeje přes půlnoc. 
Pokud chcete posunout účetní půlnoc na 3 hodiny ráno, zadejte 3. 
Pokud chcete posunout účetní půlnoc na 2:30 hodiny ráno, zadejte 2.5.</value>
  </data>
  <data name="OfficePriceMakingBehaviourSaleSummaryConfig_SaleId_DisplayName" xml:space="preserve">
    <value>Id prodejní ceny</value>
  </data>
  <data name="OfficePriceMakingBehaviourSaleSummaryConfig_SaleId_Description" xml:space="preserve">
    <value>Id cenové složky, pro kterou se bude zobrazovat prodejní cena.</value>
  </data>
  <data name="OfficePriceMakingBehaviourSaleSummaryConfig_ProfitId_DisplayName" xml:space="preserve">
    <value>Id výnosové ceny</value>
  </data>
  <data name="OfficePriceMakingBehaviourSaleSummaryConfig_ProfitId_Description" xml:space="preserve">
    <value>Id cenové složky, pro kterou se bude zobrazovat výnosová cena.</value>
  </data>
  <data name="OfficePriceMakingBehaviourSaleSummaryConfig_CostId_Description" xml:space="preserve">
    <value>Id cenové složky, pro kterou se bude zobrazovat nákladová cena.</value>
  </data>
  <data name="OfficePriceMakingBehaviourSaleSummaryConfig_CostId_DisplayName" xml:space="preserve">
    <value>Id nákladové ceny</value>
  </data>
  <data name="GlobalServicesSmtpConfig_SenderMail_Description" xml:space="preserve">
    <value>E-Mailová adresa, pod kterou se budou odesílat maily. Má přednost před Doména odesílatele.</value>
  </data>
  <data name="GlobalServicesSmtpConfig_SenderMail_DisplayName" xml:space="preserve">
    <value>E-Mail odesílatele</value>
  </data>
  <data name="Iso20022EBankingSettings_OwnerId_Description" xml:space="preserve">
    <value>Id klienta, unikátní číslo, přiděluje banka.</value>
  </data>
  <data name="Iso20022EBankingSettings_OwnerId_DisplayName" xml:space="preserve">
    <value>Id klienta</value>
  </data>
  <data name="Iso20022EBankingSettings_OwnerName_Description" xml:space="preserve">
    <value>Název klienta, který se objeví v exportu.</value>
  </data>
  <data name="Iso20022EBankingSettings_OwnerName_DisplayName" xml:space="preserve">
    <value>Název klienta</value>
  </data>
  <data name="Iso20022EBankingSettings_OwnerIban_DisplayName" xml:space="preserve">
    <value>IBAN klienta</value>
  </data>
  <data name="Iso20022EBankingSettings_OwnerIban_Description" xml:space="preserve">
    <value>IBAN klienta, použivá se při exportu</value>
  </data>
  <data name="Iso20022EBankingSettings_OwnerBic_Description" xml:space="preserve">
    <value>BIC kód banky klienta</value>
  </data>
  <data name="Iso20022EBankingSettings_OwnerBic_DisplayName" xml:space="preserve">
    <value>BIC kód banky</value>
  </data>
  <data name="GlobalRulesPasswordConfig_PasswordType_DisplayName" xml:space="preserve">
    <value>Složitost hesla</value>
  </data>
  <data name="GlobalRulesPasswordConfig_PasswordType_Description" xml:space="preserve">
    <value>Definuje složitost hesla (povolené znaky, minimální délka)</value>
  </data>
  <data name="OfficePriceMakingBehaviourSaleSummaryConfig_MarginType_DisplayName" xml:space="preserve">
    <value>Typ marže</value>
  </data>
  <data name="OfficePriceMakingBehaviourSaleSummaryConfig_MarginType_Description" xml:space="preserve">
    <value>Ovlivňuje způsob výpočtu marže</value>
  </data>
  <data name="CashDeskSaleConfig_EnableSellGoodsWithZeroPrice_Description" xml:space="preserve">
    <value>Pokud je povoleno, lze prodávat zboží, které má nastavenou nulovou cenu.
Pokud je zakázáno, zboží za nulovou cenu prodávat nelze.</value>
  </data>
  <data name="CashDeskSaleConfig_EnableSellGoodsWithZeroPrice_DisplayName" xml:space="preserve">
    <value>Povolit prodej zboží s nulovou cenou</value>
  </data>
  <data name="GlobalRulesCashDeskConfig_DepositWithdrawal_DisplayName" xml:space="preserve">
    <value>Typ vkladu/výběru z pokladní zásuvky</value>
  </data>
  <data name="GlobalRulesCashDeskConfig_DepositWithdrawal_Description" xml:space="preserve">
    <value>Nastavuje typ vkladu/výběru z pokladní zásuvky.</value>
  </data>
  <data name="GlobalRulesCashDeskConfig_SalesDeliveryEnabled_Description" xml:space="preserve">
    <value>Pokud je povoleno, je v aplikaci dostupné tlačítko pro odvod tržby.
Pokud je zakázáno, odvod tržby není povolen.</value>
  </data>
  <data name="GlobalRulesCashDeskConfig_SalesDeliveryEnabled_DisplayName" xml:space="preserve">
    <value>Povolit odvod tržby</value>
  </data>
  <data name="GlobalBehaviourSalesSlipPrintConfig_PrintLogo_Description" xml:space="preserve">
    <value>Pokud je povoleno, bude se tisknout logo na začátku paragonu.
Logo je napřed potřeba nahrát do paměti tiskárny příšlušným nástrojem pod číslem 1.</value>
  </data>
  <data name="GlobalBehaviourSalesSlipPrintConfig_PrintLogo_DisplayName" xml:space="preserve">
    <value>Tisknout logo na začátku paragonu</value>
  </data>
  <data name="StockModuleSettingsAnete_IssueSlipGenType_Description" xml:space="preserve">
    <value>Určuje, jakým způsobem se budou generovat výdejky pro zboží a jídla.</value>
  </data>
  <data name="StockModuleSettingsAnete_IssueSlipGenType_DisplayName" xml:space="preserve">
    <value>Generování výdejky zboží a jídel</value>
  </data>
  <data name="PosPrinterConfigBase_FriendlyName_Description" xml:space="preserve">
    <value>Zjednodušený název tiskárny pro uživatele. Např. místo "Bixolon SRP 300 on USB" zapište Kasa. Pokud není vyplněné, použije se Název tiskárny.</value>
  </data>
  <data name="PosPrinterConfigBase_FriendlyName_DisplayName" xml:space="preserve">
    <value>Název pro uživatele</value>
  </data>
  <data name="PosPrinterSettings_SpoolerName_Description" xml:space="preserve">
    <value>Port nebo název tiskárny, nakonfiguravané ve Windows. Musí odpovídat názvu tiskárny Windows.</value>
  </data>
  <data name="PosPrinterSettings_SpoolerName_DisplayName" xml:space="preserve">
    <value>Název tiskárny</value>
  </data>
  <data name="PosPrinterSettings_FriendlyName_DisplayName" xml:space="preserve">
    <value>Název pro uživatele</value>
  </data>
  <data name="PosPrinterSettings_FriendlyName_Description" xml:space="preserve">
    <value>Zjednodušený název tiskárny pro uživatele. Např. místo "Bixolon SRP 300 on USB" zapište Kasa. Pokud není vyplněné, použije se Název tiskárny.</value>
  </data>
  <data name="PosPrinterSettings_PrinterType_DisplayName" xml:space="preserve">
    <value>Typ tiskárny</value>
  </data>
  <data name="PosPrinterSettings_PrinterType_Description" xml:space="preserve">
    <value>Typ tiskárny z Anete.DevicesConfig.xml. Určuje sadu příkazů, posílaných do tiskárny.</value>
  </data>
  <data name="GlobalHwKitchenPrinterConfig_AddPrinters_Description" xml:space="preserve">
    <value>Seznam dalších tiskáren pro tisk objednávek.</value>
  </data>
  <data name="GlobalHwKitchenPrinterConfig_AddPrinters_DisplayName" xml:space="preserve">
    <value>Další tiskárny</value>
  </data>
  <data name="OfficeWorkplacesBehaviourMealTypeConfig_CreateMenuRowAllergens_DisplayName" xml:space="preserve">
    <value>Alergeny v názvu jídla</value>
  </data>
  <data name="OfficeWorkplacesBehaviourMealTypeConfig_CreateMenuRowAllergens_Description" xml:space="preserve">
    <value>Pokud je povoleno, jsou u názvu jídla (u každého typu jídla zvlášť) zobrazeny kódy alergenů.</value>
  </data>
  <data name="MenuPresenterBehaviourColumnsConfig_Amount_Description" xml:space="preserve">
    <value>Sloupec s množstvím definovaným v jídelníčku. Obsahuje grámáž jídla.</value>
  </data>
  <data name="MenuPresenterBehaviourColumnsConfig_Amount_DisplayName" xml:space="preserve">
    <value>Množství</value>
  </data>
  <data name="CashDeskBehaviourUserInterfaceConfig_ManagerInterfaceType_Description" xml:space="preserve">
    <value>Určuje, zda manager bude optimalizován pro klávesnici a myš nebo pro touch.</value>
  </data>
  <data name="CashDeskBehaviourUserInterfaceConfig_ManagerInterfaceType_DisplayName" xml:space="preserve">
    <value>Typ rozhraní v manageru</value>
  </data>
  <data name="CashDeskBehaviourUserInterfaceConfig_ManagerFullScreenType_DisplayName" xml:space="preserve">
    <value>Celá obrazovka</value>
  </data>
  <data name="CashDeskBehaviourUserInterfaceConfig_ManagerFullScreenType_Description" xml:space="preserve">
    <value>Určuje, zda okno managera bude zobrazeno přes celou obrazovku včetně taskbaru.</value>
  </data>
  <data name="CashDeskManagerConfig_EnableCanteenLimitsEdit_DisplayName" xml:space="preserve">
    <value>Povolit editaci limitů pro výdejnu</value>
  </data>
  <data name="CashDeskManagerConfig_EnableCanteenLimitsEdit_Description" xml:space="preserve">
    <value>Pokud je povoleno, lze editovat limity jídel pro aktuální výdejnu.</value>
  </data>
  <data name="GlobalBehaviourKitchenOrderPrintConfig_PrintForGoods_Description" xml:space="preserve">
    <value>Pokud je povoleno, nabízí se pro tisk objednávky i sortiment. Sortiment se tiskne na tiskárnu uvedenou výše. Platí pouze pro režim tisku na vyžádání.</value>
  </data>
  <data name="GlobalBehaviourKitchenOrderPrintConfig_PrintForGoods_DisplayName" xml:space="preserve">
    <value>Tisknout objednávku i pro sortiment</value>
  </data>
  <data name="GlobalBehaviourKitchenOrderPrintConfig_GoodsKitchenPrinterName_Description" xml:space="preserve">
    <value>Název tiskárny pro tisk sortimentu. Zadejte název tiskárny, ne typ tiskárny. Pokud není zadáno, tiskne se na primární tiskárnu.</value>
  </data>
  <data name="GlobalBehaviourKitchenOrderPrintConfig_GoodsKitchenPrinterName_DisplayName" xml:space="preserve">
    <value>Název tiskárny v kuchyni pro tisk sortimentu</value>
  </data>
  <data name="RequireAgreementWithTermsItem_TermsValidFrom_Description" xml:space="preserve">
    <value />
  </data>
  <data name="RequireAgreementWithTermsItem_TermsValidFrom_DisplayName" xml:space="preserve">
    <value>Podmínky platné od data</value>
  </data>
  <data name="RequireAgreementWithTermsItem_TermsText_DisplayName" xml:space="preserve">
    <value>Text podmínek užívání</value>
  </data>
  <data name="RequireAgreementWithTermsItem_TermsText_Description" xml:space="preserve">
    <value />
  </data>
  <data name="ForceChangePasswordOnDateSettings_ChangeOnDate_Description" xml:space="preserve">
    <value />
  </data>
  <data name="ForceChangePasswordOnDateSettings_ChangeOnDate_DisplayName" xml:space="preserve">
    <value>Datum</value>
  </data>
  <data name="GlobalBehaviourClientForceChangePasswordConfig_Enabled_Description" xml:space="preserve">
    <value />
  </data>
  <data name="GlobalBehaviourClientForceChangePasswordConfig_Enabled_DisplayName" xml:space="preserve">
    <value>Zapnuto (vynutit změnu)</value>
  </data>
  <data name="GlobalBehaviourClientForceChangePasswordConfig_Settings_DisplayName" xml:space="preserve">
    <value>Nastavení</value>
  </data>
  <data name="GlobalBehaviourClientForceChangePasswordConfig_Settings_Description" xml:space="preserve">
    <value />
  </data>
  <data name="GlobalBehaviourClientRequireAgreementWithTermsConfig_Enabled_Description" xml:space="preserve">
    <value />
  </data>
  <data name="GlobalBehaviourClientRequireAgreementWithTermsConfig_Enabled_DisplayName" xml:space="preserve">
    <value>Zapnuto (vyžadovat odsouhlasení)</value>
  </data>
  <data name="GlobalBehaviourClientRequireAgreementWithTermsConfig_Terms_DisplayName" xml:space="preserve">
    <value>Podmínky k odsouhlasení</value>
  </data>
  <data name="GlobalBehaviourClientRequireAgreementWithTermsConfig_Terms_Description" xml:space="preserve">
    <value />
  </data>
  <data name="CashDeskSaleConfig_EnableKartouCash_DisplayName" xml:space="preserve">
    <value>Povolit prodej Kartou-Cash</value>
  </data>
  <data name="CashDeskSaleConfig_EnableKartouCash_Description" xml:space="preserve">
    <value>Povolit nebo zakázat prodej Kartou-Cash</value>
  </data>
  <data name="OfficeWorkplacesReportsWeekMenuConfig_MealKindPosition_DisplayName" xml:space="preserve">
    <value>Pozice druhu jídla</value>
  </data>
  <data name="OfficeWorkplacesReportsWeekMenuConfig_MealKindPosition_Description" xml:space="preserve">
    <value>Definuje pozici, kde se v tiskové sestavě zobrazí druh jídla.</value>
  </data>
  <data name="SharedMenuTemplateConfig_WorkplaceStyle_Description" xml:space="preserve">
    <value>Název provozovny (vývařovny)</value>
  </data>
  <data name="SharedMenuTemplateConfig_WorkplaceStyle_DisplayName" xml:space="preserve">
    <value>Provozovna</value>
  </data>
  <data name="SharedMenuTemplateConfig_DateStyle_Description" xml:space="preserve">
    <value>Nadpis s datumem</value>
  </data>
  <data name="SharedMenuTemplateConfig_DateStyle_DisplayName" xml:space="preserve">
    <value>Datum</value>
  </data>
  <data name="SharedMenuTemplateConfig_MenuHeaderStyle_DisplayName" xml:space="preserve">
    <value>Hlavička menu</value>
  </data>
  <data name="SharedMenuTemplateConfig_MenuHeaderStyle_Description" xml:space="preserve">
    <value>Nadpisy sloupců </value>
  </data>
  <data name="SharedMenuTemplateConfig_MealKindStyle_DisplayName" xml:space="preserve">
    <value>Druh jídla</value>
  </data>
  <data name="SharedMenuTemplateConfig_MealKindStyle_Description" xml:space="preserve">
    <value>Nadpis s druhem jídla</value>
  </data>
  <data name="SharedMenuTemplateConfig_MealColumnStyle_Description" xml:space="preserve">
    <value>Bežný sloupec s alternativou, množstvím, názvem jídla...</value>
  </data>
  <data name="SharedMenuTemplateConfig_MealColumnStyle_DisplayName" xml:space="preserve">
    <value>Sloupec</value>
  </data>
  <data name="SharedMenuTemplateConfig_MealNoteColumnStyle_Description" xml:space="preserve">
    <value>Poznámka a seznam alergenů, pokud jsou odděleny na zvláštním řádku.</value>
  </data>
  <data name="SharedMenuTemplateConfig_MealNoteColumnStyle_DisplayName" xml:space="preserve">
    <value>Poznámka k jídlu</value>
  </data>
  <data name="SharedMenuTemplateConfig_MenuNoteStyle_DisplayName" xml:space="preserve">
    <value>Poznámka v patičce</value>
  </data>
  <data name="SharedMenuTemplateConfig_MenuNoteStyle_Description" xml:space="preserve">
    <value>Text, který mají někteří zákazníci zobrazen v patičce.</value>
  </data>
  <data name="ControlStyleConfig_Font_Description" xml:space="preserve">
    <value>Nastavení písma</value>
  </data>
  <data name="ControlStyleConfig_Font_DisplayName" xml:space="preserve">
    <value>Písmo</value>
  </data>
  <data name="SharedMenuTemplateConfig_TitleStyle_Description" xml:space="preserve">
    <value>Nadpis celé tiskové sestavy, obvykle text "Jídelníček"</value>
  </data>
  <data name="SharedMenuTemplateConfig_TitleStyle_DisplayName" xml:space="preserve">
    <value>Nadpis</value>
  </data>
  <data name="OfficeWorkplacesReportsTemplatesWeekMenuConfig_DateGroupStyle_Description" xml:space="preserve">
    <value>Nadpis pro jídla vztažené k danému datu</value>
  </data>
  <data name="OfficeWorkplacesReportsTemplatesWeekMenuConfig_DateGroupStyle_DisplayName" xml:space="preserve">
    <value>Datum</value>
  </data>
  <data name="CashDeskBehaviourUserInterfaceConfig_TouchDeviceType_Description" xml:space="preserve">
    <value>Podle daného typu zařízení se konfiguruje nastavení Touch a virtuální klávesnice. 
Pozor: Změna se projeví až po restartu Kasy a přihlášení k databázi.</value>
  </data>
  <data name="CashDeskBehaviourUserInterfaceConfig_TouchDeviceType_DisplayName" xml:space="preserve">
    <value>Typ zařízení</value>
  </data>
  <data name="TouchDeviceUiConfig_TouchScaleFactor_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="TouchDeviceUiConfig_TouchScaleFactor_Description" xml:space="preserve">
    <value />
  </data>
  <data name="TouchDeviceUiConfig_EnableVirtualKeyboard_Description" xml:space="preserve">
    <value />
  </data>
  <data name="TouchDeviceUiConfig_EnableVirtualKeyboard_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="GlobalBehaviourClientAssignmentConfig_OneToOneAssignment_DisplayName" xml:space="preserve">
    <value>1:1</value>
  </data>
  <data name="GlobalBehaviourClientAssignmentConfig_OneToOneAssignment_Description" xml:space="preserve">
    <value>Pokud zákazník má pro každou organizaci právě jednu účtárnu a středisko, je vhodné parametr povolit. Na základě něj se v Kanceláři přizpůsobí UI.</value>
  </data>
  <data name="LdapSearchRequestConfig_UserNameAttribute_DisplayName" xml:space="preserve">
    <value>Název atributu s přihlašovacím jménem</value>
  </data>
  <data name="LdapSearchRequestConfig_UserNameAttribute_Description" xml:space="preserve">
    <value>Pokud není vyplněno, předpokládá se, že přihlašovací jméno do Kreditu je stejné jako název uživatele ve stromu. 
Pokud je zadán název atributu, předpokládá se, že název uživatele Kredit je uložen v atributu s tímto názvem.
Pozor: Používá se pouze ve speciálních případech po domluvě s programátorem. V naprosté většině případů nechat nevyplněné. </value>
  </data>
  <data name="SkladyDbConnectionConfig_UseLinkedServerByGetSkladyLink_DisplayName" xml:space="preserve">
    <value>Linkovaný server</value>
  </data>
  <data name="SkladyDbConnectionConfig_UseLinkedServerByGetSkladyLink_Description" xml:space="preserve">
    <value>Pokud je povoleno, bude se k db Sklady přistupovat přes db Kredit pomocí linkovaného serveru, jehož název vrací funkce GetSkladyLink. Používá se u centralizovaných systémů.</value>
  </data>
  <data name="CashDeskManagerConfig_DefaultIdDph_DisplayName" xml:space="preserve">
    <value>Id DPH pro nově zakládané zboží</value>
  </data>
  <data name="GlobalRulesSpendingAndSubsidyConfig_VisibleReport_Description" xml:space="preserve">
    <value>Parametr, zda bude sestava zpřístupněna.</value>
  </data>
  <data name="GlobalRulesSpendingAndSubsidyConfig_VisibleReport_DisplayName" xml:space="preserve">
    <value>Zpřístupnění sestavy</value>
  </data>
  <data name="GlobalRulesSpendingAndSubsidyConfig_FullPriceId_DisplayName" xml:space="preserve">
    <value>Plná cena</value>
  </data>
  <data name="GlobalRulesSpendingAndSubsidyConfig_FullPriceId_Description" xml:space="preserve">
    <value>Id ceníkové položky pro sloupec - Plná cena</value>
  </data>
  <data name="GlobalRulesSpendingAndSubsidyConfig_EmployerSubsidyId_Description" xml:space="preserve">
    <value>Id ceníkové položky pro sloupec - Příspěvek zaměstnavatele</value>
  </data>
  <data name="GlobalRulesSpendingAndSubsidyConfig_EmployerSubsidyId_DisplayName" xml:space="preserve">
    <value>Příspěvek zaměstnavatele</value>
  </data>
  <data name="GlobalRulesSpendingAndSubsidyConfig_FkspSubsidyId_Description" xml:space="preserve">
    <value>Id ceníkové položky pro sloupec - FKSP příspěvek</value>
  </data>
  <data name="GlobalRulesSpendingAndSubsidyConfig_FkspSubsidyId_DisplayName" xml:space="preserve">
    <value>FKSP příspěvek</value>
  </data>
  <data name="GlobalRulesSpendingAndSubsidyConfig_FullPriceWithoutVatId_Description" xml:space="preserve">
    <value>Id ceníkové položky pro sloupec - Plná cena bez DPH</value>
  </data>
  <data name="GlobalRulesSpendingAndSubsidyConfig_FullPriceWithoutVatId_DisplayName" xml:space="preserve">
    <value>Plná cena bez DPH</value>
  </data>
  <data name="GlobalRulesSpendingAndSubsidyConfig_VatId_Description" xml:space="preserve">
    <value>Id ceníkové položky pro sloupec - DPH</value>
  </data>
  <data name="GlobalRulesSpendingAndSubsidyConfig_VatId_DisplayName" xml:space="preserve">
    <value>DPH</value>
  </data>
  <data name="CashDeskManagerConfig_DefaultIdDph_Description" xml:space="preserve">
    <value>Implicitní DPH pro nově zakládané zboží. Zadejte Id z tabulky dba.SkupinyDph.</value>
  </data>
  <data name="GlobalServicesFbsServicesConfig_ArticlesConfig_DisplayName" xml:space="preserve">
    <value>Služba pro jídelníčky</value>
  </data>
  <data name="GlobalServicesFbsServicesConfig_ArticlesConfig_Description" xml:space="preserve">
    <value>Konfigurace WCF služby pro jídelníčky</value>
  </data>
  <data name="GlobalBehaviourFbsMenuConfig_GoodsNameFrom_Description" xml:space="preserve">
    <value>Odkud se získává název zboží (suroviny pro daný řádek jídelníčku)</value>
  </data>
  <data name="GlobalBehaviourFbsMenuConfig_GoodsNameFrom_DisplayName" xml:space="preserve">
    <value>Název zboží (suroviny)</value>
  </data>
  <data name="CashDeskSaleConfig_EnablePaymentOfReceivables_DisplayName" xml:space="preserve">
    <value>Povolit úhradu pohledávky</value>
  </data>
  <data name="CashDeskSaleConfig_EnablePaymentOfReceivables_Description" xml:space="preserve">
    <value>Pokud je povoleno, je povolen prodej položky Úhrada pohledávky</value>
  </data>
  <data name="OfficeWorkplacesBehaviourMenuConfig_PrintOnByCanteen_Description" xml:space="preserve">
    <value>Povolit nastavení tisku řádku jídelníčku pro jednotlivé výdejny? Pokud je povoleno, je zobrazeno v jídelníčku tlačítko pro nastavení tisku na výdejny a sloupec, který říka, zda toto nastavení pro daný řádek jídelníčku existuje.</value>
  </data>
  <data name="OfficeWorkplacesBehaviourMenuConfig_PrintOnByCanteen_DisplayName" xml:space="preserve">
    <value>Tisk na výdejny</value>
  </data>
  <data name="SchedulerPluginsFbsMenuSyncConfig_TriggerConfig_DisplayName" xml:space="preserve">
    <value>Nastavení časovače</value>
  </data>
  <data name="SchedulerPluginsFbsMenuSyncConfig_TriggerConfig_Description" xml:space="preserve">
    <value>Nastavení parametrů časovače</value>
  </data>
  <data name="SchedulerPluginsFbsMenuSyncConfig_FailedJobConfig_Description" xml:space="preserve">
    <value>Nastavení toho, zda se při chybě v úloze má opakovat spuštění</value>
  </data>
  <data name="SchedulerPluginsFbsMenuSyncConfig_FailedJobConfig_DisplayName" xml:space="preserve">
    <value>Opakování při chybě</value>
  </data>
  <data name="AneteUpdatesDbConnectionConfig_UseLinkedServerForReplication_DisplayName" xml:space="preserve">
    <value>Na replice použít linkovaný server</value>
  </data>
  <data name="AneteUpdatesDbConnectionConfig_UseLinkedServerForReplication_Description" xml:space="preserve">
    <value>Použije se pro počítač připojený k replice linkovaný server? Tzn. že připojení k databázi AneteUpdates proběhne přes databázi Kredit a pro každý vygenerovaný dotaz se sestaví prefix v podobě [server].[název databáze AneteUpdates]. Pokud je tato volba povolena, využívá se z celé konfigurace připojení k AneteUpdates pouze název databáze.</value>
  </data>
  <data name="DbConnectionConfig_AuthenticationType_Description" xml:space="preserve">
    <value>Nastavení typu autentizace.</value>
  </data>
  <data name="CashDeskSaleConfig_EanMealPrefix_DisplayName" xml:space="preserve">
    <value>Prefixy při prodeji jídel přes EAN</value>
  </data>
  <data name="CashDeskSaleConfig_EanMealPrefix_Description" xml:space="preserve">
    <value>Startovací prefixy čárových kódů, které znamenají prodej jídla přes čárový kód.
Lze použít prefixy 20, 21, 22, 23, 24, 25, 26 a 28 - dle definice jde o kódy, které používají prodejní organizace ve svém vlastním systému. Nesmí se krýt s ostatními prefixy pro prodej sortimentu cenou a váhou.
Standardní nastavení je 25.</value>
  </data>
  <data name="GlobalBehaviourTechSupportMessagingConfig_ApplicationError_DisplayName" xml:space="preserve">
    <value>Zasílání zpráv o chybách v aplikaci</value>
  </data>
  <data name="GlobalBehaviourTechSupportMessagingConfig_ApplicationError_Description" xml:space="preserve">
    <value>Nastavuje odesílání zpráv při chybách v aplikaci.</value>
  </data>
  <data name="LanQualitySettingsBase_BackToOnlineTime_DisplayName" xml:space="preserve">
    <value>Čas, po který musí být síť v pořádku pro přechod do online [min]</value>
  </data>
  <data name="LanQualitySettingsBase_BackToOnlineTime_Description" xml:space="preserve">
    <value>Minimální čas v minutách, který stráví Kasa v offline po výpadku sítě.
Když je kasa v OFFLINE režimu, testovat znovuobnovení komunikace po dobu nastavenou tímto parametrem. Po toto zpoždění nepřepínat do ONLINE, ale nechat v OFFLINE, aby nenastalo oscilování, které v podstatě znemožní provoz. Pokud po tuto dobu nastane výpadek znovu, posunout od znovu obnovení opět na čas podle parametru - tzn. že po dobu tohoto parametru musí být síť bez výpadku.</value>
  </data>
  <data name="LanQualitySettingsBase_MaxOfflineTime_DisplayName" xml:space="preserve">
    <value>Maximální čas v offline [min]</value>
  </data>
  <data name="LanQualitySettingsBase_MaxOfflineTime_Description" xml:space="preserve">
    <value>Aby ale nenastal případ, že se do ONLINE nepřipojí nikdy, slouží tento parametr, který stanoví maximální dobu bez přepnutí.  Po této době při prvním náběhu sítě přepne do ONLINE.</value>
  </data>
  <data name="CashDeskOfflineConfig_LanQuality_DisplayName" xml:space="preserve">
    <value>Kvalita sítě</value>
  </data>
  <data name="CashDeskOfflineConfig_LanQuality_Description" xml:space="preserve">
    <value>Nastavení kvality sítě - podle kvality sítě se nastaví příslušné timeouty offline režimu.</value>
  </data>
  <data name="CashDeskOfflineConfig_LanQualitySettings_DisplayName" xml:space="preserve">
    <value>Nastavení kvality sítě</value>
  </data>
  <data name="CashDeskOfflineConfig_LanQualitySettings_Description" xml:space="preserve">
    <value>Nastavení parametrů offline podle kvality sítě.</value>
  </data>
  <data name="CashDeskSaleConfig_CanStornoDaysInClosedAccountBalance_DisplayName" xml:space="preserve">
    <value>Počet dní v uzavřeném účetním období, kdy lze stornovat sortiment</value>
  </data>
  <data name="CashDeskSaleConfig_CanStornoDaysInClosedAccountBalance_Description" xml:space="preserve">
    <value>Umožňuje omezit počet dní, po které lze stornovat sortiment z paragonu z uzavřeného účetního období v Kasa manageru.
Pokud je zadáno 10 a poslední uzávěrka je k 31.12, pak bude možno stornovat paragony z 21.12 a dále.
Jídla nejdou stornovat z uzavřeného účetního období nikdy.</value>
  </data>
  <data name="StockModuleSettingsAnete_ImportGoodsAtStartup_DisplayName" xml:space="preserve">
    <value>Aktualizovat zboží ze skladů při spuštění</value>
  </data>
  <data name="StockModuleSettingsAnete_ImportGoodsAtStartup_Description" xml:space="preserve">
    <value>Pokud je povoleno, bude se při spuštění Kasy automaticky aktualizovat zboží ze skladů. 
Pokud je zakázáno, zboží ze skladů se nebude automaticky aktualizovat.</value>
  </data>
  <data name="SchedulerPluginsHelpDeskSyncConfig_ComputersTriggerConfig_DisplayName" xml:space="preserve">
    <value>Synchronizace počítačů: Nastavení časovače</value>
  </data>
  <data name="SchedulerPluginsHelpDeskSyncConfig_ComputersTriggerConfig_Description" xml:space="preserve">
    <value>Nastavení parametrů časovače</value>
  </data>
  <data name="SchedulerPluginsHelpDeskSyncConfig_ComputersFailedJobConfig_DisplayName" xml:space="preserve">
    <value>Synchronizace počítačů: Opakování při chybě</value>
  </data>
  <data name="SchedulerPluginsHelpDeskSyncConfig_ComputersFailedJobConfig_Description" xml:space="preserve">
    <value>Nastavení toho, zda se při chybě v úloze má opakovat spuštění</value>
  </data>
  <data name="GlobalServicesHelpDeskCredentialsConfig_UserName_DisplayName" xml:space="preserve">
    <value>Uživatelské jméno</value>
  </data>
  <data name="GlobalServicesHelpDeskCredentialsConfig_UserName_Description" xml:space="preserve">
    <value>Uživatelské jméno pro přihlášení ke službám HelpDesk</value>
  </data>
  <data name="GlobalServicesHelpDeskCredentialsConfig_Password_DisplayName" xml:space="preserve">
    <value>Heslo</value>
  </data>
  <data name="GlobalServicesHelpDeskCredentialsConfig_Password_Description" xml:space="preserve">
    <value>Heslo pro přihlášení k HelpDesk</value>
  </data>
  <data name="GlobalRulesNutritionalValuesConfig_ClientGroups_DisplayName" xml:space="preserve">
    <value>Skupiny klientů</value>
  </data>
  <data name="GlobalRulesNutritionalValuesConfig_ClientGroups_Description" xml:space="preserve">
    <value>Ke každé skupině se nastaví, jaké nutriční hodnoty se pro ní budou zobrazovat.</value>
  </data>
  <data name="NutritionalValuesForClientGroup_NutritionalValues_DisplayName" xml:space="preserve">
    <value>Nutriční hodnoty</value>
  </data>
  <data name="NutritionalValuesForClientGroup_NutritionalValues_Description" xml:space="preserve">
    <value>Seznam oddělený čárkou. Pokud je prázdný, zobrazí se všechny dostupné nutriční hodnoty.</value>
  </data>
  <data name="NutritionalValuesForClientGroup_ClientGroup_DisplayName" xml:space="preserve">
    <value>Skupina klienta</value>
  </data>
  <data name="NutritionalValuesForClientGroup_ClientGroup_Description" xml:space="preserve">
    <value>Skupina klienta, pro kterou se nastavuje seznam nutričních hodnot</value>
  </data>
  <data name="GlobalBehaviorCampaignsConfig_CampaignBehavior_DisplayName" xml:space="preserve">
    <value>Zobrazování kampaní</value>
  </data>
  <data name="GlobalBehaviorCampaignsConfig_CampaignBehavior_Description" xml:space="preserve">
    <value>Nastavení pro zobrazování anketních kampaní</value>
  </data>
  <data name="GlobalServicesFbsServicesConfig_EnumerationsConfig_Description" xml:space="preserve">
    <value>Konfigurace WCF služby pro číselníky</value>
  </data>
  <data name="GlobalServicesFbsServicesConfig_EnumerationsConfig_DisplayName" xml:space="preserve">
    <value>Služba pro číselníky</value>
  </data>
  <data name="CashDeskSaleConfig_CheckServingPeriod_DisplayName" xml:space="preserve">
    <value>Kontrolovat intervaly výdeje</value>
  </data>
  <data name="CashDeskSaleConfig_CheckServingPeriod_Description" xml:space="preserve">
    <value>Pokud je povoleno, Kasa při prodeji jídel kontroluje povolené intervaly výdeje. 
Pokud je zakázáno, pak se intervaly nekontrolují.</value>
  </data>
  <data name="MenuPresenterBehaviourNoteConfig_Allergens_DisplayName" xml:space="preserve">
    <value>Alergeny</value>
  </data>
  <data name="MenuPresenterBehaviourNoteConfig_Allergens_Description" xml:space="preserve">
    <value>Alergeny</value>
  </data>
  <data name="MenuPresenterBehaviourNoteConfig_Note_DisplayName" xml:space="preserve">
    <value>Poznámka</value>
  </data>
  <data name="MenuPresenterBehaviourNoteConfig_Note_Description" xml:space="preserve">
    <value>Poznámka</value>
  </data>
  <data name="MenuPresenterBehaviourNoteConfig_NoteAlt_DisplayName" xml:space="preserve">
    <value>Alt. poznámka</value>
  </data>
  <data name="MenuPresenterBehaviourNoteConfig_NoteAlt_Description" xml:space="preserve">
    <value>Alt. poznámka</value>
  </data>
  <data name="MenuPresenterBehaviourNoteConfig_NutritionalValues_DisplayName" xml:space="preserve">
    <value>Nutriční hodnoty</value>
  </data>
  <data name="MenuPresenterBehaviourNoteConfig_NutritionalValues_Description" xml:space="preserve">
    <value>Nutriční hodnoty</value>
  </data>
  <data name="MenuPresenterBehaviourNoteConfig_NameAlt_DisplayName" xml:space="preserve">
    <value>Alt. název</value>
  </data>
  <data name="MenuPresenterBehaviourNoteConfig_NameAlt_Description" xml:space="preserve">
    <value>Alt. název</value>
  </data>
  <data name="MenuRowNotePartSettings_Visible_DisplayName" xml:space="preserve">
    <value>Zobrazit</value>
  </data>
  <data name="MenuRowNotePartSettings_Visible_Description" xml:space="preserve">
    <value>Bude tato informace zobrazena v menu jako poznámka?</value>
  </data>
  <data name="MenuRowNotePartSettings_Order_DisplayName" xml:space="preserve">
    <value>Pořadí</value>
  </data>
  <data name="MenuRowNotePartSettings_Order_Description" xml:space="preserve">
    <value>Pořadí</value>
  </data>
  <data name="CashDeskManagerConfig_BlindSalesDelivery_DisplayName" xml:space="preserve">
    <value>Odvod tržby naslepo</value>
  </data>
  <data name="CashDeskManagerConfig_BlindSalesDelivery_Description" xml:space="preserve">
    <value>Pokud je povoleno, nemá obsluha při odvodu tržby žádné informace o stavu platidel v zásuvce. Skryty jsou i další informace, ze kterých by se dal obsah zásuvky vypočítat.
Pokud je zakázáno, všechny funkce v Kase jsou přístupné.</value>
  </data>
  <data name="CashDeskSaleConfig_KartouCashNegativeCreditBalanceOnly_DisplayName" xml:space="preserve">
    <value>KartouCash doplácí pouze záporný zůstatek účtu</value>
  </data>
  <data name="CashDeskSaleConfig_KartouCashNegativeCreditBalanceOnly_Description" xml:space="preserve">
    <value>Pokud má strávník na účtu 50,- a úhrada za paragon je 70,-, tak při platbě Cash se zaplatí jen 20,-. Pokud není záporný zůstatek účtu, platba KartouCash se nepovolí.
Není možno kombinovat s "Vyrovnat záporný zůstatek účtu při platbě Kartou-Cash".</value>
  </data>
  <data name="GlobalBehaviourClientAccommodationConfig_EnabledClientAccommodation_DisplayName" xml:space="preserve">
    <value>Povolit ubytování klienta</value>
  </data>
  <data name="GlobalBehaviourClientAccommodationConfig_EnabledClientAccommodation_Description" xml:space="preserve">
    <value>Možnost povolit nebo zakázat ubytování klienta. Pokud je ubytování povoleno, bude v evidenční listu zobrazena záložka Ubytování klienta a rovněž budou dostupné i číselníky pro editaci ubytování.</value>
  </data>
  <data name="CashDeskOfflineConfig_MealPriceBySubsidyClientGroupIds_DisplayName" xml:space="preserve">
    <value>Výpočet podle Kategorie dotace pouze pro skupiny klientů</value>
  </data>
  <data name="CashDeskOfflineConfig_MealPriceBySubsidyClientGroupIds_Description" xml:space="preserve">
    <value>Zadejte Id skupin klientů oddělené čárkami, pro které se budou počítat ceny v offline podle Kategorie dotace.
Pokud není vyplněna žádná skupina, počítají se ceny podle Kategorie dotace pro všechny klienty.</value>
  </data>
  <data name="GlobalBehaviourDebugConfig_DebugLevel_DisplayName" xml:space="preserve">
    <value>Podpora pro ladění</value>
  </data>
  <data name="GlobalBehaviourDebugConfig_DebugLevel_Description" xml:space="preserve">
    <value>Nastavuje úroveň podpory pro ladění. Používá se hlavně pro testování release verze na testovně. Ve výjimečných případech lze dočasně zapnout u zákazníka, pokud programátor výslovně odsouhlasí.</value>
  </data>
  <data name="PresPointBehaviourStartMenuConfig_SubsidyCategory1_DisplayName" xml:space="preserve">
    <value>Cena 1</value>
  </data>
  <data name="PresPointBehaviourStartMenuConfig_SubsidyCategory1_Description" xml:space="preserve">
    <value>Kategorie dotace pro zobrazení ceny 1</value>
  </data>
  <data name="PresPointBehaviourStartMenuConfig_SubsidyCategory2_DisplayName" xml:space="preserve">
    <value>Cena 2</value>
  </data>
  <data name="PresPointBehaviourStartMenuConfig_SubsidyCategory2_Description" xml:space="preserve">
    <value>Kategorie dotace pro zobrazení ceny 2</value>
  </data>
  <data name="PresPointBehaviourStartMenuConfig_SubsidyCategory3_DisplayName" xml:space="preserve">
    <value>Cena 3</value>
  </data>
  <data name="PresPointBehaviourStartMenuConfig_SubsidyCategory3_Description" xml:space="preserve">
    <value>Kategorie dotace pro zobrazení ceny 3</value>
  </data>
  <data name="PresPointBehaviourStartMenuConfig_SubsidyCategory4_DisplayName" xml:space="preserve">
    <value>Cena 4</value>
  </data>
  <data name="PresPointBehaviourStartMenuConfig_SubsidyCategory4_Description" xml:space="preserve">
    <value>Kategorie dotace pro zobrazení ceny 4</value>
  </data>
  <data name="MenuPresenterBehaviourColumnsConfig_Price3_DisplayName" xml:space="preserve">
    <value>Alternativní cena 2</value>
  </data>
  <data name="MenuPresenterBehaviourColumnsConfig_Price3_Description" xml:space="preserve">
    <value>Alternativní cena 2</value>
  </data>
  <data name="MenuPresenterBehaviourColumnsConfig_Price4_DisplayName" xml:space="preserve">
    <value>Alternativní cena 3</value>
  </data>
  <data name="MenuPresenterBehaviourColumnsConfig_Price4_Description" xml:space="preserve">
    <value>Alternativní cena 3</value>
  </data>
  <data name="GlobalSupportRemoteAccessConfig_Enabled_DisplayName" xml:space="preserve">
    <value>Povoleno</value>
  </data>
  <data name="GlobalSupportRemoteAccessConfig_Enabled_Description" xml:space="preserve">
    <value>Povolení/zakázání vzdálené správy. Pokud je zakáno, je vzdálená správa (TeamViewer) ve všech aplikacích skryta.</value>
  </data>
  <data name="SloHealthyMenuConfig_Items_DisplayName" xml:space="preserve">
    <value>Nastavení</value>
  </data>
  <data name="SloHealthyMenuConfig_Items_Description" xml:space="preserve">
    <value>Nastavení k danému datu</value>
  </data>
  <data name="SloHealthyMenuItem_ValidFrom_DisplayName" xml:space="preserve">
    <value>Platné od</value>
  </data>
  <data name="SloHealthyMenuItem_ValidFrom_Description" xml:space="preserve">
    <value>Datum, od kterého je daná konfiugrace platná.</value>
  </data>
  <data name="SloHealthyMenuItem_WorkplaceId_DisplayName" xml:space="preserve">
    <value>Id provozovny</value>
  </data>
  <data name="SloHealthyMenuItem_WorkplaceId_Description" xml:space="preserve">
    <value>Provozovna zdravého jídla</value>
  </data>
  <data name="SloHealthyMenuItem_MealKindId_DisplayName" xml:space="preserve">
    <value>Id druhu jídla</value>
  </data>
  <data name="SloHealthyMenuItem_MealKindId_Description" xml:space="preserve">
    <value>Id druhu zdravého jídla</value>
  </data>
  <data name="SloHealthyMenuItem_Alt_DisplayName" xml:space="preserve">
    <value>Alternativa</value>
  </data>
  <data name="SloHealthyMenuItem_Alt_Description" xml:space="preserve">
    <value>Alternativa zdravého jídla</value>
  </data>
  <data name="SloHealthyMenuItem_MealKinds_DisplayName" xml:space="preserve">
    <value>Seznam druhů jídel</value>
  </data>
  <data name="SloHealthyMenuItem_MealKinds_Description" xml:space="preserve">
    <value>Každy druh jídla obsahuje složky zdravého jídla</value>
  </data>
  <data name="GlobalBehaviorEetConfig_EetMode_DisplayName" xml:space="preserve">
    <value>Mód EET</value>
  </data>
  <data name="GlobalBehaviorEetConfig_EetMode_Description" xml:space="preserve">
    <value>Určuje v jakém módu bude fungovat EET</value>
  </data>
  <data name="GlobalBehaviorEetConfig_Settings_DisplayName" xml:space="preserve">
    <value>Nastavení</value>
  </data>
  <data name="GlobalBehaviorEetConfig_Settings_Description" xml:space="preserve">
    <value>Nastavení pro vybraný mód EET</value>
  </data>
  <data name="GlobalServicesAppServerConnectionConfig_ServerAddress_DisplayName" xml:space="preserve">
    <value>Adresa serveru</value>
  </data>
  <data name="GlobalServicesAppServerConnectionConfig_ServerAddress_Description" xml:space="preserve">
    <value>Adresa, na které běží aplikační server.

Adresa serveru má tvar https://&lt;appserver&gt;:&lt;port&gt;
&lt;appserver&gt;: Adresa aplikačního serveru je DNS název stanice s aplikačním serverem. 
&lt;port&gt;: Implicitní port je 17000.
Příklad: https://appserver.menza.local:17000.

Pomocí čísla portu se nastavuje i port, na kterém běží server. Po změně je třeba restartovat aplikační server i stanice.

Původní verze aplikačního serveru (2020.2.2 a nižsí) používala WCF standard net.tcp. Nový aplikační server (2020.3.1 a vyšší) toto nastavení automaticky nahrazuje za https. Při upgradu tedy není třeba měnit toto nastavení.
</value>
  </data>
  <data name="EetSettingsBase_ServerAddress_DisplayName" xml:space="preserve">
    <value>Adresa serveru</value>
  </data>
  <data name="EetSettingsBase_ServerAddress_Description" xml:space="preserve">
    <value>Adresa na které běží Eet server</value>
  </data>
  <data name="EetProductionFromDateSettings_StartDate_DisplayName" xml:space="preserve">
    <value>Datum spuštění ostrého provozu EET</value>
  </data>
  <data name="EetProductionFromDateSettings_StartDate_Description" xml:space="preserve">
    <value>Od tohoto data se budou zasílat ostré hlášení na portál EET.</value>
  </data>
  <data name="EetMaintenanceConfig_TriggerConfig_DisplayName" xml:space="preserve">
    <value>Nastavení časovače</value>
  </data>
  <data name="EetMaintenanceConfig_TriggerConfig_Description" xml:space="preserve">
    <value>Nastavení parametrů časovače</value>
  </data>
  <data name="EetMaintenanceConfig_PurgeNormalItemsAfter_DisplayName" xml:space="preserve">
    <value>Smazat normální záznamy po [měs]</value>
  </data>
  <data name="EetMaintenanceConfig_PurgeNormalItemsAfter_Description" xml:space="preserve">
    <value>Nastavuje časový úsek, po který uschovávat záznamy o úspěšně odeslaných hlášeních EET. Starší záznamy budou vymazány.</value>
  </data>
  <data name="EetMaintenanceConfig_PurgeErrorItemsAfter_DisplayName" xml:space="preserve">
    <value>Smazat chybové záznamy po [měs]</value>
  </data>
  <data name="EetMaintenanceConfig_PurgeErrorItemsAfter_Description" xml:space="preserve">
    <value>Nastavuje časový úsek, po který uschovávat záznamy o hlášeních EET, při jejichž odesílání nastala chyba. Starší záznamy budou vymazány.</value>
  </data>
  <data name="GlobalBehaviorEetConfig_MaintenanceConfig_DisplayName" xml:space="preserve">
    <value>Nastavení údržby</value>
  </data>
  <data name="GlobalBehaviorEetConfig_MaintenanceConfig_Description" xml:space="preserve">
    <value>Nastavení parametrů údržby protokolu</value>
  </data>
  <data name="GlobalBehaviorEetConfig_SendTimeout_DisplayName" xml:space="preserve">
    <value>Timeout pro odeslání [s]</value>
  </data>
  <data name="GlobalBehaviorEetConfig_SendTimeout_Description" xml:space="preserve">
    <value>Timeout pro odeslání na portál EET. Po vypršení totoho timeoutu se komunikace považuje za neúspěšnou a vytiskne se paragon s offline kódy.</value>
  </data>
  <data name="GlobalBehaviorEetConfig_ResendTimeout_DisplayName" xml:space="preserve">
    <value>Timeout pro opakované odeslání [s]</value>
  </data>
  <data name="GlobalBehaviorEetConfig_ResendTimeout_Description" xml:space="preserve">
    <value>Timeout pro opakované odeslání na portál EET. Má větší hodnotu, aby se zvýšila šance na správné odeslání.</value>
  </data>
  <data name="GlobalBehaviorEetConfig_MessagesConfig_DisplayName" xml:space="preserve">
    <value>Nastavení pro zasílání událostí</value>
  </data>
  <data name="GlobalBehaviorEetConfig_MessagesConfig_Description" xml:space="preserve">
    <value>Nastavení parametrů pro zasílání událostí</value>
  </data>
  <data name="EetProductionFromDateSettings_StartTestDate_DisplayName" xml:space="preserve">
    <value>Datum spuštění testovacího provozu EET</value>
  </data>
  <data name="EetProductionFromDateSettings_StartTestDate_Description" xml:space="preserve">
    <value>Od tohoto data se budou zasílat testovací hlášení na portál EET.</value>
  </data>
  <data name="EetProductionFromDateSettings_PlayGroundServerAddress_DisplayName" xml:space="preserve">
    <value>Adresa serveru PlayGround</value>
  </data>
  <data name="EetProductionFromDateSettings_PlayGroundServerAddress_Description" xml:space="preserve">
    <value>Adresa serveru PlayGround. Použije se pro spouštění testu připojení před datem 'Datum spuštění testovacího provozu EET'</value>
  </data>
  <data name="GlobalBehaviourSalesSlipPrintConfig_ESalesSlipMode_DisplayName" xml:space="preserve">
    <value>Elektronické účtenky</value>
  </data>
  <data name="GlobalBehaviourSalesSlipPrintConfig_ESalesSlipMode_Description" xml:space="preserve">
    <value>Nastavení toto, jak se mají vystavovat elektronické účtenky.</value>
  </data>
  <data name="CashDeskPluginsTulPluginConfig_RestrictedAccessEnabled_DisplayName" xml:space="preserve">
    <value>Omezený přístup ke Kase</value>
  </data>
  <data name="CashDeskPluginsTulPluginConfig_RestrictedAccessEnabled_Description" xml:space="preserve">
    <value>Pokud je zapnuto, nejsou přístupné funkce Historie účtu, Tisk stravenky, Tisk hesla, Náhradní přihlášení.</value>
  </data>
  <data name="EetMessagesConfig_ReportUnsentAfter_DisplayName" xml:space="preserve">
    <value>Hlásit záznamy neodeslané déle než [hod]</value>
  </data>
  <data name="EetMessagesConfig_ReportUnsentAfter_Description" xml:space="preserve">
    <value>Pokud jsou ve frontě záznamy, které nemohly být odeslány déle než stanovený počet hodin, pak se odešle hlášení obsluze.</value>
  </data>
  <data name="GlobalServicesWebProxyConfig_Enabled_DisplayName" xml:space="preserve">
    <value>Povoleno</value>
  </data>
  <data name="GlobalServicesWebProxyConfig_Enabled_Description" xml:space="preserve">
    <value>Pokud je povoleno, bude proxy použita pro přístup k vnějšímu webu. Pokud je zakázáno, předpokládá se přístup bez proxy - přímý přístup či NAT.</value>
  </data>
  <data name="GlobalServicesWebProxyConfig_ProxyAddress_DisplayName" xml:space="preserve">
    <value>Adresa proxy</value>
  </data>
  <data name="GlobalServicesWebProxyConfig_ProxyAddress_Description" xml:space="preserve">
    <value>Adresa proxy serveru, může být uveden i port. Má tvar &lt;server&gt;:&lt;port&gt;
Příklad: webproxy:4043</value>
  </data>
  <data name="GlobalServicesWebProxyConfig_UserName_DisplayName" xml:space="preserve">
    <value>Uživatelské jméno</value>
  </data>
  <data name="GlobalServicesWebProxyConfig_UserName_Description" xml:space="preserve">
    <value>Uživatelské jméno, pokud proxy vyžaduje autorizaci.</value>
  </data>
  <data name="GlobalServicesWebProxyConfig_Password_DisplayName" xml:space="preserve">
    <value>Heslo</value>
  </data>
  <data name="GlobalServicesWebProxyConfig_Password_Description" xml:space="preserve">
    <value>Uživatelské heslo, pokud proxy vyžaduje autorizaci.</value>
  </data>
  <data name="GlobalServicesWebProxyConfig_Domain_DisplayName" xml:space="preserve">
    <value>Doména</value>
  </data>
  <data name="GlobalServicesWebProxyConfig_Domain_Description" xml:space="preserve">
    <value>Doména, pokud proxy vyžaduje autorizaci.</value>
  </data>
  <data name="EetProductionFromDateSettings_AutoShiftTestDateIfNotReady_DisplayName" xml:space="preserve">
    <value>Automaticky posunout datum testovacího provozu pokud není připraveno</value>
  </data>
  <data name="EetProductionFromDateSettings_AutoShiftTestDateIfNotReady_Description" xml:space="preserve">
    <value>Pokud je povoleno, pak aplikační server automaticky kontroluje každou hodinu připravenost EET ke spuštění testovacího provozu. Pokud není testovací provoz připraven, pak posune datum spuštění testovacího provozu o den dále. Lze posunout maximálně do Datum spuštění ostrého provozu - 1.
Tzn. den před ostrým provozem se testovací provoz spustí vždy.</value>
  </data>
  <data name="NetTcpSecureUserAuthBindingTypeSettings_StoreName_DisplayName" xml:space="preserve">
    <value>Úložiště</value>
  </data>
  <data name="NetTcpSecureUserAuthBindingTypeSettings_StoreName_Description" xml:space="preserve">
    <value>Typ úložiště, ve kterém se nachází certifikát. Název úložiště je stejný, jako je název uzlu stromu ve Správě certifikátů.
Certifikáty podepsané sebou musí být uloženy v Důvěryhodné kořenové certifikační autority.</value>
  </data>
  <data name="MenuPresenterBehaviourNoteConfig_AllergensAlt_DisplayName" xml:space="preserve">
    <value>Alt. alergeny</value>
  </data>
  <data name="MenuPresenterBehaviourNoteConfig_AllergensAlt_Description" xml:space="preserve">
    <value>Alt. alergeny</value>
  </data>
  <data name="NetTcpSecureUserAuthBindingTypeSettings_FindType_DisplayName" xml:space="preserve">
    <value>Typ hledání</value>
  </data>
  <data name="NetTcpSecureUserAuthBindingTypeSettings_FindType_Description" xml:space="preserve">
    <value>Určuje, jakým způsobem se bude hledat hodnota 'Hledaná hodnota'.</value>
  </data>
  <data name="NetTcpSecureUserAuthBindingTypeSettings_FindValue_DisplayName" xml:space="preserve">
    <value>Hledaná hodnota</value>
  </data>
  <data name="NetTcpSecureUserAuthBindingTypeSettings_FindValue_Description" xml:space="preserve">
    <value>Hodnota, podle které se má vyhledat certifikát.
Nejčastěji se používá hledání podle otisku certifikátu. Otisk certifikátu najdete takto:
Otevřete Správu certifikátů. Najděte příslušný certifikát ve složce shodné s nastavením  Úložiště zde.
Najděte certifikát ve složce.
Zvolte Otevřít/Podrobnosti, najděte položku Kryptografický otisk.
Okopírujte hodnotu otisku. Bude mít tvar např. "‎75 31 0a 28 f4 b5 98 f9 95 a2 07 7d 7d 61 30 71 13 fe 16 d9". 
Vložte sem, při vložení se zkonvertuje do tvaru bez mezer.</value>
  </data>
  <data name="GlobalServicesAppServerConnectionConfig_BindingTypeSettings_DisplayName" xml:space="preserve">
    <value>Nastavení pro typ spojení</value>
  </data>
  <data name="GlobalServicesAppServerConnectionConfig_BindingTypeSettings_Description" xml:space="preserve">
    <value>Nastavení specifické pro vybraný typ spojení.</value>
  </data>
  <data name="CashDeskBehaviorEetConfig_EetEnabled_DisplayName" xml:space="preserve">
    <value>EET povoleno</value>
  </data>
  <data name="CashDeskBehaviorEetConfig_EetEnabled_Description" xml:space="preserve">
    <value>Udává, zda pro tuto Kasu je povoleno EET. Kombinuje se s globálním nastavením Global.Behavior.Eet.</value>
  </data>
  <data name="CashDeskBehaviorEetConfig_SwitchMode_DisplayName" xml:space="preserve">
    <value>Povolit tlačítko EET</value>
  </data>
  <data name="CashDeskBehaviorEetConfig_SwitchMode_Description" xml:space="preserve">
    <value>Povoluje zobrazení tlačítka EET, které umožňuje obsluze ručně určit, zda paragon odešle do EET.</value>
  </data>
  <data name="CashDeskBehaviorEetConfig_SwitchPersistenceMode_DisplayName" xml:space="preserve">
    <value>Pamatovat stav tlačítka při založení nového paragonu (ne v Rest Kase)</value>
  </data>
  <data name="CashDeskBehaviorEetConfig_SwitchPersistenceMode_Description" xml:space="preserve">
    <value>Určuje, zda se má při založení nového paragonu zapamatovat stav tlačítka nebo naopak resetovat na výchozí hodnotu. Platí pouze pro Keyboard a Touch Kasu, pro retaurační se vždy přenastaví výchozí stav.</value>
  </data>
  <data name="GlobalBehaviourDebugConfig_Password_DisplayName" xml:space="preserve">
    <value>Heslo</value>
  </data>
  <data name="GlobalBehaviourDebugConfig_Password_Description" xml:space="preserve">
    <value>Heslo, které se použije při konfigurace "povoleno po zadání hesla".</value>
  </data>
  <data name="OfficeWorkplacesBehaviourMenuConfig_ShowMealTemplates_DisplayName" xml:space="preserve">
    <value>Nabízet sortiment jídelníčku</value>
  </data>
  <data name="OfficeWorkplacesBehaviourMenuConfig_ShowMealTemplates_Description" xml:space="preserve">
    <value>Povolí/zakáže vyskakovací menu s dostupným sortimentem jídelníčku pro jednotlivá jídla.</value>
  </data>
  <data name="GlobalRulesPasswordConfig_AllowBackslash_DisplayName" xml:space="preserve">
    <value>Povolit zpětné lomítko</value>
  </data>
  <data name="GlobalRulesPasswordConfig_AllowBackslash_Description" xml:space="preserve">
    <value>Povolit zpětné lomítko</value>
  </data>
  <data name="GlobalRulesPasswordConfig_AllowDot_DisplayName" xml:space="preserve">
    <value>Povolit tečku</value>
  </data>
  <data name="GlobalRulesPasswordConfig_AllowDot_Description" xml:space="preserve">
    <value>Povolit tečku</value>
  </data>
  <data name="GlobalRulesPasswordConfig_AllowSlash_DisplayName" xml:space="preserve">
    <value>Povolit lomítko</value>
  </data>
  <data name="GlobalRulesPasswordConfig_AllowSlash_Description" xml:space="preserve">
    <value>Povolit lomítko</value>
  </data>
  <data name="GlobalHwVgaClientDisplayConfig_ClientDisplayType_DisplayName" xml:space="preserve">
    <value>Typ displeje</value>
  </data>
  <data name="GlobalHwVgaClientDisplayConfig_ClientDisplayType_Description" xml:space="preserve">
    <value>Nastavení typu displeje</value>
  </data>
  <data name="GlobalHwVgaClientDisplayConfig_Settings_DisplayName" xml:space="preserve">
    <value>Nastavení</value>
  </data>
  <data name="GlobalHwVgaClientDisplayConfig_Settings_Description" xml:space="preserve">
    <value>Nastavení pro konkrétní typ displeje.</value>
  </data>
  <data name="GlobalHwVgaClientDisplayConfig_MonitorNumber_DisplayName" xml:space="preserve">
    <value>Číslo monitoru ve Windows</value>
  </data>
  <data name="GlobalHwVgaClientDisplayConfig_MonitorNumber_Description" xml:space="preserve">
    <value>Číslo monitoru zákaznického displeje přiřazené Windows. 
Win10: Pravé tlačítko na ploše, Nastavení zobrazení, opsat číslo monitoru se zákaznickým displejem.
0 = automaticky zobrazit na sekundárním monitoru.</value>
  </data>
  <data name="GlobalHwVgaClientDisplayConfig_SkinName_DisplayName" xml:space="preserve">
    <value>Název skinu</value>
  </data>
  <data name="GlobalHwVgaClientDisplayConfig_SkinName_Description" xml:space="preserve">
    <value>Název skinu DevExpress. Seznam skinů vč. toho, jak vypadají, např. v záložce Vzhled v hlavním menu.</value>
  </data>
  <data name="GlobalHwVgaClientDisplayConfig_ShowDisponsibleCreditBalance_DisplayName" xml:space="preserve">
    <value>Zobrazovat disponibilní zůstatek účtu</value>
  </data>
  <data name="GlobalHwVgaClientDisplayConfig_ShowDisponsibleCreditBalance_Description" xml:space="preserve">
    <value>Pokud je povoleno, zobrazuje se disponibilní zůstatek účtu.</value>
  </data>
  <data name="GlobalHwVgaClientDisplayConfig_FullScreen_DisplayName" xml:space="preserve">
    <value>Celá obrazovka</value>
  </data>
  <data name="GlobalHwVgaClientDisplayConfig_FullScreen_Description" xml:space="preserve">
    <value>Určuje, zda bude okno displeje zobrazeno přes celou obrazovku bez okrajů. Vypíná se pouze pro ladící účely.</value>
  </data>
  <data name="OfficeWorkplacesBehaviourOrderChangesConfig_MealKindIdRestrictions_DisplayName" xml:space="preserve">
    <value>Druhy jídel</value>
  </data>
  <data name="OfficeWorkplacesBehaviourOrderChangesConfig_MealKindIdRestrictions_Description" xml:space="preserve">
    <value>Druhy jídel, které budou dostupné pro filtrování. Pokud hodnota nebude vyplněna, filtrování bude bez omezení.</value>
  </data>
  <data name="MobileOrderingBehaviorMenuConfig_ShowMessSize_DisplayName" xml:space="preserve">
    <value>Zobrazovat velikost porce</value>
  </data>
  <data name="MobileOrderingBehaviorMenuConfig_ShowMessSize_Description" xml:space="preserve">
    <value>Pokud je povoleno, zobrazuje se v detailu menu velikost porce jídla.</value>
  </data>
  <data name="OfficeMealDistributionReportsDeliveryAlmedConfig_Shared_DisplayName" xml:space="preserve">
    <value>Sdilený text</value>
  </data>
  <data name="OfficeMealDistributionReportsDeliveryAlmedConfig_Shared_Description" xml:space="preserve">
    <value>Text, který se zobrazí pro teplá i studená jídla na konci patičky.</value>
  </data>
  <data name="OfficeMealDistributionReportsDeliveryAlmedConfig_CoolAndWarmText_DisplayName" xml:space="preserve">
    <value>Text pro teplá a studená jídla</value>
  </data>
  <data name="OfficeMealDistributionReportsDeliveryAlmedConfig_CoolAndWarmText_Description" xml:space="preserve">
    <value>Text, který se zobrazí, pokud sestava obsahuje teplá i studená jídla.</value>
  </data>
  <data name="OfficeMealDistributionReportsDeliveryAlmedConfig_OnlyCoolText_DisplayName" xml:space="preserve">
    <value>Text pro studená jídla</value>
  </data>
  <data name="OfficeMealDistributionReportsDeliveryAlmedConfig_OnlyCoolText_Description" xml:space="preserve">
    <value>Text, který se zobrazí, pokud sestava obsahuje pouze studená jídla.</value>
  </data>
  <data name="OfficeMealDistributionReportsDeliveryAlmedConfig_OnlyWarmText_DisplayName" xml:space="preserve">
    <value>Text pro teplá jídla</value>
  </data>
  <data name="OfficeMealDistributionReportsDeliveryAlmedConfig_OnlyWarmText_Description" xml:space="preserve">
    <value>Text, který se zobrazí, pokud sestava obsahuje pouze teplá jídla.</value>
  </data>
  <data name="GlobalRulesCashDeskConfig_DepositWithdrawalTime_DisplayName" xml:space="preserve">
    <value>Čas vkladu/výběru</value>
  </data>
  <data name="GlobalRulesCashDeskConfig_DepositWithdrawalTime_Description" xml:space="preserve">
    <value>Určuje, ke kdy se budou provádět vklady/výběry z pokladní zásuvky.</value>
  </data>
  <data name="TechSupportMessagesDbMaintenanceConfig_PurgeOlderThan_DisplayName" xml:space="preserve">
    <value>Smazat záznamy starší než [měs]</value>
  </data>
  <data name="TechSupportMessagesDbMaintenanceConfig_PurgeOlderThan_Description" xml:space="preserve">
    <value>Smazat záznamy starší než zadaný počet měsíců.</value>
  </data>
  <data name="TechSupportMessagesDbMaintenanceConfig_MaxTableSize_DisplayName" xml:space="preserve">
    <value>Maximální velikost dat v tabulce zpráv [MB]</value>
  </data>
  <data name="TechSupportMessagesDbMaintenanceConfig_MaxTableSize_Description" xml:space="preserve">
    <value>Maximální velikost tabulky v Megabytech. Pokud přesahuje velikost tabulky danou velikost, mažou se staré záznamy. Velikost tabulky se počítá jako součet velikosti řádků v tabulce, tzn. množství skutečně alokovaného místa je vyšší o volné místo, zarovnání stránek atd.</value>
  </data>
  <data name="GlobalServicesDbMaintenanceConfig_TriggerConfig_DisplayName" xml:space="preserve">
    <value>Nastavení časovače</value>
  </data>
  <data name="GlobalServicesDbMaintenanceConfig_TriggerConfig_Description" xml:space="preserve">
    <value>Nastavení parametrů časovače</value>
  </data>
  <data name="GlobalServicesDbMaintenanceConfig_TechSupportMessagesConfig_DisplayName" xml:space="preserve">
    <value>Zprávy technické podpory</value>
  </data>
  <data name="GlobalServicesDbMaintenanceConfig_TechSupportMessagesConfig_Description" xml:space="preserve">
    <value>Nastavení pro údržbu zpráv technické podpory</value>
  </data>
  <data name="CashDeskSaleConfig_ReadOrdersWithFullPrice_DisplayName" xml:space="preserve">
    <value>Načítat objednávky v plných cenách</value>
  </data>
  <data name="CashDeskSaleConfig_ReadOrdersWithFullPrice_Description" xml:space="preserve">
    <value>Normálně se objednávky načítají za nulovou cenu, protože jsou už předem zaplacené a na Kase se provádí pouze výdej. Někteří klienti ale požadují, aby se tyto objednávky odesílaly do EET za plnou cenu. Pak se musí zapnout tento parametr a objednávky se budou v Kase zobrazovat za plnou cenu. Tisknout se budou také za plnou cenu, do celkové ceny paragonu se ale počítat nebudou.</value>
  </data>
  <data name="OfficeBehaviorEetConfig_EetEnabled_DisplayName" xml:space="preserve">
    <value>EET povoleno</value>
  </data>
  <data name="OfficeBehaviorEetConfig_EetEnabled_Description" xml:space="preserve">
    <value>Udává, zda pro tuto Kancelář je povoleno EET. Kombinuje se s globálním nastavením Global.Behavior.Eet.</value>
  </data>
  <data name="WinForm2xVariableSettings_MonitorNumber_DisplayName" xml:space="preserve">
    <value>Číslo monitoru ve Windows</value>
  </data>
  <data name="WinForm2xVariableSettings_MonitorNumber_Description" xml:space="preserve">
    <value>Číslo monitoru zákaznického displeje přiřazené Windows. 
Win10: Pravé tlačítko na ploše, Nastavení zobrazení, opsat číslo monitoru se zákaznickým displejem.
0 = automaticky zobrazit na sekundárním monitoru.</value>
  </data>
  <data name="WinForm2xVariableSettings_FullScreen_DisplayName" xml:space="preserve">
    <value>Celá obrazovka</value>
  </data>
  <data name="WinForm2xVariableSettings_FullScreen_Description" xml:space="preserve">
    <value>Určuje, zda bude okno displeje zobrazeno přes celou obrazovku bez okrajů. Vypíná se pouze pro ladící účely.</value>
  </data>
  <data name="WinForm2xVariableSettings_Columns_DisplayName" xml:space="preserve">
    <value>Počet znaků</value>
  </data>
  <data name="WinForm2xVariableSettings_Columns_Description" xml:space="preserve">
    <value>Určuje, kolik znaků bude display zobrazovat. Velikost písma se automaticky vypočte tak, aby se daný počet znaků vešel na obrazovku. Čím méně znaků, tím větší písmo a obráceně.</value>
  </data>
  <data name="GlobalRulesCardManagementConfig_CardTypes_DisplayName" xml:space="preserve">
    <value>Použité typy karet</value>
  </data>
  <data name="GlobalRulesCardManagementConfig_CardTypes_Description" xml:space="preserve">
    <value>Seznam typů karet použitých na zakázce.</value>
  </data>
  <data name="IdentCardTypeItem_IdentCardType_DisplayName" xml:space="preserve">
    <value>Typ karty</value>
  </data>
  <data name="IdentCardTypeItem_IdentCardType_Description" xml:space="preserve">
    <value>Typ karty</value>
  </data>
  <data name="CustomVgaClientDisplaySettings_GridFontScaleFactor_DisplayName" xml:space="preserve">
    <value>Poměr fontu gridu</value>
  </data>
  <data name="CustomVgaClientDisplaySettings_GridFontScaleFactor_Description" xml:space="preserve">
    <value>Poměr fontu k originálnímu fontu. 1.0 je originální velikost, 2.0 je 2 x větší písmo než originální.</value>
  </data>
  <data name="CustomVgaClientDisplaySettings_HeaderFontScaleFactor_DisplayName" xml:space="preserve">
    <value>Poměr fontu záhlaví</value>
  </data>
  <data name="CustomVgaClientDisplaySettings_HeaderFontScaleFactor_Description" xml:space="preserve">
    <value>Poměr fontu k originálnímu fontu. 1.0 je originální velikost, 2.0 je 2 x větší písmo než originální.</value>
  </data>
  <data name="GlobalHwNawiScaleConfig_Settings_DisplayName" xml:space="preserve">
    <value>Nastavení</value>
  </data>
  <data name="GlobalHwNawiScaleConfig_Settings_Description" xml:space="preserve">
    <value>Nastavení pro konkrétní druh váhy</value>
  </data>
  <data name="GlobalHwNawiScaleConfig_NawiScaleType_DisplayName" xml:space="preserve">
    <value>Typ váhy</value>
  </data>
  <data name="GlobalHwNawiScaleConfig_NawiScaleType_Description" xml:space="preserve">
    <value>Nastavuje konkrétní typ váhy</value>
  </data>
  <data name="GlobalRulesClientManagementConfig_BirthNumberValidationType_DisplayName" xml:space="preserve">
    <value>Validace rodného čísla</value>
  </data>
  <data name="GlobalRulesClientManagementConfig_BirthNumberValidationType_Description" xml:space="preserve">
    <value>Způsob validace rozdného čísla</value>
  </data>
  <data name="GlobalRulesCollectionConfig_CollectionMaxValue_DisplayName" xml:space="preserve">
    <value>Maximální hodnota inkasa</value>
  </data>
  <data name="GlobalRulesCollectionConfig_CollectionMaxValue_Description" xml:space="preserve">
    <value>Maximální částka, na kterou lze nastavit inkaso.</value>
  </data>
  <data name="GlobalRulesCollectionConfig_CollectionLimitsEnabled_DisplayName" xml:space="preserve">
    <value>Povolení omezení inkasa</value>
  </data>
  <data name="GlobalRulesCollectionConfig_CollectionLimitsEnabled_Description" xml:space="preserve">
    <value>Pokud je povoleno, nelze v Kanceláři 8 vygenerovat požadavek na vyšší častku inkasa.</value>
  </data>
  <data name="GlobalServicesIpsConfig_Enabled_DisplayName" xml:space="preserve">
    <value>Povolit</value>
  </data>
  <data name="GlobalServicesIpsConfig_Enabled_Description" xml:space="preserve">
    <value>Povolí interní platební systém. Na základě toho se v jednotlvých aplikacích zpřístupní dialogy pro převody peněz.</value>
  </data>
  <data name="MenuPresenterHwIpCamConfig_Enabled_DisplayName" xml:space="preserve">
    <value>Povolit</value>
  </data>
  <data name="MenuPresenterHwIpCamConfig_Enabled_Description" xml:space="preserve">
    <value>Pokud je povoleno, bude na obrazovce menu zobrazen stream IP kamery.</value>
  </data>
  <data name="GlobalServicesIpsConfig_MaxTransferableAmount_DisplayName" xml:space="preserve">
    <value>Maximální častka k převodu</value>
  </data>
  <data name="GlobalServicesIpsConfig_MaxTransferableAmount_Description" xml:space="preserve">
    <value>Maximální častka k převodu</value>
  </data>
  <data name="GlobalServicesIpsConfig_MinTransferableAmount_DisplayName" xml:space="preserve">
    <value>Minimální častka k převodu</value>
  </data>
  <data name="GlobalServicesIpsConfig_MinTransferableAmount_Description" xml:space="preserve">
    <value>Minimální častka k převodu</value>
  </data>
  <data name="GlobalServicesIpsConfig_MaxKreditBalanceAfterEncashment_DisplayName" xml:space="preserve">
    <value>Maximální částka po dorovnání</value>
  </data>
  <data name="GlobalServicesIpsConfig_MaxKreditBalanceAfterEncashment_Description" xml:space="preserve">
    <value>Maximální částka po dorovnání</value>
  </data>
  <data name="GlobalServicesIpsConfig_MinKreditBalanceAfterEncashment_DisplayName" xml:space="preserve">
    <value>Minimální částka po dorovnání</value>
  </data>
  <data name="GlobalServicesIpsConfig_MinKreditBalanceAfterEncashment_Description" xml:space="preserve">
    <value>Minimální částka po dorovnání</value>
  </data>
  <data name="GlobalServicesIpsConfig_TransactionHistoryMaxRows_DisplayName" xml:space="preserve">
    <value>Počet řádků v historii transakcí</value>
  </data>
  <data name="GlobalServicesIpsConfig_TransactionHistoryMaxRows_Description" xml:space="preserve">
    <value>Počet řádku v hisorii transakcí využívá zatím jen WebKredit</value>
  </data>
  <data name="MenuPresenterHwIpCamConfig_RtspStreamUrl_DisplayName" xml:space="preserve">
    <value>RTSP adresa</value>
  </data>
  <data name="MenuPresenterHwIpCamConfig_RtspStreamUrl_Description" xml:space="preserve">
    <value>RTSP adresa streamu webkamery. Na JCU: rtsp://kam-cam10.cam.jcu.cz/axis-media/media.amp</value>
  </data>
  <data name="MenuPresenterHwIpCamConfig_ImageHeight_DisplayName" xml:space="preserve">
    <value>Výška obrazu [px]</value>
  </data>
  <data name="MenuPresenterHwIpCamConfig_ImageHeight_Description" xml:space="preserve">
    <value>Výška obrazu IP kamery. Šiřka se přizpusobí poměru stran kamery.</value>
  </data>
  <data name="MenuPresenterHwIpCamConfig_MenuPresenterCamPosition_DisplayName" xml:space="preserve">
    <value>Pozice</value>
  </data>
  <data name="MenuPresenterHwIpCamConfig_MenuPresenterCamPosition_Description" xml:space="preserve">
    <value>Pozice obrazu IP kamery.</value>
  </data>
  <data name="MenuPresenterHwIpCamConfig_FrameDelay_DisplayName" xml:space="preserve">
    <value>Zpoždění mezi snímky [ms]</value>
  </data>
  <data name="MenuPresenterHwIpCamConfig_FrameDelay_Description" xml:space="preserve">
    <value>Zpoždení mezi zobrazením jednotlivých snímků. Zadáva se v ms. Vhodným nastavením lze snížít vytížení sítě.</value>
  </data>
  <data name="GlobalRulesCashDeskConfig_SalesDeliveryTimeType_DisplayName" xml:space="preserve">
    <value>Čas odvodu tržby</value>
  </data>
  <data name="GlobalRulesCashDeskConfig_SalesDeliveryTimeType_Description" xml:space="preserve">
    <value>Určuje, ke kdy se bude provádět odvod tržby</value>
  </data>
  <data name="DevicesControllerOfflineConfig_Enabled_DisplayName" xml:space="preserve">
    <value>Povolit offline</value>
  </data>
  <data name="DevicesControllerOfflineConfig_Enabled_Description" xml:space="preserve">
    <value>Povolení/zákazání offline výdeje a prodeje jídel. Offline funguje jen pro nový typ čtečky s RPi</value>
  </data>
  <data name="DevicesControllerOfflineConfig_ConnectionTimeout_DisplayName" xml:space="preserve">
    <value>Připojení k DB timeout [s]</value>
  </data>
  <data name="DevicesControllerOfflineConfig_ConnectionTimeout_Description" xml:space="preserve">
    <value>Doba čekání na připojení k SQL serveru</value>
  </data>
  <data name="DevicesControllerOfflineConfig_CommandTimeout_DisplayName" xml:space="preserve">
    <value>SQL příkaz timeout [s]</value>
  </data>
  <data name="DevicesControllerOfflineConfig_CommandTimeout_Description" xml:space="preserve">
    <value>Doba čekání na dokončení SQL commandu</value>
  </data>
  <data name="DevicesControllerOfflineConfig_OfflineDataRefreshTimeout_DisplayName" xml:space="preserve">
    <value>Interval přenačítání offline dat [s]</value>
  </data>
  <data name="DevicesControllerOfflineConfig_OfflineDataRefreshTimeout_Description" xml:space="preserve">
    <value>Ovlivní aktuálnost offline dat. Nicměné příliš nízká hodnota může zatěžovat databázi. Načítají se všíchni klienti, jídelníček a všechny objednávky.</value>
  </data>
  <data name="DevicesControllerOfflineConfig_OnlineCheckTimeout_DisplayName" xml:space="preserve">
    <value>Interval kontroly přechodu zpět do online [s]</value>
  </data>
  <data name="DevicesControllerOfflineConfig_OnlineCheckTimeout_Description" xml:space="preserve">
    <value>Ovlivní rychlost přechodu z offline zpět do online.</value>
  </data>
  <data name="PresPointBehaviourMenuConfig_HideDisabledMenuItems_DisplayName" xml:space="preserve">
    <value>Skrýt nedostupné jídla</value>
  </data>
  <data name="PresPointBehaviourMenuConfig_HideDisabledMenuItems_Description" xml:space="preserve">
    <value>Klientovi se nebudou vůbec zobrazovat nedostupná jídla.</value>
  </data>
  <data name="PresPointBehaviourMenuConfig_HideDisabledCanteens_DisplayName" xml:space="preserve">
    <value>Skrýt nedostupné výdejny</value>
  </data>
  <data name="PresPointBehaviourMenuConfig_HideDisabledCanteens_Description" xml:space="preserve">
    <value>Klientovi se nebudou vůbec zobrazovat nedostupné výdejny.</value>
  </data>
  <data name="CsobPaymentTermSettings_Port_DisplayName" xml:space="preserve">
    <value>Port</value>
  </data>
  <data name="CsobPaymentTermSettings_Port_Description" xml:space="preserve">
    <value>Číslo portu, implicitní je 11417</value>
  </data>
  <data name="CsobPaymentTermSettings_Address_DisplayName" xml:space="preserve">
    <value>IP adresa</value>
  </data>
  <data name="CsobPaymentTermSettings_Address_Description" xml:space="preserve">
    <value>IP adresa nebo název, který jí odpovídá.

Nastavení terminálu ČSOB Verifone VX 520 (asi bude fungovat pouze v testovacím módu. V ostrém módu mají právo nastavovat pouze zaměstnanci ČSOB): 

Počkat, až terminál nastartuje - až bude na displeji logo ČSOB. 
V menu se pohybuje pomocí dvou fialových kláves pod displejem vlevo a vpravo od klávesy Alpha.
Jako Esc funguje červené tlačítko Cancel X.

* Na terminálu stisknout klávesu ALPHA
* Vybrat aplikaci M+Manager, Enter
* Vybrat Technik pomocí 2 a Enter
* Zobrazí se "Input password", odklepnout pouze Enter prázdné heslo
* Změna local IP: Network – Internet/SSL – Ethernet - IP address. Zde zadat IP adresu terminálu, masku a GW
* Nastavení IP Kasy : Cash register – Network – TCP-Server – IPAddress 1 – změníte IP na požadovanou adresu stanice s Kasou.
* Pak je třeba terminál restartovat - vypojit ze zásuvky a znovu zapojit.</value>
  </data>
  <data name="MobileOrderingBehaviorMenuConfig_ShowMessCount_DisplayName" xml:space="preserve">
    <value>Zobrazovat počet porcí</value>
  </data>
  <data name="MobileOrderingBehaviorMenuConfig_ShowMessCount_Description" xml:space="preserve">
    <value>Zobrazení počtu porcí v řádku menu.</value>
  </data>
  <data name="PresPointBehaviourCanteenConfig_DefaultCanteen_DisplayName" xml:space="preserve">
    <value>Implicitní výdejna</value>
  </data>
  <data name="PresPointBehaviourCanteenConfig_DefaultCanteen_Description" xml:space="preserve">
    <value>Jakým způsobem se učtí implicitní výdejna.</value>
  </data>
  <data name="GlobalServicesHelpDeskServicesConfig_ServerAddress_DisplayName" xml:space="preserve">
    <value>Adresa služeb serveru</value>
  </data>
  <data name="GlobalServicesHelpDeskServicesConfig_ServerAddress_Description" xml:space="preserve">
    <value>Adresa serveru, na kterém běží služby HelpDesku. Default je https://api-services-helpdesk.anete.com</value>
  </data>
  <data name="GlobalRulesGdprPersonalDataAgreementConfig_PersonalDataExpiration_DisplayName" xml:space="preserve">
    <value>Uchovávat data po skončení platnosti EL [měsíců]</value>
  </data>
  <data name="GlobalRulesGdprPersonalDataAgreementConfig_PersonalDataExpiration_Description" xml:space="preserve">
    <value>Počet měsíců představuje dobu, za kterou po ukončení platnosti evidenčního listu dojde anonymizaci klienta při uzávěrce.
Pokud je počet měsíců 0, pak anonymizaci udělá nejbližší uzávěrka.</value>
  </data>
  <data name="SchedulerPluginsStockMovementSenderConfig_ExportType_DisplayName" xml:space="preserve">
    <value>Typ exportu</value>
  </data>
  <data name="SchedulerPluginsStockMovementSenderConfig_ExportType_Description" xml:space="preserve">
    <value>Nastavení typu exportu</value>
  </data>
  <data name="OfficeWorkplacesReportsImpersonationCahConfig_Impersonate_DisplayName" xml:space="preserve">
    <value>Impersonace</value>
  </data>
  <data name="OfficeWorkplacesReportsImpersonationCahConfig_Impersonate_Description" xml:space="preserve">
    <value>Impersonace</value>
  </data>
  <data name="OfficeWorkplacesReportsImpersonationCahConfig_User_DisplayName" xml:space="preserve">
    <value>Uživatel</value>
  </data>
  <data name="OfficeWorkplacesReportsImpersonationCahConfig_User_Description" xml:space="preserve">
    <value>Uživatel</value>
  </data>
  <data name="OfficeWorkplacesReportsImpersonationCahConfig_Domain_DisplayName" xml:space="preserve">
    <value>Doména</value>
  </data>
  <data name="OfficeWorkplacesReportsImpersonationCahConfig_Domain_Description" xml:space="preserve">
    <value>Doména</value>
  </data>
  <data name="OfficeWorkplacesReportsImpersonationCahConfig_Password_DisplayName" xml:space="preserve">
    <value>Heslo</value>
  </data>
  <data name="OfficeWorkplacesReportsImpersonationCahConfig_Password_Description" xml:space="preserve">
    <value>Heslo</value>
  </data>
  <data name="OfficeWorkplacesReportsImpersonationCahConfig_PathToFolder_DisplayName" xml:space="preserve">
    <value>Cesta k uložení souboru</value>
  </data>
  <data name="OfficeWorkplacesReportsImpersonationCahConfig_PathToFolder_Description" xml:space="preserve">
    <value>Cesta k uložení souboru</value>
  </data>
  <data name="OfficeWorkplacesReportsImpersonationCahConfig_FileName_DisplayName" xml:space="preserve">
    <value>Název výstupního souboru</value>
  </data>
  <data name="OfficeWorkplacesReportsImpersonationCahConfig_FileName_Description" xml:space="preserve">
    <value>Název výstupního souboru i s příponou.</value>
  </data>
  <data name="DevicesControllerBehaviourAppInstallationsConfig_AppInstallations_DisplayName" xml:space="preserve">
    <value>Obsluhovaná zařízení</value>
  </data>
  <data name="DevicesControllerBehaviourAppInstallationsConfig_AppInstallations_Description" xml:space="preserve">
    <value>Seznam obsluhovaných zařízení. Původně se nastavoval přímo Konfigurátorem čteček a ukládal se do config souboru.</value>
  </data>
  <data name="OfficeBehaviourBalanceConfig_EnableRealPricesCheck_DisplayName" xml:space="preserve">
    <value>Kontrolovat vyplnění skutečných cen</value>
  </data>
  <data name="OfficeBehaviourBalanceConfig_EnableRealPricesCheck_Description" xml:space="preserve">
    <value>Pokud je povoleno, kontroluje se před spuštěním uzávěrky vyplnění skutečných cen u všech jídel.</value>
  </data>
  <data name="SchedulerPluginsAutoUpdaterConfig_UpdateBatchGeneratorTriggerConfig_DisplayName" xml:space="preserve">
    <value>Nastavení časovače</value>
  </data>
  <data name="SchedulerPluginsAutoUpdaterConfig_UpdateBatchGeneratorTriggerConfig_Description" xml:space="preserve">
    <value>Nastavení parametrů časovače</value>
  </data>
  <data name="SchedulerPluginsAutoUpdaterConfig_UpdateBatchGeneratorFailedJobConfig_DisplayName" xml:space="preserve">
    <value>Opakování při chybě</value>
  </data>
  <data name="SchedulerPluginsAutoUpdaterConfig_UpdateBatchGeneratorFailedJobConfig_Description" xml:space="preserve">
    <value>Nastavení toho, zda se při chybě v úloze má opakovat spuštění</value>
  </data>
  <data name="DevicesControllerServicesToolsConfig_Folder_DisplayName" xml:space="preserve">
    <value>Složka s nástroji</value>
  </data>
  <data name="DevicesControllerServicesToolsConfig_Folder_Description" xml:space="preserve">
    <value>Složka, v které jsou nástroje umístěny. Očekávají se v ní podlosložky FileZillaPortable, PuTTYPortable, TightVNCViewerPortable a WinSCP.</value>
  </data>
  <data name="EetMiscSettings_SendBankCard_DisplayName" xml:space="preserve">
    <value>Odesílat platby bankovní kartou</value>
  </data>
  <data name="EetMiscSettings_SendBankCard_Description" xml:space="preserve">
    <value>Pokud je povoleno, budou se do EET posílat platby bankovní kartou (není povinné).
Pokud je zakázáno, pak se platby bankovní kartou posílat nebudou.</value>
  </data>
  <data name="GlobalBehaviorEetConfig_MiscSettings_DisplayName" xml:space="preserve">
    <value>Nastavení - další</value>
  </data>
  <data name="GlobalBehaviorEetConfig_MiscSettings_Description" xml:space="preserve">
    <value>Další nastavení pro odesílání EET</value>
  </data>
  <data name="CashDeskSaleConfig_EnableFastBankCardPayment_DisplayName" xml:space="preserve">
    <value>Povolit rychlou úhradu bankovní kartou</value>
  </data>
  <data name="CashDeskSaleConfig_EnableFastBankCardPayment_Description" xml:space="preserve">
    <value>Pokud je povoleno, pak je na prodejní obrazovce zobrazeno tlačítko pro rychlou úhradu bankovní kartou.</value>
  </data>
  <data name="GuestOrderingBehaviourParagonConfig_OfficeAppInstallationId_DisplayName" xml:space="preserve">
    <value>Id zařízení Kanceláře</value>
  </data>
  <data name="GuestOrderingBehaviourParagonConfig_OfficeAppInstallationId_Description" xml:space="preserve">
    <value>Toto zařízení se použije při vytváření paragonu.</value>
  </data>
  <data name="GuestOrderingBehaviourParagonConfig_CurrencyTypeId_DisplayName" xml:space="preserve">
    <value>Id druhu platidla </value>
  </data>
  <data name="GuestOrderingBehaviourParagonConfig_CurrencyTypeId_Description" xml:space="preserve">
    <value>Tento druh platidla se použije při platbě stravenkou.</value>
  </data>
  <data name="GlobalBehaviourSalesSlipPrintConfig_PrintSalesSlipClientName_DisplayName" xml:space="preserve">
    <value>Tisk jména klienta na paragon</value>
  </data>
  <data name="GlobalBehaviourSalesSlipPrintConfig_PrintSalesSlipClientName_Description" xml:space="preserve">
    <value>Pokud hodnota je True, bude na paragonu zobrazen řádek se jménem klienta. V opačné případě nikoliv.</value>
  </data>
  <data name="CashDeskBehaviourServiceDisplayConfig_ShowClientNameOnServiceDisplay_DisplayName" xml:space="preserve">
    <value>Zobrazit jméno klienta na displeji obsluhy</value>
  </data>
  <data name="CashDeskBehaviourServiceDisplayConfig_ShowClientNameOnServiceDisplay_Description" xml:space="preserve">
    <value>Zobrazí jméno přihlášeného klienta na displeji obsluhy. V případě že hodnota bude nastavena na False, tak bude zobrazen text: "Známý klient" po přihlášení.</value>
  </data>
  <data name="GlobalBehaviorEetConfig_AlgorithmType_DisplayName" xml:space="preserve">
    <value>Typ algoritmu</value>
  </data>
  <data name="GlobalBehaviorEetConfig_AlgorithmType_Description" xml:space="preserve">
    <value>Nastavení algoritmu, podle kterého se počítají data odeslaná do EET</value>
  </data>
  <data name="PresPointBehaviourMenuConfig_HideForeignCanteens_DisplayName" xml:space="preserve">
    <value>Skrýt cizý výdejny</value>
  </data>
  <data name="PresPointBehaviourMenuConfig_HideForeignCanteens_Description" xml:space="preserve">
    <value>Pokud je povoleno, bude mít přihlášený uživatel dostupnou pouze výdejnu, která přísluší danému PM. Nemůže si tedy objednat na žádnou jinou výdejnu.</value>
  </data>
  <data name="PdfAttendanceSettings_FileName_DisplayName" xml:space="preserve">
    <value>Cesta k souboru</value>
  </data>
  <data name="PdfAttendanceSettings_FileName_Description" xml:space="preserve">
    <value>Definuluje celou cestu souboru včetně symbolických proměnných. Dostupné proměnné:
{period} - období ve formátu rrrrmm - 201806
{personalId} - osobní číslo

Přiklad natavení: \xolma103\BetaVykazy{period}\DV_{period(_{personalId}.pdf</value>
  </data>
  <data name="GlobalRulesBalanceConfig_SalaryDrawbacksAnet_DisplayName" xml:space="preserve">
    <value>Srážky pro ANeT</value>
  </data>
  <data name="GlobalRulesBalanceConfig_SalaryDrawbacksAnet_Description" xml:space="preserve">
    <value>Zpřístupňuje export srážek ze mzdy pro ANeT</value>
  </data>
  <data name="OfficeWorkplacesBehaviourImportNisConfig_ImportPath_DisplayName" xml:space="preserve">
    <value>Cesta k souborům pro import</value>
  </data>
  <data name="OfficeWorkplacesBehaviourImportNisConfig_ImportPath_Description" xml:space="preserve">
    <value>Cesta k souborům pro import</value>
  </data>
  <data name="GlobalBehaviourMealTicketConfig_ShowName_DisplayName" xml:space="preserve">
    <value>Zobrazit jméno</value>
  </data>
  <data name="GlobalBehaviourMealTicketConfig_ShowName_Description" xml:space="preserve">
    <value>Pokud je povoleno, na stravence se vytiskne nový řádek s jménem strávníka.</value>
  </data>
  <data name="GlobalBehaviourMealTicketConfig_ShowOcs_DisplayName" xml:space="preserve">
    <value>Zobrazit osobní číslo</value>
  </data>
  <data name="GlobalBehaviourMealTicketConfig_ShowOcs_Description" xml:space="preserve">
    <value>Pokud je povoleno, na stravence se vytiskne nový řádek s osobním číslem strávníka.</value>
  </data>
  <data name="GlobalBehaviourMealTicketConfig_ShowId_DisplayName" xml:space="preserve">
    <value>Zobrazit ID</value>
  </data>
  <data name="GlobalBehaviourMealTicketConfig_ShowId_Description" xml:space="preserve">
    <value>Pokud je povolene, na stravence se vytistkne nový řádek s ID strávníka.</value>
  </data>
  <data name="SchedulerPluginsHelpDeskSyncConfig_MsiTriggerConfig_DisplayName" xml:space="preserve">
    <value>Synchronizace Msi balíčů: Nastavení časovače</value>
  </data>
  <data name="SchedulerPluginsHelpDeskSyncConfig_MsiTriggerConfig_Description" xml:space="preserve">
    <value>Nastavení parametrů časovače</value>
  </data>
  <data name="SchedulerPluginsHelpDeskSyncConfig_MsiFailedJobConfig_DisplayName" xml:space="preserve">
    <value>Synchronizace Msi balíčků: Opakování při chybě</value>
  </data>
  <data name="SchedulerPluginsHelpDeskSyncConfig_MsiFailedJobConfig_Description" xml:space="preserve">
    <value>Nastavení toho, zda se při chybě v úloze má opakovat spuštění</value>
  </data>
  <data name="CsobPaymentTermSettings_KeepConnection_DisplayName" xml:space="preserve">
    <value>Udržovat spojení</value>
  </data>
  <data name="CsobPaymentTermSettings_KeepConnection_Description" xml:space="preserve">
    <value>Bude po dobu připojení terminálu udržováné spojení? Pokud je vypnuto, obsluha se při startu nedozví, že terminál nekomunikuje. Povolení by naopak mohlo zamezit výpadkům terminálů.</value>
  </data>
  <data name="GlobalServicesPaymentGateConfig_PaymentGateType_DisplayName" xml:space="preserve">
    <value>Typ platební brány</value>
  </data>
  <data name="GlobalServicesPaymentGateConfig_PaymentGateType_Description" xml:space="preserve">
    <value>Typ platební brány</value>
  </data>
  <data name="GlobalServicesPaymentGateConfig_Settings_DisplayName" xml:space="preserve">
    <value>Nastavení platební brány</value>
  </data>
  <data name="GlobalServicesPaymentGateConfig_Settings_Description" xml:space="preserve">
    <value>Nastavení platební brány</value>
  </data>
  <data name="GoPayPaymentGateSettings_APIUrl_DisplayName" xml:space="preserve">
    <value>API URL</value>
  </data>
  <data name="GoPayPaymentGateSettings_APIUrl_Description" xml:space="preserve">
    <value>Odkaz na GoPay API</value>
  </data>
  <data name="GoPayPaymentGateSettings_ClientID_DisplayName" xml:space="preserve">
    <value>Client ID</value>
  </data>
  <data name="GoPayPaymentGateSettings_ClientID_Description" xml:space="preserve">
    <value>Client ID</value>
  </data>
  <data name="GoPayPaymentGateSettings_ClientSecret_DisplayName" xml:space="preserve">
    <value>Client Secret</value>
  </data>
  <data name="GoPayPaymentGateSettings_ClientSecret_Description" xml:space="preserve">
    <value>Client Secret</value>
  </data>
  <data name="GoPayPaymentGateSettings_GoID_DisplayName" xml:space="preserve">
    <value>Go ID</value>
  </data>
  <data name="GoPayPaymentGateSettings_GoID_Description" xml:space="preserve">
    <value>Go ID</value>
  </data>
  <data name="OfficeWorkplacesBehaviourImportNisConfig_Domain_Description" xml:space="preserve">
    <value>Doména</value>
  </data>
  <data name="OfficeWorkplacesBehaviourImportNisConfig_Domain_DisplayName" xml:space="preserve">
    <value>Doména</value>
  </data>
  <data name="OfficeWorkplacesBehaviourImportNisConfig_Impersonate_Description" xml:space="preserve">
    <value>Impersonace</value>
  </data>
  <data name="OfficeWorkplacesBehaviourImportNisConfig_Impersonate_DisplayName" xml:space="preserve">
    <value>Impersonace</value>
  </data>
  <data name="OfficeWorkplacesBehaviourImportNisConfig_Password_Description" xml:space="preserve">
    <value>Heslo</value>
  </data>
  <data name="OfficeWorkplacesBehaviourImportNisConfig_Password_DisplayName" xml:space="preserve">
    <value>Heslo</value>
  </data>
  <data name="OfficeWorkplacesBehaviourImportNisConfig_User_Description" xml:space="preserve">
    <value>Uživatel</value>
  </data>
  <data name="OfficeWorkplacesBehaviourImportNisConfig_User_DisplayName" xml:space="preserve">
    <value>Uživatel</value>
  </data>
  <data name="LabelAppearance_TextColor_DisplayName" xml:space="preserve">
    <value>Barva textu</value>
  </data>
  <data name="LabelAppearance_TextColor_Description" xml:space="preserve">
    <value>Barva textu</value>
  </data>
  <data name="LabelAppearance_BackgroundColor_DisplayName" xml:space="preserve">
    <value>Barva pozadí</value>
  </data>
  <data name="LabelAppearance_BackgroundColor_Description" xml:space="preserve">
    <value>Barva pozadí</value>
  </data>
  <data name="GlobalServicesReportServiceConnectionConfig_ReportServiceAddress_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="GlobalServicesReportServiceConnectionConfig_ReportServiceAddress_Description" xml:space="preserve">
    <value />
  </data>
  <data name="GlobalServicesReportServiceConnectionConfig_BindingTypeSettings_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="GlobalServicesReportServiceConnectionConfig_BindingTypeSettings_Description" xml:space="preserve">
    <value />
  </data>
  <data name="AccommodationBehaviourSystemConfig_StartAccommodationHour_DisplayName" xml:space="preserve">
    <value>Hodina začátku ubytování/rezervace</value>
  </data>
  <data name="AccommodationBehaviourSystemConfig_StartAccommodationHour_Description" xml:space="preserve">
    <value>V kolik hodin začání ubytování/rezervace</value>
  </data>
  <data name="AccommodationBehaviourSystemConfig_EndAccommodationHour_DisplayName" xml:space="preserve">
    <value>Hodina konce ubytování/rezervace</value>
  </data>
  <data name="AccommodationBehaviourSystemConfig_EndAccommodationHour_Description" xml:space="preserve">
    <value>V kolik hodin končí ubytování/rezervace</value>
  </data>
  <data name="AccommodationBehaviourSystemConfig_EndAccommodationDays_DisplayName" xml:space="preserve">
    <value>Počet dnů do datumu do</value>
  </data>
  <data name="AccommodationBehaviourSystemConfig_EndAccommodationDays_Description" xml:space="preserve">
    <value>Přidá zvolený počet dnů do datumu do, defaultní rozšíření intervalu.</value>
  </data>
  <data name="AccommodationAppearanceLabelColorsConfig_RoomLockoutAppearance_DisplayName" xml:space="preserve">
    <value>Nastavení vzhledu výluk</value>
  </data>
  <data name="AccommodationAppearanceLabelColorsConfig_RoomLockoutAppearance_Description" xml:space="preserve">
    <value>Nastavení vzhledu výluk</value>
  </data>
  <data name="AccommodationAppearanceLabelColorsConfig_ReservationNewAppearance_DisplayName" xml:space="preserve">
    <value>Nastavení vzhledu nové rezervace</value>
  </data>
  <data name="AccommodationAppearanceLabelColorsConfig_ReservationNewAppearance_Description" xml:space="preserve">
    <value>Nastavení vzhledu nové rezervace</value>
  </data>
  <data name="AccommodationAppearanceLabelColorsConfig_ReservationInProgressAppearance_DisplayName" xml:space="preserve">
    <value>Nastavení vzhledu rozpracované rezervace</value>
  </data>
  <data name="AccommodationAppearanceLabelColorsConfig_ReservationInProgressAppearance_Description" xml:space="preserve">
    <value>Nastavení vzhledu rozpracované rezervace</value>
  </data>
  <data name="AccommodationAppearanceLabelColorsConfig_ReservationClosedAppearance_DisplayName" xml:space="preserve">
    <value>Nastavení vzhledu uzavřené rezervace</value>
  </data>
  <data name="AccommodationAppearanceLabelColorsConfig_ReservationClosedAppearance_Description" xml:space="preserve">
    <value>Nastavení vzhledu uzavřené rezervace</value>
  </data>
  <data name="WebKreditAccountEBankingConfig_Enabled_DisplayName" xml:space="preserve">
    <value>Zobrazit</value>
  </data>
  <data name="WebKreditAccountEBankingConfig_Enabled_Description" xml:space="preserve">
    <value>Zobrazit záložku EBanking</value>
  </data>
  <data name="WebKreditAccountEBankingConfig_EnableAccountNumberChange_DisplayName" xml:space="preserve">
    <value>Změna číslo účtu</value>
  </data>
  <data name="WebKreditAccountEBankingConfig_EnableAccountNumberChange_Description" xml:space="preserve">
    <value>Povolit změnu čísla účtu</value>
  </data>
  <data name="WebKreditAccountEBankingConfig_EnableSpecSymbolChange_DisplayName" xml:space="preserve">
    <value>Změna speciálního symbolu</value>
  </data>
  <data name="WebKreditAccountEBankingConfig_EnableSpecSymbolChange_Description" xml:space="preserve">
    <value>Povolit změnu speciálního symbolu</value>
  </data>
  <data name="WebKreditAccountEBankingConfig_EnableCollectionMinAmountChange_DisplayName" xml:space="preserve">
    <value>Změna minimální částky inkasa</value>
  </data>
  <data name="WebKreditAccountEBankingConfig_EnableCollectionMinAmountChange_Description" xml:space="preserve">
    <value>Povolit změnu minimální částky inkasa</value>
  </data>
  <data name="WebKreditAccountEBankingConfig_EnableCollectionEqualizeToChange_DisplayName" xml:space="preserve">
    <value>Změna hladiny dorovnání</value>
  </data>
  <data name="WebKreditAccountEBankingConfig_EnableCollectionEqualizeToChange_Description" xml:space="preserve">
    <value>Povolit změnu hladiny dorovnání</value>
  </data>
  <data name="WebKreditAccountEBankingConfig_EnableCollectionTypeChange_DisplayName" xml:space="preserve">
    <value>Změna způsobu inkasa</value>
  </data>
  <data name="WebKreditAccountEBankingConfig_EnableCollectionTypeChange_Description" xml:space="preserve">
    <value>Povolit změnu způsobu inkasa</value>
  </data>
  <data name="WebKreditAccountSettingsConfig_Enabled_DisplayName" xml:space="preserve">
    <value>Zobrazit</value>
  </data>
  <data name="WebKreditAccountSettingsConfig_Enabled_Description" xml:space="preserve">
    <value>Zobrazit záložku Nastavení</value>
  </data>
  <data name="WebKreditAccountSettingsConfig_CanChangeEmail_DisplayName" xml:space="preserve">
    <value>Změna emailu</value>
  </data>
  <data name="WebKreditAccountSettingsConfig_CanChangeEmail_Description" xml:space="preserve">
    <value>Povolit změnu emailu</value>
  </data>
  <data name="WebKreditAccountSettingsConfig_CanChangeSupplementsLimit_DisplayName" xml:space="preserve">
    <value>Změna limitu na sortiment</value>
  </data>
  <data name="WebKreditAccountSettingsConfig_CanChangeSupplementsLimit_Description" xml:space="preserve">
    <value>Povolit změnu limitu na sortiment</value>
  </data>
  <data name="WebKreditAccountSettingsConfig_CanChangeDefaultCanteen_DisplayName" xml:space="preserve">
    <value>Změna výchozí výdejna</value>
  </data>
  <data name="WebKreditAccountSettingsConfig_CanChangeDefaultCanteen_Description" xml:space="preserve">
    <value>Povolit změnu výchozí výdejny</value>
  </data>
  <data name="WebKreditAccountSettingsConfig_EnableUserManagement_DisplayName" xml:space="preserve">
    <value>Správa uživatelů</value>
  </data>
  <data name="WebKreditAccountSettingsConfig_EnableUserManagement_Description" xml:space="preserve">
    <value>Povolit správu uživatelů</value>
  </data>
  <data name="WebKreditAccountSettingsConfig_CanBlockUsers_DisplayName" xml:space="preserve">
    <value>Blokování uživatelů</value>
  </data>
  <data name="WebKreditAccountSettingsConfig_CanBlockUsers_Description" xml:space="preserve">
    <value>Povolit blokování uživatelů</value>
  </data>
  <data name="WebKreditAccountSettingsConfig_CanChangePassword_DisplayName" xml:space="preserve">
    <value>Změna hesla</value>
  </data>
  <data name="WebKreditAccountSettingsConfig_CanChangePassword_Description" xml:space="preserve">
    <value>Povolit změnu hesla</value>
  </data>
  <data name="WebKreditAccountSettingsConfig_MinimumPasswordLength_DisplayName" xml:space="preserve">
    <value>Minimální délka hesla</value>
  </data>
  <data name="WebKreditAccountSettingsConfig_MinimumPasswordLength_Description" xml:space="preserve">
    <value>Minimální délka hesla</value>
  </data>
  <data name="WebKreditAccountGdprConfig_Enabled_DisplayName" xml:space="preserve">
    <value>Zobrazit</value>
  </data>
  <data name="WebKreditAccountGdprConfig_Enabled_Description" xml:space="preserve">
    <value>Zobrazit záložku Gdpr</value>
  </data>
  <data name="WebKreditBannersConfig_Menu_DisplayName" xml:space="preserve">
    <value>Jídelníček</value>
  </data>
  <data name="WebKreditBannersConfig_Menu_Description" xml:space="preserve">
    <value>Cesta k reklamě pod kalendářem u modulu Objednávání</value>
  </data>
  <data name="WebKreditBannersConfig_MenuFbs_DisplayName" xml:space="preserve">
    <value>F-Regiony</value>
  </data>
  <data name="WebKreditBannersConfig_MenuFbs_Description" xml:space="preserve">
    <value>Cesta k logu F-Regiony</value>
  </data>
  <data name="WebKreditBannersConfig_MenuFbsUrl_DisplayName" xml:space="preserve">
    <value>F-Regiony odkaz</value>
  </data>
  <data name="WebKreditBannersConfig_MenuFbsUrl_Description" xml:space="preserve">
    <value>Cesta odkazu loga F-Regiony</value>
  </data>
  <data name="WebKreditBannersConfig_History_DisplayName" xml:space="preserve">
    <value>Historie účtu</value>
  </data>
  <data name="WebKreditBannersConfig_History_Description" xml:space="preserve">
    <value>Cesta k reklamě pod kalendářem u modulu Historie účtu</value>
  </data>
  <data name="WebKreditBannersConfig_Logo_DisplayName" xml:space="preserve">
    <value>Logo</value>
  </data>
  <data name="WebKreditBannersConfig_Logo_Description" xml:space="preserve">
    <value>Cesta k logu pro tiskové sestavy </value>
  </data>
  <data name="WebKreditFeedbackConfig_EnableBookOfWishesAndComplaints_DisplayName" xml:space="preserve">
    <value>Kniha přání a stížností</value>
  </data>
  <data name="WebKreditFeedbackConfig_EnableBookOfWishesAndComplaints_Description" xml:space="preserve">
    <value>Zobrazit záložku Kniha přání a stížností</value>
  </data>
  <data name="WebKreditFeedbackConfig_EnableCampaigns_DisplayName" xml:space="preserve">
    <value>Ankety</value>
  </data>
  <data name="WebKreditFeedbackConfig_EnableCampaigns_Description" xml:space="preserve">
    <value>Zobrazit záložku Ankety</value>
  </data>
  <data name="WebKreditHeaderConfig_EnableDualPrices_DisplayName" xml:space="preserve">
    <value>Dualní cena</value>
  </data>
  <data name="WebKreditHeaderConfig_EnableDualPrices_Description" xml:space="preserve">
    <value>Zobrazit v hlavičce stránky duální cenu zústatku</value>
  </data>
  <data name="WebKreditHeaderConfig_EnableBony_DisplayName" xml:space="preserve">
    <value>Bony</value>
  </data>
  <data name="WebKreditHeaderConfig_EnableBony_Description" xml:space="preserve">
    <value>Zobrazit v hlavičce stránky počet bonů</value>
  </data>
  <data name="WebKreditHeaderConfig_EnableSubsidisedMeals_DisplayName" xml:space="preserve">
    <value>Dotovaná jídla</value>
  </data>
  <data name="WebKreditHeaderConfig_EnableSubsidisedMeals_Description" xml:space="preserve">
    <value>Zobrazit v hlavičce stránky počet dotovaných jídel</value>
  </data>
  <data name="WebKreditHistoryConfig_EnableHistory_DisplayName" xml:space="preserve">
    <value>Historie účtu</value>
  </data>
  <data name="WebKreditHistoryConfig_EnableHistory_Description" xml:space="preserve">
    <value>Zobrazit záložku Historie účtu</value>
  </data>
  <data name="WebKreditHistoryConfig_EnableReceipts_DisplayName" xml:space="preserve">
    <value>Účtenky</value>
  </data>
  <data name="WebKreditHistoryConfig_EnableReceipts_Description" xml:space="preserve">
    <value>Zobrazit záložku Účtenky</value>
  </data>
  <data name="WebKreditHistoryConfig_EnableSpendingAndSubsidy_DisplayName" xml:space="preserve">
    <value>Útraty a příspěvky</value>
  </data>
  <data name="WebKreditHistoryConfig_EnableSpendingAndSubsidy_Description" xml:space="preserve">
    <value>Zobrazit záložku Útraty a příspěvky</value>
  </data>
  <data name="WebKreditHistoryConfig_EnableNutritionalValues_DisplayName" xml:space="preserve">
    <value>Nutriční hodnoty</value>
  </data>
  <data name="WebKreditHistoryConfig_EnableNutritionalValues_Description" xml:space="preserve">
    <value>Zobrazit záložku Nutriční hodnoty</value>
  </data>
  <data name="WebKreditHistoryConfig_EnableSubsidyOverview_DisplayName" xml:space="preserve">
    <value>Přehled dotací</value>
  </data>
  <data name="WebKreditHistoryConfig_EnableSubsidyOverview_Description" xml:space="preserve">
    <value>Zobrazit záložku Přehled dotací</value>
  </data>
  <data name="WebKreditHistoryConfig_EnableTaxReport_DisplayName" xml:space="preserve">
    <value>Daňový doklad</value>
  </data>
  <data name="WebKreditHistoryConfig_EnableTaxReport_Description" xml:space="preserve">
    <value>Zobrazit záložku Daňový doklad</value>
  </data>
  <data name="WebKreditHistoryConfig_EnableAttendance_DisplayName" xml:space="preserve">
    <value>Docházka</value>
  </data>
  <data name="WebKreditHistoryConfig_EnableAttendance_Description" xml:space="preserve">
    <value>Zobrazit záložku Docházka</value>
  </data>
  <data name="WebKreditHistoryConfig_ShowHistoryDetail_DisplayName" xml:space="preserve">
    <value>Historie účtu - detail</value>
  </data>
  <data name="WebKreditHistoryConfig_ShowHistoryDetail_Description" xml:space="preserve">
    <value>Zobrazit detail v záložce Histore účtu</value>
  </data>
  <data name="WebKreditHistoryConfig_ShowHistoryDetailOpened_DisplayName" xml:space="preserve">
    <value>Historie účtu - otevřít detail</value>
  </data>
  <data name="WebKreditHistoryConfig_ShowHistoryDetailOpened_Description" xml:space="preserve">
    <value>Výchozí stav otevření detailů v záložce Historie účtu</value>
  </data>
  <data name="WebKreditHistoryConfig_AttendanceConnectionString_DisplayName" xml:space="preserve">
    <value>Docházka - connection string</value>
  </data>
  <data name="WebKreditHistoryConfig_AttendanceConnectionString_Description" xml:space="preserve">
    <value>Connection string pro přípojení k databázi docházky</value>
  </data>
  <data name="WebKreditInformationsConfig_EnableHelp_DisplayName" xml:space="preserve">
    <value>Nápověda</value>
  </data>
  <data name="WebKreditInformationsConfig_EnableHelp_Description" xml:space="preserve">
    <value>Zobrazit záložku Nápověda</value>
  </data>
  <data name="WebKreditInformationsConfig_EnableAllergens_DisplayName" xml:space="preserve">
    <value>Alergeny</value>
  </data>
  <data name="WebKreditInformationsConfig_EnableAllergens_Description" xml:space="preserve">
    <value>Zobrazit záložku Alergeny</value>
  </data>
  <data name="WebKreditInformationsConfig_EnableCustom_DisplayName" xml:space="preserve">
    <value>Vlastní</value>
  </data>
  <data name="WebKreditInformationsConfig_EnableCustom_Description" xml:space="preserve">
    <value>Zobrazit vlastní konfigurovatelnou záložku</value>
  </data>
  <data name="WebKreditInformationsConfig_EnableAppInfo_DisplayName" xml:space="preserve">
    <value>O aplikaci</value>
  </data>
  <data name="WebKreditInformationsConfig_EnableAppInfo_Description" xml:space="preserve">
    <value>Zobrazit záložku O aplikace</value>
  </data>
  <data name="WebKreditInformationsConfig_EnableOwnerInfo_DisplayName" xml:space="preserve">
    <value>O provozovateli</value>
  </data>
  <data name="WebKreditInformationsConfig_EnableOwnerInfo_Description" xml:space="preserve">
    <value>Zobrazit záložku O provozovateli</value>
  </data>
  <data name="WebKreditInformationsConfig_CustomTitle_DisplayName" xml:space="preserve">
    <value>Vlastní - název</value>
  </data>
  <data name="WebKreditInformationsConfig_CustomTitle_Description" xml:space="preserve">
    <value>Název vlastní záložky</value>
  </data>
  <data name="WebKreditInformationsConfig_CustomFile_DisplayName" xml:space="preserve">
    <value>Vlastní - cesta</value>
  </data>
  <data name="WebKreditInformationsConfig_CustomFile_Description" xml:space="preserve">
    <value>Název html souboru s obsahem vlastní záložky.
Soubor musí být umístěn v adresáři "wwwroot" ve složce s WebKreditem.
(nejčastěji "C:\inetpub\wwwroot\WebKredit2\wwwroot")</value>
  </data>
  <data name="GlobalServicesIpsConfig_DisplayName_DisplayName" xml:space="preserve">
    <value>Název služby</value>
  </data>
  <data name="GlobalServicesIpsConfig_DisplayName_Description" xml:space="preserve">
    <value>Název služby - pro MU "SUPO", pro JČU "IPS"</value>
  </data>
  <data name="GlobalServicesIpsConfig_ShowHistory_DisplayName" xml:space="preserve">
    <value>Zobrazit historii</value>
  </data>
  <data name="GlobalServicesIpsConfig_ShowHistory_Description" xml:space="preserve">
    <value>Zobrazit historii transakcí</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_ShowAlternative_DisplayName" xml:space="preserve">
    <value>Alternativa</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_ShowAlternative_Description" xml:space="preserve">
    <value>Zobrazit sloupec Alternativa</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_ShowPictograms_DisplayName" xml:space="preserve">
    <value>Piktogramy</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_ShowPictograms_Description" xml:space="preserve">
    <value>Zobrazit sloupec s piktogramy</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_ShowAmount_DisplayName" xml:space="preserve">
    <value>Množství</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_ShowAmount_Description" xml:space="preserve">
    <value>Zobrazit sloupec Množství</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_ShowName_DisplayName" xml:space="preserve">
    <value>Název</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_ShowName_Description" xml:space="preserve">
    <value>Zobrazit sloupec Název</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_ShowDetail_DisplayName" xml:space="preserve">
    <value>Dodavatelé surovin</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_ShowDetail_Description" xml:space="preserve">
    <value>Zobrazit sloupec s odkazem na seznam dodavatelů surovin (z FBS)</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_ShowOrdering_DisplayName" xml:space="preserve">
    <value>Objednávání</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_ShowOrdering_Description" xml:space="preserve">
    <value>Zobrazit možnost objednat jídlo</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_ShowAvailable_DisplayName" xml:space="preserve">
    <value>Počet zbývajících porcí</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_ShowAvailable_Description" xml:space="preserve">
    <value>Zobrazit sloupec s počtem zbývajících porcí</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_ShowOrdered_DisplayName" xml:space="preserve">
    <value>Počet objednaných porcí</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_ShowOrdered_Description" xml:space="preserve">
    <value>Zobrazit sloupec s počtem již objednaných porcí</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_ShowPrice_DisplayName" xml:space="preserve">
    <value>Cena</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_ShowPrice_Description" xml:space="preserve">
    <value>Zobrazit sloupec s cenou</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_ShowPrice2_DisplayName" xml:space="preserve">
    <value>Cena dotovaná</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_ShowPrice2_Description" xml:space="preserve">
    <value>Zobrazit sloupec s dotovanou cenou</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_ShowDualPrice_DisplayName" xml:space="preserve">
    <value>Cena duální</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_ShowDualPrice_Description" xml:space="preserve">
    <value>Zobrazit sloupec s cenou v druhé měně</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_ShowDualPrice2_DisplayName" xml:space="preserve">
    <value>Cena duální dotovaná</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_ShowDualPrice2_Description" xml:space="preserve">
    <value>Zobrazit sloupec s dotovanou cenou v druhé měně</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_ShowImage_DisplayName" xml:space="preserve">
    <value>Obrázek</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_ShowImage_Description" xml:space="preserve">
    <value>Zobrazit sloupec s odkazem na obrázek jídla</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_ShowInExchange_DisplayName" xml:space="preserve">
    <value>Burza</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_ShowInExchange_Description" xml:space="preserve">
    <value>Zobrazit sloupec s ikonou indikující jestli je možné objednat jídlo z burzy</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_ShowNote_DisplayName" xml:space="preserve">
    <value>Poznámka</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_ShowNote_Description" xml:space="preserve">
    <value>Zobrazit poznámku</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_ShowAllergens_DisplayName" xml:space="preserve">
    <value>Alergeny</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_ShowAllergens_Description" xml:space="preserve">
    <value>Zobrazit sloupec Alergeny</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_ShowNutritionalValues_DisplayName" xml:space="preserve">
    <value>Nutriční hodnoty</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_ShowNutritionalValues_Description" xml:space="preserve">
    <value>Zobrazit nutriční hodnoty</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_ShowSoldoutCrossedOut_DisplayName" xml:space="preserve">
    <value>Přeškrtnout vyprodaná jídla</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_ShowSoldoutCrossedOut_Description" xml:space="preserve">
    <value>Zobrazí vyprodaná jídla přeškrtnutě</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_ShowOrderingTextBox_DisplayName" xml:space="preserve">
    <value>Objednávání - textbox</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_ShowOrderingTextBox_Description" xml:space="preserve">
    <value>Zobrazit možnost objednat jídlo jako textbox</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_PictogramSize_DisplayName" xml:space="preserve">
    <value>Piktogramy - velikost</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_PictogramSize_Description" xml:space="preserve">
    <value>Velikost piktogramů v pixelech</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_FilterHidden_DisplayName" xml:space="preserve">
    <value>Filtr - bez Zobrazit</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_FilterHidden_Description" xml:space="preserve">
    <value>Filtrovat řádky, které nemají zaškrtnuté "Zobrazit"</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_FilterWithInAdvance_DisplayName" xml:space="preserve">
    <value>Filtr - s Dopředu</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_FilterWithInAdvance_Description" xml:space="preserve">
    <value>Filtrovat řádky, které mají zaškrtnuté "Dopředu"</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_FilterWithoutInAdvance_DisplayName" xml:space="preserve">
    <value>Filtr - bez Dopředu</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_FilterWithoutInAdvance_Description" xml:space="preserve">
    <value>Filtrovat řádky, které nemají zaškrtnuté "Dopředu"</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_FilterCanteenNotAllowed_DisplayName" xml:space="preserve">
    <value>Filtr - nelze objednat</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_FilterCanteenNotAllowed_Description" xml:space="preserve">
    <value>Filtrovat řádky, které mají stav "Nelze objednat do této výdejny"</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_FilterClientNotAllowed_DisplayName" xml:space="preserve">
    <value>Filtr - není povoleno</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_FilterClientNotAllowed_Description" xml:space="preserve">
    <value>Filtrovat řádky, které mají stav "Nemáte povoleno objednat toto jídlo"</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_FilterSharedMeal_DisplayName" xml:space="preserve">
    <value>Filtr - společná jídla</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_FilterSharedMeal_Description" xml:space="preserve">
    <value>Filtrovat řádky, které mají stav "Společné jídlo"</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_FilterSoldout_DisplayName" xml:space="preserve">
    <value>Filtr - vyprodáno</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_FilterSoldout_Description" xml:space="preserve">
    <value>Filtrovat řádky, které mají stav "Vyčerpán počet vařených/vydávaných porcí"</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_PriceItemId_DisplayName" xml:space="preserve">
    <value>Cena - id položky</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_PriceItemId_Description" xml:space="preserve">
    <value>Id poločky sloupce Cena</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_Price2ItemId_DisplayName" xml:space="preserve">
    <value>Cena dotovaná - id položky</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_Price2ItemId_Description" xml:space="preserve">
    <value>Id položky sloupce Cena dotovaná</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_PriceSubsidyId_DisplayName" xml:space="preserve">
    <value>Cena - kategorie dotace</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_PriceSubsidyId_Description" xml:space="preserve">
    <value>Kategorie dotace sloupce Cena</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_Price2SubsidyId_DisplayName" xml:space="preserve">
    <value>Cena dotovaná - kategorie dotace</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_Price2SubsidyId_Description" xml:space="preserve">
    <value>Kategorie dotace sloupce Cena dotovaná</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_ShowPricesFromTime_DisplayName" xml:space="preserve">
    <value>Cena - čas zobrazení</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_ShowPricesFromTime_Description" xml:space="preserve">
    <value>Zobrazit ceny až od určitého času</value>
  </data>
  <data name="WebKreditOrderingOrdersConfig_Enabled_DisplayName" xml:space="preserve">
    <value>Povolit</value>
  </data>
  <data name="WebKreditOrderingOrdersConfig_Enabled_Description" xml:space="preserve">
    <value>Povolit záložku s objednávkama</value>
  </data>
  <data name="WebKreditOrderingOrdersConfig_EnableChanges_DisplayName" xml:space="preserve">
    <value>Povolit změny</value>
  </data>
  <data name="WebKreditOrderingOrdersConfig_EnableChanges_Description" xml:space="preserve">
    <value>Povolit změny objednávek</value>
  </data>
  <data name="WebKreditOrderingOrdersConfig_ShowDate_DisplayName" xml:space="preserve">
    <value>Datum</value>
  </data>
  <data name="WebKreditOrderingOrdersConfig_ShowDate_Description" xml:space="preserve">
    <value>Zobrazit sloupec Datum</value>
  </data>
  <data name="WebKreditOrderingOrdersConfig_ShowMealKind_DisplayName" xml:space="preserve">
    <value>Druh jídla</value>
  </data>
  <data name="WebKreditOrderingOrdersConfig_ShowMealKind_Description" xml:space="preserve">
    <value>Zobrazit sloupec Druh jídla</value>
  </data>
  <data name="WebKreditOrderingOrdersConfig_ShowAlternative_DisplayName" xml:space="preserve">
    <value>Alternativa</value>
  </data>
  <data name="WebKreditOrderingOrdersConfig_ShowAlternative_Description" xml:space="preserve">
    <value>Zobrazit sloupec Alternativa</value>
  </data>
  <data name="WebKreditOrderingOrdersConfig_ShowName_DisplayName" xml:space="preserve">
    <value>Název</value>
  </data>
  <data name="WebKreditOrderingOrdersConfig_ShowName_Description" xml:space="preserve">
    <value>Zobrazit sloupec Název</value>
  </data>
  <data name="WebKreditOrderingOrdersConfig_ShowCanteen_DisplayName" xml:space="preserve">
    <value>Výdejna</value>
  </data>
  <data name="WebKreditOrderingOrdersConfig_ShowCanteen_Description" xml:space="preserve">
    <value>Zobrazit sloupce Výdejna</value>
  </data>
  <data name="WebKreditOrderingOrdersConfig_ShowCancel_DisplayName" xml:space="preserve">
    <value>Stornování</value>
  </data>
  <data name="WebKreditOrderingOrdersConfig_ShowCancel_Description" xml:space="preserve">
    <value>Zobrazit sloupec Stornovat</value>
  </data>
  <data name="WebKreditOrderingOrdersConfig_ShowExchange_DisplayName" xml:space="preserve">
    <value>Burza</value>
  </data>
  <data name="WebKreditOrderingOrdersConfig_ShowExchange_Description" xml:space="preserve">
    <value>Zobrazit jestli je objednávka na burze</value>
  </data>
  <data name="WebKreditOrderingOrdersConfig_ShowPrice_DisplayName" xml:space="preserve">
    <value>Cena</value>
  </data>
  <data name="WebKreditOrderingOrdersConfig_ShowPrice_Description" xml:space="preserve">
    <value>Zobrazit sloupec s cenou</value>
  </data>
  <data name="WebKreditOrderingOrdersConfig_ShowDualPrice_DisplayName" xml:space="preserve">
    <value>Cena duální</value>
  </data>
  <data name="WebKreditOrderingOrdersConfig_ShowDualPrice_Description" xml:space="preserve">
    <value>Zobrazit sloupec s cenou v druhé měně</value>
  </data>
  <data name="WebKreditOrderingOrdersConfig_ShowSummary_DisplayName" xml:space="preserve">
    <value>Součet cen</value>
  </data>
  <data name="WebKreditOrderingOrdersConfig_ShowSummary_Description" xml:space="preserve">
    <value>Zobrazit součet cen objednávek</value>
  </data>
  <data name="WebKreditOrderingOrdersConfig_ShowSummaryDual_DisplayName" xml:space="preserve">
    <value>Součet cen duální</value>
  </data>
  <data name="WebKreditOrderingOrdersConfig_ShowSummaryDual_Description" xml:space="preserve">
    <value>Zobrazit součet cen objednávek v duální měně</value>
  </data>
  <data name="WebKreditOrderingOrdersConfig_ShowGroupedEnabled_DisplayName" xml:space="preserve">
    <value>Seskupení objednávek</value>
  </data>
  <data name="WebKreditOrderingOrdersConfig_ShowGroupedEnabled_Description" xml:space="preserve">
    <value>Zobrazit tlačítko Seskupit objednávky</value>
  </data>
  <data name="WebKreditOrderingOrdersConfig_ShowGroupedByDefault_DisplayName" xml:space="preserve">
    <value>Seskupení objednávek - výchozí</value>
  </data>
  <data name="WebKreditOrderingOrdersConfig_ShowGroupedByDefault_Description" xml:space="preserve">
    <value>Výchozá nastavení pro tlačítko Seskupit objednávky</value>
  </data>
  <data name="WebKreditOrderingOrdersConfig_ShowAllByDefault_DisplayName" xml:space="preserve">
    <value>Zobrazit všechny objednávky - výchozí</value>
  </data>
  <data name="WebKreditOrderingOrdersConfig_ShowAllByDefault_Description" xml:space="preserve">
    <value>Výchozí nastavení pro tlačítko Zobrazit všechny objednávky</value>
  </data>
  <data name="WebKreditOrderingExchangesConfig_Enabled_DisplayName" xml:space="preserve">
    <value>Povolit</value>
  </data>
  <data name="WebKreditOrderingExchangesConfig_Enabled_Description" xml:space="preserve">
    <value>Povolit burzu</value>
  </data>
  <data name="WebKreditOrderingExchangesConfig_ShowDate_DisplayName" xml:space="preserve">
    <value>Datum</value>
  </data>
  <data name="WebKreditOrderingExchangesConfig_ShowDate_Description" xml:space="preserve">
    <value>Zobrazit sloupec Datum</value>
  </data>
  <data name="WebKreditOrderingExchangesConfig_ShowMealKind_DisplayName" xml:space="preserve">
    <value>Druh jídla</value>
  </data>
  <data name="WebKreditOrderingExchangesConfig_ShowMealKind_Description" xml:space="preserve">
    <value>Zobrazit sloupec Druh jídla</value>
  </data>
  <data name="WebKreditOrderingExchangesConfig_ShowAlternative_DisplayName" xml:space="preserve">
    <value>Alternativa</value>
  </data>
  <data name="WebKreditOrderingExchangesConfig_ShowAlternative_Description" xml:space="preserve">
    <value>Zobrazit sloupec Alternativa</value>
  </data>
  <data name="WebKreditOrderingExchangesConfig_ShowName_DisplayName" xml:space="preserve">
    <value>Název</value>
  </data>
  <data name="WebKreditOrderingExchangesConfig_ShowName_Description" xml:space="preserve">
    <value>Zobrazit sloupec Název</value>
  </data>
  <data name="WebKreditOrderingExchangesConfig_ShowCanteen_DisplayName" xml:space="preserve">
    <value>Výdejna</value>
  </data>
  <data name="WebKreditOrderingExchangesConfig_ShowCanteen_Description" xml:space="preserve">
    <value>Zobrazit sloupce Výdejna</value>
  </data>
  <data name="WebKreditOrderingExchangesConfig_ShowOrder_DisplayName" xml:space="preserve">
    <value>Objednávání</value>
  </data>
  <data name="WebKreditOrderingExchangesConfig_ShowOrder_Description" xml:space="preserve">
    <value>Zobrazit sloupec Objednat</value>
  </data>
  <data name="WebKreditOrderingExchangesConfig_ShowPrice_DisplayName" xml:space="preserve">
    <value>Cena</value>
  </data>
  <data name="WebKreditOrderingExchangesConfig_ShowPrice_Description" xml:space="preserve">
    <value>Zobrazit sloupec s cenou</value>
  </data>
  <data name="WebKreditOrderingExchangesConfig_ShowPrice2_DisplayName" xml:space="preserve">
    <value>Cena dotovaná</value>
  </data>
  <data name="WebKreditOrderingExchangesConfig_ShowPrice2_Description" xml:space="preserve">
    <value>Zobrazit sloupec s dotovanou cenou</value>
  </data>
  <data name="WebKreditOrderingExchangesConfig_ShowDualPrice_DisplayName" xml:space="preserve">
    <value>Cena duální</value>
  </data>
  <data name="WebKreditOrderingExchangesConfig_ShowDualPrice_Description" xml:space="preserve">
    <value>Zobrazit sloupec s cenou v druhé měně</value>
  </data>
  <data name="WebKreditOrderingExchangesConfig_ShowDualPrice2_DisplayName" xml:space="preserve">
    <value>Cena duální dotovaní</value>
  </data>
  <data name="WebKreditOrderingExchangesConfig_ShowDualPrice2_Description" xml:space="preserve">
    <value>Zobrazit sloupec s dotovanou cenou v druhé měně</value>
  </data>
  <data name="WebKreditOrderingConfig_ShowOrdersInCalendar_DisplayName" xml:space="preserve">
    <value>Zvýraznit objednávky v kalendáři</value>
  </data>
  <data name="WebKreditOrderingConfig_ShowOrdersInCalendar_Description" xml:space="preserve">
    <value>Zvýraznit v kalendář dny, na které již existují objednávky</value>
  </data>
  <data name="WebKreditOrderingConfig_DaysInAdvance_DisplayName" xml:space="preserve">
    <value>Počet dní</value>
  </data>
  <data name="WebKreditOrderingConfig_DaysInAdvance_Description" xml:space="preserve">
    <value>Výchozí počet dní vybraných k zobrazení</value>
  </data>
  <data name="WebKreditOrderingConfig_MessageTypeAnonymous_DisplayName" xml:space="preserve">
    <value>Aktualita - před přihlášením</value>
  </data>
  <data name="WebKreditOrderingConfig_MessageTypeAnonymous_Description" xml:space="preserve">
    <value>Zobrazení aktuality před přihlášením</value>
  </data>
  <data name="WebKreditOrderingConfig_MessageTypeAuthenticated_DisplayName" xml:space="preserve">
    <value>Aktualita - po přihlášení</value>
  </data>
  <data name="WebKreditOrderingConfig_MessageTypeAuthenticated_Description" xml:space="preserve">
    <value>Zobrazení aktuality po přihlášení</value>
  </data>
  <data name="WebKreditLocalizationConfig_EnableApplication_DisplayName" xml:space="preserve">
    <value>Povolit lokalizaci aplikace</value>
  </data>
  <data name="WebKreditLocalizationConfig_EnableApplication_Description" xml:space="preserve">
    <value>Povolit lokalizaci aplikace</value>
  </data>
  <data name="WebKreditLocalizationConfig_EnableMenu_DisplayName" xml:space="preserve">
    <value>Povolit lokalizaci jídelníčku</value>
  </data>
  <data name="WebKreditLocalizationConfig_EnableMenu_Description" xml:space="preserve">
    <value>Povolit lokalizaci jídelníčku</value>
  </data>
  <data name="WebKreditFeedbackConfig_EnableMessages_DisplayName" xml:space="preserve">
    <value>Zprávy</value>
  </data>
  <data name="WebKreditFeedbackConfig_EnableMessages_Description" xml:space="preserve">
    <value>Zobrazit záložku Zprávy</value>
  </data>
  <data name="WebKreditFeedbackConfig_EnableSuggestions_DisplayName" xml:space="preserve">
    <value>Připomínky</value>
  </data>
  <data name="WebKreditFeedbackConfig_EnableSuggestions_Description" xml:space="preserve">
    <value>Zobrazit záložku Přípomínky</value>
  </data>
  <data name="WebKreditFeedbackConfig_SuggestionsEmail_DisplayName" xml:space="preserve">
    <value>Přípomínky - email</value>
  </data>
  <data name="WebKreditFeedbackConfig_SuggestionsEmail_Description" xml:space="preserve">
    <value>Email příjemce pro připomínky</value>
  </data>
  <data name="GoPayPaymentGateSettings_NotificationsUrl_DisplayName" xml:space="preserve">
    <value>URL notifikací</value>
  </data>
  <data name="GoPayPaymentGateSettings_NotificationsUrl_Description" xml:space="preserve">
    <value>URL pro zpracovávání notifikací o změně stavu platby.
Pro starý WebKredit - https://x.y.z/webkredit/Payments/Notification
Pro nový WebKredit - https://x.y.z/webkredit/Api/Account/PaymentsNotification</value>
  </data>
  <data name="AccommodationAppearanceLabelColorsConfig_AccommodationNewAppearance_DisplayName" xml:space="preserve">
    <value>Nastavení vzhledu nového ubytování</value>
  </data>
  <data name="AccommodationAppearanceLabelColorsConfig_AccommodationNewAppearance_Description" xml:space="preserve">
    <value>Nastavení vzhledu nového ubytování</value>
  </data>
  <data name="AccommodationAppearanceLabelColorsConfig_AccommodationClosedAppearance_DisplayName" xml:space="preserve">
    <value>Nastavení vzhledu ukončeného ubytování</value>
  </data>
  <data name="AccommodationAppearanceLabelColorsConfig_AccommodationClosedAppearance_Description" xml:space="preserve">
    <value>Nastavení vzhledu ukončeného ubytování</value>
  </data>
  <data name="AccommodationBehaviourAutoOrderingConfig_AutoOrderingType_DisplayName" xml:space="preserve">
    <value>Způsob vytváření objednávek</value>
  </data>
  <data name="AccommodationBehaviourAutoOrderingConfig_AutoOrderingType_Description" xml:space="preserve">
    <value>Nastavení způsobu vytváření objednávek</value>
  </data>
  <data name="AccommodationBehaviourAutoOrderingConfig_Settings_DisplayName" xml:space="preserve">
    <value>Nastavení</value>
  </data>
  <data name="AccommodationBehaviourAutoOrderingConfig_Settings_Description" xml:space="preserve">
    <value>Detailní nastavení vytváření objednávek</value>
  </data>
  <data name="WebKreditHeaderConfig_BackButtonUrl_DisplayName" xml:space="preserve">
    <value>Přesměrovávací tlačítko - url</value>
  </data>
  <data name="WebKreditHeaderConfig_BackButtonUrl_Description" xml:space="preserve">
    <value>Url tlačítka pro přeměrování (např. na zpět na hlavní stránky zákazníka)</value>
  </data>
  <data name="WebKreditHeaderConfig_BackButtonText_DisplayName" xml:space="preserve">
    <value>Přesměrovávací tlačítko - text</value>
  </data>
  <data name="WebKreditHeaderConfig_BackButtonText_Description" xml:space="preserve">
    <value>Text tlačítka pro přesměrování (např. na zpět na hlavní stránky zákazníka)</value>
  </data>
  <data name="CasAuthenticationSettings_Server_DisplayName" xml:space="preserve">
    <value>URL serveru</value>
  </data>
  <data name="CasAuthenticationSettings_Server_Description" xml:space="preserve">
    <value>Adresa autentifikačního serveru</value>
  </data>
  <data name="CasAuthenticationSettings_ShowRedirectButton_DisplayName" xml:space="preserve">
    <value>Zobrazit přesměrovávací tlačítko</value>
  </data>
  <data name="CasAuthenticationSettings_ShowRedirectButton_Description" xml:space="preserve">
    <value>Zobrazí v hlavičce stránky tlačítko pro přesměrování na adresu autentifikačního serveru</value>
  </data>
  <data name="LdapAuthenticationSettings_ServerConfig_DisplayName" xml:space="preserve">
    <value>LDAP server</value>
  </data>
  <data name="LdapAuthenticationSettings_ServerConfig_Description" xml:space="preserve">
    <value>Nastavení parametrů LDAP serveru</value>
  </data>
  <data name="LdapAuthenticationSettings_ServiceUserCredentials_DisplayName" xml:space="preserve">
    <value>Přihlášení servisního uživatele</value>
  </data>
  <data name="LdapAuthenticationSettings_ServiceUserCredentials_Description" xml:space="preserve">
    <value>V případě vyplnění probíhá první pokus o přihlášení a vyhledávání v LDAP stromu pod tímto uživatelem.
V případě nevyplnění probíhá pokus o přihlášení přímo pod jménem/heslem, které zadal uživatel (viz. Přihlášení uživatele).</value>
  </data>
  <data name="LdapAuthenticationSettings_SearchRequestConfig_DisplayName" xml:space="preserve">
    <value>Vyhledávání ve stromu</value>
  </data>
  <data name="LdapAuthenticationSettings_SearchRequestConfig_Description" xml:space="preserve">
    <value>Nastavení parametrů pro vyhledávání v LDAP stromu</value>
  </data>
  <data name="ShibbolethAuthenticationSettings_UserHeader_DisplayName" xml:space="preserve">
    <value>Hlavička s uživatelským jménem</value>
  </data>
  <data name="ShibbolethAuthenticationSettings_UserHeader_Description" xml:space="preserve">
    <value>Hlavička ve které shibboleth předává uživatelské jméno</value>
  </data>
  <data name="ShibbolethAuthenticationSettings_AutoLogin_DisplayName" xml:space="preserve">
    <value>Automatické přihlášení/odhlášení</value>
  </data>
  <data name="ShibbolethAuthenticationSettings_AutoLogin_Description" xml:space="preserve">
    <value>Automatické přihlašení k WebKreditu pokud se uživatel přihlásil k portálu shibbolethu i jinak než přes stránky WebKreditu.
Stejně tak automatické odhlášení pokud se uživatel odhlásil od portálu shibbolethu jinak než pomocí WebKreditu.</value>
  </data>
  <data name="UrlAuthenticationSettings_TimeTolerance_DisplayName" xml:space="preserve">
    <value>Tolerance (s)</value>
  </data>
  <data name="UrlAuthenticationSettings_TimeTolerance_Description" xml:space="preserve">
    <value>Tolerance (v sekundách) při porovnávání času v poslaných přihlašovacích údajích s aktuálním časem na serveru</value>
  </data>
  <data name="UrlAuthenticationSettings_Password_DisplayName" xml:space="preserve">
    <value>Heslo</value>
  </data>
  <data name="UrlAuthenticationSettings_Password_Description" xml:space="preserve">
    <value>Heslo použité pro šifrování přihlašovacích údajů</value>
  </data>
  <data name="WebKreditAuthenticationConfig_InactivityTimeout_DisplayName" xml:space="preserve">
    <value>Doba expirace přihlášení při nečinnosti (m)</value>
  </data>
  <data name="WebKreditAuthenticationConfig_InactivityTimeout_Description" xml:space="preserve">
    <value>Maximální doba nečinnosti (v minutách) po které bude uživatel odhlášen</value>
  </data>
  <data name="WebKreditAuthenticationConfig_LogoutRedirectUrl_DisplayName" xml:space="preserve">
    <value>Url pro přesměrování po odhlášení</value>
  </data>
  <data name="WebKreditAuthenticationConfig_LogoutRedirectUrl_Description" xml:space="preserve">
    <value>Adresa na kterou bude uživatel přesměrován po odhlášení</value>
  </data>
  <data name="WebKreditAuthenticationConfig_AuthenticationType_DisplayName" xml:space="preserve">
    <value>Autentifikace</value>
  </data>
  <data name="WebKreditAuthenticationConfig_AuthenticationType_Description" xml:space="preserve">
    <value>Typ autentifikace</value>
  </data>
  <data name="WebKreditAuthenticationConfig_Settings_DisplayName" xml:space="preserve">
    <value>Autentifikace - nastavení</value>
  </data>
  <data name="WebKreditAuthenticationConfig_Settings_Description" xml:space="preserve">
    <value>Nastavení autentifikace</value>
  </data>
  <data name="LdapUserConfig_UserNameFormat_DisplayName" xml:space="preserve">
    <value>Formát uživatelského jména - LDAP</value>
  </data>
  <data name="LdapUserConfig_UserNameFormat_Description" xml:space="preserve">
    <value>Formátovací řetězec pro uživatelské jméno pod kterým proběhne pokus o přihlášení k LDAP serveru.
Formátovací parametry (case-sensitive):
{UserName} = uživatelské jméno zadané klientem
{UserDN} = DN uživatele vyhledaného přes nastavený vyhledávací dotaz nastavený

V případě, že není zadán servisní uživatel, tak pokus o přihlášení probíhá ještě před vyhledáváním, takže {UserDN}={UserName} (tzn. je jedno které se použije).</value>
  </data>
  <data name="LdapServerConfig_EnableKerberosEncryption_DisplayName" xml:space="preserve">
    <value>Kerberos - povolit šifrování</value>
  </data>
  <data name="LdapServerConfig_EnableKerberosEncryption_Description" xml:space="preserve">
    <value>Povolit šifrování u autentifikace typu Kerberos (Signing a Sealing)</value>
  </data>
  <data name="WebKreditAuthenticationConfig_BadLoginRedirectUrl_DisplayName" xml:space="preserve">
    <value>Url pro přesměrování při neúspěšném přihlášení</value>
  </data>
  <data name="WebKreditAuthenticationConfig_BadLoginRedirectUrl_Description" xml:space="preserve">
    <value>Url pro přesměrování při neúspěšném přihlášení</value>
  </data>
  <data name="LdapAuthenticationSettings_UserConfig_DisplayName" xml:space="preserve">
    <value>Přihlášení uživatele</value>
  </data>
  <data name="LdapAuthenticationSettings_UserConfig_Description" xml:space="preserve">
    <value>Nastavení parametrů při přihlašování uživatele</value>
  </data>
  <data name="LdapUserConfig_Domain_DisplayName" xml:space="preserve">
    <value>Doména</value>
  </data>
  <data name="LdapUserConfig_Domain_Description" xml:space="preserve">
    <value>Doména, do které se přihlašuje uživatel.</value>
  </data>
  <data name="GlobalBehaviourSalesSlipPrintConfig_PrintSalesSlipCashierName_DisplayName" xml:space="preserve">
    <value>Tisknout jméno pokladní</value>
  </data>
  <data name="GlobalBehaviourSalesSlipPrintConfig_PrintSalesSlipCashierName_Description" xml:space="preserve">
    <value>Tisknout jméno pokladní</value>
  </data>
  <data name="GlobalBehaviourSalesSlipPrintConfig_PrintSalesSlipClientCardNumber_DisplayName" xml:space="preserve">
    <value>Tisknout číslo karty</value>
  </data>
  <data name="GlobalBehaviourSalesSlipPrintConfig_PrintSalesSlipClientCardNumber_Description" xml:space="preserve">
    <value>Tisknout číslo karty</value>
  </data>
  <data name="GlobalBehaviourSalesSlipPrintConfig_PrintSalesSlipClientPersonalId_DisplayName" xml:space="preserve">
    <value>Tisknout osobní číslo</value>
  </data>
  <data name="GlobalBehaviourSalesSlipPrintConfig_PrintSalesSlipClientPersonalId_Description" xml:space="preserve">
    <value>Tisknout osobní číslo</value>
  </data>
  <data name="OfficeWorkplacesBehaviourMenuConfig_EditNutritionalValues_DisplayName" xml:space="preserve">
    <value>Editovat nutriční hodnoty</value>
  </data>
  <data name="OfficeWorkplacesBehaviourMenuConfig_EditNutritionalValues_Description" xml:space="preserve">
    <value>Povolit editaci nutričních hodnot? Podmínkou je, aby měl zákazník licenci na modul nutriční hodnoty.</value>
  </data>
  <data name="GlobalHwPaymentTermConfig_AllowOfflineMode_DisplayName" xml:space="preserve">
    <value>Povolit offline mód</value>
  </data>
  <data name="GlobalHwPaymentTermConfig_AllowOfflineMode_Description" xml:space="preserve">
    <value>Umožnit provádět platby i v případě, že terminál není připojen/nekomunikuje?
Pokud ano, obsluha bude vyzvána, aby provedla platbu manuálně a potvrdila ji pak tlačítkem.</value>
  </data>
  <data name="DietaryOrderingSettings_AllowMassOrdering_DisplayName" xml:space="preserve">
    <value>Objednávání na oddělení</value>
  </data>
  <data name="DietaryOrderingSettings_AllowMassOrdering_Description" xml:space="preserve">
    <value>Povolit objednávání na oddělení</value>
  </data>
  <data name="DietaryOrderingSettings_AllowSpecifiedOrdering_DisplayName" xml:space="preserve">
    <value>Objednávání na pacienty</value>
  </data>
  <data name="DietaryOrderingSettings_AllowSpecifiedOrdering_Description" xml:space="preserve">
    <value>Povolit objednávání na pacienty</value>
  </data>
  <data name="DietaryOrderingSettings_AllowIndividualOrdering_DisplayName" xml:space="preserve">
    <value>Individuální objednávání</value>
  </data>
  <data name="DietaryOrderingSettings_AllowIndividualOrdering_Description" xml:space="preserve">
    <value>Povolit individuální objednávání</value>
  </data>
  <data name="DietaryOrderingSettings_IndividualOrderingAlt_DisplayName" xml:space="preserve">
    <value>Individuální objednávání - alternativa</value>
  </data>
  <data name="DietaryOrderingSettings_IndividualOrderingAlt_Description" xml:space="preserve">
    <value>Id alternativy individuální diety</value>
  </data>
  <data name="PatientOrderingBehaviourGeneralConfig_OrderingSettings_DisplayName" xml:space="preserve">
    <value>Typ objednávání - nastavení</value>
  </data>
  <data name="PatientOrderingBehaviourGeneralConfig_OrderingSettings_Description" xml:space="preserve">
    <value>Detailní nastavení konkrétního způsobu objednávání</value>
  </data>
  <data name="PatientOrderingBehaviourGeneralConfig_OrderingType_DisplayName" xml:space="preserve">
    <value>Typ objednávání</value>
  </data>
  <data name="PatientOrderingBehaviourGeneralConfig_OrderingType_Description" xml:space="preserve">
    <value>Nastavení způsobu objednávání</value>
  </data>
  <data name="BulkOrderingSettings_AllowOrderCopy_DisplayName" xml:space="preserve">
    <value>Povolit kopírování objednávek</value>
  </data>
  <data name="BulkOrderingSettings_AllowOrderCopy_Description" xml:space="preserve">
    <value>Povolit kopírování objednávek</value>
  </data>
  <data name="PresPointBehaviourMenuConfig_FilterByMealKind_DisplayName" xml:space="preserve">
    <value>Filtrovat dle druhů jídel?</value>
  </data>
  <data name="PresPointBehaviourMenuConfig_FilterByMealKind_Description" xml:space="preserve">
    <value>Pokud je povoleno, je v PM zobrazeno další tlačítko pro výběr druhu jídla. Hodí se typicky pro zákazníky, kteří pracují na směny - každý druh jídla ve skutečnosti obsahuje stejná jídla a směnou se volí pouze čas.</value>
  </data>
  <data name="GPWebPayPaymentGateSettings_APIUrl_DisplayName" xml:space="preserve">
    <value>API URL</value>
  </data>
  <data name="GPWebPayPaymentGateSettings_APIUrl_Description" xml:space="preserve">
    <value>Odkaz na GP webpay API</value>
  </data>
  <data name="GPWebPayPaymentGateSettings_ClientID_DisplayName" xml:space="preserve">
    <value>Client ID</value>
  </data>
  <data name="GPWebPayPaymentGateSettings_ClientID_Description" xml:space="preserve">
    <value>Client ID</value>
  </data>
  <data name="GPWebPayPaymentGateSettings_CertificatePrivatePath_DisplayName" xml:space="preserve">
    <value>Soukromý klíč</value>
  </data>
  <data name="GPWebPayPaymentGateSettings_CertificatePrivatePath_Description" xml:space="preserve">
    <value>Kompletní cesta k soukromému klíči. Používá se pro podpis zpráv při odesílání platby</value>
  </data>
  <data name="GPWebPayPaymentGateSettings_CertificatePublicPath_DisplayName" xml:space="preserve">
    <value>Veřejný klíč</value>
  </data>
  <data name="GPWebPayPaymentGateSettings_CertificatePublicPath_Description" xml:space="preserve">
    <value>Kompletní cesta k veřejnému klíči. Používá se pro ověření zpráv při přijímání odpovědí.</value>
  </data>
  <data name="GPWebPayPaymentGateSettings_CertificatePrivatePasword_DisplayName" xml:space="preserve">
    <value>Soukromý klíč - heslo</value>
  </data>
  <data name="GPWebPayPaymentGateSettings_CertificatePrivatePasword_Description" xml:space="preserve">
    <value>Heslo k soukromému klíči</value>
  </data>
  <data name="GlobalBehaviorEKasaConfig_EKasaMode_DisplayName" xml:space="preserve">
    <value>Typ eKasy</value>
  </data>
  <data name="GlobalBehaviorEKasaConfig_EKasaMode_Description" xml:space="preserve">
    <value>Typ eKasy</value>
  </data>
  <data name="GlobalBehaviorEKasaConfig_Settings_DisplayName" xml:space="preserve">
    <value>Nastavení</value>
  </data>
  <data name="GlobalBehaviorEKasaConfig_Settings_Description" xml:space="preserve">
    <value>Nastavení daného typu eKasy</value>
  </data>
  <data name="GraphicAccommodationCapacityAppearanceColorsConfig_NotHotelRoomAppearance_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="GraphicAccommodationCapacityAppearanceColorsConfig_NotHotelRoomAppearance_Description" xml:space="preserve">
    <value />
  </data>
  <data name="GraphicAccommodationCapacityAppearanceColorsConfig_HotelRoomAppearance_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="GraphicAccommodationCapacityAppearanceColorsConfig_HotelRoomAppearance_Description" xml:space="preserve">
    <value />
  </data>
  <data name="GraphicAccommodationCapacityAppearanceColorsConfig_LevelHeaderAppearance_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="GraphicAccommodationCapacityAppearanceColorsConfig_LevelHeaderAppearance_Description" xml:space="preserve">
    <value />
  </data>
  <data name="GraphicAccommodationCapacityAppearanceColorsConfig_LevelAppearance_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="GraphicAccommodationCapacityAppearanceColorsConfig_LevelAppearance_Description" xml:space="preserve">
    <value />
  </data>
  <data name="GraphicAccommodationCapacityAppearanceColorsConfig_BuildingHeaderAppearance_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="GraphicAccommodationCapacityAppearanceColorsConfig_BuildingHeaderAppearance_Description" xml:space="preserve">
    <value />
  </data>
  <data name="GraphicAccommodationCapacityAppearanceColorsConfig_BuildingAppearance_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="GraphicAccommodationCapacityAppearanceColorsConfig_BuildingAppearance_Description" xml:space="preserve">
    <value />
  </data>
  <data name="GraphicAccommodationCapacityAppearanceColorsConfig_FreeBedAppearance_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="GraphicAccommodationCapacityAppearanceColorsConfig_FreeBedAppearance_Description" xml:space="preserve">
    <value />
  </data>
  <data name="GraphicAccommodationCapacityAppearanceColorsConfig_ReservedBedAppearance_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="GraphicAccommodationCapacityAppearanceColorsConfig_ReservedBedAppearance_Description" xml:space="preserve">
    <value />
  </data>
  <data name="GraphicAccommodationCapacityAppearanceColorsConfig_ManBedAppearance_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="GraphicAccommodationCapacityAppearanceColorsConfig_ManBedAppearance_Description" xml:space="preserve">
    <value />
  </data>
  <data name="GraphicAccommodationCapacityAppearanceColorsConfig_WomanBedAppearance_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="GraphicAccommodationCapacityAppearanceColorsConfig_WomanBedAppearance_Description" xml:space="preserve">
    <value />
  </data>
  <data name="GraphicAccommodationCapacityAppearanceColorsConfig_WithoutSexTypeBedAppearance_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="GraphicAccommodationCapacityAppearanceColorsConfig_WithoutSexTypeBedAppearance_Description" xml:space="preserve">
    <value />
  </data>
  <data name="GraphicAccommodationCapacityAppearanceColorsConfig_RoomLockoutBedAppearance_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="GraphicAccommodationCapacityAppearanceColorsConfig_RoomLockoutBedAppearance_Description" xml:space="preserve">
    <value />
  </data>
  <data name="GraphicAccommodationCapacityAppearanceColorsConfig_RoomBlockedBedAppearance_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="GraphicAccommodationCapacityAppearanceColorsConfig_RoomBlockedBedAppearance_Description" xml:space="preserve">
    <value />
  </data>
  <data name="WebKreditOrderingConfig_EnableRecalculation_DisplayName" xml:space="preserve">
    <value>Povolit rekalkulaci cen</value>
  </data>
  <data name="WebKreditOrderingConfig_EnableRecalculation_Description" xml:space="preserve">
    <value>Povolí tlačítko pro rekalkulaci cen objednaných jídel v jídelníčku</value>
  </data>
  <data name="EKasaChduSettingsBase_ComPortName_DisplayName" xml:space="preserve">
    <value>COM port</value>
  </data>
  <data name="EKasaChduSettingsBase_ComPortName_Description" xml:space="preserve">
    <value>COM pork, na kterém je připojeno CHDU</value>
  </data>
  <data name="EKasaChduSettingsBase_PrinterBaudRate_DisplayName" xml:space="preserve">
    <value>Rychlost portu tiskárny</value>
  </data>
  <data name="EKasaChduSettingsBase_PrinterBaudRate_Description" xml:space="preserve">
    <value>Tiskárna je připojena na COM port CHDU. Musí být nastaveno dle dokumentace k tiskárně.</value>
  </data>
  <data name="EKasaChduSettingsBase_EKasaChduKind_DisplayName" xml:space="preserve">
    <value>Typ</value>
  </data>
  <data name="EKasaChduSettingsBase_EKasaChduKind_Description" xml:space="preserve">
    <value>Typ CHDU - používá se pro stanovení kapacity CHDU - tzn. že určuje, kdy se zákazníkovi zobrazí varování o zaplněnosti CHDU.</value>
  </data>
  <data name="EKasaSettingsBase_ChduSettings_DisplayName" xml:space="preserve">
    <value>CHDU</value>
  </data>
  <data name="EKasaSettingsBase_ChduSettings_Description" xml:space="preserve">
    <value>Nastavení CHDU</value>
  </data>
  <data name="EKasaSettingsBase_SoapSettings_DisplayName" xml:space="preserve">
    <value>SOAP</value>
  </data>
  <data name="EKasaSettingsBase_SoapSettings_Description" xml:space="preserve">
    <value>Nastavení protokolu a připojení k finanční správě</value>
  </data>
  <data name="PlaygroundEKasaChduSettings_DNumOffset_DisplayName" xml:space="preserve">
    <value>Posunutí prvního bloku</value>
  </data>
  <data name="PlaygroundEKasaChduSettings_DNumOffset_Description" xml:space="preserve">
    <value>Udává počet bloků, o kolik je posunuty první adresovatelný blok. Používá se pro případy, kdy během testování je již CHDU zaplněno a chceme simulovat situaci, kdy je CHDU prázdné. V takovém případě se nastaví posun na poslední obsazený datový blok.</value>
  </data>
  <data name="EKasaSoapSettingsBase_Timeout_DisplayName" xml:space="preserve">
    <value>Timeout odpovědi</value>
  </data>
  <data name="EKasaSoapSettingsBase_Timeout_Description" xml:space="preserve">
    <value>Server musí odpovědět do 2s. Nicméně je potřeba připočítat i odezvu místní sítě. Doporučuju proto nastavit na 3s.</value>
  </data>
  <data name="EKasaSoapSettingsBase_ServerAddress_DisplayName" xml:space="preserve">
    <value>Adresa serveru</value>
  </data>
  <data name="EKasaSoapSettingsBase_ServerAddress_Description" xml:space="preserve">
    <value>Adresa na které beží server eKasy. Nastaveno dle Slovenské finanční správy. U produkčního prostředí změnit jen v případě, ze finanční správa vydá aktualizované pokyny.</value>
  </data>
  <data name="SchedulerPluginsStockMovementSenderConfig_MovementsTriggerConfig_DisplayName" xml:space="preserve">
    <value>Skladové pohyby: Nastavení časovače</value>
  </data>
  <data name="SchedulerPluginsStockMovementSenderConfig_MovementsTriggerConfig_Description" xml:space="preserve">
    <value>Nastavení parametrů časovače</value>
  </data>
  <data name="SchedulerPluginsStockMovementSenderConfig_MovementsFailedJobConfig_DisplayName" xml:space="preserve">
    <value>Skladové pohyby: Opakování při chybě</value>
  </data>
  <data name="SchedulerPluginsStockMovementSenderConfig_MovementsFailedJobConfig_Description" xml:space="preserve">
    <value>Nastavení toho, zda se při chybě v úloze má opakovat spuštění</value>
  </data>
  <data name="SchedulerPluginsStockMovementSenderConfig_ChecksumTriggerConfig_DisplayName" xml:space="preserve">
    <value>Kontrolní součty: Nastavení časovače</value>
  </data>
  <data name="SchedulerPluginsStockMovementSenderConfig_ChecksumTriggerConfig_Description" xml:space="preserve">
    <value>Nastavení parametrů časovače</value>
  </data>
  <data name="SchedulerPluginsStockMovementSenderConfig_ChecksumFailedJobConfig_DisplayName" xml:space="preserve">
    <value>Kontrolní součty: Opakování při chybě</value>
  </data>
  <data name="SchedulerPluginsStockMovementSenderConfig_ChecksumFailedJobConfig_Description" xml:space="preserve">
    <value>Nastavení toho, zda se při chybě v úloze má opakovat spuštění</value>
  </data>
  <data name="FolderPicturesSource_Folder_DisplayName" xml:space="preserve">
    <value>Složka s obrázky</value>
  </data>
  <data name="FolderPicturesSource_Folder_Description" xml:space="preserve">
    <value>Složka, z které se načítají obrázky. Připona obrázku musí byt jpg.</value>
  </data>
  <data name="PresPointBehaviourStartScreenPicturesConfig_StartScreenPicturesSource_DisplayName" xml:space="preserve">
    <value>Zdroj</value>
  </data>
  <data name="PresPointBehaviourStartScreenPicturesConfig_StartScreenPictureSource_Description" xml:space="preserve">
    <value>Zdroj</value>
  </data>
  <data name="PresPointBehaviourStartScreenPicturesConfig_Settings_DisplayName" xml:space="preserve">
    <value>Nastavení</value>
  </data>
  <data name="PresPointBehaviourStartScreenPicturesConfig_Settings_Description" xml:space="preserve">
    <value>Nastavení</value>
  </data>
  <data name="PresPointBehaviourStartScreenPicturesConfig_SwitchPictureInterval_DisplayName" xml:space="preserve">
    <value>Interval střídání</value>
  </data>
  <data name="PresPointBehaviourStartScreenPicturesConfig_SwitchPictureInterval_Description" xml:space="preserve">
    <value>Interval střídání obrázků [ms]</value>
  </data>
  <data name="GlobalRulesPasswordConfig_AllowAtSign_DisplayName" xml:space="preserve">
    <value>Povolit zavináč</value>
  </data>
  <data name="GlobalRulesPasswordConfig_AllowAtSign_Description" xml:space="preserve">
    <value>Povolit zavináč</value>
  </data>
  <data name="EKasaSettingsBase_ChduServiceKind_DisplayName" xml:space="preserve">
    <value>Typ CHDU</value>
  </data>
  <data name="EKasaSettingsBase_ChduServiceKind_Description" xml:space="preserve">
    <value>Typ CHDU</value>
  </data>
  <data name="EKasaPortosSettings_ConfigFileName_DisplayName" xml:space="preserve">
    <value>Soubor s konfigurací</value>
  </data>
  <data name="EKasaPortosSettings_ConfigFileName_Description" xml:space="preserve">
    <value>Cesta a název souboru s konfigurací. Je možné používat symbolické proměnné.</value>
  </data>
  <data name="PlaygroundEKasaChduSettings_DataFolder_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="PlaygroundEKasaChduSettings_DataFolder_Description" xml:space="preserve">
    <value />
  </data>
  <data name="PatientsOrderingSettings_Days_DisplayName" xml:space="preserve">
    <value>Počet dnů</value>
  </data>
  <data name="PatientsOrderingSettings_Days_Description" xml:space="preserve">
    <value>Počet dnů na které se zobrazí objednávky</value>
  </data>
  <data name="PatientOrderingLoginScreenSettings_LoginScreenLogin_DisplayName" xml:space="preserve">
    <value>Login</value>
  </data>
  <data name="PatientOrderingLoginScreenSettings_LoginScreenLogin_Description" xml:space="preserve">
    <value>Popisek pole pro zadání loginu</value>
  </data>
  <data name="PatientOrderingLoginScreenSettings_LoginScreenPassword_DisplayName" xml:space="preserve">
    <value>Heslo</value>
  </data>
  <data name="PatientOrderingLoginScreenSettings_LoginScreenPassword_Description" xml:space="preserve">
    <value>Popisek pole pro zadání hesla</value>
  </data>
  <data name="PatientOrderingLoginScreenSettings_LoginScreenButton_DisplayName" xml:space="preserve">
    <value>Tlačítko</value>
  </data>
  <data name="PatientOrderingLoginScreenSettings_LoginScreenButton_Description" xml:space="preserve">
    <value>Popisek tlačítka pro přihlášení</value>
  </data>
  <data name="PatientOrderingLoginScreenSettings_LoginScreenKeyboard_DisplayName" xml:space="preserve">
    <value>Klávesnice</value>
  </data>
  <data name="PatientOrderingLoginScreenSettings_LoginScreenKeyboard_Description" xml:space="preserve">
    <value>Zobrazit numerickou klávesnici</value>
  </data>
  <data name="PatientOrderingBehaviourGeneralConfig_LoginScreen_DisplayName" xml:space="preserve">
    <value>Příhlašovací obrazovka</value>
  </data>
  <data name="PatientOrderingBehaviourGeneralConfig_LoginScreen_Description" xml:space="preserve">
    <value>Nastavení přihlašovací obrazovky</value>
  </data>
  <data name="WebKreditAccommodationsOverviewConfig_Enabled_DisplayName" xml:space="preserve">
    <value>Povolit</value>
  </data>
  <data name="WebKreditAccommodationsOverviewConfig_Enabled_Description" xml:space="preserve">
    <value>Zobrazit záložku Přehled ubytování</value>
  </data>
  <data name="WebKreditAccommodationsOverviewConfig_ShowAccommodations_DisplayName" xml:space="preserve">
    <value>Zobrazit ubytování</value>
  </data>
  <data name="WebKreditAccommodationsOverviewConfig_ShowAccommodations_Description" xml:space="preserve">
    <value>Zobrazit seznam ubytování</value>
  </data>
  <data name="WebKreditAccommodationsOverviewConfig_ShowReservations_DisplayName" xml:space="preserve">
    <value>Zobrazit rezervace</value>
  </data>
  <data name="WebKreditAccommodationsOverviewConfig_ShowReservations_Description" xml:space="preserve">
    <value>Zobrazit seznam rezervací</value>
  </data>
  <data name="WebKreditAccommodationsOverviewConfig_ShowRequests_DisplayName" xml:space="preserve">
    <value>Zobrazit žádosti</value>
  </data>
  <data name="WebKreditAccommodationsOverviewConfig_ShowRequests_Description" xml:space="preserve">
    <value>Zobrazit seznam žádostí</value>
  </data>
  <data name="UbyportConfig_Settings_Description" xml:space="preserve">
    <value>Nastavení adresy a přihlašovacích údajů webové služby,</value>
  </data>
  <data name="UbyportConfig_Settings_DisplayName" xml:space="preserve">
    <value>Nastavení</value>
  </data>
  <data name="UbyportConfig_UbyPortSetting_Description" xml:space="preserve">
    <value>Odesílání údajů na UbyPort</value>
  </data>
  <data name="UbyportConfig_UbyPortSetting_DisplayName" xml:space="preserve">
    <value>Odesílání údajů na UbyPort</value>
  </data>
  <data name="UbyPortProductionSettings_Address_DisplayName" xml:space="preserve">
    <value>Adresa webové služby</value>
  </data>
  <data name="UbyPortProductionSettings_Domain_DisplayName" xml:space="preserve">
    <value>Doména</value>
  </data>
  <data name="UbyPortProductionSettings_Password_DisplayName" xml:space="preserve">
    <value>Heslo</value>
  </data>
  <data name="UbyPortProductionSettings_UserName_DisplayName" xml:space="preserve">
    <value>Přihlašovací jméno</value>
  </data>
  <data name="UbyPortTestSettings_Address_DisplayName" xml:space="preserve">
    <value>Adresa webové služby</value>
  </data>
  <data name="UbyPortTestSettings_Domain_DisplayName" xml:space="preserve">
    <value>Doména</value>
  </data>
  <data name="UbyPortTestSettings_Password_DisplayName" xml:space="preserve">
    <value>Heslo</value>
  </data>
  <data name="UbyPortTestSettings_UserName_DisplayName" xml:space="preserve">
    <value>Přihlašovací jméno</value>
  </data>
  <data name="SchedulerPluginsHelpDeskSyncConfig_SqlServerName_DisplayName" xml:space="preserve">
    <value>Název SQL serveru</value>
  </data>
  <data name="SchedulerPluginsHelpDeskSyncConfig_SqlServerName_Description" xml:space="preserve">
    <value>Nastavení jména SQL serveru. Používá se jako bezpečnostní mechanizmus, aby nedošlo k situaci, kdy se stáhnou data od zákazníka a posílají se instalační parametry testovacích počítačů na ostrý HD.

Název se musí shodovat s 'select server_name from dba.CFServery where central=1'.</value>
  </data>
  <data name="GlobalBehaviorEKasaConfig_EKasaHostMode_DisplayName" xml:space="preserve">
    <value>Spustit jako</value>
  </data>
  <data name="GlobalBehaviorEKasaConfig_EKasaHostMode_Description" xml:space="preserve">
    <value>Říká, v rámci jakého procesu bude služba eKasy spuštěna.</value>
  </data>
  <data name="SloHealthyMenuItem_MaxWeight_DisplayName" xml:space="preserve">
    <value>Maximální hmotnost (g)</value>
  </data>
  <data name="SloHealthyMenuItem_MaxWeight_Description" xml:space="preserve">
    <value>Maximální hmotnost zdravého jídla v gramech</value>
  </data>
  <data name="WebKreditMailingConfig_EmailSender_DisplayName" xml:space="preserve">
    <value>E-mailová adresa odesílatele</value>
  </data>
  <data name="WebKreditMailingConfig_EmailSender_Description" xml:space="preserve">
    <value>E-mailová adresa odesílatele</value>
  </data>
  <data name="WebKreditMailingConfig_Host_DisplayName" xml:space="preserve">
    <value>SMTP server - adresa</value>
  </data>
  <data name="WebKreditMailingConfig_Host_Description" xml:space="preserve">
    <value>Adresa serveru SMTP</value>
  </data>
  <data name="WebKreditMailingConfig_Port_DisplayName" xml:space="preserve">
    <value>SMTP server - port</value>
  </data>
  <data name="WebKreditMailingConfig_Port_Description" xml:space="preserve">
    <value>Port serveru SMTP</value>
  </data>
  <data name="WebKreditMailingConfig_CredentialsType_DisplayName" xml:space="preserve">
    <value>Přihlašovací údaje - typ</value>
  </data>
  <data name="WebKreditMailingConfig_CredentialsType_Description" xml:space="preserve">
    <value>Nastavení typu přihlašovacích údajů</value>
  </data>
  <data name="WebKreditMailingConfig_CredentialsSettings_DisplayName" xml:space="preserve">
    <value>Přihlašovací údaje - nastavení</value>
  </data>
  <data name="WebKreditMailingConfig_CredentialsSettings_Description" xml:space="preserve">
    <value>Nastavení přihlašovacích údajů</value>
  </data>
  <data name="WebKreditMailingCredentialsSettings_UserName_DisplayName" xml:space="preserve">
    <value>Uživatelské jméno</value>
  </data>
  <data name="WebKreditMailingCredentialsSettings_UserName_Description" xml:space="preserve">
    <value>Uživatelské jméno</value>
  </data>
  <data name="WebKreditMailingCredentialsSettings_Password_DisplayName" xml:space="preserve">
    <value>Heslo</value>
  </data>
  <data name="WebKreditMailingCredentialsSettings_Password_Description" xml:space="preserve">
    <value>Heslo</value>
  </data>
  <data name="OfficeClientsBehaviourBankAccountNumberConfig_EditMode_DisplayName" xml:space="preserve">
    <value>Způsob zadávání čísla účtu</value>
  </data>
  <data name="OfficeClientsBehaviourBankAccountNumberConfig_EditMode_Description" xml:space="preserve">
    <value>Volba způsobu zadávání čísla bankovního účtu</value>
  </data>
  <data name="AccommodationBehaviourSystemConfig_AccommodationStockId_DisplayName" xml:space="preserve">
    <value>Id skladu pro ubytování</value>
  </data>
  <data name="AccommodationBehaviourSystemConfig_AccommodationStockId_Description" xml:space="preserve">
    <value>Id skladu pro ubytování</value>
  </data>
  <data name="WebKreditAccommodationsHistoryConfig_EnableReceivables_DisplayName" xml:space="preserve">
    <value>Přehled pohledávek - zobrazit</value>
  </data>
  <data name="WebKreditAccommodationsHistoryConfig_EnableReceivables_Description" xml:space="preserve">
    <value>Zobrazit záložku Přehled pohledávek</value>
  </data>
  <data name="WebKreditAccommodationsHistoryConfig_EnableGuarantees_DisplayName" xml:space="preserve">
    <value>Přehled kaucí - zobrazit</value>
  </data>
  <data name="WebKreditAccommodationsHistoryConfig_EnableGuarantees_Description" xml:space="preserve">
    <value>Zobrazit záložku Přehled kaucí</value>
  </data>
  <data name="WebKreditAccommodationsHistoryConfig_EnableMessages_DisplayName" xml:space="preserve">
    <value>Korespondence - zobrazit</value>
  </data>
  <data name="WebKreditAccommodationsHistoryConfig_EnableMessages_Description" xml:space="preserve">
    <value>Zobrazit záložku Korespondence</value>
  </data>
  <data name="WebKreditRegistrationConfig_Enabled_DisplayName" xml:space="preserve">
    <value>Povolit</value>
  </data>
  <data name="WebKreditRegistrationConfig_Enabled_Description" xml:space="preserve">
    <value>Povolit záložku Registrace</value>
  </data>
  <data name="WebKreditRegistrationConfig_ShowBirthNumber_DisplayName" xml:space="preserve">
    <value>Zobrazit rodné číslo</value>
  </data>
  <data name="WebKreditRegistrationConfig_ShowBirthNumber_Description" xml:space="preserve">
    <value>Povolí možnost zadat při registraci rodné číslo</value>
  </data>
  <data name="WebKreditRegistrationConfig_PersonalNumberPrefix_DisplayName" xml:space="preserve">
    <value>Prefix osobního čísla</value>
  </data>
  <data name="WebKreditRegistrationConfig_PersonalNumberPrefix_Description" xml:space="preserve">
    <value>Prefix použitý při generování osobního čísla</value>
  </data>
  <data name="WebKreditRegistrationConfig_ClientGroupId_DisplayName" xml:space="preserve">
    <value>Id skupiny klientů</value>
  </data>
  <data name="WebKreditRegistrationConfig_ClientGroupId_Description" xml:space="preserve">
    <value>Id skupiny klientů do které se strávníci při vytvoření zařadí</value>
  </data>
  <data name="WebKreditRegistrationConfig_ResortCode_DisplayName" xml:space="preserve">
    <value>Kód střediska</value>
  </data>
  <data name="WebKreditRegistrationConfig_ResortCode_Description" xml:space="preserve">
    <value>Kód střediska do kterého se strávníci při vytvoření zařadí</value>
  </data>
  <data name="WebKreditOrderingConfig_Enabled_DisplayName" xml:space="preserve">
    <value>Povolit</value>
  </data>
  <data name="WebKreditOrderingConfig_Enabled_Description" xml:space="preserve">
    <value>Zobrazit záložku Objednávání</value>
  </data>
  <data name="GroupOfKindMenuFilterSettings_Kinds_DisplayName" xml:space="preserve">
    <value>Skupiny druhů jídel</value>
  </data>
  <data name="GroupOfKindMenuFilterSettings_Kinds_Description" xml:space="preserve">
    <value>Tvorba skupiny jídel. Skupina je tvořená z názvu a seznamu druhou, druh je definovaný jako ID druhu a  ID provozovně</value>
  </data>
  <data name="PresPointBehaviourMenuMealKindFilterConfig_MenuFilteredType_DisplayName" xml:space="preserve">
    <value>Filtrování jídel</value>
  </data>
  <data name="PresPointBehaviourMenuMealKindFilterConfig_MenuFilteredType_Description" xml:space="preserve">
    <value>Nastavení možností filtrování jídel. Když je nastevný parametr tak se před zobrazením jídelnička zobrazí filtr vybraného druhu.</value>
  </data>
  <data name="PresPointBehaviourMenuMealKindFilterConfig_Settings_DisplayName" xml:space="preserve">
    <value>Filtrování podle skupiny</value>
  </data>
  <data name="PresPointBehaviourMenuMealKindFilterConfig_Settings_Description" xml:space="preserve">
    <value>Aktivní při parametru na filtrování skupiny druhú.</value>
  </data>
  <data name="MealKindGroupMenuSettings_Name_DisplayName" xml:space="preserve">
    <value>Název</value>
  </data>
  <data name="MealKindGroupMenuSettings_Name_Description" xml:space="preserve">
    <value>Název skupiny druhu jídel. Zobrazuje se v aplikaci při výběru.   </value>
  </data>
  <data name="MealKindGroupMenuSettings_MealKindGroups_DisplayName" xml:space="preserve">
    <value>Položky skupiny</value>
  </data>
  <data name="MealKindGroupMenuSettings_MealKindGroups_Description" xml:space="preserve">
    <value> Seznam skupiny druhu jídel pozostávajúci z ID jídla a ID provozovny</value>
  </data>
  <data name="MealKindGroupMenuSettings_WorkplaceId_DisplayName" xml:space="preserve">
    <value>ID provozovny</value>
  </data>
  <data name="MealKindGroupMenuSettings_WorkplaceId_Description" xml:space="preserve">
    <value>ID provozovny ve které se nacházejí druhy jídel</value>
  </data>
  <data name="PresPointBehaviourMenuConfig_ShowDetailWithoutNote_DisplayName" xml:space="preserve">
    <value>Zobrazováním dlouhých názvů jídel</value>
  </data>
  <data name="PresPointBehaviourMenuConfig_ShowDetailWithoutNote_Description" xml:space="preserve">
    <value>V případě, že je parametr nastaven tlačilo Detail je vždy aktivní.</value>
  </data>
  <data name="PresPointBehaviourOrderConfig_ShowMealName_DisplayName" xml:space="preserve">
    <value>Zobrazení názvu jídla v objednávce</value>
  </data>
  <data name="PresPointBehaviourOrderConfig_ShowMealName_Description" xml:space="preserve">
    <value>V případě, že je parametr nastaven se v části objednávek pro každou objednávku zobrazí název objednaného jídla.</value>
  </data>
  <data name="GlobalBehaviourKitchenOrderPrintConfig_IsPrintMealKindNameWithAltRow_Description" xml:space="preserve">
    <value>Tisk řádku s názvem druhu jídla a číslem alternativy. Pokud false, bude se tisknout pouze název jídla.</value>
  </data>
  <data name="GlobalBehaviourKitchenOrderPrintConfig_IsPrintMealKindNameWithAltRow_DisplayName" xml:space="preserve">
    <value>Tisk řádku s názvem druhu jídla a alt</value>
  </data>
  <data name="GlobalBehaviourKitchenOrderPrintConfig_IsPrintOrderRowWithLargerFont_Description" xml:space="preserve">
    <value>Tisk řádku objednávky větším písmem. Pokud false, bude se tisknout normální velikostí.</value>
  </data>
  <data name="GlobalBehaviourKitchenOrderPrintConfig_IsPrintOrderRowWithLargerFont_DisplayName" xml:space="preserve">
    <value>Tisk řádku objednávky větším písmem</value>
  </data>
  <data name="CashDeskBehaviorEetConfig_IsEnabledAccommodationButton_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="CashDeskBehaviorEetConfig_IsEnabledAccommodationButton_Description" xml:space="preserve">
    <value />
  </data>
  <data name="LdapServerConfig_UseUserAccountCredential_DisplayName" xml:space="preserve">
    <value>Použít přihlasovaného uživatele</value>
  </data>
  <data name="LdapServerConfig_UseUserAccountCredential_Description" xml:space="preserve">
    <value>Pokud je čtvereček zaškrtnut, pro přihlášení do LDAP se použije přihlašovaný uživatel. Při zaškrtnutém čtverečku není třeba vyplňovat údaje o servisem uživatelovi.</value>
  </data>
  <data name="PresPointBehaviourMenuConfig_CanNotOrder_DisplayName" xml:space="preserve">
    <value>Zakázat objednávání</value>
  </data>
  <data name="PresPointBehaviourMenuConfig_CanNotOrder_Description" xml:space="preserve">
    <value>Uživatelovi je zakázán objednávat. Také je schovaná kategorie objednávky.</value>
  </data>
  <data name="CashDeskBehaviourSaleSlipSplitConfig_SeparateGoodsFromNonCashSalesSlip_DisplayName" xml:space="preserve">
    <value>Rozdělit bezhotovostní doklad na bezhotovostní a hotovostní se sortimentem</value>
  </data>
  <data name="CashDeskBehaviourSaleSlipSplitConfig_SeparateGoodsFromNonCashSalesSlip_Description" xml:space="preserve">
    <value>Pokud je povoleno, rozdělit se bezhotovostní paragon na bezhotovostní a hotovostní se sortimentem. Resilo se kvuli STU. Obdobná funkčnost byla ve fiskálu. Nyní svázano s EKasou.</value>
  </data>
  <data name="OfficeClientsBehaviourInternetAccountReportConfig_InternetAccountReportEnabled_DisplayName" xml:space="preserve">
    <value>Zobrazení sestavy internetového účtu</value>
  </data>
  <data name="OfficeClientsBehaviourInternetAccountReportConfig_InternetAccountReportEnabled_Description" xml:space="preserve">
    <value>Povolení zobrazení sestavy internetového účtu</value>
  </data>
  <data name="MenuPresenterBehaviourMenuConfig_TitleAlt_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="MenuPresenterBehaviourMenuConfig_TitleAlt_Description" xml:space="preserve">
    <value />
  </data>
  <data name="GlobalRulesGdprClientInfoProtectionConfig_IsProtectionActivated_DisplayName" xml:space="preserve">
    <value />
  </data>
  <data name="GlobalRulesGdprClientInfoProtectionConfig_IsProtectionActivated_Description" xml:space="preserve">
    <value />
  </data>
  <data name="OfficeBehaviourBalanceConfig_AllowEarlyAccountBalance_DisplayName" xml:space="preserve">
    <value>Povolit předčasnou uzávěrku</value>
  </data>
  <data name="OfficeBehaviourBalanceConfig_AllowEarlyAccountBalance_Description" xml:space="preserve">
    <value>Povolit uzávěrku ještě před vypršením posledního dne v měsíci? Vyžádal si zákazník, který provádí uzávěrku v pátek. Přes víkend nemá žádný provoz.</value>
  </data>
  <data name="AvailableStocksSettings_AvailableStock_DisplayName" xml:space="preserve">
    <value>ID skladu</value>
  </data>
  <data name="AvailableStocksSettings_AvailableStock_Description" xml:space="preserve">
    <value />
  </data>
  <data name="MobileStockBehaviorStocksConfig_Stocks_DisplayName" xml:space="preserve">
    <value>Sklady</value>
  </data>
  <data name="MobileStockBehaviorStocksConfig_Stocks_Description" xml:space="preserve">
    <value>Přidání jednotlivých skladů s nimiž bude komunikovat aplikace mobilní inventura. Počet skladů musí být shodný nebo menší než je hodnota v licenci jinak mobilní inventura nenačte sklady a vypíše chybové hlášení.</value>
  </data>
  <data name="GlobalHwCardExternalSystemReaderConfig_ReaderType_DisplayName" xml:space="preserve">
    <value>Způsob čtení karet</value>
  </data>
  <data name="GlobalHwCardExternalSystemReaderConfig_ReaderType_Description" xml:space="preserve">
    <value>Způsob čtení čísel karet, tzn. z jakého zdroje se karty vyčítají. Vyřešeno obecně, aby bylo možné případně snadno integrovat jiné externí systémy.</value>
  </data>
  <data name="GlobalHwCardExternalSystemReaderConfig_Settings_DisplayName" xml:space="preserve">
    <value>Nastavení</value>
  </data>
  <data name="GlobalHwCardExternalSystemReaderConfig_Settings_Description" xml:space="preserve">
    <value>Nasatavení pro zvolený způsob čtení karet</value>
  </data>
  <data name="DatabaseExternalSystemReaderSettings_TerminalId_DisplayName" xml:space="preserve">
    <value>Id terminálu</value>
  </data>
  <data name="DatabaseExternalSystemReaderSettings_TerminalId_Description" xml:space="preserve">
    <value>Id terminálu (pravděpodobně IP adresa). Na základě tohoho ID se zpáruje záznam v databazí s konkréktním zařízením a čtečkou.</value>
  </data>
  <data name="TerminalIdForDeviceSettings_TerminalId_DisplayName" xml:space="preserve">
    <value>Id terminálu</value>
  </data>
  <data name="TerminalIdForDeviceSettings_TerminalId_Description" xml:space="preserve">
    <value>Id terminálu (pravděpodobně IP adresa). Na základě tohoho ID se zpáruje záznam v databazí s konkréktním zařízením a čtečkou.</value>
  </data>
  <data name="TerminalIdForDeviceSettings_DeviceTerminalId_DisplayName" xml:space="preserve">
    <value>Id čtečky</value>
  </data>
  <data name="TerminalIdForDeviceSettings_DeviceTerminalId_Description" xml:space="preserve">
    <value>Id čtečky v rámci daného Id zařízení.</value>
  </data>
  <data name="InfoProClientConfig_PlaceholderBlobId_DisplayName" xml:space="preserve">
    <value>Id zástupného obrázku jídla</value>
  </data>
  <data name="InfoProClientConfig_PlaceholderBlobId_Description" xml:space="preserve">
    <value>Id obrázku z dba.Blobs, který se zobrazí v případě, že jídlo žádný obrázek přiřazený nemá</value>
  </data>
  <data name="InfoProServerConfig_MediaFolderPath_DisplayName" xml:space="preserve">
    <value>Cesta k adresáři médií</value>
  </data>
  <data name="InfoProServerConfig_MediaFolderPath_Description" xml:space="preserve">
    <value>Cesta na disku k adresáři, kde se budou cachovat soubory médií (obrázek/video/audio/...).
Uživatel, pod kterým aplikace běží, musí mít právo na čtení i zápis do tohoto adresáře.</value>
  </data>
  <data name="WebKreditAccountSettingsConfig_CheckPasswordComplexity_DisplayName" xml:space="preserve">
    <value>Kontrolovat komplexitu hesla</value>
  </data>
  <data name="WebKreditAccountSettingsConfig_CheckPasswordComplexity_Description" xml:space="preserve">
    <value>Heslo musí obsahovat:
1) alespoň jedno číslo
2) alespoň jedno velké písmeno
3) alespoň jedno malé písmeno
4) alespoň jeden znak, který není písmeno nebo číslo</value>
  </data>
  <data name="WebKreditAuthenticationConfig_FailedLoginLockoutEnabled_DisplayName" xml:space="preserve">
    <value>Blokování účtu při neúspěšném přihlášení - povolit</value>
  </data>
  <data name="WebKreditAuthenticationConfig_FailedLoginLockoutEnabled_Description" xml:space="preserve">
    <value>Blokování účtu při neúspěšném přihlášení - povolit</value>
  </data>
  <data name="WebKreditAuthenticationConfig_FailedLoginLockoutAttempts_DisplayName" xml:space="preserve">
    <value>Blokování účtu při neúspěšném přihlášení - počet neúspěšných pokusů</value>
  </data>
  <data name="WebKreditAuthenticationConfig_FailedLoginLockoutAttempts_Description" xml:space="preserve">
    <value>Blokování účtu při neúspěšném přihlášení - počet neúspěšných pokusů</value>
  </data>
  <data name="WebKreditAuthenticationConfig_FailedLoginLockoutTimeout_DisplayName" xml:space="preserve">
    <value>Blokování účtu při neúspěšném přihlášení - doba zablokování (v minutách)</value>
  </data>
  <data name="WebKreditAuthenticationConfig_FailedLoginLockoutTimeout_Description" xml:space="preserve">
    <value>Blokování účtu při neúspěšném přihlášení - doba zablokování (v minutách)</value>
  </data>
  <data name="OfficeWorkplacesBehaviourMenuConfig_SetVisibilityByMealName_DisplayName" xml:space="preserve">
    <value>Nastavovat příznak Zobrazit</value>
  </data>
  <data name="OfficeWorkplacesBehaviourMenuConfig_SetVisibilityByMealName_Description" xml:space="preserve">
    <value>Nastavovat příznak Zobrazit, podle toho, zda je vyplněn název jídla.</value>
  </data>
  <data name="GlobalBehaviourApplicationLanguageDetailConfig_UseLanguageDetailCount_DisplayName" xml:space="preserve">
    <value>Použít pro počet zobrazených jazyků v PM</value>
  </data>
  <data name="GlobalBehaviourApplicationLanguageDetailConfig_UseLanguageDetailCount_Description" xml:space="preserve">
    <value>Určí, zda se pro počet zobrazených jazyků v PM , použije GlobalBehaviourApplicationLanguageDetailConfig, nebo GlobalBehaviourApplicationLanguageConfig.</value>
  </data>
  <data name="GlobalBehaviourApplicationLanguageDetailConfig_LanguageCount_DisplayName" xml:space="preserve">
    <value>Počet zobrazených jazyků v PM</value>
  </data>
  <data name="GlobalBehaviourApplicationLanguageDetailConfig_LanguageCount_Description" xml:space="preserve">
    <value>Ovlivňuje počet jazyků, které jsou v aplikaci nabízeny. Prozatím využívá pouze PM.</value>
  </data>
  <data name="GlobalRulesExchangeRateConfig_ExchangeRateKind_DisplayName" xml:space="preserve">
    <value>Typ kurzu</value>
  </data>
  <data name="GlobalRulesExchangeRateConfig_ExchangeRateKind_Description" xml:space="preserve">
    <value>Určuje, jakým způsobem se stanoví kurz cizí měny</value>
  </data>
  <data name="GlobalBehaviourMessageForClientsConfig_EnableMessageSending_DisplayName" xml:space="preserve">
    <value>Povolit zasílání zpráv</value>
  </data>
  <data name="GlobalBehaviourMessageForClientsConfig_EnableMessageSending_Description" xml:space="preserve">
    <value>Povolí zasílání uživatelských zpráv z Kanceláře do WK a MK.</value>
  </data>
  <data name="GlobalRulesPasswordConfig_AllowSpace_DisplayName" xml:space="preserve">
    <value>Povolit mezeru</value>
  </data>
  <data name="GlobalRulesPasswordConfig_AllowSpace_Description" xml:space="preserve">
    <value>Povolit mezeru</value>
  </data>
  <data name="PresPointBehaviourClientInfoConfig_ShowClientName_DisplayName" xml:space="preserve">
    <value>Zobrazovat jméno klienta</value>
  </data>
  <data name="PresPointBehaviourClientInfoConfig_ShowClientName_Description" xml:space="preserve">
    <value>Zobrazovat jméno klienta v hlavicce klienta?</value>
  </data>
  <data name="PresPointBehaviourPaymentTermDepositConfig_Enabled_DisplayName" xml:space="preserve">
    <value>Povolit vklad záloh</value>
  </data>
  <data name="PresPointBehaviourPaymentTermDepositConfig_Enabled_Description" xml:space="preserve">
    <value>Povolit vklad záloh přes platební terminál</value>
  </data>
  <data name="GlobalRulesVatRateConfig_SecondaryVatRateForMealEnabled_DisplayName" xml:space="preserve">
    <value>Povolit druhou sazbu pro jídla</value>
  </data>
  <data name="GlobalRulesVatRateConfig_SecondaryVatRateForMealEnabled_Description" xml:space="preserve">
    <value>Povolit druhou sazbu DPH pro jidla? Pouziva se pro jidla s sebou. Povolenim se upravi editory v Kancelari, UI Ctecek a Kasy.</value>
  </data>
  <data name="WebKreditGeneralConfig_TabAnonymous_DisplayName" xml:space="preserve">
    <value>Výchozí záložka - před přihlášením</value>
  </data>
  <data name="WebKreditGeneralConfig_TabAnonymous_Description" xml:space="preserve">
    <value>Záložka, která se zobrazí před přihlášením</value>
  </data>
  <data name="WebKreditGeneralConfig_TabAuthenticated_DisplayName" xml:space="preserve">
    <value>Výchozí záložka - po přihlášení</value>
  </data>
  <data name="WebKreditGeneralConfig_TabAuthenticated_Description" xml:space="preserve">
    <value>Záložka, která se zobrazí po přihlášení</value>
  </data>
  <data name="CashDeskBehaviourLockConfig_LongPressTimeout_DisplayName" xml:space="preserve">
    <value>Doba držení tlačítka [ms]</value>
  </data>
  <data name="CashDeskBehaviourLockConfig_LongPressTimeout_Description" xml:space="preserve">
    <value>Po uplynutí této doby je požadované jídlo namarkováno v sekundární sazbě DPH.</value>
  </data>
  <data name="TimePriceMenuSettings_From_DisplayName" xml:space="preserve">
    <value>Od</value>
  </data>
  <data name="TimePriceMenuSettings_From_Description" xml:space="preserve">
    <value>Čas od kdy se začne zobrazovat cena jídel. Den v nelze nastavit.</value>
  </data>
  <data name="TimePriceMenuSettings_To_DisplayName" xml:space="preserve">
    <value>Do</value>
  </data>
  <data name="TimePriceMenuSettings_To_Description" xml:space="preserve">
    <value>Čas do kdy se bude zobrazovat cena jídel. Den v nelze nastavit.</value>
  </data>
  <data name="PresPointBehaviourMenuConfig_ShowTimePrice_DisplayName" xml:space="preserve">
    <value>Zobrazit ceny jídel v určitém čase?</value>
  </data>
  <data name="PresPointBehaviourMenuConfig_ShowTimePrice_Description" xml:space="preserve">
    <value>Zobrazování cen v jídelníčku s parametry od do. Je možné nastavit pouze hodinu, minutu a sekundu.</value>
  </data>
  <data name="MobileOrderingBehaviorMenuConfig_Time_DisplayName" xml:space="preserve">
    <value>Čas zobrazování cen v jídelníčku.</value>
  </data>
  <data name="MobileOrderingBehaviorMenuConfig_Time_Description" xml:space="preserve">
    <value>Časy kdy se zobrazují ceny v jídelníčku.</value>
  </data>
  <data name="SchedulerPluginsKreditSyncConfig_VatRateTriggerConfig_DisplayName" xml:space="preserve">
    <value>Rekalkulace DPH: nastavení časovače</value>
  </data>
  <data name="SchedulerPluginsKreditSyncConfig_VatRateTriggerConfig_Description" xml:space="preserve">
    <value>Nastavení parametrů časovače pro synchronizaci sazeb DPH. Časovač volá SP RekalkulaceDPH.</value>
  </data>
  <data name="SchedulerPluginsKreditSyncConfig_VatRateFailedJobConfig_DisplayName" xml:space="preserve">
    <value>Rekalkulace DPH: nastavení při chybě</value>
  </data>
  <data name="SchedulerPluginsKreditSyncConfig_VatRateFailedJobConfig_Description" xml:space="preserve">
    <value>Nastavení toho, zda se při chybě v úloze má opakovat spuštění</value>
  </data>
  <data name="WebKreditHeaderConfig_LoginButtonText_DisplayName" xml:space="preserve">
    <value>Tlačítko pro přihlášení</value>
  </data>
  <data name="WebKreditHeaderConfig_LoginButtonText_Description" xml:space="preserve">
    <value>Text tlačítka pro přihlášení</value>
  </data>
  <data name="WebKreditHeaderConfig_LoginSSOButtonText_DisplayName" xml:space="preserve">
    <value>Tlačítko pro přihlášení přes SSO</value>
  </data>
  <data name="WebKreditHeaderConfig_LoginSSOButtonText_Description" xml:space="preserve">
    <value>Text tlačítka pro přihlášení přes SSO</value>
  </data>
  <data name="WebKreditLocalizedSettings_Language_DisplayName" xml:space="preserve">
    <value>Jazyk</value>
  </data>
  <data name="WebKreditLocalizedSettings_Language_Description" xml:space="preserve">
    <value>Jazyk</value>
  </data>
  <data name="WebKreditLocalizedSettings_Text_DisplayName" xml:space="preserve">
    <value>Text</value>
  </data>
  <data name="WebKreditLocalizedSettings_Text_Description" xml:space="preserve">
    <value>Text</value>
  </data>
  <data name="MobileOrderingBehaviorAccountGdprConfig_Enabled_DisplayName" xml:space="preserve">
    <value>Zakázat odvolání</value>
  </data>
  <data name="MobileOrderingBehaviorAccountGdprConfig_Enabled_Description" xml:space="preserve">
    <value>Uživatel nemůže odvolat Gdpr pomocí mobilní aplikace</value>
  </data>
  <data name="GPWebPayPaymentGateSettings_ServicesUrl_DisplayName" xml:space="preserve">
    <value>WebService URL</value>
  </data>
  <data name="GPWebPayPaymentGateSettings_ServicesUrl_Description" xml:space="preserve">
    <value>Odkaz na GP webpay WebService</value>
  </data>
  <data name="GPWebPayPaymentGateSettings_ProviderID_DisplayName" xml:space="preserve">
    <value>Identifikátor poskytovatele platebních služeb</value>
  </data>
  <data name="GPWebPayPaymentGateSettings_ProviderID_Description" xml:space="preserve">
    <value>Identifikátor poskytovatele platebních služeb</value>
  </data>
  <data name="OfficeWorkplacesBehaviourMenuConfig_SameMealNameProhibitFor_DisplayName" xml:space="preserve">
    <value>Opakování názvu jídla po</value>
  </data>
  <data name="OfficeWorkplacesBehaviourMenuConfig_SameMealNameProhibitFor_Description" xml:space="preserve">
    <value>Povolí opakování názvu jídla po daném počtu dnů. Pokud je nastaveno 0, vůbec se opakování nekontroluje.</value>
  </data>
  <data name="SchedulerPluginsBatchReportConfig_BatchReportTriggerConfig_DisplayName" xml:space="preserve">
    <value>Nastavení časovače</value>
  </data>
  <data name="SchedulerPluginsBatchReportConfig_BatchReportTriggerConfig_Description" xml:space="preserve">
    <value>Nastavení parametrů časovače</value>
  </data>
  <data name="SchedulerPluginsBatchReportConfig_BatchReportFailedJobConfig_DisplayName" xml:space="preserve">
    <value>Opakování při chybě</value>
  </data>
  <data name="SchedulerPluginsBatchReportConfig_BatchReportFailedJobConfig_Description" xml:space="preserve">
    <value>Nastavení toho, zda se při chybě v úloze má opakovat spuštění</value>
  </data>
  <data name="SchedulerPluginsBatchReportConfig_BatchPrintIds_DisplayName" xml:space="preserve">
    <value>ID hromadých tisků</value>
  </data>
  <data name="SchedulerPluginsBatchReportConfig_BatchPrintIds_Description" xml:space="preserve">
    <value>Seznam ID hromadných tisků, které se mají automaticky spouštet. Jako oddělovač se používá čárka.</value>
  </data>
  <data name="AccommodationSchedulerConfig_DefaultAccomomodationType_DisplayName" xml:space="preserve">
    <value>Výchozí Id typu ubytování pro hotel</value>
  </data>
  <data name="AccommodationSchedulerConfig_DefaultAccomomodationType_Description" xml:space="preserve">
    <value>Výchozí typ ubytování pro hotel - Id z tabulky TypUbytovani</value>
  </data>
  <data name="PatientOrderingBehaviourGeneralConfig_AuthenticationTimeout_DisplayName" xml:space="preserve">
    <value>Doba expirace přihlášení při nečinnosti (m)</value>
  </data>
  <data name="PatientOrderingBehaviourGeneralConfig_AuthenticationTimeout_Description" xml:space="preserve">
    <value>Maximální doba nečinnosti (v minutách) po které bude uživatel odhlášen</value>
  </data>
  <data name="CasErPlusDialog06ScaleDeviceSettings_UseTara_DisplayName" xml:space="preserve">
    <value>Posílat taru</value>
  </data>
  <data name="CasErPlusDialog06ScaleDeviceSettings_UseTara_Description" xml:space="preserve">
    <value>Bude Kasa posílat váze i taru? Váha musí být nakonfigurována tak, aby po každém vážení taru vynulovala.</value>
  </data>
  <data name="CashDeskBehaviourRestaurantConfig_RestaurantLayoutType_Description" xml:space="preserve">
    <value>Určuje, zda je aktivní tlačítkové nebo grafické rozhranní pro výběr židlí a stolů. Ovládání klávesnicí je původní ovládání, kdy se přařazení úseku, stolu a židle musí dělat pomocí tlačítek. Ovládání editorem restaurace je nový přístup přes grafické rozhranní znázorňují restauraci a rozložení stolů a žildí v ní.</value>
  </data>
  <data name="CashDeskBehaviourRestaurantConfig_RestaurantLayoutType_DisplayName" xml:space="preserve">
    <value>Metoda výběru stolů a židlí</value>
  </data>
  <data name="FilterSettingsIssueSlipGenParamsView_AppInstallations_Description" xml:space="preserve">
    <value>Seznam výdejek - Generování výdejky - Parametry generování výdejky - volba "Generovat výdejku pro"</value>
  </data>
  <data name="FilterSettingsIssueSlipGenParamsView_AppInstallations_DisplayName" xml:space="preserve">
    <value>Generovat výdejku pro</value>
  </data>
  <data name="OfficeBehaviourFilterSettingsConfig_IssueSlipGenParamsView_Description" xml:space="preserve">
    <value>Seznam výdejek - Generování výdejky - Parametry generování výdejky</value>
  </data>
  <data name="OfficeBehaviourFilterSettingsConfig_IssueSlipGenParamsView_DisplayName" xml:space="preserve">
    <value>Generování výdejky</value>
  </data>
  <data name="OidcAuthorizationSettingsBase_OidcUri_DisplayName" xml:space="preserve">
    <value>Adresa servra</value>
  </data>
  <data name="OidcAuthorizationSettingsBase_OidcUri_Description" xml:space="preserve">
    <value>URL adresa kde se nachází OpenID (OIDC) server</value>
  </data>
  <data name="OidcAuthorizationSettingsBase_ClientId_DisplayName" xml:space="preserve">
    <value>Id služby</value>
  </data>
  <data name="OidcAuthorizationSettingsBase_ClientId_Description" xml:space="preserve">
    <value>Id OpenID (OIDC) služby. Nachází se na OpenID (OIDC) servery</value>
  </data>
  <data name="OidcAuthorizationSettingsBase_ClientSecret_DisplayName" xml:space="preserve">
    <value>Klíč služby</value>
  </data>
  <data name="OidcAuthorizationSettingsBase_ClientSecret_Description" xml:space="preserve">
    <value>Client servret klíč OpenID (OIDC) služby. Nachází se na OpenID (OIDC) servery</value>
  </data>
  <data name="OidcAuthorizationSettingsBase_Scope_DisplayName" xml:space="preserve">
    <value>Data zo serveru</value>
  </data>
  <data name="OidcAuthorizationSettingsBase_Scope_Description" xml:space="preserve">
    <value>Hodnoty které má server vracet. Defaultně - openid profile email (oddělené medzeramy)</value>
  </data>
  <data name="OidcAuthorizationSettingsBase_RedirectUri_DisplayName" xml:space="preserve">
    <value>Přesměrování</value>
  </data>
  <data name="OidcAuthorizationSettingsBase_RedirectUri_Description" xml:space="preserve">
    <value>Musí obsahovat openidclient://callback jinak se aplikace po přihlášení neotevře a nebude vědět pokračovat v přihlášení</value>
  </data>
  <data name="OfficePriceMakingBehaviourSubsidyCategoryConfig_SubsidyCategoryEnable_Description" xml:space="preserve">
    <value>Umožňuje nastavit výchozí hodnotu filtru pro povolení kategorií dotací v editoru Sazby DPH pro jídla. Povolení kategorií dotace umožňuje editovat jednotlivé kategorie dotace. V případě zakázání Povolení dotací hodnotou false nelze editovat jednotlivé kategorie dotací a edituje se pouze Kategorie bez dotace a sloupec Kategorie dotace je v editoru skrytý. Povolení dotace lze změnit ve filtru při spuštění editoru. Stav filtru se neukládá do Layoutu.</value>
  </data>
  <data name="OfficePriceMakingBehaviourSubsidyCategoryConfig_SubsidyCategoryEnable_DisplayName" xml:space="preserve">
    <value>Povolení kategorie dotace</value>
  </data>
  <data name="OidcAuthorizationSettingsBase_KeditPrefixIdentificator_DisplayName" xml:space="preserve">
    <value>Prefix pro přihlašování Kredit</value>
  </data>
  <data name="OidcAuthorizationSettingsBase_KeditPrefixIdentificator_Description" xml:space="preserve">
    <value>Prefix pro přihlašování Kredit. Hodnota by neměla obsahovat speciální znaky až na podtržítko (_).</value>
  </data>
  <data name="CashDeskBehaviourRestaurantConfig_RestaurantFormat_Description" xml:space="preserve">
    <value>Formát výstupu paragonu, který je určen symbolickým zápisem. např. {usek}{stul}/{zidle}, takže výstupem z vybrání stolu a židle bude např. A2/5. {usek} - určuje o jaký úsek restaurace se jedná, {stul} - určuje o jaký stůl se ve vybraném úseku jedná, {židle} - o jakou židli u stolu se jedná</value>
  </data>
  <data name="CashDeskBehaviourRestaurantConfig_RestaurantFormat_DisplayName" xml:space="preserve">
    <value>Formát výstupu paragonu</value>
  </data>
  <data name="CashDeskBehaviourRestaurantConfig_RestaurantTableName_Description" xml:space="preserve">
    <value>Prefix stolu je prefix názvu stolu při vytvoření stolu v Editoru restaurace v kanceláři. Je součástí výchozího názvu.</value>
  </data>
  <data name="CashDeskBehaviourRestaurantConfig_RestaurantTableName_DisplayName" xml:space="preserve">
    <value>Prefix stolu určující jeho název</value>
  </data>
  <data name="CashDeskBehaviourRestaurantConfig_RestaurantChairName_Description" xml:space="preserve">
    <value>Prefix židle je prefix názvu židle při vytvoření židle v Editoru restaurace v kanceláři. Je součástí výchozího názvu.</value>
  </data>
  <data name="CashDeskBehaviourRestaurantConfig_RestaurantChairName_DisplayName" xml:space="preserve">
    <value>Prefix židle</value>
  </data>
  <data name="OfficeWorkplacesBehaviourPriceListConfig_ValidateCircularReferences_DisplayName" xml:space="preserve">
    <value>Validovat cirkularní reference</value>
  </data>
  <data name="OfficeWorkplacesBehaviourPriceListConfig_ValidateCircularReferences_Description" xml:space="preserve">
    <value>Validace je provedena pri uložení dat v ceníku. Kontroluje se, zda se některé ceníkové složky neodkazují navzájem samy na sebe. To poté vede k pádu uzávěrky. U složitějších ceníku může validace zpomalit uložení dat.</value>
  </data>
  <data name="AccommodationAppearanceLabelColorsConfig_AccommodationInProgressAppearance_Description" xml:space="preserve">
    <value>Nastavení vzhledu probíhajícího ubytování</value>
  </data>
  <data name="AccommodationAppearanceLabelColorsConfig_AccommodationInProgressAppearance_DisplayName" xml:space="preserve">
    <value>Nastavení vzhledu probíhajícího ubytování</value>
  </data>
  <data name="IssueOfMealsByCanteenSettings_IncludeNoServedMeals_DisplayName" xml:space="preserve">
    <value>Odepsat i nevydaná jídla</value>
  </data>
  <data name="IssueOfMealsByCanteenSettings_IncludeNoServedMeals_Description" xml:space="preserve">
    <value>Pokud se povolí, odepišou se při vytvoření výdejky i jídla, které doposudy nebyl vydány.</value>
  </data>
  <data name="OfficeMealDistributionBehaviourIssueSlipConfig_Settings_DisplayName" xml:space="preserve">
    <value>Nastavení</value>
  </data>
  <data name="OfficeMealDistributionBehaviourIssueSlipConfig_Settings_Description" xml:space="preserve">
    <value>Nastavení</value>
  </data>
  <data name="OfficeMealDistributionBehaviourIssueSlipConfig_IssuedOfMealsType_DisplayName" xml:space="preserve">
    <value>Způsob odpisu jídel</value>
  </data>
  <data name="OfficeMealDistributionBehaviourIssueSlipConfig_IssueOfMealsType_Description" xml:space="preserve">
    <value />
  </data>
  <data name="ScaleResolutionSettingsItem_ScaleResolution_DisplayName" xml:space="preserve">
    <value>Citlivost váhy (g)</value>
  </data>
  <data name="ScaleResolutionSettingsItem_ScaleResolution_Description" xml:space="preserve">
    <value>Minimální rozlišovačí schopnost váhy. Používá se při zaokrouhlování Tary - vždy se zaokrouhlí nahoru. Zadává se v gramech.</value>
  </data>
  <data name="CashDeskBehaviourLoginConfig_LoginType_DisplayName" xml:space="preserve">
    <value>Typ náhradního přihlášení</value>
  </data>
  <data name="CashDeskBehaviourLoginConfig_LoginType_Description" xml:space="preserve">
    <value>Tento parametr slouží pro nastavení výchozího typu přihlášení při náhradním přihlášení.</value>
  </data>
  <data name="CasErPlusDialog06ScaleDeviceSettings_ScaleResolutionSettings_DisplayName" xml:space="preserve">
    <value>Citlivost</value>
  </data>
  <data name="FioAboEBankingSettings_AccountNumber_Description" xml:space="preserve">
    <value>Číslo účtu ve tvaru 000000-***********</value>
  </data>
  <data name="CasErPlusDialog06ScaleDeviceSettings_ScaleResolutionSettings_Description" xml:space="preserve">
    <value>Nastavení citlivosti</value>
  </data>
  <data name="FioAboEBankingSettings_AccountNumber_DisplayName" xml:space="preserve">
    <value>Číslo účtu (tvar 000000-***********)</value>
  </data>
  <data name="ScaleResolutionSettings_Intervals_DisplayName" xml:space="preserve">
    <value>Intervaly citlivosti</value>
  </data>
  <data name="ScaleResolutionSettings_Intervals_Description" xml:space="preserve">
    <value>Umožnuje definovat citlivost váhy různě pro jednotlivé intervaly</value>
  </data>
  <data name="ScaleResolutionSettingsItem_From_DisplayName" xml:space="preserve">
    <value>Od (g)</value>
  </data>
  <data name="ScaleResolutionSettingsItem_From_Description" xml:space="preserve">
    <value>Od jaké hmotnosti (včetně) je daná citlivost platná</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_ShowWhenCanteenNotWorking_DisplayName" xml:space="preserve">
    <value>Zobrazit jídelníček i když je výdejna mimo provoz</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_ShowWhenCanteenNotWorking_Description" xml:space="preserve">
    <value>Zobrazit jídelníček i když je výdejna mimo provoz</value>
  </data>
  <data name="ExplicitCashDeskPairingSettings_CashDeskAppInstallationId_DisplayName" xml:space="preserve">
    <value>Id zařízení Kasy</value>
  </data>
  <data name="ExplicitCashDeskPairingSettings_CashDeskAppInstallationId_Description" xml:space="preserve">
    <value>Id zařízení Kasy, s kterou bude Mobilní číšník zpárován.</value>
  </data>
  <data name="WaiterCashDeskBehaviourCashDeskPairingConfig_CashDeskPairingType_DisplayName" xml:space="preserve">
    <value>Způsob párování</value>
  </data>
  <data name="WaiterCashDeskBehaviourCashDeskPairingConfig_CashDeskPairingType_Description" xml:space="preserve">
    <value>Způsob, jakým číšnická Kasa nalezne odpovídající Kasu</value>
  </data>
  <data name="WaiterCashDeskBehaviourCashDeskPairingConfig_Settings_DisplayName" xml:space="preserve">
    <value>Nastavení</value>
  </data>
  <data name="WaiterCashDeskBehaviourCashDeskPairingConfig_Settings_Description" xml:space="preserve">
    <value>Detailní nastavení</value>
  </data>
  <data name="CashDeskDataStorageConfig_StorageType_DisplayName" xml:space="preserve">
    <value>Uložiště dat</value>
  </data>
  <data name="CashDeskDataStorageConfig_StorageType_Description" xml:space="preserve">
    <value>Kde budou uloženy rozmarkované doklady</value>
  </data>
  <data name="CashDeskDataStorageConfig_DataStorageSettings_DisplayName" xml:space="preserve">
    <value>Nastavení</value>
  </data>
  <data name="CashDeskDataStorageConfig_DataStorageSettings_Description" xml:space="preserve">
    <value>Detailní nastavení</value>
  </data>
  <data name="KreditDataStorageSettings_ObserveChanges_DisplayName" xml:space="preserve">
    <value>Sledovat změny</value>
  </data>
  <data name="KreditDataStorageSettings_ObserveChanges_Description" xml:space="preserve">
    <value>Pokud je povoleno, Kasa bude sledovat změny v databázi a aktualizovat stav otevřených dokladů. Má smysl pro restaurační Kasu, kdy chceme, aby více hlavních Kas sdílelo stejné doklady. Obě Kasy musí mít stejné ID zařízení.</value>
  </data>
  <data name="CashDeskBehaviourRestaurantConfig_AllowMultipleParagonOnChair_DisplayName" xml:space="preserve">
    <value>Množsví povolených paragonů na jednu židli</value>
  </data>
  <data name="CashDeskBehaviourRestaurantConfig_AllowMultipleParagonOnChair_Description" xml:space="preserve">
    <value>Povolení, zda na jednu židli může být aplikován jeden paragon, více paragonů nebo je zakázáno vytvářet k židli paragony.</value>
  </data>
  <data name="CashDeskBehaviourRestaurantConfig_AllowNewParagonOnChair_DisplayName" xml:space="preserve">
    <value>Založení nového paragonu pro židli bez paragonu při výběru paragonu</value>
  </data>
  <data name="CashDeskBehaviourRestaurantConfig_AllowNewParagonOnChair_Description" xml:space="preserve">
    <value>Povolení, zda při výběru paragonu lze na židli bez paragonu založit nový paragon. Množství paragonu na židli záloveň nesmí být nastaveno na hodnotu Žádný paragon.</value>
  </data>
  <data name="CashDeskBehaviourRestaurantConfig_AllowMultipleParagonOnTable_DisplayName" xml:space="preserve">
    <value>Množsví povolených paragonů na jeden stůl</value>
  </data>
  <data name="CashDeskBehaviourRestaurantConfig_AllowMultipleParagonOnTable_Description" xml:space="preserve">
    <value>Povolení, zda na jeden stůl může být aplikován jeden paragon, více paragonů nebo je zakázáno vytvářet ke stolu paragony.</value>
  </data>
  <data name="CashDeskBehaviourRestaurantConfig_AllowNewParagonOnTable_DisplayName" xml:space="preserve">
    <value>Založení nového paragonu pro stůl bez paragonu při výběru paragonu</value>
  </data>
  <data name="CashDeskBehaviourRestaurantConfig_AllowNewParagonOnTable_Description" xml:space="preserve">
    <value>Povolení, zda při výběru paragonu lze na stůl bez paragonu založit nový paragon. Množství paragonu na stůl záloveň nesmí být nastaveno na hodnotu Žádný paragon.</value>
  </data>
  <data name="ClientAuthenticationSqlServerKreditSettings_UseImpersonationWindows_DisplayName" xml:space="preserve">
    <value>Použít impersonaci při windows autentizaci</value>
  </data>
  <data name="ClientAuthenticationSqlServerKreditSettings_UseImpersonationWindows_Description" xml:space="preserve">
    <value>Pokud se klient přihlásí pomocí Windows autentizace, tak se bude aplikační server připojovat k databázi Kredit pod tímto Windows účtem klienta místo účtu nastaveného pro aplikační server.

Funguje pouze pokud:
a) aplikační server běží na stejném serveru jako SQL Server
b) jsou provedena dodatečná nastavení v doméně ze strany zákazníka (povolení delegování impersonace pro PC, na kterém běží aplikační server). Viz HD62791</value>
  </data>
  <data name="ClientAuthenticationSettingsBase_CredentialTypes_DisplayName" xml:space="preserve">
    <value>Způsoby přihlášení</value>
  </data>
  <data name="ClientAuthenticationSettingsBase_CredentialTypes_Description" xml:space="preserve">
    <value>Povolené způsoby přihlášení</value>
  </data>
  <data name="ClientAuthenticationSettingsBase_IsEnabled_DisplayName" xml:space="preserve">
    <value>Povoleno</value>
  </data>
  <data name="ClientAuthenticationSettingsBase_IsEnabled_Description" xml:space="preserve">
    <value>Povoleno</value>
  </data>
  <data name="ClientAuthenticationSettingsBase_IsDefault_DisplayName" xml:space="preserve">
    <value>Implicitní</value>
  </data>
  <data name="ClientAuthenticationSettingsBase_IsDefault_Description" xml:space="preserve">
    <value>Je použito jako implicitní způsob autentizace</value>
  </data>
  <data name="GlobalAuthenticationClientConfig_AuthenticationSqlServerKredit_DisplayName" xml:space="preserve">
    <value>SQL Server Kredit</value>
  </data>
  <data name="GlobalAuthenticationClientConfig_AuthenticationSqlServerKredit_Description" xml:space="preserve">
    <value>Ověřování oproti SQL Serveru a databázi Kredit</value>
  </data>
  <data name="GlobalAuthenticationClientConfig_AuthenticationLdap_DisplayName" xml:space="preserve">
    <value>LDAP</value>
  </data>
  <data name="GlobalAuthenticationClientConfig_AuthenticationLdap_Description" xml:space="preserve">
    <value>Ověřování oproti LDAP serveru</value>
  </data>
  <data name="GlobalAuthenticationServerConfig_AuthenticationSqlServerKredit_DisplayName" xml:space="preserve">
    <value>SQL Server Kredit</value>
  </data>
  <data name="GlobalAuthenticationServerConfig_AuthenticationSqlServerKredit_Description" xml:space="preserve">
    <value>Ověřování oproti SQL Serveru a databázi Kredit</value>
  </data>
  <data name="GlobalAuthenticationServerConfig_AuthenticationLdap_DisplayName" xml:space="preserve">
    <value>LDAP</value>
  </data>
  <data name="GlobalAuthenticationServerConfig_AuthenticationLdap_Description" xml:space="preserve">
    <value>Ověřování oproti LDAP serveru</value>
  </data>
  <data name="ServerAuthenticationSettingsBase_IsEnabled_DisplayName" xml:space="preserve">
    <value>Povoleno</value>
  </data>
  <data name="ServerAuthenticationSettingsBase_IsEnabled_Description" xml:space="preserve">
    <value>Povoleno</value>
  </data>
  <data name="ServerAuthenticationLdapSettings_Server_DisplayName" xml:space="preserve">
    <value>Server</value>
  </data>
  <data name="ServerAuthenticationLdapSettings_Server_Description" xml:space="preserve">
    <value>Server</value>
  </data>
  <data name="SyncMappingSettingsBase_Key_DisplayName" xml:space="preserve">
    <value>Zdroj</value>
  </data>
  <data name="SyncMappingSettingsBase_Key_Description" xml:space="preserve">
    <value>Zdroj</value>
  </data>
  <data name="SyncMappingSettingsBase_AttributeType_DisplayName" xml:space="preserve">
    <value>Cíl</value>
  </data>
  <data name="SyncMappingSettingsBase_AttributeType_Description" xml:space="preserve">
    <value>Cíl</value>
  </data>
  <data name="SyncMappingSettingsBase_Settings_DisplayName" xml:space="preserve">
    <value>Nastavení</value>
  </data>
  <data name="SyncMappingSettingsBase_Settings_Description" xml:space="preserve">
    <value>Nastavení</value>
  </data>
  <data name="WebKreditGeneralConfig_ServerPort_DisplayName" xml:space="preserve">
    <value>Port serveru</value>
  </data>
  <data name="WebKreditGeneralConfig_ServerPort_Description" xml:space="preserve">
    <value>Port serveru</value>
  </data>
  <data name="PatientOrderingBehaviourGeneralConfig_ServerPort_DisplayName" xml:space="preserve">
    <value>Port serveru</value>
  </data>
  <data name="PatientOrderingBehaviourGeneralConfig_ServerPort_Description" xml:space="preserve">
    <value>Port serveru</value>
  </data>
  <data name="PatientsOrderingSettings_Prefix_DisplayName" xml:space="preserve">
    <value>Prefix</value>
  </data>
  <data name="PatientsOrderingSettings_Prefix_Description" xml:space="preserve">
    <value>Prefix, který se přidá před login vyplněný uživatelem</value>
  </data>
  <data name="CashDeskSaleConfig_IgnoreClientSubsidy_DisplayName" xml:space="preserve">
    <value>Ignorovat kategorii dotace klienta</value>
  </data>
  <data name="CashDeskSaleConfig_IgnoreClientSubsidy_Description" xml:space="preserve">
    <value>Pokud je povoleno, bude se ignorovat kategorie dotace klienta - jidla budou zobrazeny za plnou cenu. Vytvoreno kvuli TUK, ktery nastavuje spravnou kategorii dotace klienta az na konci dne. Obsluha tak po prilhaseni klienta potrebuje videt jidla za plnou cenou.</value>
  </data>
  <data name="BulkOrderingSettings_AllowAccessRightsAdmin_DisplayName" xml:space="preserve">
    <value>Povolit správu přístupových práv</value>
  </data>
  <data name="BulkOrderingSettings_AllowAccessRightsAdmin_Description" xml:space="preserve">
    <value>Povolit správu přístupových práv</value>
  </data>
  <data name="WebKreditHeaderConfig_EnablePasswordRecovery_DisplayName" xml:space="preserve">
    <value>Povolit obnovení hesla</value>
  </data>
  <data name="WebKreditHeaderConfig_EnablePasswordRecovery_Description" xml:space="preserve">
    <value>Povolit obnovení hesla.
Je nutné zároveň nastavit sekci Odesílání emailů (WebKredit -&gt; Mailing).</value>
  </data>
  <data name="InfoProServerConfig_ServerPort_DisplayName" xml:space="preserve">
    <value>Port serveru</value>
  </data>
  <data name="InfoProServerConfig_ServerPort_Description" xml:space="preserve">
    <value>Port serveru</value>
  </data>
  <data name="FioAboEBankingSettings_BankCode_Description" xml:space="preserve">
    <value>Kód banky ve tvaru 0000</value>
  </data>
  <data name="FioAboEBankingSettings_BankCode_DisplayName" xml:space="preserve">
    <value>Kód banky (tvar 0000)</value>
  </data>
  <data name="FioAboEBankingSettings_MandatorName_Description" xml:space="preserve">
    <value>Název toho, jehož jménem se příkaz odesílá</value>
  </data>
  <data name="FioAboEBankingSettings_MandatorName_DisplayName" xml:space="preserve">
    <value>Jméno příkazce</value>
  </data>
  <data name="UsersMappingAttributeExtendedSettings_DestinationKey_DisplayName" xml:space="preserve">
    <value>Cíl - klíč</value>
  </data>
  <data name="UsersMappingAttributeExtendedSettings_DestinationKey_Description" xml:space="preserve">
    <value>Cíl - klíč</value>
  </data>
  <data name="UsersMappingAttributeExtendedSettings_DestinationValue_DisplayName" xml:space="preserve">
    <value>Cíl - hodnota</value>
  </data>
  <data name="UsersMappingAttributeExtendedSettings_DestinationValue_Description" xml:space="preserve">
    <value>Cíl - hodnota</value>
  </data>
  <data name="SyncMappingAttributeConditionSettings_SourceValue_DisplayName" xml:space="preserve">
    <value>Zdroj - hodnota</value>
  </data>
  <data name="SyncMappingAttributeConditionSettings_SourceValue_Description" xml:space="preserve">
    <value>Zdroj - hodnota</value>
  </data>
  <data name="GlobalHwPaymentTermConfig_ReplyTimeout_DisplayName" xml:space="preserve">
    <value>Doba čekání na odpověď terminálu</value>
  </data>
  <data name="GlobalHwPaymentTermConfig_ReplyTimeout_Description" xml:space="preserve">
    <value>Jak dloubo se bude čekat na odpověď terminálu? Po uplynutí této doby se platba označí za neprovedenou nebo se zobrazí dialog pro zaznamenání offline platby.</value>
  </data>
  <data name="GlobalServicesAppServerConnectionConfig_DisableDiagnosticLogging_DisplayName" xml:space="preserve">
    <value>Vypnout diagnostické logování</value>
  </data>
  <data name="GlobalServicesAppServerConnectionConfig_DisableDiagnosticLogging_Description" xml:space="preserve">
    <value>Vypne rozšířené logování.
Nutno vypnout při použití mobilního číšníka, jinak aplikační server denně generuje gigabajtové logy.</value>
  </data>
  <data name="KreditDataStorageSettings_MobileCashierEnabled_DisplayName" xml:space="preserve">
    <value>Mobilní číšník</value>
  </data>
  <data name="KreditDataStorageSettings_MobileCashierEnabled_Description" xml:space="preserve">
    <value>Povolit synchronizace s Mobilním číšníkem? Pokud je povoleno, Kasa na pozadí komunikuje s AS. Přenaší stav dokladů do Mobilního číšníka a zároveň zpracovává jeho požadavky.</value>
  </data>
  <data name="CashDeskBehaviourNotesConfig_Delimiter_Description" xml:space="preserve">
    <value>Nastavení znaku pro oddělování poznámek. Znak musí být typu char!</value>
  </data>
  <data name="CashDeskBehaviourNotesConfig_Delimiter_DisplayName" xml:space="preserve">
    <value>Oddělovač poznámek</value>
  </data>
  <data name="KreditDataStorageSettings_MobileCashierSyncInterval_DisplayName" xml:space="preserve">
    <value>Interval [ms] synchronizace s Mobilním číšníkem</value>
  </data>
  <data name="KreditDataStorageSettings_MobileCashierSyncInterval_Description" xml:space="preserve">
    <value>Nižší hodnota způsobí rychlejší odezvu Mobilního čísníka a zároveň více vytíží Kasu i Aplikační server.</value>
  </data>
  <data name="GlobalBehaviourKitchenOrderPrintConfig_PrintPreOrderFoodEnabled_Description" xml:space="preserve">
    <value>Povolení tisku předem objednaných jídel na kuchyňské tiskárně.</value>
  </data>
  <data name="GlobalBehaviourKitchenOrderPrintConfig_PrintPreOrderFoodEnabled_DisplayName" xml:space="preserve">
    <value>Povolení tisku předem objednaných jídel</value>
  </data>
  <data name="GlobalBehaviourSalesSlipPrintConfig_IsStackingPrintEnabled_Description" xml:space="preserve">
    <value>Povolení slučování položek při tisku paragonu. Pokud nebude tento parametr povolen budou všechny položky tisknuty tak jak byly markovány.</value>
  </data>
  <data name="GlobalBehaviourSalesSlipPrintConfig_IsStackingPrintEnabled_DisplayName" xml:space="preserve">
    <value>Povolit slučování položek při tisku</value>
  </data>
  <data name="GlobalBehaviourKitchenOrderDisplayConfig_DisplayEnabled_DisplayName" xml:space="preserve">
    <value>Používat displej</value>
  </data>
  <data name="GlobalBehaviourKitchenOrderDisplayConfig_DisplayEnabled_Description" xml:space="preserve">
    <value>Povolením použití displejů se v Kanceláři zpřístupní obsluze nastavení externího displeje v jídelníčku a touch skupinách. Zároveň Kasa začne místo tisku do kuchyně používat displeje.</value>
  </data>
  <data name="GlobalBehaviourKitchenOrderPrintConfig_IsPrintClientNameEnabled_Description" xml:space="preserve">
    <value>Tento příznak povolí tisk jména strávníka na paragonu do kuchyně.</value>
  </data>
  <data name="GlobalBehaviourKitchenOrderPrintConfig_IsPrintClientNameEnabled_DisplayName" xml:space="preserve">
    <value>Povolení tisku jména strávníka do kuchyně</value>
  </data>
  <data name="GlobalBehaviourKitchenOrderPrintConfig_IsPrintCashierNameEnabled_Description" xml:space="preserve">
    <value>Tento příznak povolí tisk jména pokladní na paragonu do kuchyně.</value>
  </data>
  <data name="GlobalBehaviourKitchenOrderPrintConfig_IsPrintCashierNameEnabled_DisplayName" xml:space="preserve">
    <value>Povolení tisku jména pokladní do kuchyně</value>
  </data>
  <data name="SyncTypeSettingsBase_IsEnabled_DisplayName" xml:space="preserve">
    <value>Povoleno</value>
  </data>
  <data name="SyncTypeSettingsBase_IsEnabled_Description" xml:space="preserve">
    <value>Povoleno</value>
  </data>
  <data name="SyncTypeSettingsBase_Mappings_DisplayName" xml:space="preserve">
    <value>Mapování</value>
  </data>
  <data name="SyncTypeSettingsBase_Mappings_Description" xml:space="preserve">
    <value>Mapování atributů ze zdroje na sloupce tabulky</value>
  </data>
  <data name="SyncLdapSearchRequestConfig_BaseDN_DisplayName" xml:space="preserve">
    <value>BaseDN</value>
  </data>
  <data name="SyncLdapSearchRequestConfig_BaseDN_Description" xml:space="preserve">
    <value>Atribut BaseDN, který se používá jako kořen stromu pro vyhledávání.</value>
  </data>
  <data name="SyncLdapSearchRequestConfig_SearchRequest_DisplayName" xml:space="preserve">
    <value>Vyhledávací dotaz</value>
  </data>
  <data name="SyncLdapSearchRequestConfig_SearchRequest_Description" xml:space="preserve">
    <value>Vyhledávací dotaz ve formátu LDAP filtru</value>
  </data>
  <data name="SyncLdapSettings_ServerConfig_DisplayName" xml:space="preserve">
    <value>LDAP server</value>
  </data>
  <data name="SyncLdapSettings_ServerConfig_Description" xml:space="preserve">
    <value>Nastavení parametrů LDAP serveru</value>
  </data>
  <data name="SyncLdapSettings_ServiceUserCredentials_DisplayName" xml:space="preserve">
    <value>Přihlášení servisního uživatele</value>
  </data>
  <data name="SyncLdapSettings_ServiceUserCredentials_Description" xml:space="preserve">
    <value>Uživatel, pod kterým probíhá vyhledávání v LDAP</value>
  </data>
  <data name="SyncLdapSettings_SearchRequestConfig_DisplayName" xml:space="preserve">
    <value>Vyhledávání ve stromu</value>
  </data>
  <data name="SyncLdapSettings_SearchRequestConfig_Description" xml:space="preserve">
    <value>Nastavení parametrů pro vyhledávání v LDAP stromu</value>
  </data>
  <data name="ClientsMappingAttributeClientGroupSettings_DestinationValue_DisplayName" xml:space="preserve">
    <value>Id skupiny</value>
  </data>
  <data name="ClientsMappingAttributeClientGroupSettings_DestinationValue_Description" xml:space="preserve">
    <value>Id skupiny</value>
  </data>
  <data name="ClientsMappingAttributeOrganizationSettings_DestinationValue_DisplayName" xml:space="preserve">
    <value>Kód organizace</value>
  </data>
  <data name="ClientsMappingAttributeOrganizationSettings_DestinationValue_Description" xml:space="preserve">
    <value>Kód organizace</value>
  </data>
  <data name="ClientsMappingAttributeResortSettings_DestinationValue_DisplayName" xml:space="preserve">
    <value>Kód střediska</value>
  </data>
  <data name="ClientsMappingAttributeResortSettings_DestinationValue_Description" xml:space="preserve">
    <value>Kód střediska</value>
  </data>
  <data name="SyncMappingAttributeConditionSettings_MatchType_DisplayName" xml:space="preserve">
    <value>Způsob porovnání hodnot</value>
  </data>
  <data name="SyncMappingAttributeConditionSettings_MatchType_Description" xml:space="preserve">
    <value>Způsob porovnání hodnot</value>
  </data>
  <data name="ISyncTypeLdapSettings_Server_DisplayName" xml:space="preserve">
    <value>Server</value>
  </data>
  <data name="ISyncTypeLdapSettings_Server_Description" xml:space="preserve">
    <value>Server</value>
  </data>
  <data name="GlobalSyncUsersConfig_LdapServer_DisplayName" xml:space="preserve">
    <value>LDAP Server</value>
  </data>
  <data name="GlobalSyncUsersConfig_LdapServer_Description" xml:space="preserve">
    <value>Načítání seznamu uživatelů přímo z LDAP serveru pomocí servisního účtu</value>
  </data>
  <data name="GlobalSyncUsersConfig_Table_DisplayName" xml:space="preserve">
    <value>Tabulka Kredit.dbo.ACC_Sync_Uzivatel</value>
  </data>
  <data name="GlobalSyncUsersConfig_Table_Description" xml:space="preserve">
    <value>Načítání seznamu uživatelů z tabulky Kredit.dbo.ACC_Sync_Uzivatel</value>
  </data>
  <data name="GlobalSyncClientsConfig_LdapServer_DisplayName" xml:space="preserve">
    <value>LDAP Server</value>
  </data>
  <data name="GlobalSyncClientsConfig_LdapServer_Description" xml:space="preserve">
    <value>Načítání seznamu strávníků přímo z LDAP serveru pomocí servisního účtu</value>
  </data>
  <data name="SyncMappingSettingsBase_Transform_DisplayName" xml:space="preserve">
    <value>Transformace</value>
  </data>
  <data name="SyncMappingSettingsBase_Transform_Description" xml:space="preserve">
    <value>Transformace</value>
  </data>
  <data name="ClientsSyncSettingsBase_RunZrd_DisplayName" xml:space="preserve">
    <value>Spustit změnové řízení</value>
  </data>
  <data name="ClientsSyncSettingsBase_RunZrd_Description" xml:space="preserve">
    <value>Po načtení strávníků do tabulky dba.ZRD spustí proceduru dba.ZRD_Standart.
Možnost vypnutí je hlavně pro testovací účely.</value>
  </data>
  <data name="SyncMappingSettingsBase_Conditions_DisplayName" xml:space="preserve">
    <value>Podmínky</value>
  </data>
  <data name="SyncMappingSettingsBase_Conditions_Description" xml:space="preserve">
    <value>Podmínky</value>
  </data>
  <data name="SyncMappingAttributeConditionSettings_SourceKey_DisplayName" xml:space="preserve">
    <value>Zdroj</value>
  </data>
  <data name="SyncMappingAttributeConditionSettings_SourceKey_Description" xml:space="preserve">
    <value>Zdroj</value>
  </data>
  <data name="SyncMappingAttributeActiveSettings_Type_DisplayName" xml:space="preserve">
    <value>Způsob zjištění platnosti účtu</value>
  </data>
  <data name="SyncMappingAttributeActiveSettings_Type_Description" xml:space="preserve">
    <value>Způsob zjištění platnosti účtu</value>
  </data>
  <data name="SyncMappingAttributeTransformSettings_Type_DisplayName" xml:space="preserve">
    <value>Typ</value>
  </data>
  <data name="SyncMappingAttributeTransformSettings_Type_Description" xml:space="preserve">
    <value>Typ</value>
  </data>
  <data name="OfficeWorkplacesBehaviourImportNisConfig_LogEnable_DisplayName" xml:space="preserve">
    <value>Povolit zobrazení protokolu</value>
  </data>
  <data name="OfficeWorkplacesBehaviourImportNisConfig_LogEnable_Description" xml:space="preserve">
    <value>Povolit zobrazení protokolu</value>
  </data>
  <data name="OfficeWorkplacesBehaviourMenuConfig_AllowMenuEditInClosedPeriod_DisplayName" xml:space="preserve">
    <value>Povolit editaci jídelníčku v uzavřeném období</value>
  </data>
  <data name="OfficeWorkplacesBehaviourMenuConfig_AllowMenuEditInClosedPeriod_Description" xml:space="preserve">
    <value>Povolit editaci jídelníčku v uzavřeném období</value>
  </data>
  <data name="WaiterCashDeskBehaviourSynchronizationConfig_SyncInterval_DisplayName" xml:space="preserve">
    <value>Interval synchronizace [ms]</value>
  </data>
  <data name="WaiterCashDeskBehaviourSynchronizationConfig_SyncInterval_Description" xml:space="preserve">
    <value>Interval [ms] synchronizace Mobilního číšníka s Kasou. Nižší hodnota vede k rychlejším odezvám při markování, ale zároveň více zatězuje síť a mobilní aplikaci.</value>
  </data>
  <data name="WaiterCashDeskBehaviourSynchronizationConfig_ExceptionTimeout_DisplayName" xml:space="preserve">
    <value>Restart pri chybe [ms]</value>
  </data>
  <data name="WaiterCashDeskBehaviourSynchronizationConfig_ExceptionTimeout_Description" xml:space="preserve">
    <value>Doba [ms], po ktere dojde k restartu sychnronizacniho vlakne v pripade neocekavane chyby</value>
  </data>
  <data name="ServiceMonitorBehaviourLatencyMonitorConfig_Enabled_DisplayName" xml:space="preserve">
    <value>Monitorovat odezvu sítě</value>
  </data>
  <data name="ServiceMonitorBehaviourLatencyMonitorConfig_Enabled_Description" xml:space="preserve">
    <value>Pokud je povoleno, Monitor služeb spustí na pozadí vlákna, které logují rychlost odezvy AS a SQL serveru.</value>
  </data>
  <data name="SchedulerJobConfig_TriggerConfig_DisplayName" xml:space="preserve">
    <value>Nastavení časovače</value>
  </data>
  <data name="SchedulerJobConfig_TriggerConfig_Description" xml:space="preserve">
    <value>Nastavení časovače</value>
  </data>
  <data name="SchedulerJobConfig_FailedJobConfig_DisplayName" xml:space="preserve">
    <value>Opakování při chybě</value>
  </data>
  <data name="SchedulerJobConfig_FailedJobConfig_Description" xml:space="preserve">
    <value>Opakování při chybě</value>
  </data>
  <data name="SchedulerPrefixesAxxosConfig_SyncClients_DisplayName" xml:space="preserve">
    <value>Synchronizace strávníků</value>
  </data>
  <data name="SchedulerPrefixesAxxosConfig_SyncClients_Description" xml:space="preserve">
    <value>Synchronizace strávníků</value>
  </data>
  <data name="SchedulerPrefixesAxxosConfig_SyncSalesSlips_DisplayName" xml:space="preserve">
    <value>Synchronizace paragonů</value>
  </data>
  <data name="SchedulerPrefixesAxxosConfig_SyncSalesSlips_Description" xml:space="preserve">
    <value>Synchronizace paragonů</value>
  </data>
  <data name="SchedulerPrefixesAxxosConfig_SyncAccountBalances_DisplayName" xml:space="preserve">
    <value>Synchronizace uzávěrek</value>
  </data>
  <data name="SchedulerPrefixesAxxosConfig_SyncAccountBalances_Description" xml:space="preserve">
    <value>Synchronizace uzávěrek</value>
  </data>
  <data name="SchedulerPrefixesGerecConfig_Sync_DisplayName" xml:space="preserve">
    <value>Synchronizace</value>
  </data>
  <data name="SchedulerPrefixesGerecConfig_Sync_Description" xml:space="preserve">
    <value>Synchronizace strávníků, uživatelů a rezervací</value>
  </data>
  <data name="SchedulerPluginsAppServerConfig_SyncUsers_DisplayName" xml:space="preserve">
    <value>Synchronizace uživatelů</value>
  </data>
  <data name="SchedulerPluginsAppServerConfig_SyncUsers_Description" xml:space="preserve">
    <value>Synchronizace uživatelů</value>
  </data>
  <data name="SchedulerPrefixesGerecConfig_ConnectionString_DisplayName" xml:space="preserve">
    <value>Connection string</value>
  </data>
  <data name="SchedulerPrefixesGerecConfig_ConnectionString_Description" xml:space="preserve">
    <value>Connection string pro připojení k databází obsahující tabulky se strávníky, uživateli a rezervacemi</value>
  </data>
  <data name="SchedulerPrefixesGerecConfig_CopyOnly_DisplayName" xml:space="preserve">
    <value>Pouze zkopírovat tabulky</value>
  </data>
  <data name="SchedulerPrefixesGerecConfig_CopyOnly_Description" xml:space="preserve">
    <value>Pouze zkopíruje tabulky ze vzdáleného serveru do db Kreditu.
Nespustí se ZRD a nebudou se měnit tabulky us.Ubytovany a us.Rezervace.</value>
  </data>
  <data name="SchedulerPluginsAppServerConfig_SyncClients_DisplayName" xml:space="preserve">
    <value>Synchronizace strávníků - vše</value>
  </data>
  <data name="SchedulerPluginsAppServerConfig_SyncClients_Description" xml:space="preserve">
    <value>Synchronizace strávníků - vše</value>
  </data>
  <data name="SchedulerPluginsAppServerConfig_SyncExchangeRate_DisplayName" xml:space="preserve">
    <value>Synchronizace kurzů</value>
  </data>
  <data name="SchedulerPluginsAppServerConfig_SyncExchangeRate_Description" xml:space="preserve">
    <value>Synchronizace kurzů</value>
  </data>
  <data name="SchedulerPluginsAppServerConfig_SyncClientsChangesOnly_DisplayName" xml:space="preserve">
    <value>Synchronizace strávníků - pouze změny</value>
  </data>
  <data name="SchedulerPluginsAppServerConfig_SyncClientsChangesOnly_Description" xml:space="preserve">
    <value>Synchronizace strávníků - pouze změny</value>
  </data>
  <data name="SyncMappingAttributeTransformCharactersSettings_Count_DisplayName" xml:space="preserve">
    <value>Počet znaků</value>
  </data>
  <data name="SyncMappingAttributeTransformCharactersSettings_Count_Description" xml:space="preserve">
    <value>Počet znaků</value>
  </data>
  <data name="SyncMappingAttributeTransformSettings_Settings_DisplayName" xml:space="preserve">
    <value>Nastavení</value>
  </data>
  <data name="SyncMappingAttributeTransformSettings_Settings_Description" xml:space="preserve">
    <value>Nastavení</value>
  </data>
  <data name="CashDeskBehaviourBalanceSaleSlipConfig_BalanceMode_DisplayName" xml:space="preserve">
    <value>Vyrovnávat</value>
  </data>
  <data name="CashDeskBehaviourBalanceSaleSlipConfig_BalanceMode_Description" xml:space="preserve">
    <value>Způsob vyrovnávání paragonu</value>
  </data>
  <data name="MobileOrderingBehaviourPriceSetupConfig_VisiblePrices_Description" xml:space="preserve">
    <value>Určuje v jaké cenové kategorii budou zobrazeny ceny jídel přihlášenému klientovi.</value>
  </data>
  <data name="MobileOrderingBehaviourPriceSetupConfig_VisiblePrices_DisplayName" xml:space="preserve">
    <value>Zobrazení cen</value>
  </data>
  <data name="EKasaPortosSettings_VoucherIdPlatidlaDruhy_DisplayName" xml:space="preserve">
    <value>ID slevové poukázky</value>
  </data>
  <data name="EKasaPortosSettings_VoucherIdPlatidlaDruhy_Description" xml:space="preserve">
    <value>Obsahuje id platidla dle PlatidlaDruhy (sloupec druh). Pokud se zaplatí tímto platidlem, je zadaná částka použita jako 'uplatnenie viacúčelového poukazu vo forme položky'. Tzn. že do eKasy se pošle celková částka za doklad ponížená o hodnotu této poukázky. Jedná se o požadavek NBS, nicméně dá se použít u jakéhokoliv zákazníka.</value>
  </data>
  <data name="OfficeBehaviourBalanceConfig_EnableBalanceProvidersPreparedCheck_DisplayName" xml:space="preserve">
    <value>Povolit kontrolu připravenosti provozovatelů</value>
  </data>
  <data name="OfficeBehaviourBalanceConfig_EnableBalanceProvidersPreparedCheck_Description" xml:space="preserve">
    <value>Povolit kontrolu, zda jednotliví provozovatelé jsou připraveni k uzávěrce (příznak připravenosti nastavují v tabulce dba.UzavreniObdobi jednotliví provozovatelé).</value>
  </data>
  <data name="WebKreditAuthenticationConfig_TwoFactorAuthentication_Description" xml:space="preserve">
    <value>Způsob dvoufaktorové autentizace</value>
  </data>
  <data name="WebKreditAuthenticationConfig_TwoFactorAuthentication_DisplayName" xml:space="preserve">
    <value>Dvoufaktorová autentizace</value>
  </data>
  <data name="SchedulerPluginsHelpDeskSyncConfig_EnableTwoWaySync_DisplayName" xml:space="preserve">
    <value>Povolit obousměrnou synchronizaci</value>
  </data>
  <data name="SchedulerPluginsHelpDeskSyncConfig_EnableTwoWaySync_Description" xml:space="preserve">
    <value>Povolit obousměrnou synchronizaci (i směrem z helpdesku k zákazníkovi)</value>
  </data>
  <data name="AxaProPaymentTermSettings_Port_Description" xml:space="preserve">
    <value>Číslo portu na kterém terminál naslouchá.</value>
  </data>
  <data name="AxaProPaymentTermSettings_Address_Description" xml:space="preserve">
    <value>IP adresa nebo název, který jí odpovídá.</value>
  </data>
  <data name="AxaProPaymentTermSettings_Address_DisplayName" xml:space="preserve">
    <value>IP adresa</value>
  </data>
  <data name="AxaProPaymentTermSettings_DestinationDeviceName_Description" xml:space="preserve">
    <value>Alfa-numerický identifikátor terminálu s kterým se bude komunikovat. Identifikátor je třeba získat přímo s nastavení terminalu. Maximálne 16 znaků. </value>
  </data>
  <data name="AxaProPaymentTermSettings_DestinationDeviceName_DisplayName" xml:space="preserve">
    <value>Id terminálu</value>
  </data>
  <data name="AxaProPaymentTermSettings_Port_DisplayName" xml:space="preserve">
    <value>Port</value>
  </data>
  <data name="AxaProPaymentTermSettings_SourceDeviceName_Description" xml:space="preserve">
    <value>Alfa-numerický identifikátor zařízení, které bude zahajovat komunikaci s terminálem. Identifikátor je získán s Id-hardware-u * Id zarízení. Maximálne 16 znaků.</value>
  </data>
  <data name="AxaProPaymentTermSettings_SourceDeviceName_DisplayName" xml:space="preserve">
    <value>Id komunikujícího zarízení</value>
  </data>
  <data name="PresPointBehaviourUserInterfaceConfig_UseFormGradient_DisplayName" xml:space="preserve">
    <value>Používat gradient pro pozadí formuláře</value>
  </data>
  <data name="PresPointBehaviourUserInterfaceConfig_UseFormGradient_Description" xml:space="preserve">
    <value>Používání gradientu (barevného přechodu) na pozadí formulářů. Vypnuto = rychlejší vykreslování. </value>
  </data>
  <data name="MobileOrderingBehaviourWebKreditConfig_WebKreditRedirectUrl_DisplayName" xml:space="preserve">
    <value>Přesměrovat na adresu webkreditu</value>
  </data>
  <data name="GlobalBehaviourKitchenOrderPrintConfig_PrintOrder_Description" xml:space="preserve">
    <value>Určuje, jak budou řazeny položky objednávky do kuchyně</value>
  </data>
  <data name="GlobalBehaviourKitchenOrderPrintConfig_PrintOrder_DisplayName" xml:space="preserve">
    <value>Řazení položek objednávky do kuchyně</value>
  </data>
  <data name="GlobalBehaviourSalesSlipPrintConfig_IsPrintWebKreditUsernameEnabled_Description" xml:space="preserve">
    <value>Na paragonu bude obsaženo uživatelské jméno, ve kterém jsou obsaženy číšla pokoje(ů).</value>
  </data>
  <data name="GlobalBehaviourSalesSlipPrintConfig_IsPrintWebKreditUsernameEnabled_DisplayName" xml:space="preserve">
    <value>Tiskout uživatelské jméno dle WebKredit</value>
  </data>
  <data name="GlobalBehaviourKitchenOrderPrintConfig_IsFastPrintEnabled_Description" xml:space="preserve">
    <value>Povolení zobrazení souhrného dialogu při tisku objednávky do kuchyně. Pokud je zapnuto není možné editovat položky tisku a tisknou se data vždy jak jsou přednastavená. Platí pouze při nastavení parametru tisku "Na pokyn uživatele a na uložení paragonu".</value>
  </data>
  <data name="GlobalBehaviourKitchenOrderPrintConfig_IsFastPrintEnabled_DisplayName" xml:space="preserve">
    <value>Rychlý tisk</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_HideMealKindWithOnlySharedMeal_DisplayName" xml:space="preserve">
    <value>Filtr - druhy jídla obsahující pouze společné jídlo</value>
  </data>
  <data name="WebKreditOrderingMenuBaseConfig_HideMealKindWithOnlySharedMeal_Description" xml:space="preserve">
    <value>Filtrovat druhy jídla, které obsahují pouze společné jídlo</value>
  </data>
  <data name="MobileOrderingBehaviourWebKreditConfig_IsPaymentEnabled_DisplayName" xml:space="preserve">
    <value>Povolení dobíjení kreditu pomocí platební brány</value>
  </data>
  <data name="GlobalBehaviourKitchenOrderPrintConfig_IsPrintPositionRowWithLargerFont_DisplayName" xml:space="preserve">
    <value>Tisk umístění větším písmem</value>
  </data>
  <data name="GlobalBehaviourKitchenOrderPrintConfig_IsPrintPositionRowWithLargerFont_Description" xml:space="preserve">
    <value>Tisk umístění (úsek-stůl) větším písmem</value>
  </data>
  <data name="WaiterCashDeskGeneralConfig_IsEditSaleSlipEnabled_Description" xml:space="preserve">
    <value>Povolit editaci paragonu</value>
  </data>
  <data name="WaiterCashDeskGeneralConfig_IsEditSaleSlipEnabled_DisplayName" xml:space="preserve">
    <value>Editace paragonu</value>
  </data>
  <data name="AxaProPaymentTermSettings_PrintType_Description" xml:space="preserve">
    <value>Po úspešném prodeji se budou tisknout dokumenty podle zvoleného nastavění. Buď oba dokumenty, jeden pro obchodníka i zákazníka nebo iba jeden dokument pro obchodníka nebo jeden dokument pro zákazníka.</value>
  </data>
  <data name="AxaProPaymentTermSettings_PrintType_DisplayName" xml:space="preserve">
    <value>Tisk dokumentov z terminálu</value>
  </data>
  <data name="PresPointBehaviourMenuConfig_RestrictExchangeToWorkplace_DisplayName" xml:space="preserve">
    <value>Omezit burzu dle provozovny</value>
  </data>
  <data name="PresPointBehaviourMenuConfig_RestrictExchangeToWorkplace_Description" xml:space="preserve">
    <value>Omezí burzu jen na výdejny příslušející provozovně daného PM.</value>
  </data>
  <data name="SyncMappingAttributeTransformRegexSettings_Find_DisplayName" xml:space="preserve">
    <value>Najít</value>
  </data>
  <data name="SyncMappingAttributeTransformRegexSettings_Find_Description" xml:space="preserve">
    <value>Najít</value>
  </data>
  <data name="SyncMappingAttributeTransformRegexSettings_Replace_DisplayName" xml:space="preserve">
    <value>Nahradit</value>
  </data>
  <data name="SyncMappingAttributeTransformRegexSettings_Replace_Description" xml:space="preserve">
    <value>Nahradit</value>
  </data>
  <data name="PresPointBehaviourUserInterfaceConfig_UseThemeButtonGradient_DisplayName" xml:space="preserve">
    <value>Používat gradient pro pozadí tlačítek</value>
  </data>
  <data name="PresPointBehaviourUserInterfaceConfig_UseThemeButtonGradient_Description" xml:space="preserve">
    <value>Používání gradientu (barevného přechodu) na pozadí tlačítek. Vypnuto = rychlejší vykreslování. </value>
  </data>
  <data name="SyncTypeSettingsBase_Filter_DisplayName" xml:space="preserve">
    <value>Filtr</value>
  </data>
  <data name="SyncTypeSettingsBase_Filter_Description" xml:space="preserve">
    <value>Filtr</value>
  </data>
  <data name="GlobalAuthenticationServerConfig_AuthenticationKredit_DisplayName" xml:space="preserve">
    <value>Kredit</value>
  </data>
  <data name="GlobalAuthenticationServerConfig_AuthenticationKredit_Description" xml:space="preserve">
    <value>Autentizace vůči databázové tabulce ACC_AutentizaceUzivatele v databázi Kredit </value>
  </data>
  <data name="GlobalAuthenticationClientConfig_AuthenticationKredit_DisplayName" xml:space="preserve">
    <value>Kredit</value>
  </data>
  <data name="GlobalAuthenticationClientConfig_AuthenticationKredit_Description" xml:space="preserve">
    <value>Autentizace vůči databázové tabulce ACC_AutentizaceUzivatele v databázi Kredit </value>
  </data>
  <data name="SchedulerPluginsAppServerConfig_RelocateLogs_DisplayName" xml:space="preserve">
    <value>Přemísťování logů</value>
  </data>
  <data name="SchedulerPluginsAppServerConfig_RelocateLogs_Description" xml:space="preserve">
    <value>Přemísťení logů z dočasných tabulek do trvalých. Tyto tabulky již jsou indexovány a dá se v nich díky tomu i rozumně vyhledávat.</value>
  </data>
  <data name="SchedulerPluginsAppServerConfig_SendLogsToSysLog_DisplayName" xml:space="preserve">
    <value>Odesílání logů do SysLog</value>
  </data>
  <data name="SchedulerPluginsAppServerConfig_SendLogsToSysLog_Description" xml:space="preserve">
    <value>Provádí odesílání logů z trvalých tabulek na SysLog server. SysLog server musí být nakonfigurován v Global.Services.SysLog.</value>
  </data>
  <data name="GlobalServicesSmtpConfig_LocalHostName_DisplayName" xml:space="preserve">
    <value>Název odesilatele</value>
  </data>
  <data name="GlobalServicesSmtpConfig_LocalHostName_Description" xml:space="preserve">
    <value>Název odesilatele. Pokud není vyplněno, použije se přímo název počítače.</value>
  </data>
  <data name="GlobalServicesSmtpConfig_SysLogProtocol_DisplayName" xml:space="preserve">
    <value>Protokol</value>
  </data>
  <data name="GlobalServicesSmtpConfig_SysLogProtocol_Description" xml:space="preserve">
    <value>Použitý protokol</value>
  </data>
  <data name="GlobalServicesSmtpConfig_SysLogVersion_DisplayName" xml:space="preserve">
    <value>Verze protokolu</value>
  </data>
  <data name="GlobalServicesSmtpConfig_SysLogVersion_Description" xml:space="preserve">
    <value>Verze protokolu</value>
  </data>
  <data name="GlobalServicesSmtpConfig_SysLogServerHostname_DisplayName" xml:space="preserve">
    <value>Adresa serveru</value>
  </data>
  <data name="GlobalServicesSmtpConfig_SysLogServerHostname_Description" xml:space="preserve">
    <value>Adresa serveru</value>
  </data>
  <data name="GlobalServicesSmtpConfig_SysLogServerPort_DisplayName" xml:space="preserve">
    <value>Port</value>
  </data>
  <data name="GlobalServicesSmtpConfig_SysLogServerPort_Description" xml:space="preserve">
    <value>Pork, na kterém naslouchá server. Pro UDP se obvykle jedná o port 514, pro TPC 601.</value>
  </data>
  <data name="GlobalSyncAccessRightsConfig_Ldap_DisplayName" xml:space="preserve">
    <value>LDAP</value>
  </data>
  <data name="GlobalSyncAccessRightsConfig_Ldap_Description" xml:space="preserve">
    <value>Nastavení pro uživatele, kteří jsou spravování LDAP synchronizací</value>
  </data>
  <data name="UserRightsForAuthSettings_AllowEditProfile_DisplayName" xml:space="preserve">
    <value>Povolit editaci profilu</value>
  </data>
  <data name="UserRightsForAuthSettings_AllowEditProfile_Description" xml:space="preserve">
    <value>Pokud je povoleno, může obsluha měnit profil uživatele v Kanceláři i u uživatelů, kteří jsou synchronizováni. Toto nastavení se netýká poznámky. Ta je editovatelná vždy.</value>
  </data>
  <data name="UserRightsForAuthSettings_AllowEditUserRights_DisplayName" xml:space="preserve">
    <value>Povolit editaci práv (přiřazení rolí)</value>
  </data>
  <data name="UserRightsForAuthSettings_AllowEditUserRights_Description" xml:space="preserve">
    <value>Pokud je povoleno, může obsluha měnit přístupová práva v Kanceláři i u uživatelů, kteří jsou synchronizováni.</value>
  </data>
  <data name="UserRightsForAuthSettings_AllowEditUserRightsParameters_DisplayName" xml:space="preserve">
    <value>Povolit editaci parametrů práv</value>
  </data>
  <data name="UserRightsForAuthSettings_AllowEditUserRightsParameters_Description" xml:space="preserve">
    <value>Pokud je povoleno, může obsluha měnit parametry práv v Kanceláři i u uživatelů, kteří jsou synchronizováni.</value>
  </data>
  <data name="LdapUserConfig_UserNameKreditFormat_DisplayName" xml:space="preserve">
    <value>Formát uživatelského jména - Kredit</value>
  </data>
  <data name="LdapUserConfig_UserNameKreditFormat_Description" xml:space="preserve">
    <value>Formátovací řetězec pro uživatelské jméno pod kterým proběhne pokus o přihlášení ke Kreditu.
Formátovací parametry (case-sensitive):
{UserName} = uživatelské jméno zadané klientem</value>
  </data>
  <data name="SyncMappingSettingsBase_Description_DisplayName" xml:space="preserve">
    <value> Popis</value>
  </data>
  <data name="SyncMappingSettingsBase_Description_Description" xml:space="preserve">
    <value>Popis</value>
  </data>
  <data name="WebKreditAuthenticationConfig_AuthenticationTypeAlt_DisplayName" xml:space="preserve">
    <value>Autentifikace alt</value>
  </data>
  <data name="WebKreditAuthenticationConfig_AuthenticationTypeAlt_Description" xml:space="preserve">
    <value>Alternativní typ autentifikace, která se použije, pokud se nepovede přihlásit pod hlavním typem</value>
  </data>
  <data name="WebKreditAuthenticationConfig_SettingsAlt_DisplayName" xml:space="preserve">
    <value>Autentifikace alt - nastavení</value>
  </data>
  <data name="WebKreditAuthenticationConfig_SettingsAlt_Description" xml:space="preserve">
    <value>Nastavení alternativní autentifikace</value>
  </data>
  <data name="CashDeskBehaviourSalesVoucherConfig_Enabled_DisplayName" xml:space="preserve">
    <value>Povoleno</value>
  </data>
  <data name="CashDeskBehaviourSalesVoucherConfig_Enabled_Description" xml:space="preserve">
    <value>Pokud je povoleno, Kasa testuje, zda text vyčtený z EAN čtečky není slevový voucher. Podmínkou je, aby byly na Kase v touch skipinách nakonfigurovány automatické slevy.</value>
  </data>
  <data name="CashDeskBehaviourSalesVoucherConfig_RegexPattern_DisplayName" xml:space="preserve">
    <value>Regulérní výraz pro načtení id slev</value>
  </data>
  <data name="CashDeskBehaviourSalesVoucherConfig_RegexPattern_Description" xml:space="preserve">
    <value>Regulérní vyraz, pomocí kterého se získá id slevy jídla a id slevy zboží. Výraz může být libovolný, ale musí obsahovat maximálně jednu pojmenovou skupinu IdSlevyJidla a IdSlevyZbozi. Kód lze vygenerovat v Kanceláři.
Implicitní nastavení vede pro kombinace id slevy jídla 1 a id slevy zboží těmto QR kódům:
SLEVA 1-2-2099184020
SLEVA 1--2600294513
SLEVA -2-1117457659</value>
  </data>
  <data name="OfficeCentralManagementBehaviourRdpConfig_RdpFolder_DisplayName" xml:space="preserve">
    <value>Složka s RDP soubory</value>
  </data>
  <data name="OfficeCentralManagementBehaviourRdpConfig_RdpFolder_Description" xml:space="preserve">
    <value>Složka v ktéré se nachází RDP soubory pro jednotlivé OJ. V cestě lze použít stejné symbolické výrazy, jako u logování: {localappdatadir}, {commonappdata}, atd.</value>
  </data>
  <data name="GlobalBehaviourAccessRightsAdministrationConfig_SetSecurityAdminDbRoleToAccessRightAdmin_DisplayName" xml:space="preserve">
    <value>Nastavit securityadmin správci uživatelských práv</value>
  </data>
  <data name="GlobalBehaviourAccessRightsAdministrationConfig_SetSecurityAdminDbRoleToAccessRightAdmin_Description" xml:space="preserve">
    <value>Databázová role securityadmin je nutná k nastavování práv SQL uživatelů nad všemi spravovanými databázemi. Nicméně někteří zákazníci tuto roli chtějí z bezpečnostních důvodů nastavovat ručně jen po dobu administrace uživatelských práv. Poté roli zase odeberou. Povolení nastavení securityadmin má smysl jen v případě, že zákazník používa SQL server autentizaci.</value>
  </data>
  <data name="GlobalBehaviourSalesSlipPrintConfig_PrintSalesSlipTableSection_Description" xml:space="preserve">
    <value>Příznak, který nastaví, zda se má tisknout stůl/židle na paragon.</value>
  </data>
  <data name="GlobalBehaviourSalesSlipPrintConfig_PrintSalesSlipTableSection_DisplayName" xml:space="preserve">
    <value>Tisknout stůl/židle</value>
  </data>
  <data name="GlobalSyncUsersConfig_DeleteNonexistentLogins_DisplayName" xml:space="preserve">
    <value>Smazat loginy neexistujícím uživatelům</value>
  </data>
  <data name="GlobalSyncUsersConfig_DeleteNonexistentLogins_Description" xml:space="preserve">
    <value>Pokud uživatel neexistuje ve zdroji, tak mu bude kromě nastavení neaktivity i smazán login.
Pro případy, kdy chce zákazník loginy recyklovat pro další uživatele.</value>
  </data>
  <data name="SyncMappingAttributeSettingsBase_DestinationValue_DisplayName" xml:space="preserve">
    <value>Hodnota</value>
  </data>
  <data name="SyncMappingAttributeSettingsBase_DestinationValue_Description" xml:space="preserve">
    <value>Hodnota</value>
  </data>
  <data name="SyncMappingAttributeSettingsBase_Value_DisplayName" xml:space="preserve">
    <value>Hodnota</value>
  </data>
  <data name="SyncMappingAttributeSettingsBase_Value_Description" xml:space="preserve">
    <value>Hodnota</value>
  </data>
  <data name="SyncMappingAttributeSettingsBase_Values_DisplayName" xml:space="preserve">
    <value>Hodnoty</value>
  </data>
  <data name="SyncMappingAttributeSettingsBase_Values_Description" xml:space="preserve">
    <value>Hodnoty</value>
  </data>
  <data name="CashDeskBehaviourSalesVoucherConfig_GenerationPattern_DisplayName" xml:space="preserve">
    <value>Šablona pro generování</value>
  </data>
  <data name="CashDeskBehaviourSalesVoucherConfig_GenerationPattern_Description" xml:space="preserve">
    <value>Podle této šablony se vygeneruje QR kód. V šabloně se musí použít symbolické proměnné {QR.IdSlevyJidla}, {QR.IdSlevyZbozi} a {QR.CRC}.</value>
  </data>
  <data name="GlobalBehaviourSalesSlipPrintConfig_PrintTotalSale_DisplayName" xml:space="preserve">
    <value>Tisknout celkovou výší slevy</value>
  </data>
  <data name="GlobalBehaviourSalesSlipPrintConfig_PrintTotalSale_Description" xml:space="preserve">
    <value>Povolení tisku celkové výše slevy na paragonu</value>
  </data>
  <data name="CashDeskBehaviourSalesVoucherConfig_CrcRequired_DisplayName" xml:space="preserve">
    <value>Vyžadovat CRC kód</value>
  </data>
  <data name="CashDeskBehaviourSalesVoucherConfig_CrcRequired_Description" xml:space="preserve">
    <value>Bude se při načtení voucheru vyžadovat a tím pádem i kontrolovat CRC kód?</value>
  </data>
  <data name="CashDeskBehaviourPaymentVoucherConfig_CurrencyTypeId_DisplayName" xml:space="preserve">
    <value>Id druhu platidla</value>
  </data>
  <data name="CashDeskBehaviourPaymentVoucherConfig_CurrencyTypeId_Description" xml:space="preserve">
    <value>Id druhu platidla, které se použije pro QR voucher. Druh platidla (typu poukázka) se musí nejprve vytvořit v Kanceláři.</value>
  </data>
  <data name="MobileOrderingAuthenticationConfig_ServerPort_DisplayName" xml:space="preserve">
    <value>Port serveru</value>
  </data>
  <data name="MobileOrderingAuthenticationConfig_ServerPort_Description" xml:space="preserve">
    <value>Port serveru</value>
  </data>
  <data name="MobileOrderingAuthenticationConfig_InactivityTimeout_DisplayName" xml:space="preserve">
    <value>Doba expirace přihlášení při nečinnosti (m)</value>
  </data>
  <data name="MobileOrderingAuthenticationConfig_InactivityTimeout_Description" xml:space="preserve">
    <value>Maximální doba nečinnosti (v minutách) po které bude uživatel odhlášen</value>
  </data>
  <data name="MobileOrderingAuthenticationConfig_AuthenticationType_DisplayName" xml:space="preserve">
    <value>Autentifikace</value>
  </data>
  <data name="MobileOrderingAuthenticationConfig_AuthenticationType_Description" xml:space="preserve">
    <value>Typ autentifikace</value>
  </data>
  <data name="MobileOrderingAuthenticationConfig_AuthenticationTypeAlt_DisplayName" xml:space="preserve">
    <value>Autentifikace alt</value>
  </data>
  <data name="MobileOrderingAuthenticationConfig_AuthenticationTypeAlt_Description" xml:space="preserve">
    <value>Alternativní typ autentifikace, která se použije, pokud se nepovede přihlásit pod hlavním typem</value>
  </data>
  <data name="MobileOrderingAuthenticationConfig_Settings_DisplayName" xml:space="preserve">
    <value>Autentifikace - nastavení</value>
  </data>
  <data name="MobileOrderingAuthenticationConfig_Settings_Description" xml:space="preserve">
    <value>Nastavení autentifikace</value>
  </data>
  <data name="MobileOrderingAuthenticationConfig_SettingsAlt_DisplayName" xml:space="preserve">
    <value>Autentifikace alt - nastavení</value>
  </data>
  <data name="MobileOrderingAuthenticationConfig_SettingsAlt_Description" xml:space="preserve">
    <value>Nastavení alternativní autentifikace</value>
  </data>
  <data name="OpenIdConnectAuthenticationSettings_OidcUri_DisplayName" xml:space="preserve">
    <value>Adresa serveru</value>
  </data>
  <data name="OpenIdConnectAuthenticationSettings_OidcUri_Description" xml:space="preserve">
    <value>URL adresa kde se nachází OpenID (OIDC) server</value>
  </data>
  <data name="OpenIdConnectAuthenticationSettings_ClientId_DisplayName" xml:space="preserve">
    <value>Id služby</value>
  </data>
  <data name="OpenIdConnectAuthenticationSettings_ClientId_Description" xml:space="preserve">
    <value>Id OpenID (OIDC) služby. Nachází se na OpenID (OIDC) servery</value>
  </data>
  <data name="OpenIdConnectAuthenticationSettings_ClientSecret_DisplayName" xml:space="preserve">
    <value>Klíč služby</value>
  </data>
  <data name="OpenIdConnectAuthenticationSettings_ClientSecret_Description" xml:space="preserve">
    <value>Client servret klíč OpenID (OIDC) služby. Nachází se na OpenID (OIDC) servery</value>
  </data>
  <data name="OpenIdConnectAuthenticationSettings_Scope_DisplayName" xml:space="preserve">
    <value>Data ze serveru</value>
  </data>
  <data name="OpenIdConnectAuthenticationSettings_Scope_Description" xml:space="preserve">
    <value>Hodnoty které má server vracet. Defaultně - openid profile email (oddělené mezerami)</value>
  </data>
  <data name="OpenIdConnectAuthenticationSettings_RedirectUri_DisplayName" xml:space="preserve">
    <value>Přesměrování</value>
  </data>
  <data name="OpenIdConnectAuthenticationSettings_RedirectUri_Description" xml:space="preserve">
    <value>Musí obsahovat openidclient://callback jinak se aplikace po přihlášení neotevře a nebude vědět pokračovat v přihlášení</value>
  </data>
  <data name="CashDeskBehaviourPaymentVoucherConfig_RegexPattern_DisplayName" xml:space="preserve">
    <value>Regulérní výraz pro načtení poukázky</value>
  </data>
  <data name="CashDeskBehaviourPaymentVoucherConfig_RegexPattern_Description" xml:space="preserve">
    <value>Regulérní vyraz, pomocí kterého se získájí všechny informace o použité poukázce. Vyžadovány jsou tyto hodnoty: Id, IdPlatidla, Hodnota, Platnost, ZnovuPouzitelnost, Crc. Kód lze vygenerovat v Kanceláři.
Příklad vygenerovaného QR kódu:
PLATBA 381070D53123440DBE000D4968864D93-14-500-06.11.2022-1-3286555202</value>
  </data>
  <data name="CashDeskBehaviourPaymentVoucherConfig_GenerationPattern_DisplayName" xml:space="preserve">
    <value>Šablona pro generování</value>
  </data>
  <data name="CashDeskBehaviourPaymentVoucherConfig_GenerationPattern_Description" xml:space="preserve">
    <value>Podle této šablony se vygeneruje QR kód. V šabloně se musí použít symbolické proměnné {QR.Id}, {QR.IdPlatidla}, {QR.Hodnota}, {QR.Platnost}, {QR.ZnovuPouzitelnost} a {QR.Crc}.</value>
  </data>
  <data name="WebApiGeneralConfig_UserName_DisplayName" xml:space="preserve">
    <value>Autentizace - jméno</value>
  </data>
  <data name="WebApiGeneralConfig_UserName_Description" xml:space="preserve">
    <value>Uživatelské jméno pro přihlášení do WebApi</value>
  </data>
  <data name="WebApiGeneralConfig_UserPassword_DisplayName" xml:space="preserve">
    <value>Autentizace - heslo</value>
  </data>
  <data name="WebApiGeneralConfig_UserPassword_Description" xml:space="preserve">
    <value>Heslo pro přihlášení do WebApi</value>
  </data>
  <data name="WebApiGeneralConfig_MenuPricesPublish_DisplayName" xml:space="preserve">
    <value>Čas zobrazení cen</value>
  </data>
  <data name="WebApiGeneralConfig_MenuPricesPublish_Description" xml:space="preserve">
    <value>Čas zobrazení cen</value>
  </data>
  <data name="WebApiGeneralConfig_ServerPort_DisplayName" xml:space="preserve">
    <value>Port serveru</value>
  </data>
  <data name="WebApiGeneralConfig_ServerPort_Description" xml:space="preserve">
    <value>Port serveru</value>
  </data>
  <data name="OfficeMealDistributionBehaviourInquiriesConfig_AllowDataExtension_DisplayName" xml:space="preserve">
    <value>Povolit rozšíření distribuce jídel</value>
  </data>
  <data name="OfficeMealDistributionBehaviourInquiriesConfig_AllowDataExtension_Description" xml:space="preserve">
    <value>Povolení rozšíření distribuce jídel o další parametry ukládané do xml.</value>
  </data>
  <data name="HotelGeneralConfig_ApplicationRoleCode_DisplayName" xml:space="preserve">
    <value>ApplicationRoleCode</value>
  </data>
  <data name="HotelGeneralConfig_ApplicationRoleCode_Description" xml:space="preserve">
    <value>ApplicationRoleCode</value>
  </data>
  <data name="HotelGeneralConfig_ApplicationRoleReadOnlyCode_DisplayName" xml:space="preserve">
    <value>ApplicationRoleReadOnlyCode</value>
  </data>
  <data name="HotelGeneralConfig_ApplicationRoleReadOnlyCode_Description" xml:space="preserve">
    <value>ApplicationRoleReadOnlyCode</value>
  </data>
  <data name="HotelGeneralConfig_FixedTime_DisplayName" xml:space="preserve">
    <value>FixedTime</value>
  </data>
  <data name="HotelGeneralConfig_FixedTime_Description" xml:space="preserve">
    <value>FixedTime</value>
  </data>
  <data name="HotelGeneralConfig_EmployeeGroupIds_DisplayName" xml:space="preserve">
    <value>EmployeeGroupIds</value>
  </data>
  <data name="HotelGeneralConfig_EmployeeGroupIds_Description" xml:space="preserve">
    <value>EmployeeGroupIds</value>
  </data>
  <data name="HotelGeneralConfig_IsAirport_DisplayName" xml:space="preserve">
    <value>IsAirport</value>
  </data>
  <data name="HotelGeneralConfig_IsAirport_Description" xml:space="preserve">
    <value>IsAirport</value>
  </data>
  <data name="HotelGeneralConfig_IsConnectedToKredit_DisplayName" xml:space="preserve">
    <value>IsConnectedToKredit</value>
  </data>
  <data name="HotelGeneralConfig_IsConnectedToKredit_Description" xml:space="preserve">
    <value>IsConnectedToKredit</value>
  </data>
  <data name="HotelGeneralConfig_ServerPort_DisplayName" xml:space="preserve">
    <value>Port serveru</value>
  </data>
  <data name="HotelGeneralConfig_ServerPort_Description" xml:space="preserve">
    <value>Port serveru</value>
  </data>
  <data name="OfficeManagementBehaviourLicenseConfig_StockInventory_DisplayName" xml:space="preserve">
    <value>Stav skladu</value>
  </data>
  <data name="OfficeManagementBehaviourLicenseConfig_StockInventory_Description" xml:space="preserve">
    <value>Stav skladu</value>
  </data>
  <data name="MastersOrderingGeneralConfig_MealKindId_DisplayName" xml:space="preserve">
    <value>MealKindId</value>
  </data>
  <data name="MastersOrderingGeneralConfig_MealKindId_Description" xml:space="preserve">
    <value>MealKindId</value>
  </data>
  <data name="MastersOrderingGeneralConfig_GroupByMealKind_DisplayName" xml:space="preserve">
    <value>GroupByMealKind</value>
  </data>
  <data name="MastersOrderingGeneralConfig_GroupByMealKind_Description" xml:space="preserve">
    <value>GroupByMealKind</value>
  </data>
  <data name="MastersOrderingGeneralConfig_WorkplaceId_DisplayName" xml:space="preserve">
    <value>WorkplaceId</value>
  </data>
  <data name="MastersOrderingGeneralConfig_WorkplaceId_Description" xml:space="preserve">
    <value>WorkplaceId</value>
  </data>
  <data name="MastersOrderingGeneralConfig_ServerPort_DisplayName" xml:space="preserve">
    <value>Port serveru</value>
  </data>
  <data name="MastersOrderingGeneralConfig_ServerPort_Description" xml:space="preserve">
    <value>Port serveru</value>
  </data>
  <data name="OfficeManagementBehaviourLicenseConfig_PurchaseAmounts_DisplayName" xml:space="preserve">
    <value>Objem nákupů</value>
  </data>
  <data name="OfficeManagementBehaviourLicenseConfig_PurchaseAmounts_Description" xml:space="preserve">
    <value>Objem nákupů</value>
  </data>
  <data name="OfficeManagementBehaviourLicenseConfig_StockTurnoverAndInventory_DisplayName" xml:space="preserve">
    <value>Stavy a obraty za období</value>
  </data>
  <data name="OfficeManagementBehaviourLicenseConfig_StockTurnoverAndInventory_Description" xml:space="preserve">
    <value>Stavy a obraty za období</value>
  </data>
  <data name="OfficeManagementBehaviourLicenseConfig_LimitObserving_DisplayName" xml:space="preserve">
    <value>Dodržování limitů</value>
  </data>
  <data name="OfficeManagementBehaviourLicenseConfig_LimitObserving_Description" xml:space="preserve">
    <value>Dodržování limitů</value>
  </data>
  <data name="OfficeManagementBehaviourLicenseConfig_PurchasePricesControl_DisplayName" xml:space="preserve">
    <value>Kontrola nákupních cen</value>
  </data>
  <data name="OfficeManagementBehaviourLicenseConfig_PurchasePricesControl_Description" xml:space="preserve">
    <value>Kontrola nákupních cen</value>
  </data>
  <data name="OfficeManagementBehaviourLicenseConfig_DietPrice_DisplayName" xml:space="preserve">
    <value>Ceny diet</value>
  </data>
  <data name="OfficeManagementBehaviourLicenseConfig_DietPrice_Description" xml:space="preserve">
    <value>Ceny diet</value>
  </data>
  <data name="OfficeManagementBehaviourLicenseConfig_UnitCosts_DisplayName" xml:space="preserve">
    <value>Náklady na oddělení </value>
  </data>
  <data name="OfficeManagementBehaviourLicenseConfig_UnitCosts_Description" xml:space="preserve">
    <value>Náklady na oddělení </value>
  </data>
  <data name="OfficeManagementBehaviourLicenseConfig_IncomeAndCosts_DisplayName" xml:space="preserve">
    <value>Výnosy a náklady</value>
  </data>
  <data name="OfficeManagementBehaviourLicenseConfig_IncomeAndCosts_Description" xml:space="preserve">
    <value>Výnosy a náklady</value>
  </data>
  <data name="OfficeManagementBehaviourLicenseConfig_PortionAmount_DisplayName" xml:space="preserve">
    <value>Počty porcí</value>
  </data>
  <data name="OfficeManagementBehaviourLicenseConfig_PortionAmount_Description" xml:space="preserve">
    <value>Počty porcí</value>
  </data>
  <data name="MealKindWorkplaceConfigItem_MealKindId_DisplayName" xml:space="preserve">
    <value>Id druhu jídla</value>
  </data>
  <data name="MealKindWorkplaceConfigItem_MealKindId_Description" xml:space="preserve">
    <value>Id druhu jídla</value>
  </data>
  <data name="MealKindWorkplaceConfigItem_WorkplaceId_DisplayName" xml:space="preserve">
    <value>Id provozovny</value>
  </data>
  <data name="MealKindWorkplaceConfigItem_WorkplaceId_Description" xml:space="preserve">
    <value>Id provozovny</value>
  </data>
  <data name="OfficeMealDistributionBehaviourInquiriesConfig_MealKindLunch_DisplayName" xml:space="preserve">
    <value>Druh jídla - oběd</value>
  </data>
  <data name="OfficeMealDistributionBehaviourInquiriesConfig_MealKindLunch_Description" xml:space="preserve">
    <value>Druh jídla - oběd</value>
  </data>
  <data name="OfficeManagementBehaviourLicenseConfig_StockMovementSummary_DisplayName" xml:space="preserve">
    <value>Přehled skladových pohybů</value>
  </data>
  <data name="OfficeManagementBehaviourLicenseConfig_StockMovementSummary_Description" xml:space="preserve">
    <value>Přehled skladových pohybů</value>
  </data>
  <data name="OfficeBehaviourBatchOrdering_KeepSelectedAlt_DisplayName" xml:space="preserve">
    <value>Neměnit zvolenou alt</value>
  </data>
  <data name="OfficeBehaviourBatchOrdering_KeepSelectedAlt_Description" xml:space="preserve">
    <value>Bude se při změně druhu jídla ponechávat zvolená alternativa? Úprava pro UVN, op48171</value>
  </data>
  <data name="CashDeskBehaviorICouponConfig_Enabled_Description" xml:space="preserve">
    <value>Povolení ICoupon pro zařízení, v kase to způsobí zobrazení tlačítka na klávesnici.</value>
  </data>
  <data name="CashDeskBehaviorICouponConfig_Enabled_DisplayName" xml:space="preserve">
    <value>Povolit</value>
  </data>
  <data name="GlobalServicesICouponConfig_CurrencyTypeId_Description" xml:space="preserve">
    <value>Nastavení id druhu platidla pro ICoupon.</value>
  </data>
  <data name="GlobalServicesICouponConfig_CurrencyTypeId_DisplayName" xml:space="preserve">
    <value>Id druhu platidla</value>
  </data>
  <data name="XCouponUsernamePasswordAuthenticationSettings_Password_Description" xml:space="preserve">
    <value>Heslo pro přihlašování k ICoupon API pro získání ApiKey.</value>
  </data>
  <data name="XCouponUsernamePasswordAuthenticationSettings_Password_DisplayName" xml:space="preserve">
    <value>Heslo</value>
  </data>
  <data name="XCouponApiKeyAuthenticationSettings_ApiKey_Description" xml:space="preserve">
    <value>ApiKey pokud je vyplněn tak uživatelské jméno a heslo nemá smysl protože uživatelské jméno a heslo se používá k získání TokenId a pomocí ApiKey se autentifikuje.</value>
  </data>
  <data name="XCouponApiKeyAuthenticationSettings_ApiKey_DisplayName" xml:space="preserve">
    <value>ApiKey</value>
  </data>
  <data name="XCouponUsernamePasswordAuthenticationSettings_Username_Description" xml:space="preserve">
    <value>Uživatelské jméno pro přihlašování k ICoupon API pro získání ApiKey.</value>
  </data>
  <data name="XCouponUsernamePasswordAuthenticationSettings_Username_DisplayName" xml:space="preserve">
    <value>Uživatelské jméno</value>
  </data>
  <data name="CashDeskBehaviorICouponConfig_TradingOutlet_Description" xml:space="preserve">
    <value>Identifikátor prodejního místa. V dokumentaci je to parametr TradingOutletRef. Hodnota např. 1234</value>
  </data>
  <data name="CashDeskBehaviorICouponConfig_TradingOutlet_DisplayName" xml:space="preserve">
    <value>Prodejní místo</value>
  </data>
  <data name="GlobalServicesICouponConfig_Location_Description" xml:space="preserve">
    <value>Místo kde se prodejna poskytující ICoupon nachází. V dokumentaci je to parametr LocationRef. Hodnota např. PRG</value>
  </data>
  <data name="GlobalServicesICouponConfig_Location_DisplayName" xml:space="preserve">
    <value>Místo</value>
  </data>
  <data name="GlobalServicesICouponConfig_ServiceProvider_Description" xml:space="preserve">
    <value>Unikátní identifikátor poskytovatele služby ICoupon. V dokumentaci je to parametr ServiceProviderRef. Hodnota např. PRG1</value>
  </data>
  <data name="GlobalServicesICouponConfig_ServiceProvider_DisplayName" xml:space="preserve">
    <value>Identifikátor poskytovatele</value>
  </data>
  <data name="GlobalServicesICouponConfig_AuthenticationType_Description" xml:space="preserve">
    <value>Pokud máme k dispozici ApiKey využijeme přihlášení pomocí něho, pokud ne musíme mít uživatelské jméno a heslo pomocí níž zjistíme ApiKey a můžeme se přihlašovat</value>
  </data>
  <data name="GlobalServicesICouponConfig_AuthenticationType_DisplayName" xml:space="preserve">
    <value>Typ authentikace</value>
  </data>
  <data name="GlobalServicesICouponConfig_Settings_Description" xml:space="preserve">
    <value>Konfigurace parametrů authentickace</value>
  </data>
  <data name="GlobalServicesICouponConfig_Settings_DisplayName" xml:space="preserve">
    <value>Nastavení</value>
  </data>
  <data name="DietaryOrderingSettings_IndividualOrderingAllowEmployeesRecipes_Description" xml:space="preserve">
    <value>Povolit záložku Vařené receptury ZAM.</value>
  </data>
  <data name="DietaryOrderingSettings_IndividualOrderingAllowEmployeesRecipes_DisplayName" xml:space="preserve">
    <value>Individuální objednávání - zaměstnanecké receptury</value>
  </data>
  <data name="DietaryOrderingSettings_NotShowAdditionalRecipes_Description" xml:space="preserve">
    <value>Skryje dodatkové receptury v individuálním objednávání a ve formuláři pro zadání snězeného množství u objednávání na pacienta.</value>
  </data>
  <data name="DietaryOrderingSettings_NotShowAdditionalRecipes_DisplayName" xml:space="preserve">
    <value>Nezobrazovat dodatkové receptury</value>
  </data>
  <data name="CashDeskBehaviorICouponConfig_TradingOutletName_Description" xml:space="preserve">
    <value>Název prodejního místa. V dokumentaci je to parametr TradingOutletName. Hodnota např. Coffee Shop</value>
  </data>
  <data name="CashDeskBehaviorICouponConfig_TradingOutletName_DisplayName" xml:space="preserve">
    <value>Název prodejního místa</value>
  </data>
  <data name="GlobalServicesICouponConfig_UrlAddress_Description" xml:space="preserve">
    <value>Nastavení adresy serveru pro REST api ICouponu pro testovací server to je například adresa "https://ppd.icoupon.global/api"</value>
  </data>
  <data name="GlobalServicesICouponConfig_UrlAddress_DisplayName" xml:space="preserve">
    <value>Adresa serveru</value>
  </data>
  <data name="GlobalServicesICouponConfig_ClientId_Description" xml:space="preserve">
    <value>Nastavení klientského identifikátoru. V dokumentaci je to parametr ClientId. Hodnota např. AnetePOS</value>
  </data>
  <data name="GlobalServicesICouponConfig_ClientId_DisplayName" xml:space="preserve">
    <value>Client Id</value>
  </data>
  <data name="WebKreditOrderingOrdersConfig_OrderInExchange_Description" xml:space="preserve">
    <value>Zobrazit sloupec pořadí v burze</value>
  </data>
  <data name="WebKreditOrderingOrdersConfig_OrderInExchange_DisplayName" xml:space="preserve">
    <value>Pořadí v burze</value>
  </data>
  <data name="OfficeWorkplacesBehaviourPriceListConfig_EvaluatePricesAfterLoad_DisplayName" xml:space="preserve">
    <value>Vypočítat ceny po načtení dat</value>
  </data>
  <data name="OfficeWorkplacesBehaviourPriceListConfig_EvaluatePricesAfterLoad_Description" xml:space="preserve">
    <value>Provede se výpočet cen ihned po načtení dat? Po výpočtu se bude pracovat s ceníkem plynuleji. Nicměné výpočet může u rozsáhlých ceníků trvat delší dobu (STU). Povolení výpočtu má smysl především v kombinaci s validací cirkularních referencí. Jedná se o implicitní hodnotu, kterou lze v ceníku změnit.</value>
  </data>
  <data name="DietaryOrderingSettings_OrderedDietsRestricByResort_Description" xml:space="preserve">
    <value>V sestavě Přehled objednávek omezí zobrazené objednávky pouze na pacienty z daného nákladového střediska.</value>
  </data>
  <data name="DietaryOrderingSettings_OrderedDietsRestricByResort_DisplayName" xml:space="preserve">
    <value>Omezit zobrazení objednávek podle střediska</value>
  </data>
  <data name="CashDeskBehaviourGoodsSearchConfig_GoodsSearch_Description" xml:space="preserve">
    <value>Vyhledávání zboží dle</value>
  </data>
  <data name="CashDeskBehaviourGoodsSearchConfig_GoodsSearch_DisplayName" xml:space="preserve">
    <value>Vyhledávání zboží dle</value>
  </data>
  <data name="KbPaymentTermSettings_CardPaymentConstantSymbol_Description" xml:space="preserve">
    <value>Štvormiestny identifikátor používaný u transakcí cez bankový terminál. Môže byť závislý od krajiny:
CZ - 0008 (platby za zboží)
SK - 0008 (platby za zboží)

Aktuálne používane symboli sú identické a needitovateľné.</value>
  </data>
  <data name="KbPaymentTermSettings_CardPaymentConstantSymbol_DisplayName" xml:space="preserve">
    <value>Konstantní symbol</value>
  </data>
  <data name="WebKreditOrderingMenuAuthenticatedConfig_Price2ItemId_DisplayName" xml:space="preserve">
    <value>Cena dotovaná - id položky</value>
  </data>
  <data name="WebKreditOrderingMenuAuthenticatedConfig_Price2ItemId_Description" xml:space="preserve">
    <value>Id položky sloupce Cena dotovaná</value>
  </data>
  <data name="WebKreditOrderingMenuAuthenticatedConfig_Price2SubsidyId_DisplayName" xml:space="preserve">
    <value>Cena dotovaná - kategorie dotace</value>
  </data>
  <data name="WebKreditOrderingMenuAuthenticatedConfig_Price2SubsidyId_Description" xml:space="preserve">
    <value>Kategorie dotace sloupce Cena dotovaná</value>
  </data>
  <data name="MobileStockBehaviourConfig_IsCameraSupported_Description" xml:space="preserve">
    <value>Povolení skenování pomocí kamery, některé zařízení tuto funkčnost nepodporují je tedy potřeba pro dané zařízení tuto funkčnost vypnout.</value>
  </data>
  <data name="MobileStockBehaviourConfig_IsCameraSupported_DisplayName" xml:space="preserve">
    <value>Povolit skenování pomocí kamery</value>
  </data>
  <data name="CardPayPaymentGateSettings_APIUrl_DisplayName" xml:space="preserve">
    <value>API URL</value>
  </data>
  <data name="CardPayPaymentGateSettings_APIUrl_Description" xml:space="preserve">
    <value>Odkaz na CardPay API. Standardní adresa je https://moja.tatrabanka.sk/cgi-bin/e-commerce/start/ (adresa musí končit /start/, zbylou část doplní aplikace).</value>
  </data>
  <data name="CardPayPaymentGateSettings_MerchantId_DisplayName" xml:space="preserve">
    <value>Identifikátor obchodníka</value>
  </data>
  <data name="CardPayPaymentGateSettings_MerchantId_Description" xml:space="preserve">
    <value>Jedinečné identifikační číslo obchodníka</value>
  </data>
  <data name="CardPayPaymentGateSettings_SecurityKey_DisplayName" xml:space="preserve">
    <value>Bezpečnostní klíč</value>
  </data>
  <data name="CardPayPaymentGateSettings_SecurityKey_Description" xml:space="preserve">
    <value>Bezpečnostní klíč</value>
  </data>
  <data name="CardPayPaymentGateSettings_ECDSAKeysFilePath_DisplayName" xml:space="preserve">
    <value>Veřejné kliče</value>
  </data>
  <data name="CardPayPaymentGateSettings_ECDSAKeysFilePath_Description" xml:space="preserve">
    <value>Kompletní cesta k souboru s veřejnými klíči pro ověření digitálního podpisu ECDSA.</value>
  </data>
  <data name="WebKreditGeneralConfig_EnableSecureCookies_DisplayName" xml:space="preserve">
    <value>Povolit zabezpečené cookies</value>
  </data>
  <data name="WebKreditGeneralConfig_EnableSecureCookies_Description" xml:space="preserve">
    <value>Aplikace ke cookies přidá příznak secure.</value>
  </data>
  <data name="GlobalHwLabelPrinterConfig_PrinterName_DisplayName" xml:space="preserve">
    <value>Název tiskárny</value>
  </data>
  <data name="GlobalHwLabelPrinterConfig_PrinterName_Description" xml:space="preserve">
    <value>Tiskárna Windows, která se použije pro tisk štítků. Pokud není vyplněno, ponechá se implicitní tiskárna Windows.</value>
  </data>
  <data name="CardPayPaymentGateSettings_NotificationEmail_DisplayName" xml:space="preserve">
    <value>Notifikační email</value>
  </data>
  <data name="CardPayPaymentGateSettings_NotificationEmail_Description" xml:space="preserve">
    <value>Emailová adresa, na kterou platební brána bude zasílat zprávy o provedených platbách.</value>
  </data>
  <data name="CardPayPaymentGateSettings_EnableE2E_DisplayName" xml:space="preserve">
    <value>E2E reference platby</value>
  </data>
  <data name="CardPayPaymentGateSettings_EnableE2E_Description" xml:space="preserve">
    <value>Povolí E2E referenci platby v poli pro variabilní symbol.</value>
  </data>
  <data name="CardPayPaymentGateSettings_E2ESpecificSymbol_DisplayName" xml:space="preserve">
    <value>E2E - specifický symbol</value>
  </data>
  <data name="CardPayPaymentGateSettings_E2ESpecificSymbol_Description" xml:space="preserve">
    <value>Specifický symbol v E2E referenci platby. Není povinný.</value>
  </data>
  <data name="StateTreasuryGateSettings_APIUrl_DisplayName" xml:space="preserve">
    <value>API URL</value>
  </data>
  <data name="StateTreasuryGateSettings_APIUrl_Description" xml:space="preserve">
    <value>Odkaz na API platební brány Štátnej pokladnice.</value>
  </data>
  <data name="StateTreasuryGateSettings_MerchantId_DisplayName" xml:space="preserve">
    <value>Identifikátor klienta</value>
  </data>
  <data name="StateTreasuryGateSettings_MerchantId_Description" xml:space="preserve">
    <value>Identifikátor klienta Štátnej pokladnice.</value>
  </data>
  <data name="StateTreasuryGateSettings_CertificatePrivatePath_DisplayName" xml:space="preserve">
    <value>Soukromý klíč</value>
  </data>
  <data name="StateTreasuryGateSettings_CertificatePrivatePath_Description" xml:space="preserve">
    <value>Kompletní cesta k soukromému klíči. Používá se pro podpis zpráv při odesílání na platební bránu.</value>
  </data>
  <data name="DietaryOrderingSettings_CopyDeleteExistingOrders_DisplayName" xml:space="preserve">
    <value>Smazat existující objednávky při kopírování ze dne</value>
  </data>
  <data name="DietaryOrderingSettings_CopyDeleteExistingOrders_Description" xml:space="preserve">
    <value>Nastavuje výchozí hodnotu pole "Smazat existující objednávky" ve formuláři pro kopírování za dne. Pole je dostupné pouze u objednávání na pacienty.</value>
  </data>
  <data name="StateTreasuryGateSettings_VariableSymbol_DisplayName" xml:space="preserve">
    <value>Variabilní symbol</value>
  </data>
  <data name="StateTreasuryGateSettings_VariableSymbol_Description" xml:space="preserve">
    <value>Variabilní symbol. Pokud není nastaven, použije se ID klienta (VS je povinný údaj).</value>
  </data>
  <data name="GlobalServicesDimensoConnectionConfig_ServerAddress_DisplayName" xml:space="preserve">
    <value>Adresa centrálního Dimenso serveru</value>
  </data>
  <data name="GlobalServicesDimensoConnectionConfig_ServerAddress_Description" xml:space="preserve">
    <value>Adresa centrálního Dimenso serveru</value>
  </data>
  <data name="GlobalServicesDimensoConnectionConfig_InternalServerAddress_DisplayName" xml:space="preserve">
    <value>Adresa interního Dimenso appserveru</value>
  </data>
  <data name="GlobalServicesDimensoConnectionConfig_InternalServerAddress_Description" xml:space="preserve">
    <value>Adresa interního Dimenso appserveru</value>
  </data>
  <data name="PaymentTermSettingsBase_CashbackEnabled_Description" xml:space="preserve">
    <value>Podpora funkce cashback na terminály.</value>
  </data>
  <data name="PaymentTermSettingsBase_CashbackEnabled_DisplayName" xml:space="preserve">
    <value>Cashback</value>
  </data>
  <data name="GlobalServicesHelpDeskServicesConfig_ArchivePassword_DisplayName" xml:space="preserve">
    <value>Heslo pro offline synchronizaci</value>
  </data>
  <data name="GlobalServicesHelpDeskServicesConfig_ArchivePassword_Description" xml:space="preserve">
    <value>Heslo pro archivy s json soubory pro offline synchronizaci do Helpdesku.</value>
  </data>
  <data name="GlobalServicesDimensoConnectionConfig_ServiceProviderId_DisplayName" xml:space="preserve">
    <value>Id poskytovatele v Dimensu</value>
  </data>
  <data name="GlobalServicesDimensoConnectionConfig_ServiceProviderId_Description" xml:space="preserve">
    <value>Id poskytovatele v Dimensu</value>
  </data>
  <data name="GlobalServicesDimensoConnectionConfig_ExecutiveSystemName_DisplayName" xml:space="preserve">
    <value>Název výkonného systému</value>
  </data>
  <data name="GlobalServicesDimensoConnectionConfig_ExecutiveSystemName_Description" xml:space="preserve">
    <value>Název výkonného systému</value>
  </data>
  <data name="GlobalServicesDimensoConnectionConfig_RegistrationCodeAddress_DisplayName" xml:space="preserve">
    <value>Adresa pro registrace ve WebKreditu</value>
  </data>
  <data name="GlobalServicesDimensoConnectionConfig_RegistrationCodeAddress_Description" xml:space="preserve">
    <value>Adresa pro registrace ve WebKreditu</value>
  </data>
  <data name="GlobalServicesDimensoConnectionConfig_OutboundBindingTypeSettings_DisplayName" xml:space="preserve">
      <value>Nastavení certifikátu výkonného systému</value>
  </data>
  <data name="GlobalServicesDimensoConnectionConfig_OutboundBindingTypeSettings_Description" xml:space="preserve">
      <value>Nastavení certifikátu výkonného systému</value>
  </data>
  <data name="GlobalServicesDimensoConnectionConfig_InboundBindingTypeSettings_DisplayName" xml:space="preserve">
      <value>Nastavení certifikátu Dimensa</value>
  </data>
  <data name="GlobalServicesDimensoConnectionConfig_InboundBindingTypeSettings_Description" xml:space="preserve">
      <value>Nastavení certifikátu Dimensa</value>
  </data>
</root>