using System;
using System.Data;

namespace Anete.Data
{

	/// <summary>
	/// Porovnavac dvou DataTable
	/// </summary>
	public static class DataTableComparer
	{
		/// <summary>
		/// Porovnani dvou DataTable a navrat vysledku se zmenami
		/// </summary>
		/// <param name="first">Prvni datatable</param>
		/// <param name="second">Druha datatable</param>
		/// <returns>Tabulka s odlisnymi zaznamy</returns>
		public static DataTable CompareDataTables(DataTable first, DataTable second)
		{
			// Create Empty Table
			DataTable result = new DataTable("Difference");

			// Must use a Dataset to make use of a DataRelation object
			using (DataSet ds = new DataSet())
			{
				DataTable firstCopy = first.Copy();
				firstCopy.TableName = "FirstCopy";

				DataTable secondCopy = second.Copy();
				firstCopy.TableName = "SecondCopy";
				
				ds.Tables.Add(firstCopy);
				ds.Tables.Add(secondCopy);


				//Get Columns for DataRelation
				DataColumn[] firstcolumns = new DataColumn[ds.Tables[0].Columns.Count];

				for (int i = 0; i < firstcolumns.Length; i++)
				{
					firstcolumns[i] = ds.Tables[0].Columns[i];
				}

				DataColumn[] secondcolumns = new DataColumn[ds.Tables[1].Columns.Count];

				for (int i = 0; i < secondcolumns.Length; i++)
				{
					secondcolumns[i] = ds.Tables[1].Columns[i];
				}

				// Create DataRelation
				DataRelation r = new DataRelation(string.Empty, firstcolumns, secondcolumns, false);

				ds.Relations.Add(r);

				// Create columns for return table
				for (int i = 0; i < first.Columns.Count; i++)
				{
					result.Columns.Add(first.Columns[i].ColumnName, first.Columns[i].DataType);
				}

				// If First Row not in Second, Add to return table.
				result.BeginLoadData();

				foreach (DataRow parentrow in ds.Tables[0].Rows)
				{
					DataRow[] childrows = parentrow.GetChildRows(r);
					if (childrows == null || childrows.Length == 0)
					{
						result.LoadDataRow(parentrow.ItemArray, true);
					}
				}

				result.EndLoadData();

			}

			return result;
		}
	}
}
