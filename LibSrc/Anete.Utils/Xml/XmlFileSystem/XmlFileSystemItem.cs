using System;
using System.Collections.Generic;
using System.Text;
using System.IO;
using System.Xml;
using Anete.Utils.Patterns;

namespace Anete.Utils.Xml.XmlFileSystem
{
    /// <summary>
    /// Libovolna polozka souboroveho systemu
    /// </summary>
    public abstract class XmlFileSystemItem : Disposable
    {
        #region private fields...
        private string _tagname;        
        #endregion

        #region constructors...
        /// <summary>
        /// Initializes a new instance of the XmlFileSystemItem class.
        /// </summary>
        /// <param name="tagName">Nazev tagu pro tento typ polozky</param>
        /// <param name="fileSystem">The file system.</param>
        /// <param name="parent">Nadrazena polozka ve stromu</param>
        /// <param name="name">Nazev</param>
        /// <param name="id">Unikatni id, muze byt null.</param>
        public XmlFileSystemItem(string tagName, XmlFileSystem fileSystem, XmlFileSystemItem parent, string name, string id)
        {
            _id = id;
            _name = name;
            _tagname = tagName;
            _parent = parent;
            _fileSystem = fileSystem;
        }

        /// <summary>
        /// Initializes a new instance of the XmlFileSystemItem class.
        /// </summary>
        public XmlFileSystemItem(string tagName, XmlFileSystem fileSystem, XmlFileSystemItem parent)
            : this(tagName, fileSystem, parent, null, null)
        {
        }        
        #endregion

        #region public properties...                
        private XmlFileSystemItem _parent;
        /// <summary>
        /// Nadrazena slozka. Pokud je null, jedna se o root directory
        /// </summary>
        public XmlFileSystemItem Parent
        {
            get
            {
                return _parent;
            }
        }

        private XmlFileSystem _fileSystem;
        /// <summary>
        /// FileSystem, pod ktery item patri
        /// </summary>
        public XmlFileSystem FileSystem
        {
            get
            {
                return _fileSystem;
            }
        }


        /// <summary>
        /// Root adresar
        /// </summary>
        public XmlFileSystemDirectory RootDirectory
        {
            get
            {
                if (_parent == null)
                {
                    return (XmlFileSystemDirectory)this;
                }
                else
                {
                    return _parent.RootDirectory;
                }
            }
        }
    
        private string _id;
        /// <summary>
        /// Id. Muze byt null.
        /// </summary>
        public string Id
        {
            get { return _id; }
            protected set
            {
                _id = value;
            }
        }

        private string _name;
        /// <summary>
        /// Nazev polozky
        /// </summary>
        public string Name
        {
            get { return _name; }
            protected set
            {
                _name = value;
            }
        }
        #endregion

        /// <summary>
        /// Zapise data do xml
        /// </summary>
        /// <param name="writer"></param>
        protected internal void WriteXmlData(XmlTextWriter writer)
        {
            writer.WriteStartElement(_tagname);
            writer.WriteAttributeString(XmlFileSystem.NameTagName, Name);
            if (!String.IsNullOrEmpty(Id))
            {
                writer.WriteAttributeString(XmlFileSystem.IdTagName, Id);
            }

            WriteSpecifiedXmlData(writer);

            writer.WriteEndElement();
        }

        /// <summary>
        /// Zapis dat specifickych pro dany typ polozky
        /// </summary>
        /// <param name="writer"></param>
        protected abstract void WriteSpecifiedXmlData(XmlTextWriter writer);

        /// <summary>
        /// Zpracuje spolecne atributy vsech typu polozek
        /// </summary>
        /// <param name="reader"></param>
        protected void ProcessXmlReaderAttribute(XmlReader reader)
        {
            // pokud atribut neexistuje, vraci se korektne null
            Name = reader[XmlFileSystem.NameTagName];
            Id = reader[XmlFileSystem.IdTagName];
        }

        /// <summary>
        /// Extrahuje dany item do slozky <c>destinationPath</c>.
        /// </summary>
        /// <param name="destinationPath"></param>
        protected internal abstract void ExtractTo(string destinationPath);

        /// <summary>
        /// Vraci plnou cestu k teto item v ramci xml file systemu.
        /// </summary>
        /// <returns></returns>
        public string GetFullPath()
        {
            StringBuilder result = new StringBuilder();
            XmlFileSystemItem item = this;
            while (item != null && item.Parent != null)
            {
                result.Insert(0, item.Name + XmlFileSystem.Separator);
                item = item.Parent;
            }
            result.Remove(result.Length - 1, 1);
            return result.ToString();
        }
    }
}
