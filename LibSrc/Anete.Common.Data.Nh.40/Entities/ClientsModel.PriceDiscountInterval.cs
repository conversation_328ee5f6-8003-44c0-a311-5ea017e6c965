//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Anete.Common.Data.Nh.Entities
{

    /// <summary>
    /// Tabulka: dbo.CEN_SlevyCas
    /// Interval, v kterem sleva plati.
    /// </summary>
    public partial class PriceDiscountInterval : Anete.Data.Nh.NhEntityBase, INotifyPropertyChanging, INotifyPropertyChanged {

        private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(System.String.Empty);

        private int _Id;

        private System.DateTime? _DatumOd;

        private System.DateTime? _DatumDo;

        private string _Dow;

        private System.DateTime? _CasOd;

        private System.DateTime? _CasDo;

        private PriceDiscount _PriceDiscount;
    
        #region Extensibility Method Definitions
        
        partial void OnCreated();

        public override bool Equals(object obj)
        {
          PriceDiscountInterval toCompare = obj as PriceDiscountInterval;
          if (toCompare == null)
          {
            return false;
          }

          if (!Object.Equals(this.Id, toCompare.Id))
            return false;
         // pokud mam default primary key, znamena to, ze jedna o nove vytvorenou entitu, ktera jeste nema key prirazen. 
        // V takove pripade musim porovnat podle instance
        bool isDefault = true;
        if (this.Id != default(int))
        {
          isDefault = false;
        }
     

          if (isDefault)
          {
            return object.ReferenceEquals(toCompare, this);
          }     
          return true;
        }

	private int? _hashCode = null;
        public override int GetHashCode()
        {
	if (_hashCode == null)
          {
          _hashCode = 13;
          _hashCode = (_hashCode * 7) + Id.GetHashCode();
}
          return _hashCode.Value;
        }
        partial void OnIdChanging(int value);
        
        partial void OnIdChanged();
        partial void OnDatumOdChanging(System.DateTime? value);
        
        partial void OnDatumOdChanged();
        partial void OnDatumDoChanging(System.DateTime? value);
        
        partial void OnDatumDoChanged();
        partial void OnDowChanging(string value);
        
        partial void OnDowChanged();
        partial void OnCasOdChanging(System.DateTime? value);
        
        partial void OnCasOdChanged();
        partial void OnCasDoChanging(System.DateTime? value);
        
        partial void OnCasDoChanged();
        partial void OnPriceDiscountChanging(PriceDiscount value);

        partial void OnPriceDiscountChanged();
        
        #endregion
        public PriceDiscountInterval()
        {
            OnCreated();
        }

    
        /// <summary>
        /// Sloupec: id
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Id", typeof(PriceDiscountIntervalSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(PriceDiscountInterval), Tag="Id")]
        public virtual int Id
        {
            get
            {
                return this._Id;
            }
            set
            {
                if (this._Id != value)
                {
                    this.OnIdChanging(value);
                    this.SendPropertyChanging();
                    this._Id = value;
                    this.SendPropertyChanged("Id");
                    this.OnIdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: datum_od
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("DatumOd", typeof(PriceDiscountIntervalSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual System.DateTime? DatumOd
        {
            get
            {
                return this._DatumOd;
            }
            set
            {
                if (this._DatumOd != value)
                {
                    this.OnDatumOdChanging(value);
                    this.SendPropertyChanging();
                    this._DatumOd = value;
                    this.SendPropertyChanged("DatumOd");
                    this.OnDatumOdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: datum_do
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("DatumDo", typeof(PriceDiscountIntervalSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual System.DateTime? DatumDo
        {
            get
            {
                return this._DatumDo;
            }
            set
            {
                if (this._DatumDo != value)
                {
                    this.OnDatumDoChanging(value);
                    this.SendPropertyChanging();
                    this._DatumDo = value;
                    this.SendPropertyChanged("DatumDo");
                    this.OnDatumDoChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: dow
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Dow", typeof(PriceDiscountIntervalSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(PriceDiscountInterval), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 14,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="Dow")]
        public virtual string Dow
        {
            get
            {
                return this._Dow;
            }
            set
            {
                if (this._Dow != value)
                {
                    this.OnDowChanging(value);
                    this.SendPropertyChanging();
                    this._Dow = value;
                    this.SendPropertyChanged("Dow");
                    this.OnDowChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: cas_od
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("CasOd", typeof(PriceDiscountIntervalSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual System.DateTime? CasOd
        {
            get
            {
                return this._CasOd;
            }
            set
            {
                if (this._CasOd != value)
                {
                    this.OnCasOdChanging(value);
                    this.SendPropertyChanging();
                    this._CasOd = value;
                    this.SendPropertyChanged("CasOd");
                    this.OnCasOdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: cas_do
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("CasDo", typeof(PriceDiscountIntervalSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual System.DateTime? CasDo
        {
            get
            {
                return this._CasDo;
            }
            set
            {
                if (this._CasDo != value)
                {
                    this.OnCasDoChanging(value);
                    this.SendPropertyChanging();
                    this._CasDo = value;
                    this.SendPropertyChanged("CasDo");
                    this.OnCasDoChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: id_sleva
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("PriceDiscount", typeof(PriceDiscountIntervalSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual PriceDiscount PriceDiscount
        {
            get
            {
                return this._PriceDiscount;
            }
            set
            {
                if (this._PriceDiscount != value)
                {
                    this.OnPriceDiscountChanging(value);
                    this.SendPropertyChanging();
                    this._PriceDiscount = value;
                    this.SendPropertyChanged("PriceDiscount");
                    this.OnPriceDiscountChanged();
                }
            }
        }
   
        public virtual event PropertyChangingEventHandler PropertyChanging;

	// Mam v bazove tride, nema smysl generovat
	// public virtual event PropertyChangedEventHandler PropertyChanged;

        protected virtual void SendPropertyChanging()
        {
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, emptyChangingEventArgs);
        }

        protected virtual void SendPropertyChanging(System.String propertyName) 
        {   	
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, new PropertyChangingEventArgs(propertyName));
        }

        protected virtual void SendPropertyChanged(System.String propertyName)
        {    
		// mam v bazove tride, proto musim volat takto jednoduse
		OnPropertyChanged(propertyName);
/*		        var handler = this.PropertyChanged;
            if (handler != null)
                handler(this, new PropertyChangedEventArgs(propertyName));*/
        }
    }

}
