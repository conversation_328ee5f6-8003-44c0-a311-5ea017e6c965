using System;
using System.Collections.Generic;
using Anete.Utils.ComponentModel.Attributes;

namespace Anete.Common.Data.Interface.Entities
{
	/// <summary>
	/// Atribut pro popis entity
	/// </summary>
	public class EntityDescriptionAttribute : ClassSRDescriptionAttribute
	{
		/// <summary>
		/// Initializes a new instance of the EntityDescriptionAttribute class.
		/// </summary>
		public EntityDescriptionAttribute(Type classType)
			: base(classType, EntityDescriptionAttributeSR.ResourceManager)
		{

		}
	}
}
