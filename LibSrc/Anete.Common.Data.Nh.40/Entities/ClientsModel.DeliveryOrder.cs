//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Anete.Common.Data.Nh.Entities
{

    /// <summary>
    /// Tabulka: dbo.DST_DodakInt
    /// </summary>
    public partial class DeliveryOrder : Anete.Data.Nh.NhEntityBase, INotifyPropertyChanging, INotifyPropertyChanged {

        private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(System.String.Empty);

        private string _DeliveryOrderId;

        private System.DateTime? _DispatchTime;

        private bool _Dispatched;

        private short? _Revision;

        private string _Note;

        private Workplace _Workplace;

        private Driver _Driver;

        private Vehicle _Vehicle;

        private Delivery _Delivery;

        private Inquiry _Inquiry;

        private ISet<DeliveryOrderRow> _Rows;
    
        #region Extensibility Method Definitions
        
        partial void OnCreated();

        public override bool Equals(object obj)
        {
          DeliveryOrder toCompare = obj as DeliveryOrder;
          if (toCompare == null)
          {
            return false;
          }

          if (!Object.Equals(this.DeliveryOrderId, toCompare.DeliveryOrderId))
            return false;
         // pokud mam default primary key, znamena to, ze jedna o nove vytvorenou entitu, ktera jeste nema key prirazen. 
        // V takove pripade musim porovnat podle instance
        bool isDefault = true;
        if (this.DeliveryOrderId != default(string))
        {
          isDefault = false;
        }
     

          if (isDefault)
          {
            return object.ReferenceEquals(toCompare, this);
          }     
          return true;
        }

	private int? _hashCode = null;
        public override int GetHashCode()
        {
	if (_hashCode == null)
          {
          _hashCode = 13;
          _hashCode = (_hashCode * 7) + DeliveryOrderId.GetHashCode();
}
          return _hashCode.Value;
        }
        partial void OnDeliveryOrderIdChanging(string value);
        
        partial void OnDeliveryOrderIdChanged();
        partial void OnDispatchTimeChanging(System.DateTime? value);
        
        partial void OnDispatchTimeChanged();
        partial void OnDispatchedChanging(bool value);
        
        partial void OnDispatchedChanged();
        partial void OnRevisionChanging(short? value);
        
        partial void OnRevisionChanged();
        partial void OnNoteChanging(string value);
        
        partial void OnNoteChanged();
        partial void OnWorkplaceChanging(Workplace value);

        partial void OnWorkplaceChanged();
        partial void OnDriverChanging(Driver value);

        partial void OnDriverChanged();
        partial void OnVehicleChanging(Vehicle value);

        partial void OnVehicleChanged();
        partial void OnDeliveryChanging(Delivery value);

        partial void OnDeliveryChanged();
        partial void OnInquiryChanging(Inquiry value);

        partial void OnInquiryChanged();
        
        #endregion
        public DeliveryOrder()
        {
            this._Dispatched = false;
            this._Rows = new HashSet<DeliveryOrderRow>();
            OnCreated();
        }

    
        /// <summary>
        /// Sloupec: cislo_dokladu
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("DeliveryOrderId", typeof(DeliveryOrderSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(DeliveryOrder), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 12,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="DeliveryOrderId")]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(DeliveryOrder), Tag="DeliveryOrderId")]
        public virtual string DeliveryOrderId
        {
            get
            {
                return this._DeliveryOrderId;
            }
            set
            {
                if (this._DeliveryOrderId != value)
                {
                    this.OnDeliveryOrderIdChanging(value);
                    this.SendPropertyChanging();
                    this._DeliveryOrderId = value;
                    this.SendPropertyChanged("DeliveryOrderId");
                    this.OnDeliveryOrderIdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: cas_expedice
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("DispatchTime", typeof(DeliveryOrderSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual System.DateTime? DispatchTime
        {
            get
            {
                return this._DispatchTime;
            }
            set
            {
                if (this._DispatchTime != value)
                {
                    this.OnDispatchTimeChanging(value);
                    this.SendPropertyChanging();
                    this._DispatchTime = value;
                    this.SendPropertyChanged("DispatchTime");
                    this.OnDispatchTimeChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: expedovano
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Dispatched", typeof(DeliveryOrderSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(DeliveryOrder), Tag="Dispatched")]
        public virtual bool Dispatched
        {
            get
            {
                return this._Dispatched;
            }
            set
            {
                if (this._Dispatched != value)
                {
                    this.OnDispatchedChanging(value);
                    this.SendPropertyChanging();
                    this._Dispatched = value;
                    this.SendPropertyChanged("Dispatched");
                    this.OnDispatchedChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: revize
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Revision", typeof(DeliveryOrderSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual short? Revision
        {
            get
            {
                return this._Revision;
            }
            set
            {
                if (this._Revision != value)
                {
                    this.OnRevisionChanging(value);
                    this.SendPropertyChanging();
                    this._Revision = value;
                    this.SendPropertyChanged("Revision");
                    this.OnRevisionChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: poznamka
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Note", typeof(DeliveryOrderSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(DeliveryOrder), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 300,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="Note")]
        public virtual string Note
        {
            get
            {
                return this._Note;
            }
            set
            {
                if (this._Note != value)
                {
                    this.OnNoteChanging(value);
                    this.SendPropertyChanging();
                    this._Note = value;
                    this.SendPropertyChanged("Note");
                    this.OnNoteChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: id_jidelna
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Workplace", typeof(DeliveryOrderSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual Workplace Workplace
        {
            get
            {
                return this._Workplace;
            }
            set
            {
                if (this._Workplace != value)
                {
                    this.OnWorkplaceChanging(value);
                    this.SendPropertyChanging();
                    this._Workplace = value;
                    this.SendPropertyChanged("Workplace");
                    this.OnWorkplaceChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: id_ridic
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Driver", typeof(DeliveryOrderSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual Driver Driver
        {
            get
            {
                return this._Driver;
            }
            set
            {
                if (this._Driver != value)
                {
                    this.OnDriverChanging(value);
                    this.SendPropertyChanging();
                    this._Driver = value;
                    this.SendPropertyChanged("Driver");
                    this.OnDriverChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: id_vozidlo
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Vehicle", typeof(DeliveryOrderSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual Vehicle Vehicle
        {
            get
            {
                return this._Vehicle;
            }
            set
            {
                if (this._Vehicle != value)
                {
                    this.OnVehicleChanging(value);
                    this.SendPropertyChanging();
                    this._Vehicle = value;
                    this.SendPropertyChanged("Vehicle");
                    this.OnVehicleChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: id_zavoz
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Delivery", typeof(DeliveryOrderSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual Delivery Delivery
        {
            get
            {
                return this._Delivery;
            }
            set
            {
                if (this._Delivery != value)
                {
                    this.OnDeliveryChanging(value);
                    this.SendPropertyChanging();
                    this._Delivery = value;
                    this.SendPropertyChanged("Delivery");
                    this.OnDeliveryChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: id_poptavka
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Inquiry", typeof(DeliveryOrderSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual Inquiry Inquiry
        {
            get
            {
                return this._Inquiry;
            }
            set
            {
                if (this._Inquiry != value)
                {
                    this.OnInquiryChanging(value);
                    this.SendPropertyChanging();
                    this._Inquiry = value;
                    this.SendPropertyChanged("Inquiry");
                    this.OnInquiryChanged();
                }
            }
        }

        [Anete.Data.Attributes.EntitySRDisplayName("Rows", typeof(DeliveryOrderSR))]
        [Anete.Data.Attributes.DbCollectionProperty]
        public virtual ISet<DeliveryOrderRow> Rows
        {
            get
            {
                return this._Rows;
            }
            set
            {
                this._Rows = value;
            }
        }
   
        public virtual event PropertyChangingEventHandler PropertyChanging;

	// Mam v bazove tride, nema smysl generovat
	// public virtual event PropertyChangedEventHandler PropertyChanged;

        protected virtual void SendPropertyChanging()
        {
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, emptyChangingEventArgs);
        }

        protected virtual void SendPropertyChanging(System.String propertyName) 
        {   	
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, new PropertyChangingEventArgs(propertyName));
        }

        protected virtual void SendPropertyChanged(System.String propertyName)
        {    
		// mam v bazove tride, proto musim volat takto jednoduse
		OnPropertyChanged(propertyName);
/*		        var handler = this.PropertyChanged;
            if (handler != null)
                handler(this, new PropertyChangedEventArgs(propertyName));*/
        }
    }

}
