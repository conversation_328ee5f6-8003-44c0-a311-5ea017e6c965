using Anete.Common.Core;
using Anete.Common.Core.Interface.Enums;
using Anete.Common.Data;
using System;
using System.Linq;

namespace Anete.Infrastructure.Core.AppServices.AppInstallationIdResolvers
{
	public interface IApiStartupOptions
	{
		/*/// <summary>
		/// Automaticke prihlaseni bez zobrazeneho dialogu
		/// Pouziva jinou vetev, nez AutoLoginByDialog. Pro simulaci chovani u zakaznika lepe pouzit AutoLoginByDialog
		/// </summary>
		bool AutoLogin { get; }*/

		/// <summary>
		/// Oznaceni servisni aplikace, ktera nema standardni prihlasovaci dialog
		/// </summary>
		bool ServiceAutoLogin { get; }

		/// <summary>
		/// Provede automaticke prihlaseni v ramci zobrazeneho dialogu. Vhodne pro simulaci chovani u zakaznika.
		/// </summary>
		bool AutoLoginOnShowDialog { get; }

		bool AnonymousLogin { get; }

		/// <summary>
		/// Implicitni uzivatelske jmeno - muze byt predano z prikazove radky nebo napripklad nacteno ze souboru nebo cookie po poslednim prihlaseni klienta
		/// </summary>
		string DefaultUserName { get; }

		/// <summary>
		/// Implicitni heslo - predavam si pres prikazovou radku v pripade pouziti automatickeho prihlaseni
		/// </summary>
		string DefaultPassword { get; }

		/// <summary>
		/// Moznost explicitne definovat credentials. Umoznuje na zaklade parametru prikazove radky zmenit ciste Windows autentizaci na prihlasovaci udaje. Hodi se nam pro servisni ucely.
		/// Slouzi pro prepsani standardni konfigurace.
		/// </summary>
		AppCredentialsType[] ExplicitAppCredentialsTypes {get;}

		/// <summary>
		/// Moznost explicitne definovat, jake autentizace chci pro prihlaseni pouzit. Autentizace musi byt nastavena na serveru. Timto parametrem pouze omezim jejich vycet.
		/// Sloupzi pro prepsani standardni konfigurace.
		///
		/// Typicky Use-Case:
		/// Na serveru nastavime podporova autentizace LDAP a SQL server
		/// Pro klienta nastavime pouze LDAP
		/// Prikazovou radkou si pro jeden pocitac vynutim autentizaci SQL server a CredentialsType jako Credentials
		/// Aplikace pak zobrazi dialog s SQL server autentizaci i prestoze je nastavena v Konfiguratoru jinak
		/// </summary>
		AppAuthenticationType[] ExplicitAppAuthenticationTypes { get; }
	}
}
