using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using Anete.Utils;
using Anete.Utils.Extensions;

namespace Anete.Wcf.Faults
{
    /// <summary>
    /// Popis chyby pro sluzby, ktere odpovidaji vice zaznamy s chybovym kodem
    /// </summary>
    [DataContract(Name = "ListFaultOf{0}")]
    public class ListFault<TResponseItem> : IEquatable<ListFault<TResponseItem>>
        where TResponseItem : IEquatable<TResponseItem>
    {
        #region constructors...
		/// <summary>
		/// Initializes a new instance
		/// </summary>
		/// <param name="items">Seznam hlaseni</param>
        public ListFault(IEnumerable<TResponseItem> items)
        {
            Items = items.ToArray();
        }
		
		/// <summary>
		/// Zde musi byt i bezparametricky konstruktor. Pokud se tridy generuji z WSDL a pouzije se reuse puvodni assembly,
		/// neni kod zkompilovatelny.
		/// </summary>
		public ListFault()
		{
			
		}
        #endregion

        #region public properties...
        /// <summary>
        /// Seznam hlaseni
        /// Bohuzel DataContractSerializer nesnasi IEnumerable(of T), musi zde byt pole
        /// </summary>
        [DataMember(IsRequired = false)]
        public TResponseItem[] Items { get; private set; }
        #endregion

        #region IEquatable<MultiRecordResponse> Members
        /// <summary>
        /// Implementace IEquatable
        /// </summary>
        public bool Equals(ListFault<TResponseItem> other)
        {
            if (other == null)
            {
                return false;
            }

            return base.Equals(other) && Items.SequenceEqual(other.Items);
        }
        #endregion

        #region public overrides...
        /// <summary>
        /// Determines whether the specified <see cref="System.Object"/> is equal to this instance.
        /// </summary>
        public override bool Equals(object obj)
        {
            ListFault<TResponseItem> other = obj as ListFault<TResponseItem>;

            if (other != null)
            {
                return Equals(other);
            }

            return base.Equals(obj);
        }

        /// <summary>
        /// Returns a hash code for this instance.
        /// </summary>
        public override int GetHashCode()
        {
            return base.GetHashCode() ^ HashCodeUtils.GetHashCode(Items);
        }

		/// <summary>
		/// Returns a <see cref="System.String"/> that represents this instance.
		/// </summary>
		public override string ToString()
		{
			return Items.ToDelimitedString(Environment.NewLine);
		}
        #endregion

    }
}
