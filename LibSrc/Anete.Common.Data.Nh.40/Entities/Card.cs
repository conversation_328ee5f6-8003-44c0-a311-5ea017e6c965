using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Anete.Utils;
using System.ComponentModel.DataAnnotations;
using Anete.Utils.Extensions;

namespace Anete.Common.Data.Nh.Entities
{
    public partial class Card
    {
        /// <summary>
        /// Vraci druh pohybu, ktery byl pouzit pro zalozeni karty.
        /// </summary>
        /// <returns></returns>
        private CardMovement GetNewestIssueCardMovement()
        {
            CardMovement lastCardMovement = CardMovements.Where(cm => cm.MovementType == CardMovementType.Registration || cm.MovementType == CardMovementType.Issue).OrderByDescending(cm => cm.TimeStamp).FirstOrDefault();
            if (lastCardMovement == null)
            {
                throw new ArgumentException(string.Format("Pro kartu {0} nebyl nalezen zadny druh pohybu pouzity pro jeji zalozeni (vydej, registrace)", CardId));
            }

            return lastCardMovement;
        }

        /// <summary>
        /// Vraci zpusob vydeje karty, tzn. jakym zpusobem byla karta pridana do systemu kredit.
        /// </summary>
        /// <returns></returns>
        public virtual IssueType GetIssueType()
        {
            CardMovement lastCardMovement = GetNewestIssueCardMovement();
            switch (lastCardMovement.MovementType)
            {
                case CardMovementType.Issue:
                    return IssueType.Issue;

                case CardMovementType.Registration:
                    return IssueType.Registration;

                default:
                    throw ExcUtils.ArgumentOutOfRange("lastCardMovement.MovementType", lastCardMovement.MovementType);
            }
        }

        /// <summary>
        /// Vraci cenu, za kterou byla karta vydana/registrovana.
        /// Zjistuje se z pohybu pro danou kartu.
        /// </summary>
        /// <returns></returns>
        public virtual decimal GetIssuedPrice()
        {
            return GetNewestIssueCardMovement().Price;
        }
    }
}
