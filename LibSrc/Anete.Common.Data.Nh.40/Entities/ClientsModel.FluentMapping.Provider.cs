//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate Fluent Mapping template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;
using FluentNHibernate.Mapping;
using FluentNHibernate.MappingModel.Collections;

namespace Anete.Common.Data.Nh.Entities
{
    /// <summary>
    /// There are no comments for ProviderMap in the schema.
    /// </summary>
    public partial class ProviderMap : ClassMap<Provider>
    {
        /// <summary>
        /// There are no comments for ProviderMap constructor in the schema.
        /// </summary>
        public ProviderMap()
        {
              Schema(@"dba");
              Table(@"CFProvozovatele");
              DynamicInsert();
              DynamicUpdate();
              LazyLoad();
              Id(x => x.ProviderId)
                .Column("id_provozovatel")
                .CustomType("Int16")
                .Access.Property().CustomSqlType("smallint")
                .Not.Nullable()
                .Precision(5)
                .GeneratedBy.Assigned();
              Map(x => x.Description)    
                .Column("popis")
                .CustomType("String")
                .Access.Property()
                .Generated.Never().CustomSqlType("varchar(50)")
                .Not.Nullable()
                .Length(50);
              Map(x => x.Name)    
                .Column("nazev")
                .CustomType("String")
                .Access.Property()
                .Generated.Never().CustomSqlType("varchar(120)")
                .Length(120);
              Map(x => x.Address)    
                .Column("adresa")
                .CustomType("String")
                .Access.Property()
                .Generated.Never().CustomSqlType("varchar(60)")
                .Length(60);
              Map(x => x.Place)    
                .Column("misto")
                .CustomType("String")
                .Access.Property()
                .Generated.Never().CustomSqlType("varchar(60)")
                .Length(60);
              Map(x => x.IdentificationCode)    
                .Column("IC")
                .CustomType("Int32")
                .Access.Property()
                .Generated.Never().CustomSqlType("int")
                .Precision(10);
              Map(x => x.VatIdentificationCode)    
                .Column("DIC")
                .CustomType("String")
                .Access.Property()
                .Generated.Never().CustomSqlType("varchar(14)")
                .Length(14);
              Map(x => x.VatPayer)    
                .Column("platce_dph")
                .CustomType("Boolean")
                .Access.Property()
                .Generated.Never()
                .Default(@"1").CustomSqlType("bit")
                .Not.Nullable();
              Map(x => x.EetEnabled)    
                .Column("EetPovoleno")
                .CustomType("Boolean")
                .Access.Property()
                .Generated.Never()
                .Default(@"1").CustomSqlType("bit")
                .Not.Nullable();
              Map(x => x.IDUB)    
                .Column("IDUB")
                .CustomType("String")
                .Access.Property()
                .Generated.Never().CustomSqlType("varchar(14)")
                .Length(14);
              Map(x => x.KodUB)    
                .Column("KodUB")
                .CustomType("String")
                .Access.Property()
                .Generated.Never().CustomSqlType("char(5)")
                .Length(5);
              Map(x => x.Kod)    
                .Column("kod")
                .CustomType("String")
                .Access.Property()
                .Generated.Never().CustomSqlType("varchar(10)")
                .Length(10);
              HasMany<ProviderGroupCategory>(x => x.ProviderGroupCategories)
                .Access.Property()
                .AsSet()
                .Cascade.None()
                .LazyLoad()
                // .OptimisticLock.Version() /*bug (or missing feature) in Fluent NHibernate*/
                .Inverse()
                .Generic()
                .KeyColumns.Add("id_provozovatel", mapping => mapping.Name("id_provozovatel")
                                                                     .SqlType("smallint")
                                                                     .Not.Nullable());
              HasMany<ClientBalanceReceipt>(x => x.ClientBalanceReceipts)
                .Access.Property()
                .AsSet()
                .Cascade.None()
                .LazyLoad()
                // .OptimisticLock.Version() /*bug (or missing feature) in Fluent NHibernate*/
                .Inverse()
                .Generic()
                .KeyColumns.Add("id_provozovatel", mapping => mapping.Name("id_provozovatel")
                                                                     .SqlType("smallint")
                                                                     .Not.Nullable());
              HasMany<Workplace>(x => x.Workplaces)
                .Access.Property()
                .AsSet()
                .Cascade.None()
                .LazyLoad()
                // .OptimisticLock.Version() /*bug (or missing feature) in Fluent NHibernate*/
                .Inverse()
                .Generic()
                .KeyColumns.Add("id_provozovatel", mapping => mapping.Name("id_provozovatel")
                                                                     .SqlType("smallint")
                                                                     .Not.Nullable());
              HasMany<EetCertificate>(x => x.EetCertificates)
                .Access.Property()
                .AsSet()
                .Cascade.None()
                .LazyLoad()
                // .OptimisticLock.Version() /*bug (or missing feature) in Fluent NHibernate*/
                .Inverse()
                .Generic()
                .KeyColumns.Add("id_provozovatel", mapping => mapping.Name("id_provozovatel")
                                                                     .SqlType("smallint")
                                                                     .Not.Nullable());
              HasMany<UsBudova>(x => x.Budovas)
                .Access.Property()
                .AsSet()
                .Cascade.None()
                .LazyLoad()
                // .OptimisticLock.Version() /*bug (or missing feature) in Fluent NHibernate*/
                .Inverse()
                .Generic()
                .KeyColumns.Add("idProvozovatel", mapping => mapping.Name("idProvozovatel")
                                                                     .SqlType("smallint")
                                                                     .Not.Nullable());
              ExtendMapping();
        }

        #region Partial Methods

        partial void ExtendMapping();

        #endregion
    }

}
