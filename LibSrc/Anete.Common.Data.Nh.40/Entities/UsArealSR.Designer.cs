//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Anete.Common.Data.Nh.Entities {
    using System;
    
    
    /// <summary>
    /// A strongly-typed resource class, for looking up localized strings, formatting them, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilderEx class via the InternalResXFileCodeGeneratorEx custom tool.
    // To add or remove a member, edit your .ResX file then rerun the InternalResXFileCodeGeneratorEx custom tool or rebuild your VS.NET project.
    // Copyright (c) Dmytro <PERSON> 2006-2018 (http://dmytro.kryvko.googlepages.com/)
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("DMKSoftware.CodeGenerators.Tools.StronglyTypedResourceBuilderEx", "2.6.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Naming", "CA1724:TypeNamesShouldNotMatchNamespaces")]
    internal partial class UsArealSR {
        
        private static global::System.Resources.ResourceManager _resourceManager;
        
        private static object _internalSyncObject;
        
        private static global::System.Globalization.CultureInfo _resourceCulture;
        
        /// <summary>
        /// Initializes a UsArealSR object.
        /// </summary>
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal UsArealSR() {
        }
        
        /// <summary>
        /// Thread safe lock object used by this class.
        /// </summary>
        internal static object InternalSyncObject {
            get {
                if (object.ReferenceEquals(_internalSyncObject, null)) {
                    global::System.Threading.Interlocked.CompareExchange(ref _internalSyncObject, new object(), null);
                }
                return _internalSyncObject;
            }
        }
        
        /// <summary>
        /// Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(_resourceManager, null)) {
                    global::System.Threading.Monitor.Enter(InternalSyncObject);
                    try {
                        if (object.ReferenceEquals(_resourceManager, null)) {
                            global::System.Threading.Interlocked.Exchange(ref _resourceManager, new global::System.Resources.ResourceManager("Anete.Common.Data.Nh.Entities.UsArealSR", typeof(UsArealSR).Assembly));
                        }
                    }
                    finally {
                        global::System.Threading.Monitor.Exit(InternalSyncObject);
                    }
                }
                return _resourceManager;
            }
        }
        
        /// <summary>
        /// Overrides the current thread's CurrentUICulture property for all
        /// resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return _resourceCulture;
            }
            set {
                _resourceCulture = value;
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to '#Aktivni#'.
        /// </summary>
        internal static string Aktivni {
            get {
                return ResourceManager.GetString(ResourceNames.Aktivni, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to '#Budovy#'.
        /// </summary>
        internal static string Budovy {
            get {
                return ResourceManager.GetString(ResourceNames.Budovy, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to '#Id#'.
        /// </summary>
        internal static string Id {
            get {
                return ResourceManager.GetString(ResourceNames.Id, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to '#Mesto#'.
        /// </summary>
        internal static string Mesto {
            get {
                return ResourceManager.GetString(ResourceNames.Mesto, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to '#Nazev#'.
        /// </summary>
        internal static string Nazev {
            get {
                return ResourceManager.GetString(ResourceNames.Nazev, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to '#Popis#'.
        /// </summary>
        internal static string Popis {
            get {
                return ResourceManager.GetString(ResourceNames.Popis, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Lists all the resource names as constant string fields.
        /// </summary>
        internal class ResourceNames {
            
            /// <summary>
            /// Stores the resource name 'Aktivni'.
            /// </summary>
            internal const string Aktivni = "Aktivni";
            
            /// <summary>
            /// Stores the resource name 'Budovy'.
            /// </summary>
            internal const string Budovy = "Budovy";
            
            /// <summary>
            /// Stores the resource name 'Id'.
            /// </summary>
            internal const string Id = "Id";
            
            /// <summary>
            /// Stores the resource name 'Mesto'.
            /// </summary>
            internal const string Mesto = "Mesto";
            
            /// <summary>
            /// Stores the resource name 'Nazev'.
            /// </summary>
            internal const string Nazev = "Nazev";
            
            /// <summary>
            /// Stores the resource name 'Popis'.
            /// </summary>
            internal const string Popis = "Popis";
        }
    }
}
