using System;
using System.Text;
using System.IO;
using log4net;
using log4net.Core;
using System.Reflection;

namespace Anete.Log4Net.Core
{
    // TODO9: neotestovano!
    /// <summary>
    /// Adapter pro zapis do logu pro ty zari<PERSON>i, ktere potrebuji zapisovat do TextWriter.
    /// Pouziva se pro logovani SQL v LINQ. 
    /// Pouziti:
    /// <code>
    /// MyDataContext db = new MyDataContext();
    /// db.Log = new Log4NetTextWriter();
    /// </code>
    /// </summary>
    public class Log4NetTextWriter: TextWriter
    {
        #region private fields...
        private bool _isOpen;
        private static UnicodeEncoding _encoding;
        #endregion

        /// <summary>
        /// Initializes a new instance of the <see cref="Log4NetTextWriter"/> class.
        /// </summary>
        public Log4NetTextWriter()
            : this(null)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="Log4NetTextWriter"/> class.
        /// </summary>
        /// <param name="log">Log, do ktereho zapisovat</param>
        public Log4NetTextWriter(ILog log)
            : this(log, Level.Debug)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="Log4NetTextWriter"/> class.
        /// </summary>
        /// <param name="log">Log, do ktereho zapisovat</param>
        /// <param name="level">Uroven, s jakou zapisovat</param>
        public Log4NetTextWriter(ILog log, Level level)
            : this(log, level, null)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="Log4NetTextWriter"/> class.
        /// </summary>
        /// <param name="log">Log, do ktereho zapisovat</param>
        /// <param name="level">Uroven, s jakou zapisovat</param>
        /// <param name="formatProvider">The format provider.</param>
        public Log4NetTextWriter(ILog log, Level level, IFormatProvider formatProvider)
            : base(formatProvider)
        {
            _log = log != null ? log : LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
            _level = level;
            _isOpen = true;
        }

        #region properties...
        private Level _level;
        /// <summary>
        /// Uroven, s jakou zapisovat
        /// </summary>
        public Level Level
        {
            get
            {
                return _level;
            }
            set
            {
                _level = value;
            }
        }

        private ILog _log;
        /// <summary>
        /// Log, do ktereho zapisovat
        /// </summary>
        public ILog Log
        {
            get
            {
                return _log;
            }
            set
            {
                _log = value;
            }
        }
        #endregion

        #region protected overrides...
        /// <summary>
        /// Releases the unmanaged resources used by the <see cref="T:System.IO.TextWriter"/> and optionally releases the managed resources.
        /// </summary>
        /// <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
        protected override void Dispose(bool disposing)
        {
            _isOpen = false;
            base.Dispose(disposing);
        }
        #endregion

        #region public overrides...
        /// <summary>
        /// Writes a character to the text stream.
        /// </summary>
        /// <param name="value">The character to write to the text stream.</param>
        /// <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextWriter"/> is closed. </exception>
        /// <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
        public override void Write(char value)
        {
            Write(value.ToString());
        }

        /// <summary>
        /// Writes a string to the text stream.
        /// </summary>
        /// <param name="value">The string to write.</param>
        /// <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextWriter"/> is closed. </exception>
        /// <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
        public override void Write(string value)
        {
            if (!_isOpen)
            {
                throw new ObjectDisposedException(null);
            }
			// log4net pridava konec radku sam
            if (value.EndsWith(Environment.NewLine))
            {
                value = value.Remove(value.Length - Environment.NewLine.Length); 
            }
            if (value != null)
            {
                if (_level == Level.Error)
                {
                    _log.Error(value);
                }
                else if (_level == Level.Warn)
                {
                    _log.Warn(value);
                }
                else if (_level == Level.Info)
                {
                    _log.Info(value);
                }
                else
                {
                    _log.Debug(value);
                }
            }
        }

        /// <summary>
        /// Writes a subarray of characters to the text stream.
        /// </summary>
        /// <param name="buffer">The character array to write data from.</param>
        /// <param name="index">Starting index in the buffer.</param>
        /// <param name="count">The number of characters to write.</param>
        /// <exception cref="T:System.ArgumentException">The buffer length minus <paramref name="index"/> is less than <paramref name="count"/>. </exception>
        /// <exception cref="T:System.ArgumentNullException">The <paramref name="buffer"/> parameter is null. </exception>
        /// <exception cref="T:System.ArgumentOutOfRangeException">
        /// 	<paramref name="index"/> or <paramref name="count"/> is negative. </exception>
        /// <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextWriter"/> is closed. </exception>
        /// <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
        public override void Write(char[] buffer, int index, int count)
        {
            if (!_isOpen)
            {
                throw new ObjectDisposedException(null);
            }
            if (buffer == null || index < 0 || count < 0 || buffer.Length - index < count)
            {
                base.Write(buffer, index, count); // delegate throw exception to base class
            }
            Write(new string(buffer, index, count));
        }

        /// <summary>
        /// When overridden in a derived class, returns the <see cref="T:System.Text.Encoding"/> in which the output is written.
        /// </summary>
        /// <value></value>
        /// <returns>The Encoding in which the output is written.</returns>
        public override Encoding Encoding
        {
            get
            {
                if (_encoding == null)
                {
                    _encoding = new UnicodeEncoding(false, false);
                }
                return _encoding;
            }
        }
        #endregion

        
    }
}
