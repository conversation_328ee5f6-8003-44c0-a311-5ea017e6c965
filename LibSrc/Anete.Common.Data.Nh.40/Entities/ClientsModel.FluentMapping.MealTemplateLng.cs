//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate Fluent Mapping template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;
using FluentNHibernate.Mapping;
using FluentNHibernate.MappingModel.Collections;

namespace Anete.Common.Data.Nh.Entities
{
    /// <summary>
    /// There are no comments for MealTemplateLngMap in the schema.
    /// </summary>
    public partial class MealTemplateLngMap : ClassMap<MealTemplateLng>
    {
        /// <summary>
        /// There are no comments for MealTemplateLngMap constructor in the schema.
        /// </summary>
        public MealTemplateLngMap()
        {
              Schema(@"dba");
              Table(@"MenuPopisy");
              LazyLoad();
              CompositeId()
                .KeyProperty(x => x.MealTemplateId, set => {
                    set.Type("Int32");
                    set.ColumnName("pc");
                    set.Access.Property(); } )
                .KeyProperty(x => x.LanguageId, set => {
                    set.Type("Anete.Common.Core.Interface.Enums.ApplicationLanguage, Anete.Common.Core.Interface.40");
                    set.ColumnName("jazyk");
                    set.Access.Property(); } );
              Map(x => x.Name)    
                .Column("popis")
                .CustomType("String")
                .Access.Property()
                .Generated.Never().CustomSqlType("nvarchar(300)")
                .Length(300);
              Map(x => x.Note)    
                .Column("poznamka")
                .CustomType("String")
                .Access.Property()
                .Generated.Never().CustomSqlType("nvarchar(300)")
                .Length(300);
              References(x => x.Language)
                .Class<Language>()
                .Access.Property()
                .Cascade.None()
                .LazyLoad()
                .Not.Insert()
                .Not.Update()
                .Columns("jazyk");
              References(x => x.MealTemplate)
                .Class<MealTemplate>()
                .Access.Property()
                .Cascade.None()
                .LazyLoad()
                .Not.Insert()
                .Not.Update()
                .Columns("pc");
              ExtendMapping();
        }

        #region Partial Methods

        partial void ExtendMapping();

        #endregion
    }

}
