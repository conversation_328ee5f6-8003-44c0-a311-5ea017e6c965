using System;
using System.Collections.Generic;
using System.Xml;

namespace Anete.Utils.Xml.XmlFileSystem
{
	public class ExtractedXmlFileSystemItemsFactory : IXmlFileSystemItemsFactory
	{
		public XmlFileSystemFile CreateXmlFile(XmlFileSystemDirectory dir, XmlReader reader)
		{
			return new XmlFileSystemFileExtracted(dir, reader);
		}

		public XmlFileSystemDirectory CreateXmlDirectory(XmlFileSystemDirectory dir, XmlReader reader)
		{
			return new XmlFileSystemDirectoryExtracted(dir.FileSystem, dir, reader, this);
		}
	}
}