using Anete.Config.Core.Internals;
using Anete.Utils.ComponentModel.Attributes;
using System.ComponentModel;

namespace Anete.Config.Configs.Core.Accommodation.Behaviour
{
	/// <summary>
	/// Rezim odesilani pres sluzbu UbyPort (cizinecka policie)
	/// </summary>
	[ConfigResourceEnum]
	[TypeConverter(typeof(ConfigEnumConverter<UbyPortSetting>))]
	public enum UbyPortSetting
	{
		/// <summary>
		/// Zakazano
		/// </summary>
		[EnumBasedFactory(null)]
		Disabled,

		/// <summary>
		/// Testovaci rezim
		/// </summary>
		[EnumBasedFactory(typeof(UbyPortTestSettings))]
		Test,

		/// <summary>
		/// Produkcni rezim
		/// </summary>
		[EnumBasedFactory(typeof(UbyPortProductionSettings))]
		Production
	}
}
