using Anete.AutoUpgrades.Interface.AppServices;
using Anete.Common.Core.AppUtils;
using Anete.Common.Core.Interface.AppServices;
using Anete.Common.Core.Interface.Enums;
using Anete.Common.Core.Interface.ProductInfo;
using Anete.Common.Wcf;
using Anete.Config.Configs.Core.Global.Behaviour;
using Anete.Config.Core;
using Anete.Log.Core;
using Anete.Log4Net.Core;
using Anete.Utils;
using Anete.Utils.AppServices;
using System;
using System.ServiceModel;
using System.Threading;
using Anete.Common.Core.Interface.AppServices.InstalledApplication;
using Anete.Common.Core.Interface.Enums.InstallationLocationAttributes;
using Anete.Utils.Extensions;
using Unity;

namespace Anete.AutoUpgrades
{
	/// <summary>
	/// Trida, ktera se stara o inicializaci automatickych aktualizaci v aplikaci
	/// </summary>
	public abstract class AutoUpgradesAppInitializer : IAutoUpgradesAppInitializer
	{
		#region private fields...
		private static readonly ILogEx _log = LogManagerEx.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
		private readonly short _appInstallationId;
		private readonly IProduct _product;
		private readonly IComputerIdProvider _computerId;
		private readonly bool _throwExceptionIfCannotCheckUpgradeState;

		#endregion

		#region constructors...
		/// <summary>
		/// Initializes a new instance of the AutoUpgradesAppInitializer class.
		/// </summary>
		/// <param name="product">The product.</param>
		/// <param name="computerId">The computer id.</param>
		/// <param name="appInstallationId">The app installation id.</param>
		/// <param name="throwExceptionIfCannotCheckUpgradeState">
		/// Vyvolat vyjimku, pokud nejsem schopen zjistit stav aplikace?
		/// To se stane v pripade, kdy na pocitaci nebezi sluzba automatickych aktualizaci.
		/// Pro vyvoj aplikace, DEBUG profile, se hodi nastavit false
		/// </param>
		public AutoUpgradesAppInitializer(IProduct product, IComputerIdProvider computerId, short appInstallationId, bool throwExceptionIfCannotCheckUpgradeState)
		{
			_throwExceptionIfCannotCheckUpgradeState = throwExceptionIfCannotCheckUpgradeState;
			_computerId = computerId;
			_product = product;
			_appInstallationId = appInstallationId;
		}
		#endregion

		#region protected virtual methods...
		/// <summary>
		/// Zobrazeni varovani
		/// </summary>
		/// <param name="message"></param>
		protected abstract void ShowWarning(string message);

		/// <summary>
		/// Zobrazeni chyby
		/// </summary>
		/// <param name="message"></param>
		protected abstract void ShowError(string message);

		/// <summary>
		/// Potvrzeni spusteni aktualizatoru.
		/// Vola se ve chvili, kdy je nalezena aktualizace a uzivateli ma byt zobrazen dotaz, zda chce pro ni
		/// spustit aktualizator.
		/// </summary>
		/// <param name="message"></param>
		/// <returns></returns>
		protected abstract bool ConfirmRunAktualizator(string message);
		#endregion

		/// <summary>
		/// Zkontroluje, zda existuje nejaky vyzadovany upgrade. Pokud ano, zobrazi hlasku a vrati false.
		/// </summary>
		/// <returns></returns>
		public bool Initialize()
		{
			_log.Info("InitAutoUpgrades");

			if (MultipleApplicationInstanceUtils.IsInstance())
			{
				// pro pojmenovou instanci nechci zadnou kontrolu aktualizaci resit
				// zadne aktualizace neexistuji, kopiruje se rucne
				_log.Info("Pojmenovana instance, nebudu kontrolovat aktualizace");
				return true;
			}

			IUnityContainer container = DependencyContainer.Instance;
			IConfigManager configManager = container.Resolve<IConfigManager>();
			// aplikace muzou mit automaticke aktualizace natrvalo vypnuty
			if (!configManager.GetConfig<GlobalBehaviourAutoUpdatesConfig>().EnableAutoUpdates)
			{
				_log.Info("Kontrola aktualizaci je vypnuta");
				return true;
			}


#if !DEBUG
			// ToJNOTE: 24. 8. 2015 Je treba provest kontrolu, zda existuji prislusne zaznamy v registru. S nimi stale spolupracuji instalacni balicky.
			// Nemuzu mit zalozeny instalacni system na dostupnosti databaze AneteUpdates, musi probehnout i bez ni.
			// dale musim provest kontrolu, zda neni vubec zakazano aplikaci spoustet
			UpdateTypeId updateType = _product.ApplicationType.ToUpdateType();
			InstallationLocationBaseAttribute att = updateType.FindAttributeOfType<InstallationLocationBaseAttribute>();
			InstalledApplicationBase installedApplicationBase = att.CreateManager(container).GetInstalledApplicationDetail(updateType);

			if (installedApplicationBase == null)
			{
				throw new InvalidOperationException(AutoUpgradesAppInitializerSR.AplikaceNeniNainstalovanaFormat(EnumUtils.ToLocalizedString(_product.ApplicationType)));
			}

			if (installedApplicationBase is AneteInstalledApplication {CanRun: false})
			{
				ShowWarning(AutoUpgradesAppInitializerSR.RunApplicationDenied);
				return false;
			}
#endif

			string upgradeText = null;
			RunApplicationState applicationState = RunApplicationState.Unassigned;
			string adress = AutoUpgradesServiceConfig.GetUpgradesServiceAdress();

			int iteration = 0;
			bool canThrowException = false;
			do
			{
				try
				{
#if !NETFRAMEWORK
					throw new PlatformNotSupportedException();
#else
					WcfProxyCaller.Call<IAutoUpgradesService>(null, adress, new NetNamedPipeBinding()
					{
						// potrebuju byt schopen prenest pripadne dlouhe chybove hlaseni, viz. https://helpdesk.anete.com/issues/62324 
						MaxBufferPoolSize = 5000000, MaxBufferSize = 500000, MaxReceivedMessageSize  = 500000
					}, proxy =>
					{
						iteration++;
						applicationState = proxy.CanRunApplication(_product.ApplicationType, _appInstallationId, _product.SoftwarePlatform, out upgradeText);
					});
#endif
				}
				catch (Exception ex)
				{
					if (!ExcUtils.IsCatchableExceptionType(ex))
					{
						throw;
					}

					if (canThrowException)
					{
						if (_throwExceptionIfCannotCheckUpgradeState)
						{
							throw new ArgumentException(AutoUpgradesAppInitializerSR.AutoUpgradesServiceError, ex);
						}

						// pokud nemam vyvolavat vyjimku, ihned vratim true a dal nepokracuju
						LogWcfCallerException(ex);
						return true;
					}
					else
					{
						// vyjimku zatim nemuzu zpracovat, jen ji proste zaloguju
						LogWcfCallerException(ex);
						if (!_throwExceptionIfCannotCheckUpgradeState)
						{
							// pokud me obecne vyjimky nezajimaji, muzu ihned z metody vyskocit
							return true;
						}
					}
				}

				if (applicationState == RunApplicationState.Unassigned)
				{
					if (iteration == 5)
					{
						// u dalsi iterace jiz povolim zhavarovani programu, sluzba proste nebezi
						canThrowException = true;
					}

					// s dalsim volanim pockam 5s
					Thread.Sleep(2000);
				}
			} while (applicationState == RunApplicationState.Unassigned);

			if (applicationState == RunApplicationState.NeedsUpgrade)
			{
				// zobrazim potvrzovaci dialog
				if (ConfirmRunAktualizator(string.Format("{0}\n{1}", upgradeText, AutoUpgradesAppInitializerSR.UpgradeNow)))
				{
					try
					{
#if !NETFRAMEWORK
						throw new PlatformNotSupportedException();
#else
						// spustit aktualizator					
						WcfProxyCaller.Call<IAutoUpgradesService>(null, adress, new NetNamedPipeBinding(), proxy =>
						{
							string errorText;
							if (!proxy.RunRequestedUpgrades(_product.ApplicationType, _product.SoftwarePlatform, _appInstallationId, out errorText))
							{
								ShowError(errorText);
							}

						});
#endif
					}
					catch (Exception ex)
					{
						if (!ExcUtils.IsCatchableExceptionType(ex))
						{
							throw;
						}

						if (_throwExceptionIfCannotCheckUpgradeState)
						{
							throw new ArgumentException(AutoUpgradesAppInitializerSR.AutoUpgradesServiceError, ex);
						}

						// pokud nemam vyvolavat vyjimku, ihned vratim true a dal nepokracuju
						LogWcfCallerException(ex);
						return true;
					}
				}
				return false;
			}
			else
			{
				// zobrazim pouze varovani
				if (upgradeText != null)
				{
					if (!_product.CurrentPart.ApplicationInfo.IsKiosk)
					{
						ShowWarning(upgradeText);
					}
					else
					{
						_log.Warn(upgradeText);
					}
				}

				return applicationState == RunApplicationState.Ok;
			}
		}

		private static void LogWcfCallerException(Exception ex)
		{
			_log.Warn("Sluzba s aktualizacemi je nedostupna nebo dotaz skoncil chybou. Programove urceno, ze aplikace pobezi dale.", ex);
		}
	}
}
