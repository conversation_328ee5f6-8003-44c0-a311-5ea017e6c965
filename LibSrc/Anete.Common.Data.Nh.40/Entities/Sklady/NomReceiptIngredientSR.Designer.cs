//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Anete.Common.Data.Nh.Entities.Sklady {
    using System;
    
    
    /// <summary>
    /// A strongly-typed resource class, for looking up localized strings, formatting them, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilderEx class via the InternalResXFileCodeGeneratorEx custom tool.
    // To add or remove a member, edit your .ResX file then rerun the InternalResXFileCodeGeneratorEx custom tool or rebuild your VS.NET project.
    // Copyright (c) Dmytro Kryvko 2006-2016 (http://dmytro.kryvko.googlepages.com/)
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("DMKSoftware.CodeGenerators.Tools.StronglyTypedResourceBuilderEx", "*******")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
#if !SILVERLIGHT && !PocketPC && !Smartphone && !WindowsCE
    [global::System.Reflection.ObfuscationAttribute(Exclude=true, ApplyToMembers=true)]
#endif
    [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Naming", "CA1724:TypeNamesShouldNotMatchNamespaces")]
    internal partial class NomReceiptIngredientSR {
        
        private static global::System.Resources.ResourceManager _resourceManager;
        
        private static object _internalSyncObject;
        
        private static global::System.Globalization.CultureInfo _resourceCulture;
        
        /// <summary>
        /// Initializes a NomReceiptIngredientSR object.
        /// </summary>
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal NomReceiptIngredientSR() {
        }
        
        /// <summary>
        /// Thread safe lock object used by this class.
        /// </summary>
        internal static object InternalSyncObject {
            get {
                if (object.ReferenceEquals(_internalSyncObject, null)) {
                    global::System.Threading.Interlocked.CompareExchange(ref _internalSyncObject, new object(), null);
                }
                return _internalSyncObject;
            }
        }
        
        /// <summary>
        /// Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(_resourceManager, null)) {
                    global::System.Threading.Monitor.Enter(InternalSyncObject);
                    try {
                        if (object.ReferenceEquals(_resourceManager, null)) {
                            global::System.Threading.Interlocked.Exchange(ref _resourceManager, new global::System.Resources.ResourceManager("Anete.Common.Data.Nh.Entities.Sklady.NomReceiptIngredientSR", typeof(NomReceiptIngredientSR).Assembly));
                        }
                    }
                    finally {
                        global::System.Threading.Monitor.Exit(InternalSyncObject);
                    }
                }
                return _resourceManager;
            }
        }
        
        /// <summary>
        /// Overrides the current thread's CurrentUICulture property for all
        /// resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return _resourceCulture;
            }
            set {
                _resourceCulture = value;
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to '#KODZBOZI#'.
        /// </summary>
        internal static string KODZBOZI {
            get {
                return ResourceManager.GetString(ResourceNames.KODZBOZI, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to '#MJ#'.
        /// </summary>
        internal static string MJ {
            get {
                return ResourceManager.GetString(ResourceNames.MJ, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to '#MNOZSTVICISTE#'.
        /// </summary>
        internal static string MNOZSTVICISTE {
            get {
                return ResourceManager.GetString(ResourceNames.MNOZSTVICISTE, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to '#MNOZSTVIHRUBE#'.
        /// </summary>
        internal static string MNOZSTVIHRUBE {
            get {
                return ResourceManager.GetString(ResourceNames.MNOZSTVIHRUBE, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to '#NOMRECEPTURY_RECEPTURA_ID#'.
        /// </summary>
        internal static string NOMRECEPTURY_RECEPTURA_ID {
            get {
                return ResourceManager.GetString(ResourceNames.NOMRECEPTURY_RECEPTURA_ID, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to '#NOMRECEPTURY_REF_RECEPTURA_ID#'.
        /// </summary>
        internal static string NOMRECEPTURY_REF_RECEPTURA_ID {
            get {
                return ResourceManager.GetString(ResourceNames.NOMRECEPTURY_REF_RECEPTURA_ID, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to '#NOMRECEPTURYVARIANTY#'.
        /// </summary>
        internal static string NOMRECEPTURYVARIANTY {
            get {
                return ResourceManager.GetString(ResourceNames.NOMRECEPTURYVARIANTY, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to '#ORCENAMJSUROVINY#'.
        /// </summary>
        internal static string ORCENAMJSUROVINY {
            get {
                return ResourceManager.GetString(ResourceNames.ORCENAMJSUROVINY, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to '#POPIS#'.
        /// </summary>
        internal static string POPIS {
            get {
                return ResourceManager.GetString(ResourceNames.POPIS, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to '#RECEPTURAID#'.
        /// </summary>
        internal static string RECEPTURAID {
            get {
                return ResourceManager.GetString(ResourceNames.RECEPTURAID, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to '#SKLADID#'.
        /// </summary>
        internal static string SKLADID {
            get {
                return ResourceManager.GetString(ResourceNames.SKLADID, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to '#SLOZKAID#'.
        /// </summary>
        internal static string SLOZKAID {
            get {
                return ResourceManager.GetString(ResourceNames.SLOZKAID, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to '#VARIANTAID#'.
        /// </summary>
        internal static string VARIANTAID {
            get {
                return ResourceManager.GetString(ResourceNames.VARIANTAID, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Lists all the resource names as constant string fields.
        /// </summary>
        internal class ResourceNames {
            
            /// <summary>
            /// Stores the resource name 'KODZBOZI'.
            /// </summary>
            internal const string KODZBOZI = "KODZBOZI";
            
            /// <summary>
            /// Stores the resource name 'MJ'.
            /// </summary>
            internal const string MJ = "MJ";
            
            /// <summary>
            /// Stores the resource name 'MNOZSTVICISTE'.
            /// </summary>
            internal const string MNOZSTVICISTE = "MNOZSTVICISTE";
            
            /// <summary>
            /// Stores the resource name 'MNOZSTVIHRUBE'.
            /// </summary>
            internal const string MNOZSTVIHRUBE = "MNOZSTVIHRUBE";
            
            /// <summary>
            /// Stores the resource name 'NOMRECEPTURY_RECEPTURA_ID'.
            /// </summary>
            internal const string NOMRECEPTURY_RECEPTURA_ID = "NOMRECEPTURY_RECEPTURA_ID";
            
            /// <summary>
            /// Stores the resource name 'NOMRECEPTURY_REF_RECEPTURA_ID'.
            /// </summary>
            internal const string NOMRECEPTURY_REF_RECEPTURA_ID = "NOMRECEPTURY_REF_RECEPTURA_ID";
            
            /// <summary>
            /// Stores the resource name 'NOMRECEPTURYVARIANTY'.
            /// </summary>
            internal const string NOMRECEPTURYVARIANTY = "NOMRECEPTURYVARIANTY";
            
            /// <summary>
            /// Stores the resource name 'ORCENAMJSUROVINY'.
            /// </summary>
            internal const string ORCENAMJSUROVINY = "ORCENAMJSUROVINY";
            
            /// <summary>
            /// Stores the resource name 'POPIS'.
            /// </summary>
            internal const string POPIS = "POPIS";
            
            /// <summary>
            /// Stores the resource name 'RECEPTURAID'.
            /// </summary>
            internal const string RECEPTURAID = "RECEPTURAID";
            
            /// <summary>
            /// Stores the resource name 'SKLADID'.
            /// </summary>
            internal const string SKLADID = "SKLADID";
            
            /// <summary>
            /// Stores the resource name 'SLOZKAID'.
            /// </summary>
            internal const string SLOZKAID = "SLOZKAID";
            
            /// <summary>
            /// Stores the resource name 'VARIANTAID'.
            /// </summary>
            internal const string VARIANTAID = "VARIANTAID";
        }
    }
}
