using System;
using System.Collections.Generic;

namespace Anete.Utils.Patterns.AppActions
{    
    /// <summary>
    /// Factory je vyuzita pouze u launcheru.
    /// AppAction je pradana jako instance.
    /// </summary>
    /// <typeparam name="TCompositeAppAction"></typeparam>
    public class AppActionInstanceWithLauncherFactory<TCompositeAppAction> : AppActionWithLauncherFactory where TCompositeAppAction : ICompositeAppAction, new()
    {                
        private readonly IAppAction _childAppAction;
        private readonly IEnumerable<IAppActionLauncherFactory> _childAppActionLauncherFactory;
        
        /// <summary>
        /// Initializes a new instance of the AvailableAppActionForLaunchFromAppActionInstance class.
        /// </summary>        
        public AppActionInstanceWithLauncherFactory(IAppActionInvoker appActionInvoker, IAppAction childAppAction, IEnumerable<IAppActionLauncherFactory> childAppActionLauncherFactory)
            : base(appActionInvoker)
        {
            _childAppActionLauncherFactory = childAppActionLauncherFactory;
            _childAppAction = childAppAction;            
        }

        /// <summary>
        /// Metoda pro vytvoreni instance IAppAction
        /// </summary>
        /// <returns></returns>
        protected override IAppAction CreateAppActionInstanceInt(object state)
        {
            return _childAppAction;
        }

        /// <summary>
        /// Metoda pro zjisteni typu IAppAction
        /// </summary>
        /// <returns></returns>
        protected override Type GetAppActionType()
        {
            return _childAppAction.GetType();
        }

        /// <summary>
        /// Vraci id akce
        /// </summary>
        /// <returns></returns>
        protected override string GetAppActionId()
        {
            return _childAppAction.Id;
        }

        /// <summary>
        /// Vraci seznam launcher factories
        /// </summary>
        /// <returns></returns>
        protected override IEnumerable<IAppActionLauncherFactory> GetLauncherFactories()
        {
            return _childAppActionLauncherFactory;
        }
    }
}
