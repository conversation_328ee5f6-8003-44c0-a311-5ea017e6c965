using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Anete.Common.Data.Interface.Business.Paragon;
using NHibernate;
using System.Data.SqlClient;
using System.Data;
using Anete.Utils;
using Anete.Common.Data.Nh.Interface.Services;
using Anete.Common.Data.Nh.Entities;
using Anete.Utils.ComponentModel;
using Anete.Common.Data.Interface.AppServices;
using Anete.Common.Data.Interface.AppServices.VatServices;
using Anete.Utils.Extensions;
using Anete.Common.Data.Nh.Services.VatServices;
using Anete.Config.Core;
using Anete.Config.Configs.Core.Global.Behaviour;
using Anete.Common.Data.Interface.Enums;

namespace Anete.Common.Data.Nh.Services
{
	/// <summary>
	/// Poskytovatel sazeb Dph pro NHibernate
	/// </summary>
	public class NhVatRateProvider : IVatRateProvider, ICheckIsInitialized
	{
		private readonly IKreditSessionFactoryProvider _kreditSessionFactory;
		private IList<VatGroupFull> _groups;
		private IList<VatRate> _vatRates;
		private readonly IConfigManager _configManager;

		/// <summary>
		/// Initializes a new instance of the VatRateProvider class.
		/// </summary>
		public NhVatRateProvider(IKreditSessionFactoryProvider kreditSessionFactory, IConfigManager configManager)
		{
			_configManager = configManager;
			_kreditSessionFactory = kreditSessionFactory;
		}

		/// <summary>
		/// Inicialize. Vede k nacteni dat.
		/// </summary>
		public void Initialize()
		{
			if (IsInitialized)
			{
				throw ExcUtils.CanBeCalledOnlyOneTime();
			}

			using (ISession session = _kreditSessionFactory.SessionFactory.OpenSession())
			using (ITransaction tx = session.BeginTransaction())
			{
				_groups = session.QueryOver<VatGroupFull>().List();
				_vatRates = session.QueryOver<VatRate>().List();
				tx.Commit();
			}

			IsInitialized = true;
		}

		#region IVatRateProvider Members
		public decimal GetVatRate(byte vatRateId, DateTime date)
		{
			return GetVatRateInfo(vatRateId, date).VatPercentage.Value;
		}

		public string GetVatRateText(byte vatRateId, DateTime date)
		{
			return GetVatRateInfo(vatRateId, date).Text;
		}

		public IEnumerable<VatRateInfo> GetValidVatRatesInfo(DateTime date, bool addBuildInVatRates = false)
		{
			CheckIsInitialized();

			List<VatRateInfo> vatRateList = new List<VatRateInfo>();

			foreach (IGrouping<byte, VatGroupFull> vatRateGroup in _groups.GroupBy(g => g.VatRateId))
			{
				foreach (VatGroupFull vatRate in vatRateGroup)
				{
					Interval<DateTime> interval = new Interval<DateTime>(vatRate.ValidFrom, vatRate.ValidTo ?? DateTime.MaxValue, true, vatRate.ValidTo.HasValue) { IncludeEnd = true };
					if (interval.IsInRange(date.Date))
					{
						VatRateInfo vatRateInfo = new VatRateInfo(
							vatRate.VatRateId,
							vatRate.VatPercentage.Value,
							"",
							vatRate.Text,
							vatRate.TextExtended
						);
						vatRateList.Add(vatRateInfo);
					}
				}
			}

			// https://helpdesk.anete.com/issues/69359
			// Touto silenosti resim zobrazeni sazby DPH v novem roce, ktera jiz neni platna - je to prechodne pri zmene legislativy a odstraneni druhe snizene sazby DPH
			if (date.Year == 2024 && date.Month == 1 && _configManager.GetConfig<GlobalBehaviourApplicationLanguageConfig>().ApplicationLanguage == Core.Interface.Enums.ApplicationLanguage.Cz)
			{
				if(!vatRateList.Any(v=>v.IdDph == Data.Interface.Enums.IdDph.Specialni))
				{
					var existingSnizena = vatRateList.SingleOrDefault(v => v.IdDph == IdDph.Snizena);
					// vzdy by melo byt nastaveno, nicmene pro jistotu otestuji, kdyby mel zakaznik nejak nesstastne nastaveny sazby DPH
					if(existingSnizena != null)
					{
						VatRateInfo buildIn = new VatRateInfo((byte)IdDph.Specialni, existingSnizena.Percentage, existingSnizena.Name, existingSnizena.Text, existingSnizena.TextExtended)
						{
							BuildIn = true
						};
						vatRateList.Add(buildIn);
					}					
				}
			}

			return vatRateList.OrderBy(vr => vr.Percentage).ToArray();
		}

		public bool HasIntervalConstantVatRates(DateTime from, DateTime to, out string errorMessage)
		{
			var vatGroupsToValidate = _groups.Where(g =>
				// platnost DPH od zasahuje do zvoleneho intervalu
				g.ValidFrom >= from && g.ValidFrom <= to ||
				// platnost DPH je soucasti intervalu
				g.ValidFrom <= from && (g.ValidTo ?? DateTime.MaxValue) >= to ||
				// platnost DPH do zasahuje do zvoleneho intervalu
				(g.ValidTo ?? DateTime.MaxValue) >= from && (g.ValidTo ?? DateTime.MaxValue) <= to)
				.ToArray();

			var duplicatedVatRates = vatGroupsToValidate
				.GroupBy(v => v.VatRateId)
				.Where(v => v.Count() > 1)
				.ToArray();

			if(duplicatedVatRates.Any())
			{
				errorMessage = NhVatRateProviderSR.DuplicatedVatRatesFormat(from, to, duplicatedVatRates.Select(v => _vatRates.Single(vr => vr.VatRateId == v.Key).Name).ToCommaSpaceDelimitedString());
				return false;
			}
			else
			{
				errorMessage = null;
				return true;
			}
		}
		#endregion

		#region ICheckIsInitialized Members

		public void CheckIsInitialized()
		{
			if (!IsInitialized)
			{
				throw ExcUtils.ClassNotInitialized(this);
			}
		}
		#endregion

		#region IIsInitialized Members
		public bool IsInitialized { get; private set; }
		#endregion

		#region private methods...
		private VatGroupFull GetVatRateInfo(byte vatRateId, DateTime date)
		{
			CheckIsInitialized();

			foreach (VatGroupFull vatGroup in _groups.Where(g => g.VatRateId == vatRateId))
			{
				Interval<DateTime> interval = new Interval<DateTime>(vatGroup.ValidFrom, vatGroup.ValidTo ?? DateTime.MaxValue, true, vatGroup.ValidTo.HasValue) { IncludeEnd = true };
				if (interval.IsInRange(date.Date))
				{
					return vatGroup;
				}
			}

			throw new ArgumentException(string.Format("Pro sazbu DPH id = {0} a datum {1} neni definovana procentualni vyse DPH", vatRateId, date));
		}
		#endregion
	}
}
