using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml.Linq;

namespace Anete.Common.Data.Interface.Business.Eet
{

	/// <summary>
	/// Log komunikace Eet 
	/// </summary>
	public class EetComLog
	{

		public EetComLog(IEnumerable<EetComLogItem> items)
		{
			Items = items.ToArray();
		}

		public IEnumerable<EetComLogItem> Items { get; private set; }

		public static EetComLog Parse(string xmlData)
		{
			IEnumerable<EetComLogItem> items = 
				XElement.Parse($"<root>{xmlData}</root>")
				.Elements()
				.Select(e => new EetComLogItem(e));
			return new EetComLog(items);
		}

	}
}
