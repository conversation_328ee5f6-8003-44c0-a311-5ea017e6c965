using Anete.Config.Core.Internals;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ComponentModel;
using Anete.Config.Core.Attributes;
using Anete.Config.Core.Categories;
using Anete.Common.Core.Interface.Enums;
using Anete.Config.Configs.Core.Shared;

namespace Anete.Config.Configs.Core.MenuPresenter.Hw
{


	/// <summary>
	/// Nastaveni jidelnicku
	/// </summary>
	[MenuPresenterHwConfigDBKey("IpCam")]
	[ConfigVersion("*******")]
	[ConfigClassSRDescription(typeof(MenuPresenterHwIpCamConfig))]
	[DefaultConfigInstance(true)]
	[ApplicationCategory(ApplicationType.MenuPresenter)]
	public class MenuPresenterHwIpCamConfig : ConfigBase
	{
        private const bool _enabledDefValue = false;
        private bool _enabled = _enabledDefValue;
        /// <summary>
        /// Povoleni zobrazeni dat z Web kamery
        /// </summary>
        [DefaultValue(_enabledDefValue)]
        [ConfigSRDisplayName("Enabled", typeof(MenuPresenterHwIpCamConfig))]
        [ConfigSRDescription("Enabled", typeof(MenuPresenterHwIpCamConfig))]       
        public bool Enabled
        {
            get { return _enabled; }
            set
            {
                if (_enabled == value)
                {
                    return;
                }
                _enabled = value;
                OnPropertyChanged();
            }
        }

        private string _rtspStreamUrl;
        /// <summary>
        /// RTSC url adresa streamu. Priklad pro JCU: rtsp://kam-cam10.cam.jcu.cz/axis-media/media.amp
        /// </summary>        
        [ConfigSRDisplayName("RtspStreamUrl", typeof(MenuPresenterHwIpCamConfig))]
        [ConfigSRDescription("RtspStreamUrl", typeof(MenuPresenterHwIpCamConfig))]
        public string RtspStreamUrl
        {
            get { return _rtspStreamUrl; }
            set
            {
                if (_rtspStreamUrl == value)
                {
                    return;
                }
                _rtspStreamUrl = value;
                OnPropertyChanged();
            }
        }

        private const int _imageHeightDefValue = 200;
        private int _imageHeight = _imageHeightDefValue;
        /// <summary>
        /// Vyska obrazku z webkamery. Sirka se prizpusobi pomeru stran IP kamery.
        /// </summary>
        [DefaultValue(_imageHeightDefValue)]
        [ConfigSRDisplayName("ImageHeight", typeof(MenuPresenterHwIpCamConfig))]
        [ConfigSRDescription("ImageHeight", typeof(MenuPresenterHwIpCamConfig))]
        public int ImageHeight
        {
            get { return _imageHeight; }
            set
            {
                if (_imageHeight == value)
                {
                    return;
                }
                _imageHeight = value;
                OnPropertyChanged();
            }
        }

        private const MenuPresenterCamPosition _menuPresenterCamPositionDefValue = MenuPresenterCamPosition.Left;
        private MenuPresenterCamPosition _menuPresenterCamPosition = _menuPresenterCamPositionDefValue;
        /// <summary>
        /// Pozice kamery na obrazovce
        /// </summary>
        [DefaultValue(_menuPresenterCamPositionDefValue)]
        [ConfigSRDisplayName("MenuPresenterCamPosition", typeof(MenuPresenterHwIpCamConfig))]
        [ConfigSRDescription("MenuPresenterCamPosition", typeof(MenuPresenterHwIpCamConfig))]
        public MenuPresenterCamPosition MenuPresenterCamPosition
        {
            get { return _menuPresenterCamPosition; }
            set
            {
                if (_menuPresenterCamPosition == value)
                {
                    return;
                }
                _menuPresenterCamPosition = value;
                OnPropertyChanged();
            }
        }

        private const int _frameDelayDefValue = 200;
        private int _frameDelay = _frameDelayDefValue;
        /// <summary>
        /// Zpozdeni [ms] mezi zobrazenim jednotlivych snimku z kamery. Vhodne nastaveni muze omezit sitovy provoz.
        /// </summary>
        [DefaultValue(_frameDelayDefValue)]
        [ConfigSRDisplayName("FrameDelay", typeof(MenuPresenterHwIpCamConfig))]
        [ConfigSRDescription("FrameDelay", typeof(MenuPresenterHwIpCamConfig))]
        public int FrameDelay
        {
            get { return _frameDelay; }
            set
            {
                if (_frameDelay == value)
                {
                    return;
                }
                _frameDelay = value;
                OnPropertyChanged();
            }
        }
    }
}
