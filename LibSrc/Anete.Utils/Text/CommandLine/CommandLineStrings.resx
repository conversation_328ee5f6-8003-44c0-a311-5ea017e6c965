<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="EndOfInput" xml:space="preserve">
    <value>Konec vstupu.</value>
  </data>
  <data name="UnexpectedEndOfString" xml:space="preserve">
    <value>Neočekávaný konec řetězce.</value>
  </data>
  <data name="MissingClosingQuote" xml:space="preserve">
    <value>Chybějící uzavírací apostrof.</value>
  </data>
  <data name="MissingClosingQuoteForValue0" xml:space="preserve">
    <value>Missing closing quote for quote command line value: {0}</value>
  </data>
  <data name="DefaultCommandLineExceptionErrorMessage" xml:space="preserve">
    <value>Chyba v příkazové řádce.</value>
  </data>
  <data name="DefaultMissingOptionNameExceptionMessage" xml:space="preserve">
    <value>Pro parametr příkazové řádky chybí název parametru.</value>
  </data>
  <data name="Option0MustNotBeSpecifiedMultipleTimes" xml:space="preserve">
    <value>Parametr "{0}" nesmí být zadán vícekrát.</value>
  </data>
  <data name="Option0MustNotBeSpecifiedMoreThan1Times" xml:space="preserve">
    <value>Parametr "{0}" nesmí být zadán více než {1} krát.</value>
  </data>
  <data name="ValueFor03OutOfRangeExpectedNumericBetween1And2" xml:space="preserve">
    <value>Hodnota zadaná pro "{0}" ({3}) je mimo povolený rozsah. Je očekávána numerická hodnota v rozsahu od {1} do {2}.</value>
  </data>
  <data name="InvalidValue0ForOption1" xml:space="preserve">
    <value>Chybná hodnota ({0}) pro parametr "{1}".</value>
  </data>
  <data name="InternalErrorUnimplementedBoolFunction0UsedInOption1" xml:space="preserve">
    <value>Internal error; unimplemented BoolFunction {0} used in option "{1}"</value>
  </data>
  <data name="MissingRequiredValueForOption0" xml:space="preserve">
    <value>Pro parametr "{0}" nebyla zadána hodnota.</value>
  </data>
  <data name="Option0DoesNotAcceptAValue" xml:space="preserve">
    <value>Parametr "{0}" neumožnuje zadat hodnotu.</value>
  </data>
  <data name="Option0MayNotBeSpecifiedTogetherWithOption1" xml:space="preserve">
    <value>Parametr "{0}" nesmí být zadán zároveň s parametrem "{1}".</value>
  </data>
  <data name="AssignmentCharactersMustNotBeChangedWhileParseInProgress" xml:space="preserve">
    <value>AssigmentCharacters nesmí být měněny v průběhu práce parseru.</value>
  </data>
  <data name="UndefinedOption0ReferencedFromProhibitionSectionOfOption1" xml:space="preserve">
    <value>Undefined option "{0}" referenced from prohibition section of option "{1}"</value>
  </data>
  <data name="InternalErrorOptionSpecifiedInProhibitionDoesNotExist" xml:space="preserve">
    <value>Internal error; option specified in prohibition does not exist</value>
  </data>
  <data name="Alias0IsInvalidForOption1BoolFunctionIsSetTo2AndThe3OptionStyleIsEnabledWhichProhibitsAnyNameLongerThanOneCharacter" xml:space="preserve">
    <value>Alias "{0}" není platný pro volbu "{1}"; BoolFunction je nastavena na {2} je aktivní styl nastavení {3} který zakazuje jakékoliv parametry delší než jeden znak.</value>
  </data>
  <data name="AliasName0IsAlreadyInUseByAnotherOption" xml:space="preserve">
    <value>Alias "{0}" je už použit jiným parametrem.</value>
  </data>
  <data name="BoolFunctionCanNotBeSetTo0ForOptionWithANameLongerThanOneCharacterWhenOptionStyle1IsSpecifiedInThe2Attribute" xml:space="preserve">
    <value>BoolFunction can not be set to '{0}' for option with a name longer than one character when option style '{1}' is specified in the {2} attribute</value>
  </data>
  <data name="BoolFunctionMayNotBeSetTo0WhenOptionStyle1IsSpecifiedInThe2Attribute" xml:space="preserve">
    <value>BoolFunction may not be set to '{0}' when option style '{1}' is specified in the {2} attribute</value>
  </data>
  <data name="BoolFunctionMayNotBeSetTo0WhenOptionStyle1IsNotSpecifiedInThe2Attribute" xml:space="preserve">
    <value>BoolFunction may not be set to '{0}' when option style '{1}' is not specified in the {2} attribute</value>
  </data>
  <data name="RedefinitionOfCommandLineOptionWithParameterName01" xml:space="preserve">
    <value>Opakovaná definice parametru s názvem "{0}" ({1})</value>
  </data>
  <data name="UsingCaseSensitiveNames" xml:space="preserve">
    <value>používám jména rozlišující velikost písma</value>
  </data>
  <data name="UsingCaseInsensitiveNames" xml:space="preserve">
    <value>používám jména nerozlišující velikost písma</value>
  </data>
  <data name="RedefinitionOfGroupWithId01" xml:space="preserve">
    <value>Opakovaná definice skupiny s id "{0}" ({1}).</value>
  </data>
  <data name="InvalidIdOfGroupIdMustNotBeNullOrEmpty" xml:space="preserve">
    <value>Chybné Id skupiny, Id musí být zadáno.</value>
  </data>
  <data name="MissingRequiredAttributeForACommandLineManagerObjectCommandLineManagerAttribute" xml:space="preserve">
    <value>Missing required attribute for a command line manager object: CommandLineManagerAttribute</value>
  </data>
  <data name="MissingClosingQuoteForQuotedValue" xml:space="preserve">
    <value>Chybějící uzavírací apostrof pro hodnotu v apostrofech.</value>
  </data>
  <data name="EmptyOptionNameIsNotAllowed" xml:space="preserve">
    <value>Název parametru musí být zadán.</value>
  </data>
  <data name="FileNotFound0" xml:space="preserve">
    <value>Soubor "{0}" nebyl nalezen.</value>
  </data>
  <data name="TheSpecifiedPath0IsInvalid" xml:space="preserve">
    <value>Cesta ("{0}") je chybná.</value>
  </data>
  <data name="AccessToTheSpecifiedFile0IsDenied" xml:space="preserve">
    <value>Přístup k souboru "{0}" byl odepřen.</value>
  </data>
  <data name="TheSpecifiedPath0IsTooLong" xml:space="preserve">
    <value>Cesta ("{0}") je příliš dlouhá.</value>
  </data>
  <data name="UnknownOption0" xml:space="preserve">
    <value>Neznámý parametr "{0}".</value>
  </data>
  <data name="Unexpected0CharacterOnCommandLine" xml:space="preserve">
    <value>Neznámý znak '{0}' na příkazové řádce.</value>
  </data>
  <data name="InternalErrorUnimplementedTokenTypeReturnedToCommandLineParser" xml:space="preserve">
    <value>Internal error; Unimplemented token type returned to CommandLineParser</value>
  </data>
  <data name="MissingRequiredOption0" xml:space="preserve">
    <value>Vyžadovaný parametr "{0}" nebyl zadán.</value>
  </data>
  <data name="Only0OccurenceSOfOption1FoundItMustBeSpecifiedExactly2TimeS" xml:space="preserve">
    <value>Only {0} occurence(s) of option "{1}" found; it must be specified exactly {2} time(s)</value>
  </data>
  <data name="Only0OccurenceSOfOption1FoundItMustBeSpecifiedAtLeast2TimeS" xml:space="preserve">
    <value>Only {0} occurence(s) of option "{1}" found; it must be specified at least {2} time(s)</value>
  </data>
  <data name="AtMostOneOfTheOptions0MayBeSpecifiedAtOnce" xml:space="preserve">
    <value>Parametr {0} může být zadán maximálně jednou.</value>
  </data>
  <data name="AtLeastOneOfTheOption0MustBeSpecified" xml:space="preserve">
    <value>Musí být zadán nejméně jeden parametr {0}.</value>
  </data>
  <data name="OneOfTheOptions0MustBeSpecified" xml:space="preserve">
    <value>Musí být zadán jeden z parametrů {0}.</value>
  </data>
  <data name="OnlyOneOfTheOptions0MayBeSpecified" xml:space="preserve">
    <value>Musí být zadán pouze jeden z parametrů {0}.</value>
  </data>
  <data name="AllOfTheOptions0MustBeSpecified" xml:space="preserve">
    <value>Musí být zadány všechny parametry {0}.</value>
  </data>
  <data name="InternalErrorNonImplementedGroupRequirementSpecified" xml:space="preserve">
    <value>Internal error; non-implemented group requirement specified</value>
  </data>
  <data name="ValueWasEitherTooLargeOrTooSmallForThisOptionType" xml:space="preserve">
    <value>Hodnota je buď příliš veliká nebo příliš malá pro daný typ parametru.</value>
  </data>
  <data name="Options" xml:space="preserve">
    <value>Parametry:</value>
  </data>
  <data name="Version" xml:space="preserve">
    <value>verze</value>
  </data>
  <data name="LastItemOfExclusiveList" xml:space="preserve">
    <value>nebo "{0}"</value>
  </data>
  <data name="InvalidValue0ForOption1TheValueMustBeOneOf2" xml:space="preserve">
    <value>Chybná hodnota "{0}" pro parametr "{1}"; Hodnota musí být jedna z {2}</value>
  </data>
  <data name="InvalidEnumerationValue0" xml:space="preserve">
    <value>Chybný prvek enumerace "{0}".</value>
  </data>
  <data name="InternalErrorOptionValueWasSetMoreThanMaxOccursNumberOfTimes" xml:space="preserve">
    <value>Internal error; Option.Value was set more than MaxOccurs number of times</value>
  </data>
  <data name="ArgMustBeGreaterThanZero" xml:space="preserve">
    <value>{0} musí být větší než nula.</value>
  </data>
  <data name="ArgMustBeNonNegative" xml:space="preserve">
    <value>{0} nesmí být záporné číslo.</value>
  </data>
  <data name="InternalErrorUnimplementedVerticalAlignmentUsed" xml:space="preserve">
    <value>Internal error; Unimplemented VerticalAlignment used</value>
  </data>
  <data name="WidthMustNotBeLessThan0" xml:space="preserve">
    <value>šířka nesmí být menší než {0}</value>
  </data>
  <data name="Arg0MustNotBeLessThan1" xml:space="preserve">
    <value>{0} nesmí být menší než {1}.</value>
  </data>
  <data name="OnLine0InFile1" xml:space="preserve">
    <value> (rádek {0} v {1})</value>
  </data>
  <data name="ErrorList" xml:space="preserve">
    <value>Seznam chyb:</value>
  </data>
</root>