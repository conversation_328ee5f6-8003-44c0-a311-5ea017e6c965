using System;
using System.Collections.Generic;
using System.Linq;

namespace Anete.Common.Core.Interface.AppServices.SupportMessage.MessageQueue
{
    /// <summary>
    /// Interface pro transport hlaseni se stanice dale
    /// </summary>
    public interface IMessageTransport<TMessage>
    {
        /// <summary>
        /// Odeslani hlaseni
		/// Pozor: Zde neni neodchytavat vyjimky uvedene v GetExpectedExceptionTypes. Odchytavani vyjimek zajistuje volajici
        /// </summary>
        /// <param name="message">Odesilane hlaseni</param>
        void Send(TMessage message);

        /// <summary>
        /// Jedna se o beznou provozni vyjimku?
        /// </summary>
        /// <param name="ex">Vyjimka</param>
        /// <param name="nextTryDelay">Po jake prodleve v mSec opakovat prenos</param>
        /// <returns>
        /// Vraci true, pokud jde o vyjimku, ktera je povazovana za beznou provozni. Takove vyjimky se jenom zaloguji a
        /// transport se opakuje. Pokud vraci false, zaloguje se neocekavana vyjimka a restartuje se odesilaci thread.
        /// </returns>
        bool IsExpectedException(Exception ex, out TimeSpan nextTryDelay);

    }
}
