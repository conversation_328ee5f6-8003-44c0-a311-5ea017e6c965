using System;
using System.Collections.Generic;
using System.Data.SqlClient;

namespace Anete.Common.Data.Interface.AppServices
{
	/// <summary>
	/// Poskytovatel pripojeni pro databazi sklady.
	/// </summary>
	public interface ISkladyDbConnectionProvider : ICustomSqlConnectionProvider
	{

		SqlConnectionLinkedServerConnection CreateSqlConnectionLinked();

		/// <summary>
		/// Pouziva se pro stavy, kdy otevreni dalsiho spojeni by zpusobilo eskalaci TransactionScope do MS DTC. To vyzaduje 
		/// ale povoleny MS DTC na stanici. Proto se pro SqlConnectionLinkedServerConnection pouzije stavajici spojeni conn
		/// </summary>
		/// <param name="conn"></param>
		/// <returns></returns>
		SqlConnectionLinkedServerConnection UseSqlConnectionLinked(SqlConnection conn);

		/// <summary>
		/// Vraci true, pokud se jedna o linkovany server
		/// </summary>
		bool IsLinkedServer { get; }

	}
}
