using System;
using System.Linq;
using System.Runtime.Serialization;

namespace Anete.Common.Core.Business.InstallationParameters.Parameters.SystemKreditVersion
{
    [DataContract(Namespace = Anete.Resources.AneteNamespace.DefaultNamespace)]
    public class SystemKreditVersionParameterValueDto
    {
        public SystemKreditVersionParameterValueDto(long version)
        {
            Version = version;         
        }

        [DataMember(Order = 0)]
        public long Version { get; private set; }
    }
}
