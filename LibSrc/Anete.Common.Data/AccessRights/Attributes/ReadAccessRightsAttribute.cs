using System;
using System.Diagnostics;
using Anete.Utils;
using Anete.Common.Core.Interface.Enums;
using Anete.Common.Data.AccessRights.Enums;

namespace Anete.Common.Data.AccessRights.Attributes
{
    /// <summary>
    /// Atribut pro definici standardnich pristupovych prav pouze pro cteni
    /// Takovy objekt standardne definuje prava uvedena v ReadonlyAccessRight
    /// </summary>
    public class ReadAccessRightsAttribute : AccessRightsAttribute
    {

        #region constructors...
        /// <summary>
        /// Inicializace tridy
        /// </summary>
        /// <param name="accessRightCodeGuid">The access right code GUID.</param>
        /// <param name="accessRightCode">Kod pristupoveho prava pro danou tridu.</param>
        /// <param name="applicationType">Primarni aplikace, pro ktere pravo plati</param>
        /// <param name="otherApplicationTypes"><PERSON><PERSON><PERSON> dalsich typu aplikaci, pro ktere pravo plati</param>
        public ReadAccessRightsAttribute(string accessRightCodeGuid, string accessRightCode, ApplicationType applicationType, params ApplicationType[] otherApplicationTypes)
            : base(accessRightCodeGuid, accessRightCode, typeof(ReadAccessRight), applicationType, otherApplicationTypes)
        {

        }
        #endregion

    }
}
