using Anete.AutoUpgrades.Interface.AppServices;
using Anete.Common.Core.Interface.AppServices.SupportMessage.CommandProcessor;
using Anete.Common.Data.AppServices.SupportMessage.CommandProcessor;
using Anete.Common.Data.Interface.AppServices;
using Anete.Common.Wcf;
using Anete.Config.Configs.Core.Global.Behaviour;
using Anete.Config.Core;
using Anete.Log4Net.Core;
using Anete.Utils;
using Anete.Utils.AppServices;
using Anete.Utils.Extensions;
using Anete.Utils.Win32.Net.NamesPipes;
using log4net.Core;
using System;
using System.Diagnostics;
using System.ServiceModel;
using Unity;

namespace Anete.Infrastructure.Core.AppServices.TechSupportMessage
{
	/// <summary>
	/// Service registrator pro TechSupportMessage
	/// </summary>
	public class ServiceRegistrator : ServiceRegistratorBase
	{

		#region private static fields...
		private static readonly ILogEx _log = LogManagerEx.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
		#endregion

		#region constructors...
		/// <summary>
		/// Vytvori novou instanci
		/// </summary>
		/// <remarks>Proc se predava ConfigManager jako resolver? Protoze potrebuji volat RegisterMocks jeste predtim, nez je zaregistrovano
		/// cokoliv jineho a v tu chvili jeste neni IConfigManager registrovan</remarks>
		public ServiceRegistrator(IUnityContainer container, Func<IConfigManager> configManagerResolver, Func<IKreditDbConnectionProvider> kreditDbConnectionProviderResolver, Func<IAneteUpdatesDbConnectionProviderConfig> aneteUpdatesDbConnectionProviderConfigResolver)
			: base(container, configManagerResolver, kreditDbConnectionProviderResolver, aneteUpdatesDbConnectionProviderConfigResolver)
		{
			
		}
		#endregion

		#region protected overrides...
		/// <summary>
		/// Vraci true, pokud je command processor aktivni
		/// </summary>
		protected override bool GetIsCommandProcessorEnabled(GlobalBehaviourTechSupportMessagingConfig config)
		{
			return config.CommandProcessorSettings.Enabled;
		}

		/// <summary>
		/// Vraci typ provideru, ktere se maji zaregistrovat
		/// </summary>
		[DebuggerNonUserCode]
		protected override ProvidersType GetProvidersToRegister(GlobalBehaviourTechSupportMessagingConfig config)
		{
			if (!config.Enabled)
			{
				return ProvidersType.Mock;
			}

			switch (config.TransportType)
			{
				case TransportType.Autodetect:
					return IsAutoUpdaterServiceInstalled() ? ProvidersType.AutoUpdater : ProvidersType.Application;
				case TransportType.AutoUpdater:
					if (!IsAutoUpdaterServiceInstalled())
					{
						// Kak: 1.12.2011 Tady jsem se dostal do stavu, kdy je zvoleno zasilani pres AutoUpdater, ale ten neni
						// instalovan. Pak program zhavaruje hned pri startu a nevytvori se offline kopie konfigurace. Pri pristim
						// startu ctu zase stejnou konfiguraci a stejny problem. Zatim neresim.
						// Lze docasne vyresit smazanim Offline konfigurace
						throw new InvalidOperationException(ServiceRegistratorSR.AutoUpdaterNotDetectedFormat(
							config.TransportType.ToLocalizedString()));
					}
					return ProvidersType.AutoUpdater;
				case TransportType.Application:
					return ProvidersType.Application;
				default:
					throw ExcUtils.ArgumentOutOfRange("config.TransportType", config.TransportType);
			}
		}

		/// <summary>
		/// Registrace CommandSource pro CommandProcessor
		/// </summary>
		protected override void RegisterCommandSource()
		{
			Container.RegisterType<ICommandSource<ITechSupportCommandSourceItem>, AppFrontaPrikazuCommandSource>(
				LifetimeManagers[typeof(ICommandSource<ITechSupportCommandSourceItem>)]);
		}
		#endregion

		#region private methods...
		/// <summary>
		/// Zavola Wfc sluzbu AutoUpdateru a zkontroluje, zda je instalovana
		/// </summary>
		/// <returns></returns>
		[DebuggerNonUserCode]
		private bool IsAutoUpdaterServiceInstalled()
		{
			_log.Debug("IsAutoUpdaterServiceInstalled");			
			try
			{
#if !NETFRAMEWORK
				// pouzivam vlastni pipe protokol, wcf jiz neni podporovano
				NamedPipeProxyFactory namedPipeProxyFactory = DependencyContainer.Instance.Resolve<NamedPipeProxyFactory>();
				var proxy = namedPipeProxyFactory.CreateProxy<ITechSupportMessageService>(AutoUpgradesServiceConfig.TechSupportMessageServiceNetCorePipeName);
				return proxy.IsInstalled();
#else
				string adress = AutoUpgradesServiceConfig.GetTechSupportMessageServiceAdress();
				bool isInstalled = false;

				// Tady se debugger stejne zastavuje, i kdyz pouzivam DebuggerNonUserCode jako v TypeCreator
				// je to tim, ze atribut neni nad lambda vyrazem - nejde tam ani umistit
				// http://connect.microsoft.com/VisualStudio/feedback/details/336367/debuggerstepthroughattribute-is-ignored-when-stepping-into-lambda-expressions-from-a-different-method
				// Pokud te to stve, staci prehodit nastaveni v Global.Behaviour.TechSupportMessaging na Typ transportu: z aplikace.
				// Bude to fungovat az po 2. spusteni, protoze tady se vola s offline daty ConfigManagera

				WcfProxyCaller.Call<ITechSupportMessageService>(null, adress, new NetNamedPipeBinding(), Level.Trace,
								proxy =>
								{
									isInstalled = proxy.IsInstalled();
								});

				_log.InfoFormat("AutoUpdater is installed: Result = {0}", isInstalled);
				return isInstalled;
#endif
			}
			catch (Exception ex)
			{
				if (!ExcUtils.IsCatchableExceptionType(ex))
				{
					throw;
				}

				_log.InfoFormat("AutoUpdater is installed: Result = {0}", false);
				return false;
			}
		}
		#endregion
	}
}