//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Anete.Common.Data.Nh.Entities
{

    /// <summary>
    /// Tabulka: dbo.JASU_Strediska
    /// </summary>
    public partial class JasuResort : Anete.Data.Nh.NhEntityBase, INotifyPropertyChanging, INotifyPropertyChanged {

        private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(System.String.Empty);

        private short _MealKindId;

        private short _CanteenId;

        private string _Resort;

        private byte _Percentage;

        private byte _Rounding;

        private Canteen _Canteen;
    
        #region Extensibility Method Definitions
        
        partial void OnCreated();

        public override bool Equals(object obj)
        {
          JasuResort toCompare = obj as JasuResort;
          if (toCompare == null)
          {
            return false;
          }

          if (!Object.Equals(this.MealKindId, toCompare.MealKindId))
            return false;
          if (!Object.Equals(this.CanteenId, toCompare.CanteenId))
            return false;
          if (!Object.Equals(this.Resort, toCompare.Resort))
            return false;
         // pokud mam default primary key, znamena to, ze jedna o nove vytvorenou entitu, ktera jeste nema key prirazen. 
        // V takove pripade musim porovnat podle instance
        bool isDefault = true;
        if (this.MealKindId != default(short))
        {
          isDefault = false;
        }
     
        if (this.CanteenId != default(short))
        {
          isDefault = false;
        }
     
        if (this.Resort != default(string))
        {
          isDefault = false;
        }
     

          if (isDefault)
          {
            return object.ReferenceEquals(toCompare, this);
          }     
          return true;
        }

	private int? _hashCode = null;
        public override int GetHashCode()
        {
	if (_hashCode == null)
          {
          _hashCode = 13;
          _hashCode = (_hashCode * 7) + MealKindId.GetHashCode();
          _hashCode = (_hashCode * 7) + CanteenId.GetHashCode();
          _hashCode = (_hashCode * 7) + Resort.GetHashCode();
}
          return _hashCode.Value;
        }
        partial void OnMealKindIdChanging(short value);
        
        partial void OnMealKindIdChanged();
        partial void OnCanteenIdChanging(short value);
        
        partial void OnCanteenIdChanged();
        partial void OnResortChanging(string value);
        
        partial void OnResortChanged();
        partial void OnPercentageChanging(byte value);
        
        partial void OnPercentageChanged();
        partial void OnRoundingChanging(byte value);
        
        partial void OnRoundingChanged();
        partial void OnCanteenChanging(Canteen value);

        partial void OnCanteenChanged();
        
        #endregion
        public JasuResort()
        {
            this._Rounding = 0;
            OnCreated();
        }

    
        /// <summary>
        /// Sloupec: jidlo_druh
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("MealKindId", typeof(JasuResortSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(JasuResort), Tag="MealKindId")]
        public virtual short MealKindId
        {
            get
            {
                return this._MealKindId;
            }
            set
            {
                if (this._MealKindId != value)
                {
                    this.OnMealKindIdChanging(value);
                    this.SendPropertyChanging();
                    this._MealKindId = value;
                    this.SendPropertyChanged("MealKindId");
                    this.OnMealKindIdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: id_vydejna
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("CanteenId", typeof(JasuResortSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(JasuResort), Tag="CanteenId")]
        public virtual short CanteenId
        {
            get
            {
                return this._CanteenId;
            }
            set
            {
                if (this._CanteenId != value)
                {
                    this.OnCanteenIdChanging(value);
                    this.SendPropertyChanging();
                    this._CanteenId = value;
                    this.SendPropertyChanged("CanteenId");
                    this.OnCanteenIdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: Stredisko
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Resort", typeof(JasuResortSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(JasuResort), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 10,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="Resort")]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(JasuResort), Tag="Resort")]
        public virtual string Resort
        {
            get
            {
                return this._Resort;
            }
            set
            {
                if (this._Resort != value)
                {
                    this.OnResortChanging(value);
                    this.SendPropertyChanging();
                    this._Resort = value;
                    this.SendPropertyChanged("Resort");
                    this.OnResortChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: Procento
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Percentage", typeof(JasuResortSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableRangeValidator(typeof(JasuResort), (Byte)(1),  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, (Byte)(100),  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="Percentage")]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(JasuResort), Tag="Percentage")]
        public virtual byte Percentage
        {
            get
            {
                return this._Percentage;
            }
            set
            {
                if (this._Percentage != value)
                {
                    this.OnPercentageChanging(value);
                    this.SendPropertyChanging();
                    this._Percentage = value;
                    this.SendPropertyChanged("Percentage");
                    this.OnPercentageChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: Zaokrouhleni
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Rounding", typeof(JasuResortSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(JasuResort), Tag="Rounding")]
        public virtual byte Rounding
        {
            get
            {
                return this._Rounding;
            }
            set
            {
                if (this._Rounding != value)
                {
                    this.OnRoundingChanging(value);
                    this.SendPropertyChanging();
                    this._Rounding = value;
                    this.SendPropertyChanged("Rounding");
                    this.OnRoundingChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: id_vydejna
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Canteen", typeof(JasuResortSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual Canteen Canteen
        {
            get
            {
                return this._Canteen;
            }
            set
            {
                if (this._Canteen != value)
                {
                    this.OnCanteenChanging(value);
                    this.SendPropertyChanging();
                    this._Canteen = value;
                    this.SendPropertyChanged("Canteen");
                    this.OnCanteenChanged();
                }
            }
        }
   
        public virtual event PropertyChangingEventHandler PropertyChanging;

	// Mam v bazove tride, nema smysl generovat
	// public virtual event PropertyChangedEventHandler PropertyChanged;

        protected virtual void SendPropertyChanging()
        {
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, emptyChangingEventArgs);
        }

        protected virtual void SendPropertyChanging(System.String propertyName) 
        {   	
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, new PropertyChangingEventArgs(propertyName));
        }

        protected virtual void SendPropertyChanged(System.String propertyName)
        {    
		// mam v bazove tride, proto musim volat takto jednoduse
		OnPropertyChanged(propertyName);
/*		        var handler = this.PropertyChanged;
            if (handler != null)
                handler(this, new PropertyChangedEventArgs(propertyName));*/
        }
    }

}
