//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Anete.Common.Data.Nh.Entities
{
    [System.Runtime.Serialization.DataContract(Namespace=Anete.Resources.AneteNamespace.DefaultNamespace)]
    [Anete.Utils.ComponentModel.Attributes.ResXEnum(typeof(ExtDispTypeSR))]
    public enum ExtDispType : int
 {
        [System.Runtime.Serialization.EnumMember]
        None = 0,            [System.Runtime.Serialization.EnumMember]
        Disp1 = 1,            [System.Runtime.Serialization.EnumMember]
        Disp4 = 3
    }

}
