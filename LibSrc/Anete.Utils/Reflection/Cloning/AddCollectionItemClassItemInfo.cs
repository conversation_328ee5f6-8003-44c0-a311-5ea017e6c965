using System;
using System.Collections.Generic;
using System.Linq;
using System.Collections;
using System.Reflection;
using System.ComponentModel;

namespace Anete.Utils.Reflection.Cloning
{

    /// <summary>
    /// Predstavuje wrapper nad polozkou v kolekci. Dokaze vycist nebo zapsat do ni hodnotu pro prislusny index. Funguje pouze inkrementalne. 
	/// Tzn. ze pri zapisu musi pozadovany index odpovidat poctu polozek v kolekci. Zapis se deje volanim metody Add.
    /// </summary>
    public class AddCollectionItemClassItemInfo : ClassItemInfoBase
    {        
        private readonly int _sourceIndex;
        private readonly string _path;
		private readonly Type _collectionItemType;

		/// <summary>
		/// Initializes a new instance of the CollectionItemClassItemInfoBase class.
		/// </summary>		
		/// <param name="sourceIndex">Index polozky v zdrojove kolekci. Da se pouzit pouze pro vycteni polozky z zrojove kolekce.</param>
		/// <param name="pathToSourceCollection">Cesta k sourceCollection. Podle ni dokazu najit i cilovou kolekci.</param>
		/// <param name="collectionItemType">Typ polozek v kolekci.</param>
        public AddCollectionItemClassItemInfo(int sourceIndex, string pathToSourceCollection, Type collectionItemType)
        {
			_collectionItemType = collectionItemType;
            _path = pathToSourceCollection;
            _sourceIndex = sourceIndex;            
        }

        /// <summary>
        /// Vycteni hodnoty
        /// </summary>
        /// <param name="rootItem"></param>
        /// <returns></returns>
        protected override object GetValueInt(object rootItem)
        {
			object result;
			if(!TryGetValue(rootItem, out result))
			{
				throw new ArgumentException(string.Format("Kolekce {0} neobsahuje polozku s indexem {1}", _path, _sourceIndex));
			}
			return result;
        }

		/// <summary>
		/// Pokus o vycteni hodnoty
		/// </summary>
		/// <param name="rootItem"></param>
		/// <param name="result"></param>
		/// <returns></returns>
		protected override bool TryGetValueInt(object rootItem, out object result)
		{
			object propertyOwner;
			PropertyInfo pi = PropertyUtils.GetPropertyInfo(_path, rootItem, out propertyOwner);
			IEnumerable collection = (IEnumerable)pi.GetValue(propertyOwner, null);
			try
			{
				if (collection.Cast<object>().Count() <= _sourceIndex)
				{
					result = null;
					return false;
				}
				result = collection.Cast<object>().Skip(_sourceIndex).First();
			}
			catch (Exception ex)
			{
				if (ExcUtils.IsCatchableExceptionType(ex))
				{
					result = null;
					return false;
				}
				else
				{
					throw;
				}
			}

			return true;
		}


        /// <summary>
        /// Nastaveni hodnoty
        /// </summary>
        /// <param name="rootItem"></param>
        /// <param name="value"></param>
        protected override void SetValueInt(object rootItem, object value)
        {
            object propertyOwner;
            PropertyInfo pi = PropertyUtils.GetPropertyInfo(_path, rootItem, out propertyOwner);
            IEnumerable collection = (IEnumerable)pi.GetValue(propertyOwner, null);

			// musim zajistit, ze bude vlozen na prislusny index, protoze pomoci tohoto indexu se k polozce pote pristupuje
			IBindingList bindingList;
			IList list;			
			if ((bindingList = collection as IBindingList) != null)
			{
				bindingList.Insert(_sourceIndex, value);
			}
			else if ((list = collection as IList) != null)
			{
				list.Insert(_sourceIndex, value);
			}
			else
			{
				// nepodporuju metodu insert, pravdepodobne podporuji metodu Add a Clear.
				// abych zachoval index, musim prvni volat Clear a pak postupne Add.
				// Cela tata silenost funguje i pro ISet<>, kdy za predpokladu, ze polozky vzdy preskladam, muzu se nasledne spolehnout na to,
				// ze polzoka bude dostupna pod pozadaovanym indexem, cili v danem poradi v pripade enumerace prvku ISet<>.

				object[] backupItems = collection.Cast<object>().ToArray();
				MethodInfo mi = collection.GetType().GetMethod("Clear");
				MethodInfo addMi = collection.GetType().GetMethod("Add");

                // ToJTODO: 11.4.2012 Problem v tom, ze pridavam polozku, ktera jeste nema spravne vytvorene id. Z toho duvodu ji bude spatne vytvoren Hash, ktery se nasledne jiz neobnovi.

				mi.Invoke(collection, new object[] { });
				int index = 0;				

				foreach (object buckup in backupItems)
				{
					index++;					
					addMi.Invoke(collection, new[] { buckup });
					if (index == _sourceIndex)
					{
						addMi.Invoke(collection, new[] { value });
					}
				}

				// pokud pridavam na prvni pozici, musi byt vlozeno az naposled
				if (_sourceIndex == 0)
				{
					addMi.Invoke(collection, new[] { value });
				}

				//throw new ArgumentException(string.Format("Collection type '{0}' not supported", collection.GetType()));								
			}
        }

        /// <summary>
        /// Typ hodnoty
        /// </summary>
        /// <returns></returns>
        protected override Type GetItemValueType()
        {
			return _collectionItemType;
        }

        /// <summary>
        /// Vytvori text, ktery se da pouzit pro logovani.
        /// </summary>
        /// <param name="actionOfItem">Popis akce, ktera se s danou polozkou provadi.</param>
        /// <returns></returns>
        internal override string GetLogText(string actionOfItem)
        {
            return string.Format("Add {0} {1} item from '{2}' collection", actionOfItem, _sourceIndex, _path);
        }

		/// <summary>
		/// Vrati nazev polozky
		/// </summary>
		/// <returns></returns>
		protected override string GetItemName()
		{
			return _path;
		}
	}
}
