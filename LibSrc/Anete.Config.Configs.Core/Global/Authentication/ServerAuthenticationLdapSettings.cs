using Anete.Common.Core.Interface.Enums;
using Anete.Config.Configs.Core.Shared.Authentication;
using Anete.Config.Core.Attributes;
using Anete.Utils.ComponentModel.Attributes;

namespace Anete.Config.Configs.Core.Global.Authentication
{
	public class ServerAuthenticationLdapSettings : ServerAuthenticationSettingsBase
	{
		public override AppAuthenticationType AuthType => AppAuthenticationType.Ldap;

		private LdapAuthenticationSettings _server = new LdapAuthenticationSettings();
		[ConfigSRDisplayName(nameof(Server), typeof(ServerAuthenticationLdapSettings))]
		[ConfigSRDescription(nameof(Server), typeof(ServerAuthenticationLdapSettings))]
		[NestedClass]
		[Inherit(false)]
		public LdapAuthenticationSettings Server
		{
			get { return _server; }
			set
			{
				if (_server != value)
				{
					_server = value;
					OnPropertyChanged();
				}
			}
		}
	}
}