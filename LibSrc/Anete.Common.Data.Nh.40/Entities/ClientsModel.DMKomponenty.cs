//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Anete.Common.Data.Nh.Entities
{

    /// <summary>
    /// Tabulka: dba.DM_Komponenty
    /// </summary>
    public partial class DMKomponenty : Anete.Data.Nh.NhEntityBase, INotifyPropertyChanging, INotifyPropertyChanged {

        private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(System.String.Empty);

        private int _IdKomponenta;

        private Anete.Common.Core.Business.InfoPro.Components.ComponentType _Typ;

        private string _Nazev;

        private string _Config;

        private byte _Verze;
    
        #region Extensibility Method Definitions
        
        partial void OnCreated();

        public override bool Equals(object obj)
        {
          DMKomponenty toCompare = obj as DMKomponenty;
          if (toCompare == null)
          {
            return false;
          }

          if (!Object.Equals(this.IdKomponenta, toCompare.IdKomponenta))
            return false;
         // pokud mam default primary key, znamena to, ze jedna o nove vytvorenou entitu, ktera jeste nema key prirazen. 
        // V takove pripade musim porovnat podle instance
        bool isDefault = true;
        if (this.IdKomponenta != default(int))
        {
          isDefault = false;
        }
     

          if (isDefault)
          {
            return object.ReferenceEquals(toCompare, this);
          }     
          return true;
        }

	private int? _hashCode = null;
        public override int GetHashCode()
        {
	if (_hashCode == null)
          {
          _hashCode = 13;
          _hashCode = (_hashCode * 7) + IdKomponenta.GetHashCode();
}
          return _hashCode.Value;
        }
        partial void OnIdKomponentaChanging(int value);
        
        partial void OnIdKomponentaChanged();
        partial void OnTypChanging(Anete.Common.Core.Business.InfoPro.Components.ComponentType value);
        
        partial void OnTypChanged();
        partial void OnNazevChanging(string value);
        
        partial void OnNazevChanged();
        partial void OnConfigChanging(string value);
        
        partial void OnConfigChanged();
        partial void OnVerzeChanging(byte value);
        
        partial void OnVerzeChanged();
        
        #endregion
        public DMKomponenty()
        {
            this._Verze = 0;
            OnCreated();
        }

    
        /// <summary>
        /// Sloupec: id_komponenta
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("IdKomponenta", typeof(DMKomponentySR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(DMKomponenty), Tag="IdKomponenta")]
        public virtual int IdKomponenta
        {
            get
            {
                return this._IdKomponenta;
            }
            set
            {
                if (this._IdKomponenta != value)
                {
                    this.OnIdKomponentaChanging(value);
                    this.SendPropertyChanging();
                    this._IdKomponenta = value;
                    this.SendPropertyChanged("IdKomponenta");
                    this.OnIdKomponentaChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: typ
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Typ", typeof(DMKomponentySR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(DMKomponenty), Tag="Typ")]
        public virtual Anete.Common.Core.Business.InfoPro.Components.ComponentType Typ
        {
            get
            {
                return this._Typ;
            }
            set
            {
                if (this._Typ != value)
                {
                    this.OnTypChanging(value);
                    this.SendPropertyChanging();
                    this._Typ = value;
                    this.SendPropertyChanged("Typ");
                    this.OnTypChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: nazev
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Nazev", typeof(DMKomponentySR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(DMKomponenty), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 256,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="Nazev")]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(DMKomponenty), Tag="Nazev")]
        public virtual string Nazev
        {
            get
            {
                return this._Nazev;
            }
            set
            {
                if (this._Nazev != value)
                {
                    this.OnNazevChanging(value);
                    this.SendPropertyChanging();
                    this._Nazev = value;
                    this.SendPropertyChanged("Nazev");
                    this.OnNazevChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: config
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Config", typeof(DMKomponentySR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(DMKomponenty), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 2147483647,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="Config")]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(DMKomponenty), Tag="Config")]
        public virtual string Config
        {
            get
            {
                return this._Config;
            }
            set
            {
                if (this._Config != value)
                {
                    this.OnConfigChanging(value);
                    this.SendPropertyChanging();
                    this._Config = value;
                    this.SendPropertyChanged("Config");
                    this.OnConfigChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: verze
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Verze", typeof(DMKomponentySR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(DMKomponenty), Tag="Verze")]
        public virtual byte Verze
        {
            get
            {
                return this._Verze;
            }
            set
            {
                if (this._Verze != value)
                {
                    this.OnVerzeChanging(value);
                    this.SendPropertyChanging();
                    this._Verze = value;
                    this.SendPropertyChanged("Verze");
                    this.OnVerzeChanged();
                }
            }
        }
   
        public virtual event PropertyChangingEventHandler PropertyChanging;

	// Mam v bazove tride, nema smysl generovat
	// public virtual event PropertyChangedEventHandler PropertyChanged;

        protected virtual void SendPropertyChanging()
        {
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, emptyChangingEventArgs);
        }

        protected virtual void SendPropertyChanging(System.String propertyName) 
        {   	
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, new PropertyChangingEventArgs(propertyName));
        }

        protected virtual void SendPropertyChanged(System.String propertyName)
        {    
		// mam v bazove tride, proto musim volat takto jednoduse
		OnPropertyChanged(propertyName);
/*		        var handler = this.PropertyChanged;
            if (handler != null)
                handler(this, new PropertyChangedEventArgs(propertyName));*/
        }
    }

}
