<?xml version="1.0" encoding="utf-8"?>
<EntityDeveloperDiagram>
  <Diagram Version="1.20.0.98">
    <DiagramModel>
      <Model xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xsi:type="ContextVwModel">
        <CustomProperties>
          <OID>0</OID>
          <BackgroundColor>Window</BackgroundColor>
        </CustomProperties>
        <Children>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>1</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>2</OID>
                  <Parent>1</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>3</OID>
                      <Parent>2</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>61852001-7680-4a96-80ab-82a85b15b9d3</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>4</OID>
                      <Parent>2</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>96d8c982-ba76-4e72-b3ac-08e5e8efe31f</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>5</OID>
                      <Parent>2</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>eac54787-9628-46e2-956b-241e87db858c</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>6</OID>
                      <Parent>2</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>54.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>3ee89e1e-ea4c-49df-b959-1465644cf252</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>7</OID>
                      <Parent>2</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>72.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>3d56eeb1-d2a6-4d37-81e7-66b004f59840</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>8</OID>
                      <Parent>2</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>90.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>9f9eaeb8-0c91-4316-86c6-98e6f71cb51f</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>9</OID>
                      <Parent>2</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>108.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>05192d5a-7810-44d2-907d-46a22015fb04</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>145 px</Width>
                  <Height>127 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>127 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>10</OID>
                  <Parent>1</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>11</OID>
                      <Parent>10</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>9223b2ba-193f-471c-bfc1-675451f10d44</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>12</OID>
                      <Parent>10</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>18 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>f8353e26-be55-42de-95b2-b78a59bd0002</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>13</OID>
                      <Parent>10</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>36 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>387bcfac-8adb-4dcc-a6bd-56fc2514d8d4</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>14</OID>
                      <Parent>10</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>54 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>abebf9cf-030b-4bcf-bb05-bc08052d58dd</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>15</OID>
                      <Parent>10</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>72 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>75732e43-d147-442f-b60e-65106a29b647</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>16</OID>
                      <Parent>10</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>90 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>7d0be2c5-d6ca-4209-a723-1abbc0dd963f</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>17</OID>
                      <Parent>10</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>108 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>5ca2c18e-d962-4b3c-a5be-e0c1654f172e</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>18</OID>
                      <Parent>10</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>126 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>237e38a7-ea18-4432-81b9-437cca071565</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>19</OID>
                      <Parent>10</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>144 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>f29fb1b2-c63d-40f6-b9c9-14920e9ee026</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>20</OID>
                      <Parent>10</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>162 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>4e14d63d-2773-4830-8962-c05eee747363</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>21</OID>
                      <Parent>10</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>180 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>3d98bae4-a790-49c1-a9a5-f7662edfd2a4</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>22</OID>
                      <Parent>10</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>198 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>e4f0fde3-3c98-40fe-9163-67ab82ffdf86</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>23</OID>
                      <Parent>10</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>216 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>f4ea1b14-945b-4257-aa58-25ec944507a0</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>24</OID>
                      <Parent>10</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>234 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>b3ef0a6c-18b3-45d9-b5c1-3a693e5c74a1</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>25</OID>
                      <Parent>10</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>252 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>02c69589-4860-4160-af2c-f305b8f7ba5c</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>26</OID>
                      <Parent>10</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>ee284ea9-16a0-46cd-9385-fc20aed344cb</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>27</OID>
                      <Parent>10</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>f6d03285-8ab9-4012-9334-ff4f68baa5f5</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>28</OID>
                      <Parent>10</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>848c571b-7663-4f0d-bc75-c503191ff16a</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>29</OID>
                      <Parent>10</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>b05575cc-d37a-42d9-8ab8-3e2f92e890af</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>30</OID>
                      <Parent>10</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>75a914dd-46cb-470c-957e-e399e3c7d8ff</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>31</OID>
                      <Parent>10</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>26ce25b5-3b64-4f88-b828-145a3a9bf5f1</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>32</OID>
                      <Parent>10</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>d866e6b1-4408-4d09-9745-d9e64c80ad2f</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>33</OID>
                      <Parent>10</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>74f5a6fa-98c2-47f1-9586-650e37fc5be1</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>34</OID>
                      <Parent>10</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>4e66aab4-f7c9-46d2-84f3-c371254f3253</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>35</OID>
                      <Parent>10</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>eb219f0d-ea54-454f-920f-d18acc8df339</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>36</OID>
                      <Parent>10</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>cbb63f5c-7250-48ec-80f6-c440cd73e660</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>126 px</Y>
                </Location>
                <Size>
                  <Width>145 px</Width>
                  <Height>289 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>289 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>-240 px</X>
              <Y>-240 px</Y>
            </Location>
            <Size>
              <Width>150 px</Width>
              <Height>464 px</Height>
            </Size>
            <MinSize>
              <Width>100 px</Width>
              <Height>464 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>464 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>c49595d9-8db6-4e7c-b352-ad3096fd2c2e</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>37</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>38</OID>
                  <Parent>37</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>39</OID>
                      <Parent>38</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>4e3d6b83-3ff4-478b-a972-c1b764771649</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>40</OID>
                      <Parent>38</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>83a7d6b6-0c22-4de3-aef2-7f3fe483ec98</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>41</OID>
                      <Parent>38</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>6f694aa4-0774-4688-894e-d8b1a7af17f7</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>42</OID>
                      <Parent>38</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>54.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>0da9c999-a131-4293-b9d1-74222780a8fb</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>43</OID>
                      <Parent>38</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>72.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>0ea9fd8a-a398-4dde-a8c0-7bdd193ee4d8</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>44</OID>
                      <Parent>38</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>90.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>659273ec-3a6b-419d-8769-2d4304881ebe</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>45</OID>
                      <Parent>38</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>108.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>55c434af-2c9d-46f1-878b-4cb6eb2e3830</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>46</OID>
                      <Parent>38</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>126.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>4e817f14-4022-4afd-8c99-bb185519ccc2</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>47</OID>
                      <Parent>38</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>144.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>caed7aed-372a-470b-947d-be50db4a48e6</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>145 px</Width>
                  <Height>163 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>163 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>48</OID>
                  <Parent>37</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>49</OID>
                      <Parent>48</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>12a71ba9-d68b-4e4a-9b2a-536fc9625792</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>162 px</Y>
                </Location>
                <Size>
                  <Width>145 px</Width>
                  <Height>37 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>37 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>8 px</X>
              <Y>-240 px</Y>
            </Location>
            <Size>
              <Width>150 px</Width>
              <Height>248 px</Height>
            </Size>
            <MinSize>
              <Width>100 px</Width>
              <Height>248 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>248 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>9318f5f1-69ef-480b-a062-8aab5581366f</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>50</OID>
              <OutModel>1</OutModel>
              <InModel>37</InModel>
              <OutPort>51</OutPort>
              <InPort>52</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>124 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Left</Align>
                <CustomProperties>
                  <OID>52</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>129.967914438503 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Right</Align>
                <CustomProperties>
                  <OID>51</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <BendPoints>
              <PointD>
                <X>-70 px</X>
                <Y>-110.032085561497 px</Y>
              </PointD>
              <PointD>
                <X>-70 px</X>
                <Y>-116 px</Y>
              </PointD>
            </BendPoints>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>25fb1cb0-0497-4458-b035-be1fd5a26d18</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>53</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>54</OID>
                  <Parent>53</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>55</OID>
                      <Parent>54</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>362a33c8-dbad-4938-afb8-a01e03a46c49</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>56</OID>
                      <Parent>54</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>81de93b0-e03d-4257-9da8-a8badc4e0844</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>57</OID>
                      <Parent>54</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>9c532a83-b776-4bca-af6d-b8a06976204a</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>58</OID>
                      <Parent>54</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>54.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>50394f02-d057-429f-877b-fad1c5e11d58</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>59</OID>
                      <Parent>54</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>72.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>0b300b31-41a4-4f78-bca0-3d2c409c54d5</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>60</OID>
                      <Parent>54</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>90.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>64f14fa4-4093-4661-9950-f86c137899ee</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>61</OID>
                      <Parent>54</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>108.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>116790f9-8f10-4622-82de-34cf3441f193</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>62</OID>
                      <Parent>54</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>126.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>897a1981-d8de-4502-9c78-6fb39367890b</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>63</OID>
                      <Parent>54</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>144.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>84f0c20f-bd98-40df-b997-0dc5be6b05a4</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>64</OID>
                      <Parent>54</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>162.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>6391c0b1-1c86-44fa-9754-41246b97aec2</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>65</OID>
                      <Parent>54</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>180.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>2babdeb1-f087-4e16-8d14-38885d8a5e59</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>66</OID>
                      <Parent>54</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>198.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>a8c12390-4d1b-4260-9a0a-b7486b02ba6e</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>145 px</Width>
                  <Height>217 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>217 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>67</OID>
                  <Parent>53</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>68</OID>
                      <Parent>67</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>d3cecf97-3a16-47e8-bdb0-c3db496348ac</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>69</OID>
                      <Parent>67</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>18 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>172e6e64-c5c7-48ec-8645-56de37859650</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>70</OID>
                      <Parent>67</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>36 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>40c0a2b7-ff25-4dda-8e3e-8ea5d46c61c0</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>71</OID>
                      <Parent>67</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>54 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>60181f46-ec68-4d74-86b2-3db5aa914c2a</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>72</OID>
                      <Parent>67</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>9718d430-0f35-4b5e-ad23-7f28bd876932</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>73</OID>
                      <Parent>67</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>6aa3f356-0265-4574-93ec-2b9f1e5c5c17</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>74</OID>
                      <Parent>67</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>f4402375-4684-43f1-b9c4-736b418b9f6b</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>75</OID>
                      <Parent>67</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>4b73e14a-8666-4fb9-9abf-d06167a4750b</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>76</OID>
                      <Parent>67</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>2086bb4f-b904-48b4-964d-c661230a515f</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>77</OID>
                      <Parent>67</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>5f41b5c2-840e-4f29-955e-492c72745d12</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>216 px</Y>
                </Location>
                <Size>
                  <Width>145 px</Width>
                  <Height>91 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>91 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>-240 px</X>
              <Y>400 px</Y>
            </Location>
            <Size>
              <Width>150 px</Width>
              <Height>356 px</Height>
            </Size>
            <MinSize>
              <Width>100 px</Width>
              <Height>356 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>356 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>a458573f-4d1c-40bd-a57e-af6fe15992f2</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>78</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>79</OID>
                  <Parent>78</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>80</OID>
                      <Parent>79</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>162 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>2583dc4e-d613-4e32-a827-201b8eecf5de</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>81</OID>
                      <Parent>79</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>162 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>dfb528bf-ef09-4215-9170-745653f4ea45</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>82</OID>
                      <Parent>79</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>162 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>50b8e1ea-9c90-4276-937a-a8bb394e4a7c</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>83</OID>
                      <Parent>79</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>54.5 px</Y>
                    </Location>
                    <Size>
                      <Width>162 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>718b2d89-cf5b-46b5-8cc5-00b15dff174d</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>84</OID>
                      <Parent>79</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>72.5 px</Y>
                    </Location>
                    <Size>
                      <Width>162 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>a17258f6-c7c0-49ab-9f92-6e3a7649a18d</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>85</OID>
                      <Parent>79</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>90.5 px</Y>
                    </Location>
                    <Size>
                      <Width>162 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>2c3b23ab-6e6f-4666-a8d9-24ac90618469</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>86</OID>
                      <Parent>79</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>108.5 px</Y>
                    </Location>
                    <Size>
                      <Width>162 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>380382cd-e409-419d-ab8d-f8a5db9d9998</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>87</OID>
                      <Parent>79</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>126.5 px</Y>
                    </Location>
                    <Size>
                      <Width>162 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>adf6fb51-7638-4b4b-895a-a80dececeffe</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>88</OID>
                      <Parent>79</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>144.5 px</Y>
                    </Location>
                    <Size>
                      <Width>162 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>f790ba3c-aafd-48fd-8ff2-f10bd5f7e3dc</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>89</OID>
                      <Parent>79</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>162.5 px</Y>
                    </Location>
                    <Size>
                      <Width>162 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>003a6586-c936-4229-a21a-df561da526fd</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>90</OID>
                      <Parent>79</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>180.5 px</Y>
                    </Location>
                    <Size>
                      <Width>162 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>5b8484ee-fa08-456f-a74b-8d5ebb45dbd2</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>91</OID>
                      <Parent>79</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>198.5 px</Y>
                    </Location>
                    <Size>
                      <Width>162 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>e533548c-ae26-4b5c-b2d9-388989356029</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>92</OID>
                      <Parent>79</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>216.5 px</Y>
                    </Location>
                    <Size>
                      <Width>162 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>02ae7664-fed6-4073-b18b-6b59c9ff0989</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>93</OID>
                      <Parent>79</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>234.5 px</Y>
                    </Location>
                    <Size>
                      <Width>162 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>9d5444af-9faa-4410-b528-628a6e18f1c9</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>94</OID>
                      <Parent>79</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>252.5 px</Y>
                    </Location>
                    <Size>
                      <Width>162 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>e524d81c-e6c4-4a92-a4e0-ca32e865c4ef</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>95</OID>
                      <Parent>79</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>270.5 px</Y>
                    </Location>
                    <Size>
                      <Width>162 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>9b63bddd-2490-4384-a011-19f2368df455</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>96</OID>
                      <Parent>79</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>288.5 px</Y>
                    </Location>
                    <Size>
                      <Width>162 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>29dbb444-a146-4668-8efb-9c1a81277169</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>97</OID>
                      <Parent>79</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>306.5 px</Y>
                    </Location>
                    <Size>
                      <Width>162 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>0e8a8b00-6a34-4c9d-93a1-dd79f872e331</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>98</OID>
                      <Parent>79</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>324.5 px</Y>
                    </Location>
                    <Size>
                      <Width>162 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>ef18c641-2360-4c63-99cc-6a2609b1a721</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>99</OID>
                      <Parent>79</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>342.5 px</Y>
                    </Location>
                    <Size>
                      <Width>162 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>34c34159-7646-47e5-a40d-6bae88c9c665</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>100</OID>
                      <Parent>79</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>360.5 px</Y>
                    </Location>
                    <Size>
                      <Width>162 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>a39e18b3-2326-4c16-9448-a0c4c5e6d8ab</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>101</OID>
                      <Parent>79</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>378.5 px</Y>
                    </Location>
                    <Size>
                      <Width>162 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>6e84d62c-a8a6-4d43-8850-63f7e9db6d51</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>102</OID>
                      <Parent>79</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>396.5 px</Y>
                    </Location>
                    <Size>
                      <Width>162 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>be9e0131-5514-49ab-ac6d-b5ee9dc11653</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>103</OID>
                      <Parent>79</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>414.5 px</Y>
                    </Location>
                    <Size>
                      <Width>162 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>f7d2dc60-04e1-4edc-b115-74147eb275c6</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>163 px</Width>
                  <Height>433 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>433 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>104</OID>
                  <Parent>78</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>105</OID>
                      <Parent>104</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>162 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>925542b3-6812-4856-8b7a-9a3342cbba24</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>106</OID>
                      <Parent>104</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>18 px</Y>
                    </Location>
                    <Size>
                      <Width>162 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>49896d11-cd81-42e0-9ffb-4e5fafd42abe</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>107</OID>
                      <Parent>104</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>36 px</Y>
                    </Location>
                    <Size>
                      <Width>162 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>6e13d81d-1f30-4a70-be39-26fbdbbe08ad</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>108</OID>
                      <Parent>104</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>54 px</Y>
                    </Location>
                    <Size>
                      <Width>162 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>32c97fbc-3eba-4322-9afa-83007336d0aa</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>432 px</Y>
                </Location>
                <Size>
                  <Width>163 px</Width>
                  <Height>91 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>91 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>0 px</X>
              <Y>400 px</Y>
            </Location>
            <Size>
              <Width>168 px</Width>
              <Height>572 px</Height>
            </Size>
            <MinSize>
              <Width>100 px</Width>
              <Height>572 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>572 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>22648bad-b278-446e-89c3-2924452cf2a0</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>109</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>110</OID>
                  <Parent>109</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>111</OID>
                      <Parent>110</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>f5f6a4ec-383d-414c-9ea2-707e8fea77a2</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>112</OID>
                      <Parent>110</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>7269e49e-f94f-482c-ab25-6bfe2b6313d2</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>113</OID>
                      <Parent>110</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>e7484a4c-9aa3-4936-925c-e392deef8d72</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>114</OID>
                      <Parent>110</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>54.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>342f74fe-87d0-4266-88fb-19b3faa1d8e2</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>115</OID>
                      <Parent>110</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>72.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>73ad0d7c-728c-4a2e-8d70-1d4eaa69ae34</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>116</OID>
                      <Parent>110</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>90.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>11c0fcfc-044f-4664-8e63-df1afd8d9f9e</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>117</OID>
                      <Parent>110</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>108.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>155d797c-52c2-43cd-b46e-e52958cfa0b7</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>118</OID>
                      <Parent>110</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>126.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>4b6c760b-9987-4fd0-b60b-5dad0c2f0152</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>119</OID>
                      <Parent>110</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>144.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>06d2daaf-310f-4392-a441-1abc179080f1</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>120</OID>
                      <Parent>110</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>162.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>163a6e5d-dccf-49df-be35-6d961d9fb02f</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>121</OID>
                      <Parent>110</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>180.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>c346d598-cbde-4205-b859-f6c115062f4a</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>122</OID>
                      <Parent>110</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>198.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>a2072347-c19d-4a32-afe2-08c322acde33</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>123</OID>
                      <Parent>110</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>216.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>0beff980-ce27-4834-a9c3-bb24fee78d6b</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>124</OID>
                      <Parent>110</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>234.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>943a7f41-8482-483a-9d4e-044be1fefb97</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>125</OID>
                      <Parent>110</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>252.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>90207599-cf4f-4120-8263-93791ba32c9f</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>126</OID>
                      <Parent>110</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>270.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>acaec486-c300-4558-8ced-0e4b3812172f</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>127</OID>
                      <Parent>110</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>288.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>b1416d7c-347b-46b5-be5c-b89b9123f5fa</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>128</OID>
                      <Parent>110</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>306.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>6b4df982-52ed-4278-b2e7-552facaf6c98</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>145 px</Width>
                  <Height>325 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>325 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>129</OID>
                  <Parent>109</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>130</OID>
                      <Parent>129</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>cb3d6ce5-8939-4680-b06e-6f4b3d37451f</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>131</OID>
                      <Parent>129</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>18 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>eb7a3f2a-b2ea-44d4-97e2-8b4c68442c9d</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>132</OID>
                      <Parent>129</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>36 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>24b69f97-a3b2-4496-ace6-ffa8bc806191</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>133</OID>
                      <Parent>129</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>54 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>50d12a6c-ca10-474a-afb5-9fcc44ddd40f</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>134</OID>
                      <Parent>129</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>72 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>70c8319f-2622-4fce-8dc9-d9520843d170</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>324 px</Y>
                </Location>
                <Size>
                  <Width>145 px</Width>
                  <Height>109 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>109 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>240 px</X>
              <Y>400 px</Y>
            </Location>
            <Size>
              <Width>150 px</Width>
              <Height>482 px</Height>
            </Size>
            <MinSize>
              <Width>100 px</Width>
              <Height>482 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>482 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>a0581ef5-463a-4da7-8ced-d6caab857108</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>135</OID>
              <OutModel>78</OutModel>
              <InModel>109</InModel>
              <OutPort>136</OutPort>
              <InPort>137</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>242.051401869158 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Right</Align>
                <CustomProperties>
                  <OID>136</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>211.629268292683 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Left</Align>
                <CustomProperties>
                  <OID>137</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <BendPoints>
              <PointD>
                <X>170 px</X>
                <Y>642.051401869158 px</Y>
              </PointD>
              <PointD>
                <X>170 px</X>
                <Y>611.629268292683 px</Y>
              </PointD>
            </BendPoints>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>5fbf5e88-2d8f-4aa4-9a70-6003b1c71761</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>138</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>139</OID>
                  <Parent>138</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>140</OID>
                      <Parent>139</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>9b6d0c5c-b884-4d9d-81f6-e01529da0bb5</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>141</OID>
                      <Parent>139</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>513c1ff9-5c5c-496b-91b3-6a5652788b0a</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>142</OID>
                      <Parent>139</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>7ca7ff21-3577-421a-943a-6bd5266eb403</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>143</OID>
                      <Parent>139</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>54.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>33cc727a-5297-4a89-b996-8bd0328f4aab</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>144</OID>
                      <Parent>139</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>72.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>5341078d-f70d-4f67-ad6c-c8a724cf25d9</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>145</OID>
                      <Parent>139</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>90.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>04602555-48e8-40d3-96ac-571fc6a44ab3</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>146</OID>
                      <Parent>139</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>108.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>ec6f9ff6-ca4e-4855-a6e7-028dfca083c7</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>147</OID>
                      <Parent>139</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>126.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>0f829f2b-e265-43e2-9d81-efd6bdbd2366</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>148</OID>
                      <Parent>139</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>144.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>977a7499-4493-47f8-8a95-e2b982f41395</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>149</OID>
                      <Parent>139</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>162.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>1c29277d-aceb-400c-9b30-feee8913f0b8</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>150</OID>
                      <Parent>139</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>180.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>7baf245c-2102-4bce-939a-b77532e1839e</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>151</OID>
                      <Parent>139</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>198.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>58f3655f-24a2-4fbe-b4c2-def49f94e9ea</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>152</OID>
                      <Parent>139</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>216.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>5588b513-b214-49b6-b1e5-9b5cfbc6d275</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>153</OID>
                      <Parent>139</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>234.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>034327e7-d6f2-474c-8ebf-192d3e881939</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>154</OID>
                      <Parent>139</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>252.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>7738322c-0cf3-4f10-9b49-cf87f30e7924</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>155</OID>
                      <Parent>139</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>270.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>94c10659-e07a-4533-b886-fbf4a2437a79</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>156</OID>
                      <Parent>139</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>288.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>416663df-c525-41ca-8e78-409914559ada</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>157</OID>
                      <Parent>139</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>306.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>8a25b557-4677-4fa3-8433-25645af4f233</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>158</OID>
                      <Parent>139</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>324.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>87999ae6-79bb-4d77-ab15-b995c6559f65</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>145 px</Width>
                  <Height>343 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>343 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>159</OID>
                  <Parent>138</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>160</OID>
                      <Parent>159</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>38f6943e-0f2f-4c99-9e43-22b76625a8dc</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>161</OID>
                      <Parent>159</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>18 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>0657a23f-673a-4e75-992b-ce58ab771286</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>162</OID>
                      <Parent>159</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>36 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>6a75ecfd-661a-4682-be76-fb4c8d546a9e</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>163</OID>
                      <Parent>159</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>54 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>a9e2f028-5e9b-4b05-89d8-9cd56c2d4c85</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>164</OID>
                      <Parent>159</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>72 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>8739db07-774a-46b7-9af2-2c47bca67541</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>165</OID>
                      <Parent>159</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>90 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>e85214eb-6aff-42fa-aad0-527c6ca7d253</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>166</OID>
                      <Parent>159</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>108 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>14f292e9-8888-4653-89bf-ff672ebf709d</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>167</OID>
                      <Parent>159</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>126 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>19f4c723-125b-482d-914b-ac8001534e5b</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>168</OID>
                      <Parent>159</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>a61d0f00-d0da-4bd8-a502-8662028d5d0a</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>169</OID>
                      <Parent>159</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>244ec876-7c57-42f3-8999-14a5c2e2a4be</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>342 px</Y>
                </Location>
                <Size>
                  <Width>145 px</Width>
                  <Height>163 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>163 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>480 px</X>
              <Y>400 px</Y>
            </Location>
            <Size>
              <Width>150 px</Width>
              <Height>554 px</Height>
            </Size>
            <MinSize>
              <Width>100 px</Width>
              <Height>554 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>554 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>91d1cce5-7f2c-45b7-9095-5bbe186f1266</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>170</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>171</OID>
                  <Parent>170</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>172</OID>
                      <Parent>171</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>0 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>e3ebe478-2b1c-4d08-a2c4-f8fe5db67d69</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>173</OID>
                      <Parent>171</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>0 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>e50ed594-622b-4bc2-9a3b-7d699420f77a</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>174</OID>
                      <Parent>171</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>0 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>f39bbe49-56cf-4ac1-84b4-cf9cb97f8229</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>175</OID>
                      <Parent>171</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>54.5 px</Y>
                    </Location>
                    <Size>
                      <Width>0 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>6395bf32-7b91-4dde-94c4-9031e436ada2</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>176</OID>
                      <Parent>171</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>72.5 px</Y>
                    </Location>
                    <Size>
                      <Width>0 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>8cf2c1e3-24b9-4984-8b30-aa4950e0a646</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>177</OID>
                      <Parent>171</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>90.5 px</Y>
                    </Location>
                    <Size>
                      <Width>0 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>cae95869-f7c4-40dd-ad04-7e036d58da00</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>178</OID>
                      <Parent>171</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>108.5 px</Y>
                    </Location>
                    <Size>
                      <Width>0 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>102ede14-843b-47cd-b3a8-91e781d06e7f</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>179</OID>
                      <Parent>171</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>126.5 px</Y>
                    </Location>
                    <Size>
                      <Width>0 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>71e4092c-2ce6-4b22-9912-ded9042b7e7b</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>180</OID>
                      <Parent>171</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>144.5 px</Y>
                    </Location>
                    <Size>
                      <Width>0 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>e1984ab2-dfc9-407e-9ff6-ef4ab5a62a5c</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>181</OID>
                      <Parent>171</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>162.5 px</Y>
                    </Location>
                    <Size>
                      <Width>0 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>b684c22e-81c4-4ed8-ac83-644d10d83cbd</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>182</OID>
                      <Parent>171</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>180.5 px</Y>
                    </Location>
                    <Size>
                      <Width>0 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>f27da0bf-1c32-4e11-8eb9-eb93eb5f243b</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>183</OID>
                      <Parent>171</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>198.5 px</Y>
                    </Location>
                    <Size>
                      <Width>0 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>ac74077b-d478-4c8f-be7e-6d9345078a61</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>184</OID>
                      <Parent>171</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>216.5 px</Y>
                    </Location>
                    <Size>
                      <Width>0 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>f45df897-ce31-4a78-adba-9cbf609b520e</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>185</OID>
                      <Parent>171</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>234.5 px</Y>
                    </Location>
                    <Size>
                      <Width>0 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>ecf8ef9c-18ea-4c7d-a3bd-8573cfdb601f</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>186</OID>
                      <Parent>171</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>252.5 px</Y>
                    </Location>
                    <Size>
                      <Width>0 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>974f56f4-cd35-480e-bcdf-e24738ff80cc</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>187</OID>
                      <Parent>171</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>270.5 px</Y>
                    </Location>
                    <Size>
                      <Width>0 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>f417afef-b4db-43ac-90e3-1ec2d0d4f57d</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>289 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>188</OID>
                  <Parent>170</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>189</OID>
                      <Parent>188</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>0 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>2aa138bb-a673-4871-b4e5-ea4f3a446671</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>190</OID>
                      <Parent>188</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>18 px</Y>
                    </Location>
                    <Size>
                      <Width>0 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>ef8d2295-f1b6-46e3-baa3-81cdc5ea8682</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>191</OID>
                      <Parent>188</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>36 px</Y>
                    </Location>
                    <Size>
                      <Width>0 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>65d2ba80-8131-49d7-9e17-4470f61fb189</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>192</OID>
                      <Parent>188</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>54 px</Y>
                    </Location>
                    <Size>
                      <Width>0 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>eb2fd18c-5c21-430e-812d-cfddd4674372</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>193</OID>
                      <Parent>188</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>72 px</Y>
                    </Location>
                    <Size>
                      <Width>0 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>45774dab-fe9c-42ad-b893-81fd5fbcf9e3</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>194</OID>
                      <Parent>188</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>90 px</Y>
                    </Location>
                    <Size>
                      <Width>0 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>c51bf175-bc35-4b96-b968-fbbb18733ddd</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>195</OID>
                      <Parent>188</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>108 px</Y>
                    </Location>
                    <Size>
                      <Width>0 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>f1a67405-3784-4aaf-9bfd-405b05ce2a23</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>196</OID>
                      <Parent>188</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>126 px</Y>
                    </Location>
                    <Size>
                      <Width>0 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>089fcd1b-b191-4f28-9eed-755e2d999ab7</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>197</OID>
                      <Parent>188</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>144 px</Y>
                    </Location>
                    <Size>
                      <Width>0 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>a8987357-6a4b-4f38-a1ae-f0d022d815b1</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>198</OID>
                      <Parent>188</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>162 px</Y>
                    </Location>
                    <Size>
                      <Width>0 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>1f03c984-f975-4c7d-9ce3-3a35a1245d6d</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>199</OID>
                      <Parent>188</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>180 px</Y>
                    </Location>
                    <Size>
                      <Width>0 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>c5f08bca-28bb-4caf-928b-1bfdf502cbc0</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>200</OID>
                      <Parent>188</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>198 px</Y>
                    </Location>
                    <Size>
                      <Width>0 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>d6ea0e2e-2afd-4da1-9bb8-4281529e6eee</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>201</OID>
                      <Parent>188</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>216 px</Y>
                    </Location>
                    <Size>
                      <Width>0 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>322a0e87-5f41-47eb-82bf-71b5246c5d92</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>202</OID>
                      <Parent>188</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>234 px</Y>
                    </Location>
                    <Size>
                      <Width>0 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>be3a2ed6-42f9-49e2-a272-bcde4040ad8c</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>203</OID>
                      <Parent>188</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>3b758d1d-662f-468e-aac5-bb9a2bd9c38f</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>204</OID>
                      <Parent>188</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>8bc534b6-31b8-4226-b0be-49047761ea81</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>-295 px</Y>
                </Location>
                <Size>
                  <Width>0 px</Width>
                  <Height>271 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>271 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>232 px</X>
              <Y>-240 px</Y>
            </Location>
            <Size>
              <Width>150 px</Width>
              <Height>608 px</Height>
            </Size>
            <MinSize>
              <Width>100 px</Width>
              <Height>608 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>608 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>0b073ff6-a654-49c6-af7d-a840e1306502</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <IsCollapsed>true</IsCollapsed>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>205</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>206</OID>
                  <Parent>205</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>207</OID>
                      <Parent>206</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>a440cb5c-d44d-45dd-88d0-0a4a5b4ea13a</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>208</OID>
                      <Parent>206</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>31bba439-208d-4498-8b88-807e59dddfe4</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>209</OID>
                      <Parent>206</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>2fe77cea-0b34-46aa-932c-b8365fb1f549</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>210</OID>
                      <Parent>206</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>54.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>2eac58ef-dad3-4daf-ae80-dc635b11f7cb</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>211</OID>
                      <Parent>206</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>72.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>6af719b0-1c3f-41b4-98d6-7f697ae07557</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>145 px</Width>
                  <Height>91 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>91 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>212</OID>
                  <Parent>205</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>213</OID>
                      <Parent>212</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>b5b9f682-af07-409d-9181-3676bee38853</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>214</OID>
                      <Parent>212</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>18 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>1e16e495-bd56-464e-91d4-6fe4ef09d0fe</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>215</OID>
                      <Parent>212</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>49a059fb-a893-4e76-930d-31042543769e</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>90 px</Y>
                </Location>
                <Size>
                  <Width>145 px</Width>
                  <Height>55 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>55 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>240 px</X>
              <Y>888 px</Y>
            </Location>
            <Size>
              <Width>150 px</Width>
              <Height>194 px</Height>
            </Size>
            <MinSize>
              <Width>100 px</Width>
              <Height>194 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>194 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>0ff40614-0704-4d9e-b624-bd28c3e84285</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>216</OID>
              <OutModel>205</OutModel>
              <InModel>109</InModel>
              <OutPort>217</OutPort>
              <InPort>218</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>75 px</X>
                  <Y>0 px</Y>
                </Location>
                <Anchors>Left Right</Anchors>
                <Align>Top</Align>
                <CustomProperties>
                  <OID>217</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>75 px</X>
                  <Y>0 px</Y>
                </Location>
                <Anchors>Left Right</Anchors>
                <Align>Bottom</Align>
                <CustomProperties>
                  <OID>218</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <BendPoints>
              <PointD>
                <X>315 px</X>
                <Y>868 px</Y>
              </PointD>
              <PointD>
                <X>330 px</X>
                <Y>868 px</Y>
              </PointD>
              <PointD>
                <X>330 px</X>
                <Y>902 px</Y>
              </PointD>
              <PointD>
                <X>315 px</X>
                <Y>902 px</Y>
              </PointD>
            </BendPoints>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>61309fbb-3a55-4f11-a9b5-cf625d463b4d</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>219</OID>
              <OutModel>138</OutModel>
              <InModel>109</InModel>
              <OutPort>220</OutPort>
              <InPort>221</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>187 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Left</Align>
                <CustomProperties>
                  <OID>220</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>211.629268292683 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Right</Align>
                <CustomProperties>
                  <OID>221</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <BendPoints>
              <PointD>
                <X>460 px</X>
                <Y>587 px</Y>
              </PointD>
              <PointD>
                <X>460 px</X>
                <Y>611.629268292683 px</Y>
              </PointD>
            </BendPoints>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>0420c0da-c24d-4f95-bada-8df8138696d2</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>222</OID>
              <OutModel>170</OutModel>
              <InModel>109</InModel>
              <OutPort>223</OutPort>
              <InPort>224</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>114.5 px</X>
                  <Y>0 px</Y>
                </Location>
                <Anchors>Left Right</Anchors>
                <Align>Bottom</Align>
                <CustomProperties>
                  <OID>223</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>106.5 px</X>
                  <Y>0 px</Y>
                </Location>
                <Anchors>Left Right</Anchors>
                <Align>Top</Align>
                <CustomProperties>
                  <OID>224</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>2721b315-c8c5-4376-9a76-fc82d7170570</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>225</OID>
              <OutModel>1</OutModel>
              <InModel>109</InModel>
              <OutPort>226</OutPort>
              <InPort>227</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>294 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Right</Align>
                <CustomProperties>
                  <OID>226</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>37.5 px</X>
                  <Y>0 px</Y>
                </Location>
                <Anchors>Left Right</Anchors>
                <Align>Top</Align>
                <CustomProperties>
                  <OID>227</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <BendPoints>
              <PointD>
                <X>-70 px</X>
                <Y>54 px</Y>
              </PointD>
              <PointD>
                <X>-70 px</X>
                <Y>380 px</Y>
              </PointD>
              <PointD>
                <X>277.5 px</X>
                <Y>380 px</Y>
              </PointD>
            </BendPoints>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>3edc11a4-3291-4c97-a8ee-6a9924a904bb</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>228</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>229</OID>
                  <Parent>228</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>230</OID>
                      <Parent>229</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>162 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>c360278a-f9a1-4975-a783-0bb4097d50b1</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>231</OID>
                      <Parent>229</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>162 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>6b5b2b89-80f9-48cb-a4b5-22a7e5922d04</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>232</OID>
                      <Parent>229</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>162 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>79641c6a-817d-4130-ad05-8f59a0eea376</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>233</OID>
                      <Parent>229</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>54.5 px</Y>
                    </Location>
                    <Size>
                      <Width>162 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>f14edf19-3d13-41f1-9bd4-f1e1baa4b8d6</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>234</OID>
                      <Parent>229</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>72.5 px</Y>
                    </Location>
                    <Size>
                      <Width>162 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>72a32a65-4b0f-4085-a40f-2b977edcf70f</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>235</OID>
                      <Parent>229</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>90.5 px</Y>
                    </Location>
                    <Size>
                      <Width>162 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>31793d61-38a1-48d7-8157-059607419c86</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>236</OID>
                      <Parent>229</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>108.5 px</Y>
                    </Location>
                    <Size>
                      <Width>162 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>6998c7cb-0a1d-4328-918e-45f739f080ba</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>237</OID>
                      <Parent>229</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>126.5 px</Y>
                    </Location>
                    <Size>
                      <Width>162 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>52916d6e-3603-4127-b37c-05c6816112a6</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>238</OID>
                      <Parent>229</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>144.5 px</Y>
                    </Location>
                    <Size>
                      <Width>162 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>43f6a88d-30cf-440f-82e9-0aec99769cb1</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>239</OID>
                      <Parent>229</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>162.5 px</Y>
                    </Location>
                    <Size>
                      <Width>162 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>950adf8d-8fd4-4959-8d05-44ad285ddc77</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>240</OID>
                      <Parent>229</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>180.5 px</Y>
                    </Location>
                    <Size>
                      <Width>162 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>5c9eefca-7b4d-452f-bca8-7b87dd720192</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>163 px</Width>
                  <Height>199 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>199 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>241</OID>
                  <Parent>228</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>242</OID>
                      <Parent>241</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>162 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>b30623be-2384-414f-adf2-307d2ed6dd7b</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>198 px</Y>
                </Location>
                <Size>
                  <Width>163 px</Width>
                  <Height>37 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>37 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>424 px</X>
              <Y>-248 px</Y>
            </Location>
            <Size>
              <Width>168 px</Width>
              <Height>284 px</Height>
            </Size>
            <MinSize>
              <Width>100 px</Width>
              <Height>284 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>284 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>b4a8b7cc-5d25-480d-8735-bcd5259c5b44</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="EnumTypeVwModel">
            <CustomProperties>
              <OID>243</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>244</OID>
                  <Parent>243</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="EnumTypeMemberVwModel">
                    <CustomProperties>
                      <OID>245</OID>
                      <Parent>244</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>e41a2cc0-a2be-4637-a343-c21af41f19a3</Path>
                      <TypeName>EntityDeveloper.EnumTypeMember</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="EnumTypeMemberVwModel">
                    <CustomProperties>
                      <OID>246</OID>
                      <Parent>244</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>b8b14a53-654f-4e22-bc5a-e9cb0f08262f</Path>
                      <TypeName>EntityDeveloper.EnumTypeMember</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="EnumTypeMemberVwModel">
                    <CustomProperties>
                      <OID>247</OID>
                      <Parent>244</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>95f3d773-7b48-4273-9f35-58a7e833c49a</Path>
                      <TypeName>EntityDeveloper.EnumTypeMember</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="EnumTypeMemberVwModel">
                    <CustomProperties>
                      <OID>248</OID>
                      <Parent>244</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>54.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>9a398521-1a68-4d68-abde-c540543db630</Path>
                      <TypeName>EntityDeveloper.EnumTypeMember</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="EnumTypeMemberVwModel">
                    <CustomProperties>
                      <OID>249</OID>
                      <Parent>244</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>72.5 px</Y>
                    </Location>
                    <Size>
                      <Width>144 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>ae28d15a-f23d-43a6-9aae-0e0624f9f4b4</Path>
                      <TypeName>EntityDeveloper.EnumTypeMember</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>145 px</Width>
                  <Height>90 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>91 px</Height>
                </MaxSize>
              </Model>
            </Children>
            <Location>
              <X>616 px</X>
              <Y>-248 px</Y>
            </Location>
            <Size>
              <Width>150 px</Width>
              <Height>139 px</Height>
            </Size>
            <MinSize>
              <Width>100 px</Width>
              <Height>139 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>139 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>4b0d2899-f326-46ad-9d97-1986410fb4aa</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateEnumType</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>250</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>251</OID>
                  <Parent>250</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>252</OID>
                      <Parent>251</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>186 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>577c074b-5315-4b4a-952a-b204d53b2379</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>253</OID>
                      <Parent>251</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>186 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>18e4a7fe-5857-4b63-a838-3ac5dd83fdee</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>254</OID>
                      <Parent>251</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>186 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>66d697d1-4bde-49c4-8a48-462de78b52dc</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>255</OID>
                      <Parent>251</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>54.5 px</Y>
                    </Location>
                    <Size>
                      <Width>186 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>c2007589-94e2-4533-a213-dd65c67cb23a</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>256</OID>
                      <Parent>251</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>72.5 px</Y>
                    </Location>
                    <Size>
                      <Width>186 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>ab5482b8-774d-4dfb-8e08-141bf72d5dcf</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>257</OID>
                      <Parent>251</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>90.5 px</Y>
                    </Location>
                    <Size>
                      <Width>186 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>89411f7b-536d-41e0-9576-2e1c1d0cba47</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>258</OID>
                      <Parent>251</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>108.5 px</Y>
                    </Location>
                    <Size>
                      <Width>186 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>1970f688-301d-4a56-8729-1370f25f8f19</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>259</OID>
                      <Parent>251</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>126.5 px</Y>
                    </Location>
                    <Size>
                      <Width>186 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>90ff8c49-9edb-44cb-874e-bd0ce2445aa4</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>260</OID>
                      <Parent>251</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>144.5 px</Y>
                    </Location>
                    <Size>
                      <Width>186 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>a8c46b42-14f4-4e90-8822-5e39b6b5ef46</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>261</OID>
                      <Parent>251</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>162.5 px</Y>
                    </Location>
                    <Size>
                      <Width>186 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>f466c6fc-5aa4-444c-8598-1c95f7fb105b</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>262</OID>
                      <Parent>251</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>180.5 px</Y>
                    </Location>
                    <Size>
                      <Width>186 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>96876f11-0d75-409b-9e3e-1c712237bb6e</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>263</OID>
                      <Parent>251</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>198.5 px</Y>
                    </Location>
                    <Size>
                      <Width>186 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>0a1e35f6-db19-4ac8-964c-4fa7ba5e76ff</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>264</OID>
                      <Parent>251</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>216.5 px</Y>
                    </Location>
                    <Size>
                      <Width>186 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>641448bd-9cfe-4435-bc28-983288ccb114</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>265</OID>
                      <Parent>251</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>234.5 px</Y>
                    </Location>
                    <Size>
                      <Width>186 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>bd937eef-5443-4e8e-9aa2-cb0d93fbce8f</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>266</OID>
                      <Parent>251</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>252.5 px</Y>
                    </Location>
                    <Size>
                      <Width>186 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>badb60d0-3c3f-40a9-8902-0d573bfc021d</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>267</OID>
                      <Parent>251</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>270.5 px</Y>
                    </Location>
                    <Size>
                      <Width>186 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>26024785-8a1c-4da7-89e2-5b0857725565</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>268</OID>
                      <Parent>251</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>288.5 px</Y>
                    </Location>
                    <Size>
                      <Width>186 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>bd658eb3-f2a7-4b4f-81a0-2400f11490ad</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>269</OID>
                      <Parent>251</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>306.5 px</Y>
                    </Location>
                    <Size>
                      <Width>186 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>da7cec4c-ca8f-4731-9b04-a3a6a5a29bee</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>270</OID>
                      <Parent>251</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>324.5 px</Y>
                    </Location>
                    <Size>
                      <Width>186 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>5cd9f523-45a5-4955-b784-4044296ffcf4</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>271</OID>
                      <Parent>251</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>342.5 px</Y>
                    </Location>
                    <Size>
                      <Width>186 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>b4e5a28e-9a1e-46b3-9eb9-c034671e7535</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>272</OID>
                      <Parent>251</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>360.5 px</Y>
                    </Location>
                    <Size>
                      <Width>186 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>c879b152-263e-4f86-80e6-8aa1ed82dfe6</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>187 px</Width>
                  <Height>379 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>379 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>273</OID>
                  <Parent>250</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>274</OID>
                      <Parent>273</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>186 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>e47c1188-aacc-46b2-9e80-fc356eb53aae</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>378 px</Y>
                </Location>
                <Size>
                  <Width>187 px</Width>
                  <Height>37 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>37 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>688 px</X>
              <Y>408 px</Y>
            </Location>
            <Size>
              <Width>192 px</Width>
              <Height>464 px</Height>
            </Size>
            <MinSize>
              <Width>100 px</Width>
              <Height>464 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>464 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>1b3fcd37-9712-40b2-90d4-98b7846557da</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>275</OID>
              <OutModel>1</OutModel>
              <InModel>250</InModel>
              <OutPort>276</OutPort>
              <InPort>277</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>156 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Left</Align>
                <CustomProperties>
                  <OID>277</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>340 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Right</Align>
                <CustomProperties>
                  <OID>276</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <BendPoints>
              <PointD>
                <X>-70 px</X>
                <Y>100 px</Y>
              </PointD>
              <PointD>
                <X>-70 px</X>
                <Y>229.5 px</Y>
              </PointD>
              <PointD>
                <X>-87.5 px</X>
                <Y>229.5 px</Y>
              </PointD>
              <PointD>
                <X>-87.5 px</X>
                <Y>301 px</Y>
              </PointD>
              <PointD>
                <X>652 px</X>
                <Y>301 px</Y>
              </PointD>
              <PointD>
                <X>652 px</X>
                <Y>564 px</Y>
              </PointD>
            </BendPoints>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>0c8524c2-48f3-44c6-8fa7-59bdcece0e58</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>278</OID>
              <OutModel>1</OutModel>
              <InModel>78</InModel>
              <OutPort>279</OutPort>
              <InPort>280</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>75 px</X>
                  <Y>0 px</Y>
                </Location>
                <Anchors>Left Right</Anchors>
                <Align>Bottom</Align>
                <CustomProperties>
                  <OID>279</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>286 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Left</Align>
                <CustomProperties>
                  <OID>280</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <BendPoints>
              <PointD>
                <X>-165 px</X>
                <Y>244 px</Y>
              </PointD>
              <PointD>
                <X>-20 px</X>
                <Y>244 px</Y>
              </PointD>
              <PointD>
                <X>-20 px</X>
                <Y>686 px</Y>
              </PointD>
            </BendPoints>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>2de65c50-8f24-4371-8859-bcb797c67b40</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>281</OID>
              <OutModel>53</OutModel>
              <InModel>78</InModel>
              <OutPort>282</OutPort>
              <InPort>283</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>253.5 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Right</Align>
                <CustomProperties>
                  <OID>282</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>342.534146341464 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Left</Align>
                <CustomProperties>
                  <OID>283</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <BendPoints>
              <PointD>
                <X>-70 px</X>
                <Y>653.5 px</Y>
              </PointD>
              <PointD>
                <X>-70 px</X>
                <Y>742.534146341464 px</Y>
              </PointD>
            </BendPoints>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>fa0ea268-0860-4d0e-ac48-e14582bbe68b</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>284</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>285</OID>
                  <Parent>284</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>286</OID>
                      <Parent>285</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>218 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>2cc4187f-9934-40d6-82be-76933ee4f1b0</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>287</OID>
                      <Parent>285</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>218 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>a5548c7b-bdc8-4299-b67c-b5643a1071d0</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>288</OID>
                      <Parent>285</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>218 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>35f141e9-522d-45f4-b79c-aaa202652a09</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>289</OID>
                      <Parent>285</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>54.5 px</Y>
                    </Location>
                    <Size>
                      <Width>218 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>036fd770-97fe-4501-8534-442f2de609b2</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>290</OID>
                      <Parent>285</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>72.5 px</Y>
                    </Location>
                    <Size>
                      <Width>218 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>d2c98308-cf41-41d9-b445-712d74ddb5de</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>219 px</Width>
                  <Height>91 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>91 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>291</OID>
                  <Parent>284</Parent>
                </CustomProperties>
                <Children />
                <Location>
                  <X>0 px</X>
                  <Y>90 px</Y>
                </Location>
                <Size>
                  <Width>219 px</Width>
                  <Height>19 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>19 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>808 px</X>
              <Y>-248 px</Y>
            </Location>
            <Size>
              <Width>224 px</Width>
              <Height>158 px</Height>
            </Size>
            <MinSize>
              <Width>100 px</Width>
              <Height>158 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>158 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>84b29510-7b49-4382-b25d-7b97ef5cbf1e</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>292</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>293</OID>
                  <Parent>292</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>294</OID>
                      <Parent>293</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>178 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>9899962a-fa55-4027-9faf-1dce7e578d6a</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>295</OID>
                      <Parent>293</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>178 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>1af99ecf-735c-48be-bf37-b3dcf4bcc97e</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>296</OID>
                      <Parent>293</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>178 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>c56e3b7b-15fd-4e7c-9b9c-026bf24dba88</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>297</OID>
                      <Parent>293</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>54.5 px</Y>
                    </Location>
                    <Size>
                      <Width>178 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>6b04e806-6d8b-4054-94e4-0c2985fa9f22</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>298</OID>
                      <Parent>293</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>72.5 px</Y>
                    </Location>
                    <Size>
                      <Width>178 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>d66fb1f9-be91-4e6c-8848-da5b24cf3270</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>299</OID>
                      <Parent>293</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>90.5 px</Y>
                    </Location>
                    <Size>
                      <Width>178 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>51e202a7-b80b-4a05-806f-34351ce96ca3</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>179 px</Width>
                  <Height>109 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>109 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>300</OID>
                  <Parent>292</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>301</OID>
                      <Parent>300</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>178 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>75f2761d-2b3e-46a3-b351-bb5b8b0087b3</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>108 px</Y>
                </Location>
                <Size>
                  <Width>179 px</Width>
                  <Height>37 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>37 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>816 px</X>
              <Y>-48 px</Y>
            </Location>
            <Size>
              <Width>184 px</Width>
              <Height>194 px</Height>
            </Size>
            <MinSize>
              <Width>100 px</Width>
              <Height>194 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>194 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>17b094e2-58f8-4642-bb11-eae8b446202d</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>302</OID>
              <OutModel>1</OutModel>
              <InModel>292</InModel>
              <OutPort>303</OutPort>
              <InPort>304</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>231.51867219917 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Right</Align>
                <CustomProperties>
                  <OID>303</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>0 px</X>
                  <Y>48.5 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Left</Align>
                <CustomProperties>
                  <OID>304</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <BendPoints>
              <PointD>
                <X>-70 px</X>
                <Y>-8.48132780083 px</Y>
              </PointD>
              <PointD>
                <X>-70 px</X>
                <Y>42.5 px</Y>
              </PointD>
              <PointD>
                <X>796 px</X>
                <Y>42.5 px</Y>
              </PointD>
              <PointD>
                <X>796 px</X>
                <Y>0.5 px</Y>
              </PointD>
            </BendPoints>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>c3d0113f-17c5-4f93-8013-fc4220e4098f</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
        </Children>
        <GridSize>8 px</GridSize>
        <ViewPort>
          <ScaleMode>Free</ScaleMode>
          <Scale>1</Scale>
          <Location>
            <X>-154 px</X>
            <Y>216 px</Y>
          </Location>
        </ViewPort>
        <Oid xsi:type="SchemaModelOID">
          <Path>9cf690cb-e5be-4b9e-9cd9-51471560c52c</Path>
          <TypeName>EntityDeveloper.NHibernate.HibernateContextModel</TypeName>
        </Oid>
      </Model>
    </DiagramModel>
  </Diagram>
  <DiagramOptions Version="v2.0">
    <Options xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xsi:type="PageOptions">
      <TopLeftMargins>
        <Width>39.3700787401575 in/100</Width>
        <Height>39.3700787401575 in/100</Height>
      </TopLeftMargins>
      <BottomRightMargins>
        <Width>39.3700787401575 in/100</Width>
        <Height>39.3700787401575 in/100</Height>
      </BottomRightMargins>
      <PaperSize>
        <Width>827 in/100</Width>
        <Height>1169 in/100</Height>
      </PaperSize>
    </Options>
    <Options xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xsi:type="PrintOptions" />
    <Options xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xsi:type="ViewOptions">
      <ShadowOffset>
        <X>4 px</X>
        <Y>4 px</Y>
      </ShadowOffset>
      <CustomProperties />
    </Options>
    <EdDiagramOptions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
      <CustomProperties />
    </EdDiagramOptions>
  </DiagramOptions>
</EntityDeveloperDiagram>