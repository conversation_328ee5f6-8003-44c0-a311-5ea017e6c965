using Anete.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Anete.Common.Core.Business.InstallationParameters.Parameters.System.DeviceControllerHwTypes
{
	public class DeviceControllerHwTypeParameterItem
	{
		public DeviceControllerHwTypeParameterItem(string name, uint quantity)
		{
			Name = name;
			Quantity = quantity;
		}

		public string Name { get; }
		public uint Quantity { get; }

		public string TextRepresentation
		{
			get
			{
				return $"{Name}: {Quantity}";
			}
		}

		public override int GetHashCode()
		{
			return HashCodeUtils.GetHashCode(Name, Quantity);
		}

		public override bool Equals(object obj)
		{
			DeviceControllerHwTypeParameterItem other = obj as DeviceControllerHwTypeParameterItem;
			if (other == null)
			{
				return false;
			}

			return Name == other.Name
				&& Quantity == other.Quantity;
		}
	}
}
