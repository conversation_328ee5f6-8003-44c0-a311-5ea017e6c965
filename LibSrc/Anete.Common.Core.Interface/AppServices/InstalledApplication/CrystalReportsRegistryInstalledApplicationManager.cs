using System;
using Anete.Common.Core.Interface.Enums;
using Anete.Common.Core.Interface.Enums.InstallationLocationAttributes;
using Anete.Log.Core.Log4NetProxy;
using Anete.Utils.Extensions;
using Microsoft.Win32;

namespace Anete.Common.Core.Interface.AppServices.InstalledApplication
{
	public class CrystalReportsRegistryInstalledApplicationManager: RegistryInstalledApplicationManagerBase
	{
		private readonly CrystalReportsInstalledService _crystalReportsInstalledService;
		private static readonly ILogEx _log = LogManagerEx.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

		public CrystalReportsRegistryInstalledApplicationManager(CrystalReportsInstalledService crystalReportsInstalledService)
		{
			_crystalReportsInstalledService = crystalReportsInstalledService;
		}

		protected override InstalledApplicationBase GetInstalledApplicationByUpdateTypeInt(UpdateTypeId updateTypeId)
		{
			_log.DebugFormat("GetInstalledApplicationByUpdateTypeInt ({0})", updateTypeId);

			Version version = _crystalReportsInstalledService.GetInstalledVersionCrystalReportsVersion();
			if (version == null)
			{
				return null;
			}
			if (version.Revision == -1)	//crystal reports nemá číslo revize v registrech a guard vyhazuje exception
			{
				version = new Version(version.Major, version.Minor, version.Build, 0);
			}

			return new OtherInstalledApplication(updateTypeId.ToApplicationType(),updateTypeId.ToSoftwarePlatform(), updateTypeId, version);
		}
	}
}