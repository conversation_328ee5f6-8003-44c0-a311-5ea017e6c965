using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data.SqlClient;
using Anete.Utils.Extensions;
using System.Data.Common;
using System.Data;
using System.IO;
using Anete.Log.Core.Log4NetProxy;

namespace Anete.Data
{
	/// <summary>
	/// Extension metody pro SqlCommand
	/// </summary>
	public static class SqlCommandExt
	{
		private static readonly ILogEx _nHibernateLog = LogManagerEx.GetLogger(Anete.Data.DataLoggerNames.Sql);

		/// <summary>
		/// Vraci text vhodny pro logovani SqlCommand. Obsahuje nazev a parametry.
		/// </summary>		
		/// <returns></returns>
		public static string GetLogText(this IDbCommand sqlCommand, params string[] parametersToMask)
		{
			return SqlQueryLogger.GetLogText((SqlCommand)sqlCommand, parametersToMask);
		}

		public static void LogDebug(this IDbCommand sqlCommand, params string[] parametersToMask)
		{
			_nHibernateLog.Debug(GetLogText(sqlCommand, parametersToMask));
		}
	}
}
