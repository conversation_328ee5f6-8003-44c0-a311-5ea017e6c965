//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON>ti<PERSON> Developer tool using NHibernate Fluent Mapping template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;
using FluentNHibernate.Mapping;
using FluentNHibernate.MappingModel.Collections;

namespace Anete.Common.Data.Nh.Entities
{
    /// <summary>
    /// There are no comments for CostRecapMap in the schema.
    /// </summary>
    public partial class CostRecapMap : ClassMap<CostRecap>
    {
        /// <summary>
        /// There are no comments for CostRecapMap constructor in the schema.
        /// </summary>
        public CostRecapMap()
        {
              Schema(@"dba");
              Table(@"RekapitulaceNakladu");
              ReadOnly();
              LazyLoad();
              Id(x => x.CostRecapId)
                .Column("pc")
                .CustomType("Int32")
                .Access.Property().CustomSqlType("int")
                .Not.Nullable()
                .Precision(10)                
                .GeneratedBy.Identity();
              Map(x => x.AccountBalanceId)    
                .Column("id_uo")
                .CustomType("Int16")
                .Access.Property()
                .Generated.Never().CustomSqlType("smallint")
                .Precision(5);
              Map(x => x.WorkplaceId)    
                .Column("id_jidelna")
                .CustomType("Int16")
                .Access.Property()
                .Generated.Never().CustomSqlType("smallint")
                .Precision(5);
              Map(x => x.MealKindId)    
                .Column("jidlo_druh")
                .CustomType("Int16")
                .Access.Property()
                .Generated.Never().CustomSqlType("smallint")
                .Precision(5);
              Map(x => x.MealKindName)    
                .Column("druh")
                .CustomType("String")
                .Access.Property()
                .Generated.Never().CustomSqlType("varchar(15)")
                .Length(15);
              Map(x => x.OrganizationId)    
                .Column("id_organizace")
                .CustomType("Int16")
                .Access.Property()
                .Generated.Never().CustomSqlType("smallint")
                .Precision(5);
              Map(x => x.OrganizationName)    
                .Column("organizace")
                .CustomType("String")
                .Access.Property()
                .Generated.Never().CustomSqlType("varchar(50)")
                .Length(50);
              Map(x => x.AccountingDepartmentId)    
                .Column("id_uctarna")
                .CustomType("Int32")
                .Access.Property()
                .Generated.Never().CustomSqlType("int")
                .Precision(10);
              Map(x => x.AccountingDepartmentName)    
                .Column("uctarna")
                .CustomType("String")
                .Access.Property()
                .Generated.Never().CustomSqlType("varchar(50)")
                .Length(50);
              Map(x => x.ResortId)    
                .Column("id_stredisko")
                .CustomType("Int32")
                .Access.Property()
                .Generated.Never().CustomSqlType("int")
                .Precision(10);
              Map(x => x.ResortName)    
                .Column("stredisko")
                .CustomType("String")
                .Access.Property()
                .Generated.Never().CustomSqlType("varchar(25)")
                .Length(25);
              Map(x => x.SubsidyCategoryId)    
                .Column("k_dotace")
                .CustomType("Int16")
                .Access.Property()
                .Generated.Never().CustomSqlType("smallint")
                .Precision(5);
              Map(x => x.Count)    
                .Column("pocet")
                .CustomType("Int32")
                .Access.Property()
                .Generated.Never().CustomSqlType("int")
                .Precision(10);
              Map(x => x.Price)    
                .Column("cena")
                .CustomType("Decimal")
                .Access.Property()
                .Generated.Never().CustomSqlType("numeric(10,2)")
                .Precision(10)
                .Scale(2);
              Map(x => x.PriceSum)    
                .Column("sum_cena")
                .CustomType("Decimal")
                .Access.Property()
                .Generated.Never().CustomSqlType("numeric(10,2)")
                .Precision(10)
                .Scale(2);
              Map(x => x.N1)    
                .Column("n1")
                .CustomType("Decimal")
                .Access.Property()
                .Generated.Never().CustomSqlType("numeric(10,2)")
                .Precision(10)
                .Scale(2);
              Map(x => x.N2)    
                .Column("n2")
                .CustomType("Decimal")
                .Access.Property()
                .Generated.Never().CustomSqlType("numeric(10,2)")
                .Precision(10)
                .Scale(2);
              Map(x => x.N3)    
                .Column("n3")
                .CustomType("Decimal")
                .Access.Property()
                .Generated.Never().CustomSqlType("numeric(10,2)")
                .Precision(10)
                .Scale(2);
              Map(x => x.N4)    
                .Column("n4")
                .CustomType("Decimal")
                .Access.Property()
                .Generated.Never().CustomSqlType("numeric(10,2)")
                .Precision(10)
                .Scale(2);
              Map(x => x.N5)    
                .Column("n5")
                .CustomType("Decimal")
                .Access.Property()
                .Generated.Never().CustomSqlType("numeric(10,2)")
                .Precision(10)
                .Scale(2);
              Map(x => x.D1)    
                .Column("d1")
                .CustomType("Decimal")
                .Access.Property()
                .Generated.Never().CustomSqlType("numeric(10,2)")
                .Precision(10)
                .Scale(2);
              Map(x => x.D2)    
                .Column("d2")
                .CustomType("Decimal")
                .Access.Property()
                .Generated.Never().CustomSqlType("numeric(10,2)")
                .Precision(10)
                .Scale(2);
              Map(x => x.D3)    
                .Column("d3")
                .CustomType("Decimal")
                .Access.Property()
                .Generated.Never().CustomSqlType("numeric(10,2)")
                .Precision(10)
                .Scale(2);
              Map(x => x.D4)    
                .Column("d4")
                .CustomType("Decimal")
                .Access.Property()
                .Generated.Never().CustomSqlType("numeric(10,2)")
                .Precision(10)
                .Scale(2);
              Map(x => x.D5)    
                .Column("d5")
                .CustomType("Decimal")
                .Access.Property()
                .Generated.Never().CustomSqlType("numeric(10,2)")
                .Precision(10)
                .Scale(2);
              Map(x => x.ResortCode)    
                .Column("stredisko_kod")
                .CustomType("String")
                .Access.Property()
                .Generated.Never().CustomSqlType("varchar(10)")
                .Length(10);
              Map(x => x.Vat)    
                .Column("DPH")
                .CustomType("Decimal")
                .Access.Property()
                .Generated.Never().CustomSqlType("numeric(8,2)")
                .Precision(8)
                .Scale(2);
              Map(x => x.AccountType)    
                .Column("srazet")
                .CustomType("Anete.Common.Data.Nh.Entities.AccountType, Anete.Common.Data.Nh.40")
                .Access.Property()
                .Generated.Never().CustomSqlType("bit")
                .Not.Nullable();
              Map(x => x.KoefPorce)    
                .Column("koef_porce")
                .CustomType("Decimal")
                .Access.Property()
                .Generated.Never().CustomSqlType("numeric(11,3)")
                .Precision(11)
                .Scale(3);
              References(x => x.SubsidyCategory)
                .Class<SubsidyCategory>()
                .Access.Property()
                .Cascade.None()
                .LazyLoad()
                .Not.Insert()
                .Not.Update()
                .Columns("k_dotace");
              References(x => x.AccountBalance)
                .Class<AccountBalance>()
                .Access.Property()
                .Cascade.None()
                .LazyLoad()
                .Not.Insert()
                .Not.Update()
                .Columns("id_uo");
              References(x => x.Workplace)
                .Class<Workplace>()
                .Access.Property()
                .Cascade.None()
                .LazyLoad()
                .Not.Insert()
                .Not.Update()
                .Columns("id_jidelna");
              References(x => x.MealKind)
                .Class<MealKind>()
                .Access.Property()
                .Cascade.None()
                .LazyLoad()
                .Not.Insert()
                .Not.Update()
                .Columns("jidlo_druh", "id_jidelna");
              References(x => x.Organization)
                .Class<Organization>()
                .Access.Property()
                .Cascade.None()
                .LazyLoad()
                .Not.Insert()
                .Not.Update()
                .Columns("id_organizace");
              References(x => x.AccountingDepartment)
                .Class<AccountingDepartment>()
                .Access.Property()
                .Cascade.None()
                .LazyLoad()
                .Not.Insert()
                .Not.Update()
                .Columns("id_uctarna");
              References(x => x.Resort)
                .Class<Resort>()
                .Access.Property()
                .Cascade.None()
                .LazyLoad()
                .Not.Insert()
                .Not.Update()
                .Columns("id_stredisko");
              ExtendMapping();
        }

        #region Partial Methods

        partial void ExtendMapping();

        #endregion
    }

}
