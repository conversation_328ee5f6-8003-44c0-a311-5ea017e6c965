//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Anete.Common.Data.Nh.Entities
{

    /// <summary>
    /// Tabulka: dba.DOCHAZKA
    /// </summary>
    public partial class Attendance : Anete.Data.Nh.NhEntityBase, INotifyPropertyChanging, INotifyPropertyChanged {

        private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(System.String.Empty);

        private int _ClientId;

        private System.DateTime _Date;

        private short _Number;

        private short _Number2;
    
        #region Extensibility Method Definitions
        
        partial void OnCreated();

        public override bool Equals(object obj)
        {
          Attendance toCompare = obj as Attendance;
          if (toCompare == null)
          {
            return false;
          }

          if (!Object.Equals(this.ClientId, toCompare.ClientId))
            return false;
          if (!Object.Equals(this.Date, toCompare.Date))
            return false;
         // pokud mam default primary key, znamena to, ze jedna o nove vytvorenou entitu, ktera jeste nema key prirazen. 
        // V takove pripade musim porovnat podle instance
        bool isDefault = true;
        if (this.ClientId != default(int))
        {
          isDefault = false;
        }
     
        if (this.Date != default(System.DateTime))
        {
          isDefault = false;
        }
     

          if (isDefault)
          {
            return object.ReferenceEquals(toCompare, this);
          }     
          return true;
        }

	private int? _hashCode = null;
        public override int GetHashCode()
        {
	if (_hashCode == null)
          {
          _hashCode = 13;
          _hashCode = (_hashCode * 7) + ClientId.GetHashCode();
          _hashCode = (_hashCode * 7) + Date.GetHashCode();
}
          return _hashCode.Value;
        }
        partial void OnClientIdChanging(int value);
        
        partial void OnClientIdChanged();
        partial void OnDateChanging(System.DateTime value);
        
        partial void OnDateChanged();
        partial void OnNumberChanging(short value);
        
        partial void OnNumberChanged();
        partial void OnNumber2Changing(short value);
        
        partial void OnNumber2Changed();
        
        #endregion
        public Attendance()
        {
            this._Number = 0;
            this._Number2 = 0;
            OnCreated();
        }

    
        /// <summary>
        /// Sloupec: id_lk
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("ClientId", typeof(AttendanceSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(Attendance), Tag="ClientId")]
        public virtual int ClientId
        {
            get
            {
                return this._ClientId;
            }
            set
            {
                if (this._ClientId != value)
                {
                    this.OnClientIdChanging(value);
                    this.SendPropertyChanging();
                    this._ClientId = value;
                    this.SendPropertyChanged("ClientId");
                    this.OnClientIdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: datum
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Date", typeof(AttendanceSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(Attendance), Tag="Date")]
        public virtual System.DateTime Date
        {
            get
            {
                return this._Date;
            }
            set
            {
                if (this._Date != value)
                {
                    this.OnDateChanging(value);
                    this.SendPropertyChanging();
                    this._Date = value;
                    this.SendPropertyChanged("Date");
                    this.OnDateChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: pocet
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Number", typeof(AttendanceSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(Attendance), Tag="Number")]
        public virtual short Number
        {
            get
            {
                return this._Number;
            }
            set
            {
                if (this._Number != value)
                {
                    this.OnNumberChanging(value);
                    this.SendPropertyChanging();
                    this._Number = value;
                    this.SendPropertyChanged("Number");
                    this.OnNumberChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: pocet2
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Number2", typeof(AttendanceSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(Attendance), Tag="Number2")]
        public virtual short Number2
        {
            get
            {
                return this._Number2;
            }
            set
            {
                if (this._Number2 != value)
                {
                    this.OnNumber2Changing(value);
                    this.SendPropertyChanging();
                    this._Number2 = value;
                    this.SendPropertyChanged("Number2");
                    this.OnNumber2Changed();
                }
            }
        }
   
        public virtual event PropertyChangingEventHandler PropertyChanging;

	// Mam v bazove tride, nema smysl generovat
	// public virtual event PropertyChangedEventHandler PropertyChanged;

        protected virtual void SendPropertyChanging()
        {
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, emptyChangingEventArgs);
        }

        protected virtual void SendPropertyChanging(System.String propertyName) 
        {   	
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, new PropertyChangingEventArgs(propertyName));
        }

        protected virtual void SendPropertyChanged(System.String propertyName)
        {    
		// mam v bazove tride, proto musim volat takto jednoduse
		OnPropertyChanged(propertyName);
/*		        var handler = this.PropertyChanged;
            if (handler != null)
                handler(this, new PropertyChangedEventArgs(propertyName));*/
        }
    }

}
