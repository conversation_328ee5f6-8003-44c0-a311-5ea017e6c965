using System;
using System.Collections.Generic;

namespace Anete.Common.Data.Interface.AppServices
{
	public interface ILinkedServerSqlConnectionProvider : ICustomSqlConnectionProvider
	{
		/// <summary>
		/// Prefix, ktery se pridava pred databazove dotazy tak, aby se data ziskaly pres linkovany server.
		/// </summary>
		string SqlCommandPrefix { get; }

		/// <summary>
		/// Metoda, ktera by se mela typicky pouzit u DataSetu. Zalozi se pomoci ni nove spojeni a zaroven v sobe obsahuje i definici prefixu, kterym se DataSet ridi.
		/// </summary>
		/// <returns></returns>
		SqlConnectionLinkedServerConnection CreateSqlConnectionLinked();		
    }
}
