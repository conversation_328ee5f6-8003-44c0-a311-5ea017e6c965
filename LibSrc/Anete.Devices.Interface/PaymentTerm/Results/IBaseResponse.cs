using System;
using System.Linq;

namespace Anete.Devices.Interface.PaymentTerm
{
	public interface IBaseResponse
	{
		/// <summary>
		/// Cas kedy doslo k prijatiu odpovede s terminalu
		/// </summary>
		DateTime DateTime { get; set; }
		/// <summary>
		/// Chybova hlaska v pripade ze Succes = false;
		/// </summary>
		string ErrorMessage { get; set; }
		/// <summary>
		/// Ip adresa terminalu
		/// </summary>
		string IpAddress { get; set; }
		/// <summary>
		/// Identifikacia zariadenia iniciujuceho komunikaciu s terminalom, ma vyznam iba u terminalov fungujucich nad protokolom AxaPro
		/// </summary>
		int Port { get; set; }
		string ResponseCodeExt { get; set; }
		string ResponseText { get; set; }
		string ResponseTextExt { get; set; }
		/// <summary>
		/// Identifikacia zariadenia iniciujuceho komunikaciu s terminalom, ma vyznam iba u terminalov fungujucich nad protokolom AxaPro
		/// </summary>
		string SourceSaleDeviceId { get; set; }
		/// <summary>
		/// Informacia ci bola transakcia uspesna
		/// </summary>
		bool Success { get; set; }
		/// <summary>
		/// Identifikacia terminalu, ma vyznam iba u terminalov fundujucich nad protokolom AxaPro
		/// </summary>
		string TerminalId { get; set; }

		/// <summary>
		/// Ak dojde ku chybe predpokladam ze dosla na sieti, pri odchytavami vynimiek v handlery potrebujem nastavit na false ak doslo k zamietnutej trasnakciis
		/// </summary>
		bool IsConnectionError { get; set; }
	}
}
