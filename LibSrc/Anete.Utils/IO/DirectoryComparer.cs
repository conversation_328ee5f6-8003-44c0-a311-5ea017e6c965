using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.IO;
using Anete.Utils.Extensions;

namespace Anete.Utils.IO
{
    // TODO9: 7.11.2011 (KaK) Funguje nedokonale. Pri rozdilne delce souboru hlasi soubory jako nove v List1 i List2. Mozna by mely byt ve zvlastnim seznamu pouze rozdilne soubory stejneho jmena
    /// <summary>
    /// Porovnavac dvou adresaru
    /// Pochazi z http://msdn.microsoft.com/en-us/library/bb546137.aspx
    /// </summary>
    public class DirectoryComparer
    {

        #region nested classes...
        /// <summary>
        /// Vysledek porovnani
        /// </summary>
        public class DirectoryComparerResult
        {

            /// <summary>
            /// Initializes a new instance
            /// </summary>
            public DirectoryComparerResult(bool areEqual, IEnumerable<FileInfo> filesDir1Only, IEnumerable<FileInfo> filesDir2Only, IEnumerable<FileInfo> differentFiles)
            {
                AreEqual = areEqual;
                FilesDir1Only = filesDir1Only;
                FilesDir2Only = filesDir2Only;
                DifferentFiles = differentFiles;
            }

            /// <summary>
            /// Jsou adresare shodne?
            /// </summary>
            public bool AreEqual { get; private set; }

            /// <summary>
            /// Soubory ktere jsou pouze v adresari 1 a nejsou v adresari 2
            /// </summary>
            public IEnumerable<FileInfo> FilesDir1Only { get; private set; }

            /// <summary>
            /// Soubory ktere jsou pouze v adresari 2 a nejsou v adresari 1
            /// </summary>
            public IEnumerable<FileInfo> FilesDir2Only { get; private set; }

            // 6.11.2011 (KaK) Toto asi uplne vyhodit. Rozdile soubory jsou uz v predchozich kolekcich
            /// <summary>
            /// Soubory ktere jsou v obou adresarich ale jsou odlisne
            /// </summary>
            public IEnumerable<FileInfo> DifferentFiles { get; private set; }

        }

        /// <summary>
        /// FileInfo pro porovnavani pomoci FileInfoComparer
        /// </summary>
        private struct ComparerFileInfo
        {
            public FileInfo FileInfo { get; set; }
            public string RelativeFileName { get; set; }
        }

        /// <summary>
        /// This implementation defines a very simple comparison
        /// between two FileInfo objects. It only compares the name
        /// of the files being compared and their length in bytes.
        /// </summary>
        private class FileInfoComparer : System.Collections.Generic.IEqualityComparer<ComparerFileInfo>
        {
            /// <summary>
            /// Initializes a new instance of the <see cref="FileInfoComparer"/> class.
            /// </summary>
            public FileInfoComparer()
            {
            }

            /// <summary>
            /// Equalses the specified f1.
            /// </summary>
            public bool Equals(ComparerFileInfo f1, ComparerFileInfo f2)
            {
                return (f1.RelativeFileName == f2.RelativeFileName && f1.FileInfo.Length == f2.FileInfo.Length);
            }

            /// <summary>
            /// Returns a hash code for this instance.
            /// </summary>
            public int GetHashCode(ComparerFileInfo fi)
            {
                string s = String.Format("{0}{1}", fi.RelativeFileName, fi.FileInfo.Length);
                return s.GetHashCode();
            }
        }
        #endregion

        #region public methods...
        /// <summary>
        /// Porovna dva adresare a soubory v nich. Neporovnava obsah souboru ale pouze atributy.
        /// </summary>
        /// <param name="dir1"></param>
        /// <param name="dir2"></param>
        public DirectoryComparerResult Compare(string dir1, string dir2)
        {
            // Take a snapshot of the file system.
            IEnumerable<ComparerFileInfo> list1 = GetDirFileInfo(dir1).ToArray();
            IEnumerable<ComparerFileInfo> list2 = GetDirFileInfo(dir2).ToArray();

            // A custom file comparer defined below
            FileInfoComparer fileInfoComparer = new FileInfoComparer();

            // This query determines whether the two folders contain
            // identical file lists, based on the custom file comparer
            // that is defined in the FileCompare class.
            // The query executes immediately because it returns a bool.
            bool areIdentical = list1.SequenceEqual(list2, fileInfoComparer);

            if (areIdentical)
            {
                return new DirectoryComparerResult(true, null, null, null);
            }

            // Find the common files. It produces a sequence and doesn't 
            // execute until the foreach statement.
            IEnumerable<ComparerFileInfo> queryCommonFiles = list1.Intersect(list2, fileInfoComparer);

            // soubory, ktere jsou pouze v adresari 1
            IEnumerable<ComparerFileInfo> queryList1Only =
                (from file in list1
                 select file).Except(list2, fileInfoComparer);
            // soubory, ktere jsou pouze v adresari 2
            IEnumerable<ComparerFileInfo> queryList2Only =
                (from file in list2
                 select file).Except(list1, fileInfoComparer);

            // KaKTODO9: 6.11.2011 (KaK) Jsou rozdilne soubory?
            return new DirectoryComparerResult(false, queryList1Only.Select(info => info.FileInfo), 
                queryList2Only.Select(info => info.FileInfo), null);
        }
        #endregion

        private static IEnumerable<ComparerFileInfo> GetDirFileInfo(string dirName)
        {
            DirectoryInfo dirInfo = new DirectoryInfo(dirName);
            return dirInfo
                .GetFiles("*.*", SearchOption.AllDirectories)
                .Select(fileInfo => new ComparerFileInfo()
                    {
                        FileInfo = fileInfo,
                        RelativeFileName = fileInfo.FullName.RemovePrefix(dirInfo.FullName.EnsureEndSeparator())
                    });
        }

    }
}
