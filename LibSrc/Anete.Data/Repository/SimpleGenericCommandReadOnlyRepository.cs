using System;
using System.Collections.Generic;
using System.Linq;
using Anete.Data.Interface.Repository;
using Anete.Data.Interface;
using System.ComponentModel;
using Anete.Utils.Extensions;

namespace Anete.Data.Repository
{
	/// <summary>
	/// Repository, ktera nacita a aktualizuje data pomoci Sql command.
	/// Urcena pouze ke cteni.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	public abstract class SimpleGenericCommandReadOnlyRepository<T> : IReadOnlyRepository<T>
           where T : class, IEntity
    {
        #region private fields...                
        private readonly bool _useChache;
        private T[] _loaded;
        #endregion

        #region constructors...
        /// <summary>
        /// Initializes a new instance of the GenericSqlCommandReadOnlyRepository class.
        /// </summary>
        public SimpleGenericCommandReadOnlyRepository() : this(true)
        {
            
        }
        /// <summary>
        /// Initializes a new instance of the GenericSqlCommandReadOnlyRepository class.
        /// </summary>
        /// <param name="useChache"></param>
        public SimpleGenericCommandReadOnlyRepository(bool useChache)
        {
            _useChache = useChache;
        }        
        #endregion

		#region protected methods...
		protected void InvalidateCache()
		{
			_loaded = null;
		}
		#endregion

        #region protected virtual methods...
        /// <summary>
        /// Implementace GetAll.
        /// </summary>
        /// <returns></returns>
        protected abstract IEnumerable<T> GetAllInt();

		protected virtual T GetByOrThrowException(Func<T, bool> predicate, string id)
		{
			T entity = GetAll().SingleOrDefault(predicate);
			if (entity == null)
			{
				// pokusim se provest prenacteni - potrebuju, aby se nove vznikle polozky ihned objevili v cache AS				
				InvalidateCache();

				entity = GetAll().SingleOrDefault(predicate);

				if(entity == null)
				{
					DescriptionAttribute desc = typeof(T).GetAttribute<DescriptionAttribute>();
					string entityName = desc == null ? typeof(T).Name : desc.Description;

					throw new RepositoryEntityNotFoundException(SimpleGenericCommandReadOnlyRepositorySR.CannotFindEntityFormat(entityName, id));
				}
			}
			return entity;
		}
        #endregion

        #region IReadOnlyRepository<T> Members
        /// <summary>
        /// Gets all.
        /// </summary>
        /// <returns></returns>
        public IEnumerable<T> GetAll()
        {
            if (_useChache)
            {
                if (_loaded == null)
                {
                    _loaded = GetAllInt().ToArray();
                }
                return _loaded;
            }
            else
            {
                return GetAllInt();
            }
        }

        /// <summary>
        /// Anies this instance.
        /// </summary>
        /// <returns></returns>
        public bool Any()
        {
            return GetAll().Any();
        }

        /// <summary>
        /// Counts this instance.
        /// </summary>
        /// <returns></returns>
        public int Count()
        {
            return GetAll().Count();
        }
        #endregion
    }
}
