using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Anete.Utils.Reflection.ClassReports;

namespace Anete.Data.EntityReports
{
    /// <summary>
    /// Atribut pro oznacovani komplexnich property. 
    /// Diky tomuto atributu mohu rozpoznat komplexni property pri sestavovani vystupu do logovani. Pri logovani nechci
    /// zapisovat navigacni ani jine property
    /// </summary>
    [AttributeUsage(AttributeTargets.Property, AllowMultiple = false, Inherited = true)]
    public class ComplexPropertyAttribute : ReportedAttribute
    {
    }
}
