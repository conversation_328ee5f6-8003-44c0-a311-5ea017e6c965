//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Anete.Common.Data.Nh.Entities
{

    /// <summary>
    /// Tabulka: dbo.ACC_UzivatelHodnotaParametru
    /// </summary>
    public partial class AccRightUserParameterValue : Anete.Data.Nh.NhEntityBase, INotifyPropertyChanging, INotifyPropertyChanged {

        private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(System.String.Empty);

        private int _UserId;

        private int _IdParameter;

        private string _XmlValue;

        private User _User;

        private AccParameter _AccParameter;
    
        #region Extensibility Method Definitions
        
        partial void OnCreated();

        public override bool Equals(object obj)
        {
          AccRightUserParameterValue toCompare = obj as AccRightUserParameterValue;
          if (toCompare == null)
          {
            return false;
          }

          if (!Object.Equals(this.UserId, toCompare.UserId))
            return false;
          if (!Object.Equals(this.IdParameter, toCompare.IdParameter))
            return false;
         // pokud mam default primary key, znamena to, ze jedna o nove vytvorenou entitu, ktera jeste nema key prirazen. 
        // V takove pripade musim porovnat podle instance
        bool isDefault = true;
        if (this.UserId != default(int))
        {
          isDefault = false;
        }
     
        if (this.IdParameter != default(int))
        {
          isDefault = false;
        }
     

          if (isDefault)
          {
            return object.ReferenceEquals(toCompare, this);
          }     
          return true;
        }

	private int? _hashCode = null;
        public override int GetHashCode()
        {
	if (_hashCode == null)
          {
          _hashCode = 13;
          _hashCode = (_hashCode * 7) + UserId.GetHashCode();
          _hashCode = (_hashCode * 7) + IdParameter.GetHashCode();
}
          return _hashCode.Value;
        }
        partial void OnUserIdChanging(int value);
        
        partial void OnUserIdChanged();
        partial void OnIdParameterChanging(int value);
        
        partial void OnIdParameterChanged();
        partial void OnXmlValueChanging(string value);
        
        partial void OnXmlValueChanged();
        partial void OnUserChanging(User value);

        partial void OnUserChanged();
        partial void OnAccParameterChanging(AccParameter value);

        partial void OnAccParameterChanged();
        
        #endregion
        public AccRightUserParameterValue()
        {
            OnCreated();
        }

    
        /// <summary>
        /// Sloupec: IdUzivatel
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("UserId", typeof(AccRightUserParameterValueSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(AccRightUserParameterValue), Tag="UserId")]
        public virtual int UserId
        {
            get
            {
                return this._UserId;
            }
            set
            {
                if (this._UserId != value)
                {
                    this.OnUserIdChanging(value);
                    this.SendPropertyChanging();
                    this._UserId = value;
                    this.SendPropertyChanged("UserId");
                    this.OnUserIdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: IdParametr
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("IdParameter", typeof(AccRightUserParameterValueSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(AccRightUserParameterValue), Tag="IdParameter")]
        public virtual int IdParameter
        {
            get
            {
                return this._IdParameter;
            }
            set
            {
                if (this._IdParameter != value)
                {
                    this.OnIdParameterChanging(value);
                    this.SendPropertyChanging();
                    this._IdParameter = value;
                    this.SendPropertyChanged("IdParameter");
                    this.OnIdParameterChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: Hodnota
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("XmlValue", typeof(AccRightUserParameterValueSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(AccRightUserParameterValue), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 2147483647,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="XmlValue")]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(AccRightUserParameterValue), Tag="XmlValue")]
        public virtual string XmlValue
        {
            get
            {
                return this._XmlValue;
            }
            set
            {
                if (this._XmlValue != value)
                {
                    this.OnXmlValueChanging(value);
                    this.SendPropertyChanging();
                    this._XmlValue = value;
                    this.SendPropertyChanged("XmlValue");
                    this.OnXmlValueChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: IdUzivatel
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("User", typeof(AccRightUserParameterValueSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual User User
        {
            get
            {
                return this._User;
            }
            set
            {
                if (this._User != value)
                {
                    this.OnUserChanging(value);
                    this.SendPropertyChanging();
                    this._User = value;
                    this.SendPropertyChanged("User");
                    this.OnUserChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: IdParametr
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("AccParameter", typeof(AccRightUserParameterValueSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual AccParameter AccParameter
        {
            get
            {
                return this._AccParameter;
            }
            set
            {
                if (this._AccParameter != value)
                {
                    this.OnAccParameterChanging(value);
                    this.SendPropertyChanging();
                    this._AccParameter = value;
                    this.SendPropertyChanged("AccParameter");
                    this.OnAccParameterChanged();
                }
            }
        }
   
        public virtual event PropertyChangingEventHandler PropertyChanging;

	// Mam v bazove tride, nema smysl generovat
	// public virtual event PropertyChangedEventHandler PropertyChanged;

        protected virtual void SendPropertyChanging()
        {
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, emptyChangingEventArgs);
        }

        protected virtual void SendPropertyChanging(System.String propertyName) 
        {   	
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, new PropertyChangingEventArgs(propertyName));
        }

        protected virtual void SendPropertyChanged(System.String propertyName)
        {    
		// mam v bazove tride, proto musim volat takto jednoduse
		OnPropertyChanged(propertyName);
/*		        var handler = this.PropertyChanged;
            if (handler != null)
                handler(this, new PropertyChangedEventArgs(propertyName));*/
        }
    }

}
