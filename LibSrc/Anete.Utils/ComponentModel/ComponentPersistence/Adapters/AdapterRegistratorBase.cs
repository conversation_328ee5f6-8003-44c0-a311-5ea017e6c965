using System;

namespace Anete.Utils.ComponentModel.ComponentPersistence.Adapters
{

	/// <summary>
	/// Bazova trida pro registraci adapteru
	/// </summary>
	/// <typeparam name="T"></typeparam>
	public abstract class AdapterRegistratorBase<T> : IAdapterRegistrator
	{

		/// <summary>
		/// Vytvori novou instanci
		/// </summary>
		/// <param name="locatorService">Lokalizacni servis pro adaptery</param>
		public AdapterRegistratorBase(T locatorService)
		{
			LocatorService = locatorService;
		}

		/// <summary>
		/// Lokalizacni servis pro adaptery
		/// </summary>
		protected T LocatorService { get; private set; }

		/// <summary>
		/// Registrace adapteru
		/// </summary>
		public abstract void Register();

	}

}
