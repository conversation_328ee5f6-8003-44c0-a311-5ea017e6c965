using System;
using System.Collections.Generic;
using System.Linq;
using Anete.Utils.Text.CommandLine;
using Anete.Common.Core.Interface.Enums;

namespace Anete.AutoUpgrades.ProductConfigurations
{
	/// <summary>
	/// Konfigurace <PERSON>
	/// </summary>
	[CommandLineManager(EnabledOptionStyles = OptionStyles.File | OptionStyles.Windows)]
	public class CashDeskProductConfiguration : StandardKredit8ProductConfigurationBase
	{
		public CashDeskProductConfiguration()
			: base(ApplicationType.CashDesk)
		{
			
		}
	}
}
