//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Anete.Common.Data.Business.Vouchers {
    using System;
    
    
    /// <summary>
    /// A strongly-typed resource class, for looking up localized strings, formatting them, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilderEx class via the ResXFileCodeGeneratorEx custom tool.
    // To add or remove a member, edit your .ResX file then rerun the ResXFileCodeGeneratorEx custom tool or rebuild your VS.NET project.
    // Copyright (c) Dmytro <PERSON>v<PERSON> 2006-2023 (http://dmytro.kryvko.googlepages.com/)
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("DMKSoftware.CodeGenerators.Tools.StronglyTypedResourceBuilderEx", "2.9.1.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Naming", "CA1724:TypeNamesShouldNotMatchNamespaces")]
    public partial class PaymentVoucherReaderSR {
        
        private static global::System.Resources.ResourceManager _resourceManager;
        
        private static object _internalSyncObject;
        
        private static global::System.Globalization.CultureInfo _resourceCulture;
        
        /// <summary>
        /// Initializes a PaymentVoucherReaderSR object.
        /// </summary>
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        public PaymentVoucherReaderSR() {
        }
        
        /// <summary>
        /// Thread safe lock object used by this class.
        /// </summary>
        public static object InternalSyncObject {
            get {
                if (object.ReferenceEquals(_internalSyncObject, null)) {
                    global::System.Threading.Interlocked.CompareExchange(ref _internalSyncObject, new object(), null);
                }
                return _internalSyncObject;
            }
        }
        
        /// <summary>
        /// Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(_resourceManager, null)) {
                    global::System.Threading.Monitor.Enter(InternalSyncObject);
                    try {
                        if (object.ReferenceEquals(_resourceManager, null)) {
                            global::System.Threading.Interlocked.Exchange(ref _resourceManager, new global::System.Resources.ResourceManager("Anete.Common.Data.Business.Vouchers.PaymentVoucherReaderSR", typeof(PaymentVoucherReaderSR).Assembly));
                        }
                    }
                    finally {
                        global::System.Threading.Monitor.Exit(InternalSyncObject);
                    }
                }
                return _resourceManager;
            }
        }
        
        /// <summary>
        /// Overrides the current thread's CurrentUICulture property for all
        /// resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return _resourceCulture;
            }
            set {
                _resourceCulture = value;
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Poukázka {0} je zablokována.'.
        /// </summary>
        public static string Blocted {
            get {
                return ResourceManager.GetString(ResourceNames.Blocted, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Poukázku {0} lze použit pouze jednou.'.
        /// </summary>
        public static string CannotUseMoreTime {
            get {
                return ResourceManager.GetString(ResourceNames.CannotUseMoreTime, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Poukázka {0} je platná pouze do {1:d}.'.
        /// </summary>
        public static string Invalid {
            get {
                return ResourceManager.GetString(ResourceNames.Invalid, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Poukázka {0} má neplatný kontrolní součet. Může být způsobeno chybou čtečky nebo snahou zákazníka o vyrobení falešné poukázky.'.
        /// </summary>
        public static string InvalidCrc {
            get {
                return ResourceManager.GetString(ResourceNames.InvalidCrc, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Poukázka {0} již byla kompletně uplatněna.'.
        /// </summary>
        public static string NoValue {
            get {
                return ResourceManager.GetString(ResourceNames.NoValue, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Poukázka {0} je zablokována.'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string BloctedFormat(object arg0) {
            return string.Format(_resourceCulture, Blocted, arg0);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Poukázku {0} lze použit pouze jednou.'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string CannotUseMoreTimeFormat(object arg0) {
            return string.Format(_resourceCulture, CannotUseMoreTime, arg0);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Poukázka {0} je platná pouze do {1:d}.'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <param name="arg1">An object (1) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string InvalidFormat(object arg0, object arg1) {
            return string.Format(_resourceCulture, Invalid, arg0, arg1);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Poukázka {0} má neplatný kontrolní součet. Může být způsobeno chybou čtečky nebo snahou zákazníka o vyrobení falešné poukázky.'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string InvalidCrcFormat(object arg0) {
            return string.Format(_resourceCulture, InvalidCrc, arg0);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Poukázka {0} již byla kompletně uplatněna.'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string NoValueFormat(object arg0) {
            return string.Format(_resourceCulture, NoValue, arg0);
        }
        
        /// <summary>
        /// Lists all the resource names as constant string fields.
        /// </summary>
        public class ResourceNames {
            
            /// <summary>
            /// Stores the resource name 'Blocted'.
            /// </summary>
            public const string Blocted = "Blocted";
            
            /// <summary>
            /// Stores the resource name 'CannotUseMoreTime'.
            /// </summary>
            public const string CannotUseMoreTime = "CannotUseMoreTime";
            
            /// <summary>
            /// Stores the resource name 'Invalid'.
            /// </summary>
            public const string Invalid = "Invalid";
            
            /// <summary>
            /// Stores the resource name 'InvalidCrc'.
            /// </summary>
            public const string InvalidCrc = "InvalidCrc";
            
            /// <summary>
            /// Stores the resource name 'NoValue'.
            /// </summary>
            public const string NoValue = "NoValue";
        }
    }
}
