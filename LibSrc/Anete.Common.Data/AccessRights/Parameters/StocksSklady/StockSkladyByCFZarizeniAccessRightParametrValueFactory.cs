using System;
using System.Collections.Generic;
using System.Linq;
using Anete.Common.Data.Interface.Business.AccessRights.Parameters;
using Anete.Common.Data.Interface.Entities;
using Anete.Common.Data.Interface.Business.AccessRights.Services;
using Anete.Common.Data.Interface.Repositories;
using Unity;

namespace Anete.Common.Data.AccessRights.Parameters.StocksSklady
{
	/// <summary>
	/// Factory pro vytvoreni <![CDATA[IAccessRightParameterValue<StockSklady>]]>.
	/// </summary>
	public class StockSkladyByCFZarizeniAccessRightParametrValueFactory : CustomAccessRightParameterValueFactoryBase<StockSklady, StockSkladyByCFZarizeniAccessRightParameterValue, AccessRightParameterValueSerializableDataBase<StockSklady>>
	{
	
		#region constructors...
		/// <summary>
		/// Initializes a new instance of the StockSkladyByCFZarizeniAccessRightParametrValueFactory class.
		/// </summary>
		public StockSkladyByCFZarizeniAccessRightParametrValueFactory(IUnityContainer container)
			: base(container, StockSkladyByCFZarizeniAccessRightParametrValueFactorySR.Caption, StockSkladyByCFZarizeniAccessRightParametrValueFactorySR.Description)
		{
		
		}		
		#endregion


		protected override void InitParameterValue(StockSkladyByCFZarizeniAccessRightParameterValue value, AccessRightParameterValueSerializableDataBase<StockSklady> data)
		{
			
		}
	}
}
