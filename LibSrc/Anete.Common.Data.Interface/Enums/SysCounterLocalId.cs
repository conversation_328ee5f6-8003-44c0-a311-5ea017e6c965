using Anete.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Anete.Utils.ComponentModel.Attributes;

namespace Anete.Common.Data.Interface.Enums
{
	/// <summary>
	/// Klice pro tabulku USysPocitadlaLokalni
	/// </summary>
	[ResXEnumAttribute(typeof(SysCounterLocalIdSR))]
	public class SysCounterLocalId : GenericEnum<string>
	{

		/// <summary>
		/// SystemNumber pro platebni terminal
		/// </summary>
		public static readonly string PaymentTermSystemNumber = "PaymentTermSystemNumber";

		/// <summary>
		/// SystemNumber pro platebni brany
		/// </summary>
		public static readonly string PaymentGateSystemNumber = "PaymentGateSystemNumber";
	}
}
