using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using System.Reflection;

namespace Anete.Utils.AppServices.Resolvers
{
    /// <summary>
    /// Pravidlo pro nalezeni spravne assembly
    /// </summary>
    public abstract class AssemblyResolveRuleBase : ResolveRulebase
    {
        /// <summary>
        /// Initializes a new instance of the AssemblyResolveRuleBase class.
        /// </summary>
        public AssemblyResolveRuleBase(int priority)
            : base(priority)
        {
            
        }                

        /// <summary>
        /// Pro dany typ a assembly vrati assembly, v ktere je typ umisten.
        /// </summary>
        /// <param name="assemblyName"></param>
        /// <param name="typeName"></param>
        /// <returns>Seznam nazvu assembly, pro ktere bylo nalezeno pravidlo</returns>
        public IEnumerable<string> ResolveAssembly(string assemblyName, string typeName)
        {
            CheckIsInitialized();
            return ResolveAssemblyInt(assemblyName, typeName);
        }       

        /// <summary>
        /// Pro dany typ a assembly vrati assembly, v ktere je typ umisten.
        /// </summary>
        /// <param name="assemblyName"></param>
        /// <param name="typeName"></param>
        /// <returns>Seznam nazvu assembly, pro ktere bylo nalezeno pravidlo</returns>
        protected abstract IEnumerable<string> ResolveAssemblyInt(string assemblyName, string typeName);           
    }
}
