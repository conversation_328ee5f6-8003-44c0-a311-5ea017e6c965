using Anete.Config.Core.Attributes;
using System;
using System.ComponentModel;
using System.Linq;

namespace Anete.Config.Configs.Core.Global.Behaviour
{
	/// <summary>
	/// Nasteni externiho reseni eKasy
	/// </summary>
	public class EKasaPortosSettings : EKasaSettingsBase
	{
		private const string _configFileNameDefault = @"{commonappdata}\NineDigit\Portos.eKasa\settings.json";
		private string _configFileName = _configFileNameDefault;
		/// <summary>
		/// Plna cesta k souboru s kofiguraci. {commonappdata} = C:\ProgramData
		/// Vlastni cesta se zjistuje volanim PathUtils.ExpandPath.
		/// </summary>
		[DefaultValue(_configFileNameDefault)]
		[ConfigSRDisplayName("ConfigFileName", typeof(EKasaPortosSettings))]
		[ConfigSRDescription("ConfigFileName", typeof(EKasaPortosSettings))]
		public string ConfigFileName
		{
			get { return _configFileName; }
			set
			{
				if (_configFileName == value)
					return;
				_configFileName = value;
				OnPropertyChanged();
			}
		}

		private int? _voucherIdPlatidlaDruhy;
		/// <summary>
		/// Id platidla druhy, ktere se pouzije pro poukazy pro odpocet celkove castky z dokladu.
		/// Viz. pozadavky NBS https://helpdesk.anete.com/issues/50921
		/// </summary>
		[DefaultValue(typeof(int?), "null")]
		[ConfigSRDisplayName("VoucherIdPlatidlaDruhy", typeof(EKasaPortosSettings))]
		[ConfigSRDescription("VoucherIdPlatidlaDruhy", typeof(EKasaPortosSettings))]
		public int? VoucherIdPlatidlaDruhy
		{
			get => _voucherIdPlatidlaDruhy;
			set
			{
				if (_voucherIdPlatidlaDruhy == value)
					return;
				_voucherIdPlatidlaDruhy = value;
				OnPropertyChanged();
			}
		}
		
	}
}
