using System;
using System.Collections.Generic;
using System.Linq;
using Anete.Common.Data.Business.Allergens;
using System.Globalization;
using Anete.Common.Core.Interface.Enums;

namespace Anete.Common.Data.Business.MealNote
{
	public interface IAdvancedNoteTextResolver
	{
		/// <summary>
		/// Vraci jednotlive textove slozky poznamky
		/// </summary>		
		IEnumerable<string> GetNoteLines(NoteToFormat note, ApplicationLanguage languageId, ApplicationLanguage? altLanguageId, DisplayAllergensAs displayAllergenAs, CultureInfo culture = null, CultureInfo altCultere = null);
	}
}
