using Anete.Common.Data.Interface.Business.AccessRights.Parameters;
using Anete.Common.Data.Interface.Entities;
using Anete.Resources;
using System;
using System.Linq;
using System.Runtime.Serialization;

namespace Anete.Common.Data.AccessRights.Parameters.StocksSklady
{
	[DataContract(Namespace = AneteNamespace.DefaultNamespace)]
	public class StockSkladyByProviderAccessRightParameterValueSerializableData : AccessRightParameterValueSerializableDataBase<StockSklady>
	{

		protected override void AssignInt(IAccessRightParameterValue<StockSklady> accessRightParameterValue)
		{

		}
	}
}