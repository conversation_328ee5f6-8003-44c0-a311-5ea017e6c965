using Anete.Config.Configs.Core.Global.Behaviour;
using Anete.Config.Core;
using NHibernate;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Anete.Common.Data.Nh.Services.SloHealthyMenu
{
	/// <summary>
	/// S<PERSON><PERSON><PERSON>, ktera pro dany den poskytne seznam zdravych alternativ dle konfigurace.
	/// </summary>
	public class SloHealthyMenuService
	{
		private readonly SloHealthyMenuConfig _config;

		public SloHealthyMenuService(IConfigManager configManager)
		{
			_config = configManager.GetConfig<SloHealthyMenuConfig>();
		}

		public IEnumerable<SloHealthyMenuItem> GetHealthyMenu(ISession session, DateTime date)
		{
			Config.Configs.Core.Global.Behaviour.SloHealthyMenuItem activeItem = GetActiveItem(date);

			short[] mealKinds = activeItem.MealKinds;
			var result = session.Query<Anete.Common.Data.Nh.Entities.MenuRow>()
				.Where(m => m.Date.Date == date.Date && m.WorkplaceId == activeItem.WorkplaceId && mealKinds.Contains(m.MealKindId))
				.ToArray()
				.Select(m => new SloHealthyMenuItem(m.NameAsThreadCultureLanguage, m.Weight ?? 0, m.MenuRowId, m.MealAlt))
				.Where(m => !string.IsNullOrEmpty(m.Name));

			return result;
		}


		/// <summary>
		/// Jedna se o polozku jidelnicku, ktera predstavuje zdrave jidlo?
		/// </summary>		
		public bool IsHealtyMenuRow(DateTime date, short workplaceId, short mealKindId, short alt)
		{
			Config.Configs.Core.Global.Behaviour.SloHealthyMenuItem activeItem = GetActiveItem(date);

			return activeItem.WorkplaceId == workplaceId && activeItem.MealKindId == mealKindId && activeItem.Alt == alt;
		}

		/// <summary>
		/// Je pro zvolene datum nakonfigurovano?
		/// </summary>
		/// <param name="date"></param>
		/// <returns></returns>
		public bool IsActive(DateTime date)
		{
			Config.Configs.Core.Global.Behaviour.SloHealthyMenuItem activeItem = FindActiveItem(date);

			return activeItem != null;
		}

		/// <summary>
		/// Vrací pro zvolený datum základní informace o konfiguraci zdravých jídel pro filtrovani.
		/// </summary>
		/// <param name="date"></param>
		/// <returns></returns>
		public SloHealthyMenuRow GetHealthyMenuRow(DateTime date)
		{
			Config.Configs.Core.Global.Behaviour.SloHealthyMenuItem activeItem = GetActiveItem(date);
			return new SloHealthyMenuRow(activeItem.WorkplaceId, activeItem.MealKindId, activeItem.Alt);
		}

		private Config.Configs.Core.Global.Behaviour.SloHealthyMenuItem FindActiveItem(DateTime date)
		{
			return _config.Items.Where(i => i.ValidFrom.Date <= date)
				.OrderByDescending(i => i.ValidFrom)
				.FirstOrDefault();
		}

		public Config.Configs.Core.Global.Behaviour.SloHealthyMenuItem GetActiveItem(DateTime date)
		{
			Config.Configs.Core.Global.Behaviour.SloHealthyMenuItem activeItem = FindActiveItem(date);
			CheckActiveItem(date, activeItem);
			return activeItem;
		}

		private static void CheckActiveItem(DateTime date, Config.Configs.Core.Global.Behaviour.SloHealthyMenuItem activeItem)
		{
			if (activeItem == null)
			{
				throw new ArgumentException($"Pro datum {date} neexistuje zadne platne nastaveni zdravych jidel");
			}
		}
	}
}