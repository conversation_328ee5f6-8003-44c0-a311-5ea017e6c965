using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Anete.Common.Data.Business.Eet.Algorithm;
using Anete.Common.Data.Interface.Business.Eet.Algorithm;
using Anete.Config.Configs.Core.Global.Behaviour;
using Anete.Config.Core;
using Anete.Utils;
using Anete.Utils.ComponentModel;
using Unity;

namespace Anete.Common.Data.Business.Eet
{

	public class ServiceRegistrator : UnityContainerRegistratorBase
	{

		public ServiceRegistrator(IUnityContainer container)
			: base(container)
		{

		}

		public void Register()
		{
			IConfigManager configManager = Container.Resolve<IConfigManager>();
			GlobalBehaviorEetConfig config = configManager.GetConfig<GlobalBehaviorEetConfig>();

			switch (config.AlgorithmType)
			{
				case EetAlgorithmType.V1:
					Container.RegisterType<IEetTrzbaAlgorithm, V1Algorithm>();
					break;
				case EetAlgorithmType.V2:
					Container.RegisterType<IEetTrzbaAlgorithm, V2Algorithm>();
					break;
				default:
					throw ExcUtils.ArgumentOutOfRange(nameof(config.AlgorithmType), config.AlgorithmType);
			}

		}
	}
}
