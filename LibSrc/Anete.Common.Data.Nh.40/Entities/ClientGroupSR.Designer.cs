//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Anete.Common.Data.Nh.Entities {
    using System;
    
    
    /// <summary>
    /// A strongly-typed resource class, for looking up localized strings, formatting them, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilderEx class via the ResXFileCodeGeneratorEx custom tool.
    // To add or remove a member, edit your .ResX file then rerun the ResXFileCodeGeneratorEx custom tool or rebuild your VS.NET project.
    // Copyright (c) Dmytro <PERSON> 2006-2020 (http://dmytro.kryvko.googlepages.com/)
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("DMKSoftware.CodeGenerators.Tools.StronglyTypedResourceBuilderEx", "2.9.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Naming", "CA1724:TypeNamesShouldNotMatchNamespaces")]
    public partial class ClientGroupSR {
        
        private static global::System.Resources.ResourceManager _resourceManager;
        
        private static object _internalSyncObject;
        
        private static global::System.Globalization.CultureInfo _resourceCulture;
        
        /// <summary>
        /// Initializes a ClientGroupSR object.
        /// </summary>
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        public ClientGroupSR() {
        }
        
        /// <summary>
        /// Thread safe lock object used by this class.
        /// </summary>
        public static object InternalSyncObject {
            get {
                if (object.ReferenceEquals(_internalSyncObject, null)) {
                    global::System.Threading.Interlocked.CompareExchange(ref _internalSyncObject, new object(), null);
                }
                return _internalSyncObject;
            }
        }
        
        /// <summary>
        /// Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(_resourceManager, null)) {
                    global::System.Threading.Monitor.Enter(InternalSyncObject);
                    try {
                        if (object.ReferenceEquals(_resourceManager, null)) {
                            global::System.Threading.Interlocked.Exchange(ref _resourceManager, new global::System.Resources.ResourceManager("Anete.Common.Data.Nh.Entities.ClientGroupSR", typeof(ClientGroupSR).Assembly));
                        }
                    }
                    finally {
                        global::System.Threading.Monitor.Exit(InternalSyncObject);
                    }
                }
                return _resourceManager;
            }
        }
        
        /// <summary>
        /// Overrides the current thread's CurrentUICulture property for all
        /// resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return _resourceCulture;
            }
            set {
                _resourceCulture = value;
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Typ účtu'.
        /// </summary>
        public static string AccountTemplate {
            get {
                return ResourceManager.GetString(ResourceNames.AccountTemplate, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Aktivní'.
        /// </summary>
        public static string Active {
            get {
                return ResourceManager.GetString(ResourceNames.Active, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Věkové skupiny'.
        /// </summary>
        public static string AgeGroups {
            get {
                return ResourceManager.GetString(ResourceNames.AgeGroups, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Výdejna'.
        /// </summary>
        public static string Canteen {
            get {
                return ResourceManager.GetString(ResourceNames.Canteen, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Omezení výdejny'.
        /// </summary>
        public static string CanteenAccessCategory {
            get {
                return ResourceManager.GetString(ResourceNames.CanteenAccessCategory, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Skupina klienta'.
        /// </summary>
        public static string ClientGroup_Desc {
            get {
                return ResourceManager.GetString(ResourceNames.ClientGroup_Desc, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Id'.
        /// </summary>
        public static string ClientGroupId {
            get {
                return ResourceManager.GetString(ResourceNames.ClientGroupId, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to '#ClientRegisterHistories#'.
        /// </summary>
        public static string ClientRegisterHistories {
            get {
                return ResourceManager.GetString(ResourceNames.ClientRegisterHistories, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to '#ClientRegisters#'.
        /// </summary>
        public static string ClientRegisters {
            get {
                return ResourceManager.GetString(ResourceNames.ClientRegisters, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Omezení dní'.
        /// </summary>
        public static string DayAccessCategory {
            get {
                return ResourceManager.GetString(ResourceNames.DayAccessCategory, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Implicitní výdejna'.
        /// </summary>
        public static string DefaultCanteen {
            get {
                return ResourceManager.GetString(ResourceNames.DefaultCanteen, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Implicitní denní počet dotací'.
        /// </summary>
        public static string DefaultSubsidyCountDayLimit {
            get {
                return ResourceManager.GetString(ResourceNames.DefaultSubsidyCountDayLimit, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Počet bonů'.
        /// </summary>
        public static string DefaultVoucherCount {
            get {
                return ResourceManager.GetString(ResourceNames.DefaultVoucherCount, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'DPH pro zálohy'.
        /// </summary>
        public static string DepositVatId {
            get {
                return ResourceManager.GetString(ResourceNames.DepositVatId, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Popis skupiny'.
        /// </summary>
        public static string Description {
            get {
                return ResourceManager.GetString(ResourceNames.Description, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Omezení sady'.
        /// </summary>
        public static string DeviceControllerSetAccessCategory {
            get {
                return ResourceManager.GetString(ResourceNames.DeviceControllerSetAccessCategory, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Dietní'.
        /// </summary>
        public static string Dietary {
            get {
                return ResourceManager.GetString(ResourceNames.Dietary, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'EET povoleno'.
        /// </summary>
        public static string EetEnabled {
            get {
                return ResourceManager.GetString(ResourceNames.EetEnabled, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Elektronická účtenka'.
        /// </summary>
        public static string ElectronicReceiptEnabled {
            get {
                return ResourceManager.GetString(ResourceNames.ElectronicReceiptEnabled, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'GDPR neplatný souhlas'.
        /// </summary>
        public static string IsAnonymous {
            get {
                return ResourceManager.GetString(ResourceNames.IsAnonymous, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to '#KMigrace#'.
        /// </summary>
        public static string KMigrace {
            get {
                return ResourceManager.GetString(ResourceNames.KMigrace, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to '#Mapagrafu#'.
        /// </summary>
        public static string Mapagrafu {
            get {
                return ResourceManager.GetString(ResourceNames.Mapagrafu, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Povolené kombinace jídel'.
        /// </summary>
        public static string MealRestriction {
            get {
                return ResourceManager.GetString(ResourceNames.MealRestriction, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Stravní limit'.
        /// </summary>
        public static string MealSize {
            get {
                return ResourceManager.GetString(ResourceNames.MealSize, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Omezení jídelníčku'.
        /// </summary>
        public static string MenuCategory {
            get {
                return ResourceManager.GetString(ResourceNames.MenuCategory, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Omezení měsíců'.
        /// </summary>
        public static string MonthRestrictions {
            get {
                return ResourceManager.GetString(ResourceNames.MonthRestrictions, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Název'.
        /// </summary>
        public static string Name {
            get {
                return ResourceManager.GetString(ResourceNames.Name, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Poznámka'.
        /// </summary>
        public static string Note {
            get {
                return ResourceManager.GetString(ResourceNames.Note, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to '#OSUMagionKontaceSluzba1s#'.
        /// </summary>
        public static string OSUMagionKontaceSluzba1s {
            get {
                return ResourceManager.GetString(ResourceNames.OSUMagionKontaceSluzba1s, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Povolit zobrazení bonů'.
        /// </summary>
        public static string PovolZobrazenibonu {
            get {
                return ResourceManager.GetString(ResourceNames.PovolZobrazenibonu, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Cenová kategorie'.
        /// </summary>
        public static string PriceCategory {
            get {
                return ResourceManager.GetString(ResourceNames.PriceCategory, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Primární kategorie dotace'.
        /// </summary>
        public static string PrimarySubsidyCategory {
            get {
                return ResourceManager.GetString(ResourceNames.PrimarySubsidyCategory, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Provozovatel skupina'.
        /// </summary>
        public static string ProviderGroupCategories {
            get {
                return ResourceManager.GetString(ResourceNames.ProviderGroupCategories, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Sekundární kategorie dotace'.
        /// </summary>
        public static string SecondarySubsidyCategory {
            get {
                return ResourceManager.GetString(ResourceNames.SecondarySubsidyCategory, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Typ účtu'.
        /// </summary>
        public static string TypUctu {
            get {
                return ResourceManager.GetString(ResourceNames.TypUctu, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Jídlo zdarma'.
        /// </summary>
        public static string UnissuedSecSubsidy {
            get {
                return ResourceManager.GetString(ResourceNames.UnissuedSecSubsidy, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Zobrazit bony'.
        /// </summary>
        public static string VouchersAreVisible {
            get {
                return ResourceManager.GetString(ResourceNames.VouchersAreVisible, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Lists all the resource names as constant string fields.
        /// </summary>
        public class ResourceNames {
            
            /// <summary>
            /// Stores the resource name 'AccountTemplate'.
            /// </summary>
            public const string AccountTemplate = "AccountTemplate";
            
            /// <summary>
            /// Stores the resource name 'Active'.
            /// </summary>
            public const string Active = "Active";
            
            /// <summary>
            /// Stores the resource name 'AgeGroups'.
            /// </summary>
            public const string AgeGroups = "AgeGroups";
            
            /// <summary>
            /// Stores the resource name 'Canteen'.
            /// </summary>
            public const string Canteen = "Canteen";
            
            /// <summary>
            /// Stores the resource name 'CanteenAccessCategory'.
            /// </summary>
            public const string CanteenAccessCategory = "CanteenAccessCategory";
            
            /// <summary>
            /// Stores the resource name 'ClientGroup_Desc'.
            /// </summary>
            public const string ClientGroup_Desc = "ClientGroup_Desc";
            
            /// <summary>
            /// Stores the resource name 'ClientGroupId'.
            /// </summary>
            public const string ClientGroupId = "ClientGroupId";
            
            /// <summary>
            /// Stores the resource name 'ClientRegisterHistories'.
            /// </summary>
            public const string ClientRegisterHistories = "ClientRegisterHistories";
            
            /// <summary>
            /// Stores the resource name 'ClientRegisters'.
            /// </summary>
            public const string ClientRegisters = "ClientRegisters";
            
            /// <summary>
            /// Stores the resource name 'DayAccessCategory'.
            /// </summary>
            public const string DayAccessCategory = "DayAccessCategory";
            
            /// <summary>
            /// Stores the resource name 'DefaultCanteen'.
            /// </summary>
            public const string DefaultCanteen = "DefaultCanteen";
            
            /// <summary>
            /// Stores the resource name 'DefaultSubsidyCountDayLimit'.
            /// </summary>
            public const string DefaultSubsidyCountDayLimit = "DefaultSubsidyCountDayLimit";
            
            /// <summary>
            /// Stores the resource name 'DefaultVoucherCount'.
            /// </summary>
            public const string DefaultVoucherCount = "DefaultVoucherCount";
            
            /// <summary>
            /// Stores the resource name 'DepositVatId'.
            /// </summary>
            public const string DepositVatId = "DepositVatId";
            
            /// <summary>
            /// Stores the resource name 'Description'.
            /// </summary>
            public const string Description = "Description";
            
            /// <summary>
            /// Stores the resource name 'DeviceControllerSetAccessCategory'.
            /// </summary>
            public const string DeviceControllerSetAccessCategory = "DeviceControllerSetAccessCategory";
            
            /// <summary>
            /// Stores the resource name 'Dietary'.
            /// </summary>
            public const string Dietary = "Dietary";
            
            /// <summary>
            /// Stores the resource name 'EetEnabled'.
            /// </summary>
            public const string EetEnabled = "EetEnabled";
            
            /// <summary>
            /// Stores the resource name 'ElectronicReceiptEnabled'.
            /// </summary>
            public const string ElectronicReceiptEnabled = "ElectronicReceiptEnabled";
            
            /// <summary>
            /// Stores the resource name 'IsAnonymous'.
            /// </summary>
            public const string IsAnonymous = "IsAnonymous";
            
            /// <summary>
            /// Stores the resource name 'KMigrace'.
            /// </summary>
            public const string KMigrace = "KMigrace";
            
            /// <summary>
            /// Stores the resource name 'Mapagrafu'.
            /// </summary>
            public const string Mapagrafu = "Mapagrafu";
            
            /// <summary>
            /// Stores the resource name 'MealRestriction'.
            /// </summary>
            public const string MealRestriction = "MealRestriction";
            
            /// <summary>
            /// Stores the resource name 'MealSize'.
            /// </summary>
            public const string MealSize = "MealSize";
            
            /// <summary>
            /// Stores the resource name 'MenuCategory'.
            /// </summary>
            public const string MenuCategory = "MenuCategory";
            
            /// <summary>
            /// Stores the resource name 'MonthRestrictions'.
            /// </summary>
            public const string MonthRestrictions = "MonthRestrictions";
            
            /// <summary>
            /// Stores the resource name 'Name'.
            /// </summary>
            public const string Name = "Name";
            
            /// <summary>
            /// Stores the resource name 'Note'.
            /// </summary>
            public const string Note = "Note";
            
            /// <summary>
            /// Stores the resource name 'OSUMagionKontaceSluzba1s'.
            /// </summary>
            public const string OSUMagionKontaceSluzba1s = "OSUMagionKontaceSluzba1s";
            
            /// <summary>
            /// Stores the resource name 'PovolZobrazenibonu'.
            /// </summary>
            public const string PovolZobrazenibonu = "PovolZobrazenibonu";
            
            /// <summary>
            /// Stores the resource name 'PriceCategory'.
            /// </summary>
            public const string PriceCategory = "PriceCategory";
            
            /// <summary>
            /// Stores the resource name 'PrimarySubsidyCategory'.
            /// </summary>
            public const string PrimarySubsidyCategory = "PrimarySubsidyCategory";
            
            /// <summary>
            /// Stores the resource name 'ProviderGroupCategories'.
            /// </summary>
            public const string ProviderGroupCategories = "ProviderGroupCategories";
            
            /// <summary>
            /// Stores the resource name 'SecondarySubsidyCategory'.
            /// </summary>
            public const string SecondarySubsidyCategory = "SecondarySubsidyCategory";
            
            /// <summary>
            /// Stores the resource name 'TypUctu'.
            /// </summary>
            public const string TypUctu = "TypUctu";
            
            /// <summary>
            /// Stores the resource name 'UnissuedSecSubsidy'.
            /// </summary>
            public const string UnissuedSecSubsidy = "UnissuedSecSubsidy";
            
            /// <summary>
            /// Stores the resource name 'VouchersAreVisible'.
            /// </summary>
            public const string VouchersAreVisible = "VouchersAreVisible";
        }
    }
}
