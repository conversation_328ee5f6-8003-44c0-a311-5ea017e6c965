using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Anete.Common.Data.Interface.Enums
{

	/// <summary>
	/// Id udalosti Kredit z tabulky SCC_Udalosti
	/// </summary>
	public enum KreditEventId
	{

		/// <summary>
		/// Eet certifikát bude expirovat
		/// </summary>
		EetCertificateExpiring = 15,
		/// <summary>
		/// Eet certifikát nebyl nalezen
		/// </summary>
		EetCertificateNotFound = 16,
		/// <summary>
		/// Eet log obsahuje zaznamy ve stavu, o kterem je treba informovat. Bud zaznamy ve stavu vazna chyba, nebo zaznamy, ktere
		/// nebyly 2 dny odeslany
		/// </summary>
		EetLogStateWarning = 17,

		/// <summary>
		/// HMAC nebo ECDSA v odpovědi z platební brány CardPay není validní
		/// </summary>
		CardPaySignatureNotValid = 19,

	}
}
