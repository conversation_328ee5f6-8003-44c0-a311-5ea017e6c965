using System;
using System.Collections.Generic;
using System.Linq;
using Anete.Common.Data.Interface.Business.AccessRights.Parameters;
using Unity;

namespace Anete.Common.Data.AccessRights.Services.AcessRightParameters
{
	/// <summary>
	/// Enumerace vsech dostupnych sluzeb pro repository s parametry pristupovych prav pro dany WorkItem.
	/// </summary>
	public class AccessRightParameterRepositoryEnumerator : IAccessRightParameterRepositoryEnumerator, IAccessRightParameterRepositoryEnumeratorConfig
	{
		#region private fields...
		private readonly IUnityContainer _unityContainer;
		private readonly IList<Type> _exludedRepositoryForEditableParameters = new List<Type>();
		#endregion

		#region constructors...
		/// <summary>
		/// Initializes a new instance of the WorkItemAccessRightParameterRepositoryEnumerator class.
		/// </summary>
		public AccessRightParameterRepositoryEnumerator(IUnityContainer unityContainer)
		{
			_unityContainer = unityContainer;
		}
		#endregion
		
		#region public properties...
		public void ExcludeRepositoryForEditableParameters(Type repositoryType)
		{
			_exludedRepositoryForEditableParameters.Add(repositoryType);
		}
		#endregion

		#region IAccessRightParameterRepositoryEnumerator Members

		public IEnumerable<IAccessRightParameterEntityRepository> AvailableRepositories
		{
			get
			{
				foreach (IAccessRightParameterEntityRepository item in _unityContainer.ResolveAll<IAccessRightParameterEntityRepository>())
				{
					yield return item;
				}
			}
		}

		public IEnumerable<IAccessRightParameterEntityRepository> RepositoriesForEditableParameters
		{
			get
			{
				foreach (IAccessRightParameterEntityRepository item in AvailableRepositories
					.Where(r => !_exludedRepositoryForEditableParameters.Any(t => t.IsAssignableFrom(r.GetType()))))
				{
					yield return item;
				}
			}
		}

		#endregion
	}
}
