using System;
using Anete.Common.Core.Entities;
using Anete.Utils.Extensions;

using System.Runtime.Serialization;
using Anete.Resources;

namespace Anete.Common.Data.Business.MealServing
{
	/// <summary>
	/// Jednoznacne urceni jidla doplnene o jeho velikost.
	/// Pouzije se v pripade, kdy je potreba u jidla rozlisovat i jeho velikost.
	/// </summary>
	[DataContract(Namespace = AneteNamespace.DefaultNamespace)]
	public class MealWithSize : Meal
	{
		#region constructors...				
		public MealWithSize(Meal meal, MealSize mealSize, DateTime? serveTime = null) : this(meal.MealAlt, meal.MealKind, mealSize, serveTime)
		{
		}
		public MealWithSize(MealAlt mealAlt, MealKind mealKind, MealSize mealSize, DateTime? serveTime)
			: base(mealAlt, mealKind)
		{
			MealSize = mealSize;
			ServeTime = serveTime;

		}
		#endregion

		/// <summary>
		/// Velikost jidla
		/// </summary>
		[DataMember]
		public MealSize MealSize { get; private set; }

		/// <summary>
		/// Druh jidla, kteremu alternativa prislusi.
		/// </summary>
		[DataMember]
		public DateTime? ServeTime { get; private set; }


		#region public overrides...				
		public override int GetHashCode()
		{
			return base.GetHashCode() ^ MealSize.GetHashCode();
		}
	   
		public override bool Equals(object obj)
		{
			if (!(obj is MealWithSize))
			{
				return false;
			}

			if (!base.Equals(obj))
			{
				return false;
			}

			MealWithSize meal = (MealWithSize)obj;
			return meal.MealSize.Equals(MealSize);
		}
		
		public override string ToString()
		{
			return this.ToReportString();  
		}
		#endregion
	}
}
