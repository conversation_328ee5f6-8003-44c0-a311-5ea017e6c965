//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate Fluent Mapping template.
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;
using FluentNHibernate.Mapping;
using FluentNHibernate.MappingModel.Collections;

namespace Anete.Common.Data.Nh.Entities
{
  public partial class UsPokojVyclenenaKapacita
  {
               /// <summary>
		       /// Nastavi novou hodnotu navigacni property se zachovanim konzistentnich udaju ve vazebnich kolekcich
		       /// </summary>
           public virtual void SetUsPokoj(UsPokoj value)
           {
              UsPokoj oldValue = UsPokoj;
              UsPokoj = value;

              // uprava vazby
              if (value != null)
              {
                value.UsPokojVyclenenaKapacitas.Add(this);
              }
              if (oldValue != null)
              {
                oldValue.UsPokojVyclenenaKapacitas.Remove(this);
              }
           }
                      /// <summary>
		       /// Nastavi novou hodnotu navigacni property se zachovanim konzistentnich udaju ve vazebnich kolekcich
		       /// </summary>
           public virtual void SetUsVyclenenaKapacita(UsVyclenenaKapacita value)
           {
              UsVyclenenaKapacita oldValue = UsVyclenenaKapacita;
              UsVyclenenaKapacita = value;

              // uprava vazby
              if (value != null)
              {
                value.UsPokojVyclenenaKapacitas.Add(this);
              }
              if (oldValue != null)
              {
                oldValue.UsPokojVyclenenaKapacitas.Remove(this);
              }
           }
             }
}
