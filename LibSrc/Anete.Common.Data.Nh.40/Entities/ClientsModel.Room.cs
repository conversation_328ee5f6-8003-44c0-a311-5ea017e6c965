//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Anete.Common.Data.Nh.Entities
{

    /// <summary>
    /// Tabulka: dba.UbytovaniS
    /// </summary>
    public partial class Room : Anete.Data.Nh.NhEntityBase, INotifyPropertyChanging, INotifyPropertyChanged {

        private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(System.String.Empty);

        private short _RoomId;

        private string _Code;

        private string _Name;

        private Floor _Floor;

        private ISet<SvoClientRegisterExt> _SvoClientRegisterExts;
    
        #region Extensibility Method Definitions
        
        partial void OnCreated();

        public override bool Equals(object obj)
        {
          Room toCompare = obj as Room;
          if (toCompare == null)
          {
            return false;
          }

          if (!Object.Equals(this.RoomId, toCompare.RoomId))
            return false;
         // pokud mam default primary key, znamena to, ze jedna o nove vytvorenou entitu, ktera jeste nema key prirazen. 
        // V takove pripade musim porovnat podle instance
        bool isDefault = true;
        if (this.RoomId != default(short))
        {
          isDefault = false;
        }
     

          if (isDefault)
          {
            return object.ReferenceEquals(toCompare, this);
          }     
          return true;
        }

	private int? _hashCode = null;
        public override int GetHashCode()
        {
	if (_hashCode == null)
          {
          _hashCode = 13;
          _hashCode = (_hashCode * 7) + RoomId.GetHashCode();
}
          return _hashCode.Value;
        }
        partial void OnRoomIdChanging(short value);
        
        partial void OnRoomIdChanged();
        partial void OnCodeChanging(string value);
        
        partial void OnCodeChanged();
        partial void OnNameChanging(string value);
        
        partial void OnNameChanged();
        partial void OnFloorChanging(Floor value);

        partial void OnFloorChanged();
        
        #endregion
        public Room()
        {
            this._SvoClientRegisterExts = new HashSet<SvoClientRegisterExt>();
            OnCreated();
        }

    
        /// <summary>
        /// Sloupec: id_ubytovaniS
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("RoomId", typeof(RoomSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(Room), Tag="RoomId")]
        public virtual short RoomId
        {
            get
            {
                return this._RoomId;
            }
            set
            {
                if (this._RoomId != value)
                {
                    this.OnRoomIdChanging(value);
                    this.SendPropertyChanging();
                    this._RoomId = value;
                    this.SendPropertyChanged("RoomId");
                    this.OnRoomIdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: kod
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Code", typeof(RoomSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(Room), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 5,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="Code")]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(Room), Tag="Code")]
        public virtual string Code
        {
            get
            {
                return this._Code;
            }
            set
            {
                if (this._Code != value)
                {
                    this.OnCodeChanging(value);
                    this.SendPropertyChanging();
                    this._Code = value;
                    this.SendPropertyChanged("Code");
                    this.OnCodeChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: popis
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Name", typeof(RoomSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(Room), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 50,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="Name")]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(Room), Tag="Name")]
        public virtual string Name
        {
            get
            {
                return this._Name;
            }
            set
            {
                if (this._Name != value)
                {
                    this.OnNameChanging(value);
                    this.SendPropertyChanging();
                    this._Name = value;
                    this.SendPropertyChanged("Name");
                    this.OnNameChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: id_ubytovaniM
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Floor", typeof(RoomSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual Floor Floor
        {
            get
            {
                return this._Floor;
            }
            set
            {
                if (this._Floor != value)
                {
                    this.OnFloorChanging(value);
                    this.SendPropertyChanging();
                    this._Floor = value;
                    this.SendPropertyChanged("Floor");
                    this.OnFloorChanged();
                }
            }
        }

        [Anete.Data.Attributes.EntitySRDisplayName("SvoClientRegisterExts", typeof(RoomSR))]
        [Anete.Data.Attributes.DbCollectionProperty]
        public virtual ISet<SvoClientRegisterExt> SvoClientRegisterExts
        {
            get
            {
                return this._SvoClientRegisterExts;
            }
            set
            {
                this._SvoClientRegisterExts = value;
            }
        }
   
        public virtual event PropertyChangingEventHandler PropertyChanging;

	// Mam v bazove tride, nema smysl generovat
	// public virtual event PropertyChangedEventHandler PropertyChanged;

        protected virtual void SendPropertyChanging()
        {
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, emptyChangingEventArgs);
        }

        protected virtual void SendPropertyChanging(System.String propertyName) 
        {   	
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, new PropertyChangingEventArgs(propertyName));
        }

        protected virtual void SendPropertyChanged(System.String propertyName)
        {    
		// mam v bazove tride, proto musim volat takto jednoduse
		OnPropertyChanged(propertyName);
/*		        var handler = this.PropertyChanged;
            if (handler != null)
                handler(this, new PropertyChangedEventArgs(propertyName));*/
        }
    }

}
