using System;
using System.Collections.Generic;
using System.Linq;
using Anete.Utils.Collections.Tree;

namespace Anete.Utils.Patterns.AppActions
{
    /// <summary>
    /// Bazova implementace IUiRepositoryItem, ktera umoznuje pridavat polozky
    /// </summary>
    public abstract class ModifiableUiRepositoryItemBase<TBuildKey> : UiRepositoryItemBase<TBuildKey>, IModifiableUiRepositoryItem
    {
        private List<IUiRepositoryItem> _items = new List<IUiRepositoryItem>();

        #region constructors...
        /// <summary>
        /// Initializes a new instance of the ModifiableUiRepositoryItemBase class.
        /// </summary>
        /// <param name="name">The name.</param>
        /// <param name="parent">The parent.</param>
		public ModifiableUiRepositoryItemBase(string name, IUiRepositoryItem parent)
            : base(name, parent)
        {

        }
        #endregion

        #region IModifiableTreeNode Members
        /// <summary>
        /// Prida k uzlu novy podrizeny uzel
        /// </summary>
        /// <param name="child"></param>
        public void AddChild(IBasicTreeNode child)
        {
            _items.Add((IUiRepositoryItem)child);

            OnChildAdded((IUiRepositoryItem)child);
        }

        /// <summary>
        /// Odstraneni uzlu
        /// </summary>
        /// <param name="child"></param>
        public void RemoveChild(IBasicTreeNode child)
        {
            if (!_items.Remove((IUiRepositoryItem)child))
            {
                throw new ArgumentException(string.Format("Repository item {0} nen� sou��st� kolekce", child));
            }

            OnChildRemoved((IUiRepositoryItem)child);
        }

        /// <summary>
        /// Event vyvolany pri pridani childu
        /// </summary>
        public event EventHandler<TreeNodeEventArgs<IBasicTreeNode>> ChildAdded;

        /// <summary>
        /// Event vyvolany pri odstraneni childu
        /// </summary>
        public event EventHandler<TreeNodeEventArgs<IBasicTreeNode>> ChildRemoved;
        #endregion       

        #region protected virtual methods...
        /// <summary>
        /// Called when [child added].
        /// </summary>
        /// <param name="item">The item.</param>
        protected virtual void OnChildAdded(IUiRepositoryItem item)
        {
            if (ChildAdded != null)
            {
                ChildAdded(this, new TreeNodeEventArgs<IBasicTreeNode>(item));
            }
        }

        /// <summary>
        /// Called when [child removed].
        /// </summary>
        /// <param name="item">The item.</param>
        protected virtual void OnChildRemoved(IUiRepositoryItem item)
        {
            if (ChildRemoved != null)
            {
                ChildRemoved(this, new TreeNodeEventArgs<IBasicTreeNode>(item));
            }
        }
        #endregion

        #region protected overrides...
        /// <summary>
        /// Vraci seznam vsech child uzlu
        /// </summary>
        /// <returns></returns>
        protected override IEnumerable<IBasicTreeNode> GetChilds()
        {
            foreach (IUiRepositoryItem item in _items)
            {
                yield return item;
            }            
        }

		/// <summary>
		/// Tato metoda muze uvolnit vsechny spravovane prostredky
		/// </summary>
		/// <remarks>Je nutno prepisovat pouze pokud napr. objekt alokuje velke objemy pameti</remarks>
		protected override void DisposeManagedResources()
		{
			base.DisposeManagedResources();

			// Childy mohou byt potencionalne IDisposable, musim se tedy postarat o jejich uvolneni
			foreach (IBasicTreeNode basicTreeNode in Childs)
			{
				IDisposable disposeableTreeNode = basicTreeNode as IDisposable;
				if (disposeableTreeNode != null)
				{				
					disposeableTreeNode.Dispose();
				}
			}
		}
        #endregion
    }
}
