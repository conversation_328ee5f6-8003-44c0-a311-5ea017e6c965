using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Anete.Utils.ComponentModel
{

	/// <summary>
	/// Polozka pouzivana ke zobrazeni dne v tydnu v seznamu, CheckComboBoxu, LookupEdit atd. 
	/// Hodnotou je primo cislo ulozene v databazi. Pouziva se proto, ze pokud potrebuji editovat data v gridu pomoci LookupEdit, 
	/// pak pokud pouziji DayOfWeek, bude grid spatne serazeny - na prvnim miste nedele.
	/// </summary>
	public class DbDayOfWeekItem : ValueNameItem<int>
	{
		
		/// <summary>
		/// Initializes a new instance
		/// </summary>
		/// <param name="value">Hodnota</param>
		/// <param name="name">Popis</param>
		public DbDayOfWeekItem(int value, string name)
			: base(value, name)
		{
			
		}
		
	}
}
