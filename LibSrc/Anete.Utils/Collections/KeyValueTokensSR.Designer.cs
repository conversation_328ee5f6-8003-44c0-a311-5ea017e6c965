//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Anete.Utils.Collections {
    using System;
    
    
    /// <summary>
    /// A strongly-typed resource class, for looking up localized strings, formatting them, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilderEx class via the ResXFileCodeGeneratorEx custom tool.
    // To add or remove a member, edit your .ResX file then rerun the ResXFileCodeGeneratorEx custom tool or rebuild your VS.NET project.
    // Copyright (c) Dmy<PERSON> 2006-2018 (http://dmytro.kryvko.googlepages.com/)
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("DMKSoftware.CodeGenerators.Tools.StronglyTypedResourceBuilderEx", "2.6.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Naming", "CA1724:TypeNamesShouldNotMatchNamespaces")]
    public partial class KeyValueTokensSR {
        
        private static global::System.Resources.ResourceManager _resourceManager;
        
        private static object _internalSyncObject;
        
        private static global::System.Globalization.CultureInfo _resourceCulture;
        
        /// <summary>
        /// Initializes a KeyValueTokensSR object.
        /// </summary>
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        public KeyValueTokensSR() {
        }
        
        /// <summary>
        /// Thread safe lock object used by this class.
        /// </summary>
        public static object InternalSyncObject {
            get {
                if (object.ReferenceEquals(_internalSyncObject, null)) {
                    global::System.Threading.Interlocked.CompareExchange(ref _internalSyncObject, new object(), null);
                }
                return _internalSyncObject;
            }
        }
        
        /// <summary>
        /// Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(_resourceManager, null)) {
                    global::System.Threading.Monitor.Enter(InternalSyncObject);
                    try {
                        if (object.ReferenceEquals(_resourceManager, null)) {
                            global::System.Threading.Interlocked.Exchange(ref _resourceManager, new global::System.Resources.ResourceManager("Anete.Utils.Collections.KeyValueTokensSR", typeof(KeyValueTokensSR).Assembly));
                        }
                    }
                    finally {
                        global::System.Threading.Monitor.Exit(InternalSyncObject);
                    }
                }
                return _resourceManager;
            }
        }
        
        /// <summary>
        /// Overrides the current thread's CurrentUICulture property for all
        /// resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return _resourceCulture;
            }
            set {
                _resourceCulture = value;
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Chybný formát řetězce &apos;{0}&apos;. Musí být ve formátu Klíč=Hodnota'.
        /// </summary>
        public static string InvalidKeyValueString {
            get {
                return ResourceManager.GetString(ResourceNames.InvalidKeyValueString, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Klíč &apos;{0}&apos; už existuje.'.
        /// </summary>
        public static string KeyAlreadyExists {
            get {
                return ResourceManager.GetString(ResourceNames.KeyAlreadyExists, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Klíč &apos;{0}&apos; nebyl nalezen.'.
        /// </summary>
        public static string KeyNotFound {
            get {
                return ResourceManager.GetString(ResourceNames.KeyNotFound, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Pár klíč-hodnota musí obsahovat přesně jeden oddělovač.'.
        /// </summary>
        public static string ValueDelimiterInvalidCount {
            get {
                return ResourceManager.GetString(ResourceNames.ValueDelimiterInvalidCount, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Chybný formát řetězce &apos;{0}&apos;. Musí být ve formátu Klíč=Hodnota'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string InvalidKeyValueStringFormat(object arg0) {
            return string.Format(_resourceCulture, InvalidKeyValueString, arg0);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Klíč &apos;{0}&apos; už existuje.'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string KeyAlreadyExistsFormat(object arg0) {
            return string.Format(_resourceCulture, KeyAlreadyExists, arg0);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Klíč &apos;{0}&apos; nebyl nalezen.'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string KeyNotFoundFormat(object arg0) {
            return string.Format(_resourceCulture, KeyNotFound, arg0);
        }
        
        /// <summary>
        /// Lists all the resource names as constant string fields.
        /// </summary>
        public class ResourceNames {
            
            /// <summary>
            /// Stores the resource name 'InvalidKeyValueString'.
            /// </summary>
            public const string InvalidKeyValueString = "InvalidKeyValueString";
            
            /// <summary>
            /// Stores the resource name 'KeyAlreadyExists'.
            /// </summary>
            public const string KeyAlreadyExists = "KeyAlreadyExists";
            
            /// <summary>
            /// Stores the resource name 'KeyNotFound'.
            /// </summary>
            public const string KeyNotFound = "KeyNotFound";
            
            /// <summary>
            /// Stores the resource name 'ValueDelimiterInvalidCount'.
            /// </summary>
            public const string ValueDelimiterInvalidCount = "ValueDelimiterInvalidCount";
        }
    }
}
