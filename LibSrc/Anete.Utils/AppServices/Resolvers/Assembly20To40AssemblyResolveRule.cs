using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using System.Reflection;

namespace Anete.Utils.AppServices.Resolvers
{
    /// <summary>
    /// AssemblyResolveRule pri pokusu o nacteni assembly bez suffixem pri existenci assembly ve verzi 35.
    /// Resi problem, kdy aplikace vyuziva 20 knihovny a deserializuje se 35 knihovna.
    /// </summary>
    public class Assembly20To40AssemblyResolveRule : AssemblyResolveRuleBase
    {
        /// <summary>
        /// Initializes a new instance of the Assembly20To35AssemblyResolveRule class.
        /// </summary>
        public Assembly20To40AssemblyResolveRule()
            : base(0)
        {

        }

        /// <summary>
        /// Pro dany typ a assembly vrati assembly, v ktere je typ umisten.
        /// </summary>
        /// <param name="assemblyName"></param>
        /// <param name="typeName"></param>
        /// <returns>
        /// Seznam nazvu assembly, pro ktere bylo nalezeno pravidlo
        /// </returns>
        protected override IEnumerable<string> ResolveAssemblyInt(string assemblyName, string typeName)
        {
            if (string.IsNullOrEmpty(assemblyName) || assemblyName.EndsWith(".40"))
            {
                yield break;
            }

            string[] assemblyParts = assemblyName.Split(new char[] { ',' });

			if (assemblyParts[0].EndsWith(".40"))
			{
				yield break;
			}

			assemblyParts[0] = assemblyParts[0] + ".40";
            yield return string.Join(",", assemblyParts);
        }

        /// <summary>
        /// Inicializace
        /// </summary>
        protected override void InitializeInt()
        {
            string assemblyName = Assembly.GetExecutingAssembly().GetName().Name;
            if (!assemblyName.EndsWith(".40"))
            {
                throw new InvalidOperationException(string.Format("ResolveRule {0} lze pouzit pouze u .NET 4.0 projektu", GetType()));
            }
        }
    }
}
