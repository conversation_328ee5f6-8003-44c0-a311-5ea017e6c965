//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate Fluent Mapping template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;
using FluentNHibernate.Mapping;
using FluentNHibernate.MappingModel.Collections;

namespace Anete.Common.Data.Nh.Entities
{
    /// <summary>
    /// There are no comments for ProfitAccountCodingMap in the schema.
    /// </summary>
    public partial class ProfitAccountCodingMap : ClassMap<ProfitAccountCoding>
    {
        /// <summary>
        /// There are no comments for ProfitAccountCodingMap constructor in the schema.
        /// </summary>
        public ProfitAccountCodingMap()
        {
              Schema(@"dbo");
              Table(@"CAH_KontaceVynosu");
              DynamicInsert();
              DynamicUpdate();
              LazyLoad();
              Id(x => x.ProfitAccountCodingId)
                .Column("Id")
                .CustomType("Guid")
                .Access.Property()
                .Default(new Guid()).CustomSqlType("uniqueidentifier")
                .Not.Nullable()                
                .GeneratedBy.Guid();
              Map(x => x.Type)    
                .Column("Typ")
                .CustomType("String")
                .Access.Property()
                .Generated.Never().CustomSqlType("varchar(10)")
                .Not.Nullable()
                .Length(10);
              Map(x => x.Resolution)    
                .Column("Rozliseni")
                .CustomType("String")
                .Access.Property()
                .Generated.Never().CustomSqlType("varchar(10)")
                .Length(10);
              Map(x => x.Condition)    
                .Column("Podminka")
                .CustomType("StringClob")
                .Access.Property()
                .Generated.Never().CustomSqlType("xml");
              Map(x => x.Part)    
                .Column("Strana")
                .CustomType("String")
                .Access.Property()
                .Generated.Never().CustomSqlType("char(1)")
                .Not.Nullable()
                .Length(1);
              Map(x => x.Account)    
                .Column("Ucet")
                .CustomType("String")
                .Access.Property()
                .Generated.Never().CustomSqlType("char(6)")
                .Not.Nullable()
                .Length(6);
              Map(x => x.Resort)    
                .Column("Stredisko")
                .CustomType("String")
                .Access.Property()
                .Generated.Never().CustomSqlType("varchar(50)")
                .Not.Nullable()
                .Length(50);
              Map(x => x.ValidSince)    
                .Column("PlatiOd")
                .CustomType("DateTime")
                .Access.Property()
                .Generated.Never().CustomSqlType("date")
                .Not.Nullable();
              Map(x => x.ValidUntil)    
                .Column("PlatiDo")
                .CustomType("DateTime")
                .Access.Property()
                .Generated.Never().CustomSqlType("date")
                .Not.Nullable();
              Map(x => x.Note)    
                .Column("Poznamka")
                .CustomType("String")
                .Access.Property()
                .Generated.Never().CustomSqlType("varchar(250)")
                .Length(250);
              ExtendMapping();
        }

        #region Partial Methods

        partial void ExtendMapping();

        #endregion
    }

}
