using System;
using System.Linq;
using Microsoft.Practices.EnterpriseLibrary.Validation;
using System.Collections;
using System.Collections.Generic;
using Microsoft.Practices.EnterpriseLibrary.Validation.Validators;
using Anete.Common.Core.Interface.Validators;
using Anete.Common.Data.AppUtils;
using Anete.Common.Data.Interface;

namespace Anete.Common.Data.Validators
{
    /// <summary>
    /// Kontrola velikosti obrazku vzhledem k moznosti ulozit jej na replikovany server.
    /// </summary>
    public class PictureReplicationSizeValidator : CollectionMaximumLengthValidator
    {
		readonly bool _isReplicatedServer;

        /// <summary>
        /// Initializes a new instance of the PictureReplicationSizeValidator class.
        /// </summary>
        public PictureReplicationSizeValidator()
            : base(65535)
        {	
			_isReplicatedServer = CentralServerUtils.IsReplicatedServer;
			if (_isReplicatedServer)
			{
				// musim nastavit maximalni velikost dle konfigurace serveru
				MaxLength = CentralServerUtils.MaxTextReplSize;
			}
        }

        /// <summary>
        /// Gets the default message template.
        /// </summary>
        /// <value>The default message template.</value>
        protected override string DefaultMessageTemplate
        {
            get
            {
                return PictureReplicationSizeValidatorSR.MaximumPictureSizeFormat(MaxLength / 1024);
            }
        }

        public override void DoValidate(object objectToValidate, object currentTarget, string key, Microsoft.Practices.EnterpriseLibrary.Validation.ValidationResults validationResults)
        {			
            if (_isReplicatedServer)
            {
                base.DoValidate(objectToValidate, currentTarget, key, validationResults);
            }            
        }
    }
}
