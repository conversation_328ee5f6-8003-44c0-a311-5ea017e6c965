//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON>ti<PERSON> Developer tool using NHibernate Fluent Mapping template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;
using FluentNHibernate.Mapping;
using FluentNHibernate.MappingModel.Collections;

namespace Anete.Common.Data.Nh.Entities
{
    /// <summary>
    /// There are no comments for RADSubLoginMap in the schema.
    /// </summary>
    public partial class RADSubLoginMap : ClassMap<RADSubLogin>
    {
        /// <summary>
        /// There are no comments for RADSubLoginMap constructor in the schema.
        /// </summary>
        public RADSubLoginMap()
        {
              Schema(@"dba");
              Table(@"RAD_SubLogin");
              LazyLoad();
              CompositeId()
                .KeyProperty(x => x.ClientId, set => {
                    set.Type("Int32");
                    set.ColumnName("id_lk");
                    set.Access.Property(); } )
                .KeyProperty(x => x.AccountName, set => {
                    set.Type("String");
                    set.ColumnName("AccountName");
                    set.Length(60);
                    set.Access.Property(); } );
              Map(x => x.Passwordhash)    
                .Column("passwordhash")
                .CustomType("String")
                .Access.Property()
                .Generated.Never().CustomSqlType("char(32)")
                .Length(32);
              Map(x => x.Rights)    
                .Column("Rights")
                .CustomType("Int32")
                .Access.Property()
                .Generated.Never()
                .Default(@"0").CustomSqlType("int")
                .Not.Nullable()
                .Precision(10);
              ExtendMapping();
        }

        #region Partial Methods

        partial void ExtendMapping();

        #endregion
    }

}
