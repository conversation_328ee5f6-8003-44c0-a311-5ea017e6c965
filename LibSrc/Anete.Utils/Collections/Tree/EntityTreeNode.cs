using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Anete.Utils.Collections.Tree
{

	/// <summary>
	/// TreeListNode, ktery je spojeny s entitou.
	/// Udaje se nacitaji z entity pomoci delegatu predanych v konstruktoru
	/// </summary>
	/// <typeparam name="TEntity">Typ entity</typeparam>
	public class EntityTreeNode<TEntity> : ModifiableTreeNode, IEntityTreeNode
	{

		/// <summary>
		/// Inicializace nove instance
		/// </summary>
		/// <param name="parent">The parent.</param>
		/// <param name="entity">The entity.</param>
		/// <param name="nameSelector">Delegat pro ziskani Name = identifikace objektu</param>
		/// <param name="captionSelector">Delegat pro ziskani Caption</param>
		/// <param name="descriptionSelector">Delegat pro ziskani Description</param>
		public EntityTreeNode(IReadOnlyTreeNode parent, TEntity entity, Func<TEntity, object> nameSelector,
			Func<TEntity, object> captionSelector, Func<TEntity, object> descriptionSelector = null)
			: base(parent, GetDataByDelegate(nameSelector, entity), GetDataByDelegate(captionSelector, entity), 
				GetDataByDelegate(descriptionSelector, entity, true))
		{
			// Proc jsou delegati Func<TEntity, object>? Protoze potrebuji jako vysledek stringy a takto umim objekt
			// automaticky prevest na string. Nemusim pak pri volani volat (e) => e.Id.ToString() atd.
			Guard.ArgumentNotNull(entity, "entity");

			Entity = entity;
		}

		#region IEntityTreeListNode Members
		object IEntityTreeNode.Entity
		{
			get { return Entity; }
		}
		#endregion

		public TEntity Entity { get; private set; }

		/// <summary>
		/// Nacte data z objektu pomoci prislusneho delegata
		/// </summary>
		private static string GetDataByDelegate(Func<TEntity, object> selector, TEntity entity, bool nullable = false)
		{
			Guard.ArgumentNotNull(entity, "entity");

			if (!nullable)
			{
				Guard.ArgumentNotNull(selector, "selector");
			}

			if (nullable && selector == null)
			{
				return null;
			}
			else
			{
				object obj = selector(entity);
				if (obj == null)
				{
					return null;
				}
				else
				{
					return obj.ToString();
				}
			}
			
		}

	}
}
