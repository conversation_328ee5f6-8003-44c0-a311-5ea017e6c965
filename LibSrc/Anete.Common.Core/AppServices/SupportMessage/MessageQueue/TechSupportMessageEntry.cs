using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;
using Anete.Utils.AppServices.SupportMessage;
using Anete.Log4Net.Core;
using Anete.Utils.Extensions;
using Anete.Utils.AppServices.SupportMessage.MessageQueue;

namespace Anete.Common.Core.AppServices.SupportMessage.MessageQueue
{

	/// <summary>
	/// Data pro praci s TechSupportMessage
	/// </summary>
	[DataContract(Namespace = TechSupportMessageUtils.Namespace)]
	public class TechSupportMessageEntry : ITechSupportMessageEntry
	{

		#region private static fields...
		private static readonly ILogEx _log = LogManagerEx.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
		#endregion

		#region constructors...
		/// <summary>
		/// Vytvori novou instanci
		/// </summary>
		public TechSupportMessageEntry()
		{
			
		}

		/// <summary>
		/// Vytvori novou instanci
		/// </summary>
		public TechSupportMessageEntry(ITechSupportMessage message, MessageSettings messageSettings)
		{
			Message = message;
			MessageSettings = messageSettings;
		}
		#endregion

		#region properties...
		/// <summary>
		/// Zprava
		/// </summary>
		[DataMember]
		public ITechSupportMessage Message { get; set; }

		/// <summary>
		/// Nastaveni pro odesilani
		/// </summary>
		[DataMember]
		public MessageSettings MessageSettings { get; set; }
		#endregion

		#region IMessageWithGuid Members
		/// <summary>
		/// Guid zpravy
		/// </summary>
		public Guid MessageGuid
		{
			get { return Message.MessageGuid; }
			set
			{
				throw new NotImplementedException();
			}
		}

		#endregion

		#region IInitializeRequired Members
		/// <summary>
		/// Zkompletuje hlaseni pred zapisem do databaze
		/// </summary>
		public void Initialize()
		{
			_log.Debug("Initialize()");
			Message.Initialize();
			IsInitialized = true;
		}
		#endregion

		#region IIsInitialized Members
		/// <summary>
		/// Udava, zda byl objekt inicializovan - zda byla zavolana metoda Initialize
		/// </summary>
		/// <value></value>
		public bool IsInitialized { get; private set; }
		#endregion

		#region public methods...
		/// <summary>
		/// Returns a <see cref="System.String"/> that represents this instance.
		/// </summary>
		public override string ToString()
		{
			return this.ToReportString();  
		}
		#endregion

	}
}
