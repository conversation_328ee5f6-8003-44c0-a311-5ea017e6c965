using System;
using System.ComponentModel;

namespace Anete.Config.Configs.Core.Global.Hw
{
    /// <summary>
    /// Terminal Vega 7000 pouzivany na CZU
    /// </summary>
    public class Vega7000SerialDeviceSettings : SerialDeviceSettings
    {
        /// <summary>
        /// Initializes a new instance of the GeneralSerialReaderSettings class.
        /// </summary>
        public Vega7000SerialDeviceSettings()
            : base()
        {
            ReadTimeOut = 500;
        }

        /// <summary>
        /// TimeOut pri cteni ze serioveho portu. 
        /// Prenastavuje se na 500 mSec z nasledujiciho duvodu: Pri placeni pomoci prikazu eWallet.Pay se musi nejprve zastavit
        /// cteci vlakno, ktere periodicky cte z portu. Zastaveni vlakna trva stejne dlouho, jako je tento timeout. Mam tedy
        /// zajem na nastaveni co nejkratsiho mozneho timeoutu.
        /// Jedna se o timeout pri prijem jednoho znaku.
        /// </summary>
        /// <value></value>
        [DefaultValue(500)]
        public override int ReadTimeOut
        {
            get
            {
                return base.ReadTimeOut;
            }
            set
            {
                base.ReadTimeOut = value;
            }
        }

    }
}
