//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.1
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

#pragma warning disable 1591

namespace Anete.AutoUpgrades {
    
    
    /// <summary>
    ///Represents a strongly typed in-memory cache of data.
    ///</summary>
    [global::System.Serializable()]
    [global::System.ComponentModel.DesignerCategoryAttribute("code")]
    [global::System.ComponentModel.ToolboxItem(true)]
    [global::System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedDataSetSchema")]
    [global::System.Xml.Serialization.XmlRootAttribute("AutoUpgradeDs")]
    [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.DataSet")]
    public partial class AutoUpgradeDs : global::System.Data.DataSet {
        
        private AktualizaceDataTable tableAktualizace;
        
        private AktualizacePrubehDataTable tableAktualizacePrubeh;
        
        private AktualizaceDavkyDataTable tableAktualizaceDavky;
        
        private AktualizaceDavky_CFZarizeniDataTable tableAktualizaceDavky_CFZarizeni;
        
        private CFZarizeniDataTable tableCFZarizeni;
        
        private SaveAktualizacePrubehDataTable tableSaveAktualizacePrubeh;
        
        private InstalovaneAktualizaceDataTable tableInstalovaneAktualizace;
        
        private global::System.Data.DataRelation relationFK_AktualizacePrubeh_AktualizaceDavky;
        
        private global::System.Data.DataRelation relationFK_AktualizaceDavky_Aktualizace;
        
        private global::System.Data.DataRelation relationAktualizace_Aktualizace;
        
        private global::System.Data.DataRelation relationInstalovaneAktualizace_AktualizacePrubeh;
        
        private global::System.Data.DataRelation relationFK_AktualizaceDavky_CFZarizeni_AktualizaceDavky;
        
        private global::System.Data.DataRelation relationFK_AktualizaceDavky_CFZarizeni_CFZarizeni;
        
        private global::System.Data.SchemaSerializationMode _schemaSerializationMode = global::System.Data.SchemaSerializationMode.IncludeSchema;
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        public AutoUpgradeDs() {
            this.BeginInit();
            this.InitClass();
            global::System.ComponentModel.CollectionChangeEventHandler schemaChangedHandler = new global::System.ComponentModel.CollectionChangeEventHandler(this.SchemaChanged);
            base.Tables.CollectionChanged += schemaChangedHandler;
            base.Relations.CollectionChanged += schemaChangedHandler;
            this.EndInit();
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        protected AutoUpgradeDs(global::System.Runtime.Serialization.SerializationInfo info, global::System.Runtime.Serialization.StreamingContext context) : 
                base(info, context, false) {
            if ((this.IsBinarySerialized(info, context) == true)) {
                this.InitVars(false);
                global::System.ComponentModel.CollectionChangeEventHandler schemaChangedHandler1 = new global::System.ComponentModel.CollectionChangeEventHandler(this.SchemaChanged);
                this.Tables.CollectionChanged += schemaChangedHandler1;
                this.Relations.CollectionChanged += schemaChangedHandler1;
                return;
            }
            string strSchema = ((string)(info.GetValue("XmlSchema", typeof(string))));
            if ((this.DetermineSchemaSerializationMode(info, context) == global::System.Data.SchemaSerializationMode.IncludeSchema)) {
                global::System.Data.DataSet ds = new global::System.Data.DataSet();
                ds.ReadXmlSchema(new global::System.Xml.XmlTextReader(new global::System.IO.StringReader(strSchema)));
                if ((ds.Tables["Aktualizace"] != null)) {
                    base.Tables.Add(new AktualizaceDataTable(ds.Tables["Aktualizace"]));
                }
                if ((ds.Tables["AktualizacePrubeh"] != null)) {
                    base.Tables.Add(new AktualizacePrubehDataTable(ds.Tables["AktualizacePrubeh"]));
                }
                if ((ds.Tables["AktualizaceDavky"] != null)) {
                    base.Tables.Add(new AktualizaceDavkyDataTable(ds.Tables["AktualizaceDavky"]));
                }
                if ((ds.Tables["AktualizaceDavky_CFZarizeni"] != null)) {
                    base.Tables.Add(new AktualizaceDavky_CFZarizeniDataTable(ds.Tables["AktualizaceDavky_CFZarizeni"]));
                }
                if ((ds.Tables["CFZarizeni"] != null)) {
                    base.Tables.Add(new CFZarizeniDataTable(ds.Tables["CFZarizeni"]));
                }
                if ((ds.Tables["SaveAktualizacePrubeh"] != null)) {
                    base.Tables.Add(new SaveAktualizacePrubehDataTable(ds.Tables["SaveAktualizacePrubeh"]));
                }
                if ((ds.Tables["InstalovaneAktualizace"] != null)) {
                    base.Tables.Add(new InstalovaneAktualizaceDataTable(ds.Tables["InstalovaneAktualizace"]));
                }
                this.DataSetName = ds.DataSetName;
                this.Prefix = ds.Prefix;
                this.Namespace = ds.Namespace;
                this.Locale = ds.Locale;
                this.CaseSensitive = ds.CaseSensitive;
                this.EnforceConstraints = ds.EnforceConstraints;
                this.Merge(ds, false, global::System.Data.MissingSchemaAction.Add);
                this.InitVars();
            }
            else {
                this.ReadXmlSchema(new global::System.Xml.XmlTextReader(new global::System.IO.StringReader(strSchema)));
            }
            this.GetSerializationData(info, context);
            global::System.ComponentModel.CollectionChangeEventHandler schemaChangedHandler = new global::System.ComponentModel.CollectionChangeEventHandler(this.SchemaChanged);
            base.Tables.CollectionChanged += schemaChangedHandler;
            this.Relations.CollectionChanged += schemaChangedHandler;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Browsable(false)]
        [global::System.ComponentModel.DesignerSerializationVisibility(global::System.ComponentModel.DesignerSerializationVisibility.Content)]
        public AktualizaceDataTable Aktualizace {
            get {
                return this.tableAktualizace;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Browsable(false)]
        [global::System.ComponentModel.DesignerSerializationVisibility(global::System.ComponentModel.DesignerSerializationVisibility.Content)]
        public AktualizacePrubehDataTable AktualizacePrubeh {
            get {
                return this.tableAktualizacePrubeh;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Browsable(false)]
        [global::System.ComponentModel.DesignerSerializationVisibility(global::System.ComponentModel.DesignerSerializationVisibility.Content)]
        public AktualizaceDavkyDataTable AktualizaceDavky {
            get {
                return this.tableAktualizaceDavky;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Browsable(false)]
        [global::System.ComponentModel.DesignerSerializationVisibility(global::System.ComponentModel.DesignerSerializationVisibility.Content)]
        public AktualizaceDavky_CFZarizeniDataTable AktualizaceDavky_CFZarizeni {
            get {
                return this.tableAktualizaceDavky_CFZarizeni;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Browsable(false)]
        [global::System.ComponentModel.DesignerSerializationVisibility(global::System.ComponentModel.DesignerSerializationVisibility.Content)]
        public CFZarizeniDataTable CFZarizeni {
            get {
                return this.tableCFZarizeni;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Browsable(false)]
        [global::System.ComponentModel.DesignerSerializationVisibility(global::System.ComponentModel.DesignerSerializationVisibility.Content)]
        public SaveAktualizacePrubehDataTable SaveAktualizacePrubeh {
            get {
                return this.tableSaveAktualizacePrubeh;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Browsable(false)]
        [global::System.ComponentModel.DesignerSerializationVisibility(global::System.ComponentModel.DesignerSerializationVisibility.Content)]
        public InstalovaneAktualizaceDataTable InstalovaneAktualizace {
            get {
                return this.tableInstalovaneAktualizace;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.BrowsableAttribute(true)]
        [global::System.ComponentModel.DesignerSerializationVisibilityAttribute(global::System.ComponentModel.DesignerSerializationVisibility.Visible)]
        public override global::System.Data.SchemaSerializationMode SchemaSerializationMode {
            get {
                return this._schemaSerializationMode;
            }
            set {
                this._schemaSerializationMode = value;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.DesignerSerializationVisibilityAttribute(global::System.ComponentModel.DesignerSerializationVisibility.Hidden)]
        public new global::System.Data.DataTableCollection Tables {
            get {
                return base.Tables;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.DesignerSerializationVisibilityAttribute(global::System.ComponentModel.DesignerSerializationVisibility.Hidden)]
        public new global::System.Data.DataRelationCollection Relations {
            get {
                return base.Relations;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        protected override void InitializeDerivedDataSet() {
            this.BeginInit();
            this.InitClass();
            this.EndInit();
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        public override global::System.Data.DataSet Clone() {
            AutoUpgradeDs cln = ((AutoUpgradeDs)(base.Clone()));
            cln.InitVars();
            cln.SchemaSerializationMode = this.SchemaSerializationMode;
            return cln;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        protected override bool ShouldSerializeTables() {
            return false;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        protected override bool ShouldSerializeRelations() {
            return false;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        protected override void ReadXmlSerializable(global::System.Xml.XmlReader reader) {
            if ((this.DetermineSchemaSerializationMode(reader) == global::System.Data.SchemaSerializationMode.IncludeSchema)) {
                this.Reset();
                global::System.Data.DataSet ds = new global::System.Data.DataSet();
                ds.ReadXml(reader);
                if ((ds.Tables["Aktualizace"] != null)) {
                    base.Tables.Add(new AktualizaceDataTable(ds.Tables["Aktualizace"]));
                }
                if ((ds.Tables["AktualizacePrubeh"] != null)) {
                    base.Tables.Add(new AktualizacePrubehDataTable(ds.Tables["AktualizacePrubeh"]));
                }
                if ((ds.Tables["AktualizaceDavky"] != null)) {
                    base.Tables.Add(new AktualizaceDavkyDataTable(ds.Tables["AktualizaceDavky"]));
                }
                if ((ds.Tables["AktualizaceDavky_CFZarizeni"] != null)) {
                    base.Tables.Add(new AktualizaceDavky_CFZarizeniDataTable(ds.Tables["AktualizaceDavky_CFZarizeni"]));
                }
                if ((ds.Tables["CFZarizeni"] != null)) {
                    base.Tables.Add(new CFZarizeniDataTable(ds.Tables["CFZarizeni"]));
                }
                if ((ds.Tables["SaveAktualizacePrubeh"] != null)) {
                    base.Tables.Add(new SaveAktualizacePrubehDataTable(ds.Tables["SaveAktualizacePrubeh"]));
                }
                if ((ds.Tables["InstalovaneAktualizace"] != null)) {
                    base.Tables.Add(new InstalovaneAktualizaceDataTable(ds.Tables["InstalovaneAktualizace"]));
                }
                this.DataSetName = ds.DataSetName;
                this.Prefix = ds.Prefix;
                this.Namespace = ds.Namespace;
                this.Locale = ds.Locale;
                this.CaseSensitive = ds.CaseSensitive;
                this.EnforceConstraints = ds.EnforceConstraints;
                this.Merge(ds, false, global::System.Data.MissingSchemaAction.Add);
                this.InitVars();
            }
            else {
                this.ReadXml(reader);
                this.InitVars();
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        protected override global::System.Xml.Schema.XmlSchema GetSchemaSerializable() {
            global::System.IO.MemoryStream stream = new global::System.IO.MemoryStream();
            this.WriteXmlSchema(new global::System.Xml.XmlTextWriter(stream, null));
            stream.Position = 0;
            return global::System.Xml.Schema.XmlSchema.Read(new global::System.Xml.XmlTextReader(stream), null);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        internal void InitVars() {
            this.InitVars(true);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        internal void InitVars(bool initTable) {
            this.tableAktualizace = ((AktualizaceDataTable)(base.Tables["Aktualizace"]));
            if ((initTable == true)) {
                if ((this.tableAktualizace != null)) {
                    this.tableAktualizace.InitVars();
                }
            }
            this.tableAktualizacePrubeh = ((AktualizacePrubehDataTable)(base.Tables["AktualizacePrubeh"]));
            if ((initTable == true)) {
                if ((this.tableAktualizacePrubeh != null)) {
                    this.tableAktualizacePrubeh.InitVars();
                }
            }
            this.tableAktualizaceDavky = ((AktualizaceDavkyDataTable)(base.Tables["AktualizaceDavky"]));
            if ((initTable == true)) {
                if ((this.tableAktualizaceDavky != null)) {
                    this.tableAktualizaceDavky.InitVars();
                }
            }
            this.tableAktualizaceDavky_CFZarizeni = ((AktualizaceDavky_CFZarizeniDataTable)(base.Tables["AktualizaceDavky_CFZarizeni"]));
            if ((initTable == true)) {
                if ((this.tableAktualizaceDavky_CFZarizeni != null)) {
                    this.tableAktualizaceDavky_CFZarizeni.InitVars();
                }
            }
            this.tableCFZarizeni = ((CFZarizeniDataTable)(base.Tables["CFZarizeni"]));
            if ((initTable == true)) {
                if ((this.tableCFZarizeni != null)) {
                    this.tableCFZarizeni.InitVars();
                }
            }
            this.tableSaveAktualizacePrubeh = ((SaveAktualizacePrubehDataTable)(base.Tables["SaveAktualizacePrubeh"]));
            if ((initTable == true)) {
                if ((this.tableSaveAktualizacePrubeh != null)) {
                    this.tableSaveAktualizacePrubeh.InitVars();
                }
            }
            this.tableInstalovaneAktualizace = ((InstalovaneAktualizaceDataTable)(base.Tables["InstalovaneAktualizace"]));
            if ((initTable == true)) {
                if ((this.tableInstalovaneAktualizace != null)) {
                    this.tableInstalovaneAktualizace.InitVars();
                }
            }
            this.relationFK_AktualizacePrubeh_AktualizaceDavky = this.Relations["FK_AktualizacePrubeh_AktualizaceDavky"];
            this.relationFK_AktualizaceDavky_Aktualizace = this.Relations["FK_AktualizaceDavky_Aktualizace"];
            this.relationAktualizace_Aktualizace = this.Relations["Aktualizace_Aktualizace"];
            this.relationInstalovaneAktualizace_AktualizacePrubeh = this.Relations["InstalovaneAktualizace_AktualizacePrubeh"];
            this.relationFK_AktualizaceDavky_CFZarizeni_AktualizaceDavky = this.Relations["FK_AktualizaceDavky_CFZarizeni_AktualizaceDavky"];
            this.relationFK_AktualizaceDavky_CFZarizeni_CFZarizeni = this.Relations["FK_AktualizaceDavky_CFZarizeni_CFZarizeni"];
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        private void InitClass() {
            this.DataSetName = "AutoUpgradeDs";
            this.Prefix = "";
            this.Namespace = "http://tempuri.org/AutoUpdatesDs.xsd";
            this.EnforceConstraints = true;
            this.SchemaSerializationMode = global::System.Data.SchemaSerializationMode.IncludeSchema;
            this.tableAktualizace = new AktualizaceDataTable();
            base.Tables.Add(this.tableAktualizace);
            this.tableAktualizacePrubeh = new AktualizacePrubehDataTable();
            base.Tables.Add(this.tableAktualizacePrubeh);
            this.tableAktualizaceDavky = new AktualizaceDavkyDataTable();
            base.Tables.Add(this.tableAktualizaceDavky);
            this.tableAktualizaceDavky_CFZarizeni = new AktualizaceDavky_CFZarizeniDataTable();
            base.Tables.Add(this.tableAktualizaceDavky_CFZarizeni);
            this.tableCFZarizeni = new CFZarizeniDataTable();
            base.Tables.Add(this.tableCFZarizeni);
            this.tableSaveAktualizacePrubeh = new SaveAktualizacePrubehDataTable();
            base.Tables.Add(this.tableSaveAktualizacePrubeh);
            this.tableInstalovaneAktualizace = new InstalovaneAktualizaceDataTable();
            base.Tables.Add(this.tableInstalovaneAktualizace);
            this.relationFK_AktualizacePrubeh_AktualizaceDavky = new global::System.Data.DataRelation("FK_AktualizacePrubeh_AktualizaceDavky", new global::System.Data.DataColumn[] {
                        this.tableAktualizaceDavky.IDDavkyColumn}, new global::System.Data.DataColumn[] {
                        this.tableAktualizacePrubeh.IDDavkyColumn}, false);
            this.Relations.Add(this.relationFK_AktualizacePrubeh_AktualizaceDavky);
            this.relationFK_AktualizaceDavky_Aktualizace = new global::System.Data.DataRelation("FK_AktualizaceDavky_Aktualizace", new global::System.Data.DataColumn[] {
                        this.tableAktualizace.IDAktualizaceColumn}, new global::System.Data.DataColumn[] {
                        this.tableAktualizaceDavky.IDAktualizaceColumn}, false);
            this.Relations.Add(this.relationFK_AktualizaceDavky_Aktualizace);
            this.relationAktualizace_Aktualizace = new global::System.Data.DataRelation("Aktualizace_Aktualizace", new global::System.Data.DataColumn[] {
                        this.tableAktualizace.IDAktualizaceColumn}, new global::System.Data.DataColumn[] {
                        this.tableAktualizace.NahrazenoAktualizaciColumn}, false);
            this.Relations.Add(this.relationAktualizace_Aktualizace);
            this.relationInstalovaneAktualizace_AktualizacePrubeh = new global::System.Data.DataRelation("InstalovaneAktualizace_AktualizacePrubeh", new global::System.Data.DataColumn[] {
                        this.tableInstalovaneAktualizace.IDAktualizacePrubehColumn}, new global::System.Data.DataColumn[] {
                        this.tableAktualizacePrubeh.IDAktualizacePrubehColumn}, false);
            this.Relations.Add(this.relationInstalovaneAktualizace_AktualizacePrubeh);
            this.relationFK_AktualizaceDavky_CFZarizeni_AktualizaceDavky = new global::System.Data.DataRelation("FK_AktualizaceDavky_CFZarizeni_AktualizaceDavky", new global::System.Data.DataColumn[] {
                        this.tableAktualizaceDavky.IDDavkyColumn}, new global::System.Data.DataColumn[] {
                        this.tableAktualizaceDavky_CFZarizeni.IDDavkyColumn}, false);
            this.Relations.Add(this.relationFK_AktualizaceDavky_CFZarizeni_AktualizaceDavky);
            this.relationFK_AktualizaceDavky_CFZarizeni_CFZarizeni = new global::System.Data.DataRelation("FK_AktualizaceDavky_CFZarizeni_CFZarizeni", new global::System.Data.DataColumn[] {
                        this.tableCFZarizeni.id_zarizeniColumn}, new global::System.Data.DataColumn[] {
                        this.tableAktualizaceDavky_CFZarizeni.IDZarizeniColumn}, false);
            this.Relations.Add(this.relationFK_AktualizaceDavky_CFZarizeni_CFZarizeni);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        private bool ShouldSerializeAktualizace() {
            return false;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        private bool ShouldSerializeAktualizacePrubeh() {
            return false;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        private bool ShouldSerializeAktualizaceDavky() {
            return false;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        private bool ShouldSerializeAktualizaceDavky_CFZarizeni() {
            return false;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        private bool ShouldSerializeCFZarizeni() {
            return false;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        private bool ShouldSerializeSaveAktualizacePrubeh() {
            return false;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        private bool ShouldSerializeInstalovaneAktualizace() {
            return false;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        private void SchemaChanged(object sender, global::System.ComponentModel.CollectionChangeEventArgs e) {
            if ((e.Action == global::System.ComponentModel.CollectionChangeAction.Remove)) {
                this.InitVars();
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        public static global::System.Xml.Schema.XmlSchemaComplexType GetTypedDataSetSchema(global::System.Xml.Schema.XmlSchemaSet xs) {
            AutoUpgradeDs ds = new AutoUpgradeDs();
            global::System.Xml.Schema.XmlSchemaComplexType type = new global::System.Xml.Schema.XmlSchemaComplexType();
            global::System.Xml.Schema.XmlSchemaSequence sequence = new global::System.Xml.Schema.XmlSchemaSequence();
            global::System.Xml.Schema.XmlSchemaAny any = new global::System.Xml.Schema.XmlSchemaAny();
            any.Namespace = ds.Namespace;
            sequence.Items.Add(any);
            type.Particle = sequence;
            global::System.Xml.Schema.XmlSchema dsSchema = ds.GetSchemaSerializable();
            if (xs.Contains(dsSchema.TargetNamespace)) {
                global::System.IO.MemoryStream s1 = new global::System.IO.MemoryStream();
                global::System.IO.MemoryStream s2 = new global::System.IO.MemoryStream();
                try {
                    global::System.Xml.Schema.XmlSchema schema = null;
                    dsSchema.Write(s1);
                    for (global::System.Collections.IEnumerator schemas = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator(); schemas.MoveNext(); ) {
                        schema = ((global::System.Xml.Schema.XmlSchema)(schemas.Current));
                        s2.SetLength(0);
                        schema.Write(s2);
                        if ((s1.Length == s2.Length)) {
                            s1.Position = 0;
                            s2.Position = 0;
                            for (; ((s1.Position != s1.Length) 
                                        && (s1.ReadByte() == s2.ReadByte())); ) {
                                ;
                            }
                            if ((s1.Position == s1.Length)) {
                                return type;
                            }
                        }
                    }
                }
                finally {
                    if ((s1 != null)) {
                        s1.Close();
                    }
                    if ((s2 != null)) {
                        s2.Close();
                    }
                }
            }
            xs.Add(dsSchema);
            return type;
        }
        
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        public delegate void AktualizaceRowChangeEventHandler(object sender, AktualizaceRowChangeEvent e);
        
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        public delegate void AktualizacePrubehRowChangeEventHandler(object sender, AktualizacePrubehRowChangeEvent e);
        
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        public delegate void AktualizaceDavkyRowChangeEventHandler(object sender, AktualizaceDavkyRowChangeEvent e);
        
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        public delegate void AktualizaceDavky_CFZarizeniRowChangeEventHandler(object sender, AktualizaceDavky_CFZarizeniRowChangeEvent e);
        
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        public delegate void CFZarizeniRowChangeEventHandler(object sender, CFZarizeniRowChangeEvent e);
        
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        public delegate void SaveAktualizacePrubehRowChangeEventHandler(object sender, SaveAktualizacePrubehRowChangeEvent e);
        
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        public delegate void InstalovaneAktualizaceRowChangeEventHandler(object sender, InstalovaneAktualizaceRowChangeEvent e);
        
        /// <summary>
        ///Represents the strongly named DataTable class.
        ///</summary>
        [global::System.Serializable()]
        [global::System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedTableSchema")]
        public partial class AktualizaceDataTable : global::System.Data.DataTable, global::System.Collections.IEnumerable {
            
            private global::System.Data.DataColumn columnIDAktualizace;
            
            private global::System.Data.DataColumn columnFunkceZarizeni;
            
            private global::System.Data.DataColumn columnNazev;
            
            private global::System.Data.DataColumn columnScript;
            
            private global::System.Data.DataColumn columnSoubory;
            
            private global::System.Data.DataColumn columnExpirovano;
            
            private global::System.Data.DataColumn columnNahrazenoAktualizaci;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public AktualizaceDataTable() {
                this.TableName = "Aktualizace";
                this.BeginInit();
                this.InitClass();
                this.EndInit();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            internal AktualizaceDataTable(global::System.Data.DataTable table) {
                this.TableName = table.TableName;
                if ((table.CaseSensitive != table.DataSet.CaseSensitive)) {
                    this.CaseSensitive = table.CaseSensitive;
                }
                if ((table.Locale.ToString() != table.DataSet.Locale.ToString())) {
                    this.Locale = table.Locale;
                }
                if ((table.Namespace != table.DataSet.Namespace)) {
                    this.Namespace = table.Namespace;
                }
                this.Prefix = table.Prefix;
                this.MinimumCapacity = table.MinimumCapacity;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected AktualizaceDataTable(global::System.Runtime.Serialization.SerializationInfo info, global::System.Runtime.Serialization.StreamingContext context) : 
                    base(info, context) {
                this.InitVars();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataColumn IDAktualizaceColumn {
                get {
                    return this.columnIDAktualizace;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataColumn FunkceZarizeniColumn {
                get {
                    return this.columnFunkceZarizeni;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataColumn NazevColumn {
                get {
                    return this.columnNazev;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataColumn ScriptColumn {
                get {
                    return this.columnScript;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataColumn SouboryColumn {
                get {
                    return this.columnSoubory;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataColumn ExpirovanoColumn {
                get {
                    return this.columnExpirovano;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataColumn NahrazenoAktualizaciColumn {
                get {
                    return this.columnNahrazenoAktualizaci;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            [global::System.ComponentModel.Browsable(false)]
            public int Count {
                get {
                    return this.Rows.Count;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public AktualizaceRow this[int index] {
                get {
                    return ((AktualizaceRow)(this.Rows[index]));
                }
            }
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public event AktualizaceRowChangeEventHandler AktualizaceRowChanging;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public event AktualizaceRowChangeEventHandler AktualizaceRowChanged;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public event AktualizaceRowChangeEventHandler AktualizaceRowDeleting;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public event AktualizaceRowChangeEventHandler AktualizaceRowDeleted;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public void AddAktualizaceRow(AktualizaceRow row) {
                this.Rows.Add(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public AktualizaceRow AddAktualizaceRow(string FunkceZarizeni, string Nazev, string Script, string Soubory, bool Expirovano, AktualizaceRow parentAktualizaceRowByAktualizace_Aktualizace) {
                AktualizaceRow rowAktualizaceRow = ((AktualizaceRow)(this.NewRow()));
                object[] columnValuesArray = new object[] {
                        null,
                        FunkceZarizeni,
                        Nazev,
                        Script,
                        Soubory,
                        Expirovano,
                        null};
                if ((parentAktualizaceRowByAktualizace_Aktualizace != null)) {
                    columnValuesArray[6] = parentAktualizaceRowByAktualizace_Aktualizace[0];
                }
                rowAktualizaceRow.ItemArray = columnValuesArray;
                this.Rows.Add(rowAktualizaceRow);
                return rowAktualizaceRow;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public AktualizaceRow FindByIDAktualizace(int IDAktualizace) {
                return ((AktualizaceRow)(this.Rows.Find(new object[] {
                            IDAktualizace})));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public virtual global::System.Collections.IEnumerator GetEnumerator() {
                return this.Rows.GetEnumerator();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public override global::System.Data.DataTable Clone() {
                AktualizaceDataTable cln = ((AktualizaceDataTable)(base.Clone()));
                cln.InitVars();
                return cln;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override global::System.Data.DataTable CreateInstance() {
                return new AktualizaceDataTable();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            internal void InitVars() {
                this.columnIDAktualizace = base.Columns["IDAktualizace"];
                this.columnFunkceZarizeni = base.Columns["FunkceZarizeni"];
                this.columnNazev = base.Columns["Nazev"];
                this.columnScript = base.Columns["Script"];
                this.columnSoubory = base.Columns["Soubory"];
                this.columnExpirovano = base.Columns["Expirovano"];
                this.columnNahrazenoAktualizaci = base.Columns["NahrazenoAktualizaci"];
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            private void InitClass() {
                this.columnIDAktualizace = new global::System.Data.DataColumn("IDAktualizace", typeof(int), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnIDAktualizace);
                this.columnFunkceZarizeni = new global::System.Data.DataColumn("FunkceZarizeni", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnFunkceZarizeni);
                this.columnNazev = new global::System.Data.DataColumn("Nazev", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnNazev);
                this.columnScript = new global::System.Data.DataColumn("Script", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnScript);
                this.columnSoubory = new global::System.Data.DataColumn("Soubory", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnSoubory);
                this.columnExpirovano = new global::System.Data.DataColumn("Expirovano", typeof(bool), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnExpirovano);
                this.columnNahrazenoAktualizaci = new global::System.Data.DataColumn("NahrazenoAktualizaci", typeof(int), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnNahrazenoAktualizaci);
                this.Constraints.Add(new global::System.Data.UniqueConstraint("Constraint1", new global::System.Data.DataColumn[] {
                                this.columnIDAktualizace}, true));
                this.columnIDAktualizace.AutoIncrement = true;
                this.columnIDAktualizace.AutoIncrementSeed = -1;
                this.columnIDAktualizace.AutoIncrementStep = -1;
                this.columnIDAktualizace.AllowDBNull = false;
                this.columnIDAktualizace.ReadOnly = true;
                this.columnIDAktualizace.Unique = true;
                this.columnFunkceZarizeni.AllowDBNull = false;
                this.columnFunkceZarizeni.MaxLength = 2147483647;
                this.columnNazev.AllowDBNull = false;
                this.columnNazev.MaxLength = 256;
                this.columnScript.AllowDBNull = false;
                this.columnScript.MaxLength = 2147483647;
                this.columnExpirovano.ReadOnly = true;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public AktualizaceRow NewAktualizaceRow() {
                return ((AktualizaceRow)(this.NewRow()));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override global::System.Data.DataRow NewRowFromBuilder(global::System.Data.DataRowBuilder builder) {
                return new AktualizaceRow(builder);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override global::System.Type GetRowType() {
                return typeof(AktualizaceRow);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override void OnRowChanged(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanged(e);
                if ((this.AktualizaceRowChanged != null)) {
                    this.AktualizaceRowChanged(this, new AktualizaceRowChangeEvent(((AktualizaceRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override void OnRowChanging(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanging(e);
                if ((this.AktualizaceRowChanging != null)) {
                    this.AktualizaceRowChanging(this, new AktualizaceRowChangeEvent(((AktualizaceRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override void OnRowDeleted(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleted(e);
                if ((this.AktualizaceRowDeleted != null)) {
                    this.AktualizaceRowDeleted(this, new AktualizaceRowChangeEvent(((AktualizaceRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override void OnRowDeleting(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleting(e);
                if ((this.AktualizaceRowDeleting != null)) {
                    this.AktualizaceRowDeleting(this, new AktualizaceRowChangeEvent(((AktualizaceRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public void RemoveAktualizaceRow(AktualizaceRow row) {
                this.Rows.Remove(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public static global::System.Xml.Schema.XmlSchemaComplexType GetTypedTableSchema(global::System.Xml.Schema.XmlSchemaSet xs) {
                global::System.Xml.Schema.XmlSchemaComplexType type = new global::System.Xml.Schema.XmlSchemaComplexType();
                global::System.Xml.Schema.XmlSchemaSequence sequence = new global::System.Xml.Schema.XmlSchemaSequence();
                AutoUpgradeDs ds = new AutoUpgradeDs();
                global::System.Xml.Schema.XmlSchemaAny any1 = new global::System.Xml.Schema.XmlSchemaAny();
                any1.Namespace = "http://www.w3.org/2001/XMLSchema";
                any1.MinOccurs = new decimal(0);
                any1.MaxOccurs = decimal.MaxValue;
                any1.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any1);
                global::System.Xml.Schema.XmlSchemaAny any2 = new global::System.Xml.Schema.XmlSchemaAny();
                any2.Namespace = "urn:schemas-microsoft-com:xml-diffgram-v1";
                any2.MinOccurs = new decimal(1);
                any2.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any2);
                global::System.Xml.Schema.XmlSchemaAttribute attribute1 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute1.Name = "namespace";
                attribute1.FixedValue = ds.Namespace;
                type.Attributes.Add(attribute1);
                global::System.Xml.Schema.XmlSchemaAttribute attribute2 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute2.Name = "tableTypeName";
                attribute2.FixedValue = "AktualizaceDataTable";
                type.Attributes.Add(attribute2);
                type.Particle = sequence;
                global::System.Xml.Schema.XmlSchema dsSchema = ds.GetSchemaSerializable();
                if (xs.Contains(dsSchema.TargetNamespace)) {
                    global::System.IO.MemoryStream s1 = new global::System.IO.MemoryStream();
                    global::System.IO.MemoryStream s2 = new global::System.IO.MemoryStream();
                    try {
                        global::System.Xml.Schema.XmlSchema schema = null;
                        dsSchema.Write(s1);
                        for (global::System.Collections.IEnumerator schemas = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator(); schemas.MoveNext(); ) {
                            schema = ((global::System.Xml.Schema.XmlSchema)(schemas.Current));
                            s2.SetLength(0);
                            schema.Write(s2);
                            if ((s1.Length == s2.Length)) {
                                s1.Position = 0;
                                s2.Position = 0;
                                for (; ((s1.Position != s1.Length) 
                                            && (s1.ReadByte() == s2.ReadByte())); ) {
                                    ;
                                }
                                if ((s1.Position == s1.Length)) {
                                    return type;
                                }
                            }
                        }
                    }
                    finally {
                        if ((s1 != null)) {
                            s1.Close();
                        }
                        if ((s2 != null)) {
                            s2.Close();
                        }
                    }
                }
                xs.Add(dsSchema);
                return type;
            }
        }
        
        /// <summary>
        ///Represents the strongly named DataTable class.
        ///</summary>
        [global::System.Serializable()]
        [global::System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedTableSchema")]
        public partial class AktualizacePrubehDataTable : global::System.Data.DataTable, global::System.Collections.IEnumerable {
            
            private global::System.Data.DataColumn columnIDAktualizacePrubeh;
            
            private global::System.Data.DataColumn columnLogInfo;
            
            private global::System.Data.DataColumn columnStav;
            
            private global::System.Data.DataColumn columnIDDavky;
            
            private global::System.Data.DataColumn columnDatum;
            
            private global::System.Data.DataColumn columnPopisPC;
            
            private global::System.Data.DataColumn columnIPAdresa;
            
            private global::System.Data.DataColumn columnIdZarizeni;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public AktualizacePrubehDataTable() {
                this.TableName = "AktualizacePrubeh";
                this.BeginInit();
                this.InitClass();
                this.EndInit();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            internal AktualizacePrubehDataTable(global::System.Data.DataTable table) {
                this.TableName = table.TableName;
                if ((table.CaseSensitive != table.DataSet.CaseSensitive)) {
                    this.CaseSensitive = table.CaseSensitive;
                }
                if ((table.Locale.ToString() != table.DataSet.Locale.ToString())) {
                    this.Locale = table.Locale;
                }
                if ((table.Namespace != table.DataSet.Namespace)) {
                    this.Namespace = table.Namespace;
                }
                this.Prefix = table.Prefix;
                this.MinimumCapacity = table.MinimumCapacity;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected AktualizacePrubehDataTable(global::System.Runtime.Serialization.SerializationInfo info, global::System.Runtime.Serialization.StreamingContext context) : 
                    base(info, context) {
                this.InitVars();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataColumn IDAktualizacePrubehColumn {
                get {
                    return this.columnIDAktualizacePrubeh;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataColumn LogInfoColumn {
                get {
                    return this.columnLogInfo;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataColumn StavColumn {
                get {
                    return this.columnStav;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataColumn IDDavkyColumn {
                get {
                    return this.columnIDDavky;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataColumn DatumColumn {
                get {
                    return this.columnDatum;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataColumn PopisPCColumn {
                get {
                    return this.columnPopisPC;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataColumn IPAdresaColumn {
                get {
                    return this.columnIPAdresa;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataColumn IdZarizeniColumn {
                get {
                    return this.columnIdZarizeni;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            [global::System.ComponentModel.Browsable(false)]
            public int Count {
                get {
                    return this.Rows.Count;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public AktualizacePrubehRow this[int index] {
                get {
                    return ((AktualizacePrubehRow)(this.Rows[index]));
                }
            }
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public event AktualizacePrubehRowChangeEventHandler AktualizacePrubehRowChanging;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public event AktualizacePrubehRowChangeEventHandler AktualizacePrubehRowChanged;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public event AktualizacePrubehRowChangeEventHandler AktualizacePrubehRowDeleting;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public event AktualizacePrubehRowChangeEventHandler AktualizacePrubehRowDeleted;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public void AddAktualizacePrubehRow(AktualizacePrubehRow row) {
                this.Rows.Add(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public AktualizacePrubehRow AddAktualizacePrubehRow(string LogInfo, int Stav, AktualizaceDavkyRow parentAktualizaceDavkyRowByFK_AktualizacePrubeh_AktualizaceDavky, System.DateTime Datum, string PopisPC, string IPAdresa, string IdZarizeni) {
                AktualizacePrubehRow rowAktualizacePrubehRow = ((AktualizacePrubehRow)(this.NewRow()));
                object[] columnValuesArray = new object[] {
                        null,
                        LogInfo,
                        Stav,
                        null,
                        Datum,
                        PopisPC,
                        IPAdresa,
                        IdZarizeni};
                if ((parentAktualizaceDavkyRowByFK_AktualizacePrubeh_AktualizaceDavky != null)) {
                    columnValuesArray[3] = parentAktualizaceDavkyRowByFK_AktualizacePrubeh_AktualizaceDavky[0];
                }
                rowAktualizacePrubehRow.ItemArray = columnValuesArray;
                this.Rows.Add(rowAktualizacePrubehRow);
                return rowAktualizacePrubehRow;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public AktualizacePrubehRow FindByIDAktualizacePrubeh(int IDAktualizacePrubeh) {
                return ((AktualizacePrubehRow)(this.Rows.Find(new object[] {
                            IDAktualizacePrubeh})));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public virtual global::System.Collections.IEnumerator GetEnumerator() {
                return this.Rows.GetEnumerator();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public override global::System.Data.DataTable Clone() {
                AktualizacePrubehDataTable cln = ((AktualizacePrubehDataTable)(base.Clone()));
                cln.InitVars();
                return cln;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override global::System.Data.DataTable CreateInstance() {
                return new AktualizacePrubehDataTable();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            internal void InitVars() {
                this.columnIDAktualizacePrubeh = base.Columns["IDAktualizacePrubeh"];
                this.columnLogInfo = base.Columns["LogInfo"];
                this.columnStav = base.Columns["Stav"];
                this.columnIDDavky = base.Columns["IDDavky"];
                this.columnDatum = base.Columns["Datum"];
                this.columnPopisPC = base.Columns["PopisPC"];
                this.columnIPAdresa = base.Columns["IPAdresa"];
                this.columnIdZarizeni = base.Columns["IdZarizeni"];
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            private void InitClass() {
                this.columnIDAktualizacePrubeh = new global::System.Data.DataColumn("IDAktualizacePrubeh", typeof(int), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnIDAktualizacePrubeh);
                this.columnLogInfo = new global::System.Data.DataColumn("LogInfo", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnLogInfo);
                this.columnStav = new global::System.Data.DataColumn("Stav", typeof(int), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnStav);
                this.columnIDDavky = new global::System.Data.DataColumn("IDDavky", typeof(int), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnIDDavky);
                this.columnDatum = new global::System.Data.DataColumn("Datum", typeof(global::System.DateTime), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnDatum);
                this.columnPopisPC = new global::System.Data.DataColumn("PopisPC", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnPopisPC);
                this.columnIPAdresa = new global::System.Data.DataColumn("IPAdresa", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnIPAdresa);
                this.columnIdZarizeni = new global::System.Data.DataColumn("IdZarizeni", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnIdZarizeni);
                this.Constraints.Add(new global::System.Data.UniqueConstraint("Constraint1", new global::System.Data.DataColumn[] {
                                this.columnIDAktualizacePrubeh}, true));
                this.columnIDAktualizacePrubeh.AutoIncrement = true;
                this.columnIDAktualizacePrubeh.AutoIncrementSeed = -1;
                this.columnIDAktualizacePrubeh.AutoIncrementStep = -1;
                this.columnIDAktualizacePrubeh.AllowDBNull = false;
                this.columnIDAktualizacePrubeh.ReadOnly = true;
                this.columnIDAktualizacePrubeh.Unique = true;
                this.columnLogInfo.MaxLength = 2147483647;
                this.columnStav.AllowDBNull = false;
                this.columnIDDavky.AllowDBNull = false;
                this.columnDatum.AllowDBNull = false;
                this.columnPopisPC.AllowDBNull = false;
                this.columnPopisPC.MaxLength = 400;
                this.columnIPAdresa.AllowDBNull = false;
                this.columnIPAdresa.MaxLength = 400;
                this.columnIdZarizeni.AllowDBNull = false;
                this.columnIdZarizeni.MaxLength = 2147483647;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public AktualizacePrubehRow NewAktualizacePrubehRow() {
                return ((AktualizacePrubehRow)(this.NewRow()));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override global::System.Data.DataRow NewRowFromBuilder(global::System.Data.DataRowBuilder builder) {
                return new AktualizacePrubehRow(builder);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override global::System.Type GetRowType() {
                return typeof(AktualizacePrubehRow);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override void OnRowChanged(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanged(e);
                if ((this.AktualizacePrubehRowChanged != null)) {
                    this.AktualizacePrubehRowChanged(this, new AktualizacePrubehRowChangeEvent(((AktualizacePrubehRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override void OnRowChanging(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanging(e);
                if ((this.AktualizacePrubehRowChanging != null)) {
                    this.AktualizacePrubehRowChanging(this, new AktualizacePrubehRowChangeEvent(((AktualizacePrubehRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override void OnRowDeleted(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleted(e);
                if ((this.AktualizacePrubehRowDeleted != null)) {
                    this.AktualizacePrubehRowDeleted(this, new AktualizacePrubehRowChangeEvent(((AktualizacePrubehRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override void OnRowDeleting(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleting(e);
                if ((this.AktualizacePrubehRowDeleting != null)) {
                    this.AktualizacePrubehRowDeleting(this, new AktualizacePrubehRowChangeEvent(((AktualizacePrubehRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public void RemoveAktualizacePrubehRow(AktualizacePrubehRow row) {
                this.Rows.Remove(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public static global::System.Xml.Schema.XmlSchemaComplexType GetTypedTableSchema(global::System.Xml.Schema.XmlSchemaSet xs) {
                global::System.Xml.Schema.XmlSchemaComplexType type = new global::System.Xml.Schema.XmlSchemaComplexType();
                global::System.Xml.Schema.XmlSchemaSequence sequence = new global::System.Xml.Schema.XmlSchemaSequence();
                AutoUpgradeDs ds = new AutoUpgradeDs();
                global::System.Xml.Schema.XmlSchemaAny any1 = new global::System.Xml.Schema.XmlSchemaAny();
                any1.Namespace = "http://www.w3.org/2001/XMLSchema";
                any1.MinOccurs = new decimal(0);
                any1.MaxOccurs = decimal.MaxValue;
                any1.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any1);
                global::System.Xml.Schema.XmlSchemaAny any2 = new global::System.Xml.Schema.XmlSchemaAny();
                any2.Namespace = "urn:schemas-microsoft-com:xml-diffgram-v1";
                any2.MinOccurs = new decimal(1);
                any2.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any2);
                global::System.Xml.Schema.XmlSchemaAttribute attribute1 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute1.Name = "namespace";
                attribute1.FixedValue = ds.Namespace;
                type.Attributes.Add(attribute1);
                global::System.Xml.Schema.XmlSchemaAttribute attribute2 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute2.Name = "tableTypeName";
                attribute2.FixedValue = "AktualizacePrubehDataTable";
                type.Attributes.Add(attribute2);
                type.Particle = sequence;
                global::System.Xml.Schema.XmlSchema dsSchema = ds.GetSchemaSerializable();
                if (xs.Contains(dsSchema.TargetNamespace)) {
                    global::System.IO.MemoryStream s1 = new global::System.IO.MemoryStream();
                    global::System.IO.MemoryStream s2 = new global::System.IO.MemoryStream();
                    try {
                        global::System.Xml.Schema.XmlSchema schema = null;
                        dsSchema.Write(s1);
                        for (global::System.Collections.IEnumerator schemas = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator(); schemas.MoveNext(); ) {
                            schema = ((global::System.Xml.Schema.XmlSchema)(schemas.Current));
                            s2.SetLength(0);
                            schema.Write(s2);
                            if ((s1.Length == s2.Length)) {
                                s1.Position = 0;
                                s2.Position = 0;
                                for (; ((s1.Position != s1.Length) 
                                            && (s1.ReadByte() == s2.ReadByte())); ) {
                                    ;
                                }
                                if ((s1.Position == s1.Length)) {
                                    return type;
                                }
                            }
                        }
                    }
                    finally {
                        if ((s1 != null)) {
                            s1.Close();
                        }
                        if ((s2 != null)) {
                            s2.Close();
                        }
                    }
                }
                xs.Add(dsSchema);
                return type;
            }
        }
        
        /// <summary>
        ///Represents the strongly named DataTable class.
        ///</summary>
        [global::System.Serializable()]
        [global::System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedTableSchema")]
        public partial class AktualizaceDavkyDataTable : global::System.Data.DataTable, global::System.Collections.IEnumerable {
            
            private global::System.Data.DataColumn columnIDDavky;
            
            private global::System.Data.DataColumn columnIDAktualizace;
            
            private global::System.Data.DataColumn columnOperace;
            
            private global::System.Data.DataColumn columnVyzadovano;
            
            private global::System.Data.DataColumn columnDobaInstalace;
            
            private global::System.Data.DataColumn columnParametry;
            
            private global::System.Data.DataColumn columnPriorita;
            
            private global::System.Data.DataColumn columnSpoustet;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public AktualizaceDavkyDataTable() {
                this.TableName = "AktualizaceDavky";
                this.BeginInit();
                this.InitClass();
                this.EndInit();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            internal AktualizaceDavkyDataTable(global::System.Data.DataTable table) {
                this.TableName = table.TableName;
                if ((table.CaseSensitive != table.DataSet.CaseSensitive)) {
                    this.CaseSensitive = table.CaseSensitive;
                }
                if ((table.Locale.ToString() != table.DataSet.Locale.ToString())) {
                    this.Locale = table.Locale;
                }
                if ((table.Namespace != table.DataSet.Namespace)) {
                    this.Namespace = table.Namespace;
                }
                this.Prefix = table.Prefix;
                this.MinimumCapacity = table.MinimumCapacity;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected AktualizaceDavkyDataTable(global::System.Runtime.Serialization.SerializationInfo info, global::System.Runtime.Serialization.StreamingContext context) : 
                    base(info, context) {
                this.InitVars();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataColumn IDDavkyColumn {
                get {
                    return this.columnIDDavky;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataColumn IDAktualizaceColumn {
                get {
                    return this.columnIDAktualizace;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataColumn OperaceColumn {
                get {
                    return this.columnOperace;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataColumn VyzadovanoColumn {
                get {
                    return this.columnVyzadovano;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataColumn DobaInstalaceColumn {
                get {
                    return this.columnDobaInstalace;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataColumn ParametryColumn {
                get {
                    return this.columnParametry;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataColumn PrioritaColumn {
                get {
                    return this.columnPriorita;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataColumn SpoustetColumn {
                get {
                    return this.columnSpoustet;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            [global::System.ComponentModel.Browsable(false)]
            public int Count {
                get {
                    return this.Rows.Count;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public AktualizaceDavkyRow this[int index] {
                get {
                    return ((AktualizaceDavkyRow)(this.Rows[index]));
                }
            }
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public event AktualizaceDavkyRowChangeEventHandler AktualizaceDavkyRowChanging;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public event AktualizaceDavkyRowChangeEventHandler AktualizaceDavkyRowChanged;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public event AktualizaceDavkyRowChangeEventHandler AktualizaceDavkyRowDeleting;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public event AktualizaceDavkyRowChangeEventHandler AktualizaceDavkyRowDeleted;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public void AddAktualizaceDavkyRow(AktualizaceDavkyRow row) {
                this.Rows.Add(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public AktualizaceDavkyRow AddAktualizaceDavkyRow(AktualizaceRow parentAktualizaceRowByFK_AktualizaceDavky_Aktualizace, string Operace, bool Vyzadovano, System.DateTime DobaInstalace, string Parametry, double Priorita, bool Spoustet) {
                AktualizaceDavkyRow rowAktualizaceDavkyRow = ((AktualizaceDavkyRow)(this.NewRow()));
                object[] columnValuesArray = new object[] {
                        null,
                        null,
                        Operace,
                        Vyzadovano,
                        DobaInstalace,
                        Parametry,
                        Priorita,
                        Spoustet};
                if ((parentAktualizaceRowByFK_AktualizaceDavky_Aktualizace != null)) {
                    columnValuesArray[1] = parentAktualizaceRowByFK_AktualizaceDavky_Aktualizace[0];
                }
                rowAktualizaceDavkyRow.ItemArray = columnValuesArray;
                this.Rows.Add(rowAktualizaceDavkyRow);
                return rowAktualizaceDavkyRow;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public AktualizaceDavkyRow FindByIDDavky(int IDDavky) {
                return ((AktualizaceDavkyRow)(this.Rows.Find(new object[] {
                            IDDavky})));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public virtual global::System.Collections.IEnumerator GetEnumerator() {
                return this.Rows.GetEnumerator();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public override global::System.Data.DataTable Clone() {
                AktualizaceDavkyDataTable cln = ((AktualizaceDavkyDataTable)(base.Clone()));
                cln.InitVars();
                return cln;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override global::System.Data.DataTable CreateInstance() {
                return new AktualizaceDavkyDataTable();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            internal void InitVars() {
                this.columnIDDavky = base.Columns["IDDavky"];
                this.columnIDAktualizace = base.Columns["IDAktualizace"];
                this.columnOperace = base.Columns["Operace"];
                this.columnVyzadovano = base.Columns["Vyzadovano"];
                this.columnDobaInstalace = base.Columns["DobaInstalace"];
                this.columnParametry = base.Columns["Parametry"];
                this.columnPriorita = base.Columns["Priorita"];
                this.columnSpoustet = base.Columns["Spoustet"];
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            private void InitClass() {
                this.columnIDDavky = new global::System.Data.DataColumn("IDDavky", typeof(int), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnIDDavky);
                this.columnIDAktualizace = new global::System.Data.DataColumn("IDAktualizace", typeof(int), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnIDAktualizace);
                this.columnOperace = new global::System.Data.DataColumn("Operace", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnOperace);
                this.columnVyzadovano = new global::System.Data.DataColumn("Vyzadovano", typeof(bool), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnVyzadovano);
                this.columnDobaInstalace = new global::System.Data.DataColumn("DobaInstalace", typeof(global::System.DateTime), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnDobaInstalace);
                this.columnParametry = new global::System.Data.DataColumn("Parametry", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnParametry);
                this.columnPriorita = new global::System.Data.DataColumn("Priorita", typeof(double), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnPriorita);
                this.columnSpoustet = new global::System.Data.DataColumn("Spoustet", typeof(bool), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnSpoustet);
                this.Constraints.Add(new global::System.Data.UniqueConstraint("Constraint1", new global::System.Data.DataColumn[] {
                                this.columnIDDavky}, true));
                this.columnIDDavky.AutoIncrement = true;
                this.columnIDDavky.AutoIncrementSeed = -1;
                this.columnIDDavky.AutoIncrementStep = -1;
                this.columnIDDavky.AllowDBNull = false;
                this.columnIDDavky.ReadOnly = true;
                this.columnIDDavky.Unique = true;
                this.columnIDAktualizace.AllowDBNull = false;
                this.columnOperace.AllowDBNull = false;
                this.columnOperace.MaxLength = 100;
                this.columnVyzadovano.AllowDBNull = false;
                this.columnParametry.MaxLength = 4000;
                this.columnSpoustet.AllowDBNull = false;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public AktualizaceDavkyRow NewAktualizaceDavkyRow() {
                return ((AktualizaceDavkyRow)(this.NewRow()));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override global::System.Data.DataRow NewRowFromBuilder(global::System.Data.DataRowBuilder builder) {
                return new AktualizaceDavkyRow(builder);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override global::System.Type GetRowType() {
                return typeof(AktualizaceDavkyRow);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override void OnRowChanged(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanged(e);
                if ((this.AktualizaceDavkyRowChanged != null)) {
                    this.AktualizaceDavkyRowChanged(this, new AktualizaceDavkyRowChangeEvent(((AktualizaceDavkyRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override void OnRowChanging(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanging(e);
                if ((this.AktualizaceDavkyRowChanging != null)) {
                    this.AktualizaceDavkyRowChanging(this, new AktualizaceDavkyRowChangeEvent(((AktualizaceDavkyRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override void OnRowDeleted(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleted(e);
                if ((this.AktualizaceDavkyRowDeleted != null)) {
                    this.AktualizaceDavkyRowDeleted(this, new AktualizaceDavkyRowChangeEvent(((AktualizaceDavkyRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override void OnRowDeleting(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleting(e);
                if ((this.AktualizaceDavkyRowDeleting != null)) {
                    this.AktualizaceDavkyRowDeleting(this, new AktualizaceDavkyRowChangeEvent(((AktualizaceDavkyRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public void RemoveAktualizaceDavkyRow(AktualizaceDavkyRow row) {
                this.Rows.Remove(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public static global::System.Xml.Schema.XmlSchemaComplexType GetTypedTableSchema(global::System.Xml.Schema.XmlSchemaSet xs) {
                global::System.Xml.Schema.XmlSchemaComplexType type = new global::System.Xml.Schema.XmlSchemaComplexType();
                global::System.Xml.Schema.XmlSchemaSequence sequence = new global::System.Xml.Schema.XmlSchemaSequence();
                AutoUpgradeDs ds = new AutoUpgradeDs();
                global::System.Xml.Schema.XmlSchemaAny any1 = new global::System.Xml.Schema.XmlSchemaAny();
                any1.Namespace = "http://www.w3.org/2001/XMLSchema";
                any1.MinOccurs = new decimal(0);
                any1.MaxOccurs = decimal.MaxValue;
                any1.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any1);
                global::System.Xml.Schema.XmlSchemaAny any2 = new global::System.Xml.Schema.XmlSchemaAny();
                any2.Namespace = "urn:schemas-microsoft-com:xml-diffgram-v1";
                any2.MinOccurs = new decimal(1);
                any2.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any2);
                global::System.Xml.Schema.XmlSchemaAttribute attribute1 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute1.Name = "namespace";
                attribute1.FixedValue = ds.Namespace;
                type.Attributes.Add(attribute1);
                global::System.Xml.Schema.XmlSchemaAttribute attribute2 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute2.Name = "tableTypeName";
                attribute2.FixedValue = "AktualizaceDavkyDataTable";
                type.Attributes.Add(attribute2);
                type.Particle = sequence;
                global::System.Xml.Schema.XmlSchema dsSchema = ds.GetSchemaSerializable();
                if (xs.Contains(dsSchema.TargetNamespace)) {
                    global::System.IO.MemoryStream s1 = new global::System.IO.MemoryStream();
                    global::System.IO.MemoryStream s2 = new global::System.IO.MemoryStream();
                    try {
                        global::System.Xml.Schema.XmlSchema schema = null;
                        dsSchema.Write(s1);
                        for (global::System.Collections.IEnumerator schemas = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator(); schemas.MoveNext(); ) {
                            schema = ((global::System.Xml.Schema.XmlSchema)(schemas.Current));
                            s2.SetLength(0);
                            schema.Write(s2);
                            if ((s1.Length == s2.Length)) {
                                s1.Position = 0;
                                s2.Position = 0;
                                for (; ((s1.Position != s1.Length) 
                                            && (s1.ReadByte() == s2.ReadByte())); ) {
                                    ;
                                }
                                if ((s1.Position == s1.Length)) {
                                    return type;
                                }
                            }
                        }
                    }
                    finally {
                        if ((s1 != null)) {
                            s1.Close();
                        }
                        if ((s2 != null)) {
                            s2.Close();
                        }
                    }
                }
                xs.Add(dsSchema);
                return type;
            }
        }
        
        /// <summary>
        ///Represents the strongly named DataTable class.
        ///</summary>
        [global::System.Serializable()]
        [global::System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedTableSchema")]
        public partial class AktualizaceDavky_CFZarizeniDataTable : global::System.Data.DataTable, global::System.Collections.IEnumerable {
            
            private global::System.Data.DataColumn columnIDDavky;
            
            private global::System.Data.DataColumn columnIDZarizeni;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public AktualizaceDavky_CFZarizeniDataTable() {
                this.TableName = "AktualizaceDavky_CFZarizeni";
                this.BeginInit();
                this.InitClass();
                this.EndInit();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            internal AktualizaceDavky_CFZarizeniDataTable(global::System.Data.DataTable table) {
                this.TableName = table.TableName;
                if ((table.CaseSensitive != table.DataSet.CaseSensitive)) {
                    this.CaseSensitive = table.CaseSensitive;
                }
                if ((table.Locale.ToString() != table.DataSet.Locale.ToString())) {
                    this.Locale = table.Locale;
                }
                if ((table.Namespace != table.DataSet.Namespace)) {
                    this.Namespace = table.Namespace;
                }
                this.Prefix = table.Prefix;
                this.MinimumCapacity = table.MinimumCapacity;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected AktualizaceDavky_CFZarizeniDataTable(global::System.Runtime.Serialization.SerializationInfo info, global::System.Runtime.Serialization.StreamingContext context) : 
                    base(info, context) {
                this.InitVars();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataColumn IDDavkyColumn {
                get {
                    return this.columnIDDavky;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataColumn IDZarizeniColumn {
                get {
                    return this.columnIDZarizeni;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            [global::System.ComponentModel.Browsable(false)]
            public int Count {
                get {
                    return this.Rows.Count;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public AktualizaceDavky_CFZarizeniRow this[int index] {
                get {
                    return ((AktualizaceDavky_CFZarizeniRow)(this.Rows[index]));
                }
            }
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public event AktualizaceDavky_CFZarizeniRowChangeEventHandler AktualizaceDavky_CFZarizeniRowChanging;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public event AktualizaceDavky_CFZarizeniRowChangeEventHandler AktualizaceDavky_CFZarizeniRowChanged;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public event AktualizaceDavky_CFZarizeniRowChangeEventHandler AktualizaceDavky_CFZarizeniRowDeleting;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public event AktualizaceDavky_CFZarizeniRowChangeEventHandler AktualizaceDavky_CFZarizeniRowDeleted;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public void AddAktualizaceDavky_CFZarizeniRow(AktualizaceDavky_CFZarizeniRow row) {
                this.Rows.Add(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public AktualizaceDavky_CFZarizeniRow AddAktualizaceDavky_CFZarizeniRow(AktualizaceDavkyRow parentAktualizaceDavkyRowByFK_AktualizaceDavky_CFZarizeni_AktualizaceDavky, CFZarizeniRow parentCFZarizeniRowByFK_AktualizaceDavky_CFZarizeni_CFZarizeni) {
                AktualizaceDavky_CFZarizeniRow rowAktualizaceDavky_CFZarizeniRow = ((AktualizaceDavky_CFZarizeniRow)(this.NewRow()));
                object[] columnValuesArray = new object[] {
                        null,
                        null};
                if ((parentAktualizaceDavkyRowByFK_AktualizaceDavky_CFZarizeni_AktualizaceDavky != null)) {
                    columnValuesArray[0] = parentAktualizaceDavkyRowByFK_AktualizaceDavky_CFZarizeni_AktualizaceDavky[0];
                }
                if ((parentCFZarizeniRowByFK_AktualizaceDavky_CFZarizeni_CFZarizeni != null)) {
                    columnValuesArray[1] = parentCFZarizeniRowByFK_AktualizaceDavky_CFZarizeni_CFZarizeni[0];
                }
                rowAktualizaceDavky_CFZarizeniRow.ItemArray = columnValuesArray;
                this.Rows.Add(rowAktualizaceDavky_CFZarizeniRow);
                return rowAktualizaceDavky_CFZarizeniRow;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public AktualizaceDavky_CFZarizeniRow FindByIDDavkyIDZarizeni(int IDDavky, short IDZarizeni) {
                return ((AktualizaceDavky_CFZarizeniRow)(this.Rows.Find(new object[] {
                            IDDavky,
                            IDZarizeni})));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public virtual global::System.Collections.IEnumerator GetEnumerator() {
                return this.Rows.GetEnumerator();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public override global::System.Data.DataTable Clone() {
                AktualizaceDavky_CFZarizeniDataTable cln = ((AktualizaceDavky_CFZarizeniDataTable)(base.Clone()));
                cln.InitVars();
                return cln;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override global::System.Data.DataTable CreateInstance() {
                return new AktualizaceDavky_CFZarizeniDataTable();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            internal void InitVars() {
                this.columnIDDavky = base.Columns["IDDavky"];
                this.columnIDZarizeni = base.Columns["IDZarizeni"];
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            private void InitClass() {
                this.columnIDDavky = new global::System.Data.DataColumn("IDDavky", typeof(int), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnIDDavky);
                this.columnIDZarizeni = new global::System.Data.DataColumn("IDZarizeni", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnIDZarizeni);
                this.Constraints.Add(new global::System.Data.UniqueConstraint("Constraint1", new global::System.Data.DataColumn[] {
                                this.columnIDDavky,
                                this.columnIDZarizeni}, true));
                this.columnIDDavky.AllowDBNull = false;
                this.columnIDZarizeni.AllowDBNull = false;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public AktualizaceDavky_CFZarizeniRow NewAktualizaceDavky_CFZarizeniRow() {
                return ((AktualizaceDavky_CFZarizeniRow)(this.NewRow()));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override global::System.Data.DataRow NewRowFromBuilder(global::System.Data.DataRowBuilder builder) {
                return new AktualizaceDavky_CFZarizeniRow(builder);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override global::System.Type GetRowType() {
                return typeof(AktualizaceDavky_CFZarizeniRow);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override void OnRowChanged(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanged(e);
                if ((this.AktualizaceDavky_CFZarizeniRowChanged != null)) {
                    this.AktualizaceDavky_CFZarizeniRowChanged(this, new AktualizaceDavky_CFZarizeniRowChangeEvent(((AktualizaceDavky_CFZarizeniRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override void OnRowChanging(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanging(e);
                if ((this.AktualizaceDavky_CFZarizeniRowChanging != null)) {
                    this.AktualizaceDavky_CFZarizeniRowChanging(this, new AktualizaceDavky_CFZarizeniRowChangeEvent(((AktualizaceDavky_CFZarizeniRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override void OnRowDeleted(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleted(e);
                if ((this.AktualizaceDavky_CFZarizeniRowDeleted != null)) {
                    this.AktualizaceDavky_CFZarizeniRowDeleted(this, new AktualizaceDavky_CFZarizeniRowChangeEvent(((AktualizaceDavky_CFZarizeniRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override void OnRowDeleting(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleting(e);
                if ((this.AktualizaceDavky_CFZarizeniRowDeleting != null)) {
                    this.AktualizaceDavky_CFZarizeniRowDeleting(this, new AktualizaceDavky_CFZarizeniRowChangeEvent(((AktualizaceDavky_CFZarizeniRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public void RemoveAktualizaceDavky_CFZarizeniRow(AktualizaceDavky_CFZarizeniRow row) {
                this.Rows.Remove(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public static global::System.Xml.Schema.XmlSchemaComplexType GetTypedTableSchema(global::System.Xml.Schema.XmlSchemaSet xs) {
                global::System.Xml.Schema.XmlSchemaComplexType type = new global::System.Xml.Schema.XmlSchemaComplexType();
                global::System.Xml.Schema.XmlSchemaSequence sequence = new global::System.Xml.Schema.XmlSchemaSequence();
                AutoUpgradeDs ds = new AutoUpgradeDs();
                global::System.Xml.Schema.XmlSchemaAny any1 = new global::System.Xml.Schema.XmlSchemaAny();
                any1.Namespace = "http://www.w3.org/2001/XMLSchema";
                any1.MinOccurs = new decimal(0);
                any1.MaxOccurs = decimal.MaxValue;
                any1.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any1);
                global::System.Xml.Schema.XmlSchemaAny any2 = new global::System.Xml.Schema.XmlSchemaAny();
                any2.Namespace = "urn:schemas-microsoft-com:xml-diffgram-v1";
                any2.MinOccurs = new decimal(1);
                any2.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any2);
                global::System.Xml.Schema.XmlSchemaAttribute attribute1 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute1.Name = "namespace";
                attribute1.FixedValue = ds.Namespace;
                type.Attributes.Add(attribute1);
                global::System.Xml.Schema.XmlSchemaAttribute attribute2 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute2.Name = "tableTypeName";
                attribute2.FixedValue = "AktualizaceDavky_CFZarizeniDataTable";
                type.Attributes.Add(attribute2);
                type.Particle = sequence;
                global::System.Xml.Schema.XmlSchema dsSchema = ds.GetSchemaSerializable();
                if (xs.Contains(dsSchema.TargetNamespace)) {
                    global::System.IO.MemoryStream s1 = new global::System.IO.MemoryStream();
                    global::System.IO.MemoryStream s2 = new global::System.IO.MemoryStream();
                    try {
                        global::System.Xml.Schema.XmlSchema schema = null;
                        dsSchema.Write(s1);
                        for (global::System.Collections.IEnumerator schemas = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator(); schemas.MoveNext(); ) {
                            schema = ((global::System.Xml.Schema.XmlSchema)(schemas.Current));
                            s2.SetLength(0);
                            schema.Write(s2);
                            if ((s1.Length == s2.Length)) {
                                s1.Position = 0;
                                s2.Position = 0;
                                for (; ((s1.Position != s1.Length) 
                                            && (s1.ReadByte() == s2.ReadByte())); ) {
                                    ;
                                }
                                if ((s1.Position == s1.Length)) {
                                    return type;
                                }
                            }
                        }
                    }
                    finally {
                        if ((s1 != null)) {
                            s1.Close();
                        }
                        if ((s2 != null)) {
                            s2.Close();
                        }
                    }
                }
                xs.Add(dsSchema);
                return type;
            }
        }
        
        /// <summary>
        ///Represents the strongly named DataTable class.
        ///</summary>
        [global::System.Serializable()]
        [global::System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedTableSchema")]
        public partial class CFZarizeniDataTable : global::System.Data.DataTable, global::System.Collections.IEnumerable {
            
            private global::System.Data.DataColumn columnid_zarizeni;
            
            private global::System.Data.DataColumn columnid_funkce;
            
            private global::System.Data.DataColumn columnpouzito;
            
            private global::System.Data.DataColumn columnaktivni;
            
            private global::System.Data.DataColumn columnpopis;
            
            private global::System.Data.DataColumn columnid_jidelna;
            
            private global::System.Data.DataColumn columnid_vydejna;
            
            private global::System.Data.DataColumn columnlast_contact;
            
            private global::System.Data.DataColumn columnstav_queue;
            
            private global::System.Data.DataColumn columnid_sklad;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public CFZarizeniDataTable() {
                this.TableName = "CFZarizeni";
                this.BeginInit();
                this.InitClass();
                this.EndInit();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            internal CFZarizeniDataTable(global::System.Data.DataTable table) {
                this.TableName = table.TableName;
                if ((table.CaseSensitive != table.DataSet.CaseSensitive)) {
                    this.CaseSensitive = table.CaseSensitive;
                }
                if ((table.Locale.ToString() != table.DataSet.Locale.ToString())) {
                    this.Locale = table.Locale;
                }
                if ((table.Namespace != table.DataSet.Namespace)) {
                    this.Namespace = table.Namespace;
                }
                this.Prefix = table.Prefix;
                this.MinimumCapacity = table.MinimumCapacity;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected CFZarizeniDataTable(global::System.Runtime.Serialization.SerializationInfo info, global::System.Runtime.Serialization.StreamingContext context) : 
                    base(info, context) {
                this.InitVars();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataColumn id_zarizeniColumn {
                get {
                    return this.columnid_zarizeni;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataColumn id_funkceColumn {
                get {
                    return this.columnid_funkce;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataColumn pouzitoColumn {
                get {
                    return this.columnpouzito;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataColumn aktivniColumn {
                get {
                    return this.columnaktivni;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataColumn popisColumn {
                get {
                    return this.columnpopis;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataColumn id_jidelnaColumn {
                get {
                    return this.columnid_jidelna;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataColumn id_vydejnaColumn {
                get {
                    return this.columnid_vydejna;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataColumn last_contactColumn {
                get {
                    return this.columnlast_contact;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataColumn stav_queueColumn {
                get {
                    return this.columnstav_queue;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataColumn id_skladColumn {
                get {
                    return this.columnid_sklad;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            [global::System.ComponentModel.Browsable(false)]
            public int Count {
                get {
                    return this.Rows.Count;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public CFZarizeniRow this[int index] {
                get {
                    return ((CFZarizeniRow)(this.Rows[index]));
                }
            }
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public event CFZarizeniRowChangeEventHandler CFZarizeniRowChanging;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public event CFZarizeniRowChangeEventHandler CFZarizeniRowChanged;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public event CFZarizeniRowChangeEventHandler CFZarizeniRowDeleting;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public event CFZarizeniRowChangeEventHandler CFZarizeniRowDeleted;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public void AddCFZarizeniRow(CFZarizeniRow row) {
                this.Rows.Add(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public CFZarizeniRow AddCFZarizeniRow(short id_zarizeni, short id_funkce, short pouzito, short aktivni, string popis, short id_jidelna, short id_vydejna, System.DateTime last_contact, string stav_queue, short id_sklad) {
                CFZarizeniRow rowCFZarizeniRow = ((CFZarizeniRow)(this.NewRow()));
                object[] columnValuesArray = new object[] {
                        id_zarizeni,
                        id_funkce,
                        pouzito,
                        aktivni,
                        popis,
                        id_jidelna,
                        id_vydejna,
                        last_contact,
                        stav_queue,
                        id_sklad};
                rowCFZarizeniRow.ItemArray = columnValuesArray;
                this.Rows.Add(rowCFZarizeniRow);
                return rowCFZarizeniRow;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public CFZarizeniRow FindByid_zarizeni(short id_zarizeni) {
                return ((CFZarizeniRow)(this.Rows.Find(new object[] {
                            id_zarizeni})));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public virtual global::System.Collections.IEnumerator GetEnumerator() {
                return this.Rows.GetEnumerator();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public override global::System.Data.DataTable Clone() {
                CFZarizeniDataTable cln = ((CFZarizeniDataTable)(base.Clone()));
                cln.InitVars();
                return cln;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override global::System.Data.DataTable CreateInstance() {
                return new CFZarizeniDataTable();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            internal void InitVars() {
                this.columnid_zarizeni = base.Columns["id_zarizeni"];
                this.columnid_funkce = base.Columns["id_funkce"];
                this.columnpouzito = base.Columns["pouzito"];
                this.columnaktivni = base.Columns["aktivni"];
                this.columnpopis = base.Columns["popis"];
                this.columnid_jidelna = base.Columns["id_jidelna"];
                this.columnid_vydejna = base.Columns["id_vydejna"];
                this.columnlast_contact = base.Columns["last_contact"];
                this.columnstav_queue = base.Columns["stav_queue"];
                this.columnid_sklad = base.Columns["id_sklad"];
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            private void InitClass() {
                this.columnid_zarizeni = new global::System.Data.DataColumn("id_zarizeni", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnid_zarizeni);
                this.columnid_funkce = new global::System.Data.DataColumn("id_funkce", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnid_funkce);
                this.columnpouzito = new global::System.Data.DataColumn("pouzito", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnpouzito);
                this.columnaktivni = new global::System.Data.DataColumn("aktivni", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnaktivni);
                this.columnpopis = new global::System.Data.DataColumn("popis", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnpopis);
                this.columnid_jidelna = new global::System.Data.DataColumn("id_jidelna", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnid_jidelna);
                this.columnid_vydejna = new global::System.Data.DataColumn("id_vydejna", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnid_vydejna);
                this.columnlast_contact = new global::System.Data.DataColumn("last_contact", typeof(global::System.DateTime), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnlast_contact);
                this.columnstav_queue = new global::System.Data.DataColumn("stav_queue", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnstav_queue);
                this.columnid_sklad = new global::System.Data.DataColumn("id_sklad", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnid_sklad);
                this.Constraints.Add(new global::System.Data.UniqueConstraint("Constraint1", new global::System.Data.DataColumn[] {
                                this.columnid_zarizeni}, true));
                this.columnid_zarizeni.AllowDBNull = false;
                this.columnid_zarizeni.Unique = true;
                this.columnid_funkce.AllowDBNull = false;
                this.columnpopis.AllowDBNull = false;
                this.columnpopis.MaxLength = 100;
                this.columnid_jidelna.AllowDBNull = false;
                this.columnid_vydejna.AllowDBNull = false;
                this.columnstav_queue.MaxLength = 80;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public CFZarizeniRow NewCFZarizeniRow() {
                return ((CFZarizeniRow)(this.NewRow()));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override global::System.Data.DataRow NewRowFromBuilder(global::System.Data.DataRowBuilder builder) {
                return new CFZarizeniRow(builder);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override global::System.Type GetRowType() {
                return typeof(CFZarizeniRow);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override void OnRowChanged(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanged(e);
                if ((this.CFZarizeniRowChanged != null)) {
                    this.CFZarizeniRowChanged(this, new CFZarizeniRowChangeEvent(((CFZarizeniRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override void OnRowChanging(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanging(e);
                if ((this.CFZarizeniRowChanging != null)) {
                    this.CFZarizeniRowChanging(this, new CFZarizeniRowChangeEvent(((CFZarizeniRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override void OnRowDeleted(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleted(e);
                if ((this.CFZarizeniRowDeleted != null)) {
                    this.CFZarizeniRowDeleted(this, new CFZarizeniRowChangeEvent(((CFZarizeniRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override void OnRowDeleting(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleting(e);
                if ((this.CFZarizeniRowDeleting != null)) {
                    this.CFZarizeniRowDeleting(this, new CFZarizeniRowChangeEvent(((CFZarizeniRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public void RemoveCFZarizeniRow(CFZarizeniRow row) {
                this.Rows.Remove(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public static global::System.Xml.Schema.XmlSchemaComplexType GetTypedTableSchema(global::System.Xml.Schema.XmlSchemaSet xs) {
                global::System.Xml.Schema.XmlSchemaComplexType type = new global::System.Xml.Schema.XmlSchemaComplexType();
                global::System.Xml.Schema.XmlSchemaSequence sequence = new global::System.Xml.Schema.XmlSchemaSequence();
                AutoUpgradeDs ds = new AutoUpgradeDs();
                global::System.Xml.Schema.XmlSchemaAny any1 = new global::System.Xml.Schema.XmlSchemaAny();
                any1.Namespace = "http://www.w3.org/2001/XMLSchema";
                any1.MinOccurs = new decimal(0);
                any1.MaxOccurs = decimal.MaxValue;
                any1.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any1);
                global::System.Xml.Schema.XmlSchemaAny any2 = new global::System.Xml.Schema.XmlSchemaAny();
                any2.Namespace = "urn:schemas-microsoft-com:xml-diffgram-v1";
                any2.MinOccurs = new decimal(1);
                any2.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any2);
                global::System.Xml.Schema.XmlSchemaAttribute attribute1 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute1.Name = "namespace";
                attribute1.FixedValue = ds.Namespace;
                type.Attributes.Add(attribute1);
                global::System.Xml.Schema.XmlSchemaAttribute attribute2 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute2.Name = "tableTypeName";
                attribute2.FixedValue = "CFZarizeniDataTable";
                type.Attributes.Add(attribute2);
                type.Particle = sequence;
                global::System.Xml.Schema.XmlSchema dsSchema = ds.GetSchemaSerializable();
                if (xs.Contains(dsSchema.TargetNamespace)) {
                    global::System.IO.MemoryStream s1 = new global::System.IO.MemoryStream();
                    global::System.IO.MemoryStream s2 = new global::System.IO.MemoryStream();
                    try {
                        global::System.Xml.Schema.XmlSchema schema = null;
                        dsSchema.Write(s1);
                        for (global::System.Collections.IEnumerator schemas = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator(); schemas.MoveNext(); ) {
                            schema = ((global::System.Xml.Schema.XmlSchema)(schemas.Current));
                            s2.SetLength(0);
                            schema.Write(s2);
                            if ((s1.Length == s2.Length)) {
                                s1.Position = 0;
                                s2.Position = 0;
                                for (; ((s1.Position != s1.Length) 
                                            && (s1.ReadByte() == s2.ReadByte())); ) {
                                    ;
                                }
                                if ((s1.Position == s1.Length)) {
                                    return type;
                                }
                            }
                        }
                    }
                    finally {
                        if ((s1 != null)) {
                            s1.Close();
                        }
                        if ((s2 != null)) {
                            s2.Close();
                        }
                    }
                }
                xs.Add(dsSchema);
                return type;
            }
        }
        
        /// <summary>
        ///Represents the strongly named DataTable class.
        ///</summary>
        [global::System.Serializable()]
        [global::System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedTableSchema")]
        public partial class SaveAktualizacePrubehDataTable : global::System.Data.DataTable, global::System.Collections.IEnumerable {
            
            private global::System.Data.DataColumn columnIDAktualizacePrubeh;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public SaveAktualizacePrubehDataTable() {
                this.TableName = "SaveAktualizacePrubeh";
                this.BeginInit();
                this.InitClass();
                this.EndInit();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            internal SaveAktualizacePrubehDataTable(global::System.Data.DataTable table) {
                this.TableName = table.TableName;
                if ((table.CaseSensitive != table.DataSet.CaseSensitive)) {
                    this.CaseSensitive = table.CaseSensitive;
                }
                if ((table.Locale.ToString() != table.DataSet.Locale.ToString())) {
                    this.Locale = table.Locale;
                }
                if ((table.Namespace != table.DataSet.Namespace)) {
                    this.Namespace = table.Namespace;
                }
                this.Prefix = table.Prefix;
                this.MinimumCapacity = table.MinimumCapacity;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected SaveAktualizacePrubehDataTable(global::System.Runtime.Serialization.SerializationInfo info, global::System.Runtime.Serialization.StreamingContext context) : 
                    base(info, context) {
                this.InitVars();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataColumn IDAktualizacePrubehColumn {
                get {
                    return this.columnIDAktualizacePrubeh;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            [global::System.ComponentModel.Browsable(false)]
            public int Count {
                get {
                    return this.Rows.Count;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public SaveAktualizacePrubehRow this[int index] {
                get {
                    return ((SaveAktualizacePrubehRow)(this.Rows[index]));
                }
            }
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public event SaveAktualizacePrubehRowChangeEventHandler SaveAktualizacePrubehRowChanging;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public event SaveAktualizacePrubehRowChangeEventHandler SaveAktualizacePrubehRowChanged;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public event SaveAktualizacePrubehRowChangeEventHandler SaveAktualizacePrubehRowDeleting;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public event SaveAktualizacePrubehRowChangeEventHandler SaveAktualizacePrubehRowDeleted;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public void AddSaveAktualizacePrubehRow(SaveAktualizacePrubehRow row) {
                this.Rows.Add(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public SaveAktualizacePrubehRow AddSaveAktualizacePrubehRow() {
                SaveAktualizacePrubehRow rowSaveAktualizacePrubehRow = ((SaveAktualizacePrubehRow)(this.NewRow()));
                object[] columnValuesArray = new object[] {
                        null};
                rowSaveAktualizacePrubehRow.ItemArray = columnValuesArray;
                this.Rows.Add(rowSaveAktualizacePrubehRow);
                return rowSaveAktualizacePrubehRow;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public SaveAktualizacePrubehRow FindByIDAktualizacePrubeh(int IDAktualizacePrubeh) {
                return ((SaveAktualizacePrubehRow)(this.Rows.Find(new object[] {
                            IDAktualizacePrubeh})));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public virtual global::System.Collections.IEnumerator GetEnumerator() {
                return this.Rows.GetEnumerator();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public override global::System.Data.DataTable Clone() {
                SaveAktualizacePrubehDataTable cln = ((SaveAktualizacePrubehDataTable)(base.Clone()));
                cln.InitVars();
                return cln;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override global::System.Data.DataTable CreateInstance() {
                return new SaveAktualizacePrubehDataTable();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            internal void InitVars() {
                this.columnIDAktualizacePrubeh = base.Columns["IDAktualizacePrubeh"];
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            private void InitClass() {
                this.columnIDAktualizacePrubeh = new global::System.Data.DataColumn("IDAktualizacePrubeh", typeof(int), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnIDAktualizacePrubeh);
                this.Constraints.Add(new global::System.Data.UniqueConstraint("Constraint1", new global::System.Data.DataColumn[] {
                                this.columnIDAktualizacePrubeh}, true));
                this.columnIDAktualizacePrubeh.AutoIncrement = true;
                this.columnIDAktualizacePrubeh.AllowDBNull = false;
                this.columnIDAktualizacePrubeh.ReadOnly = true;
                this.columnIDAktualizacePrubeh.Unique = true;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public SaveAktualizacePrubehRow NewSaveAktualizacePrubehRow() {
                return ((SaveAktualizacePrubehRow)(this.NewRow()));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override global::System.Data.DataRow NewRowFromBuilder(global::System.Data.DataRowBuilder builder) {
                return new SaveAktualizacePrubehRow(builder);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override global::System.Type GetRowType() {
                return typeof(SaveAktualizacePrubehRow);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override void OnRowChanged(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanged(e);
                if ((this.SaveAktualizacePrubehRowChanged != null)) {
                    this.SaveAktualizacePrubehRowChanged(this, new SaveAktualizacePrubehRowChangeEvent(((SaveAktualizacePrubehRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override void OnRowChanging(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanging(e);
                if ((this.SaveAktualizacePrubehRowChanging != null)) {
                    this.SaveAktualizacePrubehRowChanging(this, new SaveAktualizacePrubehRowChangeEvent(((SaveAktualizacePrubehRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override void OnRowDeleted(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleted(e);
                if ((this.SaveAktualizacePrubehRowDeleted != null)) {
                    this.SaveAktualizacePrubehRowDeleted(this, new SaveAktualizacePrubehRowChangeEvent(((SaveAktualizacePrubehRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override void OnRowDeleting(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleting(e);
                if ((this.SaveAktualizacePrubehRowDeleting != null)) {
                    this.SaveAktualizacePrubehRowDeleting(this, new SaveAktualizacePrubehRowChangeEvent(((SaveAktualizacePrubehRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public void RemoveSaveAktualizacePrubehRow(SaveAktualizacePrubehRow row) {
                this.Rows.Remove(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public static global::System.Xml.Schema.XmlSchemaComplexType GetTypedTableSchema(global::System.Xml.Schema.XmlSchemaSet xs) {
                global::System.Xml.Schema.XmlSchemaComplexType type = new global::System.Xml.Schema.XmlSchemaComplexType();
                global::System.Xml.Schema.XmlSchemaSequence sequence = new global::System.Xml.Schema.XmlSchemaSequence();
                AutoUpgradeDs ds = new AutoUpgradeDs();
                global::System.Xml.Schema.XmlSchemaAny any1 = new global::System.Xml.Schema.XmlSchemaAny();
                any1.Namespace = "http://www.w3.org/2001/XMLSchema";
                any1.MinOccurs = new decimal(0);
                any1.MaxOccurs = decimal.MaxValue;
                any1.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any1);
                global::System.Xml.Schema.XmlSchemaAny any2 = new global::System.Xml.Schema.XmlSchemaAny();
                any2.Namespace = "urn:schemas-microsoft-com:xml-diffgram-v1";
                any2.MinOccurs = new decimal(1);
                any2.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any2);
                global::System.Xml.Schema.XmlSchemaAttribute attribute1 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute1.Name = "namespace";
                attribute1.FixedValue = ds.Namespace;
                type.Attributes.Add(attribute1);
                global::System.Xml.Schema.XmlSchemaAttribute attribute2 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute2.Name = "tableTypeName";
                attribute2.FixedValue = "SaveAktualizacePrubehDataTable";
                type.Attributes.Add(attribute2);
                type.Particle = sequence;
                global::System.Xml.Schema.XmlSchema dsSchema = ds.GetSchemaSerializable();
                if (xs.Contains(dsSchema.TargetNamespace)) {
                    global::System.IO.MemoryStream s1 = new global::System.IO.MemoryStream();
                    global::System.IO.MemoryStream s2 = new global::System.IO.MemoryStream();
                    try {
                        global::System.Xml.Schema.XmlSchema schema = null;
                        dsSchema.Write(s1);
                        for (global::System.Collections.IEnumerator schemas = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator(); schemas.MoveNext(); ) {
                            schema = ((global::System.Xml.Schema.XmlSchema)(schemas.Current));
                            s2.SetLength(0);
                            schema.Write(s2);
                            if ((s1.Length == s2.Length)) {
                                s1.Position = 0;
                                s2.Position = 0;
                                for (; ((s1.Position != s1.Length) 
                                            && (s1.ReadByte() == s2.ReadByte())); ) {
                                    ;
                                }
                                if ((s1.Position == s1.Length)) {
                                    return type;
                                }
                            }
                        }
                    }
                    finally {
                        if ((s1 != null)) {
                            s1.Close();
                        }
                        if ((s2 != null)) {
                            s2.Close();
                        }
                    }
                }
                xs.Add(dsSchema);
                return type;
            }
        }
        
        /// <summary>
        ///Represents the strongly named DataTable class.
        ///</summary>
        [global::System.Serializable()]
        [global::System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedTableSchema")]
        public partial class InstalovaneAktualizaceDataTable : global::System.Data.DataTable, global::System.Collections.IEnumerable {
            
            private global::System.Data.DataColumn columnIDAktualizacePrubeh;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public InstalovaneAktualizaceDataTable() {
                this.TableName = "InstalovaneAktualizace";
                this.BeginInit();
                this.InitClass();
                this.EndInit();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            internal InstalovaneAktualizaceDataTable(global::System.Data.DataTable table) {
                this.TableName = table.TableName;
                if ((table.CaseSensitive != table.DataSet.CaseSensitive)) {
                    this.CaseSensitive = table.CaseSensitive;
                }
                if ((table.Locale.ToString() != table.DataSet.Locale.ToString())) {
                    this.Locale = table.Locale;
                }
                if ((table.Namespace != table.DataSet.Namespace)) {
                    this.Namespace = table.Namespace;
                }
                this.Prefix = table.Prefix;
                this.MinimumCapacity = table.MinimumCapacity;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected InstalovaneAktualizaceDataTable(global::System.Runtime.Serialization.SerializationInfo info, global::System.Runtime.Serialization.StreamingContext context) : 
                    base(info, context) {
                this.InitVars();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataColumn IDAktualizacePrubehColumn {
                get {
                    return this.columnIDAktualizacePrubeh;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            [global::System.ComponentModel.Browsable(false)]
            public int Count {
                get {
                    return this.Rows.Count;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public InstalovaneAktualizaceRow this[int index] {
                get {
                    return ((InstalovaneAktualizaceRow)(this.Rows[index]));
                }
            }
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public event InstalovaneAktualizaceRowChangeEventHandler InstalovaneAktualizaceRowChanging;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public event InstalovaneAktualizaceRowChangeEventHandler InstalovaneAktualizaceRowChanged;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public event InstalovaneAktualizaceRowChangeEventHandler InstalovaneAktualizaceRowDeleting;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public event InstalovaneAktualizaceRowChangeEventHandler InstalovaneAktualizaceRowDeleted;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public void AddInstalovaneAktualizaceRow(InstalovaneAktualizaceRow row) {
                this.Rows.Add(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public InstalovaneAktualizaceRow AddInstalovaneAktualizaceRow(int IDAktualizacePrubeh) {
                InstalovaneAktualizaceRow rowInstalovaneAktualizaceRow = ((InstalovaneAktualizaceRow)(this.NewRow()));
                object[] columnValuesArray = new object[] {
                        IDAktualizacePrubeh};
                rowInstalovaneAktualizaceRow.ItemArray = columnValuesArray;
                this.Rows.Add(rowInstalovaneAktualizaceRow);
                return rowInstalovaneAktualizaceRow;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public InstalovaneAktualizaceRow FindByIDAktualizacePrubeh(int IDAktualizacePrubeh) {
                return ((InstalovaneAktualizaceRow)(this.Rows.Find(new object[] {
                            IDAktualizacePrubeh})));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public virtual global::System.Collections.IEnumerator GetEnumerator() {
                return this.Rows.GetEnumerator();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public override global::System.Data.DataTable Clone() {
                InstalovaneAktualizaceDataTable cln = ((InstalovaneAktualizaceDataTable)(base.Clone()));
                cln.InitVars();
                return cln;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override global::System.Data.DataTable CreateInstance() {
                return new InstalovaneAktualizaceDataTable();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            internal void InitVars() {
                this.columnIDAktualizacePrubeh = base.Columns["IDAktualizacePrubeh"];
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            private void InitClass() {
                this.columnIDAktualizacePrubeh = new global::System.Data.DataColumn("IDAktualizacePrubeh", typeof(int), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnIDAktualizacePrubeh);
                this.Constraints.Add(new global::System.Data.UniqueConstraint("Constraint1", new global::System.Data.DataColumn[] {
                                this.columnIDAktualizacePrubeh}, true));
                this.columnIDAktualizacePrubeh.AllowDBNull = false;
                this.columnIDAktualizacePrubeh.Unique = true;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public InstalovaneAktualizaceRow NewInstalovaneAktualizaceRow() {
                return ((InstalovaneAktualizaceRow)(this.NewRow()));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override global::System.Data.DataRow NewRowFromBuilder(global::System.Data.DataRowBuilder builder) {
                return new InstalovaneAktualizaceRow(builder);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override global::System.Type GetRowType() {
                return typeof(InstalovaneAktualizaceRow);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override void OnRowChanged(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanged(e);
                if ((this.InstalovaneAktualizaceRowChanged != null)) {
                    this.InstalovaneAktualizaceRowChanged(this, new InstalovaneAktualizaceRowChangeEvent(((InstalovaneAktualizaceRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override void OnRowChanging(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanging(e);
                if ((this.InstalovaneAktualizaceRowChanging != null)) {
                    this.InstalovaneAktualizaceRowChanging(this, new InstalovaneAktualizaceRowChangeEvent(((InstalovaneAktualizaceRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override void OnRowDeleted(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleted(e);
                if ((this.InstalovaneAktualizaceRowDeleted != null)) {
                    this.InstalovaneAktualizaceRowDeleted(this, new InstalovaneAktualizaceRowChangeEvent(((InstalovaneAktualizaceRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            protected override void OnRowDeleting(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleting(e);
                if ((this.InstalovaneAktualizaceRowDeleting != null)) {
                    this.InstalovaneAktualizaceRowDeleting(this, new InstalovaneAktualizaceRowChangeEvent(((InstalovaneAktualizaceRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public void RemoveInstalovaneAktualizaceRow(InstalovaneAktualizaceRow row) {
                this.Rows.Remove(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public static global::System.Xml.Schema.XmlSchemaComplexType GetTypedTableSchema(global::System.Xml.Schema.XmlSchemaSet xs) {
                global::System.Xml.Schema.XmlSchemaComplexType type = new global::System.Xml.Schema.XmlSchemaComplexType();
                global::System.Xml.Schema.XmlSchemaSequence sequence = new global::System.Xml.Schema.XmlSchemaSequence();
                AutoUpgradeDs ds = new AutoUpgradeDs();
                global::System.Xml.Schema.XmlSchemaAny any1 = new global::System.Xml.Schema.XmlSchemaAny();
                any1.Namespace = "http://www.w3.org/2001/XMLSchema";
                any1.MinOccurs = new decimal(0);
                any1.MaxOccurs = decimal.MaxValue;
                any1.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any1);
                global::System.Xml.Schema.XmlSchemaAny any2 = new global::System.Xml.Schema.XmlSchemaAny();
                any2.Namespace = "urn:schemas-microsoft-com:xml-diffgram-v1";
                any2.MinOccurs = new decimal(1);
                any2.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any2);
                global::System.Xml.Schema.XmlSchemaAttribute attribute1 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute1.Name = "namespace";
                attribute1.FixedValue = ds.Namespace;
                type.Attributes.Add(attribute1);
                global::System.Xml.Schema.XmlSchemaAttribute attribute2 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute2.Name = "tableTypeName";
                attribute2.FixedValue = "InstalovaneAktualizaceDataTable";
                type.Attributes.Add(attribute2);
                type.Particle = sequence;
                global::System.Xml.Schema.XmlSchema dsSchema = ds.GetSchemaSerializable();
                if (xs.Contains(dsSchema.TargetNamespace)) {
                    global::System.IO.MemoryStream s1 = new global::System.IO.MemoryStream();
                    global::System.IO.MemoryStream s2 = new global::System.IO.MemoryStream();
                    try {
                        global::System.Xml.Schema.XmlSchema schema = null;
                        dsSchema.Write(s1);
                        for (global::System.Collections.IEnumerator schemas = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator(); schemas.MoveNext(); ) {
                            schema = ((global::System.Xml.Schema.XmlSchema)(schemas.Current));
                            s2.SetLength(0);
                            schema.Write(s2);
                            if ((s1.Length == s2.Length)) {
                                s1.Position = 0;
                                s2.Position = 0;
                                for (; ((s1.Position != s1.Length) 
                                            && (s1.ReadByte() == s2.ReadByte())); ) {
                                    ;
                                }
                                if ((s1.Position == s1.Length)) {
                                    return type;
                                }
                            }
                        }
                    }
                    finally {
                        if ((s1 != null)) {
                            s1.Close();
                        }
                        if ((s2 != null)) {
                            s2.Close();
                        }
                    }
                }
                xs.Add(dsSchema);
                return type;
            }
        }
        
        /// <summary>
        ///Represents strongly named DataRow class.
        ///</summary>
        public partial class AktualizaceRow : global::System.Data.DataRow {
            
            private AktualizaceDataTable tableAktualizace;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            internal AktualizaceRow(global::System.Data.DataRowBuilder rb) : 
                    base(rb) {
                this.tableAktualizace = ((AktualizaceDataTable)(this.Table));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public int IDAktualizace {
                get {
                    return ((int)(this[this.tableAktualizace.IDAktualizaceColumn]));
                }
                set {
                    this[this.tableAktualizace.IDAktualizaceColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public string FunkceZarizeni {
                get {
                    return ((string)(this[this.tableAktualizace.FunkceZarizeniColumn]));
                }
                set {
                    this[this.tableAktualizace.FunkceZarizeniColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public string Nazev {
                get {
                    return ((string)(this[this.tableAktualizace.NazevColumn]));
                }
                set {
                    this[this.tableAktualizace.NazevColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public string Script {
                get {
                    return ((string)(this[this.tableAktualizace.ScriptColumn]));
                }
                set {
                    this[this.tableAktualizace.ScriptColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public string Soubory {
                get {
                    try {
                        return ((string)(this[this.tableAktualizace.SouboryColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Soubory\' in table \'Aktualizace\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableAktualizace.SouboryColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public bool Expirovano {
                get {
                    try {
                        return ((bool)(this[this.tableAktualizace.ExpirovanoColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Expirovano\' in table \'Aktualizace\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableAktualizace.ExpirovanoColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public int NahrazenoAktualizaci {
                get {
                    try {
                        return ((int)(this[this.tableAktualizace.NahrazenoAktualizaciColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'NahrazenoAktualizaci\' in table \'Aktualizace\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableAktualizace.NahrazenoAktualizaciColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public AktualizaceRow AktualizaceRowParent {
                get {
                    return ((AktualizaceRow)(this.GetParentRow(this.Table.ParentRelations["Aktualizace_Aktualizace"])));
                }
                set {
                    this.SetParentRow(value, this.Table.ParentRelations["Aktualizace_Aktualizace"]);
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public bool IsSouboryNull() {
                return this.IsNull(this.tableAktualizace.SouboryColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public void SetSouboryNull() {
                this[this.tableAktualizace.SouboryColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public bool IsExpirovanoNull() {
                return this.IsNull(this.tableAktualizace.ExpirovanoColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public void SetExpirovanoNull() {
                this[this.tableAktualizace.ExpirovanoColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public bool IsNahrazenoAktualizaciNull() {
                return this.IsNull(this.tableAktualizace.NahrazenoAktualizaciColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public void SetNahrazenoAktualizaciNull() {
                this[this.tableAktualizace.NahrazenoAktualizaciColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public AktualizaceDavkyRow[] GetAktualizaceDavkyRows() {
                if ((this.Table.ChildRelations["FK_AktualizaceDavky_Aktualizace"] == null)) {
                    return new AktualizaceDavkyRow[0];
                }
                else {
                    return ((AktualizaceDavkyRow[])(base.GetChildRows(this.Table.ChildRelations["FK_AktualizaceDavky_Aktualizace"])));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public AktualizaceRow[] GetAktualizaceRows() {
                if ((this.Table.ChildRelations["Aktualizace_Aktualizace"] == null)) {
                    return new AktualizaceRow[0];
                }
                else {
                    return ((AktualizaceRow[])(base.GetChildRows(this.Table.ChildRelations["Aktualizace_Aktualizace"])));
                }
            }
        }
        
        /// <summary>
        ///Represents strongly named DataRow class.
        ///</summary>
        public partial class AktualizacePrubehRow : global::System.Data.DataRow {
            
            private AktualizacePrubehDataTable tableAktualizacePrubeh;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            internal AktualizacePrubehRow(global::System.Data.DataRowBuilder rb) : 
                    base(rb) {
                this.tableAktualizacePrubeh = ((AktualizacePrubehDataTable)(this.Table));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public int IDAktualizacePrubeh {
                get {
                    return ((int)(this[this.tableAktualizacePrubeh.IDAktualizacePrubehColumn]));
                }
                set {
                    this[this.tableAktualizacePrubeh.IDAktualizacePrubehColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public string LogInfo {
                get {
                    try {
                        return ((string)(this[this.tableAktualizacePrubeh.LogInfoColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'LogInfo\' in table \'AktualizacePrubeh\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableAktualizacePrubeh.LogInfoColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public int Stav {
                get {
                    return ((int)(this[this.tableAktualizacePrubeh.StavColumn]));
                }
                set {
                    this[this.tableAktualizacePrubeh.StavColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public int IDDavky {
                get {
                    return ((int)(this[this.tableAktualizacePrubeh.IDDavkyColumn]));
                }
                set {
                    this[this.tableAktualizacePrubeh.IDDavkyColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public System.DateTime Datum {
                get {
                    return ((global::System.DateTime)(this[this.tableAktualizacePrubeh.DatumColumn]));
                }
                set {
                    this[this.tableAktualizacePrubeh.DatumColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public string PopisPC {
                get {
                    return ((string)(this[this.tableAktualizacePrubeh.PopisPCColumn]));
                }
                set {
                    this[this.tableAktualizacePrubeh.PopisPCColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public string IPAdresa {
                get {
                    return ((string)(this[this.tableAktualizacePrubeh.IPAdresaColumn]));
                }
                set {
                    this[this.tableAktualizacePrubeh.IPAdresaColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public string IdZarizeni {
                get {
                    return ((string)(this[this.tableAktualizacePrubeh.IdZarizeniColumn]));
                }
                set {
                    this[this.tableAktualizacePrubeh.IdZarizeniColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public AktualizaceDavkyRow AktualizaceDavkyRow {
                get {
                    return ((AktualizaceDavkyRow)(this.GetParentRow(this.Table.ParentRelations["FK_AktualizacePrubeh_AktualizaceDavky"])));
                }
                set {
                    this.SetParentRow(value, this.Table.ParentRelations["FK_AktualizacePrubeh_AktualizaceDavky"]);
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public InstalovaneAktualizaceRow InstalovaneAktualizaceRow {
                get {
                    return ((InstalovaneAktualizaceRow)(this.GetParentRow(this.Table.ParentRelations["InstalovaneAktualizace_AktualizacePrubeh"])));
                }
                set {
                    this.SetParentRow(value, this.Table.ParentRelations["InstalovaneAktualizace_AktualizacePrubeh"]);
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public bool IsLogInfoNull() {
                return this.IsNull(this.tableAktualizacePrubeh.LogInfoColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public void SetLogInfoNull() {
                this[this.tableAktualizacePrubeh.LogInfoColumn] = global::System.Convert.DBNull;
            }
        }
        
        /// <summary>
        ///Represents strongly named DataRow class.
        ///</summary>
        public partial class AktualizaceDavkyRow : global::System.Data.DataRow {
            
            private AktualizaceDavkyDataTable tableAktualizaceDavky;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            internal AktualizaceDavkyRow(global::System.Data.DataRowBuilder rb) : 
                    base(rb) {
                this.tableAktualizaceDavky = ((AktualizaceDavkyDataTable)(this.Table));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public int IDDavky {
                get {
                    return ((int)(this[this.tableAktualizaceDavky.IDDavkyColumn]));
                }
                set {
                    this[this.tableAktualizaceDavky.IDDavkyColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public int IDAktualizace {
                get {
                    return ((int)(this[this.tableAktualizaceDavky.IDAktualizaceColumn]));
                }
                set {
                    this[this.tableAktualizaceDavky.IDAktualizaceColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public string Operace {
                get {
                    return ((string)(this[this.tableAktualizaceDavky.OperaceColumn]));
                }
                set {
                    this[this.tableAktualizaceDavky.OperaceColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public bool Vyzadovano {
                get {
                    return ((bool)(this[this.tableAktualizaceDavky.VyzadovanoColumn]));
                }
                set {
                    this[this.tableAktualizaceDavky.VyzadovanoColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public System.DateTime DobaInstalace {
                get {
                    try {
                        return ((global::System.DateTime)(this[this.tableAktualizaceDavky.DobaInstalaceColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'DobaInstalace\' in table \'AktualizaceDavky\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableAktualizaceDavky.DobaInstalaceColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public string Parametry {
                get {
                    try {
                        return ((string)(this[this.tableAktualizaceDavky.ParametryColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Parametry\' in table \'AktualizaceDavky\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableAktualizaceDavky.ParametryColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public double Priorita {
                get {
                    try {
                        return ((double)(this[this.tableAktualizaceDavky.PrioritaColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Priorita\' in table \'AktualizaceDavky\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableAktualizaceDavky.PrioritaColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public bool Spoustet {
                get {
                    return ((bool)(this[this.tableAktualizaceDavky.SpoustetColumn]));
                }
                set {
                    this[this.tableAktualizaceDavky.SpoustetColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public AktualizaceRow AktualizaceRow {
                get {
                    return ((AktualizaceRow)(this.GetParentRow(this.Table.ParentRelations["FK_AktualizaceDavky_Aktualizace"])));
                }
                set {
                    this.SetParentRow(value, this.Table.ParentRelations["FK_AktualizaceDavky_Aktualizace"]);
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public bool IsDobaInstalaceNull() {
                return this.IsNull(this.tableAktualizaceDavky.DobaInstalaceColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public void SetDobaInstalaceNull() {
                this[this.tableAktualizaceDavky.DobaInstalaceColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public bool IsParametryNull() {
                return this.IsNull(this.tableAktualizaceDavky.ParametryColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public void SetParametryNull() {
                this[this.tableAktualizaceDavky.ParametryColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public bool IsPrioritaNull() {
                return this.IsNull(this.tableAktualizaceDavky.PrioritaColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public void SetPrioritaNull() {
                this[this.tableAktualizaceDavky.PrioritaColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public AktualizacePrubehRow[] GetAktualizacePrubehRows() {
                if ((this.Table.ChildRelations["FK_AktualizacePrubeh_AktualizaceDavky"] == null)) {
                    return new AktualizacePrubehRow[0];
                }
                else {
                    return ((AktualizacePrubehRow[])(base.GetChildRows(this.Table.ChildRelations["FK_AktualizacePrubeh_AktualizaceDavky"])));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public AktualizaceDavky_CFZarizeniRow[] GetAktualizaceDavky_CFZarizeniRows() {
                if ((this.Table.ChildRelations["FK_AktualizaceDavky_CFZarizeni_AktualizaceDavky"] == null)) {
                    return new AktualizaceDavky_CFZarizeniRow[0];
                }
                else {
                    return ((AktualizaceDavky_CFZarizeniRow[])(base.GetChildRows(this.Table.ChildRelations["FK_AktualizaceDavky_CFZarizeni_AktualizaceDavky"])));
                }
            }
        }
        
        /// <summary>
        ///Represents strongly named DataRow class.
        ///</summary>
        public partial class AktualizaceDavky_CFZarizeniRow : global::System.Data.DataRow {
            
            private AktualizaceDavky_CFZarizeniDataTable tableAktualizaceDavky_CFZarizeni;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            internal AktualizaceDavky_CFZarizeniRow(global::System.Data.DataRowBuilder rb) : 
                    base(rb) {
                this.tableAktualizaceDavky_CFZarizeni = ((AktualizaceDavky_CFZarizeniDataTable)(this.Table));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public int IDDavky {
                get {
                    return ((int)(this[this.tableAktualizaceDavky_CFZarizeni.IDDavkyColumn]));
                }
                set {
                    this[this.tableAktualizaceDavky_CFZarizeni.IDDavkyColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public short IDZarizeni {
                get {
                    return ((short)(this[this.tableAktualizaceDavky_CFZarizeni.IDZarizeniColumn]));
                }
                set {
                    this[this.tableAktualizaceDavky_CFZarizeni.IDZarizeniColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public AktualizaceDavkyRow AktualizaceDavkyRow {
                get {
                    return ((AktualizaceDavkyRow)(this.GetParentRow(this.Table.ParentRelations["FK_AktualizaceDavky_CFZarizeni_AktualizaceDavky"])));
                }
                set {
                    this.SetParentRow(value, this.Table.ParentRelations["FK_AktualizaceDavky_CFZarizeni_AktualizaceDavky"]);
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public CFZarizeniRow CFZarizeniRow {
                get {
                    return ((CFZarizeniRow)(this.GetParentRow(this.Table.ParentRelations["FK_AktualizaceDavky_CFZarizeni_CFZarizeni"])));
                }
                set {
                    this.SetParentRow(value, this.Table.ParentRelations["FK_AktualizaceDavky_CFZarizeni_CFZarizeni"]);
                }
            }
        }
        
        /// <summary>
        ///Represents strongly named DataRow class.
        ///</summary>
        public partial class CFZarizeniRow : global::System.Data.DataRow {
            
            private CFZarizeniDataTable tableCFZarizeni;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            internal CFZarizeniRow(global::System.Data.DataRowBuilder rb) : 
                    base(rb) {
                this.tableCFZarizeni = ((CFZarizeniDataTable)(this.Table));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public short id_zarizeni {
                get {
                    return ((short)(this[this.tableCFZarizeni.id_zarizeniColumn]));
                }
                set {
                    this[this.tableCFZarizeni.id_zarizeniColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public short id_funkce {
                get {
                    return ((short)(this[this.tableCFZarizeni.id_funkceColumn]));
                }
                set {
                    this[this.tableCFZarizeni.id_funkceColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public short pouzito {
                get {
                    try {
                        return ((short)(this[this.tableCFZarizeni.pouzitoColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'pouzito\' in table \'CFZarizeni\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableCFZarizeni.pouzitoColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public short aktivni {
                get {
                    try {
                        return ((short)(this[this.tableCFZarizeni.aktivniColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'aktivni\' in table \'CFZarizeni\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableCFZarizeni.aktivniColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public string popis {
                get {
                    return ((string)(this[this.tableCFZarizeni.popisColumn]));
                }
                set {
                    this[this.tableCFZarizeni.popisColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public short id_jidelna {
                get {
                    return ((short)(this[this.tableCFZarizeni.id_jidelnaColumn]));
                }
                set {
                    this[this.tableCFZarizeni.id_jidelnaColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public short id_vydejna {
                get {
                    return ((short)(this[this.tableCFZarizeni.id_vydejnaColumn]));
                }
                set {
                    this[this.tableCFZarizeni.id_vydejnaColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public System.DateTime last_contact {
                get {
                    try {
                        return ((global::System.DateTime)(this[this.tableCFZarizeni.last_contactColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'last_contact\' in table \'CFZarizeni\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableCFZarizeni.last_contactColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public string stav_queue {
                get {
                    try {
                        return ((string)(this[this.tableCFZarizeni.stav_queueColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'stav_queue\' in table \'CFZarizeni\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableCFZarizeni.stav_queueColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public short id_sklad {
                get {
                    try {
                        return ((short)(this[this.tableCFZarizeni.id_skladColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'id_sklad\' in table \'CFZarizeni\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableCFZarizeni.id_skladColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public bool IspouzitoNull() {
                return this.IsNull(this.tableCFZarizeni.pouzitoColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public void SetpouzitoNull() {
                this[this.tableCFZarizeni.pouzitoColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public bool IsaktivniNull() {
                return this.IsNull(this.tableCFZarizeni.aktivniColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public void SetaktivniNull() {
                this[this.tableCFZarizeni.aktivniColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public bool Islast_contactNull() {
                return this.IsNull(this.tableCFZarizeni.last_contactColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public void Setlast_contactNull() {
                this[this.tableCFZarizeni.last_contactColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public bool Isstav_queueNull() {
                return this.IsNull(this.tableCFZarizeni.stav_queueColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public void Setstav_queueNull() {
                this[this.tableCFZarizeni.stav_queueColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public bool Isid_skladNull() {
                return this.IsNull(this.tableCFZarizeni.id_skladColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public void Setid_skladNull() {
                this[this.tableCFZarizeni.id_skladColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public AktualizaceDavky_CFZarizeniRow[] GetAktualizaceDavky_CFZarizeniRows() {
                if ((this.Table.ChildRelations["FK_AktualizaceDavky_CFZarizeni_CFZarizeni"] == null)) {
                    return new AktualizaceDavky_CFZarizeniRow[0];
                }
                else {
                    return ((AktualizaceDavky_CFZarizeniRow[])(base.GetChildRows(this.Table.ChildRelations["FK_AktualizaceDavky_CFZarizeni_CFZarizeni"])));
                }
            }
        }
        
        /// <summary>
        ///Represents strongly named DataRow class.
        ///</summary>
        public partial class SaveAktualizacePrubehRow : global::System.Data.DataRow {
            
            private SaveAktualizacePrubehDataTable tableSaveAktualizacePrubeh;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            internal SaveAktualizacePrubehRow(global::System.Data.DataRowBuilder rb) : 
                    base(rb) {
                this.tableSaveAktualizacePrubeh = ((SaveAktualizacePrubehDataTable)(this.Table));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public int IDAktualizacePrubeh {
                get {
                    return ((int)(this[this.tableSaveAktualizacePrubeh.IDAktualizacePrubehColumn]));
                }
                set {
                    this[this.tableSaveAktualizacePrubeh.IDAktualizacePrubehColumn] = value;
                }
            }
        }
        
        /// <summary>
        ///Represents strongly named DataRow class.
        ///</summary>
        public partial class InstalovaneAktualizaceRow : global::System.Data.DataRow {
            
            private InstalovaneAktualizaceDataTable tableInstalovaneAktualizace;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            internal InstalovaneAktualizaceRow(global::System.Data.DataRowBuilder rb) : 
                    base(rb) {
                this.tableInstalovaneAktualizace = ((InstalovaneAktualizaceDataTable)(this.Table));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public int IDAktualizacePrubeh {
                get {
                    return ((int)(this[this.tableInstalovaneAktualizace.IDAktualizacePrubehColumn]));
                }
                set {
                    this[this.tableInstalovaneAktualizace.IDAktualizacePrubehColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public AktualizacePrubehRow[] GetAktualizacePrubehRows() {
                if ((this.Table.ChildRelations["InstalovaneAktualizace_AktualizacePrubeh"] == null)) {
                    return new AktualizacePrubehRow[0];
                }
                else {
                    return ((AktualizacePrubehRow[])(base.GetChildRows(this.Table.ChildRelations["InstalovaneAktualizace_AktualizacePrubeh"])));
                }
            }
        }
        
        /// <summary>
        ///Row event argument class
        ///</summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        public class AktualizaceRowChangeEvent : global::System.EventArgs {
            
            private AktualizaceRow eventRow;
            
            private global::System.Data.DataRowAction eventAction;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public AktualizaceRowChangeEvent(AktualizaceRow row, global::System.Data.DataRowAction action) {
                this.eventRow = row;
                this.eventAction = action;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public AktualizaceRow Row {
                get {
                    return this.eventRow;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataRowAction Action {
                get {
                    return this.eventAction;
                }
            }
        }
        
        /// <summary>
        ///Row event argument class
        ///</summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        public class AktualizacePrubehRowChangeEvent : global::System.EventArgs {
            
            private AktualizacePrubehRow eventRow;
            
            private global::System.Data.DataRowAction eventAction;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public AktualizacePrubehRowChangeEvent(AktualizacePrubehRow row, global::System.Data.DataRowAction action) {
                this.eventRow = row;
                this.eventAction = action;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public AktualizacePrubehRow Row {
                get {
                    return this.eventRow;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataRowAction Action {
                get {
                    return this.eventAction;
                }
            }
        }
        
        /// <summary>
        ///Row event argument class
        ///</summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        public class AktualizaceDavkyRowChangeEvent : global::System.EventArgs {
            
            private AktualizaceDavkyRow eventRow;
            
            private global::System.Data.DataRowAction eventAction;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public AktualizaceDavkyRowChangeEvent(AktualizaceDavkyRow row, global::System.Data.DataRowAction action) {
                this.eventRow = row;
                this.eventAction = action;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public AktualizaceDavkyRow Row {
                get {
                    return this.eventRow;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataRowAction Action {
                get {
                    return this.eventAction;
                }
            }
        }
        
        /// <summary>
        ///Row event argument class
        ///</summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        public class AktualizaceDavky_CFZarizeniRowChangeEvent : global::System.EventArgs {
            
            private AktualizaceDavky_CFZarizeniRow eventRow;
            
            private global::System.Data.DataRowAction eventAction;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public AktualizaceDavky_CFZarizeniRowChangeEvent(AktualizaceDavky_CFZarizeniRow row, global::System.Data.DataRowAction action) {
                this.eventRow = row;
                this.eventAction = action;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public AktualizaceDavky_CFZarizeniRow Row {
                get {
                    return this.eventRow;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataRowAction Action {
                get {
                    return this.eventAction;
                }
            }
        }
        
        /// <summary>
        ///Row event argument class
        ///</summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        public class CFZarizeniRowChangeEvent : global::System.EventArgs {
            
            private CFZarizeniRow eventRow;
            
            private global::System.Data.DataRowAction eventAction;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public CFZarizeniRowChangeEvent(CFZarizeniRow row, global::System.Data.DataRowAction action) {
                this.eventRow = row;
                this.eventAction = action;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public CFZarizeniRow Row {
                get {
                    return this.eventRow;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataRowAction Action {
                get {
                    return this.eventAction;
                }
            }
        }
        
        /// <summary>
        ///Row event argument class
        ///</summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        public class SaveAktualizacePrubehRowChangeEvent : global::System.EventArgs {
            
            private SaveAktualizacePrubehRow eventRow;
            
            private global::System.Data.DataRowAction eventAction;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public SaveAktualizacePrubehRowChangeEvent(SaveAktualizacePrubehRow row, global::System.Data.DataRowAction action) {
                this.eventRow = row;
                this.eventAction = action;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public SaveAktualizacePrubehRow Row {
                get {
                    return this.eventRow;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataRowAction Action {
                get {
                    return this.eventAction;
                }
            }
        }
        
        /// <summary>
        ///Row event argument class
        ///</summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        public class InstalovaneAktualizaceRowChangeEvent : global::System.EventArgs {
            
            private InstalovaneAktualizaceRow eventRow;
            
            private global::System.Data.DataRowAction eventAction;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public InstalovaneAktualizaceRowChangeEvent(InstalovaneAktualizaceRow row, global::System.Data.DataRowAction action) {
                this.eventRow = row;
                this.eventAction = action;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public InstalovaneAktualizaceRow Row {
                get {
                    return this.eventRow;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public global::System.Data.DataRowAction Action {
                get {
                    return this.eventAction;
                }
            }
        }
    }
}
namespace Anete.AutoUpgrades.AutoUpgradeDsTableAdapters {
    
    
    /// <summary>
    ///Represents the connection and commands used to retrieve and save data.
    ///</summary>
    [global::System.ComponentModel.DesignerCategoryAttribute("code")]
    [global::System.ComponentModel.ToolboxItem(true)]
    [global::System.ComponentModel.DataObjectAttribute(true)]
    [global::System.ComponentModel.DesignerAttribute("Microsoft.VSDesigner.DataSource.Design.TableAdapterDesigner, Microsoft.VSDesigner" +
        ", Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a")]
    [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
    public partial class AktualizaceTableAdapter : global::Anete.Common.Data.Interface.CentralServerTableAdapter {
        
        private global::System.Data.SqlClient.SqlDataAdapter _adapter;
        
        private global::System.Data.SqlClient.SqlConnection _connection;
        
        private global::System.Data.SqlClient.SqlTransaction _transaction;
        
        private global::System.Data.SqlClient.SqlCommand[] _commandCollection;
        
        private bool _clearBeforeFill;
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        public AktualizaceTableAdapter() {
            this.ClearBeforeFill = true;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        protected internal global::System.Data.SqlClient.SqlDataAdapter Adapter {
            get {
                if ((this._adapter == null)) {
                    this.InitAdapter();
                }
                return this._adapter;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        internal global::System.Data.SqlClient.SqlConnection Connection {
            get {
                if ((this._connection == null)) {
                    this.InitConnection();
                }
                return this._connection;
            }
            set {
                this._connection = value;
                if ((this.Adapter.InsertCommand != null)) {
                    this.Adapter.InsertCommand.Connection = value;
                }
                if ((this.Adapter.DeleteCommand != null)) {
                    this.Adapter.DeleteCommand.Connection = value;
                }
                if ((this.Adapter.UpdateCommand != null)) {
                    this.Adapter.UpdateCommand.Connection = value;
                }
                for (int i = 0; (i < this.CommandCollection.Length); i = (i + 1)) {
                    if ((this.CommandCollection[i] != null)) {
                        ((global::System.Data.SqlClient.SqlCommand)(this.CommandCollection[i])).Connection = value;
                    }
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        internal global::System.Data.SqlClient.SqlTransaction Transaction {
            get {
                return this._transaction;
            }
            set {
                this._transaction = value;
                for (int i = 0; (i < this.CommandCollection.Length); i = (i + 1)) {
                    this.CommandCollection[i].Transaction = this._transaction;
                }
                if (((this.Adapter != null) 
                            && (this.Adapter.DeleteCommand != null))) {
                    this.Adapter.DeleteCommand.Transaction = this._transaction;
                }
                if (((this.Adapter != null) 
                            && (this.Adapter.InsertCommand != null))) {
                    this.Adapter.InsertCommand.Transaction = this._transaction;
                }
                if (((this.Adapter != null) 
                            && (this.Adapter.UpdateCommand != null))) {
                    this.Adapter.UpdateCommand.Transaction = this._transaction;
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        protected global::System.Data.SqlClient.SqlCommand[] CommandCollection {
            get {
                if ((this._commandCollection == null)) {
                    this.InitCommandCollection();
                }
                return this._commandCollection;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        public bool ClearBeforeFill {
            get {
                return this._clearBeforeFill;
            }
            set {
                this._clearBeforeFill = value;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        private void InitAdapter() {
            this._adapter = new global::System.Data.SqlClient.SqlDataAdapter();
            global::System.Data.Common.DataTableMapping tableMapping = new global::System.Data.Common.DataTableMapping();
            tableMapping.SourceTable = "Table";
            tableMapping.DataSetTable = "Aktualizace";
            tableMapping.ColumnMappings.Add("IDAktualizace", "IDAktualizace");
            tableMapping.ColumnMappings.Add("FunkceZarizeni", "FunkceZarizeni");
            tableMapping.ColumnMappings.Add("Nazev", "Nazev");
            tableMapping.ColumnMappings.Add("Script", "Script");
            tableMapping.ColumnMappings.Add("Soubory", "Soubory");
            tableMapping.ColumnMappings.Add("Expirovano", "Expirovano");
            tableMapping.ColumnMappings.Add("NahrazenoAktualizaci", "NahrazenoAktualizaci");
            this._adapter.TableMappings.Add(tableMapping);
            this._adapter.DeleteCommand = new global::System.Data.SqlClient.SqlCommand();
            this._adapter.DeleteCommand.Connection = this.Connection;
            this._adapter.DeleteCommand.CommandText = "DELETE FROM [dbo].[Aktualizace] WHERE (([IDAktualizace] = @Original_IDAktualizace" +
                "))";
            this._adapter.DeleteCommand.CommandType = global::System.Data.CommandType.Text;
            this._adapter.DeleteCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_IDAktualizace", global::System.Data.SqlDbType.Int, 0, global::System.Data.ParameterDirection.Input, 0, 0, "IDAktualizace", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
            this._adapter.InsertCommand = new global::System.Data.SqlClient.SqlCommand();
            this._adapter.InsertCommand.Connection = this.Connection;
            this._adapter.InsertCommand.CommandText = @"INSERT INTO dbo.Aktualizace
                      (Nazev, Script, NahrazenoAktualizaci, FunkceZarizeni, Soubory)
VALUES     (@Nazev,@Script,@NahrazenoAktualizaci,@FunkceZarizeni,@Soubory); 
SELECT '' AS Soubory, CAST((CASE WHEN Soubory IS NULL THEN 1 ELSE 0 END) AS bit) AS Expirovano, Nazev, Script, NahrazenoAktualizaci, IDAktualizace, FunkceZarizeni FROM dbo.Aktualizace WHERE (IDAktualizace = SCOPE_IDENTITY())";
            this._adapter.InsertCommand.CommandType = global::System.Data.CommandType.Text;
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Nazev", global::System.Data.SqlDbType.VarChar, 256, global::System.Data.ParameterDirection.Input, 0, 0, "Nazev", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Script", global::System.Data.SqlDbType.Text, 2147483647, global::System.Data.ParameterDirection.Input, 0, 0, "Script", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@NahrazenoAktualizaci", global::System.Data.SqlDbType.Int, 4, global::System.Data.ParameterDirection.Input, 0, 0, "NahrazenoAktualizaci", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@FunkceZarizeni", global::System.Data.SqlDbType.VarChar, 256, global::System.Data.ParameterDirection.Input, 0, 0, "FunkceZarizeni", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Soubory", global::System.Data.SqlDbType.Text, 2147483647, global::System.Data.ParameterDirection.Input, 0, 0, "Soubory", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand = new global::System.Data.SqlClient.SqlCommand();
            this._adapter.UpdateCommand.Connection = this.Connection;
            this._adapter.UpdateCommand.CommandText = @"UPDATE    dbo.Aktualizace
SET              Nazev = @Nazev, Script = @Script, NahrazenoAktualizaci = @NahrazenoAktualizaci, FunkceZarizeni = @FunkceZarizeni, 
                      Soubory = @Soubory
WHERE     (IDAktualizace = @Original_IDAktualizace); 
SELECT '' AS Soubory, CAST((CASE WHEN Soubory IS NULL THEN 1 ELSE 0 END) AS bit) AS Expirovano, Nazev, Script, NahrazenoAktualizaci, IDAktualizace, FunkceZarizeni FROM dbo.Aktualizace WHERE (IDAktualizace = @IDAktualizace)";
            this._adapter.UpdateCommand.CommandType = global::System.Data.CommandType.Text;
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Nazev", global::System.Data.SqlDbType.VarChar, 256, global::System.Data.ParameterDirection.Input, 0, 0, "Nazev", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Script", global::System.Data.SqlDbType.Text, 2147483647, global::System.Data.ParameterDirection.Input, 0, 0, "Script", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@NahrazenoAktualizaci", global::System.Data.SqlDbType.Int, 4, global::System.Data.ParameterDirection.Input, 0, 0, "NahrazenoAktualizaci", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@FunkceZarizeni", global::System.Data.SqlDbType.VarChar, 256, global::System.Data.ParameterDirection.Input, 0, 0, "FunkceZarizeni", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Soubory", global::System.Data.SqlDbType.Text, 2147483647, global::System.Data.ParameterDirection.Input, 0, 0, "Soubory", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_IDAktualizace", global::System.Data.SqlDbType.Int, 4, global::System.Data.ParameterDirection.Input, 0, 0, "IDAktualizace", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@IDAktualizace", global::System.Data.SqlDbType.Int, 4, global::System.Data.ParameterDirection.Input, 0, 0, "IDAktualizace", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        private void InitConnection() {
            this._connection = new global::System.Data.SqlClient.SqlConnection();
            this._connection.ConnectionString = global::Anete.AutoUpgrades.Properties.Settings.Default.AneteUpdatesConnectionString;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        private void InitCommandCollection() {
            this._commandCollection = new global::System.Data.SqlClient.SqlCommand[2];
            this._commandCollection[0] = new global::System.Data.SqlClient.SqlCommand();
            this._commandCollection[0].Connection = this.Connection;
            this._commandCollection[0].CommandText = "SELECT     \'\' AS Soubory, CAST((CASE WHEN Soubory IS NULL THEN 1 ELSE 0 END) AS b" +
                "it) AS Expirovano, Nazev, Script, NahrazenoAktualizaci, IDAktualizace, \r\n       " +
                "               FunkceZarizeni\r\nFROM         dbo.Aktualizace";
            this._commandCollection[0].CommandType = global::System.Data.CommandType.Text;
            this._commandCollection[1] = new global::System.Data.SqlClient.SqlCommand();
            this._commandCollection[1].Connection = this.Connection;
            this._commandCollection[1].CommandText = "SELECT     FunkceZarizeni, IDAktualizace, NahrazenoAktualizaci, Nazev, Script, So" +
                "ubory\r\nFROM         dbo.Aktualizace\r\nWHERE     (IDAktualizace = @IdAktualizace)";
            this._commandCollection[1].CommandType = global::System.Data.CommandType.Text;
            this._commandCollection[1].Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@IdAktualizace", global::System.Data.SqlDbType.Int, 4, global::System.Data.ParameterDirection.Input, 0, 0, "IDAktualizace", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Fill, true)]
        public virtual int Fill(AutoUpgradeDs.AktualizaceDataTable dataTable) {
            this.Adapter.SelectCommand = this.CommandCollection[0];
            if ((this.ClearBeforeFill == true)) {
                dataTable.Clear();
            }
            int returnValue = this.Adapter.Fill(dataTable);
            return returnValue;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Fill, false)]
        public virtual int FillWithSouboryByIdAktualizace(AutoUpgradeDs.AktualizaceDataTable dataTable, int IdAktualizace) {
            this.Adapter.SelectCommand = this.CommandCollection[1];
            this.Adapter.SelectCommand.Parameters[0].Value = ((int)(IdAktualizace));
            if ((this.ClearBeforeFill == true)) {
                dataTable.Clear();
            }
            int returnValue = this.Adapter.Fill(dataTable);
            return returnValue;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Select, false)]
        public virtual AutoUpgradeDs.AktualizaceDataTable GetDataWithSouboryByIdAktualizace(int IdAktualizace) {
            this.Adapter.SelectCommand = this.CommandCollection[1];
            this.Adapter.SelectCommand.Parameters[0].Value = ((int)(IdAktualizace));
            AutoUpgradeDs.AktualizaceDataTable dataTable = new AutoUpgradeDs.AktualizaceDataTable();
            this.Adapter.Fill(dataTable);
            return dataTable;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        public virtual int Update(AutoUpgradeDs.AktualizaceDataTable dataTable) {
            return this.Adapter.Update(dataTable);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        public virtual int Update(AutoUpgradeDs dataSet) {
            return this.Adapter.Update(dataSet, "Aktualizace");
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        public virtual int Update(global::System.Data.DataRow dataRow) {
            return this.Adapter.Update(new global::System.Data.DataRow[] {
                        dataRow});
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        public virtual int Update(global::System.Data.DataRow[] dataRows) {
            return this.Adapter.Update(dataRows);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Delete, true)]
        public virtual int Delete(int Original_IDAktualizace) {
            this.Adapter.DeleteCommand.Parameters[0].Value = ((int)(Original_IDAktualizace));
            global::System.Data.ConnectionState previousConnectionState = this.Adapter.DeleteCommand.Connection.State;
            if (((this.Adapter.DeleteCommand.Connection.State & global::System.Data.ConnectionState.Open) 
                        != global::System.Data.ConnectionState.Open)) {
                this.Adapter.DeleteCommand.Connection.Open();
            }
            try {
                int returnValue = this.Adapter.DeleteCommand.ExecuteNonQuery();
                return returnValue;
            }
            finally {
                if ((previousConnectionState == global::System.Data.ConnectionState.Closed)) {
                    this.Adapter.DeleteCommand.Connection.Close();
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Insert, true)]
        public virtual int Insert(string Nazev, string Script, global::System.Nullable<int> NahrazenoAktualizaci, string FunkceZarizeni, string Soubory) {
            if ((Nazev == null)) {
                throw new global::System.ArgumentNullException("Nazev");
            }
            else {
                this.Adapter.InsertCommand.Parameters[0].Value = ((string)(Nazev));
            }
            if ((Script == null)) {
                throw new global::System.ArgumentNullException("Script");
            }
            else {
                this.Adapter.InsertCommand.Parameters[1].Value = ((string)(Script));
            }
            if ((NahrazenoAktualizaci.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[2].Value = ((int)(NahrazenoAktualizaci.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[2].Value = global::System.DBNull.Value;
            }
            if ((FunkceZarizeni == null)) {
                throw new global::System.ArgumentNullException("FunkceZarizeni");
            }
            else {
                this.Adapter.InsertCommand.Parameters[3].Value = ((string)(FunkceZarizeni));
            }
            if ((Soubory == null)) {
                this.Adapter.InsertCommand.Parameters[4].Value = global::System.DBNull.Value;
            }
            else {
                this.Adapter.InsertCommand.Parameters[4].Value = ((string)(Soubory));
            }
            global::System.Data.ConnectionState previousConnectionState = this.Adapter.InsertCommand.Connection.State;
            if (((this.Adapter.InsertCommand.Connection.State & global::System.Data.ConnectionState.Open) 
                        != global::System.Data.ConnectionState.Open)) {
                this.Adapter.InsertCommand.Connection.Open();
            }
            try {
                int returnValue = this.Adapter.InsertCommand.ExecuteNonQuery();
                return returnValue;
            }
            finally {
                if ((previousConnectionState == global::System.Data.ConnectionState.Closed)) {
                    this.Adapter.InsertCommand.Connection.Close();
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Update, true)]
        public virtual int Update(string Nazev, string Script, global::System.Nullable<int> NahrazenoAktualizaci, string FunkceZarizeni, string Soubory, int Original_IDAktualizace, int IDAktualizace) {
            if ((Nazev == null)) {
                throw new global::System.ArgumentNullException("Nazev");
            }
            else {
                this.Adapter.UpdateCommand.Parameters[0].Value = ((string)(Nazev));
            }
            if ((Script == null)) {
                throw new global::System.ArgumentNullException("Script");
            }
            else {
                this.Adapter.UpdateCommand.Parameters[1].Value = ((string)(Script));
            }
            if ((NahrazenoAktualizaci.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[2].Value = ((int)(NahrazenoAktualizaci.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[2].Value = global::System.DBNull.Value;
            }
            if ((FunkceZarizeni == null)) {
                throw new global::System.ArgumentNullException("FunkceZarizeni");
            }
            else {
                this.Adapter.UpdateCommand.Parameters[3].Value = ((string)(FunkceZarizeni));
            }
            if ((Soubory == null)) {
                this.Adapter.UpdateCommand.Parameters[4].Value = global::System.DBNull.Value;
            }
            else {
                this.Adapter.UpdateCommand.Parameters[4].Value = ((string)(Soubory));
            }
            this.Adapter.UpdateCommand.Parameters[5].Value = ((int)(Original_IDAktualizace));
            this.Adapter.UpdateCommand.Parameters[6].Value = ((int)(IDAktualizace));
            global::System.Data.ConnectionState previousConnectionState = this.Adapter.UpdateCommand.Connection.State;
            if (((this.Adapter.UpdateCommand.Connection.State & global::System.Data.ConnectionState.Open) 
                        != global::System.Data.ConnectionState.Open)) {
                this.Adapter.UpdateCommand.Connection.Open();
            }
            try {
                int returnValue = this.Adapter.UpdateCommand.ExecuteNonQuery();
                return returnValue;
            }
            finally {
                if ((previousConnectionState == global::System.Data.ConnectionState.Closed)) {
                    this.Adapter.UpdateCommand.Connection.Close();
                }
            }
        }
    }
    
    /// <summary>
    ///Represents the connection and commands used to retrieve and save data.
    ///</summary>
    [global::System.ComponentModel.DesignerCategoryAttribute("code")]
    [global::System.ComponentModel.ToolboxItem(true)]
    [global::System.ComponentModel.DataObjectAttribute(true)]
    [global::System.ComponentModel.DesignerAttribute("Microsoft.VSDesigner.DataSource.Design.TableAdapterDesigner, Microsoft.VSDesigner" +
        ", Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a")]
    [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
    public partial class AktualizacePrubehTableAdapter : global::Anete.Common.Data.Interface.CentralServerTableAdapter {
        
        private global::System.Data.SqlClient.SqlDataAdapter _adapter;
        
        private global::System.Data.SqlClient.SqlConnection _connection;
        
        private global::System.Data.SqlClient.SqlTransaction _transaction;
        
        private global::System.Data.SqlClient.SqlCommand[] _commandCollection;
        
        private bool _clearBeforeFill;
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        public AktualizacePrubehTableAdapter() {
            this.ClearBeforeFill = true;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        protected internal global::System.Data.SqlClient.SqlDataAdapter Adapter {
            get {
                if ((this._adapter == null)) {
                    this.InitAdapter();
                }
                return this._adapter;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        internal global::System.Data.SqlClient.SqlConnection Connection {
            get {
                if ((this._connection == null)) {
                    this.InitConnection();
                }
                return this._connection;
            }
            set {
                this._connection = value;
                if ((this.Adapter.InsertCommand != null)) {
                    this.Adapter.InsertCommand.Connection = value;
                }
                if ((this.Adapter.DeleteCommand != null)) {
                    this.Adapter.DeleteCommand.Connection = value;
                }
                if ((this.Adapter.UpdateCommand != null)) {
                    this.Adapter.UpdateCommand.Connection = value;
                }
                for (int i = 0; (i < this.CommandCollection.Length); i = (i + 1)) {
                    if ((this.CommandCollection[i] != null)) {
                        ((global::System.Data.SqlClient.SqlCommand)(this.CommandCollection[i])).Connection = value;
                    }
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        internal global::System.Data.SqlClient.SqlTransaction Transaction {
            get {
                return this._transaction;
            }
            set {
                this._transaction = value;
                for (int i = 0; (i < this.CommandCollection.Length); i = (i + 1)) {
                    this.CommandCollection[i].Transaction = this._transaction;
                }
                if (((this.Adapter != null) 
                            && (this.Adapter.DeleteCommand != null))) {
                    this.Adapter.DeleteCommand.Transaction = this._transaction;
                }
                if (((this.Adapter != null) 
                            && (this.Adapter.InsertCommand != null))) {
                    this.Adapter.InsertCommand.Transaction = this._transaction;
                }
                if (((this.Adapter != null) 
                            && (this.Adapter.UpdateCommand != null))) {
                    this.Adapter.UpdateCommand.Transaction = this._transaction;
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        protected global::System.Data.SqlClient.SqlCommand[] CommandCollection {
            get {
                if ((this._commandCollection == null)) {
                    this.InitCommandCollection();
                }
                return this._commandCollection;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        public bool ClearBeforeFill {
            get {
                return this._clearBeforeFill;
            }
            set {
                this._clearBeforeFill = value;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        private void InitAdapter() {
            this._adapter = new global::System.Data.SqlClient.SqlDataAdapter();
            global::System.Data.Common.DataTableMapping tableMapping = new global::System.Data.Common.DataTableMapping();
            tableMapping.SourceTable = "Table";
            tableMapping.DataSetTable = "AktualizacePrubeh";
            tableMapping.ColumnMappings.Add("IDAktualizacePrubeh", "IDAktualizacePrubeh");
            tableMapping.ColumnMappings.Add("LogInfo", "LogInfo");
            tableMapping.ColumnMappings.Add("Stav", "Stav");
            tableMapping.ColumnMappings.Add("IDDavky", "IDDavky");
            tableMapping.ColumnMappings.Add("Datum", "Datum");
            tableMapping.ColumnMappings.Add("PopisPC", "PopisPC");
            tableMapping.ColumnMappings.Add("IPAdresa", "IPAdresa");
            tableMapping.ColumnMappings.Add("IdZarizeni", "IdZarizeni");
            this._adapter.TableMappings.Add(tableMapping);
            this._adapter.DeleteCommand = new global::System.Data.SqlClient.SqlCommand();
            this._adapter.DeleteCommand.Connection = this.Connection;
            this._adapter.DeleteCommand.CommandText = "DELETE FROM [dbo].[AktualizacePrubeh] WHERE (([IDAktualizacePrubeh] = @Original_I" +
                "DAktualizacePrubeh))";
            this._adapter.DeleteCommand.CommandType = global::System.Data.CommandType.Text;
            this._adapter.DeleteCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_IDAktualizacePrubeh", global::System.Data.SqlDbType.Int, 0, global::System.Data.ParameterDirection.Input, 0, 0, "IDAktualizacePrubeh", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
            this._adapter.InsertCommand = new global::System.Data.SqlClient.SqlCommand();
            this._adapter.InsertCommand.Connection = this.Connection;
            this._adapter.InsertCommand.CommandText = @"INSERT INTO [dbo].[AktualizacePrubeh] ([LogInfo], [Stav], [IDDavky], [Datum], [PopisPC], [IPAdresa], [IdZarizeni]) VALUES (@LogInfo, @Stav, @IDDavky, @Datum, @PopisPC, @IPAdresa, @IdZarizeni);
SELECT IDAktualizacePrubeh, LogInfo, Stav, IDDavky, Datum, PopisPC, IPAdresa, IdZarizeni FROM dbo.AktualizacePrubeh WHERE (IDAktualizacePrubeh = SCOPE_IDENTITY())";
            this._adapter.InsertCommand.CommandType = global::System.Data.CommandType.Text;
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@LogInfo", global::System.Data.SqlDbType.Text, 0, global::System.Data.ParameterDirection.Input, 0, 0, "LogInfo", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Stav", global::System.Data.SqlDbType.Int, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Stav", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@IDDavky", global::System.Data.SqlDbType.Int, 0, global::System.Data.ParameterDirection.Input, 0, 0, "IDDavky", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Datum", global::System.Data.SqlDbType.DateTime, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Datum", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@PopisPC", global::System.Data.SqlDbType.VarChar, 0, global::System.Data.ParameterDirection.Input, 0, 0, "PopisPC", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@IPAdresa", global::System.Data.SqlDbType.Char, 0, global::System.Data.ParameterDirection.Input, 0, 0, "IPAdresa", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@IdZarizeni", global::System.Data.SqlDbType.Text, 0, global::System.Data.ParameterDirection.Input, 0, 0, "IdZarizeni", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand = new global::System.Data.SqlClient.SqlCommand();
            this._adapter.UpdateCommand.Connection = this.Connection;
            this._adapter.UpdateCommand.CommandText = @"UPDATE [dbo].[AktualizacePrubeh] SET [LogInfo] = @LogInfo, [Stav] = @Stav, [IDDavky] = @IDDavky, [Datum] = @Datum, [PopisPC] = @PopisPC, [IPAdresa] = @IPAdresa, [IdZarizeni] = @IdZarizeni WHERE (([IDAktualizacePrubeh] = @Original_IDAktualizacePrubeh));
SELECT IDAktualizacePrubeh, LogInfo, Stav, IDDavky, Datum, PopisPC, IPAdresa, IdZarizeni FROM dbo.AktualizacePrubeh WHERE (IDAktualizacePrubeh = @IDAktualizacePrubeh)";
            this._adapter.UpdateCommand.CommandType = global::System.Data.CommandType.Text;
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@LogInfo", global::System.Data.SqlDbType.Text, 0, global::System.Data.ParameterDirection.Input, 0, 0, "LogInfo", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Stav", global::System.Data.SqlDbType.Int, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Stav", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@IDDavky", global::System.Data.SqlDbType.Int, 0, global::System.Data.ParameterDirection.Input, 0, 0, "IDDavky", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Datum", global::System.Data.SqlDbType.DateTime, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Datum", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@PopisPC", global::System.Data.SqlDbType.VarChar, 0, global::System.Data.ParameterDirection.Input, 0, 0, "PopisPC", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@IPAdresa", global::System.Data.SqlDbType.Char, 0, global::System.Data.ParameterDirection.Input, 0, 0, "IPAdresa", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@IdZarizeni", global::System.Data.SqlDbType.Text, 0, global::System.Data.ParameterDirection.Input, 0, 0, "IdZarizeni", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_IDAktualizacePrubeh", global::System.Data.SqlDbType.Int, 0, global::System.Data.ParameterDirection.Input, 0, 0, "IDAktualizacePrubeh", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@IDAktualizacePrubeh", global::System.Data.SqlDbType.Int, 4, global::System.Data.ParameterDirection.Input, 0, 0, "IDAktualizacePrubeh", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        private void InitConnection() {
            this._connection = new global::System.Data.SqlClient.SqlConnection();
            this._connection.ConnectionString = global::Anete.AutoUpgrades.Properties.Settings.Default.AneteUpdatesConnectionString;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        private void InitCommandCollection() {
            this._commandCollection = new global::System.Data.SqlClient.SqlCommand[2];
            this._commandCollection[0] = new global::System.Data.SqlClient.SqlCommand();
            this._commandCollection[0].Connection = this.Connection;
            this._commandCollection[0].CommandText = "SELECT     IDAktualizacePrubeh, LogInfo, Stav, IDDavky, Datum, PopisPC, IPAdresa," +
                " IdZarizeni\r\nFROM         dbo.AktualizacePrubeh";
            this._commandCollection[0].CommandType = global::System.Data.CommandType.Text;
            this._commandCollection[1] = new global::System.Data.SqlClient.SqlCommand();
            this._commandCollection[1].Connection = this.Connection;
            this._commandCollection[1].CommandText = "SELECT        Datum, IDAktualizacePrubeh, IDDavky, IPAdresa, IdZarizeni, \'\' AS Lo" +
                "gInfo, PopisPC, Stav\r\nFROM            [dbo].AktualizacePrubeh";
            this._commandCollection[1].CommandType = global::System.Data.CommandType.Text;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Fill, true)]
        public virtual int Fill(AutoUpgradeDs.AktualizacePrubehDataTable dataTable) {
            this.Adapter.SelectCommand = this.CommandCollection[0];
            if ((this.ClearBeforeFill == true)) {
                dataTable.Clear();
            }
            int returnValue = this.Adapter.Fill(dataTable);
            return returnValue;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Select, true)]
        public virtual AutoUpgradeDs.AktualizacePrubehDataTable GetData() {
            this.Adapter.SelectCommand = this.CommandCollection[0];
            AutoUpgradeDs.AktualizacePrubehDataTable dataTable = new AutoUpgradeDs.AktualizacePrubehDataTable();
            this.Adapter.Fill(dataTable);
            return dataTable;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Fill, false)]
        public virtual int FillWithoutLogInfo(AutoUpgradeDs.AktualizacePrubehDataTable dataTable) {
            this.Adapter.SelectCommand = this.CommandCollection[1];
            if ((this.ClearBeforeFill == true)) {
                dataTable.Clear();
            }
            int returnValue = this.Adapter.Fill(dataTable);
            return returnValue;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        public virtual int Update(AutoUpgradeDs.AktualizacePrubehDataTable dataTable) {
            return this.Adapter.Update(dataTable);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        public virtual int Update(AutoUpgradeDs dataSet) {
            return this.Adapter.Update(dataSet, "AktualizacePrubeh");
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        public virtual int Update(global::System.Data.DataRow dataRow) {
            return this.Adapter.Update(new global::System.Data.DataRow[] {
                        dataRow});
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        public virtual int Update(global::System.Data.DataRow[] dataRows) {
            return this.Adapter.Update(dataRows);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Delete, true)]
        public virtual int Delete(int Original_IDAktualizacePrubeh) {
            this.Adapter.DeleteCommand.Parameters[0].Value = ((int)(Original_IDAktualizacePrubeh));
            global::System.Data.ConnectionState previousConnectionState = this.Adapter.DeleteCommand.Connection.State;
            if (((this.Adapter.DeleteCommand.Connection.State & global::System.Data.ConnectionState.Open) 
                        != global::System.Data.ConnectionState.Open)) {
                this.Adapter.DeleteCommand.Connection.Open();
            }
            try {
                int returnValue = this.Adapter.DeleteCommand.ExecuteNonQuery();
                return returnValue;
            }
            finally {
                if ((previousConnectionState == global::System.Data.ConnectionState.Closed)) {
                    this.Adapter.DeleteCommand.Connection.Close();
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Insert, true)]
        public virtual int Insert(string LogInfo, int Stav, int IDDavky, System.DateTime Datum, string PopisPC, string IPAdresa, string IdZarizeni) {
            if ((LogInfo == null)) {
                this.Adapter.InsertCommand.Parameters[0].Value = global::System.DBNull.Value;
            }
            else {
                this.Adapter.InsertCommand.Parameters[0].Value = ((string)(LogInfo));
            }
            this.Adapter.InsertCommand.Parameters[1].Value = ((int)(Stav));
            this.Adapter.InsertCommand.Parameters[2].Value = ((int)(IDDavky));
            this.Adapter.InsertCommand.Parameters[3].Value = ((System.DateTime)(Datum));
            if ((PopisPC == null)) {
                throw new global::System.ArgumentNullException("PopisPC");
            }
            else {
                this.Adapter.InsertCommand.Parameters[4].Value = ((string)(PopisPC));
            }
            if ((IPAdresa == null)) {
                throw new global::System.ArgumentNullException("IPAdresa");
            }
            else {
                this.Adapter.InsertCommand.Parameters[5].Value = ((string)(IPAdresa));
            }
            if ((IdZarizeni == null)) {
                throw new global::System.ArgumentNullException("IdZarizeni");
            }
            else {
                this.Adapter.InsertCommand.Parameters[6].Value = ((string)(IdZarizeni));
            }
            global::System.Data.ConnectionState previousConnectionState = this.Adapter.InsertCommand.Connection.State;
            if (((this.Adapter.InsertCommand.Connection.State & global::System.Data.ConnectionState.Open) 
                        != global::System.Data.ConnectionState.Open)) {
                this.Adapter.InsertCommand.Connection.Open();
            }
            try {
                int returnValue = this.Adapter.InsertCommand.ExecuteNonQuery();
                return returnValue;
            }
            finally {
                if ((previousConnectionState == global::System.Data.ConnectionState.Closed)) {
                    this.Adapter.InsertCommand.Connection.Close();
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Update, true)]
        public virtual int Update(string LogInfo, int Stav, int IDDavky, System.DateTime Datum, string PopisPC, string IPAdresa, string IdZarizeni, int Original_IDAktualizacePrubeh, int IDAktualizacePrubeh) {
            if ((LogInfo == null)) {
                this.Adapter.UpdateCommand.Parameters[0].Value = global::System.DBNull.Value;
            }
            else {
                this.Adapter.UpdateCommand.Parameters[0].Value = ((string)(LogInfo));
            }
            this.Adapter.UpdateCommand.Parameters[1].Value = ((int)(Stav));
            this.Adapter.UpdateCommand.Parameters[2].Value = ((int)(IDDavky));
            this.Adapter.UpdateCommand.Parameters[3].Value = ((System.DateTime)(Datum));
            if ((PopisPC == null)) {
                throw new global::System.ArgumentNullException("PopisPC");
            }
            else {
                this.Adapter.UpdateCommand.Parameters[4].Value = ((string)(PopisPC));
            }
            if ((IPAdresa == null)) {
                throw new global::System.ArgumentNullException("IPAdresa");
            }
            else {
                this.Adapter.UpdateCommand.Parameters[5].Value = ((string)(IPAdresa));
            }
            if ((IdZarizeni == null)) {
                throw new global::System.ArgumentNullException("IdZarizeni");
            }
            else {
                this.Adapter.UpdateCommand.Parameters[6].Value = ((string)(IdZarizeni));
            }
            this.Adapter.UpdateCommand.Parameters[7].Value = ((int)(Original_IDAktualizacePrubeh));
            this.Adapter.UpdateCommand.Parameters[8].Value = ((int)(IDAktualizacePrubeh));
            global::System.Data.ConnectionState previousConnectionState = this.Adapter.UpdateCommand.Connection.State;
            if (((this.Adapter.UpdateCommand.Connection.State & global::System.Data.ConnectionState.Open) 
                        != global::System.Data.ConnectionState.Open)) {
                this.Adapter.UpdateCommand.Connection.Open();
            }
            try {
                int returnValue = this.Adapter.UpdateCommand.ExecuteNonQuery();
                return returnValue;
            }
            finally {
                if ((previousConnectionState == global::System.Data.ConnectionState.Closed)) {
                    this.Adapter.UpdateCommand.Connection.Close();
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Update, true)]
        public virtual int Update(string LogInfo, int Stav, int IDDavky, System.DateTime Datum, string PopisPC, string IPAdresa, string IdZarizeni, int Original_IDAktualizacePrubeh) {
            return this.Update(LogInfo, Stav, IDDavky, Datum, PopisPC, IPAdresa, IdZarizeni, Original_IDAktualizacePrubeh, Original_IDAktualizacePrubeh);
        }
    }
    
    /// <summary>
    ///Represents the connection and commands used to retrieve and save data.
    ///</summary>
    [global::System.ComponentModel.DesignerCategoryAttribute("code")]
    [global::System.ComponentModel.ToolboxItem(true)]
    [global::System.ComponentModel.DataObjectAttribute(true)]
    [global::System.ComponentModel.DesignerAttribute("Microsoft.VSDesigner.DataSource.Design.TableAdapterDesigner, Microsoft.VSDesigner" +
        ", Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a")]
    [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
    public partial class AktualizaceDavkyTableAdapter : global::Anete.Common.Data.Interface.CentralServerTableAdapter {
        
        private global::System.Data.SqlClient.SqlDataAdapter _adapter;
        
        private global::System.Data.SqlClient.SqlConnection _connection;
        
        private global::System.Data.SqlClient.SqlTransaction _transaction;
        
        private global::System.Data.SqlClient.SqlCommand[] _commandCollection;
        
        private bool _clearBeforeFill;
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        public AktualizaceDavkyTableAdapter() {
            this.ClearBeforeFill = true;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        protected internal global::System.Data.SqlClient.SqlDataAdapter Adapter {
            get {
                if ((this._adapter == null)) {
                    this.InitAdapter();
                }
                return this._adapter;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        internal global::System.Data.SqlClient.SqlConnection Connection {
            get {
                if ((this._connection == null)) {
                    this.InitConnection();
                }
                return this._connection;
            }
            set {
                this._connection = value;
                if ((this.Adapter.InsertCommand != null)) {
                    this.Adapter.InsertCommand.Connection = value;
                }
                if ((this.Adapter.DeleteCommand != null)) {
                    this.Adapter.DeleteCommand.Connection = value;
                }
                if ((this.Adapter.UpdateCommand != null)) {
                    this.Adapter.UpdateCommand.Connection = value;
                }
                for (int i = 0; (i < this.CommandCollection.Length); i = (i + 1)) {
                    if ((this.CommandCollection[i] != null)) {
                        ((global::System.Data.SqlClient.SqlCommand)(this.CommandCollection[i])).Connection = value;
                    }
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        internal global::System.Data.SqlClient.SqlTransaction Transaction {
            get {
                return this._transaction;
            }
            set {
                this._transaction = value;
                for (int i = 0; (i < this.CommandCollection.Length); i = (i + 1)) {
                    this.CommandCollection[i].Transaction = this._transaction;
                }
                if (((this.Adapter != null) 
                            && (this.Adapter.DeleteCommand != null))) {
                    this.Adapter.DeleteCommand.Transaction = this._transaction;
                }
                if (((this.Adapter != null) 
                            && (this.Adapter.InsertCommand != null))) {
                    this.Adapter.InsertCommand.Transaction = this._transaction;
                }
                if (((this.Adapter != null) 
                            && (this.Adapter.UpdateCommand != null))) {
                    this.Adapter.UpdateCommand.Transaction = this._transaction;
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        protected global::System.Data.SqlClient.SqlCommand[] CommandCollection {
            get {
                if ((this._commandCollection == null)) {
                    this.InitCommandCollection();
                }
                return this._commandCollection;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        public bool ClearBeforeFill {
            get {
                return this._clearBeforeFill;
            }
            set {
                this._clearBeforeFill = value;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        private void InitAdapter() {
            this._adapter = new global::System.Data.SqlClient.SqlDataAdapter();
            global::System.Data.Common.DataTableMapping tableMapping = new global::System.Data.Common.DataTableMapping();
            tableMapping.SourceTable = "Table";
            tableMapping.DataSetTable = "AktualizaceDavky";
            tableMapping.ColumnMappings.Add("IDDavky", "IDDavky");
            tableMapping.ColumnMappings.Add("IDAktualizace", "IDAktualizace");
            tableMapping.ColumnMappings.Add("Operace", "Operace");
            tableMapping.ColumnMappings.Add("Vyzadovano", "Vyzadovano");
            tableMapping.ColumnMappings.Add("DobaInstalace", "DobaInstalace");
            tableMapping.ColumnMappings.Add("Parametry", "Parametry");
            tableMapping.ColumnMappings.Add("Priorita", "Priorita");
            tableMapping.ColumnMappings.Add("Spoustet", "Spoustet");
            this._adapter.TableMappings.Add(tableMapping);
            this._adapter.DeleteCommand = new global::System.Data.SqlClient.SqlCommand();
            this._adapter.DeleteCommand.Connection = this.Connection;
            this._adapter.DeleteCommand.CommandText = "DELETE FROM [dbo].[AktualizaceDavky] WHERE (([IDDavky] = @Original_IDDavky))";
            this._adapter.DeleteCommand.CommandType = global::System.Data.CommandType.Text;
            this._adapter.DeleteCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_IDDavky", global::System.Data.SqlDbType.Int, 0, global::System.Data.ParameterDirection.Input, 0, 0, "IDDavky", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
            this._adapter.InsertCommand = new global::System.Data.SqlClient.SqlCommand();
            this._adapter.InsertCommand.Connection = this.Connection;
            this._adapter.InsertCommand.CommandText = @"INSERT INTO dbo.AktualizaceDavky
                      (IDAktualizace, Operace, Vyzadovano, DobaInstalace, Parametry, Priorita, Spoustet)
VALUES     (@IDAktualizace,@Operace,@Vyzadovano,@DobaInstalace,@Parametry,@Priorita,@Spoustet);  
SELECT IDDavky, IDAktualizace, Operace, Vyzadovano, DobaInstalace, Parametry, Priorita FROM dbo.AktualizaceDavky WHERE (IDDavky = SCOPE_IDENTITY())";
            this._adapter.InsertCommand.CommandType = global::System.Data.CommandType.Text;
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@IDAktualizace", global::System.Data.SqlDbType.Int, 4, global::System.Data.ParameterDirection.Input, 0, 0, "IDAktualizace", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Operace", global::System.Data.SqlDbType.VarChar, 100, global::System.Data.ParameterDirection.Input, 0, 0, "Operace", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Vyzadovano", global::System.Data.SqlDbType.Bit, 1, global::System.Data.ParameterDirection.Input, 0, 0, "Vyzadovano", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@DobaInstalace", global::System.Data.SqlDbType.DateTime, 8, global::System.Data.ParameterDirection.Input, 0, 0, "DobaInstalace", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Parametry", global::System.Data.SqlDbType.VarChar, 4000, global::System.Data.ParameterDirection.Input, 0, 0, "Parametry", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Priorita", global::System.Data.SqlDbType.Float, 8, global::System.Data.ParameterDirection.Input, 0, 0, "Priorita", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Spoustet", global::System.Data.SqlDbType.Bit, 1, global::System.Data.ParameterDirection.Input, 0, 0, "Spoustet", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand = new global::System.Data.SqlClient.SqlCommand();
            this._adapter.UpdateCommand.Connection = this.Connection;
            this._adapter.UpdateCommand.CommandText = @"UPDATE    dbo.AktualizaceDavky
SET              IDAktualizace = @IDAktualizace, Operace = @Operace, Vyzadovano = @Vyzadovano, DobaInstalace = @DobaInstalace, Parametry = @Parametry, 
                      Priorita = @Priorita, Spoustet = @Spoustet
WHERE     (IDDavky = @Original_IDDavky);  
SELECT IDDavky, IDAktualizace, Operace, Vyzadovano, DobaInstalace, Parametry, Priorita, Spoustet FROM dbo.AktualizaceDavky WHERE (IDDavky = @IDDavky)";
            this._adapter.UpdateCommand.CommandType = global::System.Data.CommandType.Text;
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@IDAktualizace", global::System.Data.SqlDbType.Int, 4, global::System.Data.ParameterDirection.Input, 0, 0, "IDAktualizace", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Operace", global::System.Data.SqlDbType.VarChar, 100, global::System.Data.ParameterDirection.Input, 0, 0, "Operace", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Vyzadovano", global::System.Data.SqlDbType.Bit, 1, global::System.Data.ParameterDirection.Input, 0, 0, "Vyzadovano", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@DobaInstalace", global::System.Data.SqlDbType.DateTime, 8, global::System.Data.ParameterDirection.Input, 0, 0, "DobaInstalace", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Parametry", global::System.Data.SqlDbType.VarChar, 4000, global::System.Data.ParameterDirection.Input, 0, 0, "Parametry", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Priorita", global::System.Data.SqlDbType.Float, 8, global::System.Data.ParameterDirection.Input, 0, 0, "Priorita", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Spoustet", global::System.Data.SqlDbType.Bit, 1, global::System.Data.ParameterDirection.Input, 0, 0, "Spoustet", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_IDDavky", global::System.Data.SqlDbType.Int, 4, global::System.Data.ParameterDirection.Input, 0, 0, "IDDavky", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@IDDavky", global::System.Data.SqlDbType.Int, 4, global::System.Data.ParameterDirection.Input, 0, 0, "IDDavky", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        private void InitConnection() {
            this._connection = new global::System.Data.SqlClient.SqlConnection();
            this._connection.ConnectionString = global::Anete.AutoUpgrades.Properties.Settings.Default.AneteUpdatesConnectionString;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        private void InitCommandCollection() {
            this._commandCollection = new global::System.Data.SqlClient.SqlCommand[1];
            this._commandCollection[0] = new global::System.Data.SqlClient.SqlCommand();
            this._commandCollection[0].Connection = this.Connection;
            this._commandCollection[0].CommandText = "SELECT     IDDavky, IDAktualizace, Operace, Vyzadovano, DobaInstalace, Parametry," +
                " Priorita, Spoustet\r\nFROM         dbo.AktualizaceDavky";
            this._commandCollection[0].CommandType = global::System.Data.CommandType.Text;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Fill, true)]
        public virtual int Fill(AutoUpgradeDs.AktualizaceDavkyDataTable dataTable) {
            this.Adapter.SelectCommand = this.CommandCollection[0];
            if ((this.ClearBeforeFill == true)) {
                dataTable.Clear();
            }
            int returnValue = this.Adapter.Fill(dataTable);
            return returnValue;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Select, true)]
        public virtual AutoUpgradeDs.AktualizaceDavkyDataTable GetData() {
            this.Adapter.SelectCommand = this.CommandCollection[0];
            AutoUpgradeDs.AktualizaceDavkyDataTable dataTable = new AutoUpgradeDs.AktualizaceDavkyDataTable();
            this.Adapter.Fill(dataTable);
            return dataTable;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        public virtual int Update(AutoUpgradeDs.AktualizaceDavkyDataTable dataTable) {
            return this.Adapter.Update(dataTable);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        public virtual int Update(AutoUpgradeDs dataSet) {
            return this.Adapter.Update(dataSet, "AktualizaceDavky");
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        public virtual int Update(global::System.Data.DataRow dataRow) {
            return this.Adapter.Update(new global::System.Data.DataRow[] {
                        dataRow});
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        public virtual int Update(global::System.Data.DataRow[] dataRows) {
            return this.Adapter.Update(dataRows);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Delete, true)]
        public virtual int Delete(int Original_IDDavky) {
            this.Adapter.DeleteCommand.Parameters[0].Value = ((int)(Original_IDDavky));
            global::System.Data.ConnectionState previousConnectionState = this.Adapter.DeleteCommand.Connection.State;
            if (((this.Adapter.DeleteCommand.Connection.State & global::System.Data.ConnectionState.Open) 
                        != global::System.Data.ConnectionState.Open)) {
                this.Adapter.DeleteCommand.Connection.Open();
            }
            try {
                int returnValue = this.Adapter.DeleteCommand.ExecuteNonQuery();
                return returnValue;
            }
            finally {
                if ((previousConnectionState == global::System.Data.ConnectionState.Closed)) {
                    this.Adapter.DeleteCommand.Connection.Close();
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Insert, true)]
        public virtual int Insert(int IDAktualizace, string Operace, bool Vyzadovano, global::System.Nullable<global::System.DateTime> DobaInstalace, string Parametry, global::System.Nullable<double> Priorita, bool Spoustet) {
            this.Adapter.InsertCommand.Parameters[0].Value = ((int)(IDAktualizace));
            if ((Operace == null)) {
                throw new global::System.ArgumentNullException("Operace");
            }
            else {
                this.Adapter.InsertCommand.Parameters[1].Value = ((string)(Operace));
            }
            this.Adapter.InsertCommand.Parameters[2].Value = ((bool)(Vyzadovano));
            if ((DobaInstalace.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[3].Value = ((System.DateTime)(DobaInstalace.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[3].Value = global::System.DBNull.Value;
            }
            if ((Parametry == null)) {
                this.Adapter.InsertCommand.Parameters[4].Value = global::System.DBNull.Value;
            }
            else {
                this.Adapter.InsertCommand.Parameters[4].Value = ((string)(Parametry));
            }
            if ((Priorita.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[5].Value = ((double)(Priorita.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[5].Value = global::System.DBNull.Value;
            }
            this.Adapter.InsertCommand.Parameters[6].Value = ((bool)(Spoustet));
            global::System.Data.ConnectionState previousConnectionState = this.Adapter.InsertCommand.Connection.State;
            if (((this.Adapter.InsertCommand.Connection.State & global::System.Data.ConnectionState.Open) 
                        != global::System.Data.ConnectionState.Open)) {
                this.Adapter.InsertCommand.Connection.Open();
            }
            try {
                int returnValue = this.Adapter.InsertCommand.ExecuteNonQuery();
                return returnValue;
            }
            finally {
                if ((previousConnectionState == global::System.Data.ConnectionState.Closed)) {
                    this.Adapter.InsertCommand.Connection.Close();
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Update, true)]
        public virtual int Update(int IDAktualizace, string Operace, bool Vyzadovano, global::System.Nullable<global::System.DateTime> DobaInstalace, string Parametry, global::System.Nullable<double> Priorita, bool Spoustet, int Original_IDDavky, int IDDavky) {
            this.Adapter.UpdateCommand.Parameters[0].Value = ((int)(IDAktualizace));
            if ((Operace == null)) {
                throw new global::System.ArgumentNullException("Operace");
            }
            else {
                this.Adapter.UpdateCommand.Parameters[1].Value = ((string)(Operace));
            }
            this.Adapter.UpdateCommand.Parameters[2].Value = ((bool)(Vyzadovano));
            if ((DobaInstalace.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[3].Value = ((System.DateTime)(DobaInstalace.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[3].Value = global::System.DBNull.Value;
            }
            if ((Parametry == null)) {
                this.Adapter.UpdateCommand.Parameters[4].Value = global::System.DBNull.Value;
            }
            else {
                this.Adapter.UpdateCommand.Parameters[4].Value = ((string)(Parametry));
            }
            if ((Priorita.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[5].Value = ((double)(Priorita.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[5].Value = global::System.DBNull.Value;
            }
            this.Adapter.UpdateCommand.Parameters[6].Value = ((bool)(Spoustet));
            this.Adapter.UpdateCommand.Parameters[7].Value = ((int)(Original_IDDavky));
            this.Adapter.UpdateCommand.Parameters[8].Value = ((int)(IDDavky));
            global::System.Data.ConnectionState previousConnectionState = this.Adapter.UpdateCommand.Connection.State;
            if (((this.Adapter.UpdateCommand.Connection.State & global::System.Data.ConnectionState.Open) 
                        != global::System.Data.ConnectionState.Open)) {
                this.Adapter.UpdateCommand.Connection.Open();
            }
            try {
                int returnValue = this.Adapter.UpdateCommand.ExecuteNonQuery();
                return returnValue;
            }
            finally {
                if ((previousConnectionState == global::System.Data.ConnectionState.Closed)) {
                    this.Adapter.UpdateCommand.Connection.Close();
                }
            }
        }
    }
    
    /// <summary>
    ///Represents the connection and commands used to retrieve and save data.
    ///</summary>
    [global::System.ComponentModel.DesignerCategoryAttribute("code")]
    [global::System.ComponentModel.ToolboxItem(true)]
    [global::System.ComponentModel.DataObjectAttribute(true)]
    [global::System.ComponentModel.DesignerAttribute("Microsoft.VSDesigner.DataSource.Design.TableAdapterDesigner, Microsoft.VSDesigner" +
        ", Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a")]
    [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
    public partial class AktualizaceDavky_CFZarizeniTableAdapter : global::Anete.Common.Data.Interface.CentralServerTableAdapter {
        
        private global::System.Data.SqlClient.SqlDataAdapter _adapter;
        
        private global::System.Data.SqlClient.SqlConnection _connection;
        
        private global::System.Data.SqlClient.SqlTransaction _transaction;
        
        private global::System.Data.SqlClient.SqlCommand[] _commandCollection;
        
        private bool _clearBeforeFill;
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        public AktualizaceDavky_CFZarizeniTableAdapter() {
            this.ClearBeforeFill = true;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        protected internal global::System.Data.SqlClient.SqlDataAdapter Adapter {
            get {
                if ((this._adapter == null)) {
                    this.InitAdapter();
                }
                return this._adapter;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        internal global::System.Data.SqlClient.SqlConnection Connection {
            get {
                if ((this._connection == null)) {
                    this.InitConnection();
                }
                return this._connection;
            }
            set {
                this._connection = value;
                if ((this.Adapter.InsertCommand != null)) {
                    this.Adapter.InsertCommand.Connection = value;
                }
                if ((this.Adapter.DeleteCommand != null)) {
                    this.Adapter.DeleteCommand.Connection = value;
                }
                if ((this.Adapter.UpdateCommand != null)) {
                    this.Adapter.UpdateCommand.Connection = value;
                }
                for (int i = 0; (i < this.CommandCollection.Length); i = (i + 1)) {
                    if ((this.CommandCollection[i] != null)) {
                        ((global::System.Data.SqlClient.SqlCommand)(this.CommandCollection[i])).Connection = value;
                    }
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        internal global::System.Data.SqlClient.SqlTransaction Transaction {
            get {
                return this._transaction;
            }
            set {
                this._transaction = value;
                for (int i = 0; (i < this.CommandCollection.Length); i = (i + 1)) {
                    this.CommandCollection[i].Transaction = this._transaction;
                }
                if (((this.Adapter != null) 
                            && (this.Adapter.DeleteCommand != null))) {
                    this.Adapter.DeleteCommand.Transaction = this._transaction;
                }
                if (((this.Adapter != null) 
                            && (this.Adapter.InsertCommand != null))) {
                    this.Adapter.InsertCommand.Transaction = this._transaction;
                }
                if (((this.Adapter != null) 
                            && (this.Adapter.UpdateCommand != null))) {
                    this.Adapter.UpdateCommand.Transaction = this._transaction;
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        protected global::System.Data.SqlClient.SqlCommand[] CommandCollection {
            get {
                if ((this._commandCollection == null)) {
                    this.InitCommandCollection();
                }
                return this._commandCollection;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        public bool ClearBeforeFill {
            get {
                return this._clearBeforeFill;
            }
            set {
                this._clearBeforeFill = value;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        private void InitAdapter() {
            this._adapter = new global::System.Data.SqlClient.SqlDataAdapter();
            global::System.Data.Common.DataTableMapping tableMapping = new global::System.Data.Common.DataTableMapping();
            tableMapping.SourceTable = "Table";
            tableMapping.DataSetTable = "AktualizaceDavky_CFZarizeni";
            tableMapping.ColumnMappings.Add("IDDavky", "IDDavky");
            tableMapping.ColumnMappings.Add("IDZarizeni", "IDZarizeni");
            this._adapter.TableMappings.Add(tableMapping);
            this._adapter.DeleteCommand = new global::System.Data.SqlClient.SqlCommand();
            this._adapter.DeleteCommand.Connection = this.Connection;
            this._adapter.DeleteCommand.CommandText = "DELETE FROM [dbo].[AktualizaceDavky_CFZarizeni] WHERE (([IDDavky] = @Original_IDD" +
                "avky) AND ([IDZarizeni] = @Original_IDZarizeni))";
            this._adapter.DeleteCommand.CommandType = global::System.Data.CommandType.Text;
            this._adapter.DeleteCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_IDDavky", global::System.Data.SqlDbType.Int, 0, global::System.Data.ParameterDirection.Input, 0, 0, "IDDavky", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
            this._adapter.DeleteCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_IDZarizeni", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "IDZarizeni", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
            this._adapter.InsertCommand = new global::System.Data.SqlClient.SqlCommand();
            this._adapter.InsertCommand.Connection = this.Connection;
            this._adapter.InsertCommand.CommandText = "INSERT INTO [dbo].[AktualizaceDavky_CFZarizeni] ([IDDavky], [IDZarizeni]) VALUES " +
                "(@IDDavky, @IDZarizeni);\r\nSELECT IDDavky, IDZarizeni FROM dbo.AktualizaceDavky_C" +
                "FZarizeni WHERE (IDDavky = @IDDavky) AND (IDZarizeni = @IDZarizeni)";
            this._adapter.InsertCommand.CommandType = global::System.Data.CommandType.Text;
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@IDDavky", global::System.Data.SqlDbType.Int, 0, global::System.Data.ParameterDirection.Input, 0, 0, "IDDavky", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@IDZarizeni", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "IDZarizeni", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand = new global::System.Data.SqlClient.SqlCommand();
            this._adapter.UpdateCommand.Connection = this.Connection;
            this._adapter.UpdateCommand.CommandText = @"UPDATE [dbo].[AktualizaceDavky_CFZarizeni] SET [IDDavky] = @IDDavky, [IDZarizeni] = @IDZarizeni WHERE (([IDDavky] = @Original_IDDavky) AND ([IDZarizeni] = @Original_IDZarizeni));
SELECT IDDavky, IDZarizeni FROM dbo.AktualizaceDavky_CFZarizeni WHERE (IDDavky = @IDDavky) AND (IDZarizeni = @IDZarizeni)";
            this._adapter.UpdateCommand.CommandType = global::System.Data.CommandType.Text;
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@IDDavky", global::System.Data.SqlDbType.Int, 0, global::System.Data.ParameterDirection.Input, 0, 0, "IDDavky", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@IDZarizeni", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "IDZarizeni", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_IDDavky", global::System.Data.SqlDbType.Int, 0, global::System.Data.ParameterDirection.Input, 0, 0, "IDDavky", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_IDZarizeni", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "IDZarizeni", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        private void InitConnection() {
            this._connection = new global::System.Data.SqlClient.SqlConnection();
            this._connection.ConnectionString = global::Anete.AutoUpgrades.Properties.Settings.Default.AneteUpdatesConnectionString;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        private void InitCommandCollection() {
            this._commandCollection = new global::System.Data.SqlClient.SqlCommand[1];
            this._commandCollection[0] = new global::System.Data.SqlClient.SqlCommand();
            this._commandCollection[0].Connection = this.Connection;
            this._commandCollection[0].CommandText = "SELECT IDDavky, IDZarizeni FROM dbo.AktualizaceDavky_CFZarizeni";
            this._commandCollection[0].CommandType = global::System.Data.CommandType.Text;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Fill, true)]
        public virtual int Fill(AutoUpgradeDs.AktualizaceDavky_CFZarizeniDataTable dataTable) {
            this.Adapter.SelectCommand = this.CommandCollection[0];
            if ((this.ClearBeforeFill == true)) {
                dataTable.Clear();
            }
            int returnValue = this.Adapter.Fill(dataTable);
            return returnValue;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Select, true)]
        public virtual AutoUpgradeDs.AktualizaceDavky_CFZarizeniDataTable GetData() {
            this.Adapter.SelectCommand = this.CommandCollection[0];
            AutoUpgradeDs.AktualizaceDavky_CFZarizeniDataTable dataTable = new AutoUpgradeDs.AktualizaceDavky_CFZarizeniDataTable();
            this.Adapter.Fill(dataTable);
            return dataTable;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        public virtual int Update(AutoUpgradeDs.AktualizaceDavky_CFZarizeniDataTable dataTable) {
            return this.Adapter.Update(dataTable);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        public virtual int Update(AutoUpgradeDs dataSet) {
            return this.Adapter.Update(dataSet, "AktualizaceDavky_CFZarizeni");
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        public virtual int Update(global::System.Data.DataRow dataRow) {
            return this.Adapter.Update(new global::System.Data.DataRow[] {
                        dataRow});
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        public virtual int Update(global::System.Data.DataRow[] dataRows) {
            return this.Adapter.Update(dataRows);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Delete, true)]
        public virtual int Delete(int Original_IDDavky, short Original_IDZarizeni) {
            this.Adapter.DeleteCommand.Parameters[0].Value = ((int)(Original_IDDavky));
            this.Adapter.DeleteCommand.Parameters[1].Value = ((short)(Original_IDZarizeni));
            global::System.Data.ConnectionState previousConnectionState = this.Adapter.DeleteCommand.Connection.State;
            if (((this.Adapter.DeleteCommand.Connection.State & global::System.Data.ConnectionState.Open) 
                        != global::System.Data.ConnectionState.Open)) {
                this.Adapter.DeleteCommand.Connection.Open();
            }
            try {
                int returnValue = this.Adapter.DeleteCommand.ExecuteNonQuery();
                return returnValue;
            }
            finally {
                if ((previousConnectionState == global::System.Data.ConnectionState.Closed)) {
                    this.Adapter.DeleteCommand.Connection.Close();
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Insert, true)]
        public virtual int Insert(int IDDavky, short IDZarizeni) {
            this.Adapter.InsertCommand.Parameters[0].Value = ((int)(IDDavky));
            this.Adapter.InsertCommand.Parameters[1].Value = ((short)(IDZarizeni));
            global::System.Data.ConnectionState previousConnectionState = this.Adapter.InsertCommand.Connection.State;
            if (((this.Adapter.InsertCommand.Connection.State & global::System.Data.ConnectionState.Open) 
                        != global::System.Data.ConnectionState.Open)) {
                this.Adapter.InsertCommand.Connection.Open();
            }
            try {
                int returnValue = this.Adapter.InsertCommand.ExecuteNonQuery();
                return returnValue;
            }
            finally {
                if ((previousConnectionState == global::System.Data.ConnectionState.Closed)) {
                    this.Adapter.InsertCommand.Connection.Close();
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Update, true)]
        public virtual int Update(int IDDavky, short IDZarizeni, int Original_IDDavky, short Original_IDZarizeni) {
            this.Adapter.UpdateCommand.Parameters[0].Value = ((int)(IDDavky));
            this.Adapter.UpdateCommand.Parameters[1].Value = ((short)(IDZarizeni));
            this.Adapter.UpdateCommand.Parameters[2].Value = ((int)(Original_IDDavky));
            this.Adapter.UpdateCommand.Parameters[3].Value = ((short)(Original_IDZarizeni));
            global::System.Data.ConnectionState previousConnectionState = this.Adapter.UpdateCommand.Connection.State;
            if (((this.Adapter.UpdateCommand.Connection.State & global::System.Data.ConnectionState.Open) 
                        != global::System.Data.ConnectionState.Open)) {
                this.Adapter.UpdateCommand.Connection.Open();
            }
            try {
                int returnValue = this.Adapter.UpdateCommand.ExecuteNonQuery();
                return returnValue;
            }
            finally {
                if ((previousConnectionState == global::System.Data.ConnectionState.Closed)) {
                    this.Adapter.UpdateCommand.Connection.Close();
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Update, true)]
        public virtual int Update(int Original_IDDavky, short Original_IDZarizeni) {
            return this.Update(Original_IDDavky, Original_IDZarizeni, Original_IDDavky, Original_IDZarizeni);
        }
    }
    
    /// <summary>
    ///Represents the connection and commands used to retrieve and save data.
    ///</summary>
    [global::System.ComponentModel.DesignerCategoryAttribute("code")]
    [global::System.ComponentModel.ToolboxItem(true)]
    [global::System.ComponentModel.DataObjectAttribute(true)]
    [global::System.ComponentModel.DesignerAttribute("Microsoft.VSDesigner.DataSource.Design.TableAdapterDesigner, Microsoft.VSDesigner" +
        ", Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a")]
    [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
    public partial class CFZarizeniTableAdapter : global::Anete.Common.Data.Interface.CentralServerTableAdapter {
        
        private global::System.Data.SqlClient.SqlDataAdapter _adapter;
        
        private global::System.Data.SqlClient.SqlConnection _connection;
        
        private global::System.Data.SqlClient.SqlTransaction _transaction;
        
        private global::System.Data.SqlClient.SqlCommand[] _commandCollection;
        
        private bool _clearBeforeFill;
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        public CFZarizeniTableAdapter() {
            this.ClearBeforeFill = true;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        protected internal global::System.Data.SqlClient.SqlDataAdapter Adapter {
            get {
                if ((this._adapter == null)) {
                    this.InitAdapter();
                }
                return this._adapter;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        internal global::System.Data.SqlClient.SqlConnection Connection {
            get {
                if ((this._connection == null)) {
                    this.InitConnection();
                }
                return this._connection;
            }
            set {
                this._connection = value;
                if ((this.Adapter.InsertCommand != null)) {
                    this.Adapter.InsertCommand.Connection = value;
                }
                if ((this.Adapter.DeleteCommand != null)) {
                    this.Adapter.DeleteCommand.Connection = value;
                }
                if ((this.Adapter.UpdateCommand != null)) {
                    this.Adapter.UpdateCommand.Connection = value;
                }
                for (int i = 0; (i < this.CommandCollection.Length); i = (i + 1)) {
                    if ((this.CommandCollection[i] != null)) {
                        ((global::System.Data.SqlClient.SqlCommand)(this.CommandCollection[i])).Connection = value;
                    }
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        internal global::System.Data.SqlClient.SqlTransaction Transaction {
            get {
                return this._transaction;
            }
            set {
                this._transaction = value;
                for (int i = 0; (i < this.CommandCollection.Length); i = (i + 1)) {
                    this.CommandCollection[i].Transaction = this._transaction;
                }
                if (((this.Adapter != null) 
                            && (this.Adapter.DeleteCommand != null))) {
                    this.Adapter.DeleteCommand.Transaction = this._transaction;
                }
                if (((this.Adapter != null) 
                            && (this.Adapter.InsertCommand != null))) {
                    this.Adapter.InsertCommand.Transaction = this._transaction;
                }
                if (((this.Adapter != null) 
                            && (this.Adapter.UpdateCommand != null))) {
                    this.Adapter.UpdateCommand.Transaction = this._transaction;
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        protected global::System.Data.SqlClient.SqlCommand[] CommandCollection {
            get {
                if ((this._commandCollection == null)) {
                    this.InitCommandCollection();
                }
                return this._commandCollection;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        public bool ClearBeforeFill {
            get {
                return this._clearBeforeFill;
            }
            set {
                this._clearBeforeFill = value;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        private void InitAdapter() {
            this._adapter = new global::System.Data.SqlClient.SqlDataAdapter();
            global::System.Data.Common.DataTableMapping tableMapping = new global::System.Data.Common.DataTableMapping();
            tableMapping.SourceTable = "Table";
            tableMapping.DataSetTable = "CFZarizeni";
            tableMapping.ColumnMappings.Add("id_zarizeni", "id_zarizeni");
            tableMapping.ColumnMappings.Add("id_funkce", "id_funkce");
            tableMapping.ColumnMappings.Add("pouzito", "pouzito");
            tableMapping.ColumnMappings.Add("aktivni", "aktivni");
            tableMapping.ColumnMappings.Add("popis", "popis");
            tableMapping.ColumnMappings.Add("id_jidelna", "id_jidelna");
            tableMapping.ColumnMappings.Add("id_vydejna", "id_vydejna");
            tableMapping.ColumnMappings.Add("last_contact", "last_contact");
            tableMapping.ColumnMappings.Add("stav_queue", "stav_queue");
            tableMapping.ColumnMappings.Add("id_sklad", "id_sklad");
            this._adapter.TableMappings.Add(tableMapping);
            this._adapter.DeleteCommand = new global::System.Data.SqlClient.SqlCommand();
            this._adapter.DeleteCommand.Connection = this.Connection;
            this._adapter.DeleteCommand.CommandText = "DELETE FROM [dba].[CFZarizeni] WHERE (([id_zarizeni] = @Original_id_zarizeni))";
            this._adapter.DeleteCommand.CommandType = global::System.Data.CommandType.Text;
            this._adapter.DeleteCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_id_zarizeni", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "id_zarizeni", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
            this._adapter.InsertCommand = new global::System.Data.SqlClient.SqlCommand();
            this._adapter.InsertCommand.Connection = this.Connection;
            this._adapter.InsertCommand.CommandText = @"INSERT INTO [dba].[CFZarizeni] ([id_zarizeni], [id_funkce], [pouzito], [aktivni], [popis], [id_jidelna], [id_vydejna], [last_contact], [stav_queue], [id_sklad]) VALUES (@id_zarizeni, @id_funkce, @pouzito, @aktivni, @popis, @id_jidelna, @id_vydejna, @last_contact, @stav_queue, @id_sklad);
SELECT id_zarizeni, id_funkce, pouzito, aktivni, popis, id_jidelna, id_vydejna, last_contact, stav_queue, id_sklad FROM CFZarizeni AS zar WHERE (id_zarizeni = @id_zarizeni)";
            this._adapter.InsertCommand.CommandType = global::System.Data.CommandType.Text;
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@id_zarizeni", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "id_zarizeni", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@id_funkce", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "id_funkce", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@pouzito", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "pouzito", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@aktivni", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "aktivni", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@popis", global::System.Data.SqlDbType.VarChar, 0, global::System.Data.ParameterDirection.Input, 0, 0, "popis", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@id_jidelna", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "id_jidelna", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@id_vydejna", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "id_vydejna", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@last_contact", global::System.Data.SqlDbType.DateTime, 0, global::System.Data.ParameterDirection.Input, 0, 0, "last_contact", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@stav_queue", global::System.Data.SqlDbType.VarChar, 0, global::System.Data.ParameterDirection.Input, 0, 0, "stav_queue", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@id_sklad", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "id_sklad", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand = new global::System.Data.SqlClient.SqlCommand();
            this._adapter.UpdateCommand.Connection = this.Connection;
            this._adapter.UpdateCommand.CommandText = @"UPDATE [dba].[CFZarizeni] SET [id_zarizeni] = @id_zarizeni, [id_funkce] = @id_funkce, [pouzito] = @pouzito, [aktivni] = @aktivni, [popis] = @popis, [id_jidelna] = @id_jidelna, [id_vydejna] = @id_vydejna, [last_contact] = @last_contact, [stav_queue] = @stav_queue, [id_sklad] = @id_sklad WHERE (([id_zarizeni] = @Original_id_zarizeni));
SELECT id_zarizeni, id_funkce, pouzito, aktivni, popis, id_jidelna, id_vydejna, last_contact, stav_queue, id_sklad FROM CFZarizeni AS zar WHERE (id_zarizeni = @id_zarizeni)";
            this._adapter.UpdateCommand.CommandType = global::System.Data.CommandType.Text;
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@id_zarizeni", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "id_zarizeni", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@id_funkce", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "id_funkce", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@pouzito", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "pouzito", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@aktivni", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "aktivni", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@popis", global::System.Data.SqlDbType.VarChar, 0, global::System.Data.ParameterDirection.Input, 0, 0, "popis", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@id_jidelna", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "id_jidelna", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@id_vydejna", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "id_vydejna", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@last_contact", global::System.Data.SqlDbType.DateTime, 0, global::System.Data.ParameterDirection.Input, 0, 0, "last_contact", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@stav_queue", global::System.Data.SqlDbType.VarChar, 0, global::System.Data.ParameterDirection.Input, 0, 0, "stav_queue", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@id_sklad", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "id_sklad", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_id_zarizeni", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "id_zarizeni", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        private void InitConnection() {
            this._connection = new global::System.Data.SqlClient.SqlConnection();
            this._connection.ConnectionString = global::Anete.AutoUpgrades.Properties.Settings.Default.KreditConnectionString;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        private void InitCommandCollection() {
            this._commandCollection = new global::System.Data.SqlClient.SqlCommand[1];
            this._commandCollection[0] = new global::System.Data.SqlClient.SqlCommand();
            this._commandCollection[0].Connection = this.Connection;
            this._commandCollection[0].CommandText = "SELECT     id_zarizeni, id_funkce, pouzito, aktivni, popis, id_jidelna, id_vydejn" +
                "a, last_contact, stav_queue, id_sklad\r\nFROM         [dba].CFZarizeni AS zar";
            this._commandCollection[0].CommandType = global::System.Data.CommandType.Text;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Fill, true)]
        public virtual int Fill(AutoUpgradeDs.CFZarizeniDataTable dataTable) {
            this.Adapter.SelectCommand = this.CommandCollection[0];
            if ((this.ClearBeforeFill == true)) {
                dataTable.Clear();
            }
            int returnValue = this.Adapter.Fill(dataTable);
            return returnValue;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Select, true)]
        public virtual AutoUpgradeDs.CFZarizeniDataTable GetData() {
            this.Adapter.SelectCommand = this.CommandCollection[0];
            AutoUpgradeDs.CFZarizeniDataTable dataTable = new AutoUpgradeDs.CFZarizeniDataTable();
            this.Adapter.Fill(dataTable);
            return dataTable;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        public virtual int Update(AutoUpgradeDs.CFZarizeniDataTable dataTable) {
            return this.Adapter.Update(dataTable);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        public virtual int Update(AutoUpgradeDs dataSet) {
            return this.Adapter.Update(dataSet, "CFZarizeni");
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        public virtual int Update(global::System.Data.DataRow dataRow) {
            return this.Adapter.Update(new global::System.Data.DataRow[] {
                        dataRow});
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        public virtual int Update(global::System.Data.DataRow[] dataRows) {
            return this.Adapter.Update(dataRows);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Delete, true)]
        public virtual int Delete(short Original_id_zarizeni) {
            this.Adapter.DeleteCommand.Parameters[0].Value = ((short)(Original_id_zarizeni));
            global::System.Data.ConnectionState previousConnectionState = this.Adapter.DeleteCommand.Connection.State;
            if (((this.Adapter.DeleteCommand.Connection.State & global::System.Data.ConnectionState.Open) 
                        != global::System.Data.ConnectionState.Open)) {
                this.Adapter.DeleteCommand.Connection.Open();
            }
            try {
                int returnValue = this.Adapter.DeleteCommand.ExecuteNonQuery();
                return returnValue;
            }
            finally {
                if ((previousConnectionState == global::System.Data.ConnectionState.Closed)) {
                    this.Adapter.DeleteCommand.Connection.Close();
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Insert, true)]
        public virtual int Insert(short id_zarizeni, short id_funkce, global::System.Nullable<short> pouzito, global::System.Nullable<short> aktivni, string popis, short id_jidelna, short id_vydejna, global::System.Nullable<global::System.DateTime> last_contact, string stav_queue, global::System.Nullable<short> id_sklad) {
            this.Adapter.InsertCommand.Parameters[0].Value = ((short)(id_zarizeni));
            this.Adapter.InsertCommand.Parameters[1].Value = ((short)(id_funkce));
            if ((pouzito.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[2].Value = ((short)(pouzito.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[2].Value = global::System.DBNull.Value;
            }
            if ((aktivni.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[3].Value = ((short)(aktivni.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[3].Value = global::System.DBNull.Value;
            }
            if ((popis == null)) {
                throw new global::System.ArgumentNullException("popis");
            }
            else {
                this.Adapter.InsertCommand.Parameters[4].Value = ((string)(popis));
            }
            this.Adapter.InsertCommand.Parameters[5].Value = ((short)(id_jidelna));
            this.Adapter.InsertCommand.Parameters[6].Value = ((short)(id_vydejna));
            if ((last_contact.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[7].Value = ((System.DateTime)(last_contact.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[7].Value = global::System.DBNull.Value;
            }
            if ((stav_queue == null)) {
                this.Adapter.InsertCommand.Parameters[8].Value = global::System.DBNull.Value;
            }
            else {
                this.Adapter.InsertCommand.Parameters[8].Value = ((string)(stav_queue));
            }
            if ((id_sklad.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[9].Value = ((short)(id_sklad.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[9].Value = global::System.DBNull.Value;
            }
            global::System.Data.ConnectionState previousConnectionState = this.Adapter.InsertCommand.Connection.State;
            if (((this.Adapter.InsertCommand.Connection.State & global::System.Data.ConnectionState.Open) 
                        != global::System.Data.ConnectionState.Open)) {
                this.Adapter.InsertCommand.Connection.Open();
            }
            try {
                int returnValue = this.Adapter.InsertCommand.ExecuteNonQuery();
                return returnValue;
            }
            finally {
                if ((previousConnectionState == global::System.Data.ConnectionState.Closed)) {
                    this.Adapter.InsertCommand.Connection.Close();
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Update, true)]
        public virtual int Update(short id_zarizeni, short id_funkce, global::System.Nullable<short> pouzito, global::System.Nullable<short> aktivni, string popis, short id_jidelna, short id_vydejna, global::System.Nullable<global::System.DateTime> last_contact, string stav_queue, global::System.Nullable<short> id_sklad, short Original_id_zarizeni) {
            this.Adapter.UpdateCommand.Parameters[0].Value = ((short)(id_zarizeni));
            this.Adapter.UpdateCommand.Parameters[1].Value = ((short)(id_funkce));
            if ((pouzito.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[2].Value = ((short)(pouzito.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[2].Value = global::System.DBNull.Value;
            }
            if ((aktivni.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[3].Value = ((short)(aktivni.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[3].Value = global::System.DBNull.Value;
            }
            if ((popis == null)) {
                throw new global::System.ArgumentNullException("popis");
            }
            else {
                this.Adapter.UpdateCommand.Parameters[4].Value = ((string)(popis));
            }
            this.Adapter.UpdateCommand.Parameters[5].Value = ((short)(id_jidelna));
            this.Adapter.UpdateCommand.Parameters[6].Value = ((short)(id_vydejna));
            if ((last_contact.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[7].Value = ((System.DateTime)(last_contact.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[7].Value = global::System.DBNull.Value;
            }
            if ((stav_queue == null)) {
                this.Adapter.UpdateCommand.Parameters[8].Value = global::System.DBNull.Value;
            }
            else {
                this.Adapter.UpdateCommand.Parameters[8].Value = ((string)(stav_queue));
            }
            if ((id_sklad.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[9].Value = ((short)(id_sklad.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[9].Value = global::System.DBNull.Value;
            }
            this.Adapter.UpdateCommand.Parameters[10].Value = ((short)(Original_id_zarizeni));
            global::System.Data.ConnectionState previousConnectionState = this.Adapter.UpdateCommand.Connection.State;
            if (((this.Adapter.UpdateCommand.Connection.State & global::System.Data.ConnectionState.Open) 
                        != global::System.Data.ConnectionState.Open)) {
                this.Adapter.UpdateCommand.Connection.Open();
            }
            try {
                int returnValue = this.Adapter.UpdateCommand.ExecuteNonQuery();
                return returnValue;
            }
            finally {
                if ((previousConnectionState == global::System.Data.ConnectionState.Closed)) {
                    this.Adapter.UpdateCommand.Connection.Close();
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Update, true)]
        public virtual int Update(short id_funkce, global::System.Nullable<short> pouzito, global::System.Nullable<short> aktivni, string popis, short id_jidelna, short id_vydejna, global::System.Nullable<global::System.DateTime> last_contact, string stav_queue, global::System.Nullable<short> id_sklad, short Original_id_zarizeni) {
            return this.Update(Original_id_zarizeni, id_funkce, pouzito, aktivni, popis, id_jidelna, id_vydejna, last_contact, stav_queue, id_sklad, Original_id_zarizeni);
        }
    }
    
    /// <summary>
    ///Represents the connection and commands used to retrieve and save data.
    ///</summary>
    [global::System.ComponentModel.DesignerCategoryAttribute("code")]
    [global::System.ComponentModel.ToolboxItem(true)]
    [global::System.ComponentModel.DataObjectAttribute(true)]
    [global::System.ComponentModel.DesignerAttribute("Microsoft.VSDesigner.DataSource.Design.TableAdapterDesigner, Microsoft.VSDesigner" +
        ", Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a")]
    [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
    public partial class SaveAktualizacePrubehTableAdapter : global::Anete.Common.Data.Interface.CentralServerTableAdapter {
        
        private global::System.Data.SqlClient.SqlDataAdapter _adapter;
        
        private global::System.Data.SqlClient.SqlConnection _connection;
        
        private global::System.Data.SqlClient.SqlTransaction _transaction;
        
        private global::System.Data.SqlClient.SqlCommand[] _commandCollection;
        
        private bool _clearBeforeFill;
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        public SaveAktualizacePrubehTableAdapter() {
            this.ClearBeforeFill = true;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        protected internal global::System.Data.SqlClient.SqlDataAdapter Adapter {
            get {
                if ((this._adapter == null)) {
                    this.InitAdapter();
                }
                return this._adapter;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        internal global::System.Data.SqlClient.SqlConnection Connection {
            get {
                if ((this._connection == null)) {
                    this.InitConnection();
                }
                return this._connection;
            }
            set {
                this._connection = value;
                if ((this.Adapter.InsertCommand != null)) {
                    this.Adapter.InsertCommand.Connection = value;
                }
                if ((this.Adapter.DeleteCommand != null)) {
                    this.Adapter.DeleteCommand.Connection = value;
                }
                if ((this.Adapter.UpdateCommand != null)) {
                    this.Adapter.UpdateCommand.Connection = value;
                }
                for (int i = 0; (i < this.CommandCollection.Length); i = (i + 1)) {
                    if ((this.CommandCollection[i] != null)) {
                        ((global::System.Data.SqlClient.SqlCommand)(this.CommandCollection[i])).Connection = value;
                    }
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        internal global::System.Data.SqlClient.SqlTransaction Transaction {
            get {
                return this._transaction;
            }
            set {
                this._transaction = value;
                for (int i = 0; (i < this.CommandCollection.Length); i = (i + 1)) {
                    this.CommandCollection[i].Transaction = this._transaction;
                }
                if (((this.Adapter != null) 
                            && (this.Adapter.DeleteCommand != null))) {
                    this.Adapter.DeleteCommand.Transaction = this._transaction;
                }
                if (((this.Adapter != null) 
                            && (this.Adapter.InsertCommand != null))) {
                    this.Adapter.InsertCommand.Transaction = this._transaction;
                }
                if (((this.Adapter != null) 
                            && (this.Adapter.UpdateCommand != null))) {
                    this.Adapter.UpdateCommand.Transaction = this._transaction;
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        protected global::System.Data.SqlClient.SqlCommand[] CommandCollection {
            get {
                if ((this._commandCollection == null)) {
                    this.InitCommandCollection();
                }
                return this._commandCollection;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        public bool ClearBeforeFill {
            get {
                return this._clearBeforeFill;
            }
            set {
                this._clearBeforeFill = value;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        private void InitAdapter() {
            this._adapter = new global::System.Data.SqlClient.SqlDataAdapter();
            global::System.Data.Common.DataTableMapping tableMapping = new global::System.Data.Common.DataTableMapping();
            tableMapping.SourceTable = "Table";
            tableMapping.DataSetTable = "SaveAktualizacePrubeh";
            tableMapping.ColumnMappings.Add("IDAktualizacePrubeh", "IDAktualizacePrubeh");
            this._adapter.TableMappings.Add(tableMapping);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        private void InitConnection() {
            this._connection = new global::System.Data.SqlClient.SqlConnection();
            this._connection.ConnectionString = global::Anete.AutoUpgrades.Properties.Settings.Default.AneteUpdatesConnectionString;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        private void InitCommandCollection() {
            this._commandCollection = new global::System.Data.SqlClient.SqlCommand[1];
            this._commandCollection[0] = new global::System.Data.SqlClient.SqlCommand();
            this._commandCollection[0].Connection = this.Connection;
            this._commandCollection[0].CommandText = "dbo.SaveAktualizacePrubeh";
            this._commandCollection[0].CommandType = global::System.Data.CommandType.StoredProcedure;
            this._commandCollection[0].Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@RETURN_VALUE", global::System.Data.SqlDbType.Int, 4, global::System.Data.ParameterDirection.ReturnValue, 10, 0, null, global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._commandCollection[0].Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@LogInfo", global::System.Data.SqlDbType.Text, 2147483647, global::System.Data.ParameterDirection.Input, 0, 0, null, global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._commandCollection[0].Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Stav", global::System.Data.SqlDbType.Int, 4, global::System.Data.ParameterDirection.Input, 10, 0, null, global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._commandCollection[0].Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@IDDavky", global::System.Data.SqlDbType.Int, 4, global::System.Data.ParameterDirection.Input, 10, 0, null, global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._commandCollection[0].Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Datum", global::System.Data.SqlDbType.DateTime, 8, global::System.Data.ParameterDirection.Input, 23, 3, null, global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._commandCollection[0].Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@PopisPC", global::System.Data.SqlDbType.VarChar, 400, global::System.Data.ParameterDirection.Input, 0, 0, null, global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._commandCollection[0].Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@IPAdresa", global::System.Data.SqlDbType.VarChar, 400, global::System.Data.ParameterDirection.Input, 0, 0, null, global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._commandCollection[0].Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@IdZarizeni", global::System.Data.SqlDbType.Text, 2147483647, global::System.Data.ParameterDirection.Input, 0, 0, null, global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Fill, true)]
        public virtual int Fill(AutoUpgradeDs.SaveAktualizacePrubehDataTable dataTable, string LogInfo, global::System.Nullable<int> Stav, global::System.Nullable<int> IDDavky, global::System.Nullable<global::System.DateTime> Datum, string PopisPC, string IPAdresa, string IdZarizeni) {
            this.Adapter.SelectCommand = this.CommandCollection[0];
            if ((LogInfo == null)) {
                this.Adapter.SelectCommand.Parameters[1].Value = global::System.DBNull.Value;
            }
            else {
                this.Adapter.SelectCommand.Parameters[1].Value = ((string)(LogInfo));
            }
            if ((Stav.HasValue == true)) {
                this.Adapter.SelectCommand.Parameters[2].Value = ((int)(Stav.Value));
            }
            else {
                this.Adapter.SelectCommand.Parameters[2].Value = global::System.DBNull.Value;
            }
            if ((IDDavky.HasValue == true)) {
                this.Adapter.SelectCommand.Parameters[3].Value = ((int)(IDDavky.Value));
            }
            else {
                this.Adapter.SelectCommand.Parameters[3].Value = global::System.DBNull.Value;
            }
            if ((Datum.HasValue == true)) {
                this.Adapter.SelectCommand.Parameters[4].Value = ((System.DateTime)(Datum.Value));
            }
            else {
                this.Adapter.SelectCommand.Parameters[4].Value = global::System.DBNull.Value;
            }
            if ((PopisPC == null)) {
                this.Adapter.SelectCommand.Parameters[5].Value = global::System.DBNull.Value;
            }
            else {
                this.Adapter.SelectCommand.Parameters[5].Value = ((string)(PopisPC));
            }
            if ((IPAdresa == null)) {
                this.Adapter.SelectCommand.Parameters[6].Value = global::System.DBNull.Value;
            }
            else {
                this.Adapter.SelectCommand.Parameters[6].Value = ((string)(IPAdresa));
            }
            if ((IdZarizeni == null)) {
                this.Adapter.SelectCommand.Parameters[7].Value = global::System.DBNull.Value;
            }
            else {
                this.Adapter.SelectCommand.Parameters[7].Value = ((string)(IdZarizeni));
            }
            if ((this.ClearBeforeFill == true)) {
                dataTable.Clear();
            }
            int returnValue = this.Adapter.Fill(dataTable);
            return returnValue;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Select, true)]
        public virtual AutoUpgradeDs.SaveAktualizacePrubehDataTable GetData(string LogInfo, global::System.Nullable<int> Stav, global::System.Nullable<int> IDDavky, global::System.Nullable<global::System.DateTime> Datum, string PopisPC, string IPAdresa, string IdZarizeni) {
            this.Adapter.SelectCommand = this.CommandCollection[0];
            if ((LogInfo == null)) {
                this.Adapter.SelectCommand.Parameters[1].Value = global::System.DBNull.Value;
            }
            else {
                this.Adapter.SelectCommand.Parameters[1].Value = ((string)(LogInfo));
            }
            if ((Stav.HasValue == true)) {
                this.Adapter.SelectCommand.Parameters[2].Value = ((int)(Stav.Value));
            }
            else {
                this.Adapter.SelectCommand.Parameters[2].Value = global::System.DBNull.Value;
            }
            if ((IDDavky.HasValue == true)) {
                this.Adapter.SelectCommand.Parameters[3].Value = ((int)(IDDavky.Value));
            }
            else {
                this.Adapter.SelectCommand.Parameters[3].Value = global::System.DBNull.Value;
            }
            if ((Datum.HasValue == true)) {
                this.Adapter.SelectCommand.Parameters[4].Value = ((System.DateTime)(Datum.Value));
            }
            else {
                this.Adapter.SelectCommand.Parameters[4].Value = global::System.DBNull.Value;
            }
            if ((PopisPC == null)) {
                this.Adapter.SelectCommand.Parameters[5].Value = global::System.DBNull.Value;
            }
            else {
                this.Adapter.SelectCommand.Parameters[5].Value = ((string)(PopisPC));
            }
            if ((IPAdresa == null)) {
                this.Adapter.SelectCommand.Parameters[6].Value = global::System.DBNull.Value;
            }
            else {
                this.Adapter.SelectCommand.Parameters[6].Value = ((string)(IPAdresa));
            }
            if ((IdZarizeni == null)) {
                this.Adapter.SelectCommand.Parameters[7].Value = global::System.DBNull.Value;
            }
            else {
                this.Adapter.SelectCommand.Parameters[7].Value = ((string)(IdZarizeni));
            }
            AutoUpgradeDs.SaveAktualizacePrubehDataTable dataTable = new AutoUpgradeDs.SaveAktualizacePrubehDataTable();
            this.Adapter.Fill(dataTable);
            return dataTable;
        }
    }
    
    /// <summary>
    ///TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
    ///</summary>
    [global::System.ComponentModel.DesignerCategoryAttribute("code")]
    [global::System.ComponentModel.ToolboxItem(true)]
    [global::System.ComponentModel.DesignerAttribute("Microsoft.VSDesigner.DataSource.Design.TableAdapterManagerDesigner, Microsoft.VSD" +
        "esigner, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a")]
    [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapterManager")]
    public partial class TableAdapterManager : global::System.ComponentModel.Component {
        
        private UpdateOrderOption _updateOrder;
        
        private AktualizaceTableAdapter _aktualizaceTableAdapter;
        
        private AktualizacePrubehTableAdapter _aktualizacePrubehTableAdapter;
        
        private AktualizaceDavkyTableAdapter _aktualizaceDavkyTableAdapter;
        
        private AktualizaceDavky_CFZarizeniTableAdapter _aktualizaceDavky_CFZarizeniTableAdapter;
        
        private CFZarizeniTableAdapter _cFZarizeniTableAdapter;
        
        private bool _backupDataSetBeforeUpdate;
        
        private global::System.Data.IDbConnection _connection;
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        public UpdateOrderOption UpdateOrder {
            get {
                return this._updateOrder;
            }
            set {
                this._updateOrder = value;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.EditorAttribute("Microsoft.VSDesigner.DataSource.Design.TableAdapterManagerPropertyEditor, Microso" +
            "ft.VSDesigner, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3" +
            "a", "System.Drawing.Design.UITypeEditor")]
        public AktualizaceTableAdapter AktualizaceTableAdapter {
            get {
                return this._aktualizaceTableAdapter;
            }
            set {
                this._aktualizaceTableAdapter = value;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.EditorAttribute("Microsoft.VSDesigner.DataSource.Design.TableAdapterManagerPropertyEditor, Microso" +
            "ft.VSDesigner, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3" +
            "a", "System.Drawing.Design.UITypeEditor")]
        public AktualizacePrubehTableAdapter AktualizacePrubehTableAdapter {
            get {
                return this._aktualizacePrubehTableAdapter;
            }
            set {
                this._aktualizacePrubehTableAdapter = value;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.EditorAttribute("Microsoft.VSDesigner.DataSource.Design.TableAdapterManagerPropertyEditor, Microso" +
            "ft.VSDesigner, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3" +
            "a", "System.Drawing.Design.UITypeEditor")]
        public AktualizaceDavkyTableAdapter AktualizaceDavkyTableAdapter {
            get {
                return this._aktualizaceDavkyTableAdapter;
            }
            set {
                this._aktualizaceDavkyTableAdapter = value;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.EditorAttribute("Microsoft.VSDesigner.DataSource.Design.TableAdapterManagerPropertyEditor, Microso" +
            "ft.VSDesigner, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3" +
            "a", "System.Drawing.Design.UITypeEditor")]
        public AktualizaceDavky_CFZarizeniTableAdapter AktualizaceDavky_CFZarizeniTableAdapter {
            get {
                return this._aktualizaceDavky_CFZarizeniTableAdapter;
            }
            set {
                this._aktualizaceDavky_CFZarizeniTableAdapter = value;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.EditorAttribute("Microsoft.VSDesigner.DataSource.Design.TableAdapterManagerPropertyEditor, Microso" +
            "ft.VSDesigner, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3" +
            "a", "System.Drawing.Design.UITypeEditor")]
        public CFZarizeniTableAdapter CFZarizeniTableAdapter {
            get {
                return this._cFZarizeniTableAdapter;
            }
            set {
                this._cFZarizeniTableAdapter = value;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        public bool BackupDataSetBeforeUpdate {
            get {
                return this._backupDataSetBeforeUpdate;
            }
            set {
                this._backupDataSetBeforeUpdate = value;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Browsable(false)]
        public global::System.Data.IDbConnection Connection {
            get {
                if ((this._connection != null)) {
                    return this._connection;
                }
                if (((this._aktualizaceTableAdapter != null) 
                            && (this._aktualizaceTableAdapter.Connection != null))) {
                    return this._aktualizaceTableAdapter.Connection;
                }
                if (((this._aktualizacePrubehTableAdapter != null) 
                            && (this._aktualizacePrubehTableAdapter.Connection != null))) {
                    return this._aktualizacePrubehTableAdapter.Connection;
                }
                if (((this._aktualizaceDavkyTableAdapter != null) 
                            && (this._aktualizaceDavkyTableAdapter.Connection != null))) {
                    return this._aktualizaceDavkyTableAdapter.Connection;
                }
                if (((this._aktualizaceDavky_CFZarizeniTableAdapter != null) 
                            && (this._aktualizaceDavky_CFZarizeniTableAdapter.Connection != null))) {
                    return this._aktualizaceDavky_CFZarizeniTableAdapter.Connection;
                }
                if (((this._cFZarizeniTableAdapter != null) 
                            && (this._cFZarizeniTableAdapter.Connection != null))) {
                    return this._cFZarizeniTableAdapter.Connection;
                }
                return null;
            }
            set {
                this._connection = value;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        [global::System.ComponentModel.Browsable(false)]
        public int TableAdapterInstanceCount {
            get {
                int count = 0;
                if ((this._aktualizaceTableAdapter != null)) {
                    count = (count + 1);
                }
                if ((this._aktualizacePrubehTableAdapter != null)) {
                    count = (count + 1);
                }
                if ((this._aktualizaceDavkyTableAdapter != null)) {
                    count = (count + 1);
                }
                if ((this._aktualizaceDavky_CFZarizeniTableAdapter != null)) {
                    count = (count + 1);
                }
                if ((this._cFZarizeniTableAdapter != null)) {
                    count = (count + 1);
                }
                return count;
            }
        }
        
        /// <summary>
        ///Update rows in top-down order.
        ///</summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        private int UpdateUpdatedRows(AutoUpgradeDs dataSet, global::System.Collections.Generic.List<global::System.Data.DataRow> allChangedRows, global::System.Collections.Generic.List<global::System.Data.DataRow> allAddedRows) {
            int result = 0;
            if ((this._aktualizaceTableAdapter != null)) {
                global::System.Data.DataRow[] updatedRows = dataSet.Aktualizace.Select(null, null, global::System.Data.DataViewRowState.ModifiedCurrent);
                updatedRows = this.GetRealUpdatedRows(updatedRows, allAddedRows);
                if (((updatedRows != null) 
                            && (0 < updatedRows.Length))) {
                    this.SortSelfReferenceRows(updatedRows, dataSet.Relations["Aktualizace_Aktualizace"], false);
                    result = (result + this._aktualizaceTableAdapter.Update(updatedRows));
                    allChangedRows.AddRange(updatedRows);
                }
            }
            if ((this._cFZarizeniTableAdapter != null)) {
                global::System.Data.DataRow[] updatedRows = dataSet.CFZarizeni.Select(null, null, global::System.Data.DataViewRowState.ModifiedCurrent);
                updatedRows = this.GetRealUpdatedRows(updatedRows, allAddedRows);
                if (((updatedRows != null) 
                            && (0 < updatedRows.Length))) {
                    result = (result + this._cFZarizeniTableAdapter.Update(updatedRows));
                    allChangedRows.AddRange(updatedRows);
                }
            }
            if ((this._aktualizaceDavkyTableAdapter != null)) {
                global::System.Data.DataRow[] updatedRows = dataSet.AktualizaceDavky.Select(null, null, global::System.Data.DataViewRowState.ModifiedCurrent);
                updatedRows = this.GetRealUpdatedRows(updatedRows, allAddedRows);
                if (((updatedRows != null) 
                            && (0 < updatedRows.Length))) {
                    result = (result + this._aktualizaceDavkyTableAdapter.Update(updatedRows));
                    allChangedRows.AddRange(updatedRows);
                }
            }
            if ((this._aktualizacePrubehTableAdapter != null)) {
                global::System.Data.DataRow[] updatedRows = dataSet.AktualizacePrubeh.Select(null, null, global::System.Data.DataViewRowState.ModifiedCurrent);
                updatedRows = this.GetRealUpdatedRows(updatedRows, allAddedRows);
                if (((updatedRows != null) 
                            && (0 < updatedRows.Length))) {
                    result = (result + this._aktualizacePrubehTableAdapter.Update(updatedRows));
                    allChangedRows.AddRange(updatedRows);
                }
            }
            if ((this._aktualizaceDavky_CFZarizeniTableAdapter != null)) {
                global::System.Data.DataRow[] updatedRows = dataSet.AktualizaceDavky_CFZarizeni.Select(null, null, global::System.Data.DataViewRowState.ModifiedCurrent);
                updatedRows = this.GetRealUpdatedRows(updatedRows, allAddedRows);
                if (((updatedRows != null) 
                            && (0 < updatedRows.Length))) {
                    result = (result + this._aktualizaceDavky_CFZarizeniTableAdapter.Update(updatedRows));
                    allChangedRows.AddRange(updatedRows);
                }
            }
            return result;
        }
        
        /// <summary>
        ///Insert rows in top-down order.
        ///</summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        private int UpdateInsertedRows(AutoUpgradeDs dataSet, global::System.Collections.Generic.List<global::System.Data.DataRow> allAddedRows) {
            int result = 0;
            if ((this._aktualizaceTableAdapter != null)) {
                global::System.Data.DataRow[] addedRows = dataSet.Aktualizace.Select(null, null, global::System.Data.DataViewRowState.Added);
                if (((addedRows != null) 
                            && (0 < addedRows.Length))) {
                    this.SortSelfReferenceRows(addedRows, dataSet.Relations["Aktualizace_Aktualizace"], false);
                    result = (result + this._aktualizaceTableAdapter.Update(addedRows));
                    allAddedRows.AddRange(addedRows);
                }
            }
            if ((this._cFZarizeniTableAdapter != null)) {
                global::System.Data.DataRow[] addedRows = dataSet.CFZarizeni.Select(null, null, global::System.Data.DataViewRowState.Added);
                if (((addedRows != null) 
                            && (0 < addedRows.Length))) {
                    result = (result + this._cFZarizeniTableAdapter.Update(addedRows));
                    allAddedRows.AddRange(addedRows);
                }
            }
            if ((this._aktualizaceDavkyTableAdapter != null)) {
                global::System.Data.DataRow[] addedRows = dataSet.AktualizaceDavky.Select(null, null, global::System.Data.DataViewRowState.Added);
                if (((addedRows != null) 
                            && (0 < addedRows.Length))) {
                    result = (result + this._aktualizaceDavkyTableAdapter.Update(addedRows));
                    allAddedRows.AddRange(addedRows);
                }
            }
            if ((this._aktualizacePrubehTableAdapter != null)) {
                global::System.Data.DataRow[] addedRows = dataSet.AktualizacePrubeh.Select(null, null, global::System.Data.DataViewRowState.Added);
                if (((addedRows != null) 
                            && (0 < addedRows.Length))) {
                    result = (result + this._aktualizacePrubehTableAdapter.Update(addedRows));
                    allAddedRows.AddRange(addedRows);
                }
            }
            if ((this._aktualizaceDavky_CFZarizeniTableAdapter != null)) {
                global::System.Data.DataRow[] addedRows = dataSet.AktualizaceDavky_CFZarizeni.Select(null, null, global::System.Data.DataViewRowState.Added);
                if (((addedRows != null) 
                            && (0 < addedRows.Length))) {
                    result = (result + this._aktualizaceDavky_CFZarizeniTableAdapter.Update(addedRows));
                    allAddedRows.AddRange(addedRows);
                }
            }
            return result;
        }
        
        /// <summary>
        ///Delete rows in bottom-up order.
        ///</summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        private int UpdateDeletedRows(AutoUpgradeDs dataSet, global::System.Collections.Generic.List<global::System.Data.DataRow> allChangedRows) {
            int result = 0;
            if ((this._aktualizaceDavky_CFZarizeniTableAdapter != null)) {
                global::System.Data.DataRow[] deletedRows = dataSet.AktualizaceDavky_CFZarizeni.Select(null, null, global::System.Data.DataViewRowState.Deleted);
                if (((deletedRows != null) 
                            && (0 < deletedRows.Length))) {
                    result = (result + this._aktualizaceDavky_CFZarizeniTableAdapter.Update(deletedRows));
                    allChangedRows.AddRange(deletedRows);
                }
            }
            if ((this._aktualizacePrubehTableAdapter != null)) {
                global::System.Data.DataRow[] deletedRows = dataSet.AktualizacePrubeh.Select(null, null, global::System.Data.DataViewRowState.Deleted);
                if (((deletedRows != null) 
                            && (0 < deletedRows.Length))) {
                    result = (result + this._aktualizacePrubehTableAdapter.Update(deletedRows));
                    allChangedRows.AddRange(deletedRows);
                }
            }
            if ((this._aktualizaceDavkyTableAdapter != null)) {
                global::System.Data.DataRow[] deletedRows = dataSet.AktualizaceDavky.Select(null, null, global::System.Data.DataViewRowState.Deleted);
                if (((deletedRows != null) 
                            && (0 < deletedRows.Length))) {
                    result = (result + this._aktualizaceDavkyTableAdapter.Update(deletedRows));
                    allChangedRows.AddRange(deletedRows);
                }
            }
            if ((this._cFZarizeniTableAdapter != null)) {
                global::System.Data.DataRow[] deletedRows = dataSet.CFZarizeni.Select(null, null, global::System.Data.DataViewRowState.Deleted);
                if (((deletedRows != null) 
                            && (0 < deletedRows.Length))) {
                    result = (result + this._cFZarizeniTableAdapter.Update(deletedRows));
                    allChangedRows.AddRange(deletedRows);
                }
            }
            if ((this._aktualizaceTableAdapter != null)) {
                global::System.Data.DataRow[] deletedRows = dataSet.Aktualizace.Select(null, null, global::System.Data.DataViewRowState.Deleted);
                if (((deletedRows != null) 
                            && (0 < deletedRows.Length))) {
                    this.SortSelfReferenceRows(deletedRows, dataSet.Relations["Aktualizace_Aktualizace"], true);
                    result = (result + this._aktualizaceTableAdapter.Update(deletedRows));
                    allChangedRows.AddRange(deletedRows);
                }
            }
            return result;
        }
        
        /// <summary>
        ///Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
        ///</summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        private global::System.Data.DataRow[] GetRealUpdatedRows(global::System.Data.DataRow[] updatedRows, global::System.Collections.Generic.List<global::System.Data.DataRow> allAddedRows) {
            if (((updatedRows == null) 
                        || (updatedRows.Length < 1))) {
                return updatedRows;
            }
            if (((allAddedRows == null) 
                        || (allAddedRows.Count < 1))) {
                return updatedRows;
            }
            global::System.Collections.Generic.List<global::System.Data.DataRow> realUpdatedRows = new global::System.Collections.Generic.List<global::System.Data.DataRow>();
            for (int i = 0; (i < updatedRows.Length); i = (i + 1)) {
                global::System.Data.DataRow row = updatedRows[i];
                if ((allAddedRows.Contains(row) == false)) {
                    realUpdatedRows.Add(row);
                }
            }
            return realUpdatedRows.ToArray();
        }
        
        /// <summary>
        ///Update all changes to the dataset.
        ///</summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        public virtual int UpdateAll(AutoUpgradeDs dataSet) {
            if ((dataSet == null)) {
                throw new global::System.ArgumentNullException("dataSet");
            }
            if ((dataSet.HasChanges() == false)) {
                return 0;
            }
            if (((this._aktualizaceTableAdapter != null) 
                        && (this.MatchTableAdapterConnection(this._aktualizaceTableAdapter.Connection) == false))) {
                throw new global::System.ArgumentException("All TableAdapters managed by a TableAdapterManager must use the same connection s" +
                        "tring.");
            }
            if (((this._aktualizacePrubehTableAdapter != null) 
                        && (this.MatchTableAdapterConnection(this._aktualizacePrubehTableAdapter.Connection) == false))) {
                throw new global::System.ArgumentException("All TableAdapters managed by a TableAdapterManager must use the same connection s" +
                        "tring.");
            }
            if (((this._aktualizaceDavkyTableAdapter != null) 
                        && (this.MatchTableAdapterConnection(this._aktualizaceDavkyTableAdapter.Connection) == false))) {
                throw new global::System.ArgumentException("All TableAdapters managed by a TableAdapterManager must use the same connection s" +
                        "tring.");
            }
            if (((this._aktualizaceDavky_CFZarizeniTableAdapter != null) 
                        && (this.MatchTableAdapterConnection(this._aktualizaceDavky_CFZarizeniTableAdapter.Connection) == false))) {
                throw new global::System.ArgumentException("All TableAdapters managed by a TableAdapterManager must use the same connection s" +
                        "tring.");
            }
            if (((this._cFZarizeniTableAdapter != null) 
                        && (this.MatchTableAdapterConnection(this._cFZarizeniTableAdapter.Connection) == false))) {
                throw new global::System.ArgumentException("All TableAdapters managed by a TableAdapterManager must use the same connection s" +
                        "tring.");
            }
            global::System.Data.IDbConnection workConnection = this.Connection;
            if ((workConnection == null)) {
                throw new global::System.ApplicationException("TableAdapterManager contains no connection information. Set each TableAdapterMana" +
                        "ger TableAdapter property to a valid TableAdapter instance.");
            }
            bool workConnOpened = false;
            if (((workConnection.State & global::System.Data.ConnectionState.Broken) 
                        == global::System.Data.ConnectionState.Broken)) {
                workConnection.Close();
            }
            if ((workConnection.State == global::System.Data.ConnectionState.Closed)) {
                workConnection.Open();
                workConnOpened = true;
            }
            global::System.Data.IDbTransaction workTransaction = workConnection.BeginTransaction();
            if ((workTransaction == null)) {
                throw new global::System.ApplicationException("The transaction cannot begin. The current data connection does not support transa" +
                        "ctions or the current state is not allowing the transaction to begin.");
            }
            global::System.Collections.Generic.List<global::System.Data.DataRow> allChangedRows = new global::System.Collections.Generic.List<global::System.Data.DataRow>();
            global::System.Collections.Generic.List<global::System.Data.DataRow> allAddedRows = new global::System.Collections.Generic.List<global::System.Data.DataRow>();
            global::System.Collections.Generic.List<global::System.Data.Common.DataAdapter> adaptersWithAcceptChangesDuringUpdate = new global::System.Collections.Generic.List<global::System.Data.Common.DataAdapter>();
            global::System.Collections.Generic.Dictionary<object, global::System.Data.IDbConnection> revertConnections = new global::System.Collections.Generic.Dictionary<object, global::System.Data.IDbConnection>();
            int result = 0;
            global::System.Data.DataSet backupDataSet = null;
            if (this.BackupDataSetBeforeUpdate) {
                backupDataSet = new global::System.Data.DataSet();
                backupDataSet.Merge(dataSet);
            }
            try {
                // ---- Prepare for update -----------
                //
                if ((this._aktualizaceTableAdapter != null)) {
                    revertConnections.Add(this._aktualizaceTableAdapter, this._aktualizaceTableAdapter.Connection);
                    this._aktualizaceTableAdapter.Connection = ((global::System.Data.SqlClient.SqlConnection)(workConnection));
                    this._aktualizaceTableAdapter.Transaction = ((global::System.Data.SqlClient.SqlTransaction)(workTransaction));
                    if (this._aktualizaceTableAdapter.Adapter.AcceptChangesDuringUpdate) {
                        this._aktualizaceTableAdapter.Adapter.AcceptChangesDuringUpdate = false;
                        adaptersWithAcceptChangesDuringUpdate.Add(this._aktualizaceTableAdapter.Adapter);
                    }
                }
                if ((this._aktualizacePrubehTableAdapter != null)) {
                    revertConnections.Add(this._aktualizacePrubehTableAdapter, this._aktualizacePrubehTableAdapter.Connection);
                    this._aktualizacePrubehTableAdapter.Connection = ((global::System.Data.SqlClient.SqlConnection)(workConnection));
                    this._aktualizacePrubehTableAdapter.Transaction = ((global::System.Data.SqlClient.SqlTransaction)(workTransaction));
                    if (this._aktualizacePrubehTableAdapter.Adapter.AcceptChangesDuringUpdate) {
                        this._aktualizacePrubehTableAdapter.Adapter.AcceptChangesDuringUpdate = false;
                        adaptersWithAcceptChangesDuringUpdate.Add(this._aktualizacePrubehTableAdapter.Adapter);
                    }
                }
                if ((this._aktualizaceDavkyTableAdapter != null)) {
                    revertConnections.Add(this._aktualizaceDavkyTableAdapter, this._aktualizaceDavkyTableAdapter.Connection);
                    this._aktualizaceDavkyTableAdapter.Connection = ((global::System.Data.SqlClient.SqlConnection)(workConnection));
                    this._aktualizaceDavkyTableAdapter.Transaction = ((global::System.Data.SqlClient.SqlTransaction)(workTransaction));
                    if (this._aktualizaceDavkyTableAdapter.Adapter.AcceptChangesDuringUpdate) {
                        this._aktualizaceDavkyTableAdapter.Adapter.AcceptChangesDuringUpdate = false;
                        adaptersWithAcceptChangesDuringUpdate.Add(this._aktualizaceDavkyTableAdapter.Adapter);
                    }
                }
                if ((this._aktualizaceDavky_CFZarizeniTableAdapter != null)) {
                    revertConnections.Add(this._aktualizaceDavky_CFZarizeniTableAdapter, this._aktualizaceDavky_CFZarizeniTableAdapter.Connection);
                    this._aktualizaceDavky_CFZarizeniTableAdapter.Connection = ((global::System.Data.SqlClient.SqlConnection)(workConnection));
                    this._aktualizaceDavky_CFZarizeniTableAdapter.Transaction = ((global::System.Data.SqlClient.SqlTransaction)(workTransaction));
                    if (this._aktualizaceDavky_CFZarizeniTableAdapter.Adapter.AcceptChangesDuringUpdate) {
                        this._aktualizaceDavky_CFZarizeniTableAdapter.Adapter.AcceptChangesDuringUpdate = false;
                        adaptersWithAcceptChangesDuringUpdate.Add(this._aktualizaceDavky_CFZarizeniTableAdapter.Adapter);
                    }
                }
                if ((this._cFZarizeniTableAdapter != null)) {
                    revertConnections.Add(this._cFZarizeniTableAdapter, this._cFZarizeniTableAdapter.Connection);
                    this._cFZarizeniTableAdapter.Connection = ((global::System.Data.SqlClient.SqlConnection)(workConnection));
                    this._cFZarizeniTableAdapter.Transaction = ((global::System.Data.SqlClient.SqlTransaction)(workTransaction));
                    if (this._cFZarizeniTableAdapter.Adapter.AcceptChangesDuringUpdate) {
                        this._cFZarizeniTableAdapter.Adapter.AcceptChangesDuringUpdate = false;
                        adaptersWithAcceptChangesDuringUpdate.Add(this._cFZarizeniTableAdapter.Adapter);
                    }
                }
                // 
                //---- Perform updates -----------
                //
                if ((this.UpdateOrder == UpdateOrderOption.UpdateInsertDelete)) {
                    result = (result + this.UpdateUpdatedRows(dataSet, allChangedRows, allAddedRows));
                    result = (result + this.UpdateInsertedRows(dataSet, allAddedRows));
                }
                else {
                    result = (result + this.UpdateInsertedRows(dataSet, allAddedRows));
                    result = (result + this.UpdateUpdatedRows(dataSet, allChangedRows, allAddedRows));
                }
                result = (result + this.UpdateDeletedRows(dataSet, allChangedRows));
                // 
                //---- Commit updates -----------
                //
                workTransaction.Commit();
                if ((0 < allAddedRows.Count)) {
                    global::System.Data.DataRow[] rows = new System.Data.DataRow[allAddedRows.Count];
                    allAddedRows.CopyTo(rows);
                    for (int i = 0; (i < rows.Length); i = (i + 1)) {
                        global::System.Data.DataRow row = rows[i];
                        row.AcceptChanges();
                    }
                }
                if ((0 < allChangedRows.Count)) {
                    global::System.Data.DataRow[] rows = new System.Data.DataRow[allChangedRows.Count];
                    allChangedRows.CopyTo(rows);
                    for (int i = 0; (i < rows.Length); i = (i + 1)) {
                        global::System.Data.DataRow row = rows[i];
                        row.AcceptChanges();
                    }
                }
            }
            catch (global::System.Exception ex) {
                workTransaction.Rollback();
                // ---- Restore the dataset -----------
                if (this.BackupDataSetBeforeUpdate) {
                    global::System.Diagnostics.Debug.Assert((backupDataSet != null));
                    dataSet.Clear();
                    dataSet.Merge(backupDataSet);
                }
                else {
                    if ((0 < allAddedRows.Count)) {
                        global::System.Data.DataRow[] rows = new System.Data.DataRow[allAddedRows.Count];
                        allAddedRows.CopyTo(rows);
                        for (int i = 0; (i < rows.Length); i = (i + 1)) {
                            global::System.Data.DataRow row = rows[i];
                            row.AcceptChanges();
                            row.SetAdded();
                        }
                    }
                }
                throw ex;
            }
            finally {
                if (workConnOpened) {
                    workConnection.Close();
                }
                if ((this._aktualizaceTableAdapter != null)) {
                    this._aktualizaceTableAdapter.Connection = ((global::System.Data.SqlClient.SqlConnection)(revertConnections[this._aktualizaceTableAdapter]));
                    this._aktualizaceTableAdapter.Transaction = null;
                }
                if ((this._aktualizacePrubehTableAdapter != null)) {
                    this._aktualizacePrubehTableAdapter.Connection = ((global::System.Data.SqlClient.SqlConnection)(revertConnections[this._aktualizacePrubehTableAdapter]));
                    this._aktualizacePrubehTableAdapter.Transaction = null;
                }
                if ((this._aktualizaceDavkyTableAdapter != null)) {
                    this._aktualizaceDavkyTableAdapter.Connection = ((global::System.Data.SqlClient.SqlConnection)(revertConnections[this._aktualizaceDavkyTableAdapter]));
                    this._aktualizaceDavkyTableAdapter.Transaction = null;
                }
                if ((this._aktualizaceDavky_CFZarizeniTableAdapter != null)) {
                    this._aktualizaceDavky_CFZarizeniTableAdapter.Connection = ((global::System.Data.SqlClient.SqlConnection)(revertConnections[this._aktualizaceDavky_CFZarizeniTableAdapter]));
                    this._aktualizaceDavky_CFZarizeniTableAdapter.Transaction = null;
                }
                if ((this._cFZarizeniTableAdapter != null)) {
                    this._cFZarizeniTableAdapter.Connection = ((global::System.Data.SqlClient.SqlConnection)(revertConnections[this._cFZarizeniTableAdapter]));
                    this._cFZarizeniTableAdapter.Transaction = null;
                }
                if ((0 < adaptersWithAcceptChangesDuringUpdate.Count)) {
                    global::System.Data.Common.DataAdapter[] adapters = new System.Data.Common.DataAdapter[adaptersWithAcceptChangesDuringUpdate.Count];
                    adaptersWithAcceptChangesDuringUpdate.CopyTo(adapters);
                    for (int i = 0; (i < adapters.Length); i = (i + 1)) {
                        global::System.Data.Common.DataAdapter adapter = adapters[i];
                        adapter.AcceptChangesDuringUpdate = true;
                    }
                }
            }
            return result;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        protected virtual void SortSelfReferenceRows(global::System.Data.DataRow[] rows, global::System.Data.DataRelation relation, bool childFirst) {
            global::System.Array.Sort<global::System.Data.DataRow>(rows, new SelfReferenceComparer(relation, childFirst));
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        protected virtual bool MatchTableAdapterConnection(global::System.Data.IDbConnection inputConnection) {
            if ((this._connection != null)) {
                return true;
            }
            if (((this.Connection == null) 
                        || (inputConnection == null))) {
                return true;
            }
            if (string.Equals(this.Connection.ConnectionString, inputConnection.ConnectionString, global::System.StringComparison.Ordinal)) {
                return true;
            }
            return false;
        }
        
        /// <summary>
        ///Update Order Option
        ///</summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        public enum UpdateOrderOption {
            
            InsertUpdateDelete = 0,
            
            UpdateInsertDelete = 1,
        }
        
        /// <summary>
        ///Used to sort self-referenced table's rows
        ///</summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
        private class SelfReferenceComparer : object, global::System.Collections.Generic.IComparer<global::System.Data.DataRow> {
            
            private global::System.Data.DataRelation _relation;
            
            private int _childFirst;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            internal SelfReferenceComparer(global::System.Data.DataRelation relation, bool childFirst) {
                this._relation = relation;
                if (childFirst) {
                    this._childFirst = -1;
                }
                else {
                    this._childFirst = 1;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            private global::System.Data.DataRow GetRoot(global::System.Data.DataRow row, out int distance) {
                global::System.Diagnostics.Debug.Assert((row != null));
                global::System.Data.DataRow root = row;
                distance = 0;

                global::System.Collections.Generic.IDictionary<global::System.Data.DataRow, global::System.Data.DataRow> traversedRows = new global::System.Collections.Generic.Dictionary<global::System.Data.DataRow, global::System.Data.DataRow>();
                traversedRows[row] = row;

                global::System.Data.DataRow parent = row.GetParentRow(this._relation, global::System.Data.DataRowVersion.Default);
                for (
                ; ((parent != null) 
                            && (traversedRows.ContainsKey(parent) == false)); 
                ) {
                    distance = (distance + 1);
                    root = parent;
                    traversedRows[parent] = parent;
                    parent = parent.GetParentRow(this._relation, global::System.Data.DataRowVersion.Default);
                }

                if ((distance == 0)) {
                    traversedRows.Clear();
                    traversedRows[row] = row;
                    parent = row.GetParentRow(this._relation, global::System.Data.DataRowVersion.Original);
                    for (
                    ; ((parent != null) 
                                && (traversedRows.ContainsKey(parent) == false)); 
                    ) {
                        distance = (distance + 1);
                        root = parent;
                        traversedRows[parent] = parent;
                        parent = parent.GetParentRow(this._relation, global::System.Data.DataRowVersion.Original);
                    }
                }

                return root;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "*******")]
            public int Compare(global::System.Data.DataRow row1, global::System.Data.DataRow row2) {
                if (object.ReferenceEquals(row1, row2)) {
                    return 0;
                }
                if ((row1 == null)) {
                    return -1;
                }
                if ((row2 == null)) {
                    return 1;
                }

                int distance1 = 0;
                global::System.Data.DataRow root1 = this.GetRoot(row1, out distance1);

                int distance2 = 0;
                global::System.Data.DataRow root2 = this.GetRoot(row2, out distance2);

                if (object.ReferenceEquals(root1, root2)) {
                    return (this._childFirst * distance1.CompareTo(distance2));
                }
                else {
                    global::System.Diagnostics.Debug.Assert(((root1.Table != null) 
                                    && (root2.Table != null)));
                    if ((root1.Table.Rows.IndexOf(root1) < root2.Table.Rows.IndexOf(root2))) {
                        return -1;
                    }
                    else {
                        return 1;
                    }
                }
            }
        }
    }
}

#pragma warning restore 1591