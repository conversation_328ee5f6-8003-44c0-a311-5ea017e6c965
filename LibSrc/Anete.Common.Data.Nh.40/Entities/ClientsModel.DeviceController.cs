//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Anete.Common.Data.Nh.Entities
{

    /// <summary>
    /// Tabulka: dba.CFSeznamCtecek
    /// </summary>
    public partial class DeviceController : Anete.Data.Nh.NhEntityBase, INotifyPropertyChanging, INotifyPropertyChanged {

        private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(System.String.Empty);

        private short _DeviceControllerId;

        private short _AppInstallationId;

        private DeviceControllerType _DeviceControllerType;

        private string _Alternative;

        private string _Serve;

        private string _Name;

        private string _XmlConfig;

        private string _Class;

        private bool _Active;

        private ExtDispType _ExtDispType;

        private int _DeviceControllerCompositeId;

        private Canteen _Canteen;

        private AppInstallation _AppInstallation;

        private DeviceControllerSet _DeviceControllerSet;

        private CfTypyVydAutomatu _CfTypyVydAutomatu;

        private ISet<VendingMachine> _VendingMachines;

        private ISet<DeviceController_DeviceControllerHw> _Hws;

        private ISet<VydAutomatyMapovani> _VydAutomatyMapovani;
    
        #region Extensibility Method Definitions
        
        partial void OnCreated();

        public override bool Equals(object obj)
        {
          DeviceController toCompare = obj as DeviceController;
          if (toCompare == null)
          {
            return false;
          }

          if (!Object.Equals(this.DeviceControllerId, toCompare.DeviceControllerId))
            return false;
          if (!Object.Equals(this.AppInstallationId, toCompare.AppInstallationId))
            return false;
         // pokud mam default primary key, znamena to, ze jedna o nove vytvorenou entitu, ktera jeste nema key prirazen. 
        // V takove pripade musim porovnat podle instance
        bool isDefault = true;
        if (this.DeviceControllerId != default(short))
        {
          isDefault = false;
        }
     
        if (this.AppInstallationId != default(short))
        {
          isDefault = false;
        }
     

          if (isDefault)
          {
            return object.ReferenceEquals(toCompare, this);
          }     
          return true;
        }

	private int? _hashCode = null;
        public override int GetHashCode()
        {
	if (_hashCode == null)
          {
          _hashCode = 13;
          _hashCode = (_hashCode * 7) + DeviceControllerId.GetHashCode();
          _hashCode = (_hashCode * 7) + AppInstallationId.GetHashCode();
}
          return _hashCode.Value;
        }
        partial void OnDeviceControllerIdChanging(short value);
        
        partial void OnDeviceControllerIdChanged();
        partial void OnAppInstallationIdChanging(short value);
        
        partial void OnAppInstallationIdChanged();
        partial void OnDeviceControllerTypeChanging(DeviceControllerType value);
        
        partial void OnDeviceControllerTypeChanged();
        partial void OnAlternativeChanging(string value);
        
        partial void OnAlternativeChanged();
        partial void OnServeChanging(string value);
        
        partial void OnServeChanged();
        partial void OnNameChanging(string value);
        
        partial void OnNameChanged();
        partial void OnXmlConfigChanging(string value);
        
        partial void OnXmlConfigChanged();
        partial void OnClassChanging(string value);
        
        partial void OnClassChanged();
        partial void OnActiveChanging(bool value);
        
        partial void OnActiveChanged();
        partial void OnExtDispTypeChanging(ExtDispType value);
        
        partial void OnExtDispTypeChanged();
        partial void OnDeviceControllerCompositeIdChanging(int value);
        
        partial void OnDeviceControllerCompositeIdChanged();
        partial void OnCanteenChanging(Canteen value);

        partial void OnCanteenChanged();
        partial void OnAppInstallationChanging(AppInstallation value);

        partial void OnAppInstallationChanged();
        partial void OnDeviceControllerSetChanging(DeviceControllerSet value);

        partial void OnDeviceControllerSetChanged();
        partial void OnCfTypyVydAutomatuChanging(CfTypyVydAutomatu value);

        partial void OnCfTypyVydAutomatuChanged();
        
        #endregion
        public DeviceController()
        {
            this._DeviceControllerId = 0;
            this._AppInstallationId = 0;
            this._Active = false;
            this._VendingMachines = new HashSet<VendingMachine>();
            this._Hws = new HashSet<DeviceController_DeviceControllerHw>();
            this._VydAutomatyMapovani = new HashSet<VydAutomatyMapovani>();
            OnCreated();
        }

    
        /// <summary>
        /// Sloupec: id_ctecka
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("DeviceControllerId", typeof(DeviceControllerSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(DeviceController), Tag="DeviceControllerId")]
        public virtual short DeviceControllerId
        {
            get
            {
                return this._DeviceControllerId;
            }
            set
            {
                if (this._DeviceControllerId != value)
                {
                    this.OnDeviceControllerIdChanging(value);
                    this.SendPropertyChanging();
                    this._DeviceControllerId = value;
                    this.SendPropertyChanged("DeviceControllerId");
                    this.OnDeviceControllerIdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: id_zarizeni
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("AppInstallationId", typeof(DeviceControllerSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(DeviceController), Tag="AppInstallationId")]
        public virtual short AppInstallationId
        {
            get
            {
                return this._AppInstallationId;
            }
            set
            {
                if (this._AppInstallationId != value)
                {
                    this.OnAppInstallationIdChanging(value);
                    this.SendPropertyChanging();
                    this._AppInstallationId = value;
                    this.SendPropertyChanged("AppInstallationId");
                    this.OnAppInstallationIdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: typ
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("DeviceControllerType", typeof(DeviceControllerSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(DeviceController), Tag="DeviceControllerType")]
        public virtual DeviceControllerType DeviceControllerType
        {
            get
            {
                return this._DeviceControllerType;
            }
            set
            {
                if (this._DeviceControllerType != value)
                {
                    this.OnDeviceControllerTypeChanging(value);
                    this.SendPropertyChanging();
                    this._DeviceControllerType = value;
                    this.SendPropertyChanged("DeviceControllerType");
                    this.OnDeviceControllerTypeChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: alt
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Alternative", typeof(DeviceControllerSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(DeviceController), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 500,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="Alternative")]
        public virtual string Alternative
        {
            get
            {
                return this._Alternative;
            }
            set
            {
                if (this._Alternative != value)
                {
                    this.OnAlternativeChanging(value);
                    this.SendPropertyChanging();
                    this._Alternative = value;
                    this.SendPropertyChanged("Alternative");
                    this.OnAlternativeChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: vydava
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Serve", typeof(DeviceControllerSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(DeviceController), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 500,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="Serve")]
        public virtual string Serve
        {
            get
            {
                return this._Serve;
            }
            set
            {
                if (this._Serve != value)
                {
                    this.OnServeChanging(value);
                    this.SendPropertyChanging();
                    this._Serve = value;
                    this.SendPropertyChanged("Serve");
                    this.OnServeChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: popis
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Name", typeof(DeviceControllerSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(DeviceController), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 20,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="Name")]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(DeviceController), Tag="Name")]
        public virtual string Name
        {
            get
            {
                return this._Name;
            }
            set
            {
                if (this._Name != value)
                {
                    this.OnNameChanging(value);
                    this.SendPropertyChanging();
                    this._Name = value;
                    this.SendPropertyChanged("Name");
                    this.OnNameChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: XmlConfig
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("XmlConfig", typeof(DeviceControllerSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(DeviceController), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 4000,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="XmlConfig")]
        public virtual string XmlConfig
        {
            get
            {
                return this._XmlConfig;
            }
            set
            {
                if (this._XmlConfig != value)
                {
                    this.OnXmlConfigChanging(value);
                    this.SendPropertyChanging();
                    this._XmlConfig = value;
                    this.SendPropertyChanged("XmlConfig");
                    this.OnXmlConfigChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: Class
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Class", typeof(DeviceControllerSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(DeviceController), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 500,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="Class")]
        public virtual string Class
        {
            get
            {
                return this._Class;
            }
            set
            {
                if (this._Class != value)
                {
                    this.OnClassChanging(value);
                    this.SendPropertyChanging();
                    this._Class = value;
                    this.SendPropertyChanged("Class");
                    this.OnClassChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: Aktivni
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Active", typeof(DeviceControllerSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(DeviceController), Tag="Active")]
        public virtual bool Active
        {
            get
            {
                return this._Active;
            }
            set
            {
                if (this._Active != value)
                {
                    this.OnActiveChanging(value);
                    this.SendPropertyChanging();
                    this._Active = value;
                    this.SendPropertyChanged("Active");
                    this.OnActiveChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: externi_display
        /// Typ externiho displeje.
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("ExtDispType", typeof(DeviceControllerSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(DeviceController), Tag="ExtDispType")]
        public virtual ExtDispType ExtDispType
        {
            get
            {
                return this._ExtDispType;
            }
            set
            {
                if (this._ExtDispType != value)
                {
                    this.OnExtDispTypeChanging(value);
                    this.SendPropertyChanging();
                    this._ExtDispType = value;
                    this.SendPropertyChanged("ExtDispType");
                    this.OnExtDispTypeChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: citac
        /// Identifikace Čtečky
        /// </summary>
        /// <remark>
        /// Přidano pro identifikaci čtečky z objednavek. V objednavkach je sloupec citac složen z id_zarizeni * 100 + id_ctecky. Pro použití je potřeba použít Projekci, protože sloupec citac v objednávkach může nabývat Null hodnot a dotaz může spadnout.
        /// </remark>
        [Anete.Data.Attributes.EntitySRDisplayName("DeviceControllerCompositeId", typeof(DeviceControllerSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(DeviceController), Tag="DeviceControllerCompositeId")]
        public virtual int DeviceControllerCompositeId
        {
            get
            {
                return this._DeviceControllerCompositeId;
            }
            set
            {
                if (this._DeviceControllerCompositeId != value)
                {
                    this.OnDeviceControllerCompositeIdChanging(value);
                    this.SendPropertyChanging();
                    this._DeviceControllerCompositeId = value;
                    this.SendPropertyChanged("DeviceControllerCompositeId");
                    this.OnDeviceControllerCompositeIdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: id_vydejna
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Canteen", typeof(DeviceControllerSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual Canteen Canteen
        {
            get
            {
                return this._Canteen;
            }
            set
            {
                if (this._Canteen != value)
                {
                    this.OnCanteenChanging(value);
                    this.SendPropertyChanging();
                    this._Canteen = value;
                    this.SendPropertyChanged("Canteen");
                    this.OnCanteenChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: id_zarizeni
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("AppInstallation", typeof(DeviceControllerSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual AppInstallation AppInstallation
        {
            get
            {
                return this._AppInstallation;
            }
            set
            {
                if (this._AppInstallation != value)
                {
                    this.OnAppInstallationChanging(value);
                    this.SendPropertyChanging();
                    this._AppInstallation = value;
                    this.SendPropertyChanged("AppInstallation");
                    this.OnAppInstallationChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: sada
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("DeviceControllerSet", typeof(DeviceControllerSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual DeviceControllerSet DeviceControllerSet
        {
            get
            {
                return this._DeviceControllerSet;
            }
            set
            {
                if (this._DeviceControllerSet != value)
                {
                    this.OnDeviceControllerSetChanging(value);
                    this.SendPropertyChanging();
                    this._DeviceControllerSet = value;
                    this.SendPropertyChanged("DeviceControllerSet");
                    this.OnDeviceControllerSetChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: id_typvydautomatu
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("CfTypyVydAutomatu", typeof(DeviceControllerSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual CfTypyVydAutomatu CfTypyVydAutomatu
        {
            get
            {
                return this._CfTypyVydAutomatu;
            }
            set
            {
                if (this._CfTypyVydAutomatu != value)
                {
                    this.OnCfTypyVydAutomatuChanging(value);
                    this.SendPropertyChanging();
                    this._CfTypyVydAutomatu = value;
                    this.SendPropertyChanged("CfTypyVydAutomatu");
                    this.OnCfTypyVydAutomatuChanged();
                }
            }
        }

        [Anete.Data.Attributes.EntitySRDisplayName("VendingMachines", typeof(DeviceControllerSR))]
        [Anete.Data.Attributes.DbCollectionProperty]
        public virtual ISet<VendingMachine> VendingMachines
        {
            get
            {
                return this._VendingMachines;
            }
            set
            {
                this._VendingMachines = value;
            }
        }

        [Anete.Data.Attributes.EntitySRDisplayName("Hws", typeof(DeviceControllerSR))]
        [Anete.Data.Attributes.DbCollectionProperty]
        public virtual ISet<DeviceController_DeviceControllerHw> Hws
        {
            get
            {
                return this._Hws;
            }
            set
            {
                this._Hws = value;
            }
        }

        [Anete.Data.Attributes.EntitySRDisplayName("VydAutomatyMapovani", typeof(DeviceControllerSR))]
        [Anete.Data.Attributes.DbCollectionProperty]
        public virtual ISet<VydAutomatyMapovani> VydAutomatyMapovani
        {
            get
            {
                return this._VydAutomatyMapovani;
            }
            set
            {
                this._VydAutomatyMapovani = value;
            }
        }
   
        public virtual event PropertyChangingEventHandler PropertyChanging;

	// Mam v bazove tride, nema smysl generovat
	// public virtual event PropertyChangedEventHandler PropertyChanged;

        protected virtual void SendPropertyChanging()
        {
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, emptyChangingEventArgs);
        }

        protected virtual void SendPropertyChanging(System.String propertyName) 
        {   	
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, new PropertyChangingEventArgs(propertyName));
        }

        protected virtual void SendPropertyChanged(System.String propertyName)
        {    
		// mam v bazove tride, proto musim volat takto jednoduse
		OnPropertyChanged(propertyName);
/*		        var handler = this.PropertyChanged;
            if (handler != null)
                handler(this, new PropertyChangedEventArgs(propertyName));*/
        }
    }

}
