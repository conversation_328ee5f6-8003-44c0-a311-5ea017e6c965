using System;
using Anete.Common.Data.Interface.AppServices;

namespace Anete.Common.Data.AppServices
{
	/// <summary>
	/// IKreditMonthBalance ktere je mozne primo rict, kdy uplynulo datum uzaverky.
	/// </summary>
	public class CustomKreditMonthBalance : IKreditMonthBalance
	{
		private readonly DateTime _firstAvailableDate;

		/// <summary>
		/// Initializes a new instance of the CustomKreditMonthBalance class.
		/// </summary>
		/// <param name="firstAvailableDate">Prvni den, pro ktery jeste nebyla vytvorena mesicni uzaverka.</param>
		public CustomKreditMonthBalance(DateTime firstAvailableDate)
		{
			_firstAvailableDate = firstAvailableDate.Date;
		}

		#region IKreditMonthBalance Members
		/// <summary>
		/// Je den dostupny? Tzn. nebyl pro nej jeste vytvorena uzaverka.
		/// </summary>
		/// <param name="date"></param>
		/// <returns></returns>
		public bool IsDateAvailable(DateTime date)
		{
			return date.Date >= _firstAvailableDate;
		}

        /// <summary>
        /// Prenacte data. Metoda IsDateAvailable se cachuje. Zavolanim Reload se cache vyprazdni.
        /// </summary>
        public void Reload()
        {
            // nemusim delat nic
        }
		#endregion        
    }
}
