using System;
using System.Collections.Generic;
using Anete.Common.Data.AccessRights.Enums;

namespace Anete.Common.Data.AccessRights
{
    /// <summary>
    /// Loader, ktery se nedotazuje na efektivni prava uzivatele ale vzdy se tvari, jako k<PERSON>by mel uzivatel vsechna prava.
    /// Pouziti: Pouze pro testovaci ucely
    /// Seznam prav je dan typem T
    /// </summary>
    /// <typeparam name="T">Typ objektu, ktery definuje mnozinu pritomnych prav</typeparam>
    internal class AllRightsAccessRightSetLoader<T> : IAccessRightSetLoader
        where T : AccessRightBase
    {
        #region IAccessRightSetLoader members
        /// <summary>
        /// Do rightSet nacte efektivni prava uzivatele k danemu objektu
        /// </summary>
        public void Load(HashSet<string> rightSet)
        {
            rightSet.Clear();
            foreach (string rightCode in AccessRightBase.GetValues<T>())
            {
                rightSet.Add(rightCode);
            }      
        }
        #endregion
    }

	internal class PredefinedAccessRightSetLoader : IAccessRightSetLoader
	{
		private readonly IEnumerable<string> _rights;
		/// <summary>
		/// Initializes a new instance of the PredefinedAccessRightSetLoader class.
		/// </summary>
		public PredefinedAccessRightSetLoader(IEnumerable<string> rights)
		{
			_rights = rights;			
		}

		#region IAccessRightSetLoader Members
		public void Load(HashSet<string> rightSet)
		{
			rightSet.Clear();
			foreach (string right in _rights)
			{
				rightSet.Add(right);
			}
		}
		#endregion
	}
}
