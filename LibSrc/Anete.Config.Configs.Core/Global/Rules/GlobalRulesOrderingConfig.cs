using System;
using System.Collections.Generic;
using System.Text;
using Anete.Config.Core.Categories;
using Anete.Config.Core.Attributes;
using System.ComponentModel;
using Anete.Config.Core.CFSystKonfig;
using Anete.Utils;
using Anete.Config.Core.Internals;

namespace Anete.Config.Configs.Core.Global.Rules
{
    /// <summary>
    /// Parametry pro trideni
    /// </summary>
    [GlobalRulesConfigDBKey("Ordering")]
    [ConfigVersion("*******")]
    [ConfigClassSRDescription(typeof(GlobalRulesOrderingConfig))]
    [DefaultConfigInstance(false)]
	[DisableInheritance]
	// KAKTODO9: 8.10.2008 - spada to do nejake kategorie?
	public class GlobalRulesOrderingConfig : ConfigBase
    {
        private const bool _acceptHolidaysDefValue = false;
        private bool _acceptHolidays = _acceptHolidaysDefValue;
        /// <summary>
        /// Nastavuje aplikaci svátků pro objednací pravidla a objednací schema.
        /// </summary>
        [DefaultValue(_acceptHolidaysDefValue)]
        [ConfigSRDisplayName("AcceptHolidays", typeof(GlobalRulesOrderingConfig))]
        [ConfigSRDescription("AcceptHolidays", typeof(GlobalRulesOrderingConfig))]
        [MapOldBoolPropertyToCFSystKonfig("pravidla_svatky", MapToCFSystKonfigOption.ReadFromCFSystKonfig)]
        public bool AcceptHolidays
        {
            get
            {
                return _acceptHolidays;
            }
            set
            {
                if (_acceptHolidays == value)
                {
                    return;
                }
                _acceptHolidays = value;
                OnPropertyChanged("AcceptHolidays");
            }
        }

        private const bool _allowExchangeMessSizeDefValue = false;
        private bool _allowExchangeMessSize = _allowExchangeMessSizeDefValue;
        /// <summary>
        /// Povoluje ignorování velikostí porcí v burze
        /// </summary>
        [DefaultValue(_allowExchangeMessSizeDefValue)]
        [ConfigSRDisplayName("AllowExchangeMessSize", typeof(GlobalRulesOrderingConfig))]
        [ConfigSRDescription("AllowExchangeMessSize", typeof(GlobalRulesOrderingConfig))]
        [MapOldBoolNegatePropertyToCFSystKonfig("burza_bez_sl", MapToCFSystKonfigOption.ReadFromCFSystKonfig)]
        public bool AllowExchangeMessSize
        {
            get
            {
                return _allowExchangeMessSize;
            }
            set
            {
                if (_allowExchangeMessSize == value)
                {
                    return;
                }
                _allowExchangeMessSize = value;
                OnPropertyChanged("AllowExchangeMessSize");
            }
        }

        private const bool _enableMealTicketExchangeDefValue = true;
        private bool _enableMealTicketExchange = _enableMealTicketExchangeDefValue;
        /// <summary>
        /// Zapíná burzu stravenek. Při vypnutém parametru není možné do burzy vložit žádné jídlo.
        /// </summary>
        [DefaultValue(_enableMealTicketExchangeDefValue)]
        [ConfigSRDisplayName("EnableMealTicketExchange", typeof(GlobalRulesOrderingConfig))]
        [ConfigSRDescription("EnableMealTicketExchange", typeof(GlobalRulesOrderingConfig))]
        [MapOldBoolPropertyToCFSystKonfig("povol_BurzaStravenek", MapToCFSystKonfigOption.ReadFromCFSystKonfig)]
        public bool EnableMealTicketExchange
        {
            get
            {
                return _enableMealTicketExchange;
            }
            set
            {
                if (_enableMealTicketExchange == value)
                {
                    return;
                }
                _enableMealTicketExchange = value;
                OnPropertyChanged("EnableMealTicketExchange");
            }
        }

        private const bool _useMessSizesDefValue = false;
        private bool _useMessSizes = _useMessSizesDefValue;
        /// <summary>
        /// Zapíná mechanismus pro rozlišování velikosti porcí a podporu čtyřmístných výdejních displejů.
        /// </summary>
        [DefaultValue(_useMessSizesDefValue)]
        [ConfigSRDisplayName("UseMessSizes", typeof(GlobalRulesOrderingConfig))]
        [ConfigSRDescription("UseMessSizes", typeof(GlobalRulesOrderingConfig))]
        [MapOldBoolPropertyToCFSystKonfig("povol_strav_limit", MapToCFSystKonfigOption.ReadFromCFSystKonfig)]
        [ServePointCategoryAttribute]
        public bool UseMessSizes
        {
            get
            {
                return _useMessSizes;
            }
            set
            {
                if (_useMessSizes == value)
                {
                    return;
                }
                _useMessSizes = value;
                OnPropertyChanged("UseMessSizes");
            }
        }

        private const VouchersSystem _vouchersDefValue = VouchersSystem.WithoutVouchers;
        private VouchersSystem _vouchers = _vouchersDefValue;
        /// <summary>
        /// Umožňuje omezit měsíční počet dotovaných porcí (zapíná tzv. bonový systém). 
        /// Je-li nastaveno VoucherPerMess (–1), systém po vyčerpání bonů nedovolí strávníkovi další objednávku.
        /// Je tedy možné v rámci jednoho měsíce provést pouze tolik objednávek, kolik má strávník bonů, přičemž všechny objednávky jsou dotovány.
        /// Je-li nastaveno VoucherPerSubsidy (1), systém po vyčerpání přiděleného počtu bonů objednává strávníkovi
        /// další porce za plnou cenu jídla až do konce měsíce.
        /// Při nastavení WithoutVouchers (0) je bonový systém vypnut.
        /// </summary>
        [DefaultValue(typeof(VouchersSystem), "WithoutVouchers")]
        [ConfigSRDisplayName("Vouchers", typeof(GlobalRulesOrderingConfig))]
        [ConfigSRDescriptionWithEnumDescription("Vouchers", typeof(VouchersSystem), typeof(GlobalRulesOrderingConfig))]
        [MapEnumToIntPropertyToCFSystKonfigAttribute("povol_maxdp", MapToCFSystKonfigOption.ReadFromCFSystKonfig)]
        public VouchersSystem Vouchers
        {
            get
            {
                return _vouchers;
            }
            set
            {
                if (_vouchers == value)
                {
                    return;
                }
                Guard.EnumValueIsDefined<VouchersSystem>(value, "Vouchers");

                _vouchers = value;
                OnPropertyChanged("Vouchers");
            }
        }

        private const VouchersSource _voucherSourceDefValue = VouchersSource.ConstantByGroup;
        private VouchersSource _voucherSource = _voucherSourceDefValue;
        /// <summary>
        /// Určuje způsob plnění bonů. Při nastavení parametru na ByCalendar (-1) počet bonů určuje "kalendář"
        /// podle skupiny  strávníka a měsíce v roce (kalendář se obvykle předvyplní na celý rok dopředu podle pracovních dnů),
        /// při nastavení na ConstantByGroup (0) je počet bonů pevně stanoven skupinou strávníka (nezávisle na měsíci).
        /// </summary>
        [DefaultValue(typeof(VouchersSource), "ConstantByGroup")]
        [ConfigSRDisplayName("VoucherSource", typeof(GlobalRulesOrderingConfig))]
        [ConfigSRDescriptionWithEnumDescription("VoucherSource", typeof(VouchersSource), typeof(GlobalRulesOrderingConfig))]
        [MapEnumToIntPropertyToCFSystKonfigAttribute("povol_kalendarbonu", MapToCFSystKonfigOption.ReadFromCFSystKonfig)]
        public VouchersSource VoucherSource
        {
            get
            {
                return _voucherSource;
            }
            set
            {
                if (_voucherSource == value)
                {
                    return;
                }
                Guard.EnumValueIsDefined<VouchersSource>(value, "VoucherSource");

                _voucherSource = value;
                OnPropertyChanged("VoucherSource");
            }
        }
    }

}
