using System;
using Anete.Utils;
using Anete.Config.Configs.Core.Global.Behaviour;
using System.Runtime.Serialization;
using Anete.Resources;
using Anete.Common.Core.Interface.Enums;
using System.Threading;

namespace Anete.Common.Data.Business.MealServing
{
	/// <summary>
	/// Bazova trida reprezentujici klienta.
	/// </summary>
	[DataContract(Namespace = AneteNamespace.DefaultNamespace)]
	public class Client : IEquatable<Client>

	{

		#region constructors...
		/// <summary>
		/// Initializes a new instance of the class.
		/// </summary>
		/// <param name="title">Titul.</param>
		/// <param name="name">Jmeno.</param>
		/// <param name="surname">Prijmeni.</param>
		/// <param name="idLk">ID karty (databazova identifikace).</param>
		/// <param name="personalCode">Oso<PERSON>ni cislo.</param>
		/// <param name="birthCode">Rodne cislo.</param>
		/// <param name="disponsibleCreditBalance">Disponi<PERSON><PERSON> zu<PERSON>.</param>
		/// <param name="clientGroup">Skupina stravnika.</param>
		/// <param name="organization">Organizace.</param>
		/// <param name="resort">Stredisko.</param>
		/// <param name="mealSize">Size of the meal.</param>
		/// <param name="voucherCount">The voucher count.</param>
		/// <param name="maxObratSortiment">The max obrat sortiment.</param>
		/// <param name="stavUctu">The stav uctu.</param>
		/// <param name="priceCategory">Cenova kategoria</param>
		/// <param name="clientIdentification">Zpusob identifikace klienta</param>
		/// <param name="language">V akom jazyku sa ma klientovi zobrazovat aplikacia. Pouziva QT vydejni ctecka</param>
		/// <param name="cardCode">Kod aktivni karty</param>
		public Client(string title, string name, string surname,
						  int idLk, string personalCode, string birthCode,
						  decimal disponsibleCreditBalance, string clientGroup,
						  string organization, string resort, MealSize mealSize, int voucherCount,
						  decimal maxObratSortiment, decimal stavUctu, short priceCategory, ClientDisplayedIdentType clientIdentification, string cardCode, string cardId)
		{
            CardId = cardId;
            CardCode = cardCode;
			Title = title;
			Name = name;
			Surname = surname;
			ClientRegisterId = idLk;
			PersonalCode = personalCode;
			BirthCode = birthCode;
			DisponsibleCreditBalance = disponsibleCreditBalance;
			ClientGroup = clientGroup;
			Organization = organization;
			Resort = resort;
			MealSize = mealSize;
			VoucherCount = voucherCount;
			MaxObratSortiment = maxObratSortiment;
			AccountBalance = stavUctu;
			PriceCategory = priceCategory;			

			// kontruktor neni ve WCF volan, muzu proto ponechat inicializaci zde
			Identification = GetIdentification(clientIdentification);
			
		}
		#endregion	
		
		public void InitLanguage(ApplicationLanguage menuLanguage, ApplicationLanguage uiLanguage)
		{					
			MenuLanguage = menuLanguage;
            UiLanguage = uiLanguage;
		}

		private string GetIdentification(ClientDisplayedIdentType clientIdentification)
		{			
			switch (clientIdentification)
			{
				case ClientDisplayedIdentType.None:
					return "";										
					
				case ClientDisplayedIdentType.KOD:
					return CardCode;					

				case ClientDisplayedIdentType.OCS:
					return PersonalCode;

				case ClientDisplayedIdentType.RCS:
					return BirthCode;

				default:
					throw ExcUtils.EnumValueIsNotDefined<ClientDisplayedIdentType>(clientIdentification, "Identification");
			}
		}
		#region public properties...
		/// <summary>
		/// Titul.
		/// </summary>
		[DataMember()]
		public string Title { get; protected set; }

		/// <summary>
		/// Jmeno.
		/// </summary>
		[DataMember()]
		public string Name { get; protected set; }

		/// <summary>
		/// Prijmeni.
		/// </summary>
		[DataMember()]
		public string Surname { get; protected set; }

		/// <summary>
		/// Vraci plne jmeno tzn. jmeno a prijmeni
		/// </summary>
		public string FullName => $"{Name} {Surname}";

		/// <summary>
		/// Vraci plne jmeno v opacnom poradi tzn. prijmeni a jmeno
		/// </summary>
		public string FullNameReverse => $"{Surname} {Name}";

		/// <summary>
		/// Vraci plne jmeno s titulom tzn. titul, jmeno a prijmeni
		/// </summary>
		public string FullNameWithTitle => Title == null ? FullName : $"{Title} {Name} {Surname}";

		/// <summary>
		/// Viditelny kod karty.
		/// </summary>
		//[DataMember()]
		// s uzivatelom sa nema posielat cislo karty
		//public string CardCode { get; protected set; }

		/// <summary>
		/// Osobni cislo.
		/// </summary>
		[DataMember()]
		public string PersonalCode { get; protected set; }

		/// <summary>
		/// Rodne cislo.
		/// </summary>
		[DataMember()]
		public string BirthCode { get; protected set; }

		/// <summary>
		/// Identifikace klienta. Muze se jedna o rodne cislo, osobni cislo pripadne o vnitrni cislo karty podle konfigurace.
		/// </summary>
		[DataMember()]
		public string Identification { get; private set; }

		/// <summary>
		/// Databazova identifikace klienta.
		/// </summary>
		[DataMember()]
		public int ClientRegisterId { get; protected set; }

		/// <summary>
		/// Disponibilni zustatek na uctu klienta.
		/// </summary>
		[DataMember()]
		public decimal DisponsibleCreditBalance { get; protected set; }

		/// <summary>
		/// Skupina klientu.
		/// </summary>
		[DataMember()]
		public string ClientGroup { get; protected set; }

		/// <summary>
		/// Organizace.
		/// </summary>
		[DataMember()]
		public string Organization { get; protected set; }

		/// <summary>
		/// Stredisko.
		/// </summary>
		[DataMember()]
		public string Resort { get; protected set; }

		/// <summary>
		/// Velikost porce
		/// </summary>
		[DataMember()]
		public MealSize MealSize { get; private set; }

		/// <summary>
		/// Stav bonu.
		/// </summary>
		[DataMember()]
		public int VoucherCount { get; private set; }

		/// <summary>
		/// Udava maximalni obrat pro prodej sortimentu.
		/// <remarks>Klient muze utratit maximalni castku podle toho, ktera z castek je mensi,
		/// jestli MaxObratSortiment nebo DisponsibleCreditBalance.</remarks>
		/// </summary>
		[DataMember()]
		public decimal MaxObratSortiment { get; set; }

        /// <summary>
        /// Maximalni mesicni obrat tak jak jej vraci PM8_Prihlaseni
        /// </summary>
        public decimal? MaxObrat { get; set; }

		/// <summary>
		/// Udava celkovy stav uctu.
		/// </summary>
		[DataMember()]
		public decimal AccountBalance { get; set; }

		/// <summary>
		/// Vraci maximum, za ktere muze klient nakoupit. Vraci hodnotu nizsi castky mezi MaxObratSortiment a DisponsibleCreditBalance.
		/// Pokud je nektera z techto castek nastavena na nulu, tak se bere jako nenastavena. Pokud jsou obe nastaveny na nulu, tak je vracen StavUctu.
		/// </summary>
		public decimal MaximumPovolenyProdejSortimentu
		{
			get
			{
				if (MaxObratSortiment == 0 && DisponsibleCreditBalance == 0)
				{
					return AccountBalance;
				}
				if (MaxObratSortiment == 0)
				{
					return DisponsibleCreditBalance;
				}
				if (DisponsibleCreditBalance == 0)
				{
					return MaxObratSortiment;
				}
				if (MaxObratSortiment > DisponsibleCreditBalance)
				{
					return DisponsibleCreditBalance;
				}
				else
				{
					return MaxObratSortiment;
				}
			}
		}

		/// <summary>
		/// Cenova kategorie
		/// </summary>
		[DataMember()]
		public short PriceCategory { get; private set; }
        
		/// <summary>
		/// Kod aktivni karty klienta
		/// </summary>
		[DataMember]
		public string CardCode { get; private set; }

        private ApplicationLanguage? _menuLanguage;
        /// <summary>
        /// Id jazyku jidelnicku, tak jak jej definuje system Kredit
        /// </summary>
        [DataMember]
        public ApplicationLanguage MenuLanguage
        {
            get
            {
                return _menuLanguage ?? ApplicationLanguageExt.GetFromCulture(Thread.CurrentThread.CurrentUICulture);
            }
            private set
            {
                _menuLanguage = value;
            }
        }

        private ApplicationLanguage? _uiLanguage;
        /// <summary>
        /// Jazyk uzivatelskeho rozhrani
        /// </summary>
        [DataMember]
        public ApplicationLanguage UiLanguage
        {
            get
            {
                return _uiLanguage ?? ApplicationLanguageExt.GetFromCulture(Thread.CurrentThread.CurrentUICulture);
            }
            private set
            {
                _uiLanguage = value;
            }
        }

        /// <summary>
        /// Id karty. Obvykle neni treba nastavovat. Nesmi se nikde zobrazovat, slouzi jen k prihlaseni klienta
        /// </summary>
        [DataMember]
        public string CardId { get; private set; }

        #endregion

        #region public overrides...	
        public override int GetHashCode()
		{
			return ClientRegisterId;
		}
	  
		public override bool Equals(object obj)
		{
			if (obj is Client)
			{
				return Equals((Client)obj);
			}

			return false;
		}
	   
		public override string ToString()
		{
			return $"IdLk={ClientRegisterId}, FullName={FullName}";
		}
		#endregion

		#region IEquatable<ClientBase> Members      
		public bool Equals(Client other)
		{
			if (other == null)
			{
				return false;
			}
			return other.ClientRegisterId == ClientRegisterId;
		}
		#endregion

	}
}
