using System;
using System.Collections.Generic;
using System.Linq;
using NHibernate;
using System.Data.SqlClient;
using Anete.Data;
using System.Data;
using Anete.Utils.Extensions;
using Anete.Common.Data.Interface.AppServices;
using Anete.Log4Net.Core;
using Anete.Utils.Text;
using Anete.Resources;
using Anete.Utils;
using Anete.Common.Data.Nh.Interface.Services;
using Anete.Common.Data.Nh.Entities;
using Anete.Data.Nh;

namespace Anete.Common.Data.Nh.Services
{
	/// <summary>
	/// Sluzby pro praci s daty klienta
	/// </summary>
	/// <remarks>
	/// Diskuse s ToJ: Ma do volani servisu vstupvat ISession nebo mame pouzivat connection vytvorene pomoci IDbConnectionProvider?
	/// Asi pripad od pripadu. Pokud se metoda vola otamtud, kde uz session existuje, pouzit stavajici session. Pokud mimo,
	/// pak vytvorit Conn sam. Je to nejednotne, uvidime casem.
	/// </remarks>
	public class ClientsService : IClientsService
	{

		#region private static fields...
		private static readonly ILogEx _log = LogManagerEx.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
		#endregion

		#region private fields...
		private readonly IKreditDbConnectionProvider _connProvider;
		#endregion

		#region constructors...
		public ClientsService(IKreditDbConnectionProvider connProvider)
		{
			_connProvider = connProvider;
		}
		#endregion

		#region IClientsService members...
		/// <summary>
		/// Nacte IdLk z kodu karty. Pouziva se pri hledani stravnika dle karty
		/// </summary>
		/// <param name="session">The session.</param>
		/// <param name="cardCode">Kod karty</param>
		/// <param name="clientId">IdLk</param>
		/// <param name="errorMessage">Chybove hlaseni, pokud se neco nepovedlo</param>
		/// <returns>True, pokud bylo uspesne</returns>
		public bool GetClientIdFromCardCode(ISession session, string cardCode, out int clientId, out string errorMessage)
		{
			return GetClientCode(session, cardCode, InputCodeType.CardCode, out clientId, out errorMessage);
		}

		/// <summary>
		/// Nacte IdLk z cisla karty (cisla cipu). Pouziva se pri hledani stravnika dle karty
		/// </summary>
		/// <param name="session">The session.</param>
		/// <param name="cardId">Cislo cipu</param>
		/// <param name="clientId">IdLk</param>
		/// <param name="errorMessage">Chybove hlaseni, pokud se neco nepovedlo</param>
		/// <returns>True, pokud bylo uspesne</returns>
		public bool GetClientIdFromCardId(ISession session, string cardId, out int clientId, out string errorMessage)
		{
			return GetClientCode(session, cardId, InputCodeType.CardId, out clientId, out errorMessage);
		}

		/// <summary>
		/// Vraci pocet aktivnich klientu.
		/// Do poctu se nezahrnuji klienti, kteri maji priznak IsFakeClient v tab. dba.KartyL_ExtInfo. Jedna se o souhrnne klienty.
		/// </summary>
		/// <returns></returns>
		public int GetActiveClientsCount()
		{
			_log.DebugFormat("GetActiveClientsCount()");
			using (SqlConnection conn = _connProvider.CreateConnection())
			{
				conn.Open();

				using (SqlCommand command = conn.CreateCommand())
				{
					command.CommandType = CommandType.Text;
					command.CommandText =
string.Format(@"select count(distinct a.id_lk)
from dba.KartyF a
join dba.KartyL b on (a.id_lk=b.id_lk and (b.platido is null or b.platido>getdate()))
left outer join dba.KartyL_ExtInfo e on b.id_lk = e.id_lk and e.id_typ = {0}
where stav in (1,2)
and isnull(e.data.value('(/*:PatientOrderingExtInfo/*:IsFakeClient/node())[1]','bit'),0) = 0", (short)ClientRegisterExtInfoType.PatientOrdering);
					int result = (int)command.ExecuteScalar();
					_log.DebugFormat("result={0}", result);
					return result;
				}
			}
		}

		/// <summary>
		/// Vraci pocet aktivnich Www klientu - tech, kteri maji vyplnene Www heslo
		/// </summary>
		/// <returns></returns>
		public int GetActiveWebClientsCount()
		{
			_log.DebugFormat("GetActiveWebClientsCount()");
			using (SqlConnection conn = _connProvider.CreateConnection())
			{
				conn.Open();

				using (SqlCommand command = conn.CreateCommand())
				{
					command.CommandType = CommandType.Text;
					command.CommandText =
@"select count(distinct b.id_lk) 
	from dba.stravnici a 
join dba.KartyL b 
	on (a.id_s=b.id_s) 
join dba.KartyF c 
	on (b.id_lk=c.id_lk and c.stav in (1,2)) 
where 
	a.passwordhash is not null";

					int result = (int)command.ExecuteScalar();
					_log.DebugFormat("result={0}", result);
					return result;
				}
			}
		}

		/// <summary>
		/// Vraci cerpani limitu stravnika pro zbozi a jidla
		/// </summary>
		/// <param name="clientId">Id klienta</param>
		/// <param name="goodsLimitBalance">Cerpani limitu pro zbozi. Null = zadny limit neni nastaven</param>
		/// <param name="mealLimitBalance">Cerpani limitu pro jidla. Null = zadny limit neni nastaven</param>
		public void GetMealAndGoodsLimitBalance(int clientId, out decimal? goodsLimitBalance, out decimal? mealLimitBalance)
		{
			_log.DebugFormat("GetMealAndGoodsLimitBalance(clientId={0})", clientId);
			goodsLimitBalance = null;
			mealLimitBalance = null;

			using (SqlConnection conn = _connProvider.CreateConnection())
			{
				conn.Open();

				using (SqlCommand command = conn.CreateCommand())
				{
					command.CommandType = CommandType.Text;
					command.CommandText =
@"declare @mmaxjid numeric(9,2), @mmaxsort numeric(9,2)
select 
	@mmaxjid = mmaxjid, @mmaxsort = mmaxsort 
from 
	dba.Ucty 
where 
	id_u=@id_lk

if @mmaxjid is not null
    set @mmaxjid = @mmaxjid - (select isnull(sum(cena),0) from dba.objednavky where id_lk=@id_lk and datum between dateadd(d,1-day(getdate()), dba.date(getdate())) and dateadd(d,-day(getdate()), dateadd(m,1,dba.date(getdate()))))
if @mmaxsort is not null
    set @mmaxsort = @mmaxsort + (SELECT coalesce(sum(pohyb), 0) FROM dba.UctyP WHERE id_dpu=11 and id_u = @id_lk and tstamp>= dateadd(d,1-day(getdate()), dba.date(getdate())) and tstamp<dateadd(d,1-day(getdate()), dateadd(m,1,dba.date(getdate()))))

select @mmaxsort as LimitSortiment, @mmaxjid as LimitJidla";
					command.Parameters.Add("@id_lk", SqlDbType.Int).Value = clientId;

					using (SqlDataReader reader = command.ExecuteReader())
					{
						int readCount = 0;

						while (reader.Read())
						{
							goodsLimitBalance = reader.CastDBValue<decimal?>("LimitSortiment");
							mealLimitBalance = reader.CastDBValue<decimal?>("LimitJidla");
							readCount++;
						}

						Guard.ArgumentEquals(readCount, 1, "readCount");
						_log.DebugFormat("result: goodsLimitBalance={0}, mealLimitBalance={1}", goodsLimitBalance, mealLimitBalance);
					}
				}
			}
		}

		/// <summary>
		/// Vraci zustatek bonu k danemu dni.
		/// </summary>
		/// <returns></returns>
		public decimal? GetVoucherBalance(int clientRegisterId, int month, int year)
		{
			_log.DebugFormat("GetVoucherBalance(clientRegisterId={0}, month={1})", clientRegisterId, month);
			using (SqlConnection conn = _connProvider.CreateConnection())
			{
				conn.Open();
				using (SqlCommand command = conn.CreateCommand())
				{
					// pozor, normalni volani funkce jako SP vraci null. Sql funkci je treba volat v tomto formatu.
					command.CommandType = System.Data.CommandType.Text;
					command.CommandText = "select dba.DejZustatekBonu(@id_lk, @datum)";
					command.Parameters.Add("id_lk", System.Data.SqlDbType.Int).Value = clientRegisterId;
					// Libovolny den v danem mesici dava stejny vysledek. Proto volim prvni den, protoze mam zaruceno, ze existuje
					DateTime requestedDate = new DateTime(year, month, 1);
					command.Parameters.Add("datum", System.Data.SqlDbType.DateTime).Value = requestedDate;
					object result = command.ExecuteScalar();
					_log.DebugFormat("result={0}", result);
					if (result == null)
					{
						return null;
					}
					else
					{
						// pozor, normalni pretypovani na decimal zde havaruje
						return DbUtils.DbToDec(result);
					}
				}
			}
		}
		#endregion

		#region private methods...
		private bool GetClientCode(ISession session, string inputCode, InputCodeType inputCodeType, out int idLk,
			out string errorMessage)
		{
			_log.DebugFormat("GetClientCode(inputCode={0}, inputCodeType={1})", inputCode, inputCodeType);
			errorMessage = null;

			using (SqlCommand command = session.Connection.CreateSqlCommand())
			{
				session.EnlistCommand(command);

				command.CommandType = CommandType.StoredProcedure;
				command.CommandText = "[dba].[KAN8_NajdiStravnika]";
				command.Parameters.Add("@identifikator", SqlDbType.VarChar, 32).Value = inputCode;
				command.Parameters.Add("@typ_identif", SqlDbType.SmallInt).Value = (short)inputCodeType;

				SqlParameter idLkParam = command.Parameters.Add("@id_lk", SqlDbType.Int);
				idLkParam.Direction = ParameterDirection.Output;

				SqlParameter errorParam = command.Parameters.Add("@Err", SqlDbType.VarChar, 250);
				errorParam.Direction = ParameterDirection.Output;

				command.ExecuteNonQuery();

				if (!DbUtils.IsDbNull(errorParam.Value))
				{
					errorMessage = DbErrorFormatter.Format((string)errorParam.Value, DbRes.ResourceManager);
				}

				idLk = DbUtils.DbToInt(idLkParam.Value);
				_log.DebugFormat("idLk={0}, errorMessage={1}", idLk, errorMessage);
				return string.IsNullOrEmpty(errorMessage);
			}
		}
		#endregion

	}
}
