//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate Fluent Mapping template.
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;
using FluentNHibernate.Mapping;
using FluentNHibernate.MappingModel.Collections;

namespace Anete.Common.Data.Nh.Entities
{
  public partial class EBankingItem
  {
               /// <summary>
		       /// Nastavi novou hodnotu navigacni property se zachovanim konzistentnich udaju ve vazebnich kolekcich
		       /// </summary>
           public virtual void SetClientRegister(ClientRegister value)
           {
              ClientRegister oldValue = ClientRegister;
              ClientRegister = value;

              // uprava vazby
              if (value != null)
              {
                value.EBankingItems.Add(this);
              }
              if (oldValue != null)
              {
                oldValue.EBankingItems.Remove(this);
              }
           }
                      /// <summary>
		       /// Nastavi novou hodnotu navigacni property se zachovanim konzistentnich udaju ve vazebnich kolekcich
		       /// </summary>
           public virtual void SetEBankingBatch(EBankingBatch value)
           {
              EBankingBatch oldValue = EBankingBatch;
              EBankingBatch = value;

              // uprava vazby
              if (value != null)
              {
                value.EBankingItems.Add(this);
              }
              if (oldValue != null)
              {
                oldValue.EBankingItems.Remove(this);
              }
           }
             }
}
