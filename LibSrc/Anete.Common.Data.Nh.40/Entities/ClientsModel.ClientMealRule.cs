//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Anete.Common.Data.Nh.Entities
{

    /// <summary>
    /// Tabulka: dba.StravniciStravniPredpis
    /// </summary>
    public partial class ClientMealRule : Anete.Data.Nh.NhEntityBase, INotifyPropertyChanging, INotifyPropertyChanged {

        private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(System.String.Empty);

        private int _ClientRegisterId;

        private System.DateTime _ValidFrom;

        private ClientRegister _ClientRegister;

        private MealRule _MealRule;
    
        #region Extensibility Method Definitions
        
        partial void OnCreated();

        public override bool Equals(object obj)
        {
          ClientMealRule toCompare = obj as ClientMealRule;
          if (toCompare == null)
          {
            return false;
          }

          if (!Object.Equals(this.ClientRegisterId, toCompare.ClientRegisterId))
            return false;
          if (!Object.Equals(this.ValidFrom, toCompare.ValidFrom))
            return false;
         // pokud mam default primary key, znamena to, ze jedna o nove vytvorenou entitu, ktera jeste nema key prirazen. 
        // V takove pripade musim porovnat podle instance
        bool isDefault = true;
        if (this.ClientRegisterId != default(int))
        {
          isDefault = false;
        }
     
        if (this.ValidFrom != default(System.DateTime))
        {
          isDefault = false;
        }
     

          if (isDefault)
          {
            return object.ReferenceEquals(toCompare, this);
          }     
          return true;
        }

	private int? _hashCode = null;
        public override int GetHashCode()
        {
	if (_hashCode == null)
          {
          _hashCode = 13;
          _hashCode = (_hashCode * 7) + ClientRegisterId.GetHashCode();
          _hashCode = (_hashCode * 7) + ValidFrom.GetHashCode();
}
          return _hashCode.Value;
        }
        partial void OnClientRegisterIdChanging(int value);
        
        partial void OnClientRegisterIdChanged();
        partial void OnValidFromChanging(System.DateTime value);
        
        partial void OnValidFromChanged();
        partial void OnClientRegisterChanging(ClientRegister value);

        partial void OnClientRegisterChanged();
        partial void OnMealRuleChanging(MealRule value);

        partial void OnMealRuleChanged();
        
        #endregion
        public ClientMealRule()
        {
            OnCreated();
        }

    
        /// <summary>
        /// Sloupec: id_lk
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("ClientRegisterId", typeof(ClientMealRuleSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(ClientMealRule), Tag="ClientRegisterId")]
        public virtual int ClientRegisterId
        {
            get
            {
                return this._ClientRegisterId;
            }
            set
            {
                if (this._ClientRegisterId != value)
                {
                    this.OnClientRegisterIdChanging(value);
                    this.SendPropertyChanging();
                    this._ClientRegisterId = value;
                    this.SendPropertyChanged("ClientRegisterId");
                    this.OnClientRegisterIdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: plati_od
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("ValidFrom", typeof(ClientMealRuleSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(ClientMealRule), Tag="ValidFrom")]
        public virtual System.DateTime ValidFrom
        {
            get
            {
                return this._ValidFrom;
            }
            set
            {
                if (this._ValidFrom != value)
                {
                    this.OnValidFromChanging(value);
                    this.SendPropertyChanging();
                    this._ValidFrom = value;
                    this.SendPropertyChanged("ValidFrom");
                    this.OnValidFromChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: id_lk
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("ClientRegister", typeof(ClientMealRuleSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual ClientRegister ClientRegister
        {
            get
            {
                return this._ClientRegister;
            }
            set
            {
                if (this._ClientRegister != value)
                {
                    this.OnClientRegisterChanging(value);
                    this.SendPropertyChanging();
                    this._ClientRegister = value;
                    this.SendPropertyChanged("ClientRegister");
                    this.OnClientRegisterChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: KodSP
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("MealRule", typeof(ClientMealRuleSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual MealRule MealRule
        {
            get
            {
                return this._MealRule;
            }
            set
            {
                if (this._MealRule != value)
                {
                    this.OnMealRuleChanging(value);
                    this.SendPropertyChanging();
                    this._MealRule = value;
                    this.SendPropertyChanged("MealRule");
                    this.OnMealRuleChanged();
                }
            }
        }
   
        public virtual event PropertyChangingEventHandler PropertyChanging;

	// Mam v bazove tride, nema smysl generovat
	// public virtual event PropertyChangedEventHandler PropertyChanged;

        protected virtual void SendPropertyChanging()
        {
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, emptyChangingEventArgs);
        }

        protected virtual void SendPropertyChanging(System.String propertyName) 
        {   	
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, new PropertyChangingEventArgs(propertyName));
        }

        protected virtual void SendPropertyChanged(System.String propertyName)
        {    
		// mam v bazove tride, proto musim volat takto jednoduse
		OnPropertyChanged(propertyName);
/*		        var handler = this.PropertyChanged;
            if (handler != null)
                handler(this, new PropertyChangedEventArgs(propertyName));*/
        }
    }

}
