//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Anete.Common.Data.Nh.Entities.AneteUpdates
{
    public partial class UpdateBatchLog : Anete.Data.Nh.NhEntityBase {
    
        #region Extensibility Method Definitions
        
        partial void OnCreated();

        public override bool Equals(object obj)
        {
          UpdateBatchLog toCompare = obj as UpdateBatchLog;
          if (toCompare == null)
          {
            return false;
          }

          if (!Object.Equals(this.UpdateBatchLogId, toCompare.UpdateBatchLogId))
            return false;
         // pokud mam default primary key, znamena to, ze jedna o nove vytvorenou entitu, ktera jeste nema key prirazen. 
        // V takove pripade musim porovnat podle instance
        bool isDefault = true;
        if (this.UpdateBatchLogId != default(int))
        {
          isDefault = false;
        }
     

          if (isDefault)
          {
            return object.ReferenceEquals(toCompare, this);
          }     
          return true;
        }

	private int? _hashCode = null;
        public override int GetHashCode()
        {
	if (_hashCode == null)
          {
          _hashCode = 13;
          _hashCode = (_hashCode * 7) + UpdateBatchLogId.GetHashCode();
}
          return _hashCode.Value;
        }
        
        #endregion
        public UpdateBatchLog()
        {
            this.Repeat = false;
            OnCreated();
        }

        [Anete.Data.Attributes.EntitySRDisplayName("UpdateBatchLogId", typeof(UpdateBatchLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(UpdateBatchLog), Tag="UpdateBatchLogId")]
        public virtual int UpdateBatchLogId
        {
            get;
            set;
        }

        [Anete.Data.Attributes.EntitySRDisplayName("LogInfo", typeof(UpdateBatchLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(UpdateBatchLog), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 2147483647,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="LogInfo")]
        public virtual string LogInfo
        {
            get;
            set;
        }

        [Anete.Data.Attributes.EntitySRDisplayName("State", typeof(UpdateBatchLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(UpdateBatchLog), Tag="State")]
        public virtual Anete.AutoUpgrades.Interface.UpgradeResult State
        {
            get;
            set;
        }

        [Anete.Data.Attributes.EntitySRDisplayName("CreatedTime", typeof(UpdateBatchLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(UpdateBatchLog), Tag="CreatedTime")]
        public virtual System.DateTime CreatedTime
        {
            get;
            set;
        }

        [Anete.Data.Attributes.EntitySRDisplayName("Repeat", typeof(UpdateBatchLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(UpdateBatchLog), Tag="Repeat")]
        public virtual bool Repeat
        {
            get;
            set;
        }

        [Anete.Data.Attributes.EntitySRDisplayName("AppInstallationId", typeof(UpdateBatchLogSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        public virtual short? AppInstallationId
        {
            get;
            set;
        }

        [Anete.Data.Attributes.EntitySRDisplayName("Computer", typeof(UpdateBatchLogSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual Computer Computer
        {
            get;
            set;
        }

        [Anete.Data.Attributes.EntitySRDisplayName("UpdateBatch", typeof(UpdateBatchLogSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual UpdateBatch UpdateBatch
        {
            get;
            set;
        }
    }

}
