//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate Fluent Mapping template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;
using FluentNHibernate.Mapping;
using FluentNHibernate.MappingModel.Collections;

namespace Anete.Common.Data.Nh.Entities
{
    /// <summary>
    /// There are no comments for HsHotelAccountExtMap in the schema.
    /// </summary>
    public partial class HsHotelAccountExtMap : ClassMap<HsHotelAccountExt>
    {
        /// <summary>
        /// There are no comments for HsHotelAccountExtMap constructor in the schema.
        /// </summary>
        public HsHotelAccountExtMap()
        {
              Schema(@"dba");
              Table(@"HS_HotelAccountsExt");
              LazyLoad();
              Id(x => x.HotelAccountExtId)
                .Column("HotelAccountExtId")
                .CustomType("Guid")
                .Access.Property()
                .GeneratedBy.Assigned();
              Map(x => x.NumberOfGuests)    
                .Column("NumberOfGuests")
                .CustomType("Int16")
                .Access.Property()
                .Generated.Never();
              Component(x => x.Arrival,
                        aArrival => {
                        aArrival.Access.Property();
                        aArrival.Map(x => x.Number)    
                            .Column("Arrival_Number")
                            .CustomType("String")
                            .Access.Property()
                            .Generated.Never()
                            .Length(10);
                        aArrival.Map(x => x.Airport)    
                            .Column("Arrival_Airport")
                            .CustomType("String")
                            .Access.Property()
                            .Generated.Never()
                            .Length(10);
                        });
              Component(x => x.Departure,
                        aDeparture => {
                        aDeparture.Access.Property();
                        aDeparture.Map(x => x.Number)    
                            .Column("Departure_Number")
                            .CustomType("String")
                            .Access.Property()
                            .Generated.Never()
                            .Length(10);
                        aDeparture.Map(x => x.Airport)    
                            .Column("Departure_Airport")
                            .CustomType("String")
                            .Access.Property()
                            .Generated.Never()
                            .Length(10);
                        });
              Map(x => x.ReservationSource)    
                .Column("ReservationSource")
                .CustomType("Anete.Common.Data.Nh.Entities.HsReservationSource, Anete.Common.Data.Nh.40")
                .Access.Property()
                .Generated.Never();
              HasOne(x => x.HotelAccount)
                .Class<HsHotelAccount>()
                .Access.Property()
                .Cascade.None()
                .LazyLoad()
                .Constrained();
              ExtendMapping();
        }

        #region Partial Methods

        partial void ExtendMapping();

        #endregion
    }

}
