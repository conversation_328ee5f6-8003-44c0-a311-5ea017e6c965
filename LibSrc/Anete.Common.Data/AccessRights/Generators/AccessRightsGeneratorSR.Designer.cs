//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Anete.Common.Data.AccessRights.Generators {
    using System;
    
    
    /// <summary>
    /// A strongly-typed resource class, for looking up localized strings, formatting them, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilderEx class via the ResXFileCodeGeneratorEx custom tool.
    // To add or remove a member, edit your .ResX file then rerun the ResXFileCodeGeneratorEx custom tool or rebuild your VS.NET project.
    // Copyright (c) Dmytro <PERSON> 2006-2020 (http://dmytro.kryvko.googlepages.com/)
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("DMKSoftware.CodeGenerators.Tools.StronglyTypedResourceBuilderEx", "2.9.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Naming", "CA1724:TypeNamesShouldNotMatchNamespaces")]
    public partial class AccessRightsGeneratorSR {
        
        private static global::System.Resources.ResourceManager _resourceManager;
        
        private static object _internalSyncObject;
        
        private static global::System.Globalization.CultureInfo _resourceCulture;
        
        /// <summary>
        /// Initializes a AccessRightsGeneratorSR object.
        /// </summary>
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        public AccessRightsGeneratorSR() {
        }
        
        /// <summary>
        /// Thread safe lock object used by this class.
        /// </summary>
        public static object InternalSyncObject {
            get {
                if (object.ReferenceEquals(_internalSyncObject, null)) {
                    global::System.Threading.Interlocked.CompareExchange(ref _internalSyncObject, new object(), null);
                }
                return _internalSyncObject;
            }
        }
        
        /// <summary>
        /// Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(_resourceManager, null)) {
                    global::System.Threading.Monitor.Enter(InternalSyncObject);
                    try {
                        if (object.ReferenceEquals(_resourceManager, null)) {
                            global::System.Threading.Interlocked.Exchange(ref _resourceManager, new global::System.Resources.ResourceManager("Anete.Common.Data.AccessRights.Generators.AccessRightsGeneratorSR", typeof(AccessRightsGeneratorSR).Assembly));
                        }
                    }
                    finally {
                        global::System.Threading.Monitor.Exit(InternalSyncObject);
                    }
                }
                return _resourceManager;
            }
        }
        
        /// <summary>
        /// Overrides the current thread's CurrentUICulture property for all
        /// resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return _resourceCulture;
            }
            set {
                _resourceCulture = value;
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Přidávané právo obsahuje jiný počet parametrů než již existující právo.'.
        /// </summary>
        public static string BadParametersCount {
            get {
                return ResourceManager.GetString(ResourceNames.BadParametersCount, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Parametr přidávaného práva &apos;{0}&apos; nebyl nalezen u již existujícího práva.'.
        /// </summary>
        public static string ParameterNotFound {
            get {
                return ResourceManager.GetString(ResourceNames.ParameterNotFound, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Existuje právo použité na více místech.
        ///Právo: {0}
        ///Místa výskytu: {1}'.
        /// </summary>
        public static string ResultAlreadyContainsRight {
            get {
                return ResourceManager.GetString(ResourceNames.ResultAlreadyContainsRight, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Zadaný rightCode &apos;{0}&apos; není platný, musí obsahovat tečkovou konvenci.'.
        /// </summary>
        public static string RightCodeNotValid {
            get {
                return ResourceManager.GetString(ResourceNames.RightCodeNotValid, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Zdrojový adresář &apos;{0}&apos; neexistuje.'.
        /// </summary>
        public static string SourceDirectoryDoesntExist {
            get {
                return ResourceManager.GetString(ResourceNames.SourceDirectoryDoesntExist, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Zdrojový soubor &apos;{0}&apos; nebyl nalezen.'.
        /// </summary>
        public static string SourceFileNotFound {
            get {
                return ResourceManager.GetString(ResourceNames.SourceFileNotFound, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Parametr přidávaného práva &apos;{0}&apos; nebyl nalezen u již existujícího práva.'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string ParameterNotFoundFormat(object arg0) {
            return string.Format(_resourceCulture, ParameterNotFound, arg0);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Existuje právo použité na více místech.
        ///Právo: {0}
        ///Místa výskytu: {1}'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <param name="arg1">An object (1) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string ResultAlreadyContainsRightFormat(object arg0, object arg1) {
            return string.Format(_resourceCulture, ResultAlreadyContainsRight, arg0, arg1);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Zadaný rightCode &apos;{0}&apos; není platný, musí obsahovat tečkovou konvenci.'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string RightCodeNotValidFormat(object arg0) {
            return string.Format(_resourceCulture, RightCodeNotValid, arg0);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Zdrojový adresář &apos;{0}&apos; neexistuje.'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string SourceDirectoryDoesntExistFormat(object arg0) {
            return string.Format(_resourceCulture, SourceDirectoryDoesntExist, arg0);
        }
        
        /// <summary>
        /// Formats a localized string similar to 'Zdrojový soubor &apos;{0}&apos; nebyl nalezen.'.
        /// </summary>
        /// <param name="arg0">An object (0) to format.</param>
        /// <returns>A copy of format string in which the format items have been replaced by the String equivalent of the corresponding instances of Object in arguments.</returns>
        public static string SourceFileNotFoundFormat(object arg0) {
            return string.Format(_resourceCulture, SourceFileNotFound, arg0);
        }
        
        /// <summary>
        /// Lists all the resource names as constant string fields.
        /// </summary>
        public class ResourceNames {
            
            /// <summary>
            /// Stores the resource name 'BadParametersCount'.
            /// </summary>
            public const string BadParametersCount = "BadParametersCount";
            
            /// <summary>
            /// Stores the resource name 'ParameterNotFound'.
            /// </summary>
            public const string ParameterNotFound = "ParameterNotFound";
            
            /// <summary>
            /// Stores the resource name 'ResultAlreadyContainsRight'.
            /// </summary>
            public const string ResultAlreadyContainsRight = "ResultAlreadyContainsRight";
            
            /// <summary>
            /// Stores the resource name 'RightCodeNotValid'.
            /// </summary>
            public const string RightCodeNotValid = "RightCodeNotValid";
            
            /// <summary>
            /// Stores the resource name 'SourceDirectoryDoesntExist'.
            /// </summary>
            public const string SourceDirectoryDoesntExist = "SourceDirectoryDoesntExist";
            
            /// <summary>
            /// Stores the resource name 'SourceFileNotFound'.
            /// </summary>
            public const string SourceFileNotFound = "SourceFileNotFound";
        }
    }
}
