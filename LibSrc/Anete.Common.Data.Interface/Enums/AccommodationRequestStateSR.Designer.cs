//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Anete.Common.Data.Interface.Enums {
    using System;
    
    
    /// <summary>
    /// A strongly-typed resource class, for looking up localized strings, formatting them, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilderEx class via the InternalResXFileCodeGeneratorEx custom tool.
    // To add or remove a member, edit your .ResX file then rerun the InternalResXFileCodeGeneratorEx custom tool or rebuild your VS.NET project.
    // Copyright (c) Dmytro <PERSON> 2006-2019 (http://dmytro.kryvko.googlepages.com/)
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("DMKSoftware.CodeGenerators.Tools.StronglyTypedResourceBuilderEx", "2.6.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Naming", "CA1724:TypeNamesShouldNotMatchNamespaces")]
    internal partial class AccommodationRequestStateSR {
        
        private static global::System.Resources.ResourceManager _resourceManager;
        
        private static object _internalSyncObject;
        
        private static global::System.Globalization.CultureInfo _resourceCulture;
        
        /// <summary>
        /// Initializes a AccommodationRequestStateSR object.
        /// </summary>
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal AccommodationRequestStateSR() {
        }
        
        /// <summary>
        /// Thread safe lock object used by this class.
        /// </summary>
        internal static object InternalSyncObject {
            get {
                if (object.ReferenceEquals(_internalSyncObject, null)) {
                    global::System.Threading.Interlocked.CompareExchange(ref _internalSyncObject, new object(), null);
                }
                return _internalSyncObject;
            }
        }
        
        /// <summary>
        /// Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(_resourceManager, null)) {
                    global::System.Threading.Monitor.Enter(InternalSyncObject);
                    try {
                        if (object.ReferenceEquals(_resourceManager, null)) {
                            global::System.Threading.Interlocked.Exchange(ref _resourceManager, new global::System.Resources.ResourceManager("Anete.Common.Data.Interface.Enums.AccommodationRequestStateSR", typeof(AccommodationRequestStateSR).Assembly));
                        }
                    }
                    finally {
                        global::System.Threading.Monitor.Exit(InternalSyncObject);
                    }
                }
                return _resourceManager;
            }
        }
        
        /// <summary>
        /// Overrides the current thread's CurrentUICulture property for all
        /// resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return _resourceCulture;
            }
            set {
                _resourceCulture = value;
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Schválená'.
        /// </summary>
        internal static string AccommodationRequestState_Approved {
            get {
                return ResourceManager.GetString(ResourceNames.AccommodationRequestState_Approved, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Zrušená'.
        /// </summary>
        internal static string AccommodationRequestState_Canceled {
            get {
                return ResourceManager.GetString(ResourceNames.AccommodationRequestState_Canceled, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Zamítnutá (nesplnění podmínek)'.
        /// </summary>
        internal static string AccommodationRequestState_RejectedFailCondition {
            get {
                return ResourceManager.GetString(ResourceNames.AccommodationRequestState_RejectedFailCondition, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Zamítnuta (nedostatek kapacity)'.
        /// </summary>
        internal static string AccommodationRequestState_RejectedLackOfCapacity {
            get {
                return ResourceManager.GetString(ResourceNames.AccommodationRequestState_RejectedLackOfCapacity, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Podaná'.
        /// </summary>
        internal static string AccommodationRequestState_Submitted {
            get {
                return ResourceManager.GetString(ResourceNames.AccommodationRequestState_Submitted, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Lists all the resource names as constant string fields.
        /// </summary>
        internal class ResourceNames {
            
            /// <summary>
            /// Stores the resource name 'AccommodationRequestState_Approved'.
            /// </summary>
            internal const string AccommodationRequestState_Approved = "AccommodationRequestState_Approved";
            
            /// <summary>
            /// Stores the resource name 'AccommodationRequestState_Canceled'.
            /// </summary>
            internal const string AccommodationRequestState_Canceled = "AccommodationRequestState_Canceled";
            
            /// <summary>
            /// Stores the resource name 'AccommodationRequestState_RejectedFailCondition'.
            /// </summary>
            internal const string AccommodationRequestState_RejectedFailCondition = "AccommodationRequestState_RejectedFailCondition";
            
            /// <summary>
            /// Stores the resource name 'AccommodationRequestState_RejectedLackOfCapacity'.
            /// </summary>
            internal const string AccommodationRequestState_RejectedLackOfCapacity = "AccommodationRequestState_RejectedLackOfCapacity";
            
            /// <summary>
            /// Stores the resource name 'AccommodationRequestState_Submitted'.
            /// </summary>
            internal const string AccommodationRequestState_Submitted = "AccommodationRequestState_Submitted";
        }
    }
}
