using System;
using System.Collections.Generic;
using System.Text;
using Anete.Config.Core.Attributes;
using Anete.Common.Data.Interface.Enums;
using Anete.Common.Core.Interface.Enums;

namespace Anete.Config.Core.Categories
{
	/// <summary>
	/// Kategorie určená pro systémové věci konfigurátoru (verze konfiguračního souboru).
	/// </summary>
	[AttributeUsage(AttributeTargets.Property | AttributeTargets.Class, AllowMultiple = false)]
	public class SystemConfigCategoryAttribute : ConfigCategoryAttribute
    {
        /// <summary>
        /// Initializes a new instance of the SystemCategoryAttribute class.
        /// </summary>
        public SystemConfigCategoryAttribute()
            : base(new ConfigCategory[] {new ConfigCategory( SystemConfigCategoryAttributeSR.InformaceOKonfiguracniTride,  ApplicationType.Unassigned)}, ConfigCategoryType.Properties)
        {
        }
    }
}
