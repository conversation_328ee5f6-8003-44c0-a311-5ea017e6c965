using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using Anete.Common.Core.Interface.Validators;

namespace Anete.Common.Data.Nh.Entities
{
	public partial class VatGroupFull_Metadata
	{
		[LocalizableNotNullValidator(typeof(VatGroupFull), Tag = "VatRate")]
		public VatRate VatRate { get; set; }

		[DisplayFormat(DataFormatString = "{0:d}")]
		public virtual DateTime ValidFrom { get; set; }

		[DisplayFormat(DataFormatString = "{0:d}")]
		public virtual Nullable<DateTime> ValidTo { get; set; }
	}
}
