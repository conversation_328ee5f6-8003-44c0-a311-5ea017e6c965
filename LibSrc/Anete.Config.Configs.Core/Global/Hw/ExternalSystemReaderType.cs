using Anete.Config.Core.Internals;
using Anete.Utils.ComponentModel.Attributes;
using System;
using System.ComponentModel;

namespace Anete.Config.Configs.Core.Global.Hw
{
    /// <summary>
    /// typ ctecky identifikac<PERSON>h karet
    /// </summary>
    /// <remarks></remarks>
    [ConfigResourceEnum]
    [TypeConverter(typeof(ConfigEnumConverter<ExternalSystemReaderType>))]
    public enum ExternalSystemReaderType
    {
        [HiddenEnumValue]
        [EnumBasedFactory(null)]
        None,
        /// <summary>
        /// Nacitani prihlasenych klientu na zaklade vzniku zaznamu v tabulce DB Kredit
        /// </summary>
        [EnumBasedFactory(typeof(DatabaseExternalSystemReaderSettings))]
        Database,
    }
}
