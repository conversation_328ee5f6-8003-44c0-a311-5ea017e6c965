//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Anete.Common.Data.Nh.Entities
{

    /// <summary>
    /// Tabulka: dba.NAB_Nabidka
    /// </summary>
    public partial class Offer : Anete.Data.Nh.NhEntityBase, INotifyPropertyChanging, INotifyPropertyChanged {

        private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(System.String.Empty);

        private short _OfferId;

        private System.DateTime _OfferDate;

        private string _PurchaserName;

        private string _PurchaserAddress;

        private byte _ValidDays;

        private System.DateTime _Created;

        private bool _Closed;

        private string _Note;

        private ClientRegister _ClientRegister;

        private AppInstallation _AppInstallation;

        private User _User;

        private ISet<OfferDetail> _OfferDetails;
    
        #region Extensibility Method Definitions
        
        partial void OnCreated();

        public override bool Equals(object obj)
        {
          Offer toCompare = obj as Offer;
          if (toCompare == null)
          {
            return false;
          }

          if (!Object.Equals(this.OfferId, toCompare.OfferId))
            return false;
         // pokud mam default primary key, znamena to, ze jedna o nove vytvorenou entitu, ktera jeste nema key prirazen. 
        // V takove pripade musim porovnat podle instance
        bool isDefault = true;
        if (this.OfferId != default(short))
        {
          isDefault = false;
        }
     

          if (isDefault)
          {
            return object.ReferenceEquals(toCompare, this);
          }     
          return true;
        }

	private int? _hashCode = null;
        public override int GetHashCode()
        {
	if (_hashCode == null)
          {
          _hashCode = 13;
          _hashCode = (_hashCode * 7) + OfferId.GetHashCode();
}
          return _hashCode.Value;
        }
        partial void OnOfferIdChanging(short value);
        
        partial void OnOfferIdChanged();
        partial void OnOfferDateChanging(System.DateTime value);
        
        partial void OnOfferDateChanged();
        partial void OnPurchaserNameChanging(string value);
        
        partial void OnPurchaserNameChanged();
        partial void OnPurchaserAddressChanging(string value);
        
        partial void OnPurchaserAddressChanged();
        partial void OnValidDaysChanging(byte value);
        
        partial void OnValidDaysChanged();
        partial void OnCreatedChanging(System.DateTime value);
        
        partial void OnCreatedChanged();
        partial void OnClosedChanging(bool value);
        
        partial void OnClosedChanged();
        partial void OnNoteChanging(string value);
        
        partial void OnNoteChanged();
        partial void OnClientRegisterChanging(ClientRegister value);

        partial void OnClientRegisterChanged();
        partial void OnAppInstallationChanging(AppInstallation value);

        partial void OnAppInstallationChanged();
        partial void OnUserChanging(User value);

        partial void OnUserChanged();
        
        #endregion
        public Offer()
        {
            this._OfferDate = DateTime.Now;
            this._ValidDays = 14;
            this._Created = DateTime.Now;
            this._Closed = false;
            this._OfferDetails = new HashSet<OfferDetail>();
            OnCreated();
        }

    
        /// <summary>
        /// Sloupec: id
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("OfferId", typeof(OfferSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(Offer), Tag="OfferId")]
        public virtual short OfferId
        {
            get
            {
                return this._OfferId;
            }
            set
            {
                if (this._OfferId != value)
                {
                    this.OnOfferIdChanging(value);
                    this.SendPropertyChanging();
                    this._OfferId = value;
                    this.SendPropertyChanged("OfferId");
                    this.OnOfferIdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: datum
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("OfferDate", typeof(OfferSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(Offer), Tag="OfferDate")]
        public virtual System.DateTime OfferDate
        {
            get
            {
                return this._OfferDate;
            }
            set
            {
                if (this._OfferDate != value)
                {
                    this.OnOfferDateChanging(value);
                    this.SendPropertyChanging();
                    this._OfferDate = value;
                    this.SendPropertyChanged("OfferDate");
                    this.OnOfferDateChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: odberatel_nazev
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("PurchaserName", typeof(OfferSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(Offer), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 150,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="PurchaserName")]
        public virtual string PurchaserName
        {
            get
            {
                return this._PurchaserName;
            }
            set
            {
                if (this._PurchaserName != value)
                {
                    this.OnPurchaserNameChanging(value);
                    this.SendPropertyChanging();
                    this._PurchaserName = value;
                    this.SendPropertyChanged("PurchaserName");
                    this.OnPurchaserNameChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: odberatel_adresa
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("PurchaserAddress", typeof(OfferSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(Offer), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 300,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="PurchaserAddress")]
        public virtual string PurchaserAddress
        {
            get
            {
                return this._PurchaserAddress;
            }
            set
            {
                if (this._PurchaserAddress != value)
                {
                    this.OnPurchaserAddressChanging(value);
                    this.SendPropertyChanging();
                    this._PurchaserAddress = value;
                    this.SendPropertyChanged("PurchaserAddress");
                    this.OnPurchaserAddressChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: platnost
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("ValidDays", typeof(OfferSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(Offer), Tag="ValidDays")]
        public virtual byte ValidDays
        {
            get
            {
                return this._ValidDays;
            }
            set
            {
                if (this._ValidDays != value)
                {
                    this.OnValidDaysChanging(value);
                    this.SendPropertyChanging();
                    this._ValidDays = value;
                    this.SendPropertyChanged("ValidDays");
                    this.OnValidDaysChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: vytvoreno
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Created", typeof(OfferSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(Offer), Tag="Created")]
        public virtual System.DateTime Created
        {
            get
            {
                return this._Created;
            }
            set
            {
                if (this._Created != value)
                {
                    this.OnCreatedChanging(value);
                    this.SendPropertyChanging();
                    this._Created = value;
                    this.SendPropertyChanged("Created");
                    this.OnCreatedChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: uzavreno
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Closed", typeof(OfferSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(Offer), Tag="Closed")]
        public virtual bool Closed
        {
            get
            {
                return this._Closed;
            }
            set
            {
                if (this._Closed != value)
                {
                    this.OnClosedChanging(value);
                    this.SendPropertyChanging();
                    this._Closed = value;
                    this.SendPropertyChanged("Closed");
                    this.OnClosedChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: poznamka
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Note", typeof(OfferSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(Offer), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 500,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="Note")]
        public virtual string Note
        {
            get
            {
                return this._Note;
            }
            set
            {
                if (this._Note != value)
                {
                    this.OnNoteChanging(value);
                    this.SendPropertyChanging();
                    this._Note = value;
                    this.SendPropertyChanged("Note");
                    this.OnNoteChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: id_lk
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("ClientRegister", typeof(OfferSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual ClientRegister ClientRegister
        {
            get
            {
                return this._ClientRegister;
            }
            set
            {
                if (this._ClientRegister != value)
                {
                    this.OnClientRegisterChanging(value);
                    this.SendPropertyChanging();
                    this._ClientRegister = value;
                    this.SendPropertyChanged("ClientRegister");
                    this.OnClientRegisterChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: id_zarizeni
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("AppInstallation", typeof(OfferSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual AppInstallation AppInstallation
        {
            get
            {
                return this._AppInstallation;
            }
            set
            {
                if (this._AppInstallation != value)
                {
                    this.OnAppInstallationChanging(value);
                    this.SendPropertyChanging();
                    this._AppInstallation = value;
                    this.SendPropertyChanged("AppInstallation");
                    this.OnAppInstallationChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: IdUzivatel
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("User", typeof(OfferSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual User User
        {
            get
            {
                return this._User;
            }
            set
            {
                if (this._User != value)
                {
                    this.OnUserChanging(value);
                    this.SendPropertyChanging();
                    this._User = value;
                    this.SendPropertyChanged("User");
                    this.OnUserChanged();
                }
            }
        }

        [Anete.Data.Attributes.EntitySRDisplayName("OfferDetails", typeof(OfferSR))]
        [Anete.Data.Attributes.DbCollectionProperty]
        public virtual ISet<OfferDetail> OfferDetails
        {
            get
            {
                return this._OfferDetails;
            }
            set
            {
                this._OfferDetails = value;
            }
        }
   
        public virtual event PropertyChangingEventHandler PropertyChanging;

	// Mam v bazove tride, nema smysl generovat
	// public virtual event PropertyChangedEventHandler PropertyChanged;

        protected virtual void SendPropertyChanging()
        {
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, emptyChangingEventArgs);
        }

        protected virtual void SendPropertyChanging(System.String propertyName) 
        {   	
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, new PropertyChangingEventArgs(propertyName));
        }

        protected virtual void SendPropertyChanged(System.String propertyName)
        {    
		// mam v bazove tride, proto musim volat takto jednoduse
		OnPropertyChanged(propertyName);
/*		        var handler = this.PropertyChanged;
            if (handler != null)
                handler(this, new PropertyChangedEventArgs(propertyName));*/
        }
    }

}
