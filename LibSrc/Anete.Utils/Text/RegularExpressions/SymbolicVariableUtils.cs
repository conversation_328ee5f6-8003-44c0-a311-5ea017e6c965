using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Diagnostics;
using System.Text;
using System.Text.RegularExpressions;

namespace Anete.Utils.Text.RegularExpressions
{
    /// <summary>
    /// Utility pro praci se symbolickymi promennymi.
    /// V textu se symbolicka promenna vyskytuje ve formatu {[nazevprovideru].[nazepromenne]},
    /// napr. {Global.Date}.
    /// Cisty plny nazev promenne se udava bez slozenych zavorek.
    /// </summary>
    public static class SymbolicVariableUtils
    {
        /// <summary>
        /// Regulervni vyraz pro nalezeni symbolickych promenny uvnitr textu
        /// </summary>
        public static string FormatSymbolicRegexString = @"\{(?<id>\w+(\.\w+)*)(?<param>(,-?\d+)?:[^}]+)?\}";

        /// <summary>
        /// Vraci nazev symbolicke promenne slozene z nazvu providera a property
        /// </summary>
        /// <param name="providerName"></param>
        /// <param name="symbolicPropertyName"></param>
        /// <returns></returns>
        public static string GetSymbolicVariableFullName(string providerName, string symbolicPropertyName)
        {
            return String.Format("{0}.{1}", providerName, symbolicPropertyName);
        }

        /// <summary>
        /// Vraci nazev symbolicke promenne, kterou lze umistit do textu, tzn. vcetne slozenych zavorek.
        /// </summary>
        /// <param name="fullName"></param>
        /// <returns></returns>
        public static string GetSymbolicVariableForText(string fullName)
        {
            return string.Format ("{{{0}}}", fullName);
        }
    }
}
