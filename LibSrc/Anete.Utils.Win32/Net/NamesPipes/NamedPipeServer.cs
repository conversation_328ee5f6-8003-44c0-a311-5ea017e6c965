using Anete.Log.Core.Log4NetProxy;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading;
using Microsoft.Win32.SafeHandles;
using System.IO;
using System.Runtime.InteropServices;
using System.Security.AccessControl;
using Anete.Utils.Patterns;
using Anete.Utils.Win32.Win32Imports;

namespace Anete.Utils.Win32.Net.NamesPipes
{
    /// <summary>
    /// Server pro komunikaci s ostatnimi unmanaged aplikacemi. Zalozeni na namedpipes.
    /// </summary>
    public abstract class NamedPipeServer : Disposable
    {
        #region private fields...
        private Thread _serverThread;
        private static readonly ILogEx _log = LogManagerEx.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        private AutoResetEvent _terminatedEvent;
        #endregion

        #region constructors...
        /// <summary>
        /// Initializes a new instance of the AutoUpgradesServer class.
        /// </summary>
        public NamedPipeServer(string pipeName)
        {
            _pipeName = pipeName;
            _terminatedEvent = new AutoResetEvent(false);
        }
        #endregion

        #region public properties...
        private string _pipeName;
        /// <summary>
        /// Nazev pipy
        /// </summary>
        public string PipeName
        {
            get
            {
                return _pipeName;
            }
        }

        private const int _bufferSize = 4096;
        /// <summary>
        /// Velikost bufferu
        /// </summary>
        public int BufferSize
        {
            get
            {
                return _bufferSize;
            }
        }

        private Encoding _encoder = Encoding.GetEncoding(1250);
        /// <summary>
        /// Enkoder pouzity pro kodovani textu
        /// </summary>
        public Encoding Encoder
        {
            get
            {
                return _encoder;
            }
        }
        #endregion

        #region protected methods...
        /// <summary>
        /// Nacte data ze streamu
        /// </summary>
        /// <param name="clientStream"></param>
        /// <returns></returns>
        protected List<byte> GetData(FileStream clientStream)
        {
            List<byte> data = new List<byte>();
            byte readData;
            do
            {
                try
                {
                    readData = (byte)clientStream.ReadByte();
                    if (readData != 0)
                    {
                        data.Add(readData);
                    }
                }
                catch (Exception ex)
                {
                    _log.Error("Chyba pri cteni z namedpipe", ex);
                    //read error has occurred
                    break;
                }


            } while (readData != 0);
            return data;
        }
        #endregion

        #region public methods...
        /// <summary>
        /// Zastaveni serveru
        /// </summary>
        public void Stop()
        {
            _log.Info("Stop");
            if (_serverThread != null)
            {
                _terminatedEvent.Set();
                _serverThread.Join();
                _serverThread = null;
            }
        }

        /// <summary>
        /// Spusteni serveru
        /// </summary>
        public void Start()
        {
            _log.Info("Start");
            if (_serverThread != null)
            {
                throw new InvalidOperationException("Server je jiz spusten");
            }
            _serverThread = new Thread(ServerWork);
            _serverThread.Start();
        }
        #endregion

        #region private methods...
        private void ServerWork()
        {
            _log.Info("ServerWork");
            const uint DUPLEX = (0x00000003);
            const uint FILE_FLAG_OVERLAPPED = (0x40000000);

            System.Threading.NativeOverlapped op = new NativeOverlapped();
            AutoResetEvent _clientConnected = new AutoResetEvent(false);
            op.EventHandle = _clientConnected.SafeWaitHandle.DangerousGetHandle();

            while (true)
            {
                try
                {
					// Prevzato z http://codemortem.blogspot.com/2006/01/creating-null-dacl-in-managed-code.html
					// Musim nastavit security descriptor, abych mohl named pipe server vyuzivat pro komunikaci mezi vice uzivateli se zaplym UAC

					Advapi32.SECURITY_DESCRIPTOR sd = new Advapi32.SECURITY_DESCRIPTOR();
					Kernel32.SECURITY_ATTRIBUTES sa = new Kernel32.SECURITY_ATTRIBUTES();
                    IntPtr lpSecurityDescriptor;
                    IntPtr lpSA;

                    //Set up SECURITY_ATTRIBUTES structure
                    sa.nLength = Marshal.SizeOf(sa);
                    sa.bInheritHandle = 1;

                    //Set up lpSecurityDescriptor IntPtr
                    lpSecurityDescriptor = Marshal.AllocCoTaskMem(Marshal.SizeOf(sd));
                    Marshal.StructureToPtr(sd, lpSecurityDescriptor, false);

					//Set permission on lpSecurityDescriptor
					Advapi32.InitializeSecurityDescriptor(lpSecurityDescriptor, 1);
					Advapi32.SetSecurityDescriptorDacl(lpSecurityDescriptor, true, IntPtr.Zero, false);

                    //Assign it back to the SECURITY_ATTRIBUTES structure (sa)
                    sa.lpSecurityDescriptor = lpSecurityDescriptor;

                    //Now create a IntPtr for sa
                    lpSA = Marshal.AllocCoTaskMem(Marshal.SizeOf(sa));
                    Marshal.StructureToPtr(sa, lpSA, false);

                    SafeFileHandle clientHandle =
					Kernel32.CreateNamedPipe(
                         _pipeName,
                         DUPLEX | FILE_FLAG_OVERLAPPED,
                         4,
                         255,
                         _bufferSize,
                         _bufferSize,
                         0,
                         lpSA);

                    Marshal.FreeCoTaskMem(lpSecurityDescriptor);
                    Marshal.FreeCoTaskMem(lpSA);

                    //could not create named pipe
                    if (clientHandle.IsInvalid)
                    {
                        // vyjimku musim zabalit, protoze Win32 informace jsou casto nesmyslne
                        Exception innerException = Marshal.GetExceptionForHR(Marshal.GetHRForLastWin32Error());
                        throw new ArgumentException(NamedPipeServerSR.CannotCreateNamedPipeFormat(_pipeName), innerException);
                    }

					// blokujici funkce
					Kernel32.ConnectNamedPipe(clientHandle, ref op);

                    switch (WaitHandle.WaitAny(new WaitHandle[] { _clientConnected, _terminatedEvent }, Timeout.Infinite, false))
                    {
                        case 0:
                            // pripojene klienta obslouzim pomoci threadpoolu
                            ThreadPool.QueueUserWorkItem(
                                state =>
                                {
                                    try
                                    {
                                        ProcessClient(clientHandle);
                                    }
                                    finally
                                    {
                                        clientHandle.Close();
                                    }
                                });
                            break;

                        case 1:
                            return;

                        default:
                            throw ExcUtils.ArgumentOutOfRange("WaitHandle.WaitAny");
                    }
                }
                catch (Exception ex)
                {
                    _log.Error("Neosetrene vyjimka", ex);
                    if (!ExcUtils.IsCatchableExceptionType(ex))
                    {
                        _log.Warn("Ukoncujue server");
                        throw;
                    }
                    _log.Error("Cekam 30s a pote provedu restart");
                    // ostatni vyjimky pouze zaloguju a provedu restart
                    _terminatedEvent.WaitOne(30000, false);
                }
            }
        }
        #endregion

        /// <summary>
        /// Zpracovani klienta. O uzavreni handle se musi postarat tato metoda.
        /// </summary>
        /// <param name="clientHandle"></param>
        protected abstract void ProcessClient(SafeFileHandle clientHandle);


        /// <summary>
        /// Tato metoda musi uvolnit vsechny nespravovane prostredky.
        /// </summary>
        /// <remarks>Pokud ji zajima, zda je volana z Dispose, je mozno testovat flag IsDisposing</remarks>
        protected override void DisposeUnmanagedResources()
        {
            if (_serverThread != null)
            {
                Stop();
            }
        }
    }
}
