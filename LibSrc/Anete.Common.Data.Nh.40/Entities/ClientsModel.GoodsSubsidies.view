<?xml version="1.0" encoding="utf-8"?>
<EntityDeveloperDiagram>
  <Diagram Version="1.10.0.93">
    <DiagramModel>
      <Model xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xsi:type="ContextVwModel">
        <CustomProperties>
          <OID>0</OID>
          <BackgroundColor>Window</BackgroundColor>
        </CustomProperties>
        <Children>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>1</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>2</OID>
                  <Parent>1</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>3</OID>
                      <Parent>2</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>202 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>3992e889-b26c-4e06-be58-4467677b5ddd</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>4</OID>
                      <Parent>2</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>202 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>50a59164-8cb0-43de-acad-33fc99111832</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>5</OID>
                      <Parent>2</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>202 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>a8350999-834b-4962-be48-c4493d7242e6</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>6</OID>
                      <Parent>2</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>54.5 px</Y>
                    </Location>
                    <Size>
                      <Width>202 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>3fd04640-55d3-4ef5-900a-d395e7fce101</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>7</OID>
                      <Parent>2</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>72.5 px</Y>
                    </Location>
                    <Size>
                      <Width>202 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>8eb89318-1787-4702-ab91-5175f637412d</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>8</OID>
                      <Parent>2</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>90.5 px</Y>
                    </Location>
                    <Size>
                      <Width>202 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>573d2885-6ff9-4370-8294-94dfab0fece1</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>9</OID>
                      <Parent>2</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>108.5 px</Y>
                    </Location>
                    <Size>
                      <Width>202 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>36919320-743f-4e27-b848-05e5f0196f71</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>10</OID>
                      <Parent>2</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>126.5 px</Y>
                    </Location>
                    <Size>
                      <Width>202 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>aaef4658-5b34-4b7f-8c62-4dfc19935046</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>11</OID>
                      <Parent>2</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>144.5 px</Y>
                    </Location>
                    <Size>
                      <Width>202 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>2f73ea31-76d6-47a7-887b-5cdc05be7168</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>12</OID>
                      <Parent>2</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>162.5 px</Y>
                    </Location>
                    <Size>
                      <Width>202 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>009e09a9-23e4-47ad-b730-e787b5754d77</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>13</OID>
                      <Parent>2</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>180.5 px</Y>
                    </Location>
                    <Size>
                      <Width>202 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>c38cc72d-2d03-4e24-afb8-61c1d709d2d8</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>14</OID>
                      <Parent>2</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>198.5 px</Y>
                    </Location>
                    <Size>
                      <Width>202 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>d70d0b69-f151-4c1f-87d7-91abeae32c52</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>15</OID>
                      <Parent>2</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>216.5 px</Y>
                    </Location>
                    <Size>
                      <Width>202 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>c42c5665-6439-4701-ac1d-70559eb92c86</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>203 px</Width>
                  <Height>235 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>235 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>16</OID>
                  <Parent>1</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>17</OID>
                      <Parent>16</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>202 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>20cdcb75-7d98-4a2a-9abe-0bcd64091976</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>18</OID>
                      <Parent>16</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>18 px</Y>
                    </Location>
                    <Size>
                      <Width>202 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>19e2fa6d-0da1-49ef-a617-189bf00c51e4</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>234 px</Y>
                </Location>
                <Size>
                  <Width>203 px</Width>
                  <Height>55 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>55 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>39 px</X>
              <Y>38 px</Y>
            </Location>
            <Size>
              <Width>208 px</Width>
              <Height>338 px</Height>
            </Size>
            <MinSize>
              <Width>100 px</Width>
              <Height>338 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>338 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>23f39ac2-0852-47ef-a6f4-9da26e222a08</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>19</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>20</OID>
                  <Parent>19</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>21</OID>
                      <Parent>20</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>202 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>7761f618-18ed-43b4-ac51-b8e4ab8e000e</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>22</OID>
                      <Parent>20</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>202 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>25f4a75c-3a23-49d1-bd03-3841a09e3a3e</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>23</OID>
                      <Parent>20</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>202 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>885a1d3c-98d0-49e5-9294-4738431ea052</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>24</OID>
                      <Parent>20</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>54.5 px</Y>
                    </Location>
                    <Size>
                      <Width>202 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>48af7301-f989-4a44-a44d-85bf31c20949</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>203 px</Width>
                  <Height>73 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>73 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>25</OID>
                  <Parent>19</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>26</OID>
                      <Parent>25</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>202 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>6496858c-62c8-465c-bb82-166fa3c130c2</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>27</OID>
                      <Parent>25</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>18 px</Y>
                    </Location>
                    <Size>
                      <Width>202 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>84d177c7-e7de-4fa7-9a96-b8dca2d901da</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>72 px</Y>
                </Location>
                <Size>
                  <Width>203 px</Width>
                  <Height>55 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>55 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>39 px</X>
              <Y>436 px</Y>
            </Location>
            <Size>
              <Width>208 px</Width>
              <Height>176 px</Height>
            </Size>
            <MinSize>
              <Width>100 px</Width>
              <Height>176 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>176 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>33ae0cc1-06bd-4522-a2e6-c0d114e6fe36</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>28</OID>
              <OutModel>1</OutModel>
              <InModel>19</InModel>
              <OutPort>29</OutPort>
              <InPort>30</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>106.6 px</X>
                  <Y>336.4 px</Y>
                </Location>
                <Anchors>Left Right</Anchors>
                <Align>Bottom</Align>
                <CustomProperties>
                  <OID>29</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>106.6 px</X>
                  <Y>0 px</Y>
                </Location>
                <Anchors>Left Right</Anchors>
                <Align>Top</Align>
                <CustomProperties>
                  <OID>30</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>ae5966aa-5cfc-45fd-a8b4-c4da3c1ec66b</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>31</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>32</OID>
                  <Parent>31</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>33</OID>
                      <Parent>32</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>210 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>d25006d8-f7cc-4ed3-9425-c52350169b7e</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>34</OID>
                      <Parent>32</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>210 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>73a50e6e-e0e9-4ed4-a239-8ea6a3a5b331</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>35</OID>
                      <Parent>32</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>210 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>f4f87d5f-5b2d-4c68-9aa1-975a43471c1b</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>36</OID>
                      <Parent>32</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>54.5 px</Y>
                    </Location>
                    <Size>
                      <Width>210 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>c72e402a-9f3b-4657-8acd-c7b63438b468</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>37</OID>
                      <Parent>32</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>72.5 px</Y>
                    </Location>
                    <Size>
                      <Width>210 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>26ebd164-67cc-4b15-a19d-7f0800c8b698</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>38</OID>
                      <Parent>32</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>90.5 px</Y>
                    </Location>
                    <Size>
                      <Width>210 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>fb57ede8-eb69-45e8-9b84-f482ff5513fb</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>211 px</Width>
                  <Height>109 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>109 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>39</OID>
                  <Parent>31</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>40</OID>
                      <Parent>39</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>210 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>9fd04754-7b5d-4ba5-a518-1726b7322c60</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>41</OID>
                      <Parent>39</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>18 px</Y>
                    </Location>
                    <Size>
                      <Width>210 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>1565bedb-8d91-4571-82a3-349d17862908</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>42</OID>
                      <Parent>39</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>36 px</Y>
                    </Location>
                    <Size>
                      <Width>210 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>cd7b9e3f-9c51-488e-96d9-88a679fa6570</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>43</OID>
                      <Parent>39</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>54 px</Y>
                    </Location>
                    <Size>
                      <Width>210 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>6a39c087-1dbb-41a3-865c-6c042bf1718f</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>44</OID>
                      <Parent>39</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>60b1ebd8-eb85-4d6c-afae-57ae81811a86</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>45</OID>
                      <Parent>39</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>100 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Enabled>false</Enabled>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>d06ef12a-438b-401a-ba3e-45b6b83504c9</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>108 px</Y>
                </Location>
                <Size>
                  <Width>211 px</Width>
                  <Height>91 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>91 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>308 px</X>
              <Y>400 px</Y>
            </Location>
            <Size>
              <Width>216 px</Width>
              <Height>248 px</Height>
            </Size>
            <MinSize>
              <Width>100 px</Width>
              <Height>248 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>248 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>fd6bac83-5459-40d3-8568-a50862078df4</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>46</OID>
              <OutModel>31</OutModel>
              <InModel>19</InModel>
              <OutPort>47</OutPort>
              <InPort>48</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>0.400000000000034 px</X>
                  <Y>124 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Left</Align>
                <CustomProperties>
                  <OID>47</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>207.8 px</X>
                  <Y>88 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Right</Align>
                <CustomProperties>
                  <OID>48</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>8b742351-d740-480d-b4b0-170c3b988275</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="ClassVwModel">
            <CustomProperties>
              <OID>49</OID>
              <Parent>0</Parent>
            </CustomProperties>
            <Children>
              <Model xsi:type="PropertiesVwModel">
                <CustomProperties>
                  <OID>50</OID>
                  <Parent>49</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>51</OID>
                      <Parent>50</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>0.5 px</Y>
                    </Location>
                    <Size>
                      <Width>186 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>c56746d8-88b3-4a86-8365-e31e8bc04a13</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>52</OID>
                      <Parent>50</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>18.5 px</Y>
                    </Location>
                    <Size>
                      <Width>186 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>4baa529e-e114-41c1-9d70-054cecdb72b0</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>53</OID>
                      <Parent>50</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>36.5 px</Y>
                    </Location>
                    <Size>
                      <Width>186 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>264435f5-7e76-4624-9c65-d57b737edd4b</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>54</OID>
                      <Parent>50</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>54.5 px</Y>
                    </Location>
                    <Size>
                      <Width>186 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>e4a6da7b-cfe2-47d0-944d-0f23c4b9bb39</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>55</OID>
                      <Parent>50</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>72.5 px</Y>
                    </Location>
                    <Size>
                      <Width>186 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>7778facb-1310-4c20-9a22-0d54fc4e17ff</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>56</OID>
                      <Parent>50</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>90.5 px</Y>
                    </Location>
                    <Size>
                      <Width>186 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>852c153e-5898-4909-a3b6-920a8a2c5562</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="PropertyVwModel">
                    <CustomProperties>
                      <OID>57</OID>
                      <Parent>50</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0.5 px</X>
                      <Y>108.5 px</Y>
                    </Location>
                    <Size>
                      <Width>186 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>2188f708-dfae-456b-9546-eba1ca103f46</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Size>
                  <Width>187 px</Width>
                  <Height>127 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>127 px</Height>
                </MaxSize>
              </Model>
              <Model xsi:type="RelationPropertiesVwModel">
                <CustomProperties>
                  <OID>58</OID>
                  <Parent>49</Parent>
                </CustomProperties>
                <Children>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>59</OID>
                      <Parent>58</Parent>
                    </CustomProperties>
                    <Children />
                    <Size>
                      <Width>186 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>6dbc5277-7b30-493a-a759-1909ed40b32f</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>60</OID>
                      <Parent>58</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>18 px</Y>
                    </Location>
                    <Size>
                      <Width>186 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>7d2e313a-2aa5-41bf-be9b-fa1e5bf2d127</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>61</OID>
                      <Parent>58</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>36 px</Y>
                    </Location>
                    <Size>
                      <Width>186 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>322b9e47-59be-4ac8-95a7-d13fc62a9169</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                  <Model xsi:type="RelationPropertyVwModel">
                    <CustomProperties>
                      <OID>62</OID>
                      <Parent>58</Parent>
                    </CustomProperties>
                    <Children />
                    <Location>
                      <X>0 px</X>
                      <Y>54 px</Y>
                    </Location>
                    <Size>
                      <Width>186 px</Width>
                      <Height>18 px</Height>
                    </Size>
                    <Oid xsi:type="SchemaModelOID">
                      <Path>b9bc65e3-1a5c-43eb-a5ee-a3f79f92579c</Path>
                      <TypeName>EntityDeveloper.NHibernate.HibernateRelationProperty</TypeName>
                    </Oid>
                  </Model>
                </Children>
                <Location>
                  <X>0 px</X>
                  <Y>126 px</Y>
                </Location>
                <Size>
                  <Width>187 px</Width>
                  <Height>91 px</Height>
                </Size>
                <MaxSize>
                  <Width>0 px</Width>
                  <Height>91 px</Height>
                </MaxSize>
                <Hidden>false</Hidden>
              </Model>
            </Children>
            <Location>
              <X>318 px</X>
              <Y>74 px</Y>
            </Location>
            <Size>
              <Width>192 px</Width>
              <Height>266 px</Height>
            </Size>
            <MinSize>
              <Width>100 px</Width>
              <Height>266 px</Height>
            </MinSize>
            <MaxSize>
              <Width>800 px</Width>
              <Height>266 px</Height>
            </MaxSize>
            <Ports />
            <Oid xsi:type="SchemaModelOID">
              <Path>fecbdb3f-dc0b-4834-890f-70f2d20f08e3</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateClass</TypeName>
            </Oid>
            <FixedHeight>22 px</FixedHeight>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>63</OID>
              <OutModel>31</OutModel>
              <InModel>49</InModel>
              <OutPort>64</OutPort>
              <InPort>65</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>106 px</X>
                  <Y>0.800000000000011 px</Y>
                </Location>
                <Anchors>Left Right</Anchors>
                <Align>Top</Align>
                <CustomProperties>
                  <OID>64</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>96 px</X>
                  <Y>265.2 px</Y>
                </Location>
                <Anchors>Left Right</Anchors>
                <Align>Bottom</Align>
                <CustomProperties>
                  <OID>65</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>fa2f47fc-4fe5-4ae2-8941-a021d3c1fe7d</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
          <Model xsi:type="AssociationVwModel">
            <CustomProperties>
              <OID>66</OID>
              <OutModel>1</OutModel>
              <InModel>49</InModel>
              <OutPort>67</OutPort>
              <InPort>68</InPort>
              <Parent>0</Parent>
            </CustomProperties>
            <PortAnchors>
              <Port>
                <Location>
                  <X>207.8 px</X>
                  <Y>169.2 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Right</Align>
                <CustomProperties>
                  <OID>67</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
              <Port>
                <Location>
                  <X>-0.799999999999955 px</X>
                  <Y>133.2 px</Y>
                </Location>
                <Anchors>Top Bottom</Anchors>
                <Align>Left</Align>
                <CustomProperties>
                  <OID>68</OID>
                  <PortType>FloatAnchor</PortType>
                </CustomProperties>
              </Port>
            </PortAnchors>
            <IsManuallyRouted>false</IsManuallyRouted>
            <Oid xsi:type="SchemaModelOID">
              <Path>2f4fd197-4e59-4a88-808a-4d7bdf5a4c9c</Path>
              <TypeName>EntityDeveloper.NHibernate.HibernateAssociation</TypeName>
            </Oid>
          </Model>
        </Children>
        <GridSize>8 px</GridSize>
        <ViewPort>
          <ScaleMode>Free</ScaleMode>
          <Scale>1</Scale>
          <Location>
            <X>-122 px</X>
            <Y>6 px</Y>
          </Location>
        </ViewPort>
        <Oid xsi:type="SchemaModelOID">
          <Path>9cf690cb-e5be-4b9e-9cd9-51471560c52c</Path>
          <TypeName>EntityDeveloper.NHibernate.HibernateContextModel</TypeName>
        </Oid>
      </Model>
    </DiagramModel>
  </Diagram>
  <DiagramOptions Version="v2.0">
    <Options xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xsi:type="PageOptions">
      <TopLeftMargins>
        <Width>39.3700787401575 in/100</Width>
        <Height>39.3700787401575 in/100</Height>
      </TopLeftMargins>
      <BottomRightMargins>
        <Width>39.3700787401575 in/100</Width>
        <Height>39.3700787401575 in/100</Height>
      </BottomRightMargins>
      <PaperSize>
        <Width>827 in/100</Width>
        <Height>1169 in/100</Height>
      </PaperSize>
    </Options>
    <Options xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xsi:type="PrintOptions" />
    <Options xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xsi:type="ViewOptions">
      <ShadowOffset>
        <X>4 px</X>
        <Y>4 px</Y>
      </ShadowOffset>
      <CustomProperties />
    </Options>
    <EdDiagramOptions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
      <CustomProperties />
    </EdDiagramOptions>
  </DiagramOptions>
</EntityDeveloperDiagram>