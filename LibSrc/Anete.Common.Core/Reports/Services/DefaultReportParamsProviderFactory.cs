using System;
using System.Collections.Generic;
using System.Linq;
using Anete.Common.Core.Interface.Reports;
using Anete.Common.Core.Interface.Reports.Adapters;
using Anete.Utils.ComponentModel.ComponentPersistence;
using Unity;

namespace Anete.Common.Core.Reports.Services
{

	/// <summary>
	/// Factory pro vytvoreni poskytovatelu default hodnot pro parametry sestav.
	/// Pouziva se pri nacitani parametru sestav.Tam je zadouci, aby se vetsina parametru sestav nacetla a nektere se pak prenastavily. 
	/// Typicky u sestav ktere obsahuji rozmezi datumu chceme nacist vsechny parametry z ulozenych parametru a rozmezi datumu chceme 
	/// nastavit vzdy podle aktualniho datumu
	/// </summary>
	public class DefaultReportParamsProviderFactory : TypeAdapterFactoryBase<IDefaultReportParamsProvider>, IDefaultReportParamsProviderFactory
	{

		public DefaultReportParamsProviderFactory(IUnityContainer container)
			: base(container)
		{

		}

		public IEnumerable<IDefaultReportParamsProvider> CreateAdapters(IReportParams reportParams)
		{
			return GetAdaptersInt(reportParams.GetType(), AdapterSearchStrategy.InterfaceFirst);
		}

	}
}
