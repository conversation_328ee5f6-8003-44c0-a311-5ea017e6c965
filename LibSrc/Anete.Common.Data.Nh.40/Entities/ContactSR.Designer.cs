//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Anete.Common.Data.Nh.Entities {
    using System;
    
    
    /// <summary>
    /// A strongly-typed resource class, for looking up localized strings, formatting them, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilderEx class via the InternalResXFileCodeGeneratorEx custom tool.
    // To add or remove a member, edit your .ResX file then rerun the InternalResXFileCodeGeneratorEx custom tool or rebuild your VS.NET project.
    // Copyright (c) Dmytro <PERSON> 2006-2016 (http://dmytro.kryvko.googlepages.com/)
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("DMKSoftware.CodeGenerators.Tools.StronglyTypedResourceBuilderEx", "2.7.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
#if !SILVERLIGHT && !PocketPC && !Smartphone && !WindowsCE
    [global::System.Reflection.ObfuscationAttribute(Exclude=true, ApplyToMembers=true)]
#endif
    [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Naming", "CA1724:TypeNamesShouldNotMatchNamespaces")]
    internal partial class ContactSR {
        
        private static global::System.Resources.ResourceManager _resourceManager;
        
        private static object _internalSyncObject;
        
        private static global::System.Globalization.CultureInfo _resourceCulture;
        
        /// <summary>
        /// Initializes a ContactSR object.
        /// </summary>
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ContactSR() {
        }
        
        /// <summary>
        /// Thread safe lock object used by this class.
        /// </summary>
        internal static object InternalSyncObject {
            get {
                if (object.ReferenceEquals(_internalSyncObject, null)) {
                    global::System.Threading.Interlocked.CompareExchange(ref _internalSyncObject, new object(), null);
                }
                return _internalSyncObject;
            }
        }
        
        /// <summary>
        /// Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(_resourceManager, null)) {
                    global::System.Threading.Monitor.Enter(InternalSyncObject);
                    try {
                        if (object.ReferenceEquals(_resourceManager, null)) {
                            global::System.Threading.Interlocked.Exchange(ref _resourceManager, new global::System.Resources.ResourceManager("Anete.Common.Data.Nh.Entities.ContactSR", typeof(ContactSR).Assembly));
                        }
                    }
                    finally {
                        global::System.Threading.Monitor.Exit(InternalSyncObject);
                    }
                }
                return _resourceManager;
            }
        }
        
        /// <summary>
        /// Overrides the current thread's CurrentUICulture property for all
        /// resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return _resourceCulture;
            }
            set {
                _resourceCulture = value;
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Id kontaktu'.
        /// </summary>
        internal static string ContactId {
            get {
                return ResourceManager.GetString(ResourceNames.ContactId, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Titul'.
        /// </summary>
        internal static string Degree {
            get {
                return ResourceManager.GetString(ResourceNames.Degree, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Titul za jménem'.
        /// </summary>
        internal static string DegreeAfterName {
            get {
                return ResourceManager.GetString(ResourceNames.DegreeAfterName, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Email'.
        /// </summary>
        internal static string Email {
            get {
                return ResourceManager.GetString(ResourceNames.Email, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Jméno'.
        /// </summary>
        internal static string FirstName {
            get {
                return ResourceManager.GetString(ResourceNames.FirstName, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Příjmení'.
        /// </summary>
        internal static string LastName {
            get {
                return ResourceManager.GetString(ResourceNames.LastName, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Poznámka'.
        /// </summary>
        internal static string Note {
            get {
                return ResourceManager.GetString(ResourceNames.Note, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Organizace'.
        /// </summary>
        internal static string OrganizationContacts {
            get {
                return ResourceManager.GetString(ResourceNames.OrganizationContacts, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Looks up a localized string similar to 'Tel. číslo'.
        /// </summary>
        internal static string PhoneNumber {
            get {
                return ResourceManager.GetString(ResourceNames.PhoneNumber, _resourceCulture);
            }
        }
        
        /// <summary>
        /// Lists all the resource names as constant string fields.
        /// </summary>
        internal class ResourceNames {
            
            /// <summary>
            /// Stores the resource name 'ContactId'.
            /// </summary>
            internal const string ContactId = "ContactId";
            
            /// <summary>
            /// Stores the resource name 'Degree'.
            /// </summary>
            internal const string Degree = "Degree";
            
            /// <summary>
            /// Stores the resource name 'DegreeAfterName'.
            /// </summary>
            internal const string DegreeAfterName = "DegreeAfterName";
            
            /// <summary>
            /// Stores the resource name 'Email'.
            /// </summary>
            internal const string Email = "Email";
            
            /// <summary>
            /// Stores the resource name 'FirstName'.
            /// </summary>
            internal const string FirstName = "FirstName";
            
            /// <summary>
            /// Stores the resource name 'LastName'.
            /// </summary>
            internal const string LastName = "LastName";
            
            /// <summary>
            /// Stores the resource name 'Note'.
            /// </summary>
            internal const string Note = "Note";
            
            /// <summary>
            /// Stores the resource name 'OrganizationContacts'.
            /// </summary>
            internal const string OrganizationContacts = "OrganizationContacts";
            
            /// <summary>
            /// Stores the resource name 'PhoneNumber'.
            /// </summary>
            internal const string PhoneNumber = "PhoneNumber";
        }
    }
}
