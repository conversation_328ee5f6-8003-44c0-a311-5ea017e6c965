using System;
using System.Linq;

namespace Anete.WixSharp
{
	public static class IdGenerator
	{
		/// <summary>
		/// Z nazvu souboru udela id - musi se nahradit nektere znaky
		/// </summary>
		/// <param name="fileName"></param>
		/// <returns></returns>
		public static string EscapeId(string fileName)
		{
			// nenasel jsem ve WiX sharp verejne dostupnout metodu
			// maji nakodovane natvrdo pri generovani ID, viz. IncrementalIdFor
			// pro nase potreby toto jednoduche nahrazeni staci
			return fileName.RemoveDiacritics().Replace(' ', '_').Replace('(','_').Replace(')', '_');
		}
	}
}
