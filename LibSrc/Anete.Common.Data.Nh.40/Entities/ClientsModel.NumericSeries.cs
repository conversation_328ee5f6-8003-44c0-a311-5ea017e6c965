//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Neverzovano
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Anete.Common.Data.Nh.Entities
{

    /// <summary>
    /// Tabulka: dba.CFCiselneRady
    /// </summary>
    public partial class NumericSeries : Anete.Data.Nh.NhEntityBase, INotifyPropertyChanging, INotifyPropertyChanged {

        private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(System.String.Empty);

        private short _ProviderId;

        private string _NumericSeriesId;

        private System.DateTime _ValidFrom;

        private string _Prefix;

        private int _CurrentNumber;

        private byte _MaxLength;

        private Provider _Provider;
    
        #region Extensibility Method Definitions
        
        partial void OnCreated();

        public override bool Equals(object obj)
        {
          NumericSeries toCompare = obj as NumericSeries;
          if (toCompare == null)
          {
            return false;
          }

          if (!Object.Equals(this.ProviderId, toCompare.ProviderId))
            return false;
          if (!Object.Equals(this.NumericSeriesId, toCompare.NumericSeriesId))
            return false;
          if (!Object.Equals(this.ValidFrom, toCompare.ValidFrom))
            return false;
         // pokud mam default primary key, znamena to, ze jedna o nove vytvorenou entitu, ktera jeste nema key prirazen. 
        // V takove pripade musim porovnat podle instance
        bool isDefault = true;
        if (this.ProviderId != default(short))
        {
          isDefault = false;
        }
     
        if (this.NumericSeriesId != default(string))
        {
          isDefault = false;
        }
     
        if (this.ValidFrom != default(System.DateTime))
        {
          isDefault = false;
        }
     

          if (isDefault)
          {
            return object.ReferenceEquals(toCompare, this);
          }     
          return true;
        }

	private int? _hashCode = null;
        public override int GetHashCode()
        {
	if (_hashCode == null)
          {
          _hashCode = 13;
          _hashCode = (_hashCode * 7) + ProviderId.GetHashCode();
          _hashCode = (_hashCode * 7) + NumericSeriesId.GetHashCode();
          _hashCode = (_hashCode * 7) + ValidFrom.GetHashCode();
}
          return _hashCode.Value;
        }
        partial void OnProviderIdChanging(short value);
        
        partial void OnProviderIdChanged();
        partial void OnNumericSeriesIdChanging(string value);
        
        partial void OnNumericSeriesIdChanged();
        partial void OnValidFromChanging(System.DateTime value);
        
        partial void OnValidFromChanged();
        partial void OnPrefixChanging(string value);
        
        partial void OnPrefixChanged();
        partial void OnCurrentNumberChanging(int value);
        
        partial void OnCurrentNumberChanged();
        partial void OnMaxLengthChanging(byte value);
        
        partial void OnMaxLengthChanged();
        partial void OnProviderChanging(Provider value);

        partial void OnProviderChanged();
        
        #endregion
        public NumericSeries()
        {
            this._Prefix = @"";
            this._CurrentNumber = 1;
            this._MaxLength = 8;
            OnCreated();
        }

    
        /// <summary>
        /// Sloupec: id_provozovatel
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("ProviderId", typeof(NumericSeriesSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(NumericSeries), Tag="ProviderId")]
        public virtual short ProviderId
        {
            get
            {
                return this._ProviderId;
            }
            set
            {
                if (this._ProviderId != value)
                {
                    this.OnProviderIdChanging(value);
                    this.SendPropertyChanging();
                    this._ProviderId = value;
                    this.SendPropertyChanged("ProviderId");
                    this.OnProviderIdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: id_rada
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("NumericSeriesId", typeof(NumericSeriesSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(NumericSeries), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 1,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="NumericSeriesId")]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(NumericSeries), Tag="NumericSeriesId")]
        public virtual string NumericSeriesId
        {
            get
            {
                return this._NumericSeriesId;
            }
            set
            {
                if (this._NumericSeriesId != value)
                {
                    this.OnNumericSeriesIdChanging(value);
                    this.SendPropertyChanging();
                    this._NumericSeriesId = value;
                    this.SendPropertyChanged("NumericSeriesId");
                    this.OnNumericSeriesIdChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: PlatiOd
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("ValidFrom", typeof(NumericSeriesSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Data.Attributes.PrimaryKey]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(NumericSeries), Tag="ValidFrom")]
        public virtual System.DateTime ValidFrom
        {
            get
            {
                return this._ValidFrom;
            }
            set
            {
                if (this._ValidFrom != value)
                {
                    this.OnValidFromChanging(value);
                    this.SendPropertyChanging();
                    this._ValidFrom = value;
                    this.SendPropertyChanged("ValidFrom");
                    this.OnValidFromChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: prefix
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Prefix", typeof(NumericSeriesSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableStringLengthValidator(typeof(NumericSeries), 0,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Ignore, 10,  Microsoft.Practices.EnterpriseLibrary.Validation.Validators.RangeBoundaryType.Inclusive, Tag="Prefix")]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(NumericSeries), Tag="Prefix")]
        public virtual string Prefix
        {
            get
            {
                return this._Prefix;
            }
            set
            {
                if (this._Prefix != value)
                {
                    this.OnPrefixChanging(value);
                    this.SendPropertyChanging();
                    this._Prefix = value;
                    this.SendPropertyChanged("Prefix");
                    this.OnPrefixChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: akt_cislo
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("CurrentNumber", typeof(NumericSeriesSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(NumericSeries), Tag="CurrentNumber")]
        public virtual int CurrentNumber
        {
            get
            {
                return this._CurrentNumber;
            }
            set
            {
                if (this._CurrentNumber != value)
                {
                    this.OnCurrentNumberChanging(value);
                    this.SendPropertyChanging();
                    this._CurrentNumber = value;
                    this.SendPropertyChanged("CurrentNumber");
                    this.OnCurrentNumberChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: delka_cisla
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("MaxLength", typeof(NumericSeriesSR))]
        [Anete.Data.EntityReports.PrimitiveProperty]
        [Anete.Data.Attributes.DbPrimitiveProperty]
        [Anete.Common.Core.Interface.Validators.LocalizableNotNullValidator(typeof(NumericSeries), Tag="MaxLength")]
        public virtual byte MaxLength
        {
            get
            {
                return this._MaxLength;
            }
            set
            {
                if (this._MaxLength != value)
                {
                    this.OnMaxLengthChanging(value);
                    this.SendPropertyChanging();
                    this._MaxLength = value;
                    this.SendPropertyChanged("MaxLength");
                    this.OnMaxLengthChanged();
                }
            }
        }

    
        /// <summary>
        /// Sloupec: id_provozovatel
        /// </summary>
        [Anete.Data.Attributes.EntitySRDisplayName("Provider", typeof(NumericSeriesSR))]
        [Anete.Data.Attributes.DbNavigationProperty]
        public virtual Provider Provider
        {
            get
            {
                return this._Provider;
            }
            set
            {
                if (this._Provider != value)
                {
                    this.OnProviderChanging(value);
                    this.SendPropertyChanging();
                    this._Provider = value;
                    this.SendPropertyChanged("Provider");
                    this.OnProviderChanged();
                }
            }
        }
   
        public virtual event PropertyChangingEventHandler PropertyChanging;

	// Mam v bazove tride, nema smysl generovat
	// public virtual event PropertyChangedEventHandler PropertyChanged;

        protected virtual void SendPropertyChanging()
        {
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, emptyChangingEventArgs);
        }

        protected virtual void SendPropertyChanging(System.String propertyName) 
        {   	
		        var handler = this.PropertyChanging;
            if (handler != null)
                handler(this, new PropertyChangingEventArgs(propertyName));
        }

        protected virtual void SendPropertyChanged(System.String propertyName)
        {    
		// mam v bazove tride, proto musim volat takto jednoduse
		OnPropertyChanged(propertyName);
/*		        var handler = this.PropertyChanged;
            if (handler != null)
                handler(this, new PropertyChangedEventArgs(propertyName));*/
        }
    }

}
