using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace Anete.Utils.Reflection.PluginManager
{
	
	/// <summary>
	/// Type adapter pro natahovani pluginu z jedne assembly. Pouziva se v Shell, kdy potrebuji pri inicializaci nacist pluginy pouze pro jednu 
	/// assembly a ostatni me nezajimaji.
	/// </summary>
	public class LoadFromAssemblyPluginTypeAdapter<TPlugin> : GenericPluginTypeAdapterBase<TPlugin>
	{

		private Assembly _assembly;

		/// <summary>
		/// Initializes a new instance
		/// </summary>
		/// <param name="manager">The manager.</param>
		/// <param name="assembly">Assembly, ze ktere se nacitaji pluginy. Jine pluginy se ignoruji</param>
		public LoadFromAssemblyPluginTypeAdapter(IPluginManager manager, Assembly assembly)
			: base(manager)
		{
			_assembly = assembly;
		}

		/// <summary>
		/// Natahne vsechny pluginy daneho typu
		/// </summary>
		/// <returns></returns>
		protected override IEnumerable<Type> LoadPlugins()
		{
			return Manager.AvailablePlugins.Where(pluginType => typeof(TPlugin).IsAssignableFrom(pluginType) && 
				pluginType.Assembly == _assembly);
		}

	}
}
