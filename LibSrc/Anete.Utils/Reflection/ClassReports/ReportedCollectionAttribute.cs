using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Reflection;
using System.Runtime.Serialization;
using System.Xml.Serialization;
using System.Linq;

namespace Anete.Utils.Reflection.ClassReports
{
	/// <summary>
	/// Atributem se oznaci property, ktera obsahuje kolekci a ma byt soucasti reportu.
	/// Z jednotlivych slozek v kolekci se do reportu dostane nazev ziskany z dane property.
	/// </summary>
	public class ReportedCollectionAttribute : ReportedAttribute
	{
		/// <summary>
		/// Initializes a new instance of the ReportedCollectionAttribute class.
		/// </summary>
		public ReportedCollectionAttribute(string reportedPropertyName)
		{
			ReportedPropertyName = reportedPropertyName;
		}

		/// <summary>
		/// Nazev property prvku kolekce, ktera se ma zobrazit v reportu.
		/// </summary>
		public string ReportedPropertyName { get; set; }

		/// <summary>
		/// Pokud je nastaveno, upravi se reportovana kolekce na: Vse, Vse krome nebo vyjmenovany seznam
		/// </summary>
		public string AvailableItemsPropertyName { get; set; }
	}
}
