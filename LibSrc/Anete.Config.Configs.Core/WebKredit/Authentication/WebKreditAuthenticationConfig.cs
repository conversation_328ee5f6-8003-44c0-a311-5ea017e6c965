using Anete.Common.Core.Interface.Enums;
using Anete.Config.Configs.Core.Global.Authentication;
using Anete.Config.Configs.Core.Shared.Authentication;
using Anete.Config.Core.Attributes;
using Anete.Config.Core.Categories;
using Anete.Config.Core.Internals;
using Anete.Utils;
using Anete.Utils.ComponentModel.Attributes;
using System.Collections.Generic;
using System.ComponentModel;

namespace Anete.Config.Configs.Core.WebKredit.Authentication
{
	[WebKreditConfigDBKey("Authentication")]
	[ConfigVersion("*******")]
	[ConfigClassSRDescription(typeof(WebKreditAuthenticationConfig))]
	[DefaultConfigInstance(true)]
	[ApplicationCategory(ApplicationType.WebKredit2)]
	[ConfigAction(typeof(ReportAuthenticationServerConfigActionInfo))]
	public class WebKreditAuthenticationConfig : ConfigBase
	{
		public WebKreditAuthenticationConfig()
		{
			_settings = EnumBasedFactory<WebKreditAuthenticationType, AuthenticationSettingsBase>.CreateInstance(_authenticationTypeDefValue);
			_settingsAlt = EnumBasedFactory<WebKreditAuthenticationType, AuthenticationSettingsBase>.CreateInstance(_authenticationTypeAltDefValue);
		}

		private const int _inactivityTimeoutDefValue = 30;
		private int _inactivityTimeout = _inactivityTimeoutDefValue;

		[DefaultValue(_inactivityTimeoutDefValue)]
		[ConfigSRDisplayName(nameof(InactivityTimeout), typeof(WebKreditAuthenticationConfig))]
		[ConfigSRDescription(nameof(InactivityTimeout), typeof(WebKreditAuthenticationConfig))]
		public int InactivityTimeout
		{
			get { return _inactivityTimeout; }
			set
			{
				if (_inactivityTimeout == value)
				{
					return;
				}
				Guard.ArgumentIsGreatherThanZero(value, nameof(InactivityTimeout));
				_inactivityTimeout = value;
				OnPropertyChanged();
			}
		}

		private const string _logoutRedirectUrlDefValue = null;
		private string _logoutRedirectUrl = _logoutRedirectUrlDefValue;

		[DefaultValue(_logoutRedirectUrlDefValue)]
		[ConfigSRDisplayName(nameof(LogoutRedirectUrl), typeof(WebKreditAuthenticationConfig))]
		[ConfigSRDescription(nameof(LogoutRedirectUrl), typeof(WebKreditAuthenticationConfig))]
		public string LogoutRedirectUrl
		{
			get { return _logoutRedirectUrl; }
			set
			{
				if (_logoutRedirectUrl == value)
				{
					return;
				}
				_logoutRedirectUrl = value;
				OnPropertyChanged();
			}
		}

		private const string _badLoginRedirectUrlDefValue = null;
		private string _badLoginRedirectUrl = _badLoginRedirectUrlDefValue;

		[DefaultValue(_badLoginRedirectUrlDefValue)]
		[ConfigSRDisplayName(nameof(BadLoginRedirectUrl), typeof(WebKreditAuthenticationConfig))]
		[ConfigSRDescription(nameof(BadLoginRedirectUrl), typeof(WebKreditAuthenticationConfig))]
		public string BadLoginRedirectUrl
		{
			get { return _badLoginRedirectUrl; }
			set
			{
				if (_badLoginRedirectUrl == value)
				{
					return;
				}
				_badLoginRedirectUrl = value;
				OnPropertyChanged();
			}
		}

		private const WebKreditAuthenticationType _authenticationTypeDefValue = WebKreditAuthenticationType.Kredit;
		private WebKreditAuthenticationType _authenticationType = _authenticationTypeDefValue;

		[DefaultValue(typeof(WebKreditAuthenticationType), nameof(WebKreditAuthenticationType.Kredit))]
		[ConfigSRDisplayName(nameof(AuthenticationType), typeof(WebKreditAuthenticationConfig))]
		[ConfigSRDescriptionWithEnumDescription(nameof(AuthenticationType), typeof(WebKreditAuthenticationType), typeof(WebKreditAuthenticationConfig))]
		[RefreshProperties(RefreshProperties.All)]
		public WebKreditAuthenticationType AuthenticationType
		{
			get { return _authenticationType; }
			set
			{
				if (_authenticationType == value)
				{
					return;
				}
				Settings = EnumBasedFactory<WebKreditAuthenticationType, AuthenticationSettingsBase>.CreateInstance(value);
				_authenticationType = value;
				OnPropertyChanged();
			}
		}

		private const WebKreditAuthenticationType _authenticationTypeAltDefValue = WebKreditAuthenticationType.Kredit;
		private WebKreditAuthenticationType _authenticationTypeAlt = _authenticationTypeAltDefValue;

		[DefaultValue(typeof(WebKreditAuthenticationType), nameof(WebKreditAuthenticationType.Kredit))]
		[ConfigSRDisplayName(nameof(AuthenticationTypeAlt), typeof(WebKreditAuthenticationConfig))]
		[ConfigSRDescriptionWithEnumDescription(nameof(AuthenticationTypeAlt), typeof(WebKreditAuthenticationType), typeof(WebKreditAuthenticationConfig))]
		[RefreshProperties(RefreshProperties.All)]
		public WebKreditAuthenticationType AuthenticationTypeAlt
		{
			get { return _authenticationTypeAlt; }
			set
			{
				if (_authenticationTypeAlt == value)
				{
					return;
				}
				SettingsAlt = EnumBasedFactory<WebKreditAuthenticationType, AuthenticationSettingsBase>.CreateInstance(value);
				_authenticationTypeAlt = value;
				OnPropertyChanged();
			}
		}

		private AuthenticationSettingsBase _settings;

		[ConfigSRDisplayName(nameof(Settings), typeof(WebKreditAuthenticationConfig))]
		[ConfigSRDescription(nameof(Settings), typeof(WebKreditAuthenticationConfig))]
		[NestedClass]
		[Inherit(false)]
		public AuthenticationSettingsBase Settings
		{
			get { return _settings; }
			set
			{
				if (_settings == value)
				{
					return;
				}
				_settings = value;
				OnPropertyChanged();
			}
		}

		private AuthenticationSettingsBase _settingsAlt;

		[ConfigSRDisplayName(nameof(SettingsAlt), typeof(WebKreditAuthenticationConfig))]
		[ConfigSRDescription(nameof(SettingsAlt), typeof(WebKreditAuthenticationConfig))]
		[NestedClass]
		[Inherit(false)]
		public AuthenticationSettingsBase SettingsAlt
		{
			get { return _settingsAlt; }
			set
			{
				if (_settingsAlt == value)
				{
					return;
				}
				_settingsAlt = value;
				OnPropertyChanged();
			}
		}

		private const bool _failedLoginLockoutEnabledDefValue = false;
		private bool _failedLoginLockoutEnabled = _failedLoginLockoutEnabledDefValue;

		[DefaultValue(_failedLoginLockoutEnabledDefValue)]
		[ConfigSRDisplayName(nameof(FailedLoginLockoutEnabled), typeof(WebKreditAuthenticationConfig))]
		[ConfigSRDescription(nameof(FailedLoginLockoutEnabled), typeof(WebKreditAuthenticationConfig))]
		public bool FailedLoginLockoutEnabled
		{
			get { return _failedLoginLockoutEnabled; }
			set
			{
				if (_failedLoginLockoutEnabled == value)
				{
					return;
				}
				_failedLoginLockoutEnabled = value;
				OnPropertyChanged();
			}
		}

		private const int _failedLoginLockoutAttemptsDefValue = 5;
		private int _failedLoginLockoutAttempts = _failedLoginLockoutAttemptsDefValue;

		[DefaultValue(_failedLoginLockoutAttemptsDefValue)]
		[ConfigSRDisplayName(nameof(FailedLoginLockoutAttempts), typeof(WebKreditAuthenticationConfig))]
		[ConfigSRDescription(nameof(FailedLoginLockoutAttempts), typeof(WebKreditAuthenticationConfig))]
		public int FailedLoginLockoutAttempts
		{
			get { return _failedLoginLockoutAttempts; }
			set
			{
				if (_failedLoginLockoutAttempts == value)
				{
					return;
				}
				_failedLoginLockoutAttempts = value;
				OnPropertyChanged();
			}
		}

		private const int _failedLoginLockoutTimeoutDefValue = 15;
		private int _failedLoginLockoutTimeout = _failedLoginLockoutTimeoutDefValue;

		[DefaultValue(_failedLoginLockoutTimeoutDefValue)]
		[ConfigSRDisplayName(nameof(FailedLoginLockoutTimeout), typeof(WebKreditAuthenticationConfig))]
		[ConfigSRDescription(nameof(FailedLoginLockoutTimeout), typeof(WebKreditAuthenticationConfig))]
		public int FailedLoginLockoutTimeout
		{
			get { return _failedLoginLockoutTimeout; }
			set
			{
				if (_failedLoginLockoutTimeout == value)
				{
					return;
				}
				_failedLoginLockoutTimeout = value;
				OnPropertyChanged();
			}
		}

		private const TwoFactorAuthenticationType _twoFactorAuthenticationDefValue = TwoFactorAuthenticationType.None;
		private TwoFactorAuthenticationType _twoFactorAuthentication = _twoFactorAuthenticationDefValue;

		[DefaultValue(typeof(TwoFactorAuthenticationType), nameof(TwoFactorAuthenticationType.None))]
		[ConfigSRDisplayName(nameof(TwoFactorAuthentication), typeof(WebKreditAuthenticationConfig))]
		[ConfigSRDescriptionWithEnumDescription(nameof(TwoFactorAuthentication), typeof(TwoFactorAuthenticationType), typeof(WebKreditAuthenticationConfig))]
		public TwoFactorAuthenticationType TwoFactorAuthentication
		{
			get { return _twoFactorAuthentication; }
			set
			{
				if (_twoFactorAuthentication == value)
				{
					return;
				}

				_twoFactorAuthentication = value;
				OnPropertyChanged();
			}
		}

		[Browsable(false)]
		public IEnumerable<(WebKreditAuthenticationType Type, AuthenticationSettingsBase Settings)> Authentications
		{
			get
			{
				if (AuthenticationType != WebKreditAuthenticationType.Disabled)
				{
					yield return (AuthenticationType, Settings);
				}
				if (AuthenticationTypeAlt != WebKreditAuthenticationType.Disabled && (AuthenticationType != AuthenticationTypeAlt || SettingsAlt != null))
				{
					yield return (AuthenticationTypeAlt, SettingsAlt);
				}
			}
		}
	}
}