using System;
using System.Collections.Generic;
using System.Text;
using System.ComponentModel;

namespace Anete.Config.Configs.Core.Global.Hw
{
    /// <summary>
    /// Parametry fiskalu BowaPaegas Fm6 v1
    /// </summary>
    /// <remarks>
    /// !Pozor: Tento objekt musi zustat zachovan, i kdyz BowaFm6v1 uz neni podporovan kvuli moznosti 
    /// deserializace starych nastaveni z XmlConfig
    /// </remarks>
    public class BowaPaegasFm6v1SerialDeviceSettings : SerialDeviceSettings
    {
        /// <summary>
        /// Initializes a new instance of the GeneralSerialReaderSettings class.
        /// </summary>
        public BowaPaegasFm6v1SerialDeviceSettings()
            : base()
        {
            ReadTimeOut = 3000;
            WriteTimeOut = 3000;
            PortSettings = new BowaPaegasFm6v1ComPortSettings();
        }

        /// <summary>
        /// TimeOut pri cteni ze serioveho portu.
        /// </summary>
        /// <value></value>
        [DefaultValue(3000)]
        public override int ReadTimeOut
        {
            get
            {
                return base.ReadTimeOut;
            }
            set
            {
                base.ReadTimeOut = value;
            }
        }

        /// <summary>
        /// TimeOut pri cteni ze serioveho portu.
        /// </summary>
        /// <value></value>
        [DefaultValue(3000)]
        public override int WriteTimeOut
        {
            get
            {
                return base.WriteTimeOut;
            }
            set
            {
                base.WriteTimeOut = value;
            }
        }
    }
}
