//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Anete.Resources {
    using System;
    
    
    /// <summary>
    /// A strongly-typed resource class, for looking up localized strings, formatting them, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilderEx class via the ResXFileCodeGeneratorEx custom tool.
    // To add or remove a member, edit your .ResX file then rerun the ResXFileCodeGeneratorEx custom tool or rebuild your VS.NET project.
    // Copyright (c) Dmytro Kryv<PERSON> 2006-2021 (http://dmytro.kryvko.googlepages.com/)
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("DMKSoftware.CodeGenerators.Tools.StronglyTypedResourceBuilderEx", "2.9.1.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Naming", "CA1724:TypeNamesShouldNotMatchNamespaces")]
    public partial class SharedImages {
        
        private static global::System.Resources.ResourceManager _resourceManager;
        
        private static object _internalSyncObject;
        
        private static global::System.Globalization.CultureInfo _resourceCulture;
        
        /// <summary>
        /// Initializes a SharedImages object.
        /// </summary>
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        public SharedImages() {
        }
        
        /// <summary>
        /// Thread safe lock object used by this class.
        /// </summary>
        public static object InternalSyncObject {
            get {
                if (object.ReferenceEquals(_internalSyncObject, null)) {
                    global::System.Threading.Interlocked.CompareExchange(ref _internalSyncObject, new object(), null);
                }
                return _internalSyncObject;
            }
        }
        
        /// <summary>
        /// Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(_resourceManager, null)) {
                    global::System.Threading.Monitor.Enter(InternalSyncObject);
                    try {
                        if (object.ReferenceEquals(_resourceManager, null)) {
                            global::System.Threading.Interlocked.Exchange(ref _resourceManager, new global::System.Resources.ResourceManager("Anete.Resources.SharedImages", typeof(SharedImages).Assembly));
                        }
                    }
                    finally {
                        global::System.Threading.Monitor.Exit(InternalSyncObject);
                    }
                }
                return _resourceManager;
            }
        }
        
        /// <summary>
        /// Overrides the current thread's CurrentUICulture property for all
        /// resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return _resourceCulture;
            }
            set {
                _resourceCulture = value;
            }
        }
        
        /// <summary>
        /// Looks up a resource 'About16'.
        /// </summary>
        public static System.Drawing.Bitmap About16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.About16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'About32'.
        /// </summary>
        public static System.Drawing.Bitmap About32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.About32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'add2_16'.
        /// </summary>
        public static System.Drawing.Bitmap add2_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.add2_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'add2_32'.
        /// </summary>
        public static System.Drawing.Bitmap add2_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.add2_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'AneteLogoSvetle_120x69'.
        /// </summary>
        public static System.Drawing.Bitmap AneteLogoSvetle_120x69 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.AneteLogoSvetle_120x69, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'AneteLogoTmave_575x289'.
        /// </summary>
        public static System.Drawing.Bitmap AneteLogoTmave_575x289 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.AneteLogoTmave_575x289, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'AneteLogoTransparent'.
        /// </summary>
        public static System.Drawing.Bitmap AneteLogoTransparent {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.AneteLogoTransparent, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'ArrowDownGreen_16'.
        /// </summary>
        public static System.Drawing.Bitmap ArrowDownGreen_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.ArrowDownGreen_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'ArrowDownGreen_32'.
        /// </summary>
        public static System.Drawing.Bitmap ArrowDownGreen_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.ArrowDownGreen_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'ArrowIpGreen_32'.
        /// </summary>
        public static System.Drawing.Bitmap ArrowIpGreen_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.ArrowIpGreen_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'ArrowLeftGreen_16'.
        /// </summary>
        public static System.Drawing.Bitmap ArrowLeftGreen_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.ArrowLeftGreen_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'ArrowLeftGreen_32'.
        /// </summary>
        public static System.Drawing.Bitmap ArrowLeftGreen_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.ArrowLeftGreen_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'ArrowRightGreen_16'.
        /// </summary>
        public static System.Drawing.Bitmap ArrowRightGreen_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.ArrowRightGreen_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'ArrowRightGreen_32'.
        /// </summary>
        public static System.Drawing.Bitmap ArrowRightGreen_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.ArrowRightGreen_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'ArrowUpGreen_16'.
        /// </summary>
        public static System.Drawing.Bitmap ArrowUpGreen_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.ArrowUpGreen_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'ArrowUpGreen_32'.
        /// </summary>
        public static System.Drawing.Bitmap ArrowUpGreen_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.ArrowUpGreen_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Banana_32'.
        /// </summary>
        public static System.Drawing.Bitmap Banana_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Banana_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Barcode_16'.
        /// </summary>
        public static System.Drawing.Bitmap Barcode_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Barcode_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Barcode_32'.
        /// </summary>
        public static System.Drawing.Bitmap Barcode_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Barcode_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'BookBlue_16'.
        /// </summary>
        public static System.Drawing.Bitmap BookBlue_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.BookBlue_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'BookBlue_32'.
        /// </summary>
        public static System.Drawing.Bitmap BookBlue_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.BookBlue_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'BookBlueNext_16'.
        /// </summary>
        public static System.Drawing.Bitmap BookBlueNext_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.BookBlueNext_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'BookBlueNext_32'.
        /// </summary>
        public static System.Drawing.Bitmap BookBlueNext_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.BookBlueNext_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Books_16'.
        /// </summary>
        public static System.Drawing.Bitmap Books_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Books_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Books_24'.
        /// </summary>
        public static System.Drawing.Bitmap Books_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Books_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Books_32'.
        /// </summary>
        public static System.Drawing.Bitmap Books_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Books_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Breakpoint_16'.
        /// </summary>
        public static System.Drawing.Bitmap Breakpoint_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Breakpoint_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Breakpoint_32'.
        /// </summary>
        public static System.Drawing.Bitmap Breakpoint_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Breakpoint_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Breakpoints_16'.
        /// </summary>
        public static System.Drawing.Bitmap Breakpoints_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Breakpoints_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Breakpoints_32'.
        /// </summary>
        public static System.Drawing.Bitmap Breakpoints_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Breakpoints_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Businessman_16'.
        /// </summary>
        public static System.Drawing.Bitmap Businessman_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Businessman_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Businessman_24'.
        /// </summary>
        public static System.Drawing.Bitmap Businessman_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Businessman_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Businessman_32'.
        /// </summary>
        public static System.Drawing.Bitmap Businessman_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Businessman_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'BusinessmanPreferences_16'.
        /// </summary>
        public static System.Drawing.Bitmap BusinessmanPreferences_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.BusinessmanPreferences_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'BusinessmanPreferences_32'.
        /// </summary>
        public static System.Drawing.Bitmap BusinessmanPreferences_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.BusinessmanPreferences_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Calculator_16'.
        /// </summary>
        public static System.Drawing.Bitmap Calculator_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Calculator_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Calculator_24'.
        /// </summary>
        public static System.Drawing.Bitmap Calculator_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Calculator_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Calculator_32'.
        /// </summary>
        public static System.Drawing.Bitmap Calculator_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Calculator_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Calendar1_16'.
        /// </summary>
        public static System.Drawing.Bitmap Calendar1_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Calendar1_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Calendar1_32'.
        /// </summary>
        public static System.Drawing.Bitmap Calendar1_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Calendar1_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Calendar31_16'.
        /// </summary>
        public static System.Drawing.Bitmap Calendar31_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Calendar31_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Calendar31_32'.
        /// </summary>
        public static System.Drawing.Bitmap Calendar31_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Calendar31_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Cancel_16'.
        /// </summary>
        public static System.Drawing.Bitmap Cancel_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Cancel_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Cancel_32'.
        /// </summary>
        public static System.Drawing.Bitmap Cancel_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Cancel_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'CancelEdit_16'.
        /// </summary>
        public static System.Drawing.Bitmap CancelEdit_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.CancelEdit_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'CancelEdit_32'.
        /// </summary>
        public static System.Drawing.Bitmap CancelEdit_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.CancelEdit_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Cashier_16'.
        /// </summary>
        public static System.Drawing.Bitmap Cashier_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Cashier_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Cashier_24'.
        /// </summary>
        public static System.Drawing.Bitmap Cashier_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Cashier_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Cashier_32'.
        /// </summary>
        public static System.Drawing.Bitmap Cashier_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Cashier_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Certificate_16'.
        /// </summary>
        public static System.Drawing.Bitmap Certificate_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Certificate_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Certificate_24'.
        /// </summary>
        public static System.Drawing.Bitmap Certificate_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Certificate_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Certificate_32'.
        /// </summary>
        public static System.Drawing.Bitmap Certificate_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Certificate_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'ChangeUserPassword16'.
        /// </summary>
        public static System.Drawing.Bitmap ChangeUserPassword16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.ChangeUserPassword16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'ChangeUserPassword32'.
        /// </summary>
        public static System.Drawing.Bitmap ChangeUserPassword32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.ChangeUserPassword32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Chart_16'.
        /// </summary>
        public static System.Drawing.Bitmap Chart_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Chart_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Chart_24'.
        /// </summary>
        public static System.Drawing.Bitmap Chart_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Chart_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Chart_32'.
        /// </summary>
        public static System.Drawing.Bitmap Chart_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Chart_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Check_16'.
        /// </summary>
        public static System.Drawing.Bitmap Check_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Check_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'check_24'.
        /// </summary>
        public static System.Drawing.Bitmap check_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.check_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Check_32'.
        /// </summary>
        public static System.Drawing.Bitmap Check_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Check_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Check2_16'.
        /// </summary>
        public static System.Drawing.Bitmap Check2_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Check2_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Check2_32'.
        /// </summary>
        public static System.Drawing.Bitmap Check2_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Check2_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Clients_16'.
        /// </summary>
        public static System.Drawing.Bitmap Clients_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Clients_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Clients_24'.
        /// </summary>
        public static System.Drawing.Bitmap Clients_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Clients_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Clients_32'.
        /// </summary>
        public static System.Drawing.Bitmap Clients_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Clients_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Clock_16'.
        /// </summary>
        public static System.Drawing.Bitmap Clock_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Clock_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Clock_32'.
        /// </summary>
        public static System.Drawing.Bitmap Clock_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Clock_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Close_16'.
        /// </summary>
        public static System.Drawing.Bitmap Close_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Close_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Close_32'.
        /// </summary>
        public static System.Drawing.Bitmap Close_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Close_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'configure16'.
        /// </summary>
        public static System.Drawing.Bitmap configure16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.configure16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'configure32'.
        /// </summary>
        public static System.Drawing.Bitmap configure32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.configure32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Contract_16'.
        /// </summary>
        public static System.Drawing.Bitmap Contract_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Contract_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Contract_32'.
        /// </summary>
        public static System.Drawing.Bitmap Contract_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Contract_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Copy_16'.
        /// </summary>
        public static System.Drawing.Bitmap Copy_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Copy_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Copy_24'.
        /// </summary>
        public static System.Drawing.Bitmap Copy_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Copy_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Copy_32'.
        /// </summary>
        public static System.Drawing.Bitmap Copy_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Copy_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'DataGear_16'.
        /// </summary>
        public static System.Drawing.Bitmap DataGear_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.DataGear_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'DataGear_24'.
        /// </summary>
        public static System.Drawing.Bitmap DataGear_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.DataGear_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'DataGear_32'.
        /// </summary>
        public static System.Drawing.Bitmap DataGear_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.DataGear_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Document_16'.
        /// </summary>
        public static System.Drawing.Bitmap Document_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Document_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Document_32'.
        /// </summary>
        public static System.Drawing.Bitmap Document_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Document_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'DocumentCertificate_16'.
        /// </summary>
        public static System.Drawing.Bitmap DocumentCertificate_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.DocumentCertificate_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'DocumentCertificate_32'.
        /// </summary>
        public static System.Drawing.Bitmap DocumentCertificate_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.DocumentCertificate_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'DocumentInto_16'.
        /// </summary>
        public static System.Drawing.Bitmap DocumentInto_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.DocumentInto_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'DocumentInto_24'.
        /// </summary>
        public static System.Drawing.Bitmap DocumentInto_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.DocumentInto_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'DocumentInto_32'.
        /// </summary>
        public static System.Drawing.Bitmap DocumentInto_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.DocumentInto_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'DocumentNotebook_16'.
        /// </summary>
        public static System.Drawing.Bitmap DocumentNotebook_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.DocumentNotebook_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'DocumentNotebook_24'.
        /// </summary>
        public static System.Drawing.Bitmap DocumentNotebook_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.DocumentNotebook_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'DocumentNotebook_32'.
        /// </summary>
        public static System.Drawing.Bitmap DocumentNotebook_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.DocumentNotebook_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'DocumentOut_16'.
        /// </summary>
        public static System.Drawing.Bitmap DocumentOut_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.DocumentOut_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'DocumentOut_32'.
        /// </summary>
        public static System.Drawing.Bitmap DocumentOut_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.DocumentOut_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Documents_16'.
        /// </summary>
        public static System.Drawing.Bitmap Documents_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Documents_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Documents_32'.
        /// </summary>
        public static System.Drawing.Bitmap Documents_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Documents_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'DocumentsPreferences_16'.
        /// </summary>
        public static System.Drawing.Bitmap DocumentsPreferences_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.DocumentsPreferences_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'DocumentsPreferences_32'.
        /// </summary>
        public static System.Drawing.Bitmap DocumentsPreferences_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.DocumentsPreferences_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'DocumentText_16'.
        /// </summary>
        public static System.Drawing.Bitmap DocumentText_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.DocumentText_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'DocumentText_32'.
        /// </summary>
        public static System.Drawing.Bitmap DocumentText_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.DocumentText_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'DocumentView_16'.
        /// </summary>
        public static System.Drawing.Bitmap DocumentView_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.DocumentView_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'DocumentView_32'.
        /// </summary>
        public static System.Drawing.Bitmap DocumentView_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.DocumentView_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'DotChart16'.
        /// </summary>
        public static System.Drawing.Bitmap DotChart16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.DotChart16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'DotChart32'.
        /// </summary>
        public static System.Drawing.Bitmap DotChart32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.DotChart32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'DownMinus16'.
        /// </summary>
        public static System.Drawing.Bitmap DownMinus16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.DownMinus16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'DownMinus32'.
        /// </summary>
        public static System.Drawing.Bitmap DownMinus32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.DownMinus32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'EntityList_16'.
        /// </summary>
        public static System.Drawing.Bitmap EntityList_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.EntityList_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'EntityList_32'.
        /// </summary>
        public static System.Drawing.Bitmap EntityList_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.EntityList_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Eraser_16'.
        /// </summary>
        public static System.Drawing.Bitmap Eraser_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Eraser_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Eraser_32'.
        /// </summary>
        public static System.Drawing.Bitmap Eraser_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Eraser_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Export1_16'.
        /// </summary>
        public static System.Drawing.Bitmap Export1_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Export1_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Export1_32'.
        /// </summary>
        public static System.Drawing.Bitmap Export1_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Export1_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'FavoriteGroup_24'.
        /// </summary>
        public static System.Drawing.Bitmap FavoriteGroup_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.FavoriteGroup_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Fbs'.
        /// </summary>
        public static System.Drawing.Icon Fbs {
            get {
                return ((System.Drawing.Icon)(ResourceManager.GetObject(ResourceNames.Fbs, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Film_16'.
        /// </summary>
        public static System.Drawing.Bitmap Film_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Film_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Film_24'.
        /// </summary>
        public static System.Drawing.Bitmap Film_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Film_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Film_32'.
        /// </summary>
        public static System.Drawing.Bitmap Film_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Film_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Find_16'.
        /// </summary>
        public static System.Drawing.Bitmap Find_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Find_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Find_24'.
        /// </summary>
        public static System.Drawing.Bitmap Find_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Find_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Find_32'.
        /// </summary>
        public static System.Drawing.Bitmap Find_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Find_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'FirstAidbox_16'.
        /// </summary>
        public static System.Drawing.Bitmap FirstAidbox_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.FirstAidbox_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'FirstAidBox_24'.
        /// </summary>
        public static System.Drawing.Bitmap FirstAidBox_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.FirstAidBox_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'FirstAidBox_32'.
        /// </summary>
        public static System.Drawing.Bitmap FirstAidBox_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.FirstAidBox_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Fish_16'.
        /// </summary>
        public static System.Drawing.Bitmap Fish_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Fish_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Fish_24'.
        /// </summary>
        public static System.Drawing.Bitmap Fish_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Fish_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Fish_32'.
        /// </summary>
        public static System.Drawing.Bitmap Fish_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Fish_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Folder_16'.
        /// </summary>
        public static System.Drawing.Bitmap Folder_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Folder_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Folder_32'.
        /// </summary>
        public static System.Drawing.Bitmap Folder_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Folder_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Forbidden_16'.
        /// </summary>
        public static System.Drawing.Bitmap Forbidden_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Forbidden_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Forbidden_24'.
        /// </summary>
        public static System.Drawing.Bitmap Forbidden_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Forbidden_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Forbidden_32'.
        /// </summary>
        public static System.Drawing.Bitmap Forbidden_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Forbidden_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'FormBlue_16'.
        /// </summary>
        public static System.Drawing.Bitmap FormBlue_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.FormBlue_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'FormBlue_32'.
        /// </summary>
        public static System.Drawing.Bitmap FormBlue_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.FormBlue_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Funnel_16'.
        /// </summary>
        public static System.Drawing.Bitmap Funnel_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Funnel_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Funnel_32'.
        /// </summary>
        public static System.Drawing.Bitmap Funnel_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Funnel_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Funnel_add_16'.
        /// </summary>
        public static System.Drawing.Bitmap Funnel_add_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Funnel_add_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Funnel_add_32'.
        /// </summary>
        public static System.Drawing.Bitmap Funnel_add_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Funnel_add_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Funnel_delete_16'.
        /// </summary>
        public static System.Drawing.Bitmap Funnel_delete_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Funnel_delete_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Funnel_delete_32'.
        /// </summary>
        public static System.Drawing.Bitmap Funnel_delete_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Funnel_delete_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Gear_16'.
        /// </summary>
        public static System.Drawing.Bitmap Gear_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Gear_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Gear_24'.
        /// </summary>
        public static System.Drawing.Bitmap Gear_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Gear_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Gear_32'.
        /// </summary>
        public static System.Drawing.Bitmap Gear_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Gear_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'HandMoney_16'.
        /// </summary>
        public static System.Drawing.Bitmap HandMoney_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.HandMoney_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'HandMoney_24'.
        /// </summary>
        public static System.Drawing.Bitmap HandMoney_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.HandMoney_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'HandMoney_32'.
        /// </summary>
        public static System.Drawing.Bitmap HandMoney_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.HandMoney_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'HatWhite_16'.
        /// </summary>
        public static System.Drawing.Bitmap HatWhite_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.HatWhite_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'HatWhite_32'.
        /// </summary>
        public static System.Drawing.Bitmap HatWhite_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.HatWhite_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Help_16'.
        /// </summary>
        public static System.Drawing.Bitmap Help_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Help_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Help_24'.
        /// </summary>
        public static System.Drawing.Bitmap Help_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Help_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Help_32'.
        /// </summary>
        public static System.Drawing.Bitmap Help_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Help_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'IdCard16'.
        /// </summary>
        public static System.Drawing.Bitmap IdCard16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.IdCard16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'IdCard24'.
        /// </summary>
        public static System.Drawing.Bitmap IdCard24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.IdCard24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'IdCard32'.
        /// </summary>
        public static System.Drawing.Bitmap IdCard32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.IdCard32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Import1_16'.
        /// </summary>
        public static System.Drawing.Bitmap Import1_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Import1_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Import1_32'.
        /// </summary>
        public static System.Drawing.Bitmap Import1_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Import1_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Index_16'.
        /// </summary>
        public static System.Drawing.Bitmap Index_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Index_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Index_32'.
        /// </summary>
        public static System.Drawing.Bitmap Index_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Index_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Info_16'.
        /// </summary>
        public static System.Drawing.Bitmap Info_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Info_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Info_24'.
        /// </summary>
        public static System.Drawing.Bitmap Info_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Info_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Info_32'.
        /// </summary>
        public static System.Drawing.Bitmap Info_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Info_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Info2_16'.
        /// </summary>
        public static System.Drawing.Bitmap Info2_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Info2_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Info2_24'.
        /// </summary>
        public static System.Drawing.Bitmap Info2_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Info2_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Info2_32'.
        /// </summary>
        public static System.Drawing.Bitmap Info2_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Info2_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Key1_16'.
        /// </summary>
        public static System.Drawing.Bitmap Key1_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Key1_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Key1_24'.
        /// </summary>
        public static System.Drawing.Bitmap Key1_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Key1_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Key1_32'.
        /// </summary>
        public static System.Drawing.Bitmap Key1_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Key1_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Key1_48'.
        /// </summary>
        public static System.Drawing.Bitmap Key1_48 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Key1_48, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Layout_16'.
        /// </summary>
        public static System.Drawing.Bitmap Layout_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Layout_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Layout_32'.
        /// </summary>
        public static System.Drawing.Bitmap Layout_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Layout_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'LayoutContent_32'.
        /// </summary>
        public static System.Drawing.Bitmap LayoutContent_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.LayoutContent_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'LayoutHeader_32'.
        /// </summary>
        public static System.Drawing.Bitmap LayoutHeader_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.LayoutHeader_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'LayoutSidebar_32'.
        /// </summary>
        public static System.Drawing.Bitmap LayoutSidebar_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.LayoutSidebar_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Lock_16'.
        /// </summary>
        public static System.Drawing.Bitmap Lock_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Lock_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Lock_24'.
        /// </summary>
        public static System.Drawing.Bitmap Lock_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Lock_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Lock_32'.
        /// </summary>
        public static System.Drawing.Bitmap Lock_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Lock_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'LockOpen_16'.
        /// </summary>
        public static System.Drawing.Bitmap LockOpen_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.LockOpen_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'LockOpen_32'.
        /// </summary>
        public static System.Drawing.Bitmap LockOpen_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.LockOpen_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'LogoAnete'.
        /// </summary>
        public static byte[] LogoAnete {
            get {
                return ((byte[])(ResourceManager.GetObject(ResourceNames.LogoAnete, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Loudspeaker2_16'.
        /// </summary>
        public static System.Drawing.Bitmap Loudspeaker2_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Loudspeaker2_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Loudspeaker2_24'.
        /// </summary>
        public static System.Drawing.Bitmap Loudspeaker2_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Loudspeaker2_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Loudspeaker2_32'.
        /// </summary>
        public static System.Drawing.Bitmap Loudspeaker2_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Loudspeaker2_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Mail_16'.
        /// </summary>
        public static System.Drawing.Bitmap Mail_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Mail_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Mail_24'.
        /// </summary>
        public static System.Drawing.Bitmap Mail_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Mail_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Mail_32'.
        /// </summary>
        public static System.Drawing.Bitmap Mail_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Mail_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Mail_48'.
        /// </summary>
        public static System.Drawing.Bitmap Mail_48 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Mail_48, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'media_pause_16'.
        /// </summary>
        public static System.Drawing.Bitmap media_pause_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.media_pause_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'media_pause_32'.
        /// </summary>
        public static System.Drawing.Bitmap media_pause_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.media_pause_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'media_play_green_16'.
        /// </summary>
        public static System.Drawing.Bitmap media_play_green_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.media_play_green_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'media_play_green_32'.
        /// </summary>
        public static System.Drawing.Bitmap media_play_green_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.media_play_green_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'MediaPlayGreen_16'.
        /// </summary>
        public static System.Drawing.Bitmap MediaPlayGreen_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.MediaPlayGreen_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'MediaPlayGreen_32'.
        /// </summary>
        public static System.Drawing.Bitmap MediaPlayGreen_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.MediaPlayGreen_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Message_16'.
        /// </summary>
        public static System.Drawing.Bitmap Message_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Message_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Message_24'.
        /// </summary>
        public static System.Drawing.Bitmap Message_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Message_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Message_32'.
        /// </summary>
        public static System.Drawing.Bitmap Message_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Message_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Message_48'.
        /// </summary>
        public static System.Drawing.Bitmap Message_48 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Message_48, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Money_16'.
        /// </summary>
        public static System.Drawing.Bitmap Money_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Money_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Money_24'.
        /// </summary>
        public static System.Drawing.Bitmap Money_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Money_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Money_32'.
        /// </summary>
        public static System.Drawing.Bitmap Money_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Money_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Money2_16'.
        /// </summary>
        public static System.Drawing.Bitmap Money2_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Money2_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Money2_24'.
        /// </summary>
        public static System.Drawing.Bitmap Money2_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Money2_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Money2_32'.
        /// </summary>
        public static System.Drawing.Bitmap Money2_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Money2_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Money2_48'.
        /// </summary>
        public static System.Drawing.Bitmap Money2_48 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Money2_48, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Monitor_16'.
        /// </summary>
        public static System.Drawing.Bitmap Monitor_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Monitor_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Monitor_32'.
        /// </summary>
        public static System.Drawing.Bitmap Monitor_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Monitor_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'NavigateClose16'.
        /// </summary>
        public static System.Drawing.Bitmap NavigateClose16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.NavigateClose16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'NavigateClose32'.
        /// </summary>
        public static System.Drawing.Bitmap NavigateClose32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.NavigateClose32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'NavigateCross_16'.
        /// </summary>
        public static System.Drawing.Bitmap NavigateCross_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.NavigateCross_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'NavigateCross_32'.
        /// </summary>
        public static System.Drawing.Bitmap NavigateCross_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.NavigateCross_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'NavigateMinus_16'.
        /// </summary>
        public static System.Drawing.Bitmap NavigateMinus_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.NavigateMinus_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'NavigateMinus_32'.
        /// </summary>
        public static System.Drawing.Bitmap NavigateMinus_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.NavigateMinus_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'NavigateOpen16'.
        /// </summary>
        public static System.Drawing.Bitmap NavigateOpen16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.NavigateOpen16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'NavigateOpen32'.
        /// </summary>
        public static System.Drawing.Bitmap NavigateOpen32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.NavigateOpen32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'NavigatePlus_16'.
        /// </summary>
        public static System.Drawing.Bitmap NavigatePlus_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.NavigatePlus_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'NavigatePlus_32'.
        /// </summary>
        public static System.Drawing.Bitmap NavigatePlus_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.NavigatePlus_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'NavPlainBlue_16'.
        /// </summary>
        public static System.Drawing.Bitmap NavPlainBlue_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.NavPlainBlue_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'NavPlainBlue_32'.
        /// </summary>
        public static System.Drawing.Bitmap NavPlainBlue_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.NavPlainBlue_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Note_16'.
        /// </summary>
        public static System.Drawing.Bitmap Note_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Note_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Note_24'.
        /// </summary>
        public static System.Drawing.Bitmap Note_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Note_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Note_32'.
        /// </summary>
        public static System.Drawing.Bitmap Note_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Note_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Notebook_16'.
        /// </summary>
        public static System.Drawing.Bitmap Notebook_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Notebook_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Notebook_24'.
        /// </summary>
        public static System.Drawing.Bitmap Notebook_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Notebook_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Notebook_32'.
        /// </summary>
        public static System.Drawing.Bitmap Notebook_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Notebook_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'NotebookEdit_16'.
        /// </summary>
        public static System.Drawing.Bitmap NotebookEdit_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.NotebookEdit_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'NotebookEdit_24'.
        /// </summary>
        public static System.Drawing.Bitmap NotebookEdit_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.NotebookEdit_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'NotebookEdit_32'.
        /// </summary>
        public static System.Drawing.Bitmap NotebookEdit_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.NotebookEdit_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'NoteEdit_16'.
        /// </summary>
        public static System.Drawing.Bitmap NoteEdit_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.NoteEdit_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'NoteEdit_32'.
        /// </summary>
        public static System.Drawing.Bitmap NoteEdit_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.NoteEdit_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'NotePinned_16'.
        /// </summary>
        public static System.Drawing.Bitmap NotePinned_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.NotePinned_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'NotePinned_32'.
        /// </summary>
        public static System.Drawing.Bitmap NotePinned_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.NotePinned_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Paperclip_16'.
        /// </summary>
        public static System.Drawing.Bitmap Paperclip_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Paperclip_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Paperclip_24'.
        /// </summary>
        public static System.Drawing.Bitmap Paperclip_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Paperclip_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Paperclip_32'.
        /// </summary>
        public static System.Drawing.Bitmap Paperclip_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Paperclip_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'PaperclipAdd_16'.
        /// </summary>
        public static System.Drawing.Bitmap PaperclipAdd_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.PaperclipAdd_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'PaperclipAdd_24'.
        /// </summary>
        public static System.Drawing.Bitmap PaperclipAdd_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.PaperclipAdd_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'PaperclipAdd_32'.
        /// </summary>
        public static System.Drawing.Bitmap PaperclipAdd_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.PaperclipAdd_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'PaperclipDelete_16'.
        /// </summary>
        public static System.Drawing.Bitmap PaperclipDelete_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.PaperclipDelete_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'PaperclipDelete_24'.
        /// </summary>
        public static System.Drawing.Bitmap PaperclipDelete_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.PaperclipDelete_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'PaperclipDelete_32'.
        /// </summary>
        public static System.Drawing.Bitmap PaperclipDelete_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.PaperclipDelete_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'PaperclipEdit_16'.
        /// </summary>
        public static System.Drawing.Bitmap PaperclipEdit_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.PaperclipEdit_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'PaperclipEdit_24'.
        /// </summary>
        public static System.Drawing.Bitmap PaperclipEdit_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.PaperclipEdit_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'PaperclipEdit_32'.
        /// </summary>
        public static System.Drawing.Bitmap PaperclipEdit_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.PaperclipEdit_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Pda2_16'.
        /// </summary>
        public static System.Drawing.Bitmap Pda2_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Pda2_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Pda2_32'.
        /// </summary>
        public static System.Drawing.Bitmap Pda2_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Pda2_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Pda2Into_16'.
        /// </summary>
        public static System.Drawing.Bitmap Pda2Into_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Pda2Into_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Pda2Into_32'.
        /// </summary>
        public static System.Drawing.Bitmap Pda2Into_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Pda2Into_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Pda2Out_16'.
        /// </summary>
        public static System.Drawing.Bitmap Pda2Out_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Pda2Out_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Pda2Out_32'.
        /// </summary>
        public static System.Drawing.Bitmap Pda2Out_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Pda2Out_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Pencil_16'.
        /// </summary>
        public static System.Drawing.Bitmap Pencil_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Pencil_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Pencil_32'.
        /// </summary>
        public static System.Drawing.Bitmap Pencil_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Pencil_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'PosPrinter_16'.
        /// </summary>
        public static System.Drawing.Bitmap PosPrinter_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.PosPrinter_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'PosPrinter_24'.
        /// </summary>
        public static System.Drawing.Bitmap PosPrinter_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.PosPrinter_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'PosPrinter_32'.
        /// </summary>
        public static System.Drawing.Bitmap PosPrinter_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.PosPrinter_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Printer_16'.
        /// </summary>
        public static System.Drawing.Bitmap Printer_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Printer_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Printer_32'.
        /// </summary>
        public static System.Drawing.Bitmap Printer_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Printer_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'PrintPreview_16'.
        /// </summary>
        public static System.Drawing.Bitmap PrintPreview_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.PrintPreview_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'PrintPreview_32'.
        /// </summary>
        public static System.Drawing.Bitmap PrintPreview_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.PrintPreview_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Question_32'.
        /// </summary>
        public static System.Drawing.Bitmap Question_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Question_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Recycle_16'.
        /// </summary>
        public static System.Drawing.Bitmap Recycle_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Recycle_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Recycle_32'.
        /// </summary>
        public static System.Drawing.Bitmap Recycle_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Recycle_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Redo_16'.
        /// </summary>
        public static System.Drawing.Bitmap Redo_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Redo_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Redo_24'.
        /// </summary>
        public static System.Drawing.Bitmap Redo_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Redo_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Redo_32'.
        /// </summary>
        public static System.Drawing.Bitmap Redo_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Redo_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Refresh_16'.
        /// </summary>
        public static System.Drawing.Bitmap Refresh_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Refresh_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Refresh_24'.
        /// </summary>
        public static System.Drawing.Bitmap Refresh_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Refresh_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Refresh_32'.
        /// </summary>
        public static System.Drawing.Bitmap Refresh_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Refresh_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Report_16'.
        /// </summary>
        public static System.Drawing.Bitmap Report_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Report_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Report_24'.
        /// </summary>
        public static System.Drawing.Bitmap Report_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Report_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Report_32'.
        /// </summary>
        public static System.Drawing.Bitmap Report_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Report_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Row_16'.
        /// </summary>
        public static System.Drawing.Bitmap Row_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Row_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Row_32'.
        /// </summary>
        public static System.Drawing.Bitmap Row_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Row_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'RowAdd_16'.
        /// </summary>
        public static System.Drawing.Bitmap RowAdd_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.RowAdd_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'RowAdd_32'.
        /// </summary>
        public static System.Drawing.Bitmap RowAdd_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.RowAdd_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'RowDelete_16'.
        /// </summary>
        public static System.Drawing.Bitmap RowDelete_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.RowDelete_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'RowDelete_32'.
        /// </summary>
        public static System.Drawing.Bitmap RowDelete_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.RowDelete_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'RowEdit_16'.
        /// </summary>
        public static System.Drawing.Bitmap RowEdit_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.RowEdit_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'RowEdit_32'.
        /// </summary>
        public static System.Drawing.Bitmap RowEdit_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.RowEdit_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'RowPreferences_16'.
        /// </summary>
        public static System.Drawing.Bitmap RowPreferences_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.RowPreferences_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'RowPreferences_32'.
        /// </summary>
        public static System.Drawing.Bitmap RowPreferences_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.RowPreferences_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'SaveEdit_16'.
        /// </summary>
        public static System.Drawing.Bitmap SaveEdit_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.SaveEdit_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'SaveEdit_32'.
        /// </summary>
        public static System.Drawing.Bitmap SaveEdit_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.SaveEdit_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Scroll_16'.
        /// </summary>
        public static System.Drawing.Bitmap Scroll_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Scroll_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Scroll_24'.
        /// </summary>
        public static System.Drawing.Bitmap Scroll_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Scroll_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Scroll_32'.
        /// </summary>
        public static System.Drawing.Bitmap Scroll_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Scroll_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'ScrollAdd_32'.
        /// </summary>
        public static System.Drawing.Bitmap ScrollAdd_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.ScrollAdd_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'ScrollAdd16'.
        /// </summary>
        public static System.Drawing.Bitmap ScrollAdd16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.ScrollAdd16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'ScrollDelete_32'.
        /// </summary>
        public static System.Drawing.Bitmap ScrollDelete_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.ScrollDelete_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'ScrollDelete16'.
        /// </summary>
        public static System.Drawing.Bitmap ScrollDelete16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.ScrollDelete16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'ScrollEdit_32'.
        /// </summary>
        public static System.Drawing.Bitmap ScrollEdit_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.ScrollEdit_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'ScrollEdit16'.
        /// </summary>
        public static System.Drawing.Bitmap ScrollEdit16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.ScrollEdit16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Selection_16'.
        /// </summary>
        public static System.Drawing.Bitmap Selection_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Selection_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Selection_32'.
        /// </summary>
        public static System.Drawing.Bitmap Selection_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Selection_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Selection_delete_16'.
        /// </summary>
        public static System.Drawing.Bitmap Selection_delete_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Selection_delete_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Selection_delete_32'.
        /// </summary>
        public static System.Drawing.Bitmap Selection_delete_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Selection_delete_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'ServerClientExchange_16'.
        /// </summary>
        public static System.Drawing.Bitmap ServerClientExchange_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.ServerClientExchange_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'ServerClientExchange_32'.
        /// </summary>
        public static System.Drawing.Bitmap ServerClientExchange_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.ServerClientExchange_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'SortDown_16'.
        /// </summary>
        public static System.Drawing.Bitmap SortDown_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.SortDown_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'SortDown_24'.
        /// </summary>
        public static System.Drawing.Bitmap SortDown_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.SortDown_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'SortDown_32'.
        /// </summary>
        public static System.Drawing.Bitmap SortDown_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.SortDown_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'star_blue_16'.
        /// </summary>
        public static System.Drawing.Bitmap star_blue_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.star_blue_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'star_grey_16'.
        /// </summary>
        public static System.Drawing.Bitmap star_grey_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.star_grey_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Stop_16'.
        /// </summary>
        public static System.Drawing.Bitmap Stop_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Stop_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Stop_32'.
        /// </summary>
        public static System.Drawing.Bitmap Stop_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Stop_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'SystemAdministration_24'.
        /// </summary>
        public static System.Drawing.Bitmap SystemAdministration_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.SystemAdministration_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Table_16'.
        /// </summary>
        public static System.Drawing.Bitmap Table_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Table_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Table_32'.
        /// </summary>
        public static System.Drawing.Bitmap Table_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Table_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'TableSqlAdd_16'.
        /// </summary>
        public static System.Drawing.Bitmap TableSqlAdd_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.TableSqlAdd_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'TableSqlAdd_32'.
        /// </summary>
        public static System.Drawing.Bitmap TableSqlAdd_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.TableSqlAdd_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'TextCode_16'.
        /// </summary>
        public static System.Drawing.Bitmap TextCode_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.TextCode_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'TextCode_32'.
        /// </summary>
        public static System.Drawing.Bitmap TextCode_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.TextCode_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'TruckBlue_16'.
        /// </summary>
        public static System.Drawing.Bitmap TruckBlue_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.TruckBlue_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'TruckBlue_32'.
        /// </summary>
        public static System.Drawing.Bitmap TruckBlue_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.TruckBlue_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Undo_16'.
        /// </summary>
        public static System.Drawing.Bitmap Undo_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Undo_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Undo_24'.
        /// </summary>
        public static System.Drawing.Bitmap Undo_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Undo_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Undo_32'.
        /// </summary>
        public static System.Drawing.Bitmap Undo_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Undo_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'UpPlus16'.
        /// </summary>
        public static System.Drawing.Bitmap UpPlus16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.UpPlus16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'UpPlus32'.
        /// </summary>
        public static System.Drawing.Bitmap UpPlus32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.UpPlus32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'User1_16'.
        /// </summary>
        public static System.Drawing.Bitmap User1_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.User1_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'User1_32'.
        /// </summary>
        public static System.Drawing.Bitmap User1_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.User1_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'User1Lock_16'.
        /// </summary>
        public static System.Drawing.Bitmap User1Lock_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.User1Lock_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'User1Lock_32'.
        /// </summary>
        public static System.Drawing.Bitmap User1Lock_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.User1Lock_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Users1_16'.
        /// </summary>
        public static System.Drawing.Bitmap Users1_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Users1_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Users1_24'.
        /// </summary>
        public static System.Drawing.Bitmap Users1_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Users1_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Users1_32'.
        /// </summary>
        public static System.Drawing.Bitmap Users1_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Users1_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Users3_16'.
        /// </summary>
        public static System.Drawing.Bitmap Users3_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Users3_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Users3_32'.
        /// </summary>
        public static System.Drawing.Bitmap Users3_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Users3_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Users3Preferences_16'.
        /// </summary>
        public static System.Drawing.Bitmap Users3Preferences_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Users3Preferences_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Users3Preferences_32'.
        /// </summary>
        public static System.Drawing.Bitmap Users3Preferences_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Users3Preferences_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'View_16'.
        /// </summary>
        public static System.Drawing.Bitmap View_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.View_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'View_24'.
        /// </summary>
        public static System.Drawing.Bitmap View_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.View_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'View_32'.
        /// </summary>
        public static System.Drawing.Bitmap View_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.View_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Warning_16'.
        /// </summary>
        public static System.Drawing.Bitmap Warning_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Warning_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Warning_24'.
        /// </summary>
        public static System.Drawing.Bitmap Warning_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Warning_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Warning_32'.
        /// </summary>
        public static System.Drawing.Bitmap Warning_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Warning_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Window_16'.
        /// </summary>
        public static System.Drawing.Bitmap Window_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Window_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Window_32'.
        /// </summary>
        public static System.Drawing.Bitmap Window_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Window_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'WindowSidebar_16'.
        /// </summary>
        public static System.Drawing.Bitmap WindowSidebar_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.WindowSidebar_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'WindowSidebar_24'.
        /// </summary>
        public static System.Drawing.Bitmap WindowSidebar_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.WindowSidebar_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'WindowSidebar_32'.
        /// </summary>
        public static System.Drawing.Bitmap WindowSidebar_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.WindowSidebar_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'WindowSplitHor_16'.
        /// </summary>
        public static System.Drawing.Bitmap WindowSplitHor_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.WindowSplitHor_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'WindowSplitHor_24'.
        /// </summary>
        public static System.Drawing.Bitmap WindowSplitHor_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.WindowSplitHor_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'WindowSplitHor_32'.
        /// </summary>
        public static System.Drawing.Bitmap WindowSplitHor_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.WindowSplitHor_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'WineRedBottle_32'.
        /// </summary>
        public static System.Drawing.Bitmap WineRedBottle_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.WineRedBottle_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'WineWhiteGlass_16'.
        /// </summary>
        public static System.Drawing.Bitmap WineWhiteGlass_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.WineWhiteGlass_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'WineWhiteGlass_32'.
        /// </summary>
        public static System.Drawing.Bitmap WineWhiteGlass_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.WineWhiteGlass_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Workstation1_16'.
        /// </summary>
        public static System.Drawing.Bitmap Workstation1_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Workstation1_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Workstation1_24'.
        /// </summary>
        public static System.Drawing.Bitmap Workstation1_24 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Workstation1_24, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'Workstation1_32'.
        /// </summary>
        public static System.Drawing.Bitmap Workstation1_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.Workstation1_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'ZoomFit_16'.
        /// </summary>
        public static System.Drawing.Bitmap ZoomFit_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.ZoomFit_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'ZoomFit_32'.
        /// </summary>
        public static System.Drawing.Bitmap ZoomFit_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.ZoomFit_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'ZoomIn_16'.
        /// </summary>
        public static System.Drawing.Bitmap ZoomIn_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.ZoomIn_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'ZoomIn_32'.
        /// </summary>
        public static System.Drawing.Bitmap ZoomIn_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.ZoomIn_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'ZoomOut_16'.
        /// </summary>
        public static System.Drawing.Bitmap ZoomOut_16 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.ZoomOut_16, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Looks up a resource 'ZoomOut_32'.
        /// </summary>
        public static System.Drawing.Bitmap ZoomOut_32 {
            get {
                return ((System.Drawing.Bitmap)(ResourceManager.GetObject(ResourceNames.ZoomOut_32, _resourceCulture)));
            }
        }
        
        /// <summary>
        /// Lists all the resource names as constant string fields.
        /// </summary>
        public class ResourceNames {
            
            /// <summary>
            /// Stores the resource name 'About16'.
            /// </summary>
            public const string About16 = "About16";
            
            /// <summary>
            /// Stores the resource name 'About32'.
            /// </summary>
            public const string About32 = "About32";
            
            /// <summary>
            /// Stores the resource name 'add2_16'.
            /// </summary>
            public const string add2_16 = "add2_16";
            
            /// <summary>
            /// Stores the resource name 'add2_32'.
            /// </summary>
            public const string add2_32 = "add2_32";
            
            /// <summary>
            /// Stores the resource name 'AneteLogoSvetle_120x69'.
            /// </summary>
            public const string AneteLogoSvetle_120x69 = "AneteLogoSvetle_120x69";
            
            /// <summary>
            /// Stores the resource name 'AneteLogoTmave_575x289'.
            /// </summary>
            public const string AneteLogoTmave_575x289 = "AneteLogoTmave_575x289";
            
            /// <summary>
            /// Stores the resource name 'AneteLogoTransparent'.
            /// </summary>
            public const string AneteLogoTransparent = "AneteLogoTransparent";
            
            /// <summary>
            /// Stores the resource name 'ArrowDownGreen_16'.
            /// </summary>
            public const string ArrowDownGreen_16 = "ArrowDownGreen_16";
            
            /// <summary>
            /// Stores the resource name 'ArrowDownGreen_32'.
            /// </summary>
            public const string ArrowDownGreen_32 = "ArrowDownGreen_32";
            
            /// <summary>
            /// Stores the resource name 'ArrowIpGreen_32'.
            /// </summary>
            public const string ArrowIpGreen_32 = "ArrowIpGreen_32";
            
            /// <summary>
            /// Stores the resource name 'ArrowLeftGreen_16'.
            /// </summary>
            public const string ArrowLeftGreen_16 = "ArrowLeftGreen_16";
            
            /// <summary>
            /// Stores the resource name 'ArrowLeftGreen_32'.
            /// </summary>
            public const string ArrowLeftGreen_32 = "ArrowLeftGreen_32";
            
            /// <summary>
            /// Stores the resource name 'ArrowRightGreen_16'.
            /// </summary>
            public const string ArrowRightGreen_16 = "ArrowRightGreen_16";
            
            /// <summary>
            /// Stores the resource name 'ArrowRightGreen_32'.
            /// </summary>
            public const string ArrowRightGreen_32 = "ArrowRightGreen_32";
            
            /// <summary>
            /// Stores the resource name 'ArrowUpGreen_16'.
            /// </summary>
            public const string ArrowUpGreen_16 = "ArrowUpGreen_16";
            
            /// <summary>
            /// Stores the resource name 'ArrowUpGreen_32'.
            /// </summary>
            public const string ArrowUpGreen_32 = "ArrowUpGreen_32";
            
            /// <summary>
            /// Stores the resource name 'Banana_32'.
            /// </summary>
            public const string Banana_32 = "Banana_32";
            
            /// <summary>
            /// Stores the resource name 'Barcode_16'.
            /// </summary>
            public const string Barcode_16 = "Barcode_16";
            
            /// <summary>
            /// Stores the resource name 'Barcode_32'.
            /// </summary>
            public const string Barcode_32 = "Barcode_32";
            
            /// <summary>
            /// Stores the resource name 'BookBlue_16'.
            /// </summary>
            public const string BookBlue_16 = "BookBlue_16";
            
            /// <summary>
            /// Stores the resource name 'BookBlue_32'.
            /// </summary>
            public const string BookBlue_32 = "BookBlue_32";
            
            /// <summary>
            /// Stores the resource name 'BookBlueNext_16'.
            /// </summary>
            public const string BookBlueNext_16 = "BookBlueNext_16";
            
            /// <summary>
            /// Stores the resource name 'BookBlueNext_32'.
            /// </summary>
            public const string BookBlueNext_32 = "BookBlueNext_32";
            
            /// <summary>
            /// Stores the resource name 'Books_16'.
            /// </summary>
            public const string Books_16 = "Books_16";
            
            /// <summary>
            /// Stores the resource name 'Books_24'.
            /// </summary>
            public const string Books_24 = "Books_24";
            
            /// <summary>
            /// Stores the resource name 'Books_32'.
            /// </summary>
            public const string Books_32 = "Books_32";
            
            /// <summary>
            /// Stores the resource name 'Breakpoint_16'.
            /// </summary>
            public const string Breakpoint_16 = "Breakpoint_16";
            
            /// <summary>
            /// Stores the resource name 'Breakpoint_32'.
            /// </summary>
            public const string Breakpoint_32 = "Breakpoint_32";
            
            /// <summary>
            /// Stores the resource name 'Breakpoints_16'.
            /// </summary>
            public const string Breakpoints_16 = "Breakpoints_16";
            
            /// <summary>
            /// Stores the resource name 'Breakpoints_32'.
            /// </summary>
            public const string Breakpoints_32 = "Breakpoints_32";
            
            /// <summary>
            /// Stores the resource name 'Businessman_16'.
            /// </summary>
            public const string Businessman_16 = "Businessman_16";
            
            /// <summary>
            /// Stores the resource name 'Businessman_24'.
            /// </summary>
            public const string Businessman_24 = "Businessman_24";
            
            /// <summary>
            /// Stores the resource name 'Businessman_32'.
            /// </summary>
            public const string Businessman_32 = "Businessman_32";
            
            /// <summary>
            /// Stores the resource name 'BusinessmanPreferences_16'.
            /// </summary>
            public const string BusinessmanPreferences_16 = "BusinessmanPreferences_16";
            
            /// <summary>
            /// Stores the resource name 'BusinessmanPreferences_32'.
            /// </summary>
            public const string BusinessmanPreferences_32 = "BusinessmanPreferences_32";
            
            /// <summary>
            /// Stores the resource name 'Calculator_16'.
            /// </summary>
            public const string Calculator_16 = "Calculator_16";
            
            /// <summary>
            /// Stores the resource name 'Calculator_24'.
            /// </summary>
            public const string Calculator_24 = "Calculator_24";
            
            /// <summary>
            /// Stores the resource name 'Calculator_32'.
            /// </summary>
            public const string Calculator_32 = "Calculator_32";
            
            /// <summary>
            /// Stores the resource name 'Calendar1_16'.
            /// </summary>
            public const string Calendar1_16 = "Calendar1_16";
            
            /// <summary>
            /// Stores the resource name 'Calendar1_32'.
            /// </summary>
            public const string Calendar1_32 = "Calendar1_32";
            
            /// <summary>
            /// Stores the resource name 'Calendar31_16'.
            /// </summary>
            public const string Calendar31_16 = "Calendar31_16";
            
            /// <summary>
            /// Stores the resource name 'Calendar31_32'.
            /// </summary>
            public const string Calendar31_32 = "Calendar31_32";
            
            /// <summary>
            /// Stores the resource name 'Cancel_16'.
            /// </summary>
            public const string Cancel_16 = "Cancel_16";
            
            /// <summary>
            /// Stores the resource name 'Cancel_32'.
            /// </summary>
            public const string Cancel_32 = "Cancel_32";
            
            /// <summary>
            /// Stores the resource name 'CancelEdit_16'.
            /// </summary>
            public const string CancelEdit_16 = "CancelEdit_16";
            
            /// <summary>
            /// Stores the resource name 'CancelEdit_32'.
            /// </summary>
            public const string CancelEdit_32 = "CancelEdit_32";
            
            /// <summary>
            /// Stores the resource name 'Cashier_16'.
            /// </summary>
            public const string Cashier_16 = "Cashier_16";
            
            /// <summary>
            /// Stores the resource name 'Cashier_24'.
            /// </summary>
            public const string Cashier_24 = "Cashier_24";
            
            /// <summary>
            /// Stores the resource name 'Cashier_32'.
            /// </summary>
            public const string Cashier_32 = "Cashier_32";
            
            /// <summary>
            /// Stores the resource name 'Certificate_16'.
            /// </summary>
            public const string Certificate_16 = "Certificate_16";
            
            /// <summary>
            /// Stores the resource name 'Certificate_24'.
            /// </summary>
            public const string Certificate_24 = "Certificate_24";
            
            /// <summary>
            /// Stores the resource name 'Certificate_32'.
            /// </summary>
            public const string Certificate_32 = "Certificate_32";
            
            /// <summary>
            /// Stores the resource name 'ChangeUserPassword16'.
            /// </summary>
            public const string ChangeUserPassword16 = "ChangeUserPassword16";
            
            /// <summary>
            /// Stores the resource name 'ChangeUserPassword32'.
            /// </summary>
            public const string ChangeUserPassword32 = "ChangeUserPassword32";
            
            /// <summary>
            /// Stores the resource name 'Chart_16'.
            /// </summary>
            public const string Chart_16 = "Chart_16";
            
            /// <summary>
            /// Stores the resource name 'Chart_24'.
            /// </summary>
            public const string Chart_24 = "Chart_24";
            
            /// <summary>
            /// Stores the resource name 'Chart_32'.
            /// </summary>
            public const string Chart_32 = "Chart_32";
            
            /// <summary>
            /// Stores the resource name 'Check_16'.
            /// </summary>
            public const string Check_16 = "Check_16";
            
            /// <summary>
            /// Stores the resource name 'check_24'.
            /// </summary>
            public const string check_24 = "check_24";
            
            /// <summary>
            /// Stores the resource name 'Check_32'.
            /// </summary>
            public const string Check_32 = "Check_32";
            
            /// <summary>
            /// Stores the resource name 'Check2_16'.
            /// </summary>
            public const string Check2_16 = "Check2_16";
            
            /// <summary>
            /// Stores the resource name 'Check2_32'.
            /// </summary>
            public const string Check2_32 = "Check2_32";
            
            /// <summary>
            /// Stores the resource name 'Clients_16'.
            /// </summary>
            public const string Clients_16 = "Clients_16";
            
            /// <summary>
            /// Stores the resource name 'Clients_24'.
            /// </summary>
            public const string Clients_24 = "Clients_24";
            
            /// <summary>
            /// Stores the resource name 'Clients_32'.
            /// </summary>
            public const string Clients_32 = "Clients_32";
            
            /// <summary>
            /// Stores the resource name 'Clock_16'.
            /// </summary>
            public const string Clock_16 = "Clock_16";
            
            /// <summary>
            /// Stores the resource name 'Clock_32'.
            /// </summary>
            public const string Clock_32 = "Clock_32";
            
            /// <summary>
            /// Stores the resource name 'Close_16'.
            /// </summary>
            public const string Close_16 = "Close_16";
            
            /// <summary>
            /// Stores the resource name 'Close_32'.
            /// </summary>
            public const string Close_32 = "Close_32";
            
            /// <summary>
            /// Stores the resource name 'configure16'.
            /// </summary>
            public const string configure16 = "configure16";
            
            /// <summary>
            /// Stores the resource name 'configure32'.
            /// </summary>
            public const string configure32 = "configure32";
            
            /// <summary>
            /// Stores the resource name 'Contract_16'.
            /// </summary>
            public const string Contract_16 = "Contract_16";
            
            /// <summary>
            /// Stores the resource name 'Contract_32'.
            /// </summary>
            public const string Contract_32 = "Contract_32";
            
            /// <summary>
            /// Stores the resource name 'Copy_16'.
            /// </summary>
            public const string Copy_16 = "Copy_16";
            
            /// <summary>
            /// Stores the resource name 'Copy_24'.
            /// </summary>
            public const string Copy_24 = "Copy_24";
            
            /// <summary>
            /// Stores the resource name 'Copy_32'.
            /// </summary>
            public const string Copy_32 = "Copy_32";
            
            /// <summary>
            /// Stores the resource name 'DataGear_16'.
            /// </summary>
            public const string DataGear_16 = "DataGear_16";
            
            /// <summary>
            /// Stores the resource name 'DataGear_24'.
            /// </summary>
            public const string DataGear_24 = "DataGear_24";
            
            /// <summary>
            /// Stores the resource name 'DataGear_32'.
            /// </summary>
            public const string DataGear_32 = "DataGear_32";
            
            /// <summary>
            /// Stores the resource name 'Document_16'.
            /// </summary>
            public const string Document_16 = "Document_16";
            
            /// <summary>
            /// Stores the resource name 'Document_32'.
            /// </summary>
            public const string Document_32 = "Document_32";
            
            /// <summary>
            /// Stores the resource name 'DocumentCertificate_16'.
            /// </summary>
            public const string DocumentCertificate_16 = "DocumentCertificate_16";
            
            /// <summary>
            /// Stores the resource name 'DocumentCertificate_32'.
            /// </summary>
            public const string DocumentCertificate_32 = "DocumentCertificate_32";
            
            /// <summary>
            /// Stores the resource name 'DocumentInto_16'.
            /// </summary>
            public const string DocumentInto_16 = "DocumentInto_16";
            
            /// <summary>
            /// Stores the resource name 'DocumentInto_24'.
            /// </summary>
            public const string DocumentInto_24 = "DocumentInto_24";
            
            /// <summary>
            /// Stores the resource name 'DocumentInto_32'.
            /// </summary>
            public const string DocumentInto_32 = "DocumentInto_32";
            
            /// <summary>
            /// Stores the resource name 'DocumentNotebook_16'.
            /// </summary>
            public const string DocumentNotebook_16 = "DocumentNotebook_16";
            
            /// <summary>
            /// Stores the resource name 'DocumentNotebook_24'.
            /// </summary>
            public const string DocumentNotebook_24 = "DocumentNotebook_24";
            
            /// <summary>
            /// Stores the resource name 'DocumentNotebook_32'.
            /// </summary>
            public const string DocumentNotebook_32 = "DocumentNotebook_32";
            
            /// <summary>
            /// Stores the resource name 'DocumentOut_16'.
            /// </summary>
            public const string DocumentOut_16 = "DocumentOut_16";
            
            /// <summary>
            /// Stores the resource name 'DocumentOut_32'.
            /// </summary>
            public const string DocumentOut_32 = "DocumentOut_32";
            
            /// <summary>
            /// Stores the resource name 'Documents_16'.
            /// </summary>
            public const string Documents_16 = "Documents_16";
            
            /// <summary>
            /// Stores the resource name 'Documents_32'.
            /// </summary>
            public const string Documents_32 = "Documents_32";
            
            /// <summary>
            /// Stores the resource name 'DocumentsPreferences_16'.
            /// </summary>
            public const string DocumentsPreferences_16 = "DocumentsPreferences_16";
            
            /// <summary>
            /// Stores the resource name 'DocumentsPreferences_32'.
            /// </summary>
            public const string DocumentsPreferences_32 = "DocumentsPreferences_32";
            
            /// <summary>
            /// Stores the resource name 'DocumentText_16'.
            /// </summary>
            public const string DocumentText_16 = "DocumentText_16";
            
            /// <summary>
            /// Stores the resource name 'DocumentText_32'.
            /// </summary>
            public const string DocumentText_32 = "DocumentText_32";
            
            /// <summary>
            /// Stores the resource name 'DocumentView_16'.
            /// </summary>
            public const string DocumentView_16 = "DocumentView_16";
            
            /// <summary>
            /// Stores the resource name 'DocumentView_32'.
            /// </summary>
            public const string DocumentView_32 = "DocumentView_32";
            
            /// <summary>
            /// Stores the resource name 'DotChart16'.
            /// </summary>
            public const string DotChart16 = "DotChart16";
            
            /// <summary>
            /// Stores the resource name 'DotChart32'.
            /// </summary>
            public const string DotChart32 = "DotChart32";
            
            /// <summary>
            /// Stores the resource name 'DownMinus16'.
            /// </summary>
            public const string DownMinus16 = "DownMinus16";
            
            /// <summary>
            /// Stores the resource name 'DownMinus32'.
            /// </summary>
            public const string DownMinus32 = "DownMinus32";
            
            /// <summary>
            /// Stores the resource name 'EntityList_16'.
            /// </summary>
            public const string EntityList_16 = "EntityList_16";
            
            /// <summary>
            /// Stores the resource name 'EntityList_32'.
            /// </summary>
            public const string EntityList_32 = "EntityList_32";
            
            /// <summary>
            /// Stores the resource name 'Eraser_16'.
            /// </summary>
            public const string Eraser_16 = "Eraser_16";
            
            /// <summary>
            /// Stores the resource name 'Eraser_32'.
            /// </summary>
            public const string Eraser_32 = "Eraser_32";
            
            /// <summary>
            /// Stores the resource name 'Export1_16'.
            /// </summary>
            public const string Export1_16 = "Export1_16";
            
            /// <summary>
            /// Stores the resource name 'Export1_32'.
            /// </summary>
            public const string Export1_32 = "Export1_32";
            
            /// <summary>
            /// Stores the resource name 'FavoriteGroup_24'.
            /// </summary>
            public const string FavoriteGroup_24 = "FavoriteGroup_24";
            
            /// <summary>
            /// Stores the resource name 'Fbs'.
            /// </summary>
            public const string Fbs = "Fbs";
            
            /// <summary>
            /// Stores the resource name 'Film_16'.
            /// </summary>
            public const string Film_16 = "Film_16";
            
            /// <summary>
            /// Stores the resource name 'Film_24'.
            /// </summary>
            public const string Film_24 = "Film_24";
            
            /// <summary>
            /// Stores the resource name 'Film_32'.
            /// </summary>
            public const string Film_32 = "Film_32";
            
            /// <summary>
            /// Stores the resource name 'Find_16'.
            /// </summary>
            public const string Find_16 = "Find_16";
            
            /// <summary>
            /// Stores the resource name 'Find_24'.
            /// </summary>
            public const string Find_24 = "Find_24";
            
            /// <summary>
            /// Stores the resource name 'Find_32'.
            /// </summary>
            public const string Find_32 = "Find_32";
            
            /// <summary>
            /// Stores the resource name 'FirstAidbox_16'.
            /// </summary>
            public const string FirstAidbox_16 = "FirstAidbox_16";
            
            /// <summary>
            /// Stores the resource name 'FirstAidBox_24'.
            /// </summary>
            public const string FirstAidBox_24 = "FirstAidBox_24";
            
            /// <summary>
            /// Stores the resource name 'FirstAidBox_32'.
            /// </summary>
            public const string FirstAidBox_32 = "FirstAidBox_32";
            
            /// <summary>
            /// Stores the resource name 'Fish_16'.
            /// </summary>
            public const string Fish_16 = "Fish_16";
            
            /// <summary>
            /// Stores the resource name 'Fish_24'.
            /// </summary>
            public const string Fish_24 = "Fish_24";
            
            /// <summary>
            /// Stores the resource name 'Fish_32'.
            /// </summary>
            public const string Fish_32 = "Fish_32";
            
            /// <summary>
            /// Stores the resource name 'Folder_16'.
            /// </summary>
            public const string Folder_16 = "Folder_16";
            
            /// <summary>
            /// Stores the resource name 'Folder_32'.
            /// </summary>
            public const string Folder_32 = "Folder_32";
            
            /// <summary>
            /// Stores the resource name 'Forbidden_16'.
            /// </summary>
            public const string Forbidden_16 = "Forbidden_16";
            
            /// <summary>
            /// Stores the resource name 'Forbidden_24'.
            /// </summary>
            public const string Forbidden_24 = "Forbidden_24";
            
            /// <summary>
            /// Stores the resource name 'Forbidden_32'.
            /// </summary>
            public const string Forbidden_32 = "Forbidden_32";
            
            /// <summary>
            /// Stores the resource name 'FormBlue_16'.
            /// </summary>
            public const string FormBlue_16 = "FormBlue_16";
            
            /// <summary>
            /// Stores the resource name 'FormBlue_32'.
            /// </summary>
            public const string FormBlue_32 = "FormBlue_32";
            
            /// <summary>
            /// Stores the resource name 'Funnel_16'.
            /// </summary>
            public const string Funnel_16 = "Funnel_16";
            
            /// <summary>
            /// Stores the resource name 'Funnel_32'.
            /// </summary>
            public const string Funnel_32 = "Funnel_32";
            
            /// <summary>
            /// Stores the resource name 'Funnel_add_16'.
            /// </summary>
            public const string Funnel_add_16 = "Funnel_add_16";
            
            /// <summary>
            /// Stores the resource name 'Funnel_add_32'.
            /// </summary>
            public const string Funnel_add_32 = "Funnel_add_32";
            
            /// <summary>
            /// Stores the resource name 'Funnel_delete_16'.
            /// </summary>
            public const string Funnel_delete_16 = "Funnel_delete_16";
            
            /// <summary>
            /// Stores the resource name 'Funnel_delete_32'.
            /// </summary>
            public const string Funnel_delete_32 = "Funnel_delete_32";
            
            /// <summary>
            /// Stores the resource name 'Gear_16'.
            /// </summary>
            public const string Gear_16 = "Gear_16";
            
            /// <summary>
            /// Stores the resource name 'Gear_24'.
            /// </summary>
            public const string Gear_24 = "Gear_24";
            
            /// <summary>
            /// Stores the resource name 'Gear_32'.
            /// </summary>
            public const string Gear_32 = "Gear_32";
            
            /// <summary>
            /// Stores the resource name 'HandMoney_16'.
            /// </summary>
            public const string HandMoney_16 = "HandMoney_16";
            
            /// <summary>
            /// Stores the resource name 'HandMoney_24'.
            /// </summary>
            public const string HandMoney_24 = "HandMoney_24";
            
            /// <summary>
            /// Stores the resource name 'HandMoney_32'.
            /// </summary>
            public const string HandMoney_32 = "HandMoney_32";
            
            /// <summary>
            /// Stores the resource name 'HatWhite_16'.
            /// </summary>
            public const string HatWhite_16 = "HatWhite_16";
            
            /// <summary>
            /// Stores the resource name 'HatWhite_32'.
            /// </summary>
            public const string HatWhite_32 = "HatWhite_32";
            
            /// <summary>
            /// Stores the resource name 'Help_16'.
            /// </summary>
            public const string Help_16 = "Help_16";
            
            /// <summary>
            /// Stores the resource name 'Help_24'.
            /// </summary>
            public const string Help_24 = "Help_24";
            
            /// <summary>
            /// Stores the resource name 'Help_32'.
            /// </summary>
            public const string Help_32 = "Help_32";
            
            /// <summary>
            /// Stores the resource name 'IdCard16'.
            /// </summary>
            public const string IdCard16 = "IdCard16";
            
            /// <summary>
            /// Stores the resource name 'IdCard24'.
            /// </summary>
            public const string IdCard24 = "IdCard24";
            
            /// <summary>
            /// Stores the resource name 'IdCard32'.
            /// </summary>
            public const string IdCard32 = "IdCard32";
            
            /// <summary>
            /// Stores the resource name 'Import1_16'.
            /// </summary>
            public const string Import1_16 = "Import1_16";
            
            /// <summary>
            /// Stores the resource name 'Import1_32'.
            /// </summary>
            public const string Import1_32 = "Import1_32";
            
            /// <summary>
            /// Stores the resource name 'Index_16'.
            /// </summary>
            public const string Index_16 = "Index_16";
            
            /// <summary>
            /// Stores the resource name 'Index_32'.
            /// </summary>
            public const string Index_32 = "Index_32";
            
            /// <summary>
            /// Stores the resource name 'Info_16'.
            /// </summary>
            public const string Info_16 = "Info_16";
            
            /// <summary>
            /// Stores the resource name 'Info_24'.
            /// </summary>
            public const string Info_24 = "Info_24";
            
            /// <summary>
            /// Stores the resource name 'Info_32'.
            /// </summary>
            public const string Info_32 = "Info_32";
            
            /// <summary>
            /// Stores the resource name 'Info2_16'.
            /// </summary>
            public const string Info2_16 = "Info2_16";
            
            /// <summary>
            /// Stores the resource name 'Info2_24'.
            /// </summary>
            public const string Info2_24 = "Info2_24";
            
            /// <summary>
            /// Stores the resource name 'Info2_32'.
            /// </summary>
            public const string Info2_32 = "Info2_32";
            
            /// <summary>
            /// Stores the resource name 'Key1_16'.
            /// </summary>
            public const string Key1_16 = "Key1_16";
            
            /// <summary>
            /// Stores the resource name 'Key1_24'.
            /// </summary>
            public const string Key1_24 = "Key1_24";
            
            /// <summary>
            /// Stores the resource name 'Key1_32'.
            /// </summary>
            public const string Key1_32 = "Key1_32";
            
            /// <summary>
            /// Stores the resource name 'Key1_48'.
            /// </summary>
            public const string Key1_48 = "Key1_48";
            
            /// <summary>
            /// Stores the resource name 'Layout_16'.
            /// </summary>
            public const string Layout_16 = "Layout_16";
            
            /// <summary>
            /// Stores the resource name 'Layout_32'.
            /// </summary>
            public const string Layout_32 = "Layout_32";
            
            /// <summary>
            /// Stores the resource name 'LayoutContent_32'.
            /// </summary>
            public const string LayoutContent_32 = "LayoutContent_32";
            
            /// <summary>
            /// Stores the resource name 'LayoutHeader_32'.
            /// </summary>
            public const string LayoutHeader_32 = "LayoutHeader_32";
            
            /// <summary>
            /// Stores the resource name 'LayoutSidebar_32'.
            /// </summary>
            public const string LayoutSidebar_32 = "LayoutSidebar_32";
            
            /// <summary>
            /// Stores the resource name 'Lock_16'.
            /// </summary>
            public const string Lock_16 = "Lock_16";
            
            /// <summary>
            /// Stores the resource name 'Lock_24'.
            /// </summary>
            public const string Lock_24 = "Lock_24";
            
            /// <summary>
            /// Stores the resource name 'Lock_32'.
            /// </summary>
            public const string Lock_32 = "Lock_32";
            
            /// <summary>
            /// Stores the resource name 'LockOpen_16'.
            /// </summary>
            public const string LockOpen_16 = "LockOpen_16";
            
            /// <summary>
            /// Stores the resource name 'LockOpen_32'.
            /// </summary>
            public const string LockOpen_32 = "LockOpen_32";
            
            /// <summary>
            /// Stores the resource name 'LogoAnete'.
            /// </summary>
            public const string LogoAnete = "LogoAnete";
            
            /// <summary>
            /// Stores the resource name 'Loudspeaker2_16'.
            /// </summary>
            public const string Loudspeaker2_16 = "Loudspeaker2_16";
            
            /// <summary>
            /// Stores the resource name 'Loudspeaker2_24'.
            /// </summary>
            public const string Loudspeaker2_24 = "Loudspeaker2_24";
            
            /// <summary>
            /// Stores the resource name 'Loudspeaker2_32'.
            /// </summary>
            public const string Loudspeaker2_32 = "Loudspeaker2_32";
            
            /// <summary>
            /// Stores the resource name 'Mail_16'.
            /// </summary>
            public const string Mail_16 = "Mail_16";
            
            /// <summary>
            /// Stores the resource name 'Mail_24'.
            /// </summary>
            public const string Mail_24 = "Mail_24";
            
            /// <summary>
            /// Stores the resource name 'Mail_32'.
            /// </summary>
            public const string Mail_32 = "Mail_32";
            
            /// <summary>
            /// Stores the resource name 'Mail_48'.
            /// </summary>
            public const string Mail_48 = "Mail_48";
            
            /// <summary>
            /// Stores the resource name 'media_pause_16'.
            /// </summary>
            public const string media_pause_16 = "media_pause_16";
            
            /// <summary>
            /// Stores the resource name 'media_pause_32'.
            /// </summary>
            public const string media_pause_32 = "media_pause_32";
            
            /// <summary>
            /// Stores the resource name 'media_play_green_16'.
            /// </summary>
            public const string media_play_green_16 = "media_play_green_16";
            
            /// <summary>
            /// Stores the resource name 'media_play_green_32'.
            /// </summary>
            public const string media_play_green_32 = "media_play_green_32";
            
            /// <summary>
            /// Stores the resource name 'MediaPlayGreen_16'.
            /// </summary>
            public const string MediaPlayGreen_16 = "MediaPlayGreen_16";
            
            /// <summary>
            /// Stores the resource name 'MediaPlayGreen_32'.
            /// </summary>
            public const string MediaPlayGreen_32 = "MediaPlayGreen_32";
            
            /// <summary>
            /// Stores the resource name 'Message_16'.
            /// </summary>
            public const string Message_16 = "Message_16";
            
            /// <summary>
            /// Stores the resource name 'Message_24'.
            /// </summary>
            public const string Message_24 = "Message_24";
            
            /// <summary>
            /// Stores the resource name 'Message_32'.
            /// </summary>
            public const string Message_32 = "Message_32";
            
            /// <summary>
            /// Stores the resource name 'Message_48'.
            /// </summary>
            public const string Message_48 = "Message_48";
            
            /// <summary>
            /// Stores the resource name 'Money_16'.
            /// </summary>
            public const string Money_16 = "Money_16";
            
            /// <summary>
            /// Stores the resource name 'Money_24'.
            /// </summary>
            public const string Money_24 = "Money_24";
            
            /// <summary>
            /// Stores the resource name 'Money_32'.
            /// </summary>
            public const string Money_32 = "Money_32";
            
            /// <summary>
            /// Stores the resource name 'Money2_16'.
            /// </summary>
            public const string Money2_16 = "Money2_16";
            
            /// <summary>
            /// Stores the resource name 'Money2_24'.
            /// </summary>
            public const string Money2_24 = "Money2_24";
            
            /// <summary>
            /// Stores the resource name 'Money2_32'.
            /// </summary>
            public const string Money2_32 = "Money2_32";
            
            /// <summary>
            /// Stores the resource name 'Money2_48'.
            /// </summary>
            public const string Money2_48 = "Money2_48";
            
            /// <summary>
            /// Stores the resource name 'Monitor_16'.
            /// </summary>
            public const string Monitor_16 = "Monitor_16";
            
            /// <summary>
            /// Stores the resource name 'Monitor_32'.
            /// </summary>
            public const string Monitor_32 = "Monitor_32";
            
            /// <summary>
            /// Stores the resource name 'NavigateClose16'.
            /// </summary>
            public const string NavigateClose16 = "NavigateClose16";
            
            /// <summary>
            /// Stores the resource name 'NavigateClose32'.
            /// </summary>
            public const string NavigateClose32 = "NavigateClose32";
            
            /// <summary>
            /// Stores the resource name 'NavigateCross_16'.
            /// </summary>
            public const string NavigateCross_16 = "NavigateCross_16";
            
            /// <summary>
            /// Stores the resource name 'NavigateCross_32'.
            /// </summary>
            public const string NavigateCross_32 = "NavigateCross_32";
            
            /// <summary>
            /// Stores the resource name 'NavigateMinus_16'.
            /// </summary>
            public const string NavigateMinus_16 = "NavigateMinus_16";
            
            /// <summary>
            /// Stores the resource name 'NavigateMinus_32'.
            /// </summary>
            public const string NavigateMinus_32 = "NavigateMinus_32";
            
            /// <summary>
            /// Stores the resource name 'NavigateOpen16'.
            /// </summary>
            public const string NavigateOpen16 = "NavigateOpen16";
            
            /// <summary>
            /// Stores the resource name 'NavigateOpen32'.
            /// </summary>
            public const string NavigateOpen32 = "NavigateOpen32";
            
            /// <summary>
            /// Stores the resource name 'NavigatePlus_16'.
            /// </summary>
            public const string NavigatePlus_16 = "NavigatePlus_16";
            
            /// <summary>
            /// Stores the resource name 'NavigatePlus_32'.
            /// </summary>
            public const string NavigatePlus_32 = "NavigatePlus_32";
            
            /// <summary>
            /// Stores the resource name 'NavPlainBlue_16'.
            /// </summary>
            public const string NavPlainBlue_16 = "NavPlainBlue_16";
            
            /// <summary>
            /// Stores the resource name 'NavPlainBlue_32'.
            /// </summary>
            public const string NavPlainBlue_32 = "NavPlainBlue_32";
            
            /// <summary>
            /// Stores the resource name 'Note_16'.
            /// </summary>
            public const string Note_16 = "Note_16";
            
            /// <summary>
            /// Stores the resource name 'Note_24'.
            /// </summary>
            public const string Note_24 = "Note_24";
            
            /// <summary>
            /// Stores the resource name 'Note_32'.
            /// </summary>
            public const string Note_32 = "Note_32";
            
            /// <summary>
            /// Stores the resource name 'Notebook_16'.
            /// </summary>
            public const string Notebook_16 = "Notebook_16";
            
            /// <summary>
            /// Stores the resource name 'Notebook_24'.
            /// </summary>
            public const string Notebook_24 = "Notebook_24";
            
            /// <summary>
            /// Stores the resource name 'Notebook_32'.
            /// </summary>
            public const string Notebook_32 = "Notebook_32";
            
            /// <summary>
            /// Stores the resource name 'NotebookEdit_16'.
            /// </summary>
            public const string NotebookEdit_16 = "NotebookEdit_16";
            
            /// <summary>
            /// Stores the resource name 'NotebookEdit_24'.
            /// </summary>
            public const string NotebookEdit_24 = "NotebookEdit_24";
            
            /// <summary>
            /// Stores the resource name 'NotebookEdit_32'.
            /// </summary>
            public const string NotebookEdit_32 = "NotebookEdit_32";
            
            /// <summary>
            /// Stores the resource name 'NoteEdit_16'.
            /// </summary>
            public const string NoteEdit_16 = "NoteEdit_16";
            
            /// <summary>
            /// Stores the resource name 'NoteEdit_32'.
            /// </summary>
            public const string NoteEdit_32 = "NoteEdit_32";
            
            /// <summary>
            /// Stores the resource name 'NotePinned_16'.
            /// </summary>
            public const string NotePinned_16 = "NotePinned_16";
            
            /// <summary>
            /// Stores the resource name 'NotePinned_32'.
            /// </summary>
            public const string NotePinned_32 = "NotePinned_32";
            
            /// <summary>
            /// Stores the resource name 'Paperclip_16'.
            /// </summary>
            public const string Paperclip_16 = "Paperclip_16";
            
            /// <summary>
            /// Stores the resource name 'Paperclip_24'.
            /// </summary>
            public const string Paperclip_24 = "Paperclip_24";
            
            /// <summary>
            /// Stores the resource name 'Paperclip_32'.
            /// </summary>
            public const string Paperclip_32 = "Paperclip_32";
            
            /// <summary>
            /// Stores the resource name 'PaperclipAdd_16'.
            /// </summary>
            public const string PaperclipAdd_16 = "PaperclipAdd_16";
            
            /// <summary>
            /// Stores the resource name 'PaperclipAdd_24'.
            /// </summary>
            public const string PaperclipAdd_24 = "PaperclipAdd_24";
            
            /// <summary>
            /// Stores the resource name 'PaperclipAdd_32'.
            /// </summary>
            public const string PaperclipAdd_32 = "PaperclipAdd_32";
            
            /// <summary>
            /// Stores the resource name 'PaperclipDelete_16'.
            /// </summary>
            public const string PaperclipDelete_16 = "PaperclipDelete_16";
            
            /// <summary>
            /// Stores the resource name 'PaperclipDelete_24'.
            /// </summary>
            public const string PaperclipDelete_24 = "PaperclipDelete_24";
            
            /// <summary>
            /// Stores the resource name 'PaperclipDelete_32'.
            /// </summary>
            public const string PaperclipDelete_32 = "PaperclipDelete_32";
            
            /// <summary>
            /// Stores the resource name 'PaperclipEdit_16'.
            /// </summary>
            public const string PaperclipEdit_16 = "PaperclipEdit_16";
            
            /// <summary>
            /// Stores the resource name 'PaperclipEdit_24'.
            /// </summary>
            public const string PaperclipEdit_24 = "PaperclipEdit_24";
            
            /// <summary>
            /// Stores the resource name 'PaperclipEdit_32'.
            /// </summary>
            public const string PaperclipEdit_32 = "PaperclipEdit_32";
            
            /// <summary>
            /// Stores the resource name 'Pda2_16'.
            /// </summary>
            public const string Pda2_16 = "Pda2_16";
            
            /// <summary>
            /// Stores the resource name 'Pda2_32'.
            /// </summary>
            public const string Pda2_32 = "Pda2_32";
            
            /// <summary>
            /// Stores the resource name 'Pda2Into_16'.
            /// </summary>
            public const string Pda2Into_16 = "Pda2Into_16";
            
            /// <summary>
            /// Stores the resource name 'Pda2Into_32'.
            /// </summary>
            public const string Pda2Into_32 = "Pda2Into_32";
            
            /// <summary>
            /// Stores the resource name 'Pda2Out_16'.
            /// </summary>
            public const string Pda2Out_16 = "Pda2Out_16";
            
            /// <summary>
            /// Stores the resource name 'Pda2Out_32'.
            /// </summary>
            public const string Pda2Out_32 = "Pda2Out_32";
            
            /// <summary>
            /// Stores the resource name 'Pencil_16'.
            /// </summary>
            public const string Pencil_16 = "Pencil_16";
            
            /// <summary>
            /// Stores the resource name 'Pencil_32'.
            /// </summary>
            public const string Pencil_32 = "Pencil_32";
            
            /// <summary>
            /// Stores the resource name 'PosPrinter_16'.
            /// </summary>
            public const string PosPrinter_16 = "PosPrinter_16";
            
            /// <summary>
            /// Stores the resource name 'PosPrinter_24'.
            /// </summary>
            public const string PosPrinter_24 = "PosPrinter_24";
            
            /// <summary>
            /// Stores the resource name 'PosPrinter_32'.
            /// </summary>
            public const string PosPrinter_32 = "PosPrinter_32";
            
            /// <summary>
            /// Stores the resource name 'Printer_16'.
            /// </summary>
            public const string Printer_16 = "Printer_16";
            
            /// <summary>
            /// Stores the resource name 'Printer_32'.
            /// </summary>
            public const string Printer_32 = "Printer_32";
            
            /// <summary>
            /// Stores the resource name 'PrintPreview_16'.
            /// </summary>
            public const string PrintPreview_16 = "PrintPreview_16";
            
            /// <summary>
            /// Stores the resource name 'PrintPreview_32'.
            /// </summary>
            public const string PrintPreview_32 = "PrintPreview_32";
            
            /// <summary>
            /// Stores the resource name 'Question_32'.
            /// </summary>
            public const string Question_32 = "Question_32";
            
            /// <summary>
            /// Stores the resource name 'Recycle_16'.
            /// </summary>
            public const string Recycle_16 = "Recycle_16";
            
            /// <summary>
            /// Stores the resource name 'Recycle_32'.
            /// </summary>
            public const string Recycle_32 = "Recycle_32";
            
            /// <summary>
            /// Stores the resource name 'Redo_16'.
            /// </summary>
            public const string Redo_16 = "Redo_16";
            
            /// <summary>
            /// Stores the resource name 'Redo_24'.
            /// </summary>
            public const string Redo_24 = "Redo_24";
            
            /// <summary>
            /// Stores the resource name 'Redo_32'.
            /// </summary>
            public const string Redo_32 = "Redo_32";
            
            /// <summary>
            /// Stores the resource name 'Refresh_16'.
            /// </summary>
            public const string Refresh_16 = "Refresh_16";
            
            /// <summary>
            /// Stores the resource name 'Refresh_24'.
            /// </summary>
            public const string Refresh_24 = "Refresh_24";
            
            /// <summary>
            /// Stores the resource name 'Refresh_32'.
            /// </summary>
            public const string Refresh_32 = "Refresh_32";
            
            /// <summary>
            /// Stores the resource name 'Report_16'.
            /// </summary>
            public const string Report_16 = "Report_16";
            
            /// <summary>
            /// Stores the resource name 'Report_24'.
            /// </summary>
            public const string Report_24 = "Report_24";
            
            /// <summary>
            /// Stores the resource name 'Report_32'.
            /// </summary>
            public const string Report_32 = "Report_32";
            
            /// <summary>
            /// Stores the resource name 'Row_16'.
            /// </summary>
            public const string Row_16 = "Row_16";
            
            /// <summary>
            /// Stores the resource name 'Row_32'.
            /// </summary>
            public const string Row_32 = "Row_32";
            
            /// <summary>
            /// Stores the resource name 'RowAdd_16'.
            /// </summary>
            public const string RowAdd_16 = "RowAdd_16";
            
            /// <summary>
            /// Stores the resource name 'RowAdd_32'.
            /// </summary>
            public const string RowAdd_32 = "RowAdd_32";
            
            /// <summary>
            /// Stores the resource name 'RowDelete_16'.
            /// </summary>
            public const string RowDelete_16 = "RowDelete_16";
            
            /// <summary>
            /// Stores the resource name 'RowDelete_32'.
            /// </summary>
            public const string RowDelete_32 = "RowDelete_32";
            
            /// <summary>
            /// Stores the resource name 'RowEdit_16'.
            /// </summary>
            public const string RowEdit_16 = "RowEdit_16";
            
            /// <summary>
            /// Stores the resource name 'RowEdit_32'.
            /// </summary>
            public const string RowEdit_32 = "RowEdit_32";
            
            /// <summary>
            /// Stores the resource name 'RowPreferences_16'.
            /// </summary>
            public const string RowPreferences_16 = "RowPreferences_16";
            
            /// <summary>
            /// Stores the resource name 'RowPreferences_32'.
            /// </summary>
            public const string RowPreferences_32 = "RowPreferences_32";
            
            /// <summary>
            /// Stores the resource name 'SaveEdit_16'.
            /// </summary>
            public const string SaveEdit_16 = "SaveEdit_16";
            
            /// <summary>
            /// Stores the resource name 'SaveEdit_32'.
            /// </summary>
            public const string SaveEdit_32 = "SaveEdit_32";
            
            /// <summary>
            /// Stores the resource name 'Scroll_16'.
            /// </summary>
            public const string Scroll_16 = "Scroll_16";
            
            /// <summary>
            /// Stores the resource name 'Scroll_24'.
            /// </summary>
            public const string Scroll_24 = "Scroll_24";
            
            /// <summary>
            /// Stores the resource name 'Scroll_32'.
            /// </summary>
            public const string Scroll_32 = "Scroll_32";
            
            /// <summary>
            /// Stores the resource name 'ScrollAdd_32'.
            /// </summary>
            public const string ScrollAdd_32 = "ScrollAdd_32";
            
            /// <summary>
            /// Stores the resource name 'ScrollAdd16'.
            /// </summary>
            public const string ScrollAdd16 = "ScrollAdd16";
            
            /// <summary>
            /// Stores the resource name 'ScrollDelete_32'.
            /// </summary>
            public const string ScrollDelete_32 = "ScrollDelete_32";
            
            /// <summary>
            /// Stores the resource name 'ScrollDelete16'.
            /// </summary>
            public const string ScrollDelete16 = "ScrollDelete16";
            
            /// <summary>
            /// Stores the resource name 'ScrollEdit_32'.
            /// </summary>
            public const string ScrollEdit_32 = "ScrollEdit_32";
            
            /// <summary>
            /// Stores the resource name 'ScrollEdit16'.
            /// </summary>
            public const string ScrollEdit16 = "ScrollEdit16";
            
            /// <summary>
            /// Stores the resource name 'Selection_16'.
            /// </summary>
            public const string Selection_16 = "Selection_16";
            
            /// <summary>
            /// Stores the resource name 'Selection_32'.
            /// </summary>
            public const string Selection_32 = "Selection_32";
            
            /// <summary>
            /// Stores the resource name 'Selection_delete_16'.
            /// </summary>
            public const string Selection_delete_16 = "Selection_delete_16";
            
            /// <summary>
            /// Stores the resource name 'Selection_delete_32'.
            /// </summary>
            public const string Selection_delete_32 = "Selection_delete_32";
            
            /// <summary>
            /// Stores the resource name 'ServerClientExchange_16'.
            /// </summary>
            public const string ServerClientExchange_16 = "ServerClientExchange_16";
            
            /// <summary>
            /// Stores the resource name 'ServerClientExchange_32'.
            /// </summary>
            public const string ServerClientExchange_32 = "ServerClientExchange_32";
            
            /// <summary>
            /// Stores the resource name 'SortDown_16'.
            /// </summary>
            public const string SortDown_16 = "SortDown_16";
            
            /// <summary>
            /// Stores the resource name 'SortDown_24'.
            /// </summary>
            public const string SortDown_24 = "SortDown_24";
            
            /// <summary>
            /// Stores the resource name 'SortDown_32'.
            /// </summary>
            public const string SortDown_32 = "SortDown_32";
            
            /// <summary>
            /// Stores the resource name 'star_blue_16'.
            /// </summary>
            public const string star_blue_16 = "star_blue_16";
            
            /// <summary>
            /// Stores the resource name 'star_grey_16'.
            /// </summary>
            public const string star_grey_16 = "star_grey_16";
            
            /// <summary>
            /// Stores the resource name 'Stop_16'.
            /// </summary>
            public const string Stop_16 = "Stop_16";
            
            /// <summary>
            /// Stores the resource name 'Stop_32'.
            /// </summary>
            public const string Stop_32 = "Stop_32";
            
            /// <summary>
            /// Stores the resource name 'SystemAdministration_24'.
            /// </summary>
            public const string SystemAdministration_24 = "SystemAdministration_24";
            
            /// <summary>
            /// Stores the resource name 'Table_16'.
            /// </summary>
            public const string Table_16 = "Table_16";
            
            /// <summary>
            /// Stores the resource name 'Table_32'.
            /// </summary>
            public const string Table_32 = "Table_32";
            
            /// <summary>
            /// Stores the resource name 'TableSqlAdd_16'.
            /// </summary>
            public const string TableSqlAdd_16 = "TableSqlAdd_16";
            
            /// <summary>
            /// Stores the resource name 'TableSqlAdd_32'.
            /// </summary>
            public const string TableSqlAdd_32 = "TableSqlAdd_32";
            
            /// <summary>
            /// Stores the resource name 'TextCode_16'.
            /// </summary>
            public const string TextCode_16 = "TextCode_16";
            
            /// <summary>
            /// Stores the resource name 'TextCode_32'.
            /// </summary>
            public const string TextCode_32 = "TextCode_32";
            
            /// <summary>
            /// Stores the resource name 'TruckBlue_16'.
            /// </summary>
            public const string TruckBlue_16 = "TruckBlue_16";
            
            /// <summary>
            /// Stores the resource name 'TruckBlue_32'.
            /// </summary>
            public const string TruckBlue_32 = "TruckBlue_32";
            
            /// <summary>
            /// Stores the resource name 'Undo_16'.
            /// </summary>
            public const string Undo_16 = "Undo_16";
            
            /// <summary>
            /// Stores the resource name 'Undo_24'.
            /// </summary>
            public const string Undo_24 = "Undo_24";
            
            /// <summary>
            /// Stores the resource name 'Undo_32'.
            /// </summary>
            public const string Undo_32 = "Undo_32";
            
            /// <summary>
            /// Stores the resource name 'UpPlus16'.
            /// </summary>
            public const string UpPlus16 = "UpPlus16";
            
            /// <summary>
            /// Stores the resource name 'UpPlus32'.
            /// </summary>
            public const string UpPlus32 = "UpPlus32";
            
            /// <summary>
            /// Stores the resource name 'User1_16'.
            /// </summary>
            public const string User1_16 = "User1_16";
            
            /// <summary>
            /// Stores the resource name 'User1_32'.
            /// </summary>
            public const string User1_32 = "User1_32";
            
            /// <summary>
            /// Stores the resource name 'User1Lock_16'.
            /// </summary>
            public const string User1Lock_16 = "User1Lock_16";
            
            /// <summary>
            /// Stores the resource name 'User1Lock_32'.
            /// </summary>
            public const string User1Lock_32 = "User1Lock_32";
            
            /// <summary>
            /// Stores the resource name 'Users1_16'.
            /// </summary>
            public const string Users1_16 = "Users1_16";
            
            /// <summary>
            /// Stores the resource name 'Users1_24'.
            /// </summary>
            public const string Users1_24 = "Users1_24";
            
            /// <summary>
            /// Stores the resource name 'Users1_32'.
            /// </summary>
            public const string Users1_32 = "Users1_32";
            
            /// <summary>
            /// Stores the resource name 'Users3_16'.
            /// </summary>
            public const string Users3_16 = "Users3_16";
            
            /// <summary>
            /// Stores the resource name 'Users3_32'.
            /// </summary>
            public const string Users3_32 = "Users3_32";
            
            /// <summary>
            /// Stores the resource name 'Users3Preferences_16'.
            /// </summary>
            public const string Users3Preferences_16 = "Users3Preferences_16";
            
            /// <summary>
            /// Stores the resource name 'Users3Preferences_32'.
            /// </summary>
            public const string Users3Preferences_32 = "Users3Preferences_32";
            
            /// <summary>
            /// Stores the resource name 'View_16'.
            /// </summary>
            public const string View_16 = "View_16";
            
            /// <summary>
            /// Stores the resource name 'View_24'.
            /// </summary>
            public const string View_24 = "View_24";
            
            /// <summary>
            /// Stores the resource name 'View_32'.
            /// </summary>
            public const string View_32 = "View_32";
            
            /// <summary>
            /// Stores the resource name 'Warning_16'.
            /// </summary>
            public const string Warning_16 = "Warning_16";
            
            /// <summary>
            /// Stores the resource name 'Warning_24'.
            /// </summary>
            public const string Warning_24 = "Warning_24";
            
            /// <summary>
            /// Stores the resource name 'Warning_32'.
            /// </summary>
            public const string Warning_32 = "Warning_32";
            
            /// <summary>
            /// Stores the resource name 'Window_16'.
            /// </summary>
            public const string Window_16 = "Window_16";
            
            /// <summary>
            /// Stores the resource name 'Window_32'.
            /// </summary>
            public const string Window_32 = "Window_32";
            
            /// <summary>
            /// Stores the resource name 'WindowSidebar_16'.
            /// </summary>
            public const string WindowSidebar_16 = "WindowSidebar_16";
            
            /// <summary>
            /// Stores the resource name 'WindowSidebar_24'.
            /// </summary>
            public const string WindowSidebar_24 = "WindowSidebar_24";
            
            /// <summary>
            /// Stores the resource name 'WindowSidebar_32'.
            /// </summary>
            public const string WindowSidebar_32 = "WindowSidebar_32";
            
            /// <summary>
            /// Stores the resource name 'WindowSplitHor_16'.
            /// </summary>
            public const string WindowSplitHor_16 = "WindowSplitHor_16";
            
            /// <summary>
            /// Stores the resource name 'WindowSplitHor_24'.
            /// </summary>
            public const string WindowSplitHor_24 = "WindowSplitHor_24";
            
            /// <summary>
            /// Stores the resource name 'WindowSplitHor_32'.
            /// </summary>
            public const string WindowSplitHor_32 = "WindowSplitHor_32";
            
            /// <summary>
            /// Stores the resource name 'WineRedBottle_32'.
            /// </summary>
            public const string WineRedBottle_32 = "WineRedBottle_32";
            
            /// <summary>
            /// Stores the resource name 'WineWhiteGlass_16'.
            /// </summary>
            public const string WineWhiteGlass_16 = "WineWhiteGlass_16";
            
            /// <summary>
            /// Stores the resource name 'WineWhiteGlass_32'.
            /// </summary>
            public const string WineWhiteGlass_32 = "WineWhiteGlass_32";
            
            /// <summary>
            /// Stores the resource name 'Workstation1_16'.
            /// </summary>
            public const string Workstation1_16 = "Workstation1_16";
            
            /// <summary>
            /// Stores the resource name 'Workstation1_24'.
            /// </summary>
            public const string Workstation1_24 = "Workstation1_24";
            
            /// <summary>
            /// Stores the resource name 'Workstation1_32'.
            /// </summary>
            public const string Workstation1_32 = "Workstation1_32";
            
            /// <summary>
            /// Stores the resource name 'ZoomFit_16'.
            /// </summary>
            public const string ZoomFit_16 = "ZoomFit_16";
            
            /// <summary>
            /// Stores the resource name 'ZoomFit_32'.
            /// </summary>
            public const string ZoomFit_32 = "ZoomFit_32";
            
            /// <summary>
            /// Stores the resource name 'ZoomIn_16'.
            /// </summary>
            public const string ZoomIn_16 = "ZoomIn_16";
            
            /// <summary>
            /// Stores the resource name 'ZoomIn_32'.
            /// </summary>
            public const string ZoomIn_32 = "ZoomIn_32";
            
            /// <summary>
            /// Stores the resource name 'ZoomOut_16'.
            /// </summary>
            public const string ZoomOut_16 = "ZoomOut_16";
            
            /// <summary>
            /// Stores the resource name 'ZoomOut_32'.
            /// </summary>
            public const string ZoomOut_32 = "ZoomOut_32";
        }
    }
}
