using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;

namespace Anete.Common.Data.Nh.Entities
{
	public partial class OrganizationAddress
	{
		/// <summary>
		/// Jednodussi vycteni PSC a mesta z fakturacni adresy
		/// </summary>
		public virtual string ZipCodeAndCity
		{
			get
			{
				return string.Format("{0}{1}{2}", ZipCode,
												  (string.IsNullOrEmpty(ZipCode) ? string.Empty : " ")
												  , Place);
			}
		}

		/// <summary>
		/// Resi problem, kdy adresa nema vyplneny nazev
		/// </summary>
		public virtual string NameOrEstablishmentName
		{
			get
			{
				if (string.IsNullOrEmpty(Name))
				{
					// pokud nemam vyplneny rejstrikovy nazev, pouziju popis z Kreditu
					if (string.IsNullOrWhiteSpace(Organization.IdentificationName))
					{
						return Organization.Name;
					}
					return Organization.IdentificationName;
				}
				else
				{
					return Name;
				}
			}
		}
	}
}
