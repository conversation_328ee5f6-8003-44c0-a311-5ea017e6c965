using Microsoft.CodeAnalysis;
using System.Linq;
using System.Text;

namespace Anete.Generators
{
	public class ApiServiceCreator : ApiServiceCreatorBase
	{
		protected override void GenerateClassHeader(StringBuilder sb, INamedTypeSymbol symbol)
		{
			sb.AppendLine("\t\tprivate readonly Unity.IUnityContainer _container;");
			sb.AppendLine();
			sb.AppendLine($"\t\tpublic {GetClassName(symbol, false)}(Unity.IUnityContainer container)");
			sb.AppendLine("\t\t{");
			sb.AppendLine("\t\t\t_container = container;");
			sb.AppendLine("\t\t}");
		}

		protected override void GenerateHeader(StringBuilder sb)
		{
			sb.AppendLine("using Unity;");
			sb.AppendLine();
		}

		protected override void GenerateMethodBody(StringBuilder sb, IMethodSymbol method, INamedTypeSymbol taskSymbol, INamedTypeSymbol taskGenericSymbol)
		{
			var parameters = method.Parameters.Select(x =>
			{
				var type = x.Type.ToString();
				return new { x.Name, x.Type, IsCancellationToken = type == "System.Threading.CancellationToken" || type == "System.Threading.CancellationToken?" };
			}).ToArray();

			var parameterRequest = parameters.FirstOrDefault(x => !x.IsCancellationToken);
			var parameterCancellationToken = parameters.FirstOrDefault(x => x.IsCancellationToken);

			// Task
			if (SymbolEqualityComparer.Default.Equals(method.ReturnType, taskSymbol))
			{
				if (parameterRequest != null)
				{
					sb.AppendLine($"\t\t\tvar handler = _container.Resolve<{GetRequestHandlerType(parameterRequest.Type, null, true)}>();");
					sb.AppendLine($"\t\t\treturn handler.HandleAsync({parameterRequest.Name}, {parameterCancellationToken.Name});");
				}
				else
				{
					sb.AppendLine("\t\t\treturn System.Threading.Tasks.Task.FromResult<object>(null);");
				}
			}
			// Task<T>
			else if (SymbolEqualityComparer.Default.Equals(method.ReturnType.OriginalDefinition, taskGenericSymbol))
			{
				var returnType = ((INamedTypeSymbol)method.ReturnType).TypeArguments.First();

				if (parameterRequest != null)
				{
					sb.AppendLine($"\t\t\tvar handler = _container.Resolve<{GetRequestHandlerType(parameterRequest.Type, returnType, true)}> ();");
					sb.AppendLine($"\t\t\treturn handler.HandleAsync({parameterRequest.Name}, {parameterCancellationToken.Name});");
				}
				else
				{
					sb.AppendLine($"\t\t\treturn System.Threading.Tasks.Task.FromResult<{returnType}>(null);");
				}
			}
			// void
			else if (method.ReturnsVoid)
			{
				if (parameterRequest != null)
				{
					sb.AppendLine($"\t\t\tvar handler = _container.Resolve<{GetRequestHandlerType(parameterRequest.Type, null, false)}>();");
					sb.AppendLine($"\t\t\thandler.Handle({parameterRequest.Name});");
				}
			}
			// ostatni navratove hodnoty
			else
			{
				if (parameterRequest != null)
				{
					sb.AppendLine($"\t\t\tvar handler = _container.Resolve<{GetRequestHandlerType(parameterRequest.Type, method.ReturnType, false)}>();");
					sb.AppendLine($"\t\t\treturn handler.Handle({parameterRequest.Name});");
				}
				else
				{
					sb.AppendLine($"return default({method.ReturnType});");
				}
			}
		}

		protected override void GenerateMethodFullName(StringBuilder sb, IMethodSymbol method)
		{
			sb.Append(method.ToDisplayString(new SymbolDisplayFormat(
				typeQualificationStyle: SymbolDisplayTypeQualificationStyle.NameAndContainingTypesAndNamespaces,
				genericsOptions: SymbolDisplayGenericsOptions.IncludeTypeParameters,
				memberOptions: SymbolDisplayMemberOptions.IncludeType | SymbolDisplayMemberOptions.IncludeParameters,
				parameterOptions: SymbolDisplayParameterOptions.IncludeType | SymbolDisplayParameterOptions.IncludeName
			)));
		}

		protected override string GetInterfaceName(INamedTypeSymbol symbol)
		{
			return symbol.ToDisplayString(new SymbolDisplayFormat(
				typeQualificationStyle: SymbolDisplayTypeQualificationStyle.NameAndContainingTypesAndNamespaces,
				genericsOptions: SymbolDisplayGenericsOptions.IncludeTypeParameters
			));
		}

		protected override string GetNewClassName(string nameOld)
		{
			return nameOld + "Generated";
		}

		private string GetRequestHandlerType(ITypeSymbol request, ITypeSymbol response, bool async)
		{
			var requestHandler = async ? "IRequestHandlerAsync" : "IRequestHandler";

			if (response != null)
			{
				return $"Anete.Utils.ComponentModel.Commands.{requestHandler}<{request}, {response}>";
			}
			else
			{
				return $"Anete.Utils.ComponentModel.Commands.{requestHandler}<{request}>";
			}
		}
	}
}
