using System;
using Anete.Common.Core.Interface.Enums;
using Anete.Common.Core.Interface.ProductInfo;
using Unity;
using Anete.Common.Core.Interface.AppServices;

namespace Anete.Common.Data.Interface.AppServices
{
	/// <summary>
	/// Informace o danem zarizeni. Toto je bazova trida bez konkretni implementace pristupu k datum
	/// Drive se nazyvalo ZarizeniService.
	/// Aby se odstranila duplicita zadavani typu zarizeni, je typ zarizeni ziskan primo ze sluzby IProduct.
	/// </summary>
	public class MockAppInstallationInfoService : IAppInstallationInfoService, IAppInstallationInfoServiceConfig
	{

		#region constructors...
		public bool ProviderEetEnabled
		{
			get
			{
				throw new NotImplementedException();
			}
			set
			{
				throw new NotImplementedException();
			}
		}
		/// <summary>
		/// Id provozovny dle Eet. 
		/// Kak: 22.07.2016: <PERSON><PERSON>, zatahl jsem to sem do spolecnych knihoven, protoze Kasa a zbytek aplikaci pouziva spolecny DataSet
		/// a nechce se mi to delit na zvlastni casti. Tak tady budeme muset snest dalsi short
		/// </summary>
		public int? EetWorkplaceId
		{
			get
			{
				throw new NotImplementedException();
			}
		}
		/// <summary>
		/// Initializes a new instance of the AppInstallationInfoServiceBase class.
		/// </summary>
		public MockAppInstallationInfoService(IProduct product, IDbConnectionProvider dbConnectionProvider, IAppInstallationIdProvider appInstallationIdProvider)
		{
            ApplicationType = product.ApplicationType;
            AppInstallationId = appInstallationIdProvider.AppInstallationId;
		}
		#endregion

		#region IAppInstallationInfoService members...
		/// <summary>
		/// Funkce zarizeni
		/// </summary>
		public ApplicationType ApplicationType { get; private set; }

		/// <summary>
		/// Identifikace Id instalace
		/// </summary>
		public short AppInstallationId { get; private set; }
		
		/// <summary>
		/// Identifikace jidelny
		/// </summary>
		public short WorkplaceId { get; set; }

		/// <summary>
		/// Nazev jidelny
		/// </summary>
		public string WorkplaceName { get; set; }

		/// <summary>
		/// Identifikace vydejny
		/// </summary>
		public short CanteenId { get; set; }

		/// <summary>
		/// Identifikace skladu
		/// </summary>
		public short StockId { get; set; }

		/// <summary>
		/// Popis zarizeni
		/// </summary>
		public string Name { get; set; }

		/// <summary>
		/// Zkratka typu aplikace
		/// </summary>
		public string ApplicationTypeShortName { get; set; }

		/// <summary>
		/// Popis vydejny
		/// </summary>
		public string CanteenName { get; set; }

		public short ProviderId { get; set; }

		public string ProviderVat { get; set; }

		public bool ProviderVatPayer { get; set; }
		#endregion

		#region IAppInstallationInfoServiceConfig Members
		/// <summary>
		/// Inicializace. Musi se volat ve chvili, kdy je dostupne pripojeni k databazi.
		/// </summary>        		
		public void Initialize()
		{
			
		}
		#endregion

		#region IDataChanged members...
		/// <summary>
		/// Event vyvolavany po zmene dat
		/// </summary>
		public event EventHandler DataChanged;

		/// <summary>
		/// Triggers the DataChanged event.
		/// </summary>
		public virtual void OnDataChanged()
		{
			if (DataChanged != null)
			{
				DataChanged(this, EventArgs.Empty);
			}
		}
		#endregion    
	
	    #region IAppInstallationIdProvider Members
#pragma warning disable 67
		/// <summary>
        /// Event volany pri zmene id zarizeni. Vyvola se v pripade, ze je id zarizeni prenastaveno dle argumentu prikazove radky.
        /// V teto tride nema smysl vubec volat. 
        /// </summary>
        public event EventHandler<Utils.EventArgs.SimpleDataEventArgs<short>> AppInstallationIdChanged;
#pragma warning restore 67
		#endregion
	}
}
