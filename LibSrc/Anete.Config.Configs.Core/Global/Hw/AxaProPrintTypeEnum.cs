using System;
using Anete.Utils;
using Anete.Utils.ComponentModel.Attributes;
using System.ComponentModel;
using Anete.Config.Core.Internals;

namespace Anete.Config.Configs.Core.Global.Hw
{
    /// <summary>
    /// typ ctecky identifikacnich karet
    /// </summary>
    /// <remarks></remarks>
    [ConfigResourceEnum]
    [TypeConverter(typeof(ConfigEnumConverter<AxaProPrintTypeEnum>))]
    public enum AxaProPrintTypeEnum
	{
        /// <summary>
        /// Doklad pre zakaznika aj obchodnika
        /// </summary>
        /// <remarks></remarks>
        Oboje,
		/// <summary>
		/// Doklad pre zakaznika 
		/// </summary>
		Zakaznik,
		/// <summary>
		/// Doklad pre obchodnika
		/// </summary>
		Obchodnik



	}
}
