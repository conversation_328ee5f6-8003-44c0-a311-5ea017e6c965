using System;
using System.Collections.Generic;
using System.Linq;
using Anete.Common.Data.Interface.Entities;
using Anete.Common.Data.Interface.Enums;

namespace Anete.Common.Data.Interface.Business.Paragon
{

	/// <summary>
	/// Spolecny interface pro radky paragonu. 
	/// </summary>
	/// <remarks>
	/// Pro nektere business objekty je potreba jednotny pristup k radku paragonu. Nekde se vsak vyskytuje SalesSlipRow, jinde nejaka
	/// projekce atd... Tim, ze objekt implementuje tento interface s nim pak muzu pracovat jednotne.
	/// </remarks>
    public interface ISalesSlipRowItem: IVatSalesSlipRowItem
	{

		/// <summary>
		/// Id zaznamu
		/// </summary>
		short RowId { get; }

		/// <summary>
		/// Id skladu
		/// </summary>
		short IdSklad { get; }

		/// <summary>
		/// Id zbozi
		/// </summary>
		int IdSortiment { get; }

		/// <summary>
		/// Mnozstvi
		/// </summary>
		decimal Mnozstvi { get; }

		/// <summary>
		/// Seznam poradovych cisel objednavek
		/// </summary>
		IEnumerable<int> ListPC { get; }

		/// <summary>
		/// Nazev polozky
		/// </summary>
		string Nazev { get; }

    }
}
