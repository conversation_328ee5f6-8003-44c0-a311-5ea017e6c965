using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Anete.Common.Core.Interface.AppServices.SupportMessage.CommandProcessor
{
	
	/// <summary>
	/// Informace o polozce commandu nactena z CommandSource
	/// </summary>
	/// <typeparam name="TCommand"></typeparam>
	/// <typeparam name="TItemInfo"></typeparam>
	public interface ICommandSourceItem<TCommand, TItemInfo>
	{
		/// <summary>
		/// Prikaz
		/// </summary>
		TCommand Command { get; set; }
		
		/// <summary>
		/// Informace o nactene polozce
		/// </summary>
		TItemInfo ItemInfo { get; set; }
	}

}
