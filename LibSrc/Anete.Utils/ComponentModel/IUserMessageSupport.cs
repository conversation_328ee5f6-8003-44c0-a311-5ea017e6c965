using System;
using System.Collections.Generic;
using System.Text;

namespace Anete.Utils.ComponentModel
{
	/// <summary>
	/// Pomocny objekt pro tridy, implementujici IUserMessage. Je tu proto, aby kazdy objekt nemusel znovu a znovu implementovat
	/// vsechny metody Info, Warning atd.
	/// </summary>
	public interface IUserMessageSupport
	{
		/// <summary>
		/// Objekt podporujici IUserMessage
		/// </summary>
		IUserMessage UserMessageAdapter
		{
			get;
		}

        /// <summary>
        /// Zapis hlaseni
        /// </summary>
        /// <param name="msgItem">Zaznam s informacemi o zprave</param>
		void AddMessage(IUserMessageItem msgItem);

        /// <summary>
        /// Pokud to lze, aktualizuje naposledy zobrazenou zpravu. Pokud ne, vytvori novou.
        /// V praxi se aktualizace tyka predevsim ProgressFormu. Hodi se pro pripady, kdy chci mit na radku prubezne aktualizovany procentualni udaj.
        /// </summary>
        /// <param name="msgItem"></param>
        void UpdateOrAddMessage(IUserMessageItem msgItem);

		void Question(string title, string message, string[] values, TimeSpan timeout, Action<string> setResult);
	}	
}
