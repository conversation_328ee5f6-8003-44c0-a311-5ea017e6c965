using Anete.Config.Core.Attributes;
using System.ComponentModel;

namespace Anete.Config.Configs.Core.Global.Authentication
{
	public abstract class ServerAuthenticationSettingsBase : AppAuthenticationSettingsBase
	{
		private bool _isEnabled;
		[DefaultValue(false)]
		[ConfigSRDisplayName(nameof(IsEnabled), typeof(ServerAuthenticationSettingsBase))]
		[ConfigSRDescription(nameof(IsEnabled), typeof(ServerAuthenticationSettingsBase))]
		public virtual bool IsEnabled
		{
			get { return _isEnabled; }
			set
			{
				if (_isEnabled != value)
				{
					_isEnabled = value;
					OnPropertyChanged();
				}
			}
		}
	}
}